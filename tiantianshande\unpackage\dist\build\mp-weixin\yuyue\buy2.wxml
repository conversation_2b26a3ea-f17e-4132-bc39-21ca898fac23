<view class="container"><block wx:if="{{isload}}"><block><form data-event-opts="{{[['submit',[['topay',['$event']]]]]}}" bindsubmit="__e"><block wx:if="{{sindex==1}}"><view class="address-add"><view class="linkitem"><text class="f1">联 系 人：</text><input class="input" type="text" placeholder="请输入您的姓名" placeholder-style="color:#626262;font-size:28rpx;" data-event-opts="{{[['input',[['inputLinkman',['$event']]]]]}}" value="{{linkman}}" bindinput="__e"/></view><view class="linkitem"><text class="f1">联系电话：</text><input class="input" type="text" placeholder="请输入您的手机号" placeholder-style="color:#626262;font-size:28rpx;" data-event-opts="{{[['input',[['inputTel',['$event']]]]]}}" value="{{tel}}" bindinput="__e"/></view></view></block><block wx:else><view class="address-add flex-y-center" data-url="{{'/pages/address/'+(address.id?'address':'addressadd')+'?fromPage=buy&type=1'}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="f1"><image class="img" src="/static/img/address.png"></image></view><block wx:if="{{address.id}}"><view class="f2 flex1"><view style="font-weight:bold;color:#111111;font-size:30rpx;">{{address.name+" "+address.tel+''}}<block wx:if="{{address.company}}"><text>{{address.company}}</text></block></view><view style="font-size:24rpx;">{{address.area+" "+address.address}}</view></view></block><block wx:else><view class="f2 flex1">请选择您的地点</view></block><image class="f3" src="/static/img/arrowright.png"></image></view></block><view class="buydata"><view class="bcontent"><view class="btitle">服务信息</view><view class="product"><view class="item flex"><view class="img" data-url="{{'product2?id='+allbuydata.skillId}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><block wx:if="{{allbuydata.pic}}"><image src="{{allbuydata.pic}}"></image></block><block wx:else><image src="{{allbuydata.pic}}"></image></block></view><view class="info flex1"><view class="f1">{{allbuydata.name}}</view><view class="f3"><text style="font-weight:bold;">{{"￥"+allbuydata.price+allbuydata.unit}}</text><text style="padding-left:20rpx;">{{'× '+allbuydata.num}}</text></view></view></view></view></view><view class="bcontent2"><view class="btitle2">服务方式</view><view class="price"><view class="f1">预约时间</view><view class="f2">{{yydate}}</view></view><view class="price"><view class="f1">服务人员</view><view class="f2">{{master.name}}</view></view><view class="price"><view class="f1">服务类型</view><view class="f2">{{allbuydata.serviceType}}</view></view></view><view class="bcontent2"><view class="price"><text class="f1">服务价格</text><text class="f2">{{"¥"+allbuydata.price}}</text></view><view class="price"><text class="f1">路程费用</text><view class="f2"><text style="color:red;">{{master.juli+'km'}}</text><text>{{'¥'+allbuydata.freight_price}}</text></view></view><view class="form-item"><view class="label">顾客备注<text></text></view><input class="input" name="remark" placeholder="请输入备注" placeholder-style="font-size:28rpx"/></view></view></view><view style="width:100%;height:110rpx;"></view><view class="footer flex"><view class="text1 flex1">总计：<text style="font-weight:bold;font-size:36rpx;">{{"￥"+totalprice}}</text></view><button class="op" style="{{'background:'+('linear-gradient(-90deg,'+$root.m0+' 0%,rgba('+$root.m1+',0.8) 100%)')+';'}}" form-type="submit">确认提交</button></view></form></block></block><block wx:if="{{loading}}"><loading vue-id="4db5e72c-1" bind:__l="__l"></loading></block><dp-tabbar vue-id="4db5e72c-2" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="4db5e72c-3" data-ref="popmsg" bind:__l="__l"></popmsg></view>