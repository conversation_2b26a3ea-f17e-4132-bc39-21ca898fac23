<view class="page"><block wx:if="{{loading}}"><loading vue-id="02d773c9-1" bind:__l="__l"></loading></block><block wx:if="{{detail}}"><view class="detail-content"><view class="header"><view class="title">{{detail.title}}</view><view class="info"><text class="category">{{detail.categoryname}}</text><text class="time">{{detail.addtime}}</text></view></view><view class="main-content"><view class="section"><view class="section-title">详细内容</view><view class="content-text">{{detail.content}}</view></view><view class="section"><view class="section-title">采购信息</view><view class="info-item"><text class="label">单价：</text><text class="value price">{{"￥"+(detail.price||'面议')}}</text></view><view class="info-item"><text class="label">数量：</text><text class="value">{{(detail.quantity||'待商议')+(detail.unit||'')}}</text></view><block wx:if="{{detail.total_price}}"><view class="info-item"><text class="label">总价：</text><text class="value price">{{"￥"+detail.total_price}}</text></view></block></view><view class="section"><view class="section-title">联系方式</view><view class="info-item"><text class="label">联系人：</text><text class="value">{{detail.contact_name}}</text></view><view class="info-item"><text class="label">联系电话：</text><text data-event-opts="{{[['tap',[['makePhoneCall',['$event']]]]]}}" class="value phone" bindtap="__e">{{detail.contact_phone}}</text></view></view><view class="section comment-section"><view class="section-title">评论区</view><block wx:if="{{$root.g0>0}}"><view class="comment-list"><block wx:for="{{$root.l3}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="comment-item"><view class="comment-header"><image class="avatar" src="{{item.$orig.headimg||'/static/images/default-avatar.png'}}" mode="aspectFill"></image><view class="comment-info"><text class="username">{{item.$orig.nickname}}</text><text class="time">{{item.$orig.addtime}}</text></view></view><view class="comment-content">{{item.$orig.content}}</view><block wx:if="{{item.$orig.images}}"><view class="image-list"><block wx:for="{{item.l0}}" wx:for-item="img" wx:for-index="imgIndex" wx:key="imgIndex"><image class="comment-image" src="{{img}}" mode="aspectFill" data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" data-event-params="{{({item:item.$orig,imgIndex})}}" bindtap="__e"></image></block></view></block><view class="action-bar"><text data-event-opts="{{[['tap',[['showReply',['$0'],[[['comments.list','',index,'id']]]]]]]}}" class="reply-btn" bindtap="__e">回复</text><block wx:if="{{item.$orig.reply_count>0}}"><text data-event-opts="{{[['tap',[['getReplyList',['$0'],[[['comments.list','',index,'id']]]]]]]}}" class="reply-count" bindtap="__e">{{''+item.$orig.reply_count+'条回复'}}</text></block></view><block wx:if="{{item.g1}}"><view class="reply-list"><block wx:for="{{item.l2}}" wx:for-item="reply" wx:for-index="replyIndex" wx:key="replyIndex"><view class="reply-item"><view class="reply-header"><image class="avatar small" src="{{reply.$orig.headimg||'/static/images/default-avatar.png'}}" mode="aspectFill"></image><view class="reply-info"><text class="username">{{reply.$orig.nickname}}</text><text class="time">{{reply.$orig.addtime}}</text></view></view><view class="reply-content">{{reply.$orig.content}}</view><block wx:if="{{reply.$orig.images}}"><view class="image-list"><block wx:for="{{reply.l1}}" wx:for-item="img" wx:for-index="imgIndex" wx:key="imgIndex"><image class="reply-image" src="{{img}}" mode="aspectFill" data-event-opts="{{[['tap',[['e1',['$event']]]]]}}" data-event-params="{{({reply:reply.$orig,imgIndex})}}" bindtap="__e"></image></block></view></block></view></block></view></block><block wx:if="{{showReplyInput&&currentCommentId===item.$orig.id}}"><view class="reply-input-wrapper"><input class="reply-input" type="text" placeholder="回复..." data-event-opts="{{[['confirm',[['submitReply',['$0'],[[['comments.list','',index,'id']]]]]],['input',[['__set_model',['','replyContent','$event',[]]]]]]}}" value="{{replyContent}}" bindconfirm="__e" bindinput="__e"/><view class="reply-btn-group"><view data-event-opts="{{[['tap',[['hideReply',['$event']]]]]}}" class="cancel-btn" bindtap="__e"><text class="btn-text">取消</text></view><view data-event-opts="{{[['tap',[['submitReply',['$0'],[[['comments.list','',index,'id']]]]]]]}}" class="submit-btn" bindtap="__e"><text class="btn-text">发送</text></view></view></view></block></view></block></view></block><block wx:else><view class="no-comment"><text class="no-comment-text">暂无评论</text></view></block></view></view><view class="comment-input-wrapper"><input class="comment-input" type="text" placeholder="说点什么..." data-event-opts="{{[['confirm',[['submitComment',['$event']]]],['input',[['__set_model',['','commentContent','$event',[]]]]]]}}" value="{{commentContent}}" bindconfirm="__e" bindinput="__e"/><view data-event-opts="{{[['tap',[['submitComment',['$event']]]]]}}" class="submit-btn" bindtap="__e"><text class="submit-text">发送</text></view></view><view class="footer"><view data-event-opts="{{[['tap',[['shareInfo',['$event']]]]]}}" class="btn share" bindtap="__e"><text class="btn-text">分享</text></view><view data-event-opts="{{[['tap',[['makePhoneCall',['$event']]]]]}}" class="btn contact" bindtap="__e"><text class="btn-text">立即联系</text></view></view></view></block><popmsg class="vue-ref" vue-id="02d773c9-2" data-ref="popmsg" bind:__l="__l"></popmsg></view>