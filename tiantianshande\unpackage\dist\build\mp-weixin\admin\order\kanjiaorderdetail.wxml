<view class="container"><block wx:if="{{isload}}"><block><view class="ordertop" style="{{('background:url('+pre_url+'/static/img/ordertop.png);background-size:100%')}}"><block wx:if="{{detail.status==0}}"><view class="f1"><view class="t1">等待买家付款</view></view></block><block wx:if="{{detail.status==1}}"><view class="f1"><view class="t1">已成功付款</view><block wx:if="{{detail.freight_type!=1}}"><view class="t2">请尽快发货</view></block><block wx:if="{{detail.freight_type==1}}"><view class="t2">待提货</view></block></view></block><block wx:if="{{detail.status==2}}"><view class="f1"><view class="t1">订单已发货</view><block wx:if="{{detail.freight_type!=3}}"><view class="t2">{{"发货信息："+detail.express_com+" "+detail.express_no}}</view></block></view></block><block wx:if="{{detail.status==3}}"><view class="f1"><view class="t1">订单已完成</view></view></block><block wx:if="{{detail.status==4}}"><view class="f1"><view class="t1">订单已取消</view></view></block></view><view class="address"><view class="img"><image src="/static/img/address3.png"></image></view><view class="info"><text class="t1" user-select="true" selectable="true">{{detail.linkman+" "+detail.tel}}</text><block wx:if="{{detail.freight_type!=1&&detail.freight_type!=3}}"><text class="t2" user-select="true" selectable="true">{{"地址："+detail.area+detail.address}}</text></block><block wx:if="{{detail.freight_type==1}}"><text class="t2" data-address="{{storeinfo.address}}" data-latitude="{{storeinfo.latitude}}" data-longitude="{{storeinfo.longitude}}" user-select="true" selectable="true" data-event-opts="{{[['tap',[['openLocation',['$event']]]]]}}" bindtap="__e">{{"取货地点："+storeinfo.name+" - "+storeinfo.address}}</text></block></view></view><view class="product"><view class="content"><view data-url="{{'/activity/kanjia/product?id='+detail.proid}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image src="{{detail.propic}}"></image></view><view class="detail"><text class="t1">{{detail.proname}}</text><text class="t2">{{detail.ggname}}</text><view class="t3"><text class="x1 flex1">{{"￥"+detail.sell_price}}</text><text class="x2">{{"×"+detail.num}}</text></view></view></view></view><block wx:if="{{(detail.status==3||detail.status==2)&&(detail.freight_type==3||detail.freight_type==4)}}"><view class="orderinfo"><view class="item flex-col"><text class="t1" style="color:#111;">发货信息</text><text class="t2" style="text-align:left;margin-top:10rpx;padding:0 10rpx;" user-select="true" selectable="true">{{detail.freight_content}}</text></view></view></block><view class="orderinfo"><view class="item"><text class="t1">下单人</text><text class="flex1"></text><image style="width:80rpx;height:80rpx;margin-right:8rpx;" src="{{detail.headimg}}"></image><text style="height:80rpx;line-height:80rpx;">{{detail.nickname}}</text></view><view class="item"><text class="t1">{{$root.m0+"ID"}}</text><text class="t2">{{detail.mid}}</text></view></view><block wx:if="{{detail.remark}}"><view class="orderinfo"><view class="item"><text class="t1">备注</text><text class="t2" user-select="true" selectable="true">{{detail.remark}}</text></view></view></block><view class="orderinfo"><view class="item"><text class="t1">订单编号</text><text class="t2" user-select="true" selectable="true">{{detail.ordernum}}</text></view><view class="item"><text class="t1">下单时间</text><text class="t2">{{detail.createtime}}</text></view><block wx:if="{{detail.status>0&&detail.paytime}}"><view class="item"><text class="t1">支付时间</text><text class="t2">{{detail.paytime}}</text></view></block><block wx:if="{{detail.status>0&&detail.paytime}}"><view class="item"><text class="t1">支付方式</text><text class="t2">{{detail.paytype}}</text></view></block><block wx:if="{{detail.status>1&&detail.send_time}}"><view class="item"><text class="t1">发货时间</text><text class="t2">{{detail.send_time}}</text></view></block><block wx:if="{{detail.status==3&&detail.collect_time}}"><view class="item"><text class="t1">收货时间</text><text class="t2">{{detail.collect_time}}</text></view></block></view><view class="orderinfo"><view class="item"><text class="t1">商品金额</text><text class="t2 red">{{"¥"+detail.product_price}}</text></view><block wx:if="{{detail.disprice>0}}"><view class="item"><text class="t1">{{$root.m1+"折扣"}}</text><text class="t2 red">{{"-¥"+detail.leveldk_money}}</text></view></block><block wx:if="{{detail.jianmoney>0}}"><view class="item"><text class="t1">满减活动</text><text class="t2 red">{{"-¥"+detail.manjian_money}}</text></view></block><view class="item"><text class="t1">配送方式</text><text class="t2">{{detail.freight_text}}</text></view><block wx:if="{{detail.freight_type==1&&detail.freightprice>0}}"><view class="item"><text class="t1">服务费</text><text class="t2 red">{{"+¥"+detail.freight_price}}</text></view></block><block wx:if="{{detail.freight_time}}"><view class="item"><text class="t1">{{(detail.freight_type!=1?'配送':'提货')+"时间"}}</text><text class="t2">{{detail.freight_time}}</text></view></block><block wx:if="{{detail.couponmoney>0}}"><view class="item"><text class="t1">{{$root.m2+"抵扣"}}</text><text class="t2 red">{{"-¥"+detail.coupon_money}}</text></view></block><block wx:if="{{detail.scoredk>0}}"><view class="item"><text class="t1">{{$root.m3+"抵扣"}}</text><text class="t2 red">{{"-¥"+detail.scoredk_money}}</text></view></block><view class="item"><text class="t1">实付款</text><text class="t2 red">{{"¥"+detail.totalprice}}</text></view><view class="item"><text class="t1">订单状态</text><block wx:if="{{detail.status==0}}"><text class="t2">未付款</text></block><block wx:if="{{detail.status==1}}"><block><block wx:if="{{detail.freight_type!=1}}"><text class="t2">待发货</text></block><block wx:if="{{detail.freight_type==1}}"><text class="t2">待提货</text></block></block></block><block wx:if="{{detail.status==2}}"><text class="t2">已发货</text></block><block wx:if="{{detail.status==3}}"><text class="t2">已收货</text></block><block wx:if="{{detail.status==4}}"><text class="t2">已关闭</text></block></view><block wx:if="{{detail.refund_status>0}}"><view class="item"><text class="t1">退款状态</text><block wx:if="{{detail.refund_status==1}}"><text class="t2 red">{{"审核中,¥"+detail.refund_money}}</text></block><block wx:if="{{detail.refund_status==2}}"><text class="t2 red">{{"已退款,¥"+detail.refund_money}}</text></block><block wx:if="{{detail.refund_status==3}}"><text class="t2 red">{{"已驳回,¥"+detail.refund_money}}</text></block></view></block><block wx:if="{{detail.refund_status>0}}"><view class="item"><text class="t1">退款原因</text><text class="t2 red">{{detail.refund_reason}}</text></view></block><block wx:if="{{detail.refund_checkremark}}"><view class="item"><text class="t1">审核备注</text><text class="t2 red">{{detail.refund_checkremark}}</text></view></block><block wx:if="{{detail.isfuwu&&detail.fuwuendtime>0}}"><view class="item"><text class="t1">到期时间</text><text class="t2 red">{{$root.g0}}</text></view></block><block wx:if="{{(detail.status==1||detail.status==2)&&detail.freight_type==1}}"><view class="item flex-col"><text class="t1">核销码</text><view class="flex-x-center"><image style="width:400rpx;height:400rpx;" src="{{detail.hexiao_qr}}" data-url="{{detail.hexiao_qr}}" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image></view></view></block></view><block wx:if="{{$root.g1>0}}"><view class="orderinfo"><block wx:for="{{detail.formdata}}" wx:for-item="item" wx:for-index="__i0__" wx:key="*this"><view class="item"><text class="t1">{{item[0]}}</text><block wx:if="{{item[2]=='upload'}}"><view class="t2"><image style="width:400rpx;height:auto;" src="{{item[1]}}" mode="widthFix" data-url="{{item[1]}}" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image></view></block><block wx:else><text class="t2" user-select="true" selectable="true">{{item[1]}}</text></block></view></block></view></block><view style="width:100%;height:160rpx;"></view><view class="bottom notabbarbot"><block wx:if="{{detail.refund_status==1}}"><view class="btn2" data-id="{{detail.id}}" data-event-opts="{{[['tap',[['refundnopass',['$event']]]]]}}" bindtap="__e">退款驳回</view></block><block wx:if="{{detail.refund_status==1}}"><view class="btn2" data-id="{{detail.id}}" data-event-opts="{{[['tap',[['refundpass',['$event']]]]]}}" bindtap="__e">退款通过</view></block><block wx:if="{{detail.status==0}}"><view class="btn2" data-id="{{detail.id}}" data-event-opts="{{[['tap',[['closeOrder',['$event']]]]]}}" bindtap="__e">关闭订单</view></block><block wx:if="{{detail.status==0&&detail.bid==0}}"><view class="btn2" data-id="{{detail.id}}" data-event-opts="{{[['tap',[['ispay',['$event']]]]]}}" bindtap="__e">改为已支付</view></block><block wx:if="{{detail.status==1}}"><view class="btn2" data-id="{{detail.id}}" data-event-opts="{{[['tap',[['fahuo',['$event']]]]]}}" bindtap="__e">发货</view></block><block wx:if="{{(detail.status==1||detail.status==2)&&detail.freight_type==1}}"><view class="btn2" data-id="{{detail.id}}" data-event-opts="{{[['tap',[['hexiao',['$event']]]]]}}" bindtap="__e">核销</view></block><block wx:if="{{detail.status==1&&detail.canpeisong}}"><view class="btn2" data-id="{{detail.id}}" data-event-opts="{{[['tap',[['peisong',['$event']]]]]}}" bindtap="__e">配送</view></block><block wx:if="{{(detail.status==2||detail.status==3)&&detail.express_com}}"><view class="btn2" data-url="{{'/pagesExt/order/logistics?express_com='+detail.express_com+'&express_no='+detail.express_no}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">查物流</view></block><block wx:if="{{detail.status==2&&detail.freight_type==10}}"><view class="btn2" data-id="{{detail.id}}" data-event-opts="{{[['tap',[['fahuo',['$event']]]]]}}" bindtap="__e">修改物流</view></block><block wx:if="{{detail.status==4}}"><view class="btn2" data-id="{{detail.id}}" data-event-opts="{{[['tap',[['delOrder',['$event']]]]]}}" bindtap="__e">删除</view></block><view class="btn2" data-id="{{detail.id}}" data-event-opts="{{[['tap',[['setremark',['$event']]]]]}}" bindtap="__e">设置备注</view></view><uni-popup class="vue-ref" vue-id="36dda2f0-1" id="dialogSetremark" type="dialog" data-ref="dialogSetremark" bind:__l="__l" vue-slots="{{['default']}}"><uni-popup-dialog vue-id="{{('36dda2f0-2')+','+('36dda2f0-1')}}" mode="input" title="设置备注" value="{{detail.remark}}" placeholder="请输入备注" data-event-opts="{{[['^confirm',[['setremarkconfirm']]]]}}" bind:confirm="__e" bind:__l="__l"></uni-popup-dialog></uni-popup><uni-popup class="vue-ref" vue-id="36dda2f0-3" id="dialogExpress" type="dialog" data-ref="dialogExpress" bind:__l="__l" vue-slots="{{['default']}}"><view class="uni-popup-dialog"><view class="uni-dialog-title"><text class="uni-dialog-title-text">发货</text></view><view class="uni-dialog-content"><view><view class="flex-y-center flex-x-center" style="margin:20rpx 20rpx;"><text style="font-size:28rpx;color:#000;">快递公司：</text><picker style="font-size:28rpx;border:1px #eee solid;padding:10rpx;height:70rpx;border-radius:4px;flex:1;" value="{{express_index}}" range="{{expressdata}}" data-event-opts="{{[['change',[['expresschange',['$event']]]]]}}" bindchange="__e"><view class="picker">{{expressdata[express_index]}}</view></picker></view><view class="flex-y-center flex-x-center" style="margin:20rpx 20rpx;"><view style="font-size:28rpx;color:#555;">快递单号：</view><input style="border:1px #eee solid;padding:10rpx;height:70rpx;border-radius:4px;flex:1;font-size:28rpx;" type="text" placeholder="请输入快递单号" data-event-opts="{{[['input',[['setexpressno',['$event']]]]]}}" bindinput="__e"/></view></view></view><view class="uni-dialog-button-group"><view data-event-opts="{{[['tap',[['dialogExpressClose',['$event']]]]]}}" class="uni-dialog-button" bindtap="__e"><text class="uni-dialog-button-text">取消</text></view><view data-event-opts="{{[['tap',[['confirmfahuo',['$event']]]]]}}" class="uni-dialog-button uni-border-left" bindtap="__e"><text class="uni-dialog-button-text uni-button-color">确定</text></view></view></view></uni-popup><uni-popup class="vue-ref" vue-id="36dda2f0-4" id="dialogPeisong" type="dialog" data-ref="dialogPeisong" bind:__l="__l" vue-slots="{{['default']}}"><view class="uni-popup-dialog"><view class="uni-dialog-title"><text class="uni-dialog-title-text">请选择配送员</text></view><view class="uni-dialog-content"><view><picker style="font-size:28rpx;border:1px #eee solid;padding:10rpx;height:70rpx;border-radius:4px;flex:1;" value="{{index2}}" range="{{peisonguser2}}" data-event-opts="{{[['change',[['peisongChange',['$event']]]]]}}" bindchange="__e"><view class="picker">{{peisonguser2[index2]}}</view></picker></view></view><view class="uni-dialog-button-group"><view data-event-opts="{{[['tap',[['dialogPeisongClose',['$event']]]]]}}" class="uni-dialog-button" bindtap="__e"><text class="uni-dialog-button-text">取消</text></view><view data-event-opts="{{[['tap',[['confirmPeisong',['$event']]]]]}}" class="uni-dialog-button uni-border-left" bindtap="__e"><text class="uni-dialog-button-text uni-button-color">确定</text></view></view></view></uni-popup><uni-popup class="vue-ref" vue-id="36dda2f0-5" id="dialogExpress10" type="dialog" data-ref="dialogExpress10" bind:__l="__l" vue-slots="{{['default']}}"><view class="uni-popup-dialog"><view class="uni-dialog-title"><text class="uni-dialog-title-text">发货信息</text></view><view class="uni-dialog-content"><view><view class="form-item flex" style="border-bottom:0;"><view class="f1" style="margin-right:20rpx;">物流单照片</view><view class="f2"><block wx:if="{{express_pic}}"><view class="layui-imgbox"><view class="layui-imgbox-close" data-index="{{0}}" data-field="express_pic" data-event-opts="{{[['tap',[['removeimg',['$event']]]]]}}" bindtap="__e"><image style="display:block;" src="/static/img/ico-del.png"></image></view><view class="layui-imgbox-img"><image src="{{express_pic}}" data-url="{{express_pic}}" mode="widthFix" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image></view></view></block><block wx:else><view class="uploadbtn" style="{{('background:url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 60rpx;background-size:80rpx 80rpx;background-color:#F3F3F3;')}}" data-field="express_pic" data-pernum="1" data-event-opts="{{[['tap',[['uploadimg',['$event']]]]]}}" bindtap="__e"></view></block></view><input type="text" hidden="true" name="express_pic" maxlength="-1" value="{{express_pic}}"/></view><view class="flex-y-center flex-x-center" style="margin:20rpx 20rpx;"><view style="font-size:28rpx;color:#555;">发货人：</view><input style="border:1px #eee solid;padding:10rpx;height:70rpx;border-radius:4px;flex:1;font-size:28rpx;" type="text" placeholder="请输入发货人信息" data-event-opts="{{[['input',[['setexpress_fhname',['$event']]]]]}}" bindinput="__e"/></view><view class="flex-y-center flex-x-center" style="margin:20rpx 20rpx;"><view style="font-size:28rpx;color:#555;">发货地址：</view><input style="border:1px #eee solid;padding:10rpx;height:70rpx;border-radius:4px;flex:1;font-size:28rpx;" type="text" placeholder="请输入发货地址" data-event-opts="{{[['input',[['setexpress_fhaddress',['$event']]]]]}}" bindinput="__e"/></view><view class="flex-y-center flex-x-center" style="margin:20rpx 20rpx;"><view style="font-size:28rpx;color:#555;">收货人：</view><input style="border:1px #eee solid;padding:10rpx;height:70rpx;border-radius:4px;flex:1;font-size:28rpx;" type="text" placeholder="请输入发货人信息" data-event-opts="{{[['input',[['setexpress_shname',['$event']]]]]}}" bindinput="__e"/></view><view class="flex-y-center flex-x-center" style="margin:20rpx 20rpx;"><view style="font-size:28rpx;color:#555;">收货地址：</view><input style="border:1px #eee solid;padding:10rpx;height:70rpx;border-radius:4px;flex:1;font-size:28rpx;" type="text" placeholder="请输入发货地址" data-event-opts="{{[['input',[['setexpress_shaddress',['$event']]]]]}}" bindinput="__e"/></view><view class="flex-y-center flex-x-center" style="margin:20rpx 20rpx;"><view style="font-size:28rpx;color:#555;">备注：</view><input style="border:1px #eee solid;padding:10rpx;height:70rpx;border-radius:4px;flex:1;font-size:28rpx;" type="text" placeholder="请输入备注" data-event-opts="{{[['input',[['setexpress_remark',['$event']]]]]}}" bindinput="__e"/></view></view></view><view class="uni-dialog-button-group"><view data-event-opts="{{[['tap',[['dialogExpress10Close',['$event']]]]]}}" class="uni-dialog-button" bindtap="__e"><text class="uni-dialog-button-text">取消</text></view><view data-event-opts="{{[['tap',[['confirmfahuo10',['$event']]]]]}}" class="uni-dialog-button uni-border-left" bindtap="__e"><text class="uni-dialog-button-text uni-button-color">确定</text></view></view></view></uni-popup></block></block><block wx:if="{{loading}}"><loading vue-id="36dda2f0-6" bind:__l="__l"></loading></block><dp-tabbar vue-id="36dda2f0-7" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="36dda2f0-8" data-ref="popmsg" bind:__l="__l"></popmsg></view>