(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["zhaopin/jobFilter"],{"1e9d":function(t,e,i){"use strict";(function(t){Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var i=getApp(),n={data:function(){return{isLoading:!0,loadRetryCount:0,selectedCities:[],areaList:[],provinceList:[],cityList:[],districtList:[],provinceIndex:0,cityIndex:0,districtIndex:0,currentProvince:null,currentCity:null,currentDistrict:null,currentProvinceName:"",currentCityName:"",currentDistrictName:"",showAreaPicker:!1,salaryOptions:[{value:1,label:"3千~5千",min:3e3,max:5e3},{value:2,label:"4千~6千",min:4e3,max:6e3},{value:3,label:"5千~7千",min:5e3,max:7e3},{value:4,label:"6千~8千",min:6e3,max:8e3},{value:5,label:"7千~9千",min:7e3,max:9e3},{value:6,label:"8千~1万",min:8e3,max:1e4},{value:7,label:"9千~1.1万",min:9e3,max:11e3},{value:8,label:"1万~1.2万",min:1e4,max:12e3},{value:9,label:"1.2万~1.5万",min:12e3,max:15e3},{value:10,label:"不限",min:2e3,max:1e5}],selectedSalary:null,salaryRange:[2e3,1e5],salaryStartIndex:0,salaryEndIndex:98,salaryStartOptions:Array.from({length:99},(function(t,e){return 1e3*e+2e3})).filter((function(t){return t<=1e5})),salaryEndOptions:Array.from({length:99},(function(t,e){return 1e3*e+2e3})).filter((function(t){return t<=1e5})),workModeOptions:[{value:1,label:"长期工"},{value:2,label:"短期工"},{value:3,label:"临时工"}],selectedWorkMode:null,paymentOptions:[{value:1,label:"月结"},{value:2,label:"周结"},{value:3,label:"日结"},{value:4,label:"完工结"}],selectedPayment:null,workTimeOptions:[{value:1,label:"白班",start:8,end:17},{value:2,label:"夜班",start:18,end:8},{value:3,label:"两班倒",start:0,end:24},{value:4,label:"不限",start:0,end:24}],selectedWorkTime:null,workTimeRange:[9,18],ageValue:""}},watch:{jobCategories:{handler:function(t){console.log("jobCategories数据变化：",t),t&&t.length>0&&(this.isLoading=!1)},immediate:!0}},onLoad:function(){console.log("页面加载"),this.getJobCategories(),this.getAreaList()},onShow:function(){console.log("页面显示"),0===this.jobCategories.length&&!this.isLoading&&this.loadRetryCount<3&&(console.log("数据为空，尝试重新加载"),this.loadRetryCount++,this.getJobCategories())},onReady:function(){console.log("页面就绪"),0!==this.jobCategories.length||this.isLoading||(console.log("页面就绪时数据为空，尝试重新加载"),this.getJobCategories())},methods:{getJobCategories:function(){var e=this;this.isLoading=!0,t.showLoading({title:"加载中...",mask:!0}),console.log("开始获取职位类别数据"),i.post("apiZhaopin/getTypeList",{pid:-1},(function(i){if(t.hideLoading(),e.isLoading=!1,console.log("接口返回数据：",i),1===i.status&&i.data&&Array.isArray(i.data)){var n=i.data.map((function(t){var e;return console.log("处理类别项：",t),{id:t.id,name:t.name,description:null!==(e=t.children)&&void 0!==e&&e.length?"包含".concat(t.children.length,"个细分职位"):"暂无子分类",icon:"/static/icons/job-".concat(t.id,".png"),children:t.children||[],sort:t.sort||0}})).sort((function(t,e){return e.sort-t.sort}));e.jobCategories=n,console.log("处理后的职位类型数据：",e.jobCategories),e.$forceUpdate(),setTimeout((function(){0===e.jobCategories.length&&(console.error("数据加载异常：数据为空"),t.showToast({title:"数据加载异常，请重试",icon:"none"}))}),100)}else console.error("获取职位类型失败：",i.msg),t.showToast({title:i.msg||"获取职位类型失败",icon:"none"})}))},onProvinceChange:function(t){var e=t.detail.value;this.provinceIndex=e,this.currentProvince=this.provinceList[e],this.currentProvinceName=this.currentProvince.name,this.cityList=this.currentProvince.children||[],this.cityIndex=0,this.currentCity=null,this.currentCityName="",this.districtList=[],this.districtIndex=0,this.currentDistrict=null,this.currentDistrictName="",this.cityList.length>0&&(this.currentCity=this.cityList[0],this.currentCityName=this.currentCity.name,this.districtList=this.currentCity.children||[],this.districtList.length>0&&(this.currentDistrict=this.districtList[0],this.currentDistrictName=this.currentDistrict.name))},onCityChange:function(t){var e=t.detail.value;this.cityIndex=e,this.currentCity=this.cityList[e],this.currentCityName=this.currentCity.name,this.districtList=this.currentCity.children||[],this.districtIndex=0,this.currentDistrict=null,this.currentDistrictName="",this.districtList.length>0&&(this.currentDistrict=this.districtList[0],this.currentDistrictName=this.currentDistrict.name)},onDistrictChange:function(t){var e=t.detail.value;this.districtIndex=e,this.currentDistrict=this.districtList[e],this.currentDistrictName=this.currentDistrict.name},addSelectedCity:function(){if(this.currentProvince&&this.currentCity&&this.currentDistrict){var e="".concat(this.currentProvinceName).concat(this.currentCityName).concat(this.currentDistrictName);this.selectedCities.length>=5?t.showToast({title:"最多选择5个城市",icon:"none"}):this.selectedCities.includes(e)?t.showToast({title:"该地址已添加",icon:"none"}):(this.selectedCities.push(e),t.showToast({title:"添加成功",icon:"none"}))}},showCityPicker:function(){this.selectedCities.length>=5?t.showToast({title:"最多选择5个城市",icon:"none"}):0===this.provinceList.length&&this.getAreaList()},getAreaList:function(){var e=this;t.showLoading({title:"加载中...",mask:!0}),i.get("apiZhaopin/getAreaList",{},(function(i){t.hideLoading(),1===i.status?(e.areaList=i.data,e.provinceList=i.data,e.provinceList.length>0&&(e.currentProvince=e.provinceList[0],e.currentProvinceName=e.currentProvince.name,e.cityList=e.currentProvince.children||[],e.cityList.length>0&&(e.currentCity=e.cityList[0],e.currentCityName=e.currentCity.name,e.districtList=e.currentCity.children||[],e.districtList.length>0&&(e.currentDistrict=e.districtList[0],e.currentDistrictName=e.currentDistrict.name)))):t.showToast({title:i.msg||"获取地区列表失败",icon:"none"})}))},removeCity:function(t){this.selectedCities.splice(t,1)},selectSalary:function(t){this.selectedSalary=t;var e=this.salaryOptions.find((function(e){return e.value===t}));e&&(this.salaryRange=[e.min,e.max],this.salaryStartIndex=this.salaryStartOptions.indexOf(e.min),this.salaryEndIndex=this.salaryEndOptions.indexOf(e.max))},handleSalaryPickerChange:function(t,e){var i=this.salaryStartOptions[t.detail.value];if(0===e)if(this.salaryStartIndex=t.detail.value,i<this.salaryRange[1])this.salaryRange.splice(0,1,i);else{var n=Math.min(i+5e3,1e5);this.salaryRange.splice(1,1,n),this.salaryEndIndex=this.salaryEndOptions.indexOf(n)}else if(1===e){this.salaryEndIndex=t.detail.value;var a=this.salaryEndOptions[t.detail.value];if(a>this.salaryRange[0])this.salaryRange.splice(1,1,a);else{var r=Math.max(a-5e3,2e3);this.salaryRange.splice(0,1,r),this.salaryStartIndex=this.salaryStartOptions.indexOf(r)}}this.selectedSalary=null},selectWorkMode:function(t){this.selectedWorkMode=t},selectPayment:function(t){this.selectedPayment=t},selectWorkTime:function(t){this.selectedWorkTime=t;var e=this.workTimeOptions.find((function(e){return e.value===t}));e&&(this.workTimeRange=[e.start,e.end])},validateAndSubmit:function(){return null===this.selectedWorkMode||null===this.selectedPayment?(t.showToast({title:"请完善工作要求信息",icon:"none"}),!1):0!==this.selectedCities.length||(t.showToast({title:"请至少选择期望城市",icon:"none"}),!1)},submitFilter:function(){var e,n,a,r=this;if(this.validateAndSubmit()){var s=(null===(e=this.workModeOptions.find((function(t){return t.value===r.selectedWorkMode})))||void 0===e?void 0:e.label)||"",o=(null===(n=this.paymentOptions.find((function(t){return t.value===r.selectedPayment})))||void 0===n?void 0:n.label)||"",l=this.selectedSalary?"".concat(this.formatSalary(this.salaryRange[0]),"-").concat(this.formatSalary(this.salaryRange[1])):"不限",c=this.selectedWorkTime?null===(a=this.workTimeOptions.find((function(t){return t.value===r.selectedWorkTime})))||void 0===a?void 0:a.label:"不限",u="".concat(this.ageValue,"岁"),h={workMode:s,payment:o,cities:this.selectedCities,salary:l,workTime:c,age:u};t.showLoading({title:"匹配中...",mask:!0});i.post("apiZhaopin/jobMatch",h,(function(e){t.hideLoading(),1===e.status?(t.setStorageSync("jobMatchFilterData",h),t.setStorageSync("jobMatchResult",e),t.navigateTo({url:"/zhaopin/jobMatch?from=filter"})):t.showToast({title:e.msg||"匹配失败，请重试",icon:"none"})}))}},closeAreaPicker:function(){this.showAreaPicker=!1,this.currentProvince=null,this.currentCity=null,this.currentDistrict=null},formatSalary:function(t){return t>=1e4?(t/1e4).toFixed(1).replace(/\.0$/,"")+"万元":t+"元"},handleAgeInputChange:function(t,e){var i=parseInt(e);if(!isNaN(i)){var n=Math.min(Math.max(i,16),70);this.ageRange.splice(t,1,n),0===t&&this.ageRange[0]>this.ageRange[1]?this.ageRange.splice(1,1,this.ageRange[0]):1===t&&this.ageRange[1]<this.ageRange[0]&&this.ageRange.splice(0,1,this.ageRange[1])}}}};e.default=n}).call(this,i("df3c")["default"])},2330:function(t,e,i){"use strict";i.r(e);var n=i("ab95"),a=i("ae49");for(var r in a)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(r);i("508c");var s=i("828b"),o=Object(s["a"])(a["default"],n["b"],n["c"],!1,null,null,null,!1,n["a"],void 0);e["default"]=o.exports},"508c":function(t,e,i){"use strict";var n=i("9820"),a=i.n(n);a.a},9820:function(t,e,i){},ab95:function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return a})),i.d(e,"a",(function(){}));var n=function(){var t=this,e=t.$createElement,i=(t._self._c,t.t("color1")),n=t.selectedCities.length,a=t.t("color1"),r=t.t("color1"),s=t.__map(t.workModeOptions,(function(e,i){var n=t.__get_orig(e),a=t.selectedWorkMode===e.value?t.t("color1"):null;return{$orig:n,m3:a}})),o=t.t("color1"),l=t.__map(t.paymentOptions,(function(e,i){var n=t.__get_orig(e),a=t.selectedPayment===e.value?t.t("color1"):null;return{$orig:n,m5:a}})),c=t.t("color1"),u=t.__map(t.workTimeOptions,(function(e,i){var n=t.__get_orig(e),a=t.selectedWorkTime===e.value?t.t("color1"):null;return{$orig:n,m7:a}})),h=t.t("color1"),d=t.formatSalary(t.salaryRange[0]),m=t.formatSalary(t.salaryRange[1]),g=t.salaryStartOptions.map((function(e){return t.formatSalary(e)})),f=t.formatSalary(t.salaryRange[0]),y=t.salaryEndOptions.map((function(e){return t.formatSalary(e)})),v=t.formatSalary(t.salaryRange[1]),p=t.__map(t.salaryOptions,(function(e,i){var n=t.__get_orig(e),a=t.selectedSalary===e.value?t.t("color1"):null;return{$orig:n,m13:a}})),b=t.t("color1"),C=t.t("color1"),L=t.t("color1");t._isMounted||(t.e0=function(e){return t.handleSalaryPickerChange(e,0)},t.e1=function(e){return t.handleSalaryPickerChange(e,1)}),t.$mp.data=Object.assign({},{$root:{m0:i,g0:n,m1:a,m2:r,l0:s,m4:o,l1:l,m6:c,l2:u,m8:h,m9:d,m10:m,g1:g,m11:f,g2:y,m12:v,l3:p,m14:b,m15:C,m16:L}})},a=[]},ae49:function(t,e,i){"use strict";i.r(e);var n=i("1e9d"),a=i.n(n);for(var r in n)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(r);e["default"]=a.a},d60e:function(t,e,i){"use strict";(function(t,e){var n=i("47a9");i("06e9");n(i("3240"));var a=n(i("2330"));t.__webpack_require_UNI_MP_PLUGIN__=i,e(a.default)}).call(this,i("3223")["default"],i("df3c")["createPage"])}},[["d60e","common/runtime","common/vendor"]]]);