require('../common/vendor.js');(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["adminExt/restaurant/shoporderdetail"],{1825:function(e,t,n){},"2db6":function(e,t,n){"use strict";n.r(t);var o=n("a96d"),r=n.n(o);for(var i in o)["default"].indexOf(i)<0&&function(e){n.d(t,e,(function(){return o[e]}))}(i);t["default"]=r.a},"340a":function(e,t,n){"use strict";n.d(t,"b",(function(){return r})),n.d(t,"c",(function(){return i})),n.d(t,"a",(function(){return o}));var o={uniPopup:function(){return Promise.all([n.e("common/vendor"),n.e("components/uni-popup/uni-popup")]).then(n.bind(null,"ca44a"))},uniPopupDialog:function(){return n.e("components/uni-popup-dialog/uni-popup-dialog").then(n.bind(null,"267c"))},loading:function(){return n.e("components/loading/loading").then(n.bind(null,"ceaa"))},dpTabbar:function(){return n.e("components/dp-tabbar/dp-tabbar").then(n.bind(null,"b875"))}},r=function(){var e=this,t=e.$createElement,n=(e._self._c,e.isload?e.__map(e.prolist,(function(t,n){var o=e.__get_orig(t),r=t.ggtext&&t.ggtext.length,i=t.jlprice?parseFloat(parseFloat(t.sell_price)+parseFloat(t.jlprice)).toFixed(2):null;return{$orig:o,g0:r,g1:i}})):null),o=e.isload?e.t("会员"):null,r=e.isload&&e.detail.leveldk_money>0?e.t("会员"):null,i=e.isload&&e.detail.coupon_money>0?e.t("优惠券"):null,s=e.isload&&e.detail.scoredk>0?e.t("积分"):null;e.$mp.data=Object.assign({},{$root:{l0:n,m0:o,m1:r,m2:i,m3:s}})},i=[]},3969:function(e,t,n){"use strict";var o=n("1825"),r=n.n(o);r.a},8644:function(e,t,n){"use strict";n.r(t);var o=n("340a"),r=n("2db6");for(var i in r)["default"].indexOf(i)<0&&function(e){n.d(t,e,(function(){return r[e]}))}(i);n("3969");var s=n("828b"),a=Object(s["a"])(r["default"],o["b"],o["c"],!1,null,null,null,!1,o["a"],void 0);t["default"]=a.exports},"9ac9":function(e,t,n){"use strict";(function(e,t){var o=n("47a9");n("06e9");o(n("3240"));var r=o(n("8644"));e.__webpack_require_UNI_MP_PLUGIN__=n,t(r.default)}).call(this,n("3223")["default"],n("df3c")["createPage"])},a96d:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=getApp(),r=null,i={data:function(){return{opt:{},loading:!1,isload:!1,menuindex:-1,pre_url:o.globalData.pre_url,expressdata:[],express_index:0,express_no:"",prodata:"",djs:"",detail:"",prolist:"",shopset:"",storeinfo:"",lefttime:"",codtxt:"",peisonguser:[],peisonguser2:[],index2:0,returnProlist:[],refundNum:[],refundTotalprice:0,refundReason:""}},onLoad:function(e){this.opt=o.getopts(e),this.getdata()},onPullDownRefresh:function(){this.getdata()},onUnload:function(){clearInterval(r)},methods:{getdata:function(){var e=this;e.loading=!0,o.get("ApiAdminRestaurantShopOrder/detail",{id:e.opt.id},(function(t){e.loading=!1,e.expressdata=t.expressdata,e.detail=t.detail,e.prolist=t.prolist,e.shopset=t.shopset,e.storeinfo=t.storeinfo,e.lefttime=t.lefttime,e.codtxt=t.codtxt,t.lefttime>0&&(r=setInterval((function(){e.lefttime=e.lefttime-1,e.getdjs()}),1e3)),e.loaded()}))},getdjs:function(){var e=this.lefttime;if(e<=0)this.djs="00时00分00秒";else{var t=Math.floor(e/3600),n=Math.floor((e-3600*t)/60),o=e-3600*t-60*n,r=(t<10?"0":"")+t+"时"+(n<10?"0":"")+n+"分"+(o<10?"0":"")+o+"秒";this.djs=r}},setremark:function(){this.$refs.dialogSetremark.open()},setremarkconfirm:function(e,t){this.$refs.dialogSetremark.close();var n=this;o.post("ApiAdminOrder/setremark",{type:"restaurant_shop",orderid:n.detail.id,content:t},(function(e){o.success(e.msg),setTimeout((function(){n.getdata()}),1e3)}))},fahuo:function(){this.$refs.dialogExpress.open()},dialogExpressClose:function(){this.$refs.dialogExpress.close()},expresschange:function(e){this.express_index=e.detail.value},setexpressno:function(e){this.express_no=e.detail.value},confirmfahuo:function(){this.$refs.dialogExpress.close();var e=this,t=this.expressdata[this.express_index];o.post("ApiAdminOrder/sendExpress",{type:"restaurant_shop",orderid:e.detail.id,express_no:e.express_no,express_com:t},(function(t){o.success(t.msg),setTimeout((function(){e.getdata()}),1e3)}))},ispay:function(e){var t=this,n=e.currentTarget.dataset.id;o.confirm("确定要改为已支付吗?",(function(){o.showLoading("提交中"),o.post("ApiAdminOrder/ispay",{type:"restaurant_shop",orderid:n},(function(e){o.showLoading(!1),o.success(e.msg),setTimeout((function(){t.getdata()}),1e3)}))}))},delOrder:function(e){var t=e.currentTarget.dataset.id;o.showLoading("删除中"),o.confirm("确定要删除该订单吗?",(function(){o.post("ApiAdminOrder/delOrder",{type:"restaurant_shop",orderid:t},(function(e){o.showLoading(!1),o.success(e.msg),setTimeout((function(){o.goto("shoporder")}),1e3)}))}))},closeOrder:function(e){var t=this,n=e.currentTarget.dataset.id;o.confirm("确定要关闭该订单吗?",(function(){o.showLoading("提交中"),o.post("ApiAdminOrder/closeOrder",{type:"restaurant_shop",orderid:n},(function(e){o.showLoading(!1),o.success(e.msg),setTimeout((function(){t.getdata()}),1e3)}))}))},refundnopass:function(e){var t=this,n=e.currentTarget.dataset.id;o.confirm("确定要驳回退款申请吗?",(function(){o.showLoading("提交中"),o.post("ApiAdminOrder/refundnopass",{type:"restaurant_shop",orderid:n},(function(e){o.showLoading(!1),o.success(e.msg),setTimeout((function(){t.getdata()}),1e3)}))}))},refundpass:function(e){var t=this,n=e.currentTarget.dataset.id;o.confirm("确定要审核通过并退款吗?",(function(){o.showLoading("提交中"),o.post("ApiAdminOrder/refundpass",{type:"restaurant_shop",orderid:n},(function(e){o.showLoading(!1),o.success(e.msg),setTimeout((function(){t.getdata()}),1e3)}))}))},print:function(e){var t=e.currentTarget.dataset.id;o.showLoading("打印中"),o.post("ApiAdminOrder/print",{type:"restaurant_takeaway",orderid:t},(function(e){o.showLoading(!1),o.success(e.msg)}))},refundinit:function(e){var t=this;t.loading=!0,o.post("ApiAdminRestaurantShopOrder/refundProlist",{orderid:t.detail.id},(function(e){t.loading=!1;var n=e.prolist;for(var o in t.returnProlist=e.prolist,t.refundTotalprice=e.detail.totalprice,n)t.refundNum.push({ogid:n[o].id,num:n[o].can_num});t.$refs.dialogRefund.open()}))},retundInput:function(e){var t=e.detail.value,n=e.currentTarget.dataset,r=n.max,i=n.ogid,s=this.returnProlist,a=this.refundNum,d=0;if(t=t||0,t>r)return o.error("请输入正确的数量");for(var u in a){a[u].ogid==i&&(a[u].num=t),console.log(s[u]);var l=s[u];a[u].num==l.num?d+=parseFloat(s[u].real_totalprice):d+=a[u].num*parseFloat(s[u].real_totalprice)/s[u].num}d=parseFloat(d),d=d.toFixed(2),this.refundTotalprice=d},refundMoneyReason:function(e){this.refundReason=e.detail.value},refundMoney:function(e){this.refundTotalprice=e.detail.value},dialogRefundClose:function(){this.returnProlist=[],this.refundReason="",this.$refs.dialogRefund.close()},gotoRefundMoney:function(){var e=this;console.log(e.refundNum,11111),o.confirm("确定要退款吗?",(function(){e.$refs.dialogRefund.close(),o.showLoading("提交中"),o.post("ApiAdminRestaurantShopOrder/refund",{orderid:e.detail.id,refundNum:e.refundNum,reason:e.refundReason,money:e.refundTotalprice},(function(t){0!=t.status?(o.showLoading(!1),o.success(t.msg),setTimeout((function(){e.getdata()}),1e3)):o.error(t.msg)}))}))}}};t.default=i}},[["9ac9","common/runtime","common/vendor"]]]);