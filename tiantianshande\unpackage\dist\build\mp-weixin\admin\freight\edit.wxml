<view class="container"><form data-event-opts="{{[['submit',[['saveTemplate',['$event']]]]]}}" bindsubmit="__e"><view class="form-item"><text class="item-label">配送方式</text><view class="radio-group"><label class="radio-item"><radio checked="{{formData.pstype===0}}" data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" bindtap="__e"></radio><text>普通快递</text></label><label class="radio-item"><radio checked="{{formData.pstype===1}}" data-event-opts="{{[['tap',[['e1',['$event']]]]]}}" bindtap="__e"></radio><text>到店自提</text></label><label class="radio-item"><radio checked="{{formData.pstype===2}}" data-event-opts="{{[['tap',[['e2',['$event']]]]]}}" bindtap="__e"></radio><text>同城配送</text></label></view></view><view class="form-item"><text class="item-label">显示名称</text><input class="input" placeholder="请输入模板名称" data-event-opts="{{[['input',[['__set_model',['$0','name','$event',[]],['formData']]]]]}}" value="{{formData.name}}" bindinput="__e"/></view><view class="form-item"><text class="item-label">计价方式</text><view class="radio-group"><label class="radio-item"><radio checked="{{formData.type===1}}" data-event-opts="{{[['tap',[['handleTypeChange',[1]]]]]}}" catchtap="__e"></radio><text data-event-opts="{{[['tap',[['handleTypeChange',[1]]]]]}}" catchtap="__e">按重量(克)</text></label><label class="radio-item"><radio checked="{{formData.type===2}}" data-event-opts="{{[['tap',[['handleTypeChange',[2]]]]]}}" catchtap="__e"></radio><text data-event-opts="{{[['tap',[['handleTypeChange',[2]]]]]}}" catchtap="__e">按件数(个)</text></label></view></view><view class="rules-section"><view class="table-header"><text>区域</text><text>是否配送</text><text>{{formData.type===1?'首重重量(克)':'首件数量(个)'}}</text><text>{{formData.type===1?'首重费用(元)':'首件费用(元)'}}</text><text>{{formData.type===1?'续重重量(克)':'续件数量(个)'}}</text><text>{{formData.type===1?'续重费用(元)':'续件费用(元)'}}</text><text>操作</text></view><view class="table-body"><block wx:for="{{$root.l0}}" wx:for-item="area" wx:for-index="index" wx:key="index"><view class="table-row"><view data-event-opts="{{[['tap',[['handleRegionSelect',[index]]]]]}}" class="{{['table-cell','region-cell',(area.$orig.region!=='全国(默认运费)')?'clickable':'']}}" bindtap="__e"><text class="{{[(area.$orig.region==='全国(默认运费)')?'default-region':'']}}">{{area.m0}}</text><block wx:if="{{area.$orig.region!=='全国(默认运费)'}}"><text class="select-icon">选择</text></block></view><view class="table-cell"><switch checked="{{area.$orig.is_delivery}}" data-event-opts="{{[['change',[['toggleDelivery',[index]]]]]}}" bindchange="__e"></switch></view><view class="table-cell"><input type="number" data-event-opts="{{[['input',[['__set_model',['$0','fristweight','$event',[]],[[['formData.pricedata','',index]]]]]]]}}" value="{{area.$orig.fristweight}}" bindinput="__e"/></view><view class="table-cell"><input type="number" data-event-opts="{{[['input',[['__set_model',['$0','fristprice','$event',[]],[[['formData.pricedata','',index]]]]]]]}}" value="{{area.$orig.fristprice}}" bindinput="__e"/></view><view class="table-cell"><input type="number" data-event-opts="{{[['input',[['__set_model',['$0','secondweight','$event',[]],[[['formData.pricedata','',index]]]]]]]}}" value="{{area.$orig.secondweight}}" bindinput="__e"/></view><view class="table-cell"><input type="number" data-event-opts="{{[['input',[['__set_model',['$0','secondprice','$event',[]],[[['formData.pricedata','',index]]]]]]]}}" value="{{area.$orig.secondprice}}" bindinput="__e"/></view><view class="table-cell"><block wx:if="{{area.$orig.region!=='全国(默认运费)'}}"><text data-event-opts="{{[['tap',[['removeArea',[index]]]]]}}" class="delete-btn" bindtap="__e">删除</text></block></view></view></block></view><block wx:if="{{$root.g0<20}}"><view data-event-opts="{{[['tap',[['addArea',['$event']]]]]}}" class="add-area-btn" bindtap="__e"><text class="icon">+</text><text>添加区域</text></view></block></view><view class="form-item"><text class="item-label">满额包邮</text><view class="switch-group"><switch checked="{{formData.freeset===1}}" data-event-opts="{{[['change',[['e3',['$event']]]]]}}" bindchange="__e"></switch><block wx:if="{{formData.freeset===1}}"><input class="price-input" type="digit" placeholder="请输入包邮金额" data-event-opts="{{[['input',[['__set_model',['$0','free_price','$event',[]],['formData']]]]]}}" value="{{formData.free_price}}" bindinput="__e"/></block><block wx:if="{{formData.freeset===1}}"><text class="unit">元</text></block></view></view><view class="form-item"><text class="item-label">满额起送</text><view class="switch-group"><switch checked="{{formData.minpriceset===1}}" data-event-opts="{{[['change',[['e4',['$event']]]]]}}" bindchange="__e"></switch><block wx:if="{{formData.minpriceset===1}}"><input class="price-input" type="digit" placeholder="请输入起送金额" data-event-opts="{{[['input',[['__set_model',['$0','minprice','$event',[]],['formData']]]]]}}" value="{{formData.minprice}}" bindinput="__e"/></block><block wx:if="{{formData.minpriceset===1}}"><text class="unit">元</text></block></view></view><button class="save-btn" form-type="submit">保存</button></form></view>