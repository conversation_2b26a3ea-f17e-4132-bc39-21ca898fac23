(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["zhaopin/search"],{1043:function(t,e,a){"use strict";a.d(e,"b",(function(){return i})),a.d(e,"c",(function(){return s})),a.d(e,"a",(function(){return r}));var r={nodata:function(){return a.e("components/nodata/nodata").then(a.bind(null,"101c"))},nomore:function(){return a.e("components/nomore/nomore").then(a.bind(null,"3892"))},loading:function(){return a.e("components/loading/loading").then(a.bind(null,"ceaa"))},dpTabbar:function(){return a.e("components/dp-tabbar/dp-tabbar").then(a.bind(null,"b875"))},popmsg:function(){return a.e("components/popmsg/popmsg").then(a.bind(null,"2bf2"))}},i=function(){var t=this,e=t.$createElement,a=(t._self._c,t.t("color1")),r=t.showFilterPanel?t.__map(t.categoryList,(function(e,a){var r=t.__get_orig(e),i=t.searchParams.category_id===e.id?t.t("color1rgb"):null,s=t.searchParams.category_id===e.id?t.t("color1"):null,n=t.searchParams.category_id===e.id?t.t("color1"):null;return{$orig:r,m1:i,m2:s,m3:n}})):null,i=t.showFilterPanel?t.__map(t.jobTypes,(function(e,a){var r=t.__get_orig(e),i=t.searchParams.type_id===e.id?t.t("color1rgb"):null,s=t.searchParams.type_id===e.id?t.t("color1"):null,n=t.searchParams.type_id===e.id?t.t("color1"):null;return{$orig:r,m4:i,m5:s,m6:n}})):null,s=t.showFilterPanel?t.__map(t.educationList,(function(e,a){var r=t.__get_orig(e),i=t.searchParams.education===e?t.t("color1rgb"):null,s=t.searchParams.education===e?t.t("color1"):null,n=t.searchParams.education===e?t.t("color1"):null;return{$orig:r,m7:i,m8:s,m9:n}})):null,n=t.showFilterPanel?t.__map(t.experienceList,(function(e,a){var r=t.__get_orig(e),i=t.searchParams.experience===e?t.t("color1rgb"):null,s=t.searchParams.experience===e?t.t("color1"):null,n=t.searchParams.experience===e?t.t("color1"):null;return{$orig:r,m10:i,m11:s,m12:n}})):null,o=t.showFilterPanel?t.__map(t.salaryRanges,(function(e,a){var r=t.__get_orig(e),i=t.searchParams.salary_min===e.min&&t.searchParams.salary_max===e.max?t.t("color1rgb"):null,s=t.searchParams.salary_min===e.min&&t.searchParams.salary_max===e.max?t.t("color1"):null,n=t.searchParams.salary_min===e.min&&t.searchParams.salary_max===e.max?t.t("color1"):null;return{$orig:r,m13:i,m14:s,m15:n}})):null,c=t.showFilterPanel?t.__map(t.benefitsList,(function(e,a){var r=t.__get_orig(e),i=t.searchParams.benefits.includes(e),s=t.searchParams.benefits.includes(e),n=s?t.t("color1rgb"):null,o=s?t.t("color1"):null,c=s?t.t("color1"):null;return{$orig:r,g0:i,g1:s,m16:n,m17:o,m18:c}})):null,l=t.showFilterPanel?t.t("color1"):null,u=t.t("color1"),h=t.__map(t.jobList,(function(e,a){var r=t.__get_orig(e),i=e.work_time_start.substring(0,5),s=e.work_time_end.substring(0,5),n=e.update_time.substring(0,10);return{$orig:r,g2:i,g3:s,g4:n}}));t.$mp.data=Object.assign({},{$root:{m0:a,l0:r,l1:i,l2:s,l3:n,l4:o,l5:c,m19:l,m20:u,l6:h}})},s=[]},"3c65":function(t,e,a){"use strict";a.r(e);var r=a("1043"),i=a("d8c4");for(var s in i)["default"].indexOf(s)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(s);a("d9e3");var n=a("828b"),o=Object(n["a"])(i["default"],r["b"],r["c"],!1,null,"8274ff22",null,!1,r["a"],void 0);e["default"]=o.exports},"7b11":function(t,e,a){},"7bfd4":function(t,e,a){"use strict";(function(t,e){var r=a("47a9");a("06e9");r(a("3240"));var i=r(a("3c65"));t.__webpack_require_UNI_MP_PLUGIN__=a,e(i.default)}).call(this,a("3223")["default"],a("df3c")["createPage"])},b92f:function(t,e,a){"use strict";var r=a("47a9");Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var i=r(a("7ca3")),s=r(a("34cf")),n=r(a("af34"));function o(t,e){var a=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),a.push.apply(a,r)}return a}function c(t){for(var e=1;e<arguments.length;e++){var a=null!=arguments[e]?arguments[e]:{};e%2?o(Object(a),!0).forEach((function(e){(0,i.default)(t,e,a[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(a)):o(Object(a)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(a,e))}))}return t}var l=getApp(),u={data:function(){return{searchParams:{keyword:"",category_id:"",type_id:"",education:"",experience:"",salary_min:"",salary_max:"",province:"",city:"",district:"",benefits:[],sort_field:"time",sort_type:"desc",page:1,limit:10},showFilterPanel:!1,currentSort:"time",sortType:"desc",jobList:[],statistics:null,isLoading:!1,noMore:!1,isRefreshing:!1,categoryList:[],jobTypes:[],educationList:["不限","高中","专科","本科","硕士","博士"],experienceList:["不限","应届生","1-3年","3-5年","5-10年","10年以上"],salaryRanges:[{min:"",max:"",text:"不限"},{min:0,max:5e3,text:"5K以下"},{min:5e3,max:1e4,text:"5-10K"},{min:1e4,max:15e3,text:"10-15K"},{min:15e3,max:2e4,text:"15-20K"},{min:2e4,max:3e4,text:"20-30K"},{min:3e4,max:5e4,text:"30-50K"},{min:5e4,max:"",text:"50K以上"}],benefitsList:["五险一金","年终奖","加班补助","餐补","交通补助","住房补贴","节日福利","团建活动"]}},computed:{topSalaryRange:function(){var t;if(null===(t=this.statistics)||void 0===t||!t.salary_distribution)return"";var e=Object.entries(this.statistics.salary_distribution);return e.sort((function(t,e){return e[1]-t[1]}))[0][0]},topSalaryPercentage:function(){var t;if(null===(t=this.statistics)||void 0===t||!t.salary_distribution)return 0;var e=Object.values(this.statistics.salary_distribution),a=e.reduce((function(t,e){return t+e}),0),r=Math.max.apply(Math,e);return Math.round(r/a*100)},topExperience:function(){var t;if(null===(t=this.statistics)||void 0===t||!t.experience_distribution)return"";var e=Object.entries(this.statistics.experience_distribution);return e.sort((function(t,e){return e[1]-t[1]}))[0][0]},topExperiencePercentage:function(){var t;if(null===(t=this.statistics)||void 0===t||!t.experience_distribution)return 0;var e=Object.values(this.statistics.experience_distribution),a=e.reduce((function(t,e){return t+e}),0),r=Math.max.apply(Math,e);return Math.round(r/a*100)}},onLoad:function(t){t.keyword&&(this.searchParams.keyword=decodeURIComponent(t.keyword)),this.getInitialData(),this.searchJobs()},methods:{getInitialData:function(){var t=this;l.get("ApiZhaopin/getSearchInitData",{},(function(e){1===e.status&&(t.categoryList=e.data.categories,t.jobTypes=e.data.job_types)}))},searchJobs:function(){var t=arguments.length>0&&void 0!==arguments[0]&&arguments[0];if(!this.isLoading){var e=this;this.isLoading=!0,t||(this.searchParams.page=1,this.jobList=[],this.noMore=!1),l.post("ApiZhaopin/searchPositions",this.searchParams,(function(a){e.isLoading=!1,e.isRefreshing=!1,1===a.status?(e.jobList=t?[].concat((0,n.default)(e.jobList),(0,n.default)(a.data.list)):a.data.list,e.statistics=a.data.statistics,e.noMore=a.data.list.length<e.searchParams.limit):l.error(a.msg)}))}},handleSearch:function(){this.showFilterPanel=!1,this.searchJobs()},handleSort:function(t){this.currentSort===t?this.sortType="desc"===this.sortType?"asc":"desc":(this.currentSort=t,this.sortType="desc"),this.searchParams.sort_field=t,this.searchParams.sort_type=this.sortType,this.searchJobs()},toggleFilterPanel:function(){this.showFilterPanel=!this.showFilterPanel},selectCategory:function(t){this.searchParams.category_id=this.searchParams.category_id===t.id?"":t.id},selectJobType:function(t){this.searchParams.type_id=this.searchParams.type_id===t.id?"":t.id},selectEducation:function(t){this.searchParams.education=this.searchParams.education===t?"":t},selectExperience:function(t){this.searchParams.experience=this.searchParams.experience===t?"":t},selectSalary:function(t){this.searchParams.salary_min===t.min&&this.searchParams.salary_max===t.max?(this.searchParams.salary_min="",this.searchParams.salary_max=""):(this.searchParams.salary_min=t.min,this.searchParams.salary_max=t.max)},handleRegionChange:function(t){var e=(0,s.default)(t.detail.value,3),a=e[0],r=e[1],i=e[2];this.searchParams.province=a,this.searchParams.city=r,this.searchParams.district=i},toggleBenefit:function(t){var e=this.searchParams.benefits.indexOf(t);e>-1?this.searchParams.benefits.splice(e,1):this.searchParams.benefits.push(t)},resetFilters:function(){this.searchParams=c(c({},this.searchParams),{},{category_id:"",type_id:"",education:"",experience:"",salary_min:"",salary_max:"",province:"",city:"",district:"",benefits:[]})},confirmFilters:function(){this.showFilterPanel=!1,this.searchJobs()},loadMore:function(){this.isLoading||this.noMore||(this.searchParams.page++,this.searchJobs(!0))},onRefresh:function(){this.isRefreshing=!0,this.searchJobs()},goToDetail:function(t){l.goto("/zhaopin/partdetails?id="+t)}}};e.default=u},d8c4:function(t,e,a){"use strict";a.r(e);var r=a("b92f"),i=a.n(r);for(var s in r)["default"].indexOf(s)<0&&function(t){a.d(e,t,(function(){return r[t]}))}(s);e["default"]=i.a},d9e3:function(t,e,a){"use strict";var r=a("7b11"),i=a.n(r);i.a}},[["7bfd4","common/runtime","common/vendor"]]]);