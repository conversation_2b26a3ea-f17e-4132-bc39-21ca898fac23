<view><block wx:if="{{isload}}"><block><view class="container"><view class="tab-box"><view data-event-opts="{{[['tap',[['changeRankType',[1]]]]]}}" class="{{['tab-item',(rankType===1)?'active':'']}}" bindtap="__e">成员排行</view><view data-event-opts="{{[['tap',[['changeRankType',[2]]]]]}}" class="{{['tab-item',(rankType===2)?'active':'']}}" bindtap="__e">进货排行</view></view><view class="filter-box"><block wx:if="{{rankType===1}}"><view class="filter-row"><picker value="{{productIndex}}" range="{{productList}}" range-key="name" data-event-opts="{{[['change',[['onProductChange',['$event']]]]]}}" bindchange="__e"><view class="picker-item"><text>{{getProductName}}</text><text class="icon-arrow"></text></view></picker><picker value="{{monthIndex}}" range="{{monthList}}" range-key="name" data-event-opts="{{[['change',[['onMonthChange',['$event']]]]]}}" bindchange="__e"><view class="picker-item"><text>{{getMonthName}}</text><text class="icon-arrow"></text></view></picker></view></block><block wx:if="{{rankType===2}}"><view class="filter-row"><picker value="{{monthIndex}}" range="{{monthList}}" range-key="name" data-event-opts="{{[['change',[['onMonthChange',['$event']]]]]}}" bindchange="__e"><view class="picker-item"><text>{{getMonthName}}</text><text class="icon-arrow"></text></view></picker></view></block></view><view class="rank-list"><block wx:for="{{rankList}}" wx:for-item="item" wx:for-index="index" wx:key="id"><block><block wx:if="{{rankType===1}}"><view class="rank-item"><view class="{{['avatar-box','rank-'+(index+1)]}}"><image class="avatar" src="{{item.avatar||'/static/images/default-avatar.png'}}" mode="aspectFill"></image></view><view class="info"><view class="name">{{item.nickname||'匿名用户'}}</view><view class="level">{{item.level_name||'-'}}</view></view><view class="data"><view class="num">{{item.total_num||0}}</view></view></view></block><block wx:if="{{rankType===2}}"><view class="rank-item"><view class="{{['rank-num','rank-'+(index+1)]}}"><text class="num">{{index+1}}</text><block wx:if="{{index<3}}"><view class="crown"></view></block></view><view class="info"><view data-event-opts="{{[['tap',[['goToProduct',['$0'],[[['rankList','id',item.id,'id']]]]]]]}}" class="name product-link" bindtap="__e">{{item.name||'-'}}</view><view class="shop-name">{{item.shop_name||'-'}}</view></view><view class="data"><view class="num">{{"销量："+(item.total_num||0)}}</view></view></view></block></block></block></view><block wx:if="{{loading}}"><view class="loading-more">加载中...</view></block><block wx:if="{{nomore}}"><view class="no-more">没有更多数据了</view></block></view></block></block><block wx:if="{{loading}}"><loading vue-id="2eaf179b-1" bind:__l="__l"></loading></block></view>