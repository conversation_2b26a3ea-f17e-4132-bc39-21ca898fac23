<view class="container"><block wx:if="{{isload}}"><block><form data-event-opts="{{[['submit',[['formSubmit',['$event']]]],['reset',[['formReset',['$event']]]]]}}" bindsubmit="__e" bindreset="__e"><view class="form"><view class="form-item"><text class="label">微信号</text><input class="input" type="text" placeholder="请输入微信号" placeholder-style="color:#BBBBBB;font-size:28rpx" name="weixin" value="{{userinfo.weixin}}"/></view></view><view class="form"><view class="form-item"><text class="label">支付宝账号</text><input class="input" type="text" placeholder="请输入支付宝账号" placeholder-style="color:#BBBBBB;font-size:28rpx" name="aliaccount" value="{{userinfo.aliaccount}}"/></view></view><view class="form"><view class="form-item"><text class="label">开户行</text><picker class="picker" mode="selector" name="bankname" value="0" range="{{banklist}}" data-event-opts="{{[['change',[['bindBanknameChange',['$event']]]]]}}" bindchange="__e"><block wx:if="{{bankname}}"><view>{{bankname}}</view></block><block wx:else><view>请选择开户行</view></block></picker></view><view class="form-item"><text class="label">持卡人姓名</text><input class="input" type="text" placeholder="请输入持卡人姓名" name="bankcarduser" placeholder-style="color:#BBBBBB;font-size:28rpx" value="{{userinfo.bankcarduser}}"/></view><view class="form-item"><text class="label">银行卡号</text><input class="input" type="text" placeholder="请输入银行卡号" name="bankcardnum" placeholder-style="color:#BBBBBB;font-size:28rpx" value="{{userinfo.bankcardnum}}"/></view></view><view class="form"><view class="form-item"><text class="label">短信验证码</text><input class="input" type="text" placeholder="请输入短信验证码" placeholder-style="color:#BBBBBB;font-size:28rpx" name="code" value=""/><view data-event-opts="{{[['tap',[['smscode',['$event']]]]]}}" class="code" bindtap="__e">{{smsdjs||'获取验证码'}}</view></view></view><button class="set-btn" form-type="submit">保 存</button></form></block></block><block wx:if="{{loading}}"><loading vue-id="7e50878a-1" bind:__l="__l"></loading></block><dp-tabbar vue-id="7e50878a-2" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="7e50878a-3" data-ref="popmsg" bind:__l="__l"></popmsg></view>