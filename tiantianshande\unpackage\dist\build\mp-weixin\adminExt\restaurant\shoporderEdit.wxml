<view><block wx:if="{{isload}}"><block><form data-event-opts="{{[['submit',[['subform',['$event']]]]]}}" bindsubmit="__e"><view class="form-box"><view class="form-item"><view class="f1">桌台<text style="color:red;"></text></view><view class="f2">{{info.tableName}}</view></view><view class="form-item"><view class="f1">订单号<text style="color:red;"></text></view><view class="f2">{{info.ordernum}}</view></view></view><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="form-box"><view class="form-item" style="height:80rpx;line-height:80rpx;"><view class="f1">菜品名称</view><view class="f2 product-name" style="font-weight:bold;">{{item.$orig.name}}</view></view><block wx:if="{{item.$orig.ggname}}"><view class="form-item" style="height:80rpx;line-height:80rpx;"><view class="f1">规格</view><view class="f2">{{item.$orig.ggname}}</view></view></block><block wx:if="{{item.g0}}"><view class="form-item" style="line-height:80rpx;"><view class="f1">套餐</view><view class="flex-col"><block wx:for="{{item.$orig.ggtext}}" wx:for-item="item2" wx:for-index="index"><block><text style="line-height:40rpx;text-align:left;">{{item2}}</text></block></block></view></view></block><view class="form-item" style="height:80rpx;line-height:80rpx;"><view class="f1">价格（元）</view><view class="f2"><input type="text" data-index="{{index}}" data-field="sell_price" name="{{'sell_price['+index+']'}}" placeholder="请填写销售价" placeholder-style="color:#888" data-event-opts="{{[['input',[['ginput',['$event']]]]]}}" value="{{item.$orig.sell_price}}" bindinput="__e"/></view></view><view class="form-item" style="height:80rpx;line-height:80rpx;"><block wx:if="{{item.$orig.product_type&&item.$orig.product_type==1}}"><view class="f1">重量</view></block><block wx:else><view class="f1">数量</view></block><block wx:if="{{item.$orig.product_type&&item.$orig.product_type==1}}"><view class="f2"><input type="text" data-index="{{index}}" data-field="num" name="{{'num['+index+']'}}" placeholder="请填写重量" placeholder-style="color:#888" data-event-opts="{{[['input',[['ginput',['$event']]]]]}}" value="{{item.$orig.num}}" bindinput="__e"/>斤</view></block><block wx:else><view class="f2"><input type="text" data-index="{{index}}" data-field="num" name="{{'num['+index+']'}}" placeholder="请填写数量" placeholder-style="color:#888" data-event-opts="{{[['input',[['ginput',['$event']]]]]}}" value="{{item.$orig.num}}" bindinput="__e"/></view></block></view></view></block><view class="form-box"><view class="form-item"><view class="f1">菜品总价</view><view class="f2"><input type="number" name="product_price" disabled="true" data-field="product_price" placeholder placeholder-style="color:#888" data-event-opts="{{[['input',[['input',['$event']]]]]}}" value="{{info.product_price}}" bindinput="__e"/></view></view><view class="form-item"><view class="f1">会员折扣</view><view class="f2"><input type="number" name="leveldk_money" data-field="leveldk_money" placeholder placeholder-style="color:#888" data-event-opts="{{[['input',[['input',['$event']]]]]}}" value="{{info.leveldk_money}}" bindinput="__e"/></view></view><view class="form-item"><view class="f1">优惠券抵扣</view><view class="f2"><input type="number" name="coupon_money" data-field="coupon_money" placeholder placeholder-style="color:#888" data-event-opts="{{[['input',[['input',['$event']]]]]}}" value="{{info.coupon_money}}" bindinput="__e"/></view></view><view class="form-item"><view class="f1">优惠券抵扣</view><view class="f2"><input type="number" name="scoredk_money" data-field="scoredk_money" placeholder placeholder-style="color:#888" data-event-opts="{{[['input',[['input',['$event']]]]]}}" value="{{info.scoredk_money}}" bindinput="__e"/></view></view><view class="form-item"><view class="f1">实付款</view><view class="f2"><input type="number" name="totalprice" data-field="totalprice" placeholder placeholder-style="color:#888" data-event-opts="{{[['input',[['input',['$event']]]]]}}" value="{{info.totalprice}}" bindinput="__e"/></view></view></view><button class="savebtn" style="{{('background:linear-gradient(90deg,'+$root.m0+' 0%,rgba('+$root.m1+',0.8) 100%)')}}" form-type="submit">提交</button><view style="height:50rpx;"></view></form></block></block><block wx:if="{{loading}}"><loading vue-id="c0666612-1" bind:__l="__l"></loading></block></view>