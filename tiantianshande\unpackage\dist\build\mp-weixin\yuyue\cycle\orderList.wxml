<view class="container"><block wx:if="{{isload}}"><block><dd-tab vue-id="624e0d7a-1" itemdata="{{['全部','进行中','已暂停','已完成','已取消']}}" itemst="{{['all','1','2','3','4']}}" st="{{st}}" isfixed="{{true}}" data-event-opts="{{[['^changetab',[['changetab']]]]}}" bind:changetab="__e" bind:__l="__l"></dd-tab><view style="width:100%;height:100rpx;"></view><view class="order-content"><block wx:for="{{datalist}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><view class="order-box" data-url="{{'/yuyue/cycle/orderDetail?id='+item.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="head"><text class="flex1">{{item.product_name}}</text><text class="{{['status-tag st'+item.status]}}">{{item.status_text}}</text></view><view class="content"><view class="img-wrapper" data-url="{{'/yuyue/cycle/productDetail?id='+item.product_id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e"><image src="{{item.product_pic}}" mode="aspectFill"></image></view><view class="detail"><view class="f1">{{"总期数："+item.total_period+"期"}}</view><view class="f2">{{"已完成："+item.served_period+"期"}}</view><view class="f3">{{"开始于："+item.start_date}}</view><view class="f3">{{"下次服务："+(item.next_service_date||'暂无')}}</view></view></view><view class="op"><view class="btn" data-url="{{'/yuyue/cycle/orderDetail?id='+item.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e">查看详情</view><block wx:if="{{item.status==1||item.status==2}}"><block><view class="{{['btn',item.status==1?'btn-pause':'btn-resume']}}" data-id="{{item.id}}" data-event-opts="{{[['tap',[['togglePause',['$event']]]]]}}" catchtap="__e">{{item.status==1?'暂停服务':'恢复服务'}}</view><view class="btn btn-cancel" data-id="{{item.id}}" data-event-opts="{{[['tap',[['cancelOrder',['$event']]]]]}}" catchtap="__e">取消服务</view></block></block></view></view></block></block></view><block wx:if="{{nomore}}"><nomore vue-id="624e0d7a-2" bind:__l="__l"></nomore></block><block wx:if="{{nodata}}"><nodata vue-id="624e0d7a-3" bind:__l="__l"></nodata></block></block></block><block wx:if="{{loading}}"><loading vue-id="624e0d7a-4" bind:__l="__l"></loading></block><dp-tabbar vue-id="624e0d7a-5" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="624e0d7a-6" data-ref="popmsg" bind:__l="__l"></popmsg></view>