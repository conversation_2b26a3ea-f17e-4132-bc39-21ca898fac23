<view class="container"><block wx:if="{{isload}}"><block><dd-tab vue-id="7d9fb6d4-1" itemdata="{{['全部','待入住','已到店','已离店','退款']}}" itemst="{{['all','2','3','4','10']}}" st="{{st}}" isfixed="{{true}}" data-event-opts="{{[['^changetab',[['changetab']]]]}}" bind:changetab="__e" bind:__l="__l"></dd-tab><view style="width:100%;height:100rpx;"></view><view class="topsearch flex-y-center"><view class="f1 flex-y-center"><image class="img" src="{{pre_url+'/static/img/search_ico.png'}}"></image><input placeholder="输入关键字搜索" placeholder-style="font-size:24rpx;color:#C2C2C2" data-event-opts="{{[['confirm',[['searchConfirm',['$event']]]]]}}" value="{{keyword}}" bindconfirm="__e"/></view></view><view class="order-content"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><view class="order-box" data-url="{{'orderdetail?id='+item.$orig.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="head"><view class="f1"><image src="{{pre_url+'/static/img/ico-shop.png'}}"></image>{{''+item.$orig.hotel.name}}</view><view class="flex1"></view><block wx:if="{{item.$orig.status==0}}"><text class="st0">待付款</text></block><block wx:if="{{item.$orig.status==1}}"><text class="st1">待确认</text></block><block wx:if="{{item.$orig.status==2}}"><text class="st2">待入住</text></block><block wx:if="{{item.$orig.status==3}}"><text class="st3">已到店</text></block><block wx:if="{{item.$orig.status==4}}"><text class="st4">已离店</text></block><block wx:if="{{item.$orig.status==-1}}"><text class="st4">已关闭</text></block></view><view class="content"><view><image src="{{item.$orig.pic}}"></image></view><view class="detail"><text class="t1">{{item.$orig.totalnum+text['间']+", "+item.$orig.title}}</text><text class="t2" style="color:#666;">{{item.$orig.in_date+" - "+item.$orig.leave_date}}</text><view class="t3"><block wx:if="{{item.$orig.isbefore==1}}"><block><block wx:if="{{item.$orig.real_usemoney>0&&item.$orig.real_roomprice>0}}"><text class="x1 flex1">{{"实付房费："+item.$orig.real_usemoney+moneyunit+" + ￥"+item.$orig.real_roomprice}}</text></block><block wx:else><block wx:if="{{item.$orig.real_usemoney>0&&item.$orig.real_roomprice==0}}"><text class="x1 flex1">{{"实付房费：￥"+item.$orig.real_usemoney+moneyunit}}</text></block><block wx:else><text class="x1 flex1">{{"实付房费：￥"+item.$orig.real_roomprice}}</text></block></block></block></block><block wx:else><block><block wx:if="{{item.$orig.use_money>0&&item.$orig.leftmoney>0}}"><text class="x1 flex1">{{"房费："+item.$orig.use_money+moneyunit+" + ￥"+item.$orig.leftmoney}}</text></block><block wx:else><block wx:if="{{item.$orig.use_money>0&&item.$orig.leftmoney==0}}"><text class="x1 flex1">{{"房费：￥"+item.$orig.use_money+moneyunit}}</text></block><block wx:else><text class="x1 flex1">{{"房费：￥"+item.$orig.sell_price}}</text></block></block></block></block></view><block wx:if="{{item.$orig.coupon_money>0}}"><view class="t3"><block><text class="x1 flex1">{{"优惠券:￥"+item.$orig.coupon_money}}</text></block></view></block></view></view><view class="bottom" style="display:flex;justify-content:space-between;"><text>{{"共"+item.$orig.daycount+'晚'}}<block wx:if="{{item.$orig.use_money>0&&item.$orig.leftmoney>0}}"><block>{{'实付: 押金￥'+item.$orig.yajin_money+"+"+text['服务费']+"￥"+item.$orig.fuwu_money+"+房费￥"+item.$orig.leftmoney+"+"+(item.$orig.use_money?item.$orig.use_money:0)+moneyunit+''}}</block></block><block wx:else><block wx:if="{{item.$orig.use_money>0&&item.$orig.leftmoney==0}}"><block>{{'实付: 押金￥'+item.$orig.yajin_money+"+"+text['服务费']+"￥"+item.$orig.fuwu_money+"+房费"+(item.$orig.use_money?item.$orig.use_money:0)+moneyunit+''}}</block></block><block wx:else><block>{{'实付:￥'+item.$orig.totalprice+''}}</block></block></block></text></view><block wx:if="{{item.$orig.refund_status>0}}"><view class="bottom"><block wx:if="{{item.$orig.refund_status==1}}"><text style="color:red;">{{'退款中￥'+item.$orig.refund_money}}</text></block><block wx:if="{{item.$orig.refund_status==2}}"><text style="color:red;">{{'已退款￥'+item.$orig.refund_money}}</text></block><block wx:if="{{item.$orig.refund_status==3}}"><text style="color:red;">退款申请已驳回</text></block></view></block><view class="op"><view class="btn2" data-url="{{'orderdetail?id='+item.$orig.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e">详情</view><block wx:if="{{item.$orig.status==0}}"><block><view class="btn2" data-id="{{item.$orig.id}}" data-event-opts="{{[['tap',[['toclose',['$event']]]]]}}" catchtap="__e">关闭订单</view><view class="btn1" style="{{'background:'+(item.m0)+';'}}" data-url="{{'/pagesExt/pay/pay?id='+item.$orig.payorderid}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e">去付款</view></block></block><block wx:if="{{item.$orig.status==1&&item.$orig.isrefund==1}}"><block><block wx:if="{{item.$orig.refund_status==0||item.$orig.refund_status==3}}"><view class="btn2" data-url="{{'refund?id='+item.$orig.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e">申请退款</view></block></block></block><block wx:if="{{item.$orig.status==2&&item.$orig.isrefund==1}}"><block><block wx:if="{{item.$orig.refund_status==0||item.$orig.refund_status==3}}"><view class="btn2" data-url="{{'refund?id='+item.$orig.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e">申请退款</view></block></block></block><block wx:if="{{item.$orig.status==4}}"><block><block wx:if="{{item.$orig.yajin_refund_status==2}}"><block><text class="btn2 color1" style="{{('background:#1BA035;color:#FFF')}}">押金已退</text></block></block><block wx:else><block><block wx:if="{{item.$orig.yajin_money>0}}"><text class="btn2 color1" style="{{('background:rgba('+item.m1+',0.8);color:#FFF')}}" data-index="{{index}}" data-event-opts="{{[['tap',[['refundyajin',['$event']]]]]}}" catchtap="__e">退押金</text></block></block></block><block wx:if="{{item.$orig.hotel.comment==1}}"><view class="btn2 color1" style="{{('background:#FCC421;color:#FFF')}}" data-url="{{'comment?oid='+item.$orig.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e">评价</view></block></block></block><block wx:if="{{item.$orig.status==-1}}"><block><view class="btn2" data-id="{{item.$orig.id}}" data-event-opts="{{[['tap',[['todel',['$event']]]]]}}" catchtap="__e">删除订单</view></block></block></view></view></block></block><block wx:if="{{showyajin}}"><view class="model_yajin"><view class="yajinbox"><view class="yajincontent"><view class="title">押金详情</view><block wx:if="{{yajininfo.refund_status==0&&set.yajin_desc}}"><view class="desc">{{''+set.yajin_desc+''}}</view></block><block wx:if="{{yajininfo.refund_status>0}}"><block><view class="item"><label>申请时间：</label><view class="f2"><text>{{$root.m2}}</text></view></view><view class="item"><label>退款状态：</label><block wx:if="{{yajininfo.refund_status==1}}"><text class="t2 red">审核中</text></block><block wx:if="{{yajininfo.refund_status==2}}"><text class="t2 red">已退款</text></block><block wx:if="{{yajininfo.refund_status==-1}}"><text class="t2 red">已驳回</text></block></view><block wx:if="{{yajininfo.refund_status==2}}"><view class="item"><label>退款时间：</label><text>{{yajininfo.refund_time}}</text></view></block></block></block><block wx:if="{{yajininfo.refund_status==-1}}"><block><view class="item"><label>申请时间：</label><text>{{$root.m3}}</text></view><view class="item"><label>退款状态：</label><text class="t2 red">已驳回</text></view><block wx:if="{{yajininfo.refund_status==2}}"><view class="item"><label>退款时间：</label><text>{{yajininfo.refund_time}}</text></view></block></block></block><block wx:if="{{yajininfo.refund_status==-1}}"><view class="item"><label>驳回原因：</label><text>{{yajininfo.refund_reason}}</text></view></block><block wx:if="{{yajininfo.refund_status<1}}"><view class="yajinbtn"><view data-event-opts="{{[['tap',[['closeYajin',['$event']]]]]}}" class="btn1" style="{{('background:#FCC421;color:#FFF; border:none')}}" bindtap="__e">稍后申请</view><view data-event-opts="{{[['tap',[['yajinsubmit',['$event']]]]]}}" class="btn2" style="{{('background:rgba('+$root.m4+',0.8);color:#FFF;border:none')}}" bindtap="__e">立即申请</view></view></block></view><view data-event-opts="{{[['tap',[['closeYajin',['$event']]]]]}}" class="close" bindtap="__e"><image style="width:100%;height:100%;" src="{{pre_url+'/static/img/close2.png'}}"></image></view></view></view></block></view><block wx:if="{{nomore}}"><nomore vue-id="7d9fb6d4-2" bind:__l="__l"></nomore></block><block wx:if="{{nodata}}"><nodata vue-id="7d9fb6d4-3" bind:__l="__l"></nodata></block></block></block><block wx:if="{{loading}}"><loading vue-id="7d9fb6d4-4" bind:__l="__l"></loading></block><dp-tabbar vue-id="7d9fb6d4-5" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="7d9fb6d4-6" data-ref="popmsg" bind:__l="__l"></popmsg></view>