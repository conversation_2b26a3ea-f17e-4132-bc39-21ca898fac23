<view class="container"><block wx:if="{{isload}}"><block><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="content flex" data-id="{{item.$orig.id}}" data-index="{{index}}" data-yystatus="{{item.$orig.yystatus}}" data-event-opts="{{[['tap',[['changeradio',['$event']]]]]}}" catchtap="__e"><view class="btitle"><view class="radio" style="{{(sindex==index?'background:'+item.m0+';border:0':'')}}"><image class="radio-img" src="{{pre_url+'/static/img/checkd.png'}}"></image></view></view><view class="f1"><view class="headimg"><image src="{{item.$orig.headimg}}"></image></view><view class="text1"><text class="t1">{{item.$orig.realname+''}}</text><block wx:if="{{item.$orig.typename}}"><text class="t2">{{item.$orig.typename}}</text></block><view class="tags"><block wx:if="{{item.$orig.citys}}"><text class="t3">{{item.$orig.citys}}</text></block><block wx:if="{{item.$orig.age}}"><text class="t3">{{''+item.$orig.age+"岁"}}</text></block><text class="t3">{{"评分"+item.$orig.comment_score}}</text></view><view class="f3"><text class="juli">距离<text class="t4">{{item.$orig.juli}}</text>{{item.$orig.juli_unit}}</text><image class="tel" src="{{pre_url+'/static/img/tel2.png'}}" data-url="{{'tel::'+item.$orig.tel}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e"></image></view></view></view></view></block><block wx:if="{{fwname}}"><view class="bottom"><view data-event-opts="{{[['tap',[['confirm',['$event']]]]]}}" class="btn" style="{{'background:'+('linear-gradient(90deg,'+$root.m1+' 0%,rgba('+$root.m2+',0.8) 100%)')+';'}}" bindtap="__e">确定指派<block wx:if="{{fwname}}"><text>{{fwname}}</text></block></view></view></block><block wx:if="{{nodata}}"><nodata vue-id="7306660b-1" bind:__l="__l"></nodata></block><block wx:if="{{nomore}}"><nomore vue-id="7306660b-2" bind:__l="__l"></nomore></block><view style="height:140rpx;"></view></block></block><block wx:if="{{loading}}"><loading vue-id="7306660b-3" bind:__l="__l"></loading></block><dp-tabbar vue-id="7306660b-4" opt="{{opt}}" data-event-opts="{{[['^getmenuindex',[['getmenuindex']]]]}}" bind:getmenuindex="__e" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="7306660b-5" data-ref="popmsg" bind:__l="__l"></popmsg></view>