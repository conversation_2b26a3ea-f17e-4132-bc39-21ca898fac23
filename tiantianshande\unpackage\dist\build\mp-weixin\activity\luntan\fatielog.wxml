<view><block wx:if="{{isload}}"><block><view class="content" id="datalist"><block wx:for="{{datalist}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><view class="item" style="overflow:hidden;" data-url="{{'detail?id='+item.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view style="width:100rpx;"><image style="width:90rpx;height:90rpx;" src="{{item.pic}}"></image></view><view class="f1" style="width:560rpx;float:right;"><text class="t1">{{item.content}}</text><text class="t2">{{item.createtime}}</text></view></view></block></block><block wx:if="{{nodata}}"><nodata vue-id="02f1b5d4-1" bind:__l="__l"></nodata></block></view><block wx:if="{{nomore}}"><nomore vue-id="02f1b5d4-2" bind:__l="__l"></nomore></block></block></block><block wx:if="{{loading}}"><loading vue-id="02f1b5d4-3" bind:__l="__l"></loading></block><dp-tabbar vue-id="02f1b5d4-4" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="02f1b5d4-5" data-ref="popmsg" bind:__l="__l"></popmsg></view>