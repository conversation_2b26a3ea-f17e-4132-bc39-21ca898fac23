<view class="container"><block wx:if="{{isload}}"><block><view class="filter-bar"><view data-event-opts="{{[['tap',[['changeFilter',['today']]]]]}}" class="{{['filter-item',(filter==='today')?'active':'']}}" bindtap="__e">今日</view><view data-event-opts="{{[['tap',[['changeFilter',['yesterday']]]]]}}" class="{{['filter-item',(filter==='yesterday')?'active':'']}}" bindtap="__e">昨日</view><view data-event-opts="{{[['tap',[['changeFilter',['all']]]]]}}" class="{{['filter-item',(filter==='all')?'active':'']}}" bindtap="__e">全部</view></view><view><view class="order-box"><view class="head"><view class="f1">今日需配单数量:</view><view class="flex1"></view><view class="f2"><text class="t1">{{datalist.ds}}</text>单</view></view><view class="head"><view class="f1">今日需配件数量:</view><view class="flex1"></view><view class="f2"><text class="t1">{{datalist.js}}</text>件</view></view><block wx:for="{{datalist.data}}" wx:for-item="item" wx:for-index="index" wx:key="id"><view class="f1" style="margin-top:20rpx;">{{''+item.name+''}}<label style="color:red;margin-left:50rpx;" class="_span">{{"X"+item.xnum}}</label></view></block></view></view><block wx:if="{{nomore}}"><nomore vue-id="401fe2e6-1" bind:__l="__l"></nomore></block><block wx:if="{{nodata}}"><nodata vue-id="401fe2e6-2" bind:__l="__l"></nodata></block><view class="tabbar"><view class="tabbar-bot"></view><view class="tabbar-bar" style="background-color:#ffffff;"><view class="tabbar-item" data-url="dating" data-opentype="reLaunch" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="tabbar-image-box"><image class="tabbar-icon" src="{{pre_url+'/static/img/peisong/home2.png'}}"></image></view><view class="tabbar-text active">大厅</view></view><view class="tabbar-item" data-url="orderlist" data-opentype="reLaunch" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="tabbar-image-box"><image class="tabbar-icon" src="{{pre_url+'/static/img/peisong/order.png'}}"></image></view><view class="tabbar-text">订单</view></view><view class="tabbar-item" data-url="orderlist?st=4" data-opentype="reLaunch" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="tabbar-image-box"><image class="tabbar-icon" src="{{pre_url+'/static/img/peisong/orderwc.png'}}"></image></view><view class="tabbar-text">已完成</view></view><view class="tabbar-item" data-url="my" data-opentype="reLaunch" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="tabbar-image-box"><image class="tabbar-icon" src="{{pre_url+'/static/img/peisong/my.png'}}"></image></view><view class="tabbar-text">我的</view></view></view></view></block></block><block wx:if="{{loading}}"><loading vue-id="401fe2e6-3" bind:__l="__l"></loading></block><dp-tabbar vue-id="401fe2e6-4" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="401fe2e6-5" data-ref="popmsg" bind:__l="__l"></popmsg><view style="display:none;">{{timestamp}}</view></view>