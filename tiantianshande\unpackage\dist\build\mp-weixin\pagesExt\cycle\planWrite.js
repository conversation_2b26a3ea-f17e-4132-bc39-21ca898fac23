require('../common/vendor.js');(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pagesExt/cycle/planWrite"],{"01e7":function(t,i,s){},20635:function(t,i,s){"use strict";var e=s("42f1"),n=s.n(e);n.a},"42f1":function(t,i,s){},"55e6":function(t,i,s){"use strict";s.r(i);var e=s("a260"),n=s("8cf2");for(var u in n)["default"].indexOf(u)<0&&function(t){s.d(i,t,(function(){return n[t]}))}(u);s("20635"),s("a479");var a=s("828b"),r=Object(a["a"])(n["default"],e["b"],e["c"],!1,null,"0093be47",null,!1,e["a"],void 0);i["default"]=r.exports},"8cf2":function(t,i,s){"use strict";s.r(i);var e=s("a9f0"),n=s.n(e);for(var u in e)["default"].indexOf(u)<0&&function(t){s.d(i,t,(function(){return e[t]}))}(u);i["default"]=n.a},"9ba48":function(t,i,s){"use strict";(function(t,i){var e=s("47a9");s("06e9");e(s("3240"));var n=e(s("55e6"));t.__webpack_require_UNI_MP_PLUGIN__=s,i(n.default)}).call(this,s("3223")["default"],s("df3c")["createPage"])},a260:function(t,i,s){"use strict";s.d(i,"b",(function(){return n})),s.d(i,"c",(function(){return u})),s.d(i,"a",(function(){return e}));var e={loading:function(){return s.e("components/loading/loading").then(s.bind(null,"ceaa"))}},n=function(){var t=this.$createElement,i=(this._self._c,1==this.product.status?this.t("color1"):null);this.$mp.data=Object.assign({},{$root:{m0:i}})},u=[]},a479:function(t,i,s){"use strict";var e=s("01e7"),n=s.n(e);n.a},a9f0:function(t,i,s){"use strict";(function(t){Object.defineProperty(i,"__esModule",{value:!0}),i.default=void 0;var s=getApp(),e={data:function(){return{opt:{},pre_url:s.globalData.pre_url,product:{},isload:!1,loading:!1,num:1,qsnum:1,min_num:1,min_qsnum:1,guigedata:{},ggselected:{},nowguige:{},specsIndex:0,rateList:{},rateIndex:0,totalprice:0,ks:"",startDate:"",week:"",pspl:"",qsnumState:!0,numState:!0,ps_cycle:""}},onLoad:function(t){this.opt=s.getopts(t),this.getdata()},onShow:function(){var i=this;t.$on("selectedDate",(function(t){i.startDate=t.startStr.dateStr,i.week=t.startStr.week}))},methods:{tobuy:function(t){var i=this.ks,e=this.product.id,n=this.guigelist[i].id,u=this.guigelist[i].stock,a=this.num;if(a<1&&(a=1),u<a)s.error("库存不足");else if(""!=this.startDate)if(this.qsnumState)if(this.numState){var r=e+","+n+","+a,o=this.pspl?this.pspl.value:"",c=this.startDate+","+this.qsnum+","+o;s.goto("/pagesExt/cycle/buy?prodata="+r+"&qsdata="+c)}else s.error("每期数量最小数量为"+this.min_num);else s.error("配送期数最小数量为"+this.min_qsnum);else s.error("请选择开始时间")},getdata:function(){var t=this;t.loading=!0,s.get("ApiCycle/product",{id:t.opt.id},(function(i){if(t.loading=!1,1==i.status){t.loading=!1,t.ps_cycle=i.product.ps_cycle,t.product=i.product,t.shopset=i.shopset,t.guigelist=i.guigelist,t.guigedata=i.guigedata,t.rateList=i.product.everyday_item,t.num=i.product.min_num,t.qsnum=i.product.min_qsnum,t.min_num=i.product.min_num,t.min_qsnum=i.product.min_qsnum;for(var e=i.guigedata,n=[],u=0;u<e.length;u++)n.push(0);t.ks=n.join(","),console.log(t.ks,"ks"),t.nowguige=t.guigelist[t.ks],t.ggselected=n,t.pspl=t.rateList?t.rateList[0]:"",t.isload=!0,3!=t.product.freighttype&&4!=t.product.freighttype||(t.canaddcart=!1),t.totalprice=(t.nowguige.sell_price*t.num*t.qsnum).toFixed(2),t.isload=!0}else s.alert(i.msg)}))},ggchange:function(t){var i=t.currentTarget.dataset.idx,s=t.currentTarget.dataset.itemk,e=this.ggselected;e[s]=i;var n=e.join(",");console.log(n,"ks"),this.ggselected=e,this.ks=n,this.nowguige=this.guigelist[this.ks],console.log(this.nowguige,"nowguige"),this.nowguige.limit_start>0&&this.gwcnum<this.nowguige.limit_start&&(this.gwcnum=this.nowguige.limit_start),this.totalprice=(this.nowguige.sell_price*this.num*this.qsnum).toFixed(2)},rateClick:function(t){this.rateIndex=t,this.pspl=this.rateList[t],this.startDate="",this.week=""},qsplus:function(){this.qsnum+=1,""==this.qsnum?(this.qsnumState=!1,s.error("配送期数不能为空")):this.qsnum<this.min_qsnum?(this.qsnumState=!1,s.error("配送期数最小数量为"+this.min_qsnum)):this.qsnumState=!0,this.totalprice=(this.nowguige.sell_price*this.qsnum*this.num).toFixed(2)},qsminus:function(){1!=this.qsnum&&(this.qsnum-=1),this.qsnum=this.qsnum<=this.min_qsnum?this.min_qsnum:this.qsnum,this.totalprice=(this.nowguige.sell_price*this.qsnum*this.num).toFixed(2)},gwcplus:function(){this.num+=1,""==this.num?(this.numState=!1,s.error("每期数量不能为空")):this.num<this.min_num?(this.numState=!1,s.error("每期数量最小数量为"+this.min_num)):this.numState=!0,this.totalprice=(this.nowguige.sell_price*this.qsnum*this.num).toFixed(2)},gwcminus:function(){1!=this.num&&(this.num-=1),this.num=this.num<=this.min_num?this.min_num:this.num,this.totalprice=(this.nowguige.sell_price*this.qsnum*this.num).toFixed(2)},getQsTotal:function(t){""==t.detail.value?(this.qsnumState=!1,s.error("配送期数不能为空")):parseInt(t.detail.value)<this.min_qsnum?(this.qsnumState=!1,s.error("配送期数最小数量为"+this.min_qsnum)):this.qsnumState=!0,this.qsnum=parseInt(t.detail.value),this.totalprice=(this.nowguige.sell_price*this.qsnum*this.num).toFixed(2)},getTotal:function(t){""==t.detail.value?(this.numState=!1,s.error("每期数量不能为空")):parseInt(t.detail.value)<this.min_num?(this.numState=!1,s.error("每期数量最小数量为"+this.min_num)):this.numState=!0,this.num=parseInt(t.detail.value),this.totalprice=(this.nowguige.sell_price*this.qsnum*this.num).toFixed(2)},toCheckDate:function(){var t="";t="2"==this.ps_cycle?5:"3"==this.ps_cycle?6:this.pspl?this.pspl.value:"",s.goto("/pagesExt/cycle/checkDate?date="+this.startDate+"&ys="+this.product.advance_pay_days+"&type="+t)}}};i.default=e}).call(this,s("df3c")["default"])}},[["9ba48","common/runtime","common/vendor"]]]);