<view class="container"><block wx:if="{{isload}}"><block><form report-submit="true" data-event-opts="{{[['submit',[['formSubmit',['$event']]]],['reset',[['formReset',['$event']]]]]}}" bindsubmit="__e" bindreset="__e"><view class="orderinfo"><view class="item"><text class="t1">选中订单数</text><text class="t2">{{$root.g0+"个"}}</text></view><view class="item"><text class="t1">可开票总金额</text><text class="t2 red">{{"¥"+totalAmount}}</text></view></view><view class="orderinfo"><view class="item"><text class="t1">发票类型</text><view class="t2"><radio-group class="radio-group" name="invoice_type" data-event-opts="{{[['change',[['changeOrderType',['$event']]]]]}}" bindchange="__e"><block wx:if="{{$root.m0}}"><label class="radio"><radio value="1" checked="{{invoice_type_select==1?true:false}}"></radio>普通发票</label></block><block wx:if="{{$root.m1}}"><label class="radio"><radio value="2" checked="{{invoice_type_select==2?true:false}}"></radio>增值税专用发票</label></block></radio-group></view></view><view class="item"><text class="t1">抬头类型</text><view class="t2"><radio-group class="radio-group" name="name_type" data-event-opts="{{[['change',[['changeNameType',['$event']]]]]}}" bindchange="__e"><label class="radio"><radio value="1" checked="{{name_type_select==1?true:false}}" disabled="{{name_type_personal_disabled?true:false}}"></radio>个人</label><label class="radio"><radio value="2" checked="{{name_type_select==2?true:false}}"></radio>公司</label></radio-group></view></view><view class="item"><text class="t1">抬头名称</text><input class="t2" type="text" placeholder="抬头名称" placeholder-style="font-size:28rpx;color:#BBBBBB" name="invoice_name" data-event-opts="{{[['input',[['__set_model',['$0','invoice_name','$event',[]],['formData']]]]]}}" value="{{formData.invoice_name}}" bindinput="__e"/></view><block wx:if="{{name_type_select==2}}"><view class="item"><text class="t1">公司税号</text><input class="t2" type="text" placeholder="公司税号" placeholder-style="font-size:28rpx;color:#BBBBBB" name="tax_no" data-event-opts="{{[['input',[['__set_model',['$0','tax_no','$event',[]],['formData']]]]]}}" value="{{formData.tax_no}}" bindinput="__e"/></view></block><block wx:if="{{invoice_type_select==2}}"><view class="item"><text class="t1">注册地址</text><input class="t2" type="text" placeholder="注册地址" placeholder-style="font-size:28rpx;color:#BBBBBB" name="address" data-event-opts="{{[['input',[['__set_model',['$0','address','$event',[]],['formData']]]]]}}" value="{{formData.address}}" bindinput="__e"/></view></block><block wx:if="{{invoice_type_select==2}}"><view class="item"><text class="t1">注册电话</text><input class="t2" type="text" placeholder="注册电话" placeholder-style="font-size:28rpx;color:#BBBBBB" name="tel" data-event-opts="{{[['input',[['__set_model',['$0','tel','$event',[]],['formData']]]]]}}" value="{{formData.tel}}" bindinput="__e"/></view></block><block wx:if="{{invoice_type_select==2}}"><view class="item"><text class="t1">开户银行</text><input class="t2" type="text" placeholder="开户银行" placeholder-style="font-size:28rpx;color:#BBBBBB" name="bank_name" data-event-opts="{{[['input',[['__set_model',['$0','bank_name','$event',[]],['formData']]]]]}}" value="{{formData.bank_name}}" bindinput="__e"/></view></block><block wx:if="{{invoice_type_select==2}}"><view class="item"><text class="t1">银行账号</text><input class="t2" type="text" placeholder="银行账号" placeholder-style="font-size:28rpx;color:#BBBBBB" name="bank_account" data-event-opts="{{[['input',[['__set_model',['$0','bank_account','$event',[]],['formData']]]]]}}" value="{{formData.bank_account}}" bindinput="__e"/></view></block><view class="item"><text class="t1">手机号</text><input class="t2" type="text" placeholder="接收电子发票手机号" placeholder-style="font-size:28rpx;color:#BBBBBB" name="mobile" data-event-opts="{{[['input',[['__set_model',['$0','mobile','$event',[]],['formData']]]]]}}" value="{{formData.mobile}}" bindinput="__e"/></view><view class="item"><text class="t1">邮箱</text><input class="t2" type="text" placeholder="接收电子发票邮箱" placeholder-style="font-size:28rpx;color:#BBBBBB" name="email" data-event-opts="{{[['input',[['__set_model',['$0','email','$event',[]],['formData']]]]]}}" value="{{formData.email}}" bindinput="__e"/></view></view><button class="btn" style="{{'background:'+($root.m2)+';'}}" form-type="submit">确定</button><view data-event-opts="{{[['tap',[['back',['$event']]]]]}}" class="btn-a" bindtap="__e">返回上一步</view><view style="padding-top:30rpx;"></view></form></block></block><block wx:if="{{loading}}"><loading vue-id="c7c9a444-1" bind:__l="__l"></loading></block><dp-tabbar vue-id="c7c9a444-2" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="c7c9a444-3" data-ref="popmsg" bind:__l="__l"></popmsg></view>