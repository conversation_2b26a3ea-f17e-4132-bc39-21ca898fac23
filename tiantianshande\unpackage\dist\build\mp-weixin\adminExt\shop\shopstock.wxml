<view><view class="navigation"><view class="navcontent" style="{{'margin-top:'+(navigationMenu.top+'px')+';'+('width:'+(navigationMenu.right+'px')+';')}}"><view class="header-location-top" style="{{'height:'+(navigationMenu.height+'px')+';'}}"><view data-event-opts="{{[['tap',[['goBack',['$event']]]]]}}" class="header-back-but" bindtap="__e"><image src="{{pre_url+'/static/img/adminExt/goback.png'}}"></image></view><view class="header-page-title">录入库存</view></view></view></view><block wx:if="{{isload}}"><view class="content"><view class="item"><view class="title-view flex-y-center"><view>商品列表</view><view data-event-opts="{{[['tap',[['addshop',['$event']]]]]}}" class="but-class" style="{{'background:'+('linear-gradient(-90deg,'+$root.m0+' 0%,rgba('+$root.m1+',0.8) 100%)')+';'}}" bindtap="__e">添加商品</view></view><view class="product"><block wx:for="{{prodata}}" wx:for-item="item" wx:for-index="index2" wx:key="index2"><view><view class="item flex"><view class="img"><block wx:if="{{item.guige.pic}}"><image src="{{item.guige.pic}}"></image></block><block wx:else><image src="{{item.product.pic}}"></image></block></view><view class="info flex1"><view class="f1">{{item.product.name}}</view><view class="f2">{{"规格："+item.guige.name}}</view><view class="f3"></view></view><view data-event-opts="{{[['tap',[['clearShopCartFn',['$0'],[[['prodata','',index2,'id']]]]]]]}}" class="del-view flex-y-center" style="color:#999999;font-size:24rpx;" catchtap="__e"><image style="width:24rpx;height:24rpx;margin-right:6rpx;" src="{{pre_url+'/static/img/del.png'}}"></image></view></view><view class="flex flex-y-center sb"><view class="modify-price flex-y-center"><input class="inputPrice" type="digit" placeholder="录入数量" data-event-opts="{{[['input',[['inputNum',['$event',index2]]]]]}}" value="{{item.num}}" bindinput="__e"/></view>*<view class="modify-price flex-y-center"><input class="inputPrice" type="digit" placeholder="进货单价" data-event-opts="{{[['input',[['inputPrice',['$event',index2]]]]]}}" value="{{item.guige.sell_price}}" bindinput="__e"/></view>=<view class="modify-price flex-y-center"><input class="inputPrice" type="digit" placeholder="总价" data-event-opts="{{[['input',[['inputTotalPrice',['$event',index2]]]]]}}" value="{{item.buytotal}}" bindinput="__e"/></view></view></view></block><block wx:if="{{nodata}}"><nodata vue-id="13f49980-1" text="请添加需要操作的商品" bind:__l="__l"></nodata></block></view></view><view style="width:100%;height:182rpx;"></view><view class="footer flex notabbarbot"><button data-event-opts="{{[['tap',[['tosubmit',['$event']]]]]}}" class="op" style="{{'background:'+('linear-gradient(-90deg,'+$root.m2+' 0%,rgba('+$root.m3+',0.8) 100%)')+';'}}" bindtap="__e">确定</button></view></view></block><block wx:if="{{loading}}"><loading vue-id="13f49980-2" bind:__l="__l"></loading></block></view>