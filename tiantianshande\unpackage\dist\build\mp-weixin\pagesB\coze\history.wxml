<view class="container"><block wx:if="{{isload}}"><block><view class="header"><view class="header-title">对话历史</view><view class="header-actions"><view data-event-opts="{{[['tap',[['clearAllHistory',['$event']]]]]}}" class="action-btn" bindtap="__e"><text class="iconfont iconqingchu"></text><text>清空</text></view></view></view><view class="conversation-list"><block wx:for="{{$root.l0}}" wx:for-item="conversation" wx:for-index="index" wx:key="index"><block><view class="conversation-item" data-conversation="{{conversation.g0}}" data-event-opts="{{[['tap',[['openConversation',['$event']]]]]}}" bindtap="__e"><view class="conversation-header"><view class="conversation-title">{{''+(conversation.$orig.title||'对话 '+(index+1))+''}}</view><view class="conversation-time">{{conversation.$orig.update_time_text}}</view></view><block wx:if="{{conversation.$orig.last_message}}"><view class="conversation-preview">{{''+conversation.$orig.last_message+''}}</view></block><view class="conversation-actions"><view class="action-item" data-conversation-id="{{conversation.$orig.conversation_id}}" data-event-opts="{{[['tap',[['deleteConversation',['$event']]]]]}}" catchtap="__e"><text class="iconfont iconshanchu"></text><text>删除</text></view></view></view></block></block><block wx:if="{{nomore}}"><nomore vue-id="69570a12-1" bind:__l="__l"></nomore></block><block wx:if="{{nodata}}"><nodata vue-id="69570a12-2" bind:__l="__l"></nodata></block></view><uni-popup class="vue-ref" vue-id="69570a12-3" type="bottom" data-ref="conversationDetailPopup" bind:__l="__l" vue-slots="{{['default']}}"><view class="detail-popup"><view class="popup-header"><view class="popup-title">对话详情</view><view data-event-opts="{{[['tap',[['closeConversationDetail',['$event']]]]]}}" class="popup-close" bindtap="__e"><text class="iconfont iconguanbi"></text></view></view><scroll-view class="detail-content" scroll-y="true"><view class="message-list"><block wx:for="{{currentMessages}}" wx:for-item="message" wx:for-index="index" wx:key="index"><block><view class="{{['message-item',message.role==='user'?'user-message':'bot-message']}}"><view class="message-avatar"><block wx:if="{{message.role==='user'}}"><image src="{{userInfo.avatar||'/static/img/default-user.png'}}"></image></block><block wx:else><image src="/static/img/default-bot.png"></image></block></view><view class="message-content"><view class="message-text">{{message.content}}</view><view class="message-time">{{message.create_time_text}}</view></view></view></block></block></view></scroll-view><view class="popup-footer"><view data-event-opts="{{[['tap',[['closeConversationDetail',['$event']]]]]}}" class="btn-cancel" bindtap="__e">关闭</view><view data-event-opts="{{[['tap',[['continueConversation',['$event']]]]]}}" class="btn-confirm" style="{{'background:'+($root.m0)+';'}}" bindtap="__e">继续对话</view></view></view></uni-popup><uni-popup class="vue-ref" vue-id="69570a12-4" type="dialog" data-ref="confirmDeletePopup" bind:__l="__l" vue-slots="{{['default']}}"><uni-popup-dialog vue-id="{{('69570a12-5')+','+('69570a12-4')}}" type="confirm" title="确认删除" content="确定要删除这个对话吗？删除后无法恢复。" data-event-opts="{{[['^confirm',[['confirmDelete']]],['^close',[['closeConfirmDelete']]]]}}" bind:confirm="__e" bind:close="__e" bind:__l="__l"></uni-popup-dialog></uni-popup><uni-popup class="vue-ref" vue-id="69570a12-6" type="dialog" data-ref="confirmClearPopup" bind:__l="__l" vue-slots="{{['default']}}"><uni-popup-dialog vue-id="{{('69570a12-7')+','+('69570a12-6')}}" type="confirm" title="确认清空" content="确定要清空所有对话历史吗？清空后无法恢复。" data-event-opts="{{[['^confirm',[['confirmClearAll']]],['^close',[['closeClearConfirm']]]]}}" bind:confirm="__e" bind:close="__e" bind:__l="__l"></uni-popup-dialog></uni-popup></block></block><block wx:if="{{loading}}"><loading vue-id="69570a12-8" bind:__l="__l"></loading></block></view>