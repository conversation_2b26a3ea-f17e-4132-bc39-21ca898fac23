<view><block wx:if="{{isload}}"><block><block wx:if="{{sysset.showgzts}}"><block><view style="width:100%;height:88rpx;"></view><view class="follow_topbar"><view class="headimg"><image src="{{sysset.logo}}"></image></view><view class="info"><view class="i">欢迎进入<text style="{{'color:'+($root.m0)+';'}}">{{sysset.name}}</text></view><view class="i">关注公众号享更多专属服务</view></view><view data-event-opts="{{[['tap',[['showsubqrcode',['$event']]]]]}}" class="sub" style="{{'background-color:'+($root.m1)+';'}}" bindtap="__e">立即关注</view></view><uni-popup class="vue-ref" vue-id="cedb6e98-1" id="qrcodeDialog" type="dialog" data-ref="qrcodeDialog" bind:__l="__l" vue-slots="{{['default']}}"><view class="qrcodebox"><image class="img" src="{{sysset.qrcode}}" data-url="{{sysset.qrcode}}" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image><view class="txt">长按识别二维码关注</view><view data-event-opts="{{[['tap',[['closesubqrcode',['$event']]]]]}}" class="close" bindtap="__e"><image style="width:100%;height:100%;" src="{{pre_url+'/static/img/close2.png'}}"></image></view></view></uni-popup></block></block><block wx:if="{{$root.g0>0}}"><view style="position:fixed;top:15vh;left:20rpx;z-index:9;background:rgba(0,0,0,0.6);border-radius:20rpx;color:#fff;padding:0 10rpx;"><swiper style="position:relative;height:54rpx;width:350rpx;" autoplay="{{true}}" interval="{{5000}}" vertical="{{true}}"><block wx:for="{{bboglist}}" wx:for-item="item" wx:for-index="index" wx:key="index"><swiper-item class="flex-y-center" data-url="{{'/pagesB/huodongbaoming/product?id='+item.proid}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image style="width:40rpx;height:40rpx;border:1px solid rgba(255,255,255,0.7);border-radius:50%;margin-right:4px;" src="{{item.headimg}}"></image><view style="width:300rpx;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;font-size:22rpx;">{{item.nickname+" "+item.showtime+"预约了该活动"}}</view></swiper-item></block></swiper></view></block><block wx:if="{{isplay==0}}"><view class="swiper-container"><swiper class="swiper" indicator-dots="{{false}}" autoplay="{{true}}" interval="{{500000}}" data-event-opts="{{[['change',[['swiperChange',['$event']]]]]}}" bindchange="__e"><block wx:for="{{product.pics}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><swiper-item class="swiper-item"><view class="swiper-item-view"><image class="img" src="{{item}}" mode="widthFix"></image></view></swiper-item></block></block></swiper><block wx:if="{{$root.g1>1}}"><view class="imageCount">{{current+1+"/"+$root.g2}}</view></block></view></block><view class="seckill_title"><view class="f3"><block wx:if="{{!product.is_start}}"><view class="t1" style="{{(!product.is_start?'padding-left:35%':'padding-left:20%')}}">报名未开始</view></block><block wx:else><view class="t1" style="letter-spacing:0.5px;align-items:center;padding-top:3px;">距报名截止还剩</view></block><block wx:if="{{product.is_start}}"><view class="t2" style="align-items:center;padding-top:3px;" id="djstime"><text class="djsspan">{{day}}</text>天<text class="djsspan">{{djshour}}</text>时<text class="djsspan">{{djsmin}}</text>分</view></block></view></view><view class="header"><view class="price_share"><view class="title">{{product.name}}</view><view data-event-opts="{{[['tap',[['shareClick',['$event']]]]]}}" class="share" bindtap="__e"><image class="img" src="{{pre_url+'/static/img/share.png'}}"></image><text class="txt">分享</text></view></view><view class="pricebox flex"><view class="price"><block wx:if="{{product.is_fufei==1&&(product.score_price>0||product.sell_price>0)}}"><block><view class="f1" style="{{'color:'+($root.m2)+';'}}"><block wx:if="{{product.score_price>0&&product.sell_price>0}}"><block>{{''+product.score_price+$root.m3}}<block wx:if="{{product.sell_price*1>0}}"><text>{{"+"+product.sell_price+"元"}}</text></block></block></block><block wx:else><block wx:if="{{product.sell_price>0}}"><block><block wx:if="{{product.sell_price*1>0}}"><text>{{product.sell_price+"元"}}</text></block></block></block><block wx:else><block wx:if="{{product.score_price>0}}"><block><text>{{product.score_price+$root.m4}}</text></block></block></block></block></view></block></block><block wx:else><view class="f1" style="{{'color:'+($root.m5)+';'}}">免费</view></block></view><view class="sales_stock"><view class="f1">{{"已报名："+product.sales+''}}</view></view></view><block wx:if="{{product.sellpoint}}"><view class="sellpoint">{{product.sellpoint}}</view></block><view style="margin:20rpx 0;font-size:22rpx;">{{"访问量："+product.viewnum+''}}</view></view><block wx:if="{{$root.g3>0}}"><view class="cuxiaopoint"><view class="f0">优惠</view><view data-event-opts="{{[['tap',[['showcuxiaodetail',['$event']]]]]}}" class="f1" bindtap="__e"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="t" style="{{'background:'+('rgba('+item.m6+',0.1)')+';'+('color:'+(item.m7)+';')}}"><text class="t0" style="padding:0 6px;">券</text><text class="t1">{{item.$orig.name}}</text></view></block></view><view data-event-opts="{{[['tap',[['showcuxiaodetail',['$event']]]]]}}" class="f2" bindtap="__e"><image src="{{pre_url+'/static/img/arrow-point.png'}}" mode="widthFix"></image></view></view></block><view class="choosebox"><block wx:if="{{$root.g4>0}}"><block><block wx:for="{{product.huodong_danwei}}" wx:for-item="item" wx:for-index="index"><block><view class="choose"><view class="f0">{{text['活动单位']}}</view><view class="f1 flex1">{{''+item.name+''}}</view></view><view class="choose"><view class="f0">{{text['联系电话']}}</view><view class="f1 flex1">{{item.tel}}</view><image class="f2" src="{{pre_url+'/static/img/peisong/tel2.png'}}" data-phone="{{item.tel}}" data-event-opts="{{[['tap',[['callphone',['$event']]]]]}}" catchtap="__e"></image></view></block></block></block></block><block wx:else><block><view class="choose"><view class="f0">{{text['活动单位']}}</view><view class="f1 flex1">{{''+product.zhubanfang_name+''}}</view></view><view class="choose"><view class="f0">{{text['联系电话']}}</view><view class="f1 flex1">{{product.zhubanfang_tel}}</view><image class="f2" src="{{pre_url+'/static/img/peisong/tel2.png'}}" data-phone="{{product.zhubanfang_tel}}" data-event-opts="{{[['tap',[['callphone',['$event']]]]]}}" catchtap="__e"></image></view></block></block><block wx:if="{{product.huodong_address}}"><view class="choose"><view class="f0">{{text['活动地址']}}</view><view class="f1 flex1">{{product.huodong_address}}</view><image class="f2" src="{{pre_url+'/static/img/address.png'}}" data-address="{{product.huodong_address}}" data-latitude="{{product.latitude}}" data-longitude="{{product.longitude}}" data-event-opts="{{[['tap',[['openLocation',['$event']]]]]}}" bindtap="__e"></image></view></block><view class="choose"><view class="f0">{{text['活动时间']}}</view><view class="f1 flex1 flex-y-center"><text class="xuanzefuwu-text">{{product.huodong_start_time+"至"}}</text><text class="xuanzefuwu-text">{{product.huodong_end_time}}</text></view></view></view><view class="detail_title"><view class="t1"></view><view class="t2"></view><view class="t0">活动详情</view><view class="t2"></view><view class="t1"></view></view><view class="detail"><dp vue-id="cedb6e98-2" pagecontent="{{pagecontent}}" bind:__l="__l"></dp></view><block wx:if="{{product.show_member_order==1&&orderlistlength>0}}"><view class="orderlistbox"><view class="title"><view class="f1">已报名</view><view class="f2" data-url="{{'proorderlist?proid='+product.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">更多<image style="width:32rpx;height:32rpx;" src="{{pre_url+'/static/img/arrowright.png'}}"></image></view></view><view class="orderlist"><block wx:if="{{orderlistlength>0}}"><view class="item"><block wx:for="{{orderlist}}" wx:for-item="itemp" wx:for-index="index" wx:key="index"><block><view class="f1"><image class="t1" style="margin:7px 0px 6px 0px;" src="{{itemp.headimg}}"></image><view class="t2" style="margin-bottom:10px;">{{itemp.nickname}}</view><view class="flex1"></view></view></block></block></view></block><block wx:else><view class="nocomment">暂无</view></block></view></view></block><view style="width:100%;height:140rpx;"></view><block wx:if="{{product.status==1}}"><view class="{{['bottombar','flex-row',menuindex>-1?'tabbarbot':'']}}"><view class="f1"><view class="item" data-url="{{'prolist?bid='+product.bid}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image class="img" src="{{pre_url+'/static/img/shou.png'}}"></image><view class="t1">首页</view></view><block wx:if="{{kfurl!='contact::'}}"><view class="item" data-url="{{kfurl}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image class="img" src="{{pre_url+'/static/img/kefu.png'}}"></image><view class="t1">客服</view></view></block><block wx:else><button class="item" open-type="contact" show-message-card="true"><image class="img" src="{{pre_url+'/static/img/kefu.png'}}"></image><view class="t1">客服</view></button></block><view data-event-opts="{{[['tap',[['addfavorite',['$event']]]]]}}" class="item" bindtap="__e"><image class="img" src="{{pre_url+'/static/img/shoucang.png'}}"></image><view class="t1">{{isfavorite?'已收藏':'收藏'}}</view></view></view><view class="op"><block wx:if="{{product.isend}}"><view class="tobuy flex-x-center flex-y-center" style="{{'background:'+('#aaa')+';'}}">已结束</view></block><block wx:else><block wx:if="{{product.is_fufei==1}}"><view data-event-opts="{{[['tap',[['tobuy',['$event']]]]]}}" class="tobuy flex-x-center flex-y-center" style="{{'background:'+($root.m8)+';'}}" bindtap="__e">立即报名</view></block><block wx:else><view data-event-opts="{{[['tap',[['tobuy2',['$event']]]]]}}" class="tobuy flex-x-center flex-y-center" style="{{'background:'+($root.m9)+';'}}" bindtap="__e">立即报名</view></block></block></view></view></block><block wx:if="{{buydialogShow}}"><huodongbmbuydialog vue-id="cedb6e98-3" proid="{{product.id}}" btntype="{{btntype}}" menuindex="{{menuindex}}" isfuwu="{{isfuwu}}" data-event-opts="{{[['^currgg',[['currgg']]],['^buydialogChange',[['buydialogChange']]],['^addcart',[['addcart']]],['^tobuy',[['tobuy']]]]}}" bind:currgg="__e" bind:buydialogChange="__e" bind:addcart="__e" bind:tobuy="__e" bind:__l="__l"></huodongbmbuydialog></block><scrolltop vue-id="cedb6e98-4" isshow="{{scrolltopshow}}" bind:__l="__l"></scrolltop><block wx:if="{{sharetypevisible}}"><view class="popup__container"><view data-event-opts="{{[['tap',[['handleClickMask',['$event']]]]]}}" class="popup__overlay" catchtap="__e"></view><view class="popup__modal" style="height:320rpx;min-height:320rpx;"><view class="popup__content"><view class="sharetypecontent"><block wx:if="{{$root.m10=='app'}}"><view data-event-opts="{{[['tap',[['shareapp',['$event']]]]]}}" class="f1" bindtap="__e"><image class="img" src="{{pre_url+'/static/img/sharefriends.png'}}"></image><text class="t1">分享给好友</text></view></block><block wx:else><block wx:if="{{$root.m11=='mp'}}"><view data-event-opts="{{[['tap',[['sharemp',['$event']]]]]}}" class="f1" bindtap="__e"><image class="img" src="{{pre_url+'/static/img/sharefriends.png'}}"></image><text class="t1">分享给好友</text></view></block><block wx:else><block wx:if="{{$root.m12!='h5'}}"><button class="f1" open-type="share"><image class="img" src="{{pre_url+'/static/img/sharefriends.png'}}"></image><text class="t1">分享给好友</text></button></block></block></block><view data-event-opts="{{[['tap',[['showPoster',['$event']]]]]}}" class="f2" bindtap="__e"><image class="img" src="{{pre_url+'/static/img/sharepic.png'}}"></image><text class="t1">生成分享图片</text></view></view></view></view></view></block><block wx:if="{{showposter}}"><view class="posterDialog"><view class="main"><view data-event-opts="{{[['tap',[['posterDialogClose',['$event']]]]]}}" class="close" bindtap="__e"><image class="img" src="{{pre_url+'/static/img/close.png'}}"></image></view><view class="content"><image class="img" src="{{posterpic}}" mode="widthFix" data-url="{{posterpic}}" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image></view></view></view></block></block></block><block wx:if="{{loading}}"><loading vue-id="cedb6e98-5" bind:__l="__l"></loading></block><dp-tabbar vue-id="cedb6e98-6" opt="{{opt}}" data-event-opts="{{[['^getmenuindex',[['getmenuindex']]]]}}" bind:getmenuindex="__e" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="cedb6e98-7" data-ref="popmsg" bind:__l="__l"></popmsg></view>