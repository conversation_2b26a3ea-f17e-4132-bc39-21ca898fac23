<view class="live data-v-54580510"><block wx:if="{{loading}}"><loading vue-id="5709c815-1" class="data-v-54580510" bind:__l="__l"></loading></block><video class="video data-v-54580510" object-fit="contain" loop="{{true}}" autoplay="{{true}}" controls="{{false}}" show-center-play-btn="{{false}}" src="{{videoSrc}}" poster="{{data.cover_img||'/static/live/video/1-poster.png'}}" data-event-opts="{{[['error',[['handleVideoError',['$event']]]]]}}" binderror="__e"></video><cover-view class="{{['content','data-v-54580510',(hideCoverStatus)?'hide':'']}}"><cover-view class="nav-wrapper data-v-54580510"><live-header vue-id="5709c815-2" data="{{data}}" data-event-opts="{{[['^onShare',[['handleHeaderShare']]]]}}" bind:onShare="__e" class="data-v-54580510" bind:__l="__l"></live-header></cover-view><cover-view class="msg-wrapper data-v-54580510"><live-msg vue-id="5709c815-3" data-ref="liveMsg" class="data-v-54580510 vue-ref" bind:__l="__l"></live-msg></cover-view><cover-view class="footer-wrapper data-v-54580510"><cover-view class="footer-content data-v-54580510">@clear="handleClear" 
					@sendLiveMsg="sendLiveMsg" 
					@sendLike="sendLike"
					@welcomeUser="welcomeUser" 
					@sendGift="sendGift"
				></cover-view></cover-view></cover-view><uni-popup vue-id="5709c815-4" type="bottom" data-ref="shopShow" data-event-opts="{{[['^change',[['onPopupChange']]]]}}" bind:change="__e" class="data-v-54580510 vue-ref" bind:__l="__l" vue-slots="{{['default']}}"><view class="viewShop popup-content data-v-54580510"><view class="shop-title data-v-54580510"><text class="data-v-54580510">{{"文中提到的商品（"+$root.g0+"）"}}</text><view data-event-opts="{{[['tap',[['closeShopPopup']]]]}}" class="close-btn data-v-54580510" bindtap="__e"><uni-icons vue-id="{{('5709c815-5')+','+('5709c815-4')}}" type="closeempty" size="20" color="#666" class="data-v-54580510" bind:__l="__l"></uni-icons></view></view><scroll-view class="scroll-view data-v-54580510" scroll-top="{{0}}" scroll-y="true" show-scrollbar="false" data-event-opts="{{[['scrolltolower',[['loadMore',['$event']]]]]}}" bindscrolltolower="__e"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['gotoProduct',['$0'],[[['matchedData','',index,'proid']]]]]]]}}" class="cart-item data-v-54580510" bindtap="__e"><view class="image-box data-v-54580510"><image class="item-image data-v-54580510" src="{{item.$orig.pic}}" mode="aspectFill"></image></view><view class="item-info data-v-54580510"><text class="item-name data-v-54580510">{{item.$orig.name}}</text><view class="price-action data-v-54580510"><text class="item-price data-v-54580510"><text class="symbol data-v-54580510">￥</text><text class="number data-v-54580510">{{item.m0}}</text></text><view data-event-opts="{{[['tap',[['gotoProduct',['$0'],[[['matchedData','',index,'proid']]]]]]]}}" class="buy-btn data-v-54580510" catchtap="__e"><text class="data-v-54580510">去购买</text></view></view></view></view></block><block wx:if="{{!loading}}"><uni-load-more vue-id="{{('5709c815-6')+','+('5709c815-4')}}" status="{{loadStatus}}" class="data-v-54580510" bind:__l="__l"></uni-load-more></block></scroll-view></view></uni-popup><popmsg vue-id="5709c815-7" data-ref="popmsg" class="data-v-54580510 vue-ref" bind:__l="__l"></popmsg><block wx:if="{{sharetypevisible}}"><view class="popup__container data-v-54580510"><view data-event-opts="{{[['tap',[['handleClickMask',['$event']]]]]}}" class="popup__overlay data-v-54580510" catchtap="__e"></view><view class="popup__modal data-v-54580510" style="height:320rpx;min-height:320rpx;"><view class="popup__content data-v-54580510"><view class="sharetypecontent data-v-54580510"><block wx:if="{{$root.m1=='app'}}"><view data-event-opts="{{[['tap',[['shareapp',['$event']]]]]}}" class="f1 data-v-54580510" bindtap="__e"><image class="img data-v-54580510" src="/static/img/weixin.png"></image><text class="t1 data-v-54580510">分享给好友</text></view></block><block wx:else><block wx:if="{{$root.m2=='mp'}}"><view data-event-opts="{{[['tap',[['sharemp',['$event']]]]]}}" class="f1 data-v-54580510" bindtap="__e"><image class="img data-v-54580510" src="/static/img/weixin.png"></image><text class="t1 data-v-54580510">分享给好友</text></view></block><block wx:else><block wx:if="{{$root.m3=='h5'}}"><view data-event-opts="{{[['tap',[['sharemp',['$event']]]]]}}" class="f1 data-v-54580510" bindtap="__e"><image class="img data-v-54580510" src="/static/img/weixin.png"></image><text class="t1 data-v-54580510">分享给好友</text></view></block><block wx:else><button class="f1 data-v-54580510" open-type="share"><image class="img data-v-54580510" src="/static/img/weixin.png"></image><text class="t1 data-v-54580510">分享给好友</text></button></block></block></block><view data-event-opts="{{[['tap',[['showPoster',['$event']]]]]}}" class="f2 data-v-54580510" bindtap="__e"><image class="img data-v-54580510" src="/static/img/sharepic.png"></image><text class="t1 data-v-54580510">生成分享图片</text></view></view></view></view></view></block><block wx:if="{{showposter}}"><view class="posterDialog data-v-54580510"><view class="main data-v-54580510"><view data-event-opts="{{[['tap',[['posterDialogClose',['$event']]]]]}}" class="close data-v-54580510" bindtap="__e"><image class="img data-v-54580510" src="{{pre_url+'/static/img/close.png'}}" mode="widthFix" data-url="{{posterpic}}" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image></view><view class="content data-v-54580510"><image class="img data-v-54580510" src="{{posterpic}}" mode="widthFix" data-url="{{posterpic}}" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image></view><view style="display:flex;justify-content:space-between;padding:0 10px 10px 10px;" class="data-v-54580510"><button data-event-opts="{{[['tap',[['savePhoto',['$event']]]]]}}" class="pp4 data-v-54580510" style="{{'background:'+('linear-gradient(90deg,'+$root.m4+' 0%,rgba('+$root.m5+',0.8) 100%)')+';'}}" bindtap="__e">保存</button><block wx:if="{{$root.m6=='app'}}"><button data-event-opts="{{[['tap',[['shareapp',['$event']]]]]}}" class="pp4 data-v-54580510" style="{{'background:'+('linear-gradient(90deg,'+$root.m7+' 0%,rgba('+$root.m8+',0.8) 100%)')+';'}}" bindtap="__e">转发</button></block><block wx:else><block wx:if="{{$root.m9=='mp'}}"><button data-event-opts="{{[['tap',[['sharemp',['$event']]]]]}}" class="pp4 data-v-54580510" style="{{'background:'+('linear-gradient(90deg,'+$root.m10+' 0%,rgba('+$root.m11+',0.8) 100%)')+';'}}" bindtap="__e">转发</button></block><block wx:else><block wx:if="{{$root.m12=='h5'}}"><button data-event-opts="{{[['tap',[['sharemp',['$event']]]]]}}" class="pp4 data-v-54580510" style="{{'background:'+('linear-gradient(90deg,'+$root.m13+' 0%,rgba('+$root.m14+',0.8) 100%)')+';'}}" bindtap="__e">转发</button></block><block wx:else><button class="pp4 data-v-54580510" style="{{'background:'+('linear-gradient(90deg,'+$root.m15+' 0%,rgba('+$root.m16+',0.8) 100%)')+';'}}" open-type="share">转发</button></block></block></block></view></view></view></block><view data-event-opts="{{[['tap',[['showPosterDialog',['$event']]]]]}}" class="poster-float-btn data-v-54580510" bindtap="__e"><image src="{{pre_url+'/static/img/poster-icon.png'}}" mode="aspectFit" class="data-v-54580510"></image></view><block wx:if="{{showPosterModal}}"><view data-event-opts="{{[['tap',[['closePosterModal',['$event']]]]]}}" class="poster-modal data-v-54580510" bindtap="__e"><view class="poster-modal-content data-v-54580510"><view data-event-opts="{{[['tap',[['closePosterModal',['$event']]]]]}}" class="poster-modal-close data-v-54580510" bindtap="__e"><uni-icons vue-id="5709c815-8" type="closeempty" size="24" color="#666" class="data-v-54580510" bind:__l="__l"></uni-icons></view><view class="poster-modal-image data-v-54580510"><image src="{{posterpic}}" mode="widthFix" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e" class="data-v-54580510"></image></view><view class="poster-modal-actions data-v-54580510"><button data-event-opts="{{[['tap',[['savePhoto',['$event']]]]]}}" class="poster-save-btn data-v-54580510" bindtap="__e">保存海报</button></view></view></view></block><block wx:if="{{watchRewardToast.show}}"><view class="watch-reward-toast data-v-54580510" style="{{'background:'+('linear-gradient(90deg,'+$root.m17+' 0%,rgba('+$root.m18+',0.8) 100%)')+';'}}">{{'恭喜获得 '+watchRewardToast.score+' 积分奖励！'}}</view></block></view>