<view><block wx:if="{{isload}}"><block><view class="topsearch flex-y-center"><view class="f1 flex-y-center"><image class="img" src="{{pre_url+'/static/img/search_ico.png'}}"></image><input placeholder="输入昵称/姓名/手机号搜索" placeholder-style="font-size:24rpx;color:#C2C2C2" data-event-opts="{{[['confirm',[['searchConfirm',['$event']]]],['input',[['searchChange',['$event']]]]]}}" value="{{keyword}}" bindconfirm="__e" bindinput="__e"/></view></view><block wx:if="{{$root.g0}}"><view class="content"><view class="label"><text class="t1">{{$root.m0+"列表（共"+count+"人）"}}</text><block wx:if="{{is_add_member}}"><view class="btn" data-url="/adminExt/order/addmember?type=1" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">添加会员</view></block><block wx:if="{{!couponShow}}"><view data-event-opts="{{[['tap',[['pushAll',['$event']]]]]}}" class="btn" bindtap="__e">全部推送</view></block></view><block wx:for="{{datalist}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><view class="item"><view class="f1" data-url="{{'detail?mid='+item.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image src="{{item.headimg}}"></image><view class="t2"><block wx:if="{{dkopen&&item.realname}}"><view>{{''+item.realname+''}}</view></block><view class="x1 flex-y-center">{{''+item.nickname+''}}<block wx:if="{{item.sex==1}}"><image style="margin-left:10rpx;width:40rpx;height:40rpx;" src="{{pre_url+'/static/img/nan2.png'}}"></image></block><block wx:if="{{item.sex==2}}"><image style="margin-left:10rpx;width:40rpx;height:40rpx;" src="{{pre_url+'/static/img/nv2.png'}}"></image></block></view><block wx:if="{{!dkopen}}"><block><text class="x2">{{"最后访问："+item.last_visittime}}</text><text class="x2">{{"加入时间："+item.createtime}}</text><text class="x2">{{(item.province?'':item.province)+(item.city?'':item.city)}}</text><block wx:if="{{item.remark}}"><text class="x2" style="color:#a66;font-size:22rpx;">{{item.remark}}</text></block></block></block><block wx:if="{{item.tel&&dkopen}}"><block><text class="x2">{{"手机号："+item.tel}}</text></block></block></view></view><block wx:if="{{couponShow}}"><block><block wx:if="{{!dkopen}}"><view class="f2"><view class="btn" data-url="{{'detail?mid='+item.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">详情</view><view class="btn" data-url="{{'/adminExt/member/history?id='+item.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">足迹</view><block wx:if="{{item.can_login}}"><view data-event-opts="{{[['tap',[['loginUser',['$0','$1'],[[['datalist','',index,'id']],[['datalist','',index,'tel']]]]]]]}}" class="btn" bindtap="__e">登录</view></block></view></block><block wx:else><view class="f2"><view data-event-opts="{{[['tap',[['SelectMembers',['$0'],[[['datalist','',index]]]]]]]}}" class="btn" bindtap="__e">选择</view></view></block></block></block><block wx:else><block><view class="f2"><view data-event-opts="{{[['tap',[['pushCoupons',['$0'],[[['datalist','',index]]]]]]]}}" class="btn" bindtap="__e">推送</view></view></block></block></view></block></block></view></block><block wx:if="{{nomore}}"><nomore vue-id="26bbfe6a-1" bind:__l="__l"></nomore></block><block wx:if="{{nodata}}"><nodata vue-id="26bbfe6a-2" bind:__l="__l"></nodata></block><block wx:if="{{!dkopen}}"><view class="tabbar"><view class="tabbar-bot"></view><view class="tabbar-bar" style="background-color:#ffffff;"><block wx:if="{{auth_data.member}}"><view class="tabbar-item" data-url="../member/index" data-opentype="reLaunch" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="tabbar-image-box"><image class="tabbar-icon" src="{{pre_url+'/static/img/adminExt/member2.png?v=1'}}"></image></view><view class="tabbar-text active">{{$root.m1}}</view></view></block><block wx:if="{{auth_data.zixun}}"><view class="tabbar-item" data-url="../kefu/index" data-opentype="reLaunch" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="tabbar-image-box"><image class="tabbar-icon" src="{{pre_url+'/static/img/adminExt/zixun.png?v=1'}}"></image></view><view class="tabbar-text">咨询</view></view></block><block wx:if="{{auth_data.finance}}"><view class="tabbar-item" data-url="../finance/index" data-opentype="reLaunch" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="tabbar-image-box"><image class="tabbar-icon" src="{{pre_url+'/static/img/adminExt/finance.png?v=1'}}"></image></view><view class="tabbar-text">财务</view></view></block><view class="tabbar-item" data-url="../index/index" data-opentype="reLaunch" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="tabbar-image-box"><image class="tabbar-icon" src="{{pre_url+'/static/img/adminExt/my.png?v=1'}}"></image></view><view class="tabbar-text">我的</view></view></view></view></block><uni-popup class="vue-ref" vue-id="26bbfe6a-3" id="popup" type="center" data-ref="popup" bind:__l="__l" vue-slots="{{['default']}}"><view class="uni-popup-dialog"><view class="uni-dialog-title"><text class="{{['uni-dialog-title-text','uni-popup__'+dialogType]}}">{{"推送"+$root.m2}}</text></view><view class="uni-dialog-content"><view class="uni-dialog-content-options"><view class="uni-dialog-content-text">{{$root.m3+"名称："}}</view><view style="word-break:break-all;">{{couponRes.name}}</view></view><view class="uni-dialog-content-options"><view class="uni-dialog-content-text">每人发送数量：</view><input class="uni-dialog-input" type="text" placeholder="{{placeholder}}" data-event-opts="{{[['input',[['__set_model',['','sendingQuantity','$event',[]]]]]]}}" value="{{sendingQuantity}}" bindinput="__e"/></view><view class="uni-dialog-content-options"><view class="uni-dialog-content-text">共计发送人数：</view><view>{{numberSenders}}</view></view></view><view class="uni-dialog-button-group"><view data-event-opts="{{[['tap',[['closePopop',['$event']]]]]}}" class="uni-dialog-button" bindtap="__e"><text class="uni-dialog-button-text">取消</text></view><view data-event-opts="{{[['tap',[['SendCoupons',['$event']]]]]}}" class="uni-dialog-button uni-border-left" bindtap="__e"><text class="uni-dialog-button-text uni-button-color">发送</text></view></view></view></uni-popup></block></block><popmsg class="vue-ref" vue-id="26bbfe6a-4" data-ref="popmsg" bind:__l="__l"></popmsg><block wx:if="{{loading}}"><loading vue-id="26bbfe6a-5" bind:__l="__l"></loading></block></view>