<view><block wx:if="{{isload}}"><block><view class="contentdata"><view class="list"><view class="item"><view class="head-view"><view class="avat-view" data-url="{{uinfo.bid==0?'/pages/index/index':'/pagesExt/business/index?id='+uinfo.bid}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="avat-img-view"><image src="{{set.logo}}"></image></view><view class="user-info"><text class="un-text">{{uinfo.un}}</text></view></view><view class="flex flex-y-center"><block wx:if="{{showrecharge}}"><view class="recharge" data-url="recharge" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">充值</view></block><image class="f4" src="{{pre_url+'/static/img/arrowright.png'}}"></image></view></view></view></view><block wx:if="{{false}}"><view class="list"><block wx:if="{{uinfo.tmpl_orderconfirm_show==1}}"><view><view class="item"><view class="f2">订单提交通知</view><view class="f3"><switch value="1" checked="{{uinfo.tmpl_orderconfirm==1?true:false}}" data-type="tmpl_orderconfirm" data-event-opts="{{[['change',[['switchchange',['$event']]]]]}}" bindchange="__e"></switch></view></view><view style="color:#999;font-size:24rpx;margin-bottom:10rpx;" data-tmplid="{{wxtmplset.tmpl_orderconfirm}}" data-event-opts="{{[['tap',[['addsubnum',['$event']]]]]}}" bindtap="__e">剩余可接收次数：<text style="color:#FC5648;font-weight:bold;font-size:30rpx;">{{uinfo.tmpl_orderconfirmNum}}</text>，每点击此处一次可增加一次机会</view></view></block><block wx:if="{{uinfo.tmpl_orderpay_show==1}}"><view><view class="item"><view class="f2">订单支付通知</view><view class="f3"><switch value="1" checked="{{uinfo.tmpl_orderpay==1?true:false}}" data-type="tmpl_orderpay" data-event-opts="{{[['change',[['switchchange',['$event']]]]]}}" bindchange="__e"></switch></view></view><view style="color:#999;font-size:24rpx;margin-bottom:10rpx;" data-tmplid="{{wxtmplset.tmpl_orderconfirm}}" data-event-opts="{{[['tap',[['addsubnum',['$event']]]]]}}" bindtap="__e">剩余可接收次数：<text style="color:#FC5648;font-weight:bold;font-size:30rpx;">{{uinfo.tmpl_orderconfirmNum}}</text>，每点击此处一次可增加一次机会</view></view></block><block wx:if="{{uinfo.tmpl_ordershouhuo_show==1}}"><view><view class="item"><view class="f2">订单收货通知</view><view class="f3"><switch value="1" checked="{{uinfo.tmpl_ordershouhuo==1?true:false}}" data-type="tmpl_ordershouhuo" data-event-opts="{{[['change',[['switchchange',['$event']]]]]}}" bindchange="__e"></switch></view></view><view style="color:#999;font-size:24rpx;margin-bottom:10rpx;" data-tmplid="{{wxtmplset.tmpl_ordershouhuo}}" data-event-opts="{{[['tap',[['addsubnum',['$event']]]]]}}" bindtap="__e">剩余可接收次数：<text style="color:#FC5648;font-weight:bold;font-size:30rpx;">{{uinfo.tmpl_ordershouhuoNum}}</text>，每点击此处一次可增加一次机会</view></view></block><block wx:if="{{uinfo.tmpl_ordertui_show==1}}"><view><view class="item"><view class="f2">退款申请通知</view><view class="f3"><switch value="1" checked="{{uinfo.tmpl_ordertui==1?true:false}}" data-type="tmpl_ordertui" data-event-opts="{{[['change',[['switchchange',['$event']]]]]}}" bindchange="__e"></switch></view></view><view style="color:#999;font-size:24rpx;margin-bottom:10rpx;" data-tmplid="{{wxtmplset.tmpl_ordertui}}" data-event-opts="{{[['tap',[['addsubnum',['$event']]]]]}}" bindtap="__e">剩余可接收次数：<text style="color:#FC5648;font-weight:bold;font-size:30rpx;">{{uinfo.tmpl_ordertuiNum}}</text>，每点击此处一次可增加一次机会</view></view></block><block wx:if="{{uinfo.tmpl_withdraw_show==1}}"><view><view class="item"><view class="f2">提现申请通知</view><view class="f3"><switch value="1" checked="{{uinfo.tmpl_withdraw==1?true:false}}" data-type="tmpl_withdraw" data-event-opts="{{[['change',[['switchchange',['$event']]]]]}}" bindchange="__e"></switch></view></view><view style="color:#999;font-size:24rpx;margin-bottom:10rpx;" data-tmplid="{{wxtmplset.tmpl_withdraw}}" data-event-opts="{{[['tap',[['addsubnum',['$event']]]]]}}" bindtap="__e">剩余可接收次数：<text style="color:#FC5648;font-weight:bold;font-size:30rpx;">{{uinfo.tmpl_withdrawNum}}</text>，每点击此处一次可增加一次机会</view></view></block><block wx:if="{{uinfo.tmpl_formsub_show==1}}"><view class="item"><view class="f2">表单提交通知</view><view class="f3"><switch value="1" checked="{{uinfo.tmpl_formsub==1?true:false}}" data-type="tmpl_formsub" data-event-opts="{{[['change',[['switchchange',['$event']]]]]}}" bindchange="__e"></switch></view></view></block><block wx:if="{{uinfo.tmpl_kehuzixun_show==1}}"><view><view class="item"><view class="f2">用户咨询通知</view><view class="f3"><switch value="1" checked="{{uinfo.tmpl_kehuzixun==1?true:false}}" data-type="tmpl_kehuzixun" data-event-opts="{{[['change',[['switchchange',['$event']]]]]}}" bindchange="__e"></switch></view></view><view style="color:#999;font-size:24rpx;margin-bottom:30rpx;" data-tmplid="{{wxtmplset.tmpl_kehuzixun}}" data-event-opts="{{[['tap',[['addsubnum',['$event']]]]]}}" bindtap="__e">剩余可接收次数：<text style="color:#FC5648;font-weight:bold;font-size:30rpx;">{{uinfo.tmpl_kehuzixunNum}}</text>，每点击此处一次可增加一次机会</view></view></block></view></block><view class="list"><block wx:if="{{uinfo.bid>0}}"><view class="item"><view class="f2">店铺休息</view><view class="f3"><switch value="1" checked="{{set.is_open==0?true:false}}" data-type="is_open" data-event-opts="{{[['change',[['switchOpen',['$event']]]]]}}" bindchange="__e"></switch></view></view></block><block wx:if="{{uinfo.bid>0}}"><view style="color:#999;font-size:24rpx;margin-bottom:30rpx;">休息时不接单</view></block><block wx:if="{{uinfo.bid>0}}"><view class="item" data-url="setinfo" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="f2">店铺设置</view><text class="f3"></text><image class="f4" src="{{pre_url+'/static/img/arrowright.png'}}"></image></view></block><view class="item" data-url="setpwd" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="f2">修改密码</view><text class="f3"></text><image class="f4" src="{{pre_url+'/static/img/arrowright.png'}}"></image></view><view class="item" data-url="../index/login" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="f2">切换账号</view><text class="f3"></text><image class="f4" src="{{pre_url+'/static/img/arrowright.png'}}"></image></view></view></view></block></block><popmsg class="vue-ref" vue-id="f2b8a13e-1" data-ref="popmsg" bind:__l="__l"></popmsg></view>