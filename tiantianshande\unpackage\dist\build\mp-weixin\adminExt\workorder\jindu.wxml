<view><view class="banner" style="{{('background: linear-gradient(180deg, '+$root.m0+' 0%, rgba('+$root.m1+',0) 100%);')}}"></view><view class="page"><view class="header fa"><view class="f1"><block wx:if="{{detail.status==0}}"><view class="header_title">待处理</view></block><block wx:if="{{detail.status==1}}"><view class="header_title">{{''+detail.clname+''}}</view></block><block wx:if="{{detail.clusername}}"><view class="clusername" style="color:#fff;margin-top:10rpx;"><text>{{"处理人："+detail.clusername}}</text></view></block><view class="header_text fa" data-url="{{'formdetail?id='+id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">查看工单详情<image class="header_icon" src="{{pre_url+'/static/imgsrc/work_detail.png'}}" mode="widthFix"></image></view></view><image class="header_tag" src="{{pre_url+'/static/imgsrc/work_tag.png'}}" mode="widthFix"></image></view><view class="desc"><parse vue-id="e62e7d32-1" content="{{form.desc}}" bind:__l="__l"></parse></view><view class="body"><view class="body_title"><text>工单进度</text><image class="body_icon" src="{{pre_url+'/static/imgsrc/work_title.png'}}" mode="widthFix"></image></view><view class="content"><block wx:if="{{$root.g0>0}}"><block><block wx:for="{{data}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="module"><image class="module_tag" src="{{pre_url+'/static/imgsrc/'+(index>0?'work_dot':'work_dotA')+'.jpg'}}" mode="widthFix"></image><view class="{{['module_title '+(index>0?'module_null':'')]}}">{{''+item.desc+''}}</view><view class="module_text">{{''+item.remark+''}}</view><block wx:if="{{item.content_pic}}"><view class="module_img"><block wx:for="{{item.content_pic}}" wx:for-item="pic" wx:for-index="picindex"><view><image style="width:100rpx;" src="{{pic}}" mode="widthFix" data-url="{{pic}}" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image></view></block></view></block><block wx:for="{{form.contentuser}}" wx:for-item="formitem" wx:for-index="formindex"><view class="hfitem" style="{{(dataindex!=index&&formindex>1?'display:none':'')}}"><text class="{{['t1',formitem.key=='separate'?'title':'']}}">{{formitem.val1}}</text><block wx:if="{{formitem.key!='upload'&&formitem.key!='upload_file'&&formitem.key!='upload_video'}}"><text class="t2">{{item['form'+formindex]}}</text></block><block wx:if="{{formitem.key=='upload'}}"><view class="t2" style="display:flex;justify-content:flex-end;"><block wx:for="{{item['form'+formindex]}}" wx:for-item="sub" wx:for-index="indx" wx:key="indx"><view><image style="width:50px;margin-left:10rpx;" src="{{sub}}" mode="widthFix" data-url="{{sub}}" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image></view></block></view></block><block wx:if="{{formitem.key=='upload_file'&&item['form'+formindex]}}"><view class="t2" style="overflow:hidden;white-space:pre-wrap;word-wrap:break-word;color:#4786BC;" data-file="{{item['form'+formindex]}}" data-event-opts="{{[['tap',[['download',['$event']]]]]}}" bindtap="__e">点击下载查看</view></block><block wx:if="{{formitem.key=='upload_video'&&item['form'+formindex]}}"><view class="t2"><video style="width:100%;" src="{{item['form'+formindex]}}"></video></view></block></view></block><block wx:if="{{dataindex==formindex}}"><view class="zktext" data-dataindex="{{index}}" data-type="zhedie" data-event-opts="{{[['tap',[['zhankai1',['$event']]]]]}}" bindtap="__e">收起<image src="{{pre_url+'/static/img/workorder/shoqi.png'}}"></image></view></block><block wx:else><view class="zktext" data-dataindex="{{index}}" data-type="zhankai" data-event-opts="{{[['tap',[['zhankai1',['$event']]]]]}}" bindtap="__e">展开<image src="{{pre_url+'/static/img/workorder/more.png'}}"></image></view></block><view class="module_time">{{''+item.time+''}}</view><view class="module_opt fx"><block wx:for="{{item.hflist}}" wx:for-item="hf" wx:for-index="hfindex" wx:key="hfindex"><view><block wx:if="{{hf.hfremark}}"><view class="t3"><text class="t3_1">我的回复：</text>{{hf.hfremark+''}}</view></block><block wx:if="{{hf.hftime}}"><view class="t4"><text class="t4_1">回复时间：</text>{{hf.hftime+''}}</view></block><block wx:for="{{form.contentuser}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="hfitem" style="{{(curindex!=hfindex&&index>1?'display:none':'')}}"><text class="{{['t1',item.key=='separate'?'title':'']}}">{{item.val1}}</text><block wx:if="{{item.key!='upload'&&item.key!='upload_file'&&item.key!='upload_video'}}"><text class="t2">{{hf['form'+index]}}</text></block><block wx:if="{{item.key=='upload'}}"><view class="t2" style="display:flex;justify-content:flex-end;"><block wx:for="{{hf['form'+index]}}" wx:for-item="sub" wx:for-index="indx" wx:key="indx"><view><image style="width:50px;margin-left:10rpx;" src="{{sub}}" mode="widthFix" data-url="{{sub}}" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image></view></block></view></block><block wx:if="{{item.key=='upload_file'&&hf['form'+index]}}"><view class="t2" style="overflow:hidden;white-space:pre-wrap;word-wrap:break-word;color:#4786BC;" data-file="{{hf['form'+index]}}" data-event-opts="{{[['tap',[['download',['$event']]]]]}}" bindtap="__e">点击下载查看</view></block><block wx:if="{{item.key=='upload_video'&&hf['form'+index]}}"><view class="t2"><video style="width:100%;" src="{{hf['form'+index]}}"></video></view></block></view></block><block wx:if="{{curindex==hfindex}}"><view class="zktext" data-curindex="{{hfindex}}" data-type="zhedie" data-event-opts="{{[['tap',[['zhankai',['$event']]]]]}}" bindtap="__e">收起<image src="{{pre_url+'/static/img/workorder/shoqi.png'}}"></image></view></block><block wx:else><view class="zktext" data-curindex="{{hfindex}}" data-type="zhankai" data-event-opts="{{[['tap',[['zhankai',['$event']]]]]}}" bindtap="__e">展开<image src="{{pre_url+'/static/img/workorder/more.png'}}"></image></view></block></view></block></view></view></block></block></block><block wx:else><block><view class="module"><image class="module_tag" src="{{pre_url+'/static/imgsrc/work_dot.jpg'}}" mode="widthFix"></image><view class="module_title module_null">等待处理</view></view></block></block></view></view><view class="item"><block wx:if="{{detail.status!=2}}"><view class="modal"><view class="modal_jindu"><form data-formcontent="{{form.contentuser}}" data-event-opts="{{[['submit',[['formsubmit',['$event']]]]]}}" bindsubmit="__e"><view class="title">选择流程</view><view class="uni-list"><radio-group name="liucheng"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index"><label class="uni-list-cell uni-list-cell-pd"><view><block wx:if="{{item.g1>0}}"><block><radio style="transform:scale(0.7);" value="{{''+item.$orig.id}}" checked="{{data[0].lcid==item.$orig.id?true:false}}"></radio></block></block><block wx:else><block><radio style="transform:scale(0.7);" value="{{''+item.$orig.id}}"></radio></block></block></view><view>{{item.$orig.name}}</view></label></block></radio-group><block wx:for="{{form.contentuser}}" wx:for-item="item" wx:for-index="index"><block><view class="form-item"><view class="label">{{item.val1}}<block wx:if="{{item.val3==1}}"><text style="color:red;">*</text></block></view><block wx:if="{{item.key=='input'}}"><block><input class="input" type="text" name="{{'form'+index}}" placeholder="{{item.val2}}" data-formidx="{{'form'+index}}" placeholder-style="font-size:28rpx"/></block></block><block wx:if="{{item.key=='upload'}}"><block><input style="display:none;" type="text" name="{{'form'+index}}" value="{{editorFormdata['content_pics'+index]}}"/><view class="flex" style="flex-wrap:wrap;padding-top:20rpx;"><block wx:for="{{editorFormdata['content_pic'+index]}}" wx:for-item="item1" wx:for-index="index1" wx:key="*this"><block wx:if="{{editorFormdata['content_pic'+index]}}"><view class="form-imgbox"><view class="form-imgbox-close" data-pindex="{{index1}}" data-field="{{'content_pic'+index}}" data-idx="{{index}}" data-event-opts="{{[['tap',[['removeimg',['$event']]]]]}}" bindtap="__e"><image src="{{pre_url+'/static/img/ico-del.png'}}"></image></view><view class="form-imgbox-img"><image src="{{item1}}" data-url="{{item1}}" mode="widthFix" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image></view></view></block></block><view class="form-uploadbtn" style="{{'background:'+('url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 50rpx')+';'+('background-size:'+('80rpx 80rpx')+';')+('background-color:'+('#F3F3F3')+';')+('background-position:'+('center')+';')}}" data-field="{{'content_pic'+index}}" data-idx="{{index}}" data-formidx="{{'form'+index}}" data-event-opts="{{[['tap',[['editorChooseImage',['$event']]]]]}}" bindtap="__e"></view></view></block></block><block wx:if="{{item.key=='upload_file'}}"><block><input style="display:none;" type="text" name="{{'form'+index}}" value="{{editorFormdata[index]}}"/><view class="flex-y-center" style="flex-wrap:wrap;padding-top:20rpx;"><block wx:if="{{editorFormdata[index]}}"><view class="dp-form-imgbox"><view class="dp-form-imgbox-close" data-idx="{{index}}" data-formidx="{{'form'+index}}" data-event-opts="{{[['tap',[['removeimg2',['$event']]]]]}}" bindtap="__e"><image class="image" src="{{pre_url+'/static/img/ico-del.png'}}"></image></view><view style="overflow:hidden;white-space:pre-wrap;word-wrap:break-word;color:#4786BC;" data-file="{{editorFormdata[index]}}" data-event-opts="{{[['tap',[['download',['$event']]]]]}}" bindtap="__e">文件已上传成功</view></view></block><block wx:else><block><view class="dp-form-uploadbtn" style="{{'margin-right:20rpx;'+('background:'+('url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 30rpx')+';')+('background-size:'+('50rpx 50rpx')+';')+('background-color:'+('#F3F3F3')+';')}}" data-idx="{{index}}" data-id="{{index}}" data-formidx="{{'form'+index}}" data-event-opts="{{[['tap',[['chooseFile',['$event']]]]]}}" bindtap="__e"></view><block wx:if="{{item.val2}}"><view style="color:#999;">{{item.val2}}</view></block></block></block></view></block></block><block wx:if="{{item.key=='upload_video'}}"><block><input style="display:none;" type="text" name="{{'form'+index}}" value="{{editorFormdata[index]}}"/><view class="flex-y-center" style="flex-wrap:wrap;padding-top:20rpx;"><block wx:if="{{editorFormdata[index]}}"><view class="dp-form-imgbox"><view class="dp-form-imgbox-close" data-idx="{{index}}" data-formidx="{{'form'+index}}" data-event-opts="{{[['tap',[['removeimg2',['$event']]]]]}}" bindtap="__e"><image class="image" src="{{pre_url+'/static/img/ico-del.png'}}"></image></view><view style="overflow:hidden;white-space:pre-wrap;word-wrap:break-word;color:#4786BC;width:230rpx;"><video style="width:100%;" src="{{editorFormdata[index]}}"></video></view></view></block><block wx:else><block><view class="dp-form-uploadbtn" style="{{'margin-right:20rpx;'+('background:'+('url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 30rpx')+';')+('background-size:'+('50rpx 50rpx')+';')+('background-color:'+('#F3F3F3')+';')}}" data-idx="{{index}}" data-id="{{index}}" data-formidx="{{'form'+index}}" data-event-opts="{{[['tap',[['upVideo',['$event']]]]]}}" bindtap="__e"></view><block wx:if="{{item.val2}}"><view style="color:#999;">{{item.val2}}</view></block></block></block></view></block></block></view></block></block></view><button class="btn" form-type="submit">提交</button></form></view></view></block></view></view></view>