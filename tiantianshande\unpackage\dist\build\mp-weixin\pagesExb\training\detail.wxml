<view><block wx:if="{{isload}}"><block><view class="container"><view class="header"><block wx:if="{{detail.showname==1}}"><text class="title">{{detail.name}}</text></block><block wx:if="{{detail.subname}}"><view class="training-meta"><text class="subtitle">{{detail.subname}}</text></view></block><block wx:if="{{detail.showsendtime==1||detail.showauthor==1||detail.showreadcount==1}}"><view class="artinfo"><block wx:if="{{detail.showsendtime==1}}"><text class="t1">{{detail.createtime}}</text></block><block wx:if="{{detail.showauthor==1}}"><text class="t2">{{detail.author}}</text></block><block wx:if="{{detail.showreadcount==1}}"><text class="t3">{{"阅读："+detail.readcount}}</text></block></view></block><block wx:if="{{detail.kcname}}"><view class="course-binding"><view class="course-info-card" style="{{'background:'+('linear-gradient(135deg,'+$root.m0+' 0%,rgba('+$root.m1+',0.8) 100%)')+';'}}"><view class="course-left"><image class="course-pic" src="{{detail.kcpic||'/static/img/default-course.png'}}" mode="aspectFill"></image></view><view class="course-right"><view class="course-badge"><text class="course-label">关联课程</text></view><text class="course-title">{{detail.kcname}}</text><view class="course-meta"><block wx:if="{{detail.kcprice>0}}"><text class="course-price">{{"¥"+detail.kcprice}}</text></block><block wx:else><text class="course-price free">免费</text></block><text class="course-tip" data-id="{{detail.kcid}}" data-event-opts="{{[['tap',[['toCourse',['$event']]]]]}}" bindtap="__e">查看课程 ></text></view></view></view></view></block><block wx:if="{{detail.has_permission||detail.need_buy==0}}"><block><view style="padding:8rpx 0;"><dp vue-id="aa1efc94-1" pagecontent="{{pagecontent}}" bind:__l="__l"></dp></view></block></block><block wx:else><block><view class="no-permission-container"><view class="preview-content"><view class="preview-text">{{detail.preview_content||'该训练营需要购买相关课程后才能查看完整内容'}}</view></view><block wx:if="{{buyInfo}}"><view class="buy-prompt-card" style="{{'background:'+('linear-gradient(135deg,'+$root.m2+' 0%,rgba('+$root.m3+',0.8) 100%)')+';'}}"><view class="lock-icon"><image class="lock-img" src="/static/img/lock-icon.png"></image></view><view class="buy-content"><text class="buy-title">解锁完整内容</text><text class="buy-subtitle">{{"购买《"+buyInfo.kc_name+"》课程即可学习"}}</text><block wx:if="{{buyInfo.kc_pic}}"><view class="course-preview"><image class="course-thumb" src="{{buyInfo.kc_pic}}" mode="aspectFill"></image><view class="course-details"><text class="course-price">{{"¥"+buyInfo.kc_price}}</text></view></view></block><button data-event-opts="{{[['tap',[['buyCourse',['$event']]]]]}}" class="buy-btn" style="{{'background:'+($root.m4)+';'}}" bindtap="__e">立即购买课程</button></view></view></block></view></block></block></view><block wx:if="{{detail.canpl==1&&(detail.has_permission||detail.need_buy==0)}}"><block><view class="plbox"><view class="plbox_title"><text class="t1">评论</text><text>{{"("+plcount+")"}}</text></view><view class="plbox_content"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="idx" wx:key="id"><block><view class="item1 flex"><view class="f1 flex0"><image src="{{item.$orig.headimg}}"></image></view><view class="f2 flex-col"><text class="t1">{{item.$orig.nickname}}</text><view class="t2 plcontent"><parse vue-id="{{'aa1efc94-2-'+idx}}" content="{{item.$orig.content}}" bind:__l="__l"></parse></view><block wx:if="{{item.g0>0}}"><block><view class="relist"><block wx:for="{{item.$orig.replylist}}" wx:for-item="hfitem" wx:for-index="index" wx:key="index"><block><view class="item2"><view>{{hfitem.nickname+"："}}</view><view class="f2 plcontent"><parse vue-id="{{'aa1efc94-3-'+idx+'-'+index}}" content="{{hfitem.content}}" bind:__l="__l"></parse></view></view></block></block></view></block></block><view class="t3 flex"><text>{{item.$orig.createtime}}</text><view class="flex1"><block wx:if="{{detail.canplrp==1}}"><text class="phuifu" style="cursor:pointer;" data-url="{{'../training/pinglun?type=1&id='+detail.id+'&hfid='+item.$orig.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">回复</text></block></view><view class="flex-y-center pzan" data-id="{{item.$orig.id}}" data-index="{{idx}}" data-event-opts="{{[['tap',[['pzan',['$event']]]]]}}" bindtap="__e"><image src="{{'/static/img/zan-'+(item.$orig.iszan==1?'2':'1')+'.png'}}"></image>{{item.$orig.zan}}</view></view></view></view></block></block></view><block wx:if="{{loading}}"><loading vue-id="aa1efc94-4" bind:__l="__l"></loading></block></view><view style="height:160rpx;"></view><view class="pinglun notabbarbot"><view class="pinput" data-url="{{'../training/pinglun?type=0&id='+detail.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">发表评论</view><view class="zan flex-y-center" data-id="{{detail.id}}" data-event-opts="{{[['tap',[['zan',['$event']]]]]}}" bindtap="__e"><image src="{{'/static/img/zan-'+(iszan?'2':'1')+'.png'}}"></image><text style="padding-left:2px;">{{detail.zan}}</text></view></view></block></block></view></block></block><block wx:if="{{loading}}"><loading vue-id="aa1efc94-5" bind:__l="__l"></loading></block><dp-tabbar vue-id="aa1efc94-6" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="aa1efc94-7" data-ref="popmsg" bind:__l="__l"></popmsg></view>