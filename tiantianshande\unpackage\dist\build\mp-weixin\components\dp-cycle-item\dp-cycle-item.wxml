<view style="width:100%;"><view class="dp-product-item"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="id"><view class="item" style="{{(showstyle==2?'width:49%;margin-right:'+(index%2==0?'2%':0):showstyle==3?'width:32%;margin-right:'+(index%3!=2?'2%':0):'width:100%')}}" data-url="{{'/pagesExt/cycle/product?id='+item.$orig[idfield]}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="product-pic"><image class="image" src="{{item.$orig.pic}}" mode="widthFix"></image><block wx:if="{{saleimg!=''}}"><image class="saleimg" src="{{saleimg}}" mode="widthFix"></image></block></view><view class="product-info"><block wx:if="{{showname==1}}"><view class="p1">{{item.$orig.name}}</view></block><view class="p2"><block wx:if="{{showprice!='0'&&(item.$orig.price_type!=1||item.$orig.sell_price>0)}}"><view class="p2-1"><text class="t1" style="{{'color:'+(item.m0)+';'}}"><text style="font-size:24rpx;">￥</text>{{item.$orig.sell_price}}</text><block wx:if="{{showprice=='1'&&item.$orig.market_price*1>item.$orig.sell_price*1}}"><text class="t2">{{"￥"+item.$orig.market_price}}</text></block><block wx:if="{{item.$orig.juli}}"><text class="t3" style="color:#888;">{{item.$orig.juli}}</text></block></view></block><block wx:if="{{item.$orig.xunjia_text&&item.$orig.price_type==1&&item.$orig.sell_price<=0}}"><view class="p2-1" style="height:50rpx;line-height:44rpx;"><block wx:if="{{showstyle!=1}}"><text class="t1" style="{{'color:'+(item.m1)+';'+('font-size:'+('30rpx')+';')}}">询价</text></block><block wx:if="{{showstyle==1}}"><text class="t1" style="{{'color:'+(item.m2)+';'}}">询价</text></block><block wx:if="{{item.$orig.xunjia_text&&item.$orig.price_type==1}}"><block><view class="lianxi" style="{{'background:'+(item.m3)+';'}}" data-lx_name="{{item.$orig.lx_name}}" data-lx_bid="{{item.$orig.lx_bid}}" data-lx_bname="{{item.$orig.lx_bname}}" data-lx_tel="{{item.$orig.lx_tel}}" data-btntype="2" data-event-opts="{{[['tap',[['showLinkChange',['$event']]]]]}}" catchtap="__e">{{item.$orig.xunjia_text?item.$orig.xunjia_text:'联系TA'}}</view></block></block></view></block></view><block wx:if="{{item.$orig.merchant_name}}"><view class="p1" style="color:#666;font-size:24rpx;white-space:nowrap;text-overflow:ellipsis;margin-top:6rpx;height:30rpx;line-height:30rpx;font-weight:normal;"><text>{{item.$orig.merchant_name}}</text></view></block><block wx:if="{{item.$orig.main_business}}"><view class="p1" style="color:#666;font-size:24rpx;margin-top:4rpx;font-weight:normal;"><text>{{item.$orig.main_business}}</text></view></block><view class="p3"><view class="p3-1" style="{{'background:'+('rgba('+$root.m4+',0.12)')+';'+('color:'+($root.m5)+';')}}">{{item.$orig.ps_cycle_title}}</view><block wx:if="{{showsales=='1'&&item.$orig.sales>0}}"><view class="p3-2"><text style="overflow:hidden;">{{"已售"+item.$orig.sales+"件"}}</text></view></block></view><block wx:if="{{showsales!='1'||item.$orig.sales<=0}}"><view style="height:44rpx;"></view></block></view><block wx:if="{{item.$orig.hongbaoEdu>0}}"><view class="bg-desc" style="{{'background:'+('linear-gradient(90deg,'+item.m6+' 0%,rgba('+item.m7+',0.8) 100%)')+';'}}">{{"可获额度 +"+item.$orig.hongbaoEdu}}</view></block></view></block></view><block wx:if="{{buydialogShow}}"><buydialog vue-id="617f540a-1" proid="{{proid}}" menuindex="{{menuindex}}" data-event-opts="{{[['^addcart',[['addcart']]],['^buydialogChange',[['buydialogChange']]]]}}" bind:addcart="__e" bind:buydialogChange="__e" bind:__l="__l"></buydialog></block><block wx:if="{{showLinkStatus}}"><view class="posterDialog linkDialog"><view class="main"><view data-event-opts="{{[['tap',[['showLinkChange',['$event']]]]]}}" class="close" bindtap="__e"><image class="img" src="/static/img/close.png"></image></view><view class="content"><view class="title">{{lx_name}}</view><block wx:if="{{lx_bid>0}}"><view class="row"><view class="f1">店铺名称</view><view class="f2" data-url="{{'/pagesExt/business/index?id='+lx_bid}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">{{lx_bname}}<image class="image" src="/static/img/arrowright.png"></image></view></view></block><block wx:if="{{lx_tel}}"><view class="row"><view class="f1">联系电话</view><view class="f2" style="{{'color:'+($root.m8)+';'}}" data-url="{{'tel::'+lx_tel}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">{{lx_tel}}<image class="copyicon" src="/static/img/copy.png" data-text="{{lx_tel}}" data-event-opts="{{[['tap',[['copy',['$event']]]]]}}" catchtap="__e"></image></view></view></block></view></view></view></block></view>