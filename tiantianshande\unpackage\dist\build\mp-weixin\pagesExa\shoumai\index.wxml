<view class="container"><view class="page-header"><text class="page-title">{{pageTitle}}</text><view class="header-actions"><view data-event-opts="{{[['tap',[['toggleFilter',['$event']]]]]}}" class="filter-btn" bindtap="__e"><text class="filter-text">{{my===1?'查看全部':'只看我的'}}</text><text class="filter-icon">{{my===1?'🔍':'👤'}}</text></view><view class="add-btn" data-url="/pagesExa/shoumai/sale" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><text class="add-icon">+</text><text class="add-text">發布</text></view></view></view><view class="segment-control"><view class="scroll-container"><view data-event-opts="{{[['tap',[['activeTabFn',[0]]]]]}}" class="{{['segment',(activeTab===0)?'active':'']}}" bindtap="__e">售賣中</view><view data-event-opts="{{[['tap',[['activeTabFn',[1]]]]]}}" class="{{['segment',(activeTab===1)?'active':'']}}" bindtap="__e">交易中</view><view data-event-opts="{{[['tap',[['activeTabFn',[2]]]]]}}" class="{{['segment',(activeTab===2)?'active':'']}}" bindtap="__e">已完成</view></view></view><view class="tab-content"><block wx:if="{{$root.g0}}"><view class="empty-state"><image class="empty-icon" src="/static/img/empty.png" mode="aspectFit"></image><text class="empty-text">{{emptyStateText}}</text><block wx:if="{{activeTab===0&&my===0}}"><view class="empty-action"><text data-url="/pagesExa/shoumai/sale" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">立即發布售賣</text></view></block></view></block><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="list-item"><view class="list-item-header"><view class="seller-info"><block wx:if="{{item.$orig.is_author===1}}"><text class="seller-badge">我的</text></block><text class="seller-name">{{item.$orig.is_author===1?'我':item.$orig.nickname}}</text></view><text class="{{['item-status',(activeTab===0)?'status-selling':'',(activeTab===2&&item.$orig.status===3)?'status-canceled':'',(activeTab===2&&item.$orig.status===1)?'status-success':'',(activeTab===2&&item.$orig.status===2)?'status-rejected':'',(activeTab===2&&item.$orig.status!==1&&item.$orig.status!==2&&item.$orig.status!==3)?'status-completed':'',(activeTab===1&&item.$orig.is_voucher===0)?'status-pending':'',(activeTab===1&&item.$orig.is_voucher===1)?'status-reviewing':'']}}">{{item.m0}}</text></view><view class="list-item-content"><view class="asset-info"><view class="asset-type"><text class="asset-label">{{item.m1}}</text></view><view class="asset-amount"><text class="amount-value">{{item.$orig.commission}}</text></view></view><view class="transaction-details"><view class="detail-row"><text class="detail-label">折扣率:</text><text class="detail-value">{{item.g1+"%"}}</text></view><view class="detail-row"><text class="detail-label">約合人民幣:</text><text class="detail-value price">{{"¥"+item.g2}}</text></view><block wx:if="{{activeTab===1&&item.$orig.is_sale===1}}"><view class="detail-row"><text class="detail-label">購買人:</text><text class="detail-value">{{item.$orig.buy_nickname}}</text></view></block><view class="detail-row"><text class="detail-label">發布時間:</text><text class="detail-value time">{{item.$orig.createtime}}</text></view></view></view><view class="list-item-footer"><block wx:if="{{activeTab===0}}"><block wx:if="{{item.$orig.is_author===0}}"><button data-event-opts="{{[['tap',[['openModal',['$0'],[[['saleList','',index]]]]]]]}}" class="action-btn buy-btn" bindtap="__e"><text class="btn-icon">💰</text><text class="btn-text">{{item.m2}}</text></button></block><block wx:if="{{item.$orig.is_author===1}}"><button data-event-opts="{{[['tap',[['cancelOrder',['$0',index],[[['saleList','',index]]]]]]]}}" class="action-btn cancel-btn" bindtap="__e"><text class="btn-icon">✖</text><text class="btn-text">{{item.m3}}</text></button></block></block><block wx:if="{{activeTab===1}}"><block wx:if="{{item.$orig.is_buy===1&&item.$orig.is_voucher===0}}"><button data-event-opts="{{[['tap',[['upVoucher',['$0'],[[['saleList','',index]]]]]]]}}" class="action-btn upload-btn" bindtap="__e"><text class="btn-icon">📤</text><text class="btn-text">{{item.m4}}</text></button></block><block wx:if="{{item.$orig.is_sale===1&&item.$orig.is_voucher===1}}"><button data-event-opts="{{[['tap',[['openAudit',['$0'],[[['saleList','',index]]]]]]]}}" class="action-btn audit-btn" bindtap="__e"><text class="btn-icon">✓</text><text class="btn-text">{{item.m5}}</text></button></block><block wx:if="{{item.$orig.is_buy===1}}"><button data-event-opts="{{[['tap',[['cancelOrder',['$0',index],[[['saleList','',index]]]]]]]}}" class="action-btn cancel-btn" bindtap="__e"><text class="btn-icon">✖</text><text class="btn-text">{{item.m6}}</text></button></block></block><block wx:if="{{activeTab===2}}"><button data-event-opts="{{[['tap',[['showDetail',['$0'],[[['saleList','',index]]]]]]]}}" class="action-btn detail-btn" bindtap="__e"><text class="btn-text">查看詳情</text></button></block></view></view></block><block wx:if="{{loading}}"><view class="loading-more"><text class="loading-text">加載中...</text></view></block></view><block wx:if="{{showModal&&selectedItem}}"><view class="modal _div"><view class="modal-content _div"><view class="modal-header"><text class="modal-title">{{$root.m7}}</text><text data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" class="modal-close" bindtap="__e">✕</text></view><view class="modal-body"><view class="modal-section"><view class="section-title">賣家信息</view><view class="info-row"><text class="info-label">賣家:</text><text class="info-value">{{selectedItem.nickname}}</text></view><view class="info-row"><text class="info-label">資產類型:</text><text class="info-value">{{$root.m8}}</text></view><view class="info-row"><text class="info-label">可買數量:</text><text class="info-value">{{selectedItem.commission}}</text></view><view class="info-row"><text class="info-label">折扣率:</text><text class="info-value">{{$root.g3+"%"}}</text></view></view><view class="modal-section"><view class="section-title">購買信息</view><view class="buy-input-wrapper"><text class="input-label">購買數量</text><input class="buy-input" type="number" placeholder="請輸入購買數量" data-event-opts="{{[['input',[['__set_model',['','buyData','$event',[]]],['inputData',['$event']]]]]}}" value="{{buyData}}" bindinput="__e"/></view><view class="buy-result"><text class="result-label">應付金額:</text><text class="result-value">{{"¥"+$root.g4}}</text></view><view class="buy-note"><text>* 提交後請及時上傳支付憑證，確保交易順利完成</text></view></view></view><view class="modal-footer"><button data-event-opts="{{[['tap',[['e1',['$event']]]]]}}" class="modal-btn cancel" bindtap="__e">{{$root.m9}}</button><button data-event-opts="{{[['tap',[['buyItem',['$0'],['selectedItem']]]]]}}" class="modal-btn confirm" bindtap="__e">{{$root.m10}}</button></view></view></view></block><block wx:if="{{showAuditModal&&selectedItem}}"><view class="modal _div"><view class="modal-content _div"><view class="modal-header"><text class="modal-title">審核支付憑證</text><text data-event-opts="{{[['tap',[['e2',['$event']]]]]}}" class="modal-close" bindtap="__e">✕</text></view><view class="modal-body"><view class="modal-section"><view class="section-title">訂單信息</view><view class="info-row"><text class="info-label">購買人:</text><text class="info-value">{{selectedItem.buy_nickname}}</text></view><view class="info-row"><text class="info-label">資產類型:</text><text class="info-value">{{$root.m11}}</text></view><view class="info-row"><text class="info-label">交易數量:</text><text class="info-value">{{selectedItem.commission}}</text></view><view class="info-row"><text class="info-label">應付金額:</text><text class="info-value highlight">{{"¥"+$root.g5}}</text></view><view class="info-row"><text class="info-label">發布時間:</text><text class="info-value">{{selectedItem.createtime}}</text></view></view><view class="modal-section"><view class="section-title">支付憑證</view><view class="voucher-images"><block wx:for="{{selectedItem.buy_voucher}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="voucher-image-wrapper"><image class="voucher-image" src="{{item}}" data-url="{{item}}" mode="aspectFit" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image></view></block></view><view class="audit-note"><text>請務必確認上傳支付憑證的有效性與真實性，支付金額必須與訂單應付金額一致。</text></view></view></view><view class="modal-footer"><button data-event-opts="{{[['tap',[['postAudit',['$0',2],['selectedItem']]]]]}}" class="modal-btn reject" bindtap="__e">不通過</button><button data-event-opts="{{[['tap',[['postAudit',['$0',1],['selectedItem']]]]]}}" class="modal-btn approve" bindtap="__e">通過</button></view></view></view></block><dp-tabbar vue-id="be693540-1" opt="{{opt}}" data-event-opts="{{[['^getmenuindex',[['getmenuindex']]]]}}" bind:getmenuindex="__e" bind:__l="__l"></dp-tabbar></view>