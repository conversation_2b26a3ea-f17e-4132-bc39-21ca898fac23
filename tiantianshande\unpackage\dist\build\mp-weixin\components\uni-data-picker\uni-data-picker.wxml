<view class="uni-data-tree data-v-4192b466" style="{{(styleData)}}"><view data-event-opts="{{[['tap',[['handleInput',['$event']]]]]}}" class="uni-data-tree-input data-v-4192b466" bindtap="__e"><block wx:if="{{$slots.default}}"><slot></slot><scoped-slots-default options="{{options}}" data="{{inputSelected}}" error="{{errorMessage}}" class="scoped-ref" bind:__l="__l"></scoped-slots-default></block><block wx:else><view class="{{['input-value','data-v-4192b466',(border)?'input-value-border':'']}}"><block wx:if="{{errorMessage}}"><text class="selected-area error-text data-v-4192b466">{{errorMessage}}</text></block><block wx:else><block wx:if="{{loading&&!isOpened}}"><view class="selected-area data-v-4192b466"><uni-load-more class="load-more data-v-4192b466" vue-id="07d06f50-1" contentText="{{loadMore}}" status="loading" bind:__l="__l"></uni-load-more></view></block><block wx:else><block wx:if="{{$root.g0}}"><scroll-view class="selected-area data-v-4192b466" scroll-x="true"><view class="selected-list data-v-4192b466"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="selected-item data-v-4192b466"><text class="data-v-4192b466">{{item.$orig.text}}</text><block wx:if="{{index<item.g1-1}}"><text class="input-split-line data-v-4192b466">{{split}}</text></block></view></block></view></scroll-view></block><block wx:else><text class="selected-area placeholder data-v-4192b466">{{placeholder}}</text></block></block></block><block wx:if="{{!readonly&&border}}"><view class="arrow-area data-v-4192b466"><view class="input-arrow data-v-4192b466"></view></view></block></view></block></view><block wx:if="{{isOpened}}"><view data-event-opts="{{[['tap',[['handleClose',['$event']]]]]}}" class="uni-data-tree-cover data-v-4192b466" bindtap="__e"></view></block><block wx:if="{{isOpened}}"><view class="uni-data-tree-dialog data-v-4192b466"><view class="dialog-caption data-v-4192b466"><view class="title-area data-v-4192b466"><text class="dialog-title data-v-4192b466">{{popupTitle}}</text></view><view data-event-opts="{{[['tap',[['handleClose',['$event']]]]]}}" class="dialog-close data-v-4192b466" bindtap="__e"><view class="dialog-close-plus data-v-4192b466" data-id="close"></view><view class="dialog-close-plus dialog-close-rotate data-v-4192b466" data-id="close"></view></view></view><data-picker-view class="picker-view data-v-4192b466 vue-ref" vue-id="07d06f50-2" localdata="{{localdata}}" preload="{{preload}}" collection="{{collection}}" field="{{field}}" orderby="{{orderby}}" where="{{where}}" step-searh="{{stepSearh}}" self-field="{{selfField}}" parent-field="{{parentField}}" managed-mode="{{true}}" data-ref="pickerView" value="{{value}}" data-event-opts="{{[['^change',[['onchange']]],['^datachange',[['ondatachange']]],['^input',[['__set_model',['','value','$event',[]]]]]]}}" bind:change="__e" bind:datachange="__e" bind:input="__e" bind:__l="__l"></data-picker-view></view></block></view>