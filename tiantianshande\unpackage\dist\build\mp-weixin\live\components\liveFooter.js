require('../common/vendor.js');(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["live/components/liveFooter"],{1114:function(e,t,n){"use strict";n.r(t);var i=n("5c35"),o=n("bf00");for(var s in o)["default"].indexOf(s)<0&&function(e){n.d(t,e,(function(){return o[e]}))}(s);n("3b565");var a=n("828b"),u=Object(a["a"])(o["default"],i["b"],i["c"],!1,null,"914506cc",null,!1,i["a"],void 0);t["default"]=u.exports},"23e6":function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i=getApp(),o={components:{likeButton:function(){n.e("components/like-button/like-button").then(function(){return resolve(n("e2a2"))}.bind(null,n)).catch(n.oe)}},props:{room_id:{type:[Number,String],required:!0}},data:function(){return{likeImgs:[],msg:"",isCleared:!1}},mounted:function(){this.likeImgs=[n("c565"),n("13e4"),n("308d"),n("8962"),n("97b3"),n("5117")]},methods:{openGift:function(){this.$emit("sendGift",{index:1})},sendGift:function(e){this.$refs.giftPopup.close(),this.$emit("sendGift",{index:e})},welcomeUser:function(){this.$emit("welcomeUser")},sendMsg:function(){i.sendSocketMessage({type:"live_web",content:this.msg,data:{aid:i.globalData.aid,mid:i.globalData.mid,room:this.room_id}}),this.msg=""},clear:function(){this.isCleared=!this.isCleared,console.log("Clear state:",this.isCleared),this.$emit("clear",this.isCleared)},zan:function(){i.sendSocketMessage({type:"live_web",data:{aid:i.globalData.aid,mid:i.globalData.mid,room:this.room_id,live_type:"like"}})}}};t.default=o},"3b565":function(e,t,n){"use strict";var i=n("7374"),o=n.n(i);o.a},"5c35":function(e,t,n){"use strict";n.d(t,"b",(function(){return o})),n.d(t,"c",(function(){return s})),n.d(t,"a",(function(){return i}));var i={uniPopup:function(){return Promise.all([n.e("common/vendor"),n.e("components/uni-popup/uni-popup")]).then(n.bind(null,"ca44a"))}},o=function(){var e=this.$createElement;this._self._c},s=[]},7374:function(e,t,n){},bf00:function(e,t,n){"use strict";n.r(t);var i=n("23e6"),o=n.n(i);for(var s in i)["default"].indexOf(s)<0&&function(e){n.d(t,e,(function(){return i[e]}))}(s);t["default"]=o.a}}]);
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'live/components/liveFooter-create-component',
    {
        'live/components/liveFooter-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('df3c')['createComponent'](__webpack_require__("1114"))
        })
    },
    [['live/components/liveFooter-create-component']]
]);
