<view class="container"><block wx:if="{{isload}}"><block><form data-event-opts="{{[['submit',[['formSubmit',['$event']]]],['reset',[['formReset',['$event']]]]]}}" bindsubmit="__e" bindreset="__e"><view class="avatar-setting"><view class="title">请设置头像昵称</view><view class="form"><view class="form-item avatar-item"><view class="label">头像</view><button class="avatar-btn" open-type="chooseAvatar" data-event-opts="{{[['chooseavatar',[['onChooseAvatar',['$event']]]]]}}" bindchooseavatar="__e"><image class="avatar-img" src="{{headimg||default_headimg}}"></image></button></view><view class="form-item nickname-item"><view class="label">昵称</view><input class="input" type="nickname" placeholder="请输入昵称" name="nickname" placeholder-style="font-size:30rpx;color:#B2B5BE" data-event-opts="{{[['input',[['__set_model',['','nickname','$event',[]]]]]]}}" value="{{nickname}}" bindinput="__e"/></view></view></view><button class="set-btn" style="{{'background:'+('linear-gradient(90deg,'+$root.m0+' 0%,rgba('+$root.m1+',0.8) 100%)')+';'}}" form-type="submit">保 存</button></form></block></block><block wx:if="{{loading}}"><loading vue-id="fad293b2-1" bind:__l="__l"></loading></block><dp-tabbar vue-id="fad293b2-2" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="fad293b2-3" data-ref="popmsg" bind:__l="__l"></popmsg></view>