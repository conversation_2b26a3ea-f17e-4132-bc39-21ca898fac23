<view class="container"><block wx:if="{{packageInfo}}"><view class="card order-info-card"><view class="product-info"><image class="product-image" src="{{packageInfo.product_pic||packageInfo.service_pic}}" mode="aspectFill"></image><view class="info"><text class="name">{{packageInfo.product_name||packageInfo.service_name}}</text><text class="package">{{"来自套餐："+packageOrderInfo.package_name}}</text></view></view><view class="order-detail"><view class="detail-item"><text class="label">套餐订单号：</text><text class="value">{{packageOrderInfo.ordernum}}</text></view><view class="detail-item"><text class="label">剩余次数：</text><text class="value">{{packageInfo.remain_num||packageInfo.remain_times}}</text></view></view></view></block><view class="card service-info-card"><view class="body_item service-method-item"><view class="body_title flex flex-bt">服务方式<text class="body_text">请选择服务方式</text></view><view class="body_content"><view data-event-opts="{{[['tap',[['selectServiceType',[1]]]]]}}" class="{{['body_tag',fwtype==1?'body_active':'']}}" style="{{(fwtype==1?'border-color:'+$root.m0+';background:rgba('+$root.m1+',0.1);color:'+$root.m2:'')}}" bindtap="__e">到店服务</view><view data-event-opts="{{[['tap',[['selectServiceType',[2]]]]]}}" class="{{['body_tag',fwtype==2?'body_active':'']}}" style="{{(fwtype==2?'border-color:'+$root.m3+';background:rgba('+$root.m4+',0.1);color:'+$root.m5:'')}}" bindtap="__e">上门服务</view></view></view><block wx:if="{{fwtype==2}}"><view data-event-opts="{{[['tap',[['goToAddressPage',['$event']]]]]}}" class="address-section" bindtap="__e"><view class="address-content flex-y-center"><image class="location-icon" src="/static/img/address.png"></image><block wx:if="{{addressInfo&&addressInfo.id}}"><view class="address-details flex1"><view class="user-info">{{addressInfo.name+" "+addressInfo.tel+''}}<block wx:if="{{addressInfo.company}}"><text>{{addressInfo.company}}</text></block></view><view class="address-text">{{addressInfo.area+" "+addressInfo.address}}</view></view></block><block wx:else><view class="placeholder flex1">请选择服务地址</view></block><image class="arrow-icon" src="/static/img/arrowright.png"></image></view></view></block><block wx:if="{{fwtype==1}}"><view class="contact-section"><view class="linkitem"><text class="label">联 系 人</text><input class="input" type="text" placeholder="请输入您的姓名" placeholder-class="input-placeholder" data-event-opts="{{[['input',[['inputLinkman',['$event']]]]]}}" value="{{linkman}}" bindinput="__e"/></view><view class="linkitem"><text class="label">联系电话</text><input class="input" type="text" placeholder="请输入您的手机号" placeholder-class="input-placeholder" data-event-opts="{{[['input',[['inputTel',['$event']]]]]}}" value="{{tel}}" bindinput="__e"/></view></view></block></view><view class="footer-placeholder"></view><view class="footer"><button class="submit-btn" style="{{(canSubmit?'background:linear-gradient(90deg,'+$root.m6+' 0%,rgba('+$root.m7+',0.8) 100%); box-shadow: 0 6rpx 12rpx rgba('+$root.m8+',0.3)':'background:#ccc')}}" disabled="{{!canSubmit}}" data-event-opts="{{[['tap',[['submitAppoint',['$event']]]]]}}" bindtap="__e">确认使用</button></view></view>