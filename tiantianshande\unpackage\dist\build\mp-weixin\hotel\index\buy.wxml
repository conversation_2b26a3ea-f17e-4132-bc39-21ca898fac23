<view><block wx:if="{{isload}}"><block><form data-event-opts="{{[['submit',[['createorder',['$event']]]]]}}" bindsubmit="__e"><view class="navigation" style="{{('background:rgba('+$root.m0+')')}}"><view class="navcontent" style="{{'margin-top:'+(navigationMenu.top+'px')+';'+('width:'+(navigationMenu.right+'px')+';')}}"><view class="header-location-top" style="{{'height:'+(navigationMenu.height+'px')+';'}}"><view data-event-opts="{{[['tap',[['goback',['$event']]]]]}}" class="header-back-but" bindtap="__e"><image src="{{pre_url+'/static/img/hotel/fanhui.png'}}"></image></view><view class="header-page-title" style="color:#fff;">订单详情</view></view></view></view><view style="height:140rpx;"></view><view style="height:80rpx;"></view><view class="titlebg-view" style="{{('background:rgba('+$root.m1+')')}}"></view><view class="position-view"><view class="order-time-details flex flex-col"><view class="time-view flex flex-y-center flex-bt"><view class="time-options flex flex-y-center flex-bt"><view class="month-tetx">{{startDate}}</view><view class="day-tetx">{{startweek+"入住"}}</view></view><view class="content-text"><view class="content-decorate left-c-d"></view>{{'共'+dayCount+'晚'}}<view class="content-decorate right-c-d"></view></view><view class="time-options flex flex-y-center flex-bt"><view class="month-tetx">{{endDate}}</view><view class="day-tetx">{{endweek+"离店"}}</view></view></view><view class="name-info flex flex-y-center flex-bt"><view class="flex flex-col"><view class="name-text">{{room.name}}</view><view class="name-tisp">{{room.tag}}</view></view><view data-event-opts="{{[['tap',[['showDetail',['$event']]]]]}}" class="hotel-details-view flex flex-y-center" style="{{'color:'+($root.m2)+';'}}" bindtap="__e">房型详情<image src="{{pre_url+'/static/img/arrowright.png'}}"></image></view></view><block wx:if="{{hotel.isrefund}}"><view class="time-warning flex flex-y-center" style="{{('background: rgba('+$root.m3+',0.2);color:'+$root.m4+'')}}"><image src="{{pre_url+'/static/img/hotel/error.png'}}"></image>{{'限时取消 '+startDate+" "+hotel.refund_hour+'点前免费取消'}}</view></block></view><view class="order-options-view flex flex-col"><view class="options-title flex flex-bt flex-y-center"><view>订房信息</view><view data-event-opts="{{[['tap',[['bookingChange',['$event']]]]]}}" class="right-view flex flex-y-center" style="{{'color:'+($root.m5)+';'}}" bindtap="__e">订房说明<image src="{{pre_url+'/static/img/hotel/shuom.png'}}"></image></view></view><view class="booking-options flex flex-y-center flex-bt"><view class="book-title">{{''+text['间']+'数'}}</view><view class="room-number-view"><scroll-view style="width:100%;white-space:nowrap;" scroll-x="{{true}}"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index"><block><view data-event-opts="{{[['tap',[['changebook',['$0'],[[['room.limitnums','',index]]]]]]]}}" class="room-options" style="{{(item.$orig==changebookIndex?'background: rgba('+item.m6+',0.2);color:'+item.m7+'':'')}}" bindtap="__e">{{item.$orig+text['间']}}</view></block></block></scroll-view></view></view><block wx:for="{{num}}" wx:for-item="item" wx:for-index="index"><block><view class="booking-options flex flex-y-center flex-bt form-item" style="padding:0;"><view class="book-title">住客姓名</view><view class="room-form"><input style="text-align:right;" name="{{'name'+index}}" placeholder="仅填写1人姓名(姓名不可重复)"/></view></view></block></block><view class="booking-options flex flex-y-center flex-bt form-item"><view class="book-title">联系方式</view><view class="room-form"><input style="text-align:right;" name="tel" placeholder="请填写联系方式" data-event-opts="{{[['input',[['__set_model',['','tel','$event',[]]]]]]}}" value="{{tel}}" bindinput="__e"/></view></view><block wx:for="{{hotel.formdata}}" wx:for-item="item" wx:for-index="idx" wx:key="id"><view class="form-item booking-options flex flex-y-center flex-bt"><view class="book-title">{{item.val1}}<block wx:if="{{item.val3==1}}"><text style="color:red;">*</text></block></view><block wx:if="{{item.key=='input'}}"><block><input class="input" type="text" name="{{'form'+'_'+idx}}" placeholder="{{item.val2}}" placeholder-style="font-size:28rpx"/></block></block><block wx:if="{{item.key=='textarea'}}"><block><textarea class="textarea" name="{{'form'+'_'+idx}}" placeholder="{{item.val2}}" placeholder-style="font-size:28rpx"></textarea></block></block><block wx:if="{{item.key=='radio'}}"><block><radio-group class="radio-group" name="{{'form'+'_'+idx}}"><block wx:for="{{item.val2}}" wx:for-item="item1" wx:for-index="idx1" wx:key="id"><label class="flex-y-center"><radio class="radio" value="{{item1}}"></radio>{{item1+''}}</label></block></radio-group></block></block><block wx:if="{{item.key=='checkbox'}}"><block><checkbox-group class="checkbox-group" name="{{'form'+'_'+idx}}"><block wx:for="{{item.val2}}" wx:for-item="item1" wx:for-index="idx1" wx:key="id"><label class="flex-y-center"><checkbox class="checkbox" value="{{item1}}"></checkbox>{{item1+''}}</label></block></checkbox-group></block></block><block wx:if="{{item.key=='selector'}}"><block><picker class="picker" mode="selector" name="{{'form'+'_'+idx}}" value="" range="{{item.val2}}" data-bid="{{hotel.bid}}" data-idx="{{idx}}" data-event-opts="{{[['change',[['editorBindPickerChange',['$event']]]]]}}" bindchange="__e"><block wx:if="{{hotel.editorFormdata[idx]||hotel.editorFormdata[idx]===0}}"><view>{{''+item.val2[hotel.editorFormdata[idx]]}}</view></block><block wx:else><view>请选择</view></block></picker><text class="iconfont iconjiantou" style="color:#999;font-weight:normal;"></text></block></block><block wx:if="{{item.key=='time'}}"><block><picker class="picker" mode="time" name="{{'form'+'_'+idx}}" value="" start="{{item.val2[0]}}" end="{{item.val2[1]}}" range="{{item.val2}}" data-bid="{{hotel.bid}}" data-idx="{{idx}}" data-event-opts="{{[['change',[['editorBindPickerChange',['$event']]]]]}}" bindchange="__e"><block wx:if="{{hotel.editorFormdata[idx]}}"><view>{{hotel.editorFormdata[idx]}}</view></block><block wx:else><view>请选择</view></block></picker><text class="iconfont iconjiantou" style="color:#999;font-weight:normal;"></text></block></block><block wx:if="{{item.key=='date'}}"><block><picker class="picker" mode="date" name="{{'form'+'_'+idx}}" value="" start="{{item.val2[0]}}" end="{{item.val2[1]}}" range="{{item.val2}}" data-bid="{{hotel.bid}}" data-idx="{{idx}}" data-event-opts="{{[['change',[['editorBindPickerChange',['$event']]]]]}}" bindchange="__e"><block wx:if="{{hotel.editorFormdata[idx]}}"><view>{{hotel.editorFormdata[idx]}}</view></block><block wx:else><view>请选择</view></block></picker><text class="iconfont iconjiantou" style="color:#999;font-weight:normal;"></text></block></block><block wx:if="{{item.key=='upload'}}"><block><input style="display:none;" type="text" name="{{'form'+'_'+idx}}" value="{{hotel.editorFormdata[idx]}}"/><view class="flex" style="flex-wrap:wrap;padding-top:20rpx;"><block wx:if="{{hotel.editorFormdata[idx]}}"><view class="form-imgbox"><view class="layui-imgbox-close" style="z-index:2;" data-bid="{{hotel.bid}}" data-idx="{{idx}}" data-event-opts="{{[['tap',[['removeimg',['$event']]]]]}}" bindtap="__e"><image style="display:block;" src="{{pre_url+'/static/img/ico-del.png'}}"></image></view><view class="form-imgbox-img"><image class="image" src="{{hotel.editorFormdata[idx]}}" data-url="{{hotel.editorFormdata[idx]}}" mode="aspectFit" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image></view></view></block><block wx:else><view class="form-uploadbtn" style="{{'background:'+('url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 50rpx')+';'+('background-size:'+('80rpx 80rpx')+';')+('background-color:'+('#F3F3F3')+';')}}" data-bid="{{hotel.bid}}" data-idx="{{idx}}" data-event-opts="{{[['tap',[['editorChooseImage',['$event']]]]]}}" bindtap="__e"></view></block></view></block></block></view></block></view><view class="order-options-view flex flex-col"><view class="options-title">本单可享</view><view class="preferential-view flex flex-y-center flex-bt"><view class="pre-title">{{$root.m8}}</view><view class="pre-text flex flex-y-center"><block wx:if="{{$root.g0}}"><view data-event-opts="{{[['tap',[['showCouponList',['$event']]]]]}}" class="f2" bindtap="__e"><text style="{{'color:#fff;padding:4rpx 16rpx;font-weight:normal;border-radius:8rpx;font-size:24rpx;'+('background:'+($root.m9)+';')}}">{{''+(couponrid!=0?couponnametext:$root.g1+'张可用')+''}}</text><text class="iconfont iconjiantou" style="color:#999;font-weight:normal;"></text></view></block><block wx:else><text class="f2" style="color:#999;">{{"无可用"+$root.m10}}</text></block></view></view></view><block wx:if="{{hotel.money_dikou==1&&userinfo.money>0}}"><view class="price order-options-view flex flex-col"><checkbox-group class="flex" style="width:100%;" data-rate="{{hotel.dikou_bl}}" data-event-opts="{{[['change',[['moneydk',['$event']]]]]}}" bindchange="__e"><view class="f1"><view>{{'使用'+$root.m11+"抵扣（余额："}}<text style="color:#e94745;">{{userinfo.money}}</text>{{moneyunit+'）'}}</view><view style="font-size:24rpx;color:#999;">{{'1、选择此项提交订单时将直接扣除'+$root.m12+''}}</view></view><view class="f2" style="font-weight:normal;"><checkbox style="margin-left:6px;transform:scale(.8);" value="1" checked="{{moneydec?true:false}}"></checkbox></view></checkbox-group></view></block><block wx:if="{{hotel.score_dikou==1&&userinfo.score>0&&hotel.scoredkmaxpercent>0}}"><view class="price order-options-view flex flex-col"><checkbox-group data-event-opts="{{[['change',[['scoredk',['$event']]]]]}}" class="flex" style="width:100%;" bindchange="__e"><view class="f1"><view>{{userinfo.score*1+" "+$root.m13+'可抵扣'}}<text style="color:#e94745;">{{userinfo.scoredk_money*1}}</text>元</view><block wx:if="{{hotel.scoredkmaxpercent>0}}"><view style="font-size:22rpx;color:#999;">{{'最多可抵扣房费金额的'+hotel.scoredkmaxpercent+"%"}}</view></block></view><view class="f2" style="font-weight:normal;"><checkbox style="margin-left:6px;transform:scale(.8);" value="1" disabled="{{isdisabled}}" checked="{{usescore?true:false}}"></checkbox></view></checkbox-group></view></block><view class="order-options-view flex flex-col"><view class="options-title">特殊要求</view><view class="requirement-view"><textarea name="message" placeholder="填写您的入驻环境,服务等要求"></textarea></view></view><view style="height:300rpx;"></view></view><view class="but-view flex flex-col"><block wx:if="{{hotel.xystatus==1}}"><view class="read-agreement flex flex-y-center"><view data-event-opts="{{[['tap',[['changeChecked',['$event']]]]]}}" class="{{['select-view '+(isagree&&isagree1?'select-view-active':'')]}}" style="{{(isagree&&isagree1?'border-color:'+$root.m14+';background-color:'+$root.m15+'':'')}}" bindtap="__e"><image src="{{pre_url+'/static/img/checkd.png'}}"></image></view><view class="flex flex-y-center">我已阅读并同意<view data-event-opts="{{[['tap',[['showxieyiFun2',['$event']]]]]}}" style="{{(ysfont_size>0?'font-size:'+ysfont_size+'rpx;color:red':'color:red')}}" bindtap="__e">{{"《"+hotel.ysname+"》"}}</view>以及<view data-event-opts="{{[['tap',[['showxieyiFun',['$event']]]]]}}" style="{{(ydfont_size>0?'font-size:'+ydfont_size+'rpx;color:red':'color:red')}}" bindtap="__e">{{"《"+hotel.xyname+"》"}}</view></view></view></block><view class="yuding-view flex flex-y-center flex-bt"><view class="price-view flex flex-col"><view class="text">共计:</view><block wx:if="{{moneydec}}"><block><view class="price-text" style="{{'color:'+($root.m16)+';'}}">{{"￥"+totalprice+'+'}}<text style="font-size:24rpx;">{{(moneydec?usemoney:0)+moneyunit}}</text></view></block></block><block wx:else><block><view class="price-text" style="{{'color:'+($root.m17)+';'}}">{{"￥"+totalprice}}</view></block></block></view><view class="flex flex-row flex-y-center"><view data-event-opts="{{[['tap',[['mignxiChange',['$event']]]]]}}" class="cost-details flex flex-y-center" style="{{'color:'+($root.m18)+';'}}" bindtap="__e">费用明细<image src="{{pre_url+'/static/img/arrowdown.png'}}"></image></view><button class="but-class1" style="{{('background:rgba('+$root.m19+',0.8);color:#FFF')}}" form-type="submit">预定</button></view></view></view><block wx:if="{{showxieyi}}"><view class="xieyibox"><view class="xieyibox-content"><view style="overflow:scroll;height:100%;"><parse vue-id="71f13c14-1" content="{{hotel.xycontent}}" data-event-opts="{{[['^navigate',[['navigate']]]]}}" bind:navigate="__e" bind:__l="__l"></parse></view><view class="xieyibut-view flex-y-center"><view data-event-opts="{{[['tap',[['closeXieyi',['$event']]]]]}}" class="but-class" style="background:#A9A9A9;" bindtap="__e">不同意并退出</view><block wx:if="{{hotel.isqianzi==1}}"><view class="but-class" style="{{'background:'+('linear-gradient(90deg,'+$root.m20+' 0%,rgba('+$root.m21+',0.8) 100%)')+';'}}" data-url="signature" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">去签字</view></block><block wx:else><view data-event-opts="{{[['tap',[['hidexieyi',['$event']]]]]}}" class="but-class" style="{{'background:'+('linear-gradient(90deg,'+$root.m22+' 0%,rgba('+$root.m23+',0.8) 100%)')+';'}}" bindtap="__e">阅读并同意</view></block></view></view></view></block><block wx:if="{{showxieyi2}}"><view class="xieyibox"><view class="xieyibox-content"><view style="overflow:scroll;height:100%;"><parse vue-id="71f13c14-2" content="{{hotel.yscontent}}" data-event-opts="{{[['^navigate',[['navigate']]]]}}" bind:navigate="__e" bind:__l="__l"></parse></view><view class="xieyibut-view flex-y-center"><view data-event-opts="{{[['tap',[['closeXieyi',['$event']]]]]}}" class="but-class" style="background:#A9A9A9;" bindtap="__e">不同意并退出</view><view data-event-opts="{{[['tap',[['hidexieyi2',['$event']]]]]}}" class="but-class" style="{{'background:'+('linear-gradient(90deg,'+$root.m24+' 0%,rgba('+$root.m25+',0.8) 100%)')+';'}}" bindtap="__e">阅读并同意</view></view></view></view></block><block wx:if="{{couponvisible}}"><view class="popup__container"><view data-event-opts="{{[['tap',[['handleClickMask',['$event']]]]]}}" class="popup__overlay" catchtap="__e"></view><view class="popup__modal"><view class="popup__title"><text class="popup__title-text">{{"请选择"+$root.m26}}</text><image class="popup__close" style="width:36rpx;height:36rpx;" src="{{pre_url+'/static/img/close.png'}}" data-event-opts="{{[['tap',[['handleClickMask',['$event']]]]]}}" catchtap="__e"></image></view><view class="popup__content"><couponlist vue-id="71f13c14-3" couponlist="{{couponList}}" choosecoupon="{{true}}" selectedrid="{{couponrid}}" bid="{{hotel.bid}}" data-event-opts="{{[['^chooseCoupon',[['chooseCoupon']]]]}}" bind:chooseCoupon="__e" bind:__l="__l"></couponlist></view></view></view></block><uni-popup class="vue-ref" vue-id="71f13c14-4" id="popup" type="bottom" data-ref="popup" bind:__l="__l" vue-slots="{{['default']}}"><view class="hotelpopup__content"><view data-event-opts="{{[['tap',[['popupClose',['$event']]]]]}}" class="popup-close" bindtap="__e"><image src="{{pre_url+'/static/img/hotel/popupClose2.png'}}"></image></view><scroll-view style="height:auto;max-height:50vh;" scroll-y="{{true}}"><view class="hotel-equity-view flex flex-col"><view class="equity-title-view flex" style="padding-bottom:40rpx;"><view class="equity-title">在线支付</view></view><view class="cost-details flex flex-col"><view class="price-view flex flex-bt flex-y-center" style="margin-bottom:30rpx;"><view class="price-text-title">房费</view></view><view class="price-view flex flex-bt flex-y-center"><view class="price-text">{{$root.m27+"抵扣"}}</view><view class="price-num">{{(moneydec?usemoney:0)+moneyunit}}</view></view><view class="price-view flex flex-bt flex-y-center"><view class="price-text">现金支付</view><view class="price-num">{{"￥"+leftmoney}}</view></view></view><view class="cost-details flex flex-col"><view class="price-view flex flex-bt flex-y-center" style="margin-bottom:30rpx;"><view class="price-text-title">其他</view><view class="price-num-title"></view></view><view class="price-view flex flex-bt flex-y-center"><view class="price-text">押金(可退)</view><view class="price-num">{{"￥"+yajin}}</view></view><block wx:if="{{service_money>0}}"><view class="price-view flex flex-bt flex-y-center"><view class="price-text">{{text['服务费']}}</view><view class="price-num">{{"￥"+service_money}}</view></view></block></view><block wx:if="{{coupon_money>0||usescore>0}}"><view class="cost-details flex flex-col"><view class="price-view flex flex-bt flex-y-center" style="margin-bottom:30rpx;"><view class="price-text-title">优惠</view><view class="price-num-title"></view></view><block wx:if="{{coupon_money>0}}"><view class="price-view flex flex-bt flex-y-center"><view class="price-text">优惠券抵扣</view><view class="price-num">{{"-￥"+coupon_money}}</view></view></block><block wx:if="{{usescore>0}}"><view class="price-view flex flex-bt flex-y-center"><view class="price-text">{{$root.m28+"抵扣"}}</view><view class="price-num">{{"-"+scoredk_money}}</view></view></block></view></block></view></scroll-view><view class="popup-but-view flex flex-y-center flex-bt"><view class="price-view flex flex-col"><view class="text">在线付:</view><block wx:if="{{moneydec}}"><block><view class="price-text" style="{{'color:'+($root.m29)+';'}}">{{"￥"+totalprice+''}}<text style="font-size:24rpx;">{{"+"+(moneydec?usemoney:0)+moneyunit}}</text></view></block></block><block wx:else><block><view class="price-text" style="{{'color:'+($root.m30)+';'}}">{{"￥"+totalprice}}</view></block></block></view><view class="flex flex-row flex-y-center"><view data-event-opts="{{[['tap',[['popupClose',['$event']]]]]}}" class="cost-details flex flex-y-center" style="{{'color:'+($root.m31)+';'}}" bindtap="__e">费用明细<image src="{{pre_url+'/static/img/arrowdown.png'}}"></image></view><button class="but-class" style="{{'background:'+('linear-gradient(90deg,'+$root.m32+' 0%,rgba('+$root.m33+',0.8) 100%)')+';'}}" form-type="submit">极速支付</button></view></view></view></uni-popup><uni-popup class="vue-ref" vue-id="71f13c14-5" id="popupBook" type="bottom" data-ref="popupBook" bind:__l="__l" vue-slots="{{['default']}}"><view class="hotelpopup__content" style="background:#fff;"><view data-event-opts="{{[['tap',[['popupClose',['$event']]]]]}}" class="popup-close" bindtap="__e"><image src="{{pre_url+'/static/img/hotel/popupClose2.png'}}"></image></view><view class="popup__title"><view class="popup__title-text">订房说明</view></view><view class="booking_notice">{{room.booking_notice}}</view></view></uni-popup><uni-popup class="vue-ref" vue-id="71f13c14-6" id="popupDetail" type="bottom" data-ref="popupDetail" bind:__l="__l" vue-slots="{{['default']}}"><view class="hotelpopup__content"><view data-event-opts="{{[['tap',[['popupdetailClose',['$event']]]]]}}" class="popup-close" bindtap="__e"><image src="{{pre_url+'/static/img/hotel/popupClose.png'}}"></image></view><scroll-view style="height:auto;max-height:70vh;" scroll-y="{{true}}"><view class="popup-banner-view" style="height:450rpx;"><swiper class="dp-banner-swiper" autoplay="{{true}}" indicator-dots="{{false}}" current="{{0}}" circular="{{true}}" interval="{{3000}}" data-event-opts="{{[['change',[['swiperChange',['$event']]]]]}}" bindchange="__e"><block wx:for="{{room.pics}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><swiper-item><view data-event-opts="{{[['tap',[['viewPicture',['$0'],[[['room.pics','',index]]]]]]]}}" bindtap="__e"><image class="dp-banner-swiper-img" src="{{item}}" mode="widthFix"></image></view></swiper-item></block></block></swiper><block wx:if="{{$root.g2}}"><view class="popup-numstatistics flex flex-xy-center">{{''+bannerindex+" / "+$root.g3+''}}</view></block></view><view class="hotel-details-view flex flex-col"><view class="hotel-title">{{room.name}}</view><view class="introduce-view flex"><block wx:if="{{room.bedxing!='0'}}"><view class="options-intro flex flex-y-center"><image src="{{pre_url+'/static/img/hotel/dachuang.png'}}"></image><view class="options-title">{{room.bedxing}}</view></view></block><view class="options-intro flex flex-y-center"><image src="{{pre_url+'/static/img/hotel/pingfang.png'}}"></image><view class="options-title">{{room.square+"m²"}}</view></view><view class="options-intro flex flex-y-center"><image src="{{pre_url+'/static/img/hotel/dachuang.png'}}"></image><view class="options-title">{{room.bedwidth+"米"}}</view></view><block wx:if="{{room.ischuanghu!=0}}"><view class="options-intro flex flex-y-center"><image src="{{pre_url+'/static/img/hotel/youchuang.png'}}"></image><view class="options-title">{{room.ischuanghu}}</view></view></block><block wx:if="{{room.breakfast!=0}}"><view class="options-intro flex flex-y-center"><image src="{{pre_url+'/static/img/hotel/zaocan.png'}}"></image><view class="options-title">{{room.breakfast+"早餐"}}</view></view></block></view><view class="other-view flex flex-y-center"><view class="other-title">特色</view><view class="other-text" style="white-space:pre-line;">{{room.tese}}</view></view></view><block wx:if="{{qystatus==1}}"><view class="hotel-equity-view flex flex-col"><view class="equity-title-view flex"><view class="equity-title">{{qyname}}</view></view><view class="equity-options flex flex-col"><parse vue-id="{{('71f13c14-7')+','+('71f13c14-6')}}" content="{{hotel.hotelquanyi}}" data-event-opts="{{[['^navigate',[['navigate']]]]}}" bind:navigate="__e" bind:__l="__l"></parse></view></view></block><block wx:if="{{fwstatus==1}}"><view class="hotel-equity-view flex flex-col"><view class="equity-title-view flex"><view class="equity-title">{{fwname}}</view></view><view class="equity-options flex flex-col"><parse vue-id="{{('71f13c14-8')+','+('71f13c14-6')}}" content="{{hotel.hotelfuwu}}" data-event-opts="{{[['^navigate',[['navigate']]]]}}" bind:navigate="__e" bind:__l="__l"></parse></view></view></block></scroll-view></view></uni-popup></form></block></block></view>