require('../../common/vendor.js');(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pagesExa/tuanzhang/buydialog/buydialog"],{2289:function(t,i,o){"use strict";var e=o("454c"),a=o.n(e);a.a},"454c":function(t,i,o){},"56dd":function(t,i,o){"use strict";(function(t){Object.defineProperty(i,"__esModule",{value:!0}),i.default=void 0;var o=getApp(),e={data:function(){return{ks:"",product:{},guigelist:{},guigedata:{},ggselected:{},nowguige:{},jialiaodata:[],jlprice:0,jltitle:"",gwcnum:1,isload:!1,loading:!1,canaddcart:!0,shopset:{},glassrecord:{},showglass:!1,totalprice:0,jlselected:[]}},props:{btntype:{default:0},menuindex:{default:-1},controller:{default:"ApiTuanzhang"},needaddcart:{default:!0},proid:{},tid:{default:0},bid:{type:[String,Number],default:0}},mounted:function(){var i=this;t.$on("getglassrecord",(function(t){i.getglassrecord()})),i.getdata()},beforeDestroy:function(){t.$off("getglassrecord")},methods:{getdata:function(){var t=this;if(console.log("准备发送请求，bid:",t.bid),"ApiShop"==this.controller&&1==o.globalData.isdouyin)return o.showLoading("加载中"),void o.post("ApiShop/getDouyinProductId",{proid:t.proid},(function(t){o.showLoading(!1),1==t.status?tt.openEcGood({promotionId:t.douyin_product_id}):o.alert(t.msg)}));t.loading=!0,o.post(this.controller+"/getproductdetail",{id:t.proid,bid:t.bid||t.$parent.bid||0},(function(i){console.log("请求发送的参数:",{id:t.proid,bid:t.bid}),t.loading=!1,t.product=i.product,t.shopset=i.shopset,t.product.limit_start||(t.product.limit_start=1),t.guigelist=i.guigelist,t.guigedata=i.guigedata;for(var o=i.guigedata,e=[],a=0;a<o.length;a++)e.push(0);t.ks=e.join(","),t.nowguige=t.guigelist[t.ks],t.ggselected=e,t.nowguige.limit_start>0?t.gwcnum=t.nowguige.limit_start:t.gwcnum=t.product.limit_start,t.isload=!0,3!=t.product.freighttype&&4!=t.product.freighttype||(t.canaddcart=!1),1==t.product.product_type&&(t.showglass=!0,t.getglassrecord()),"ApiRestaurantShop"!=t.controller&&"ApiRestaurantTakeaway"!=t.controller||(t.jialiaodata=i.jialiaodata,t.totalprice=t.nowguige.sell_price)}))},buydialogChange:function(){this.$emit("buydialogChange")},getglassrecord:function(t){var i=this,e=o.getCache("_glass_record_id");!0===i.showglass&&(!i.glassrecord||i.glassrecord&&i.glassrecord.id!=e)&&o.post("ApiGlass/myrecord",{pagenum:1,listrow:1,id:e},(function(t){var o=t.data;o.length>0&&(i.glassrecord=o[0])}))},showLinkChange:function(){this.$emit("showLinkChange")},ggchange:function(t){var i=t.currentTarget.dataset.idx,o=t.currentTarget.dataset.itemk,e=this.ggselected;e[o]=i;var a=e.join(",");this.ggselected=e,this.ks=a,this.nowguige=this.guigelist[this.ks],this.nowguige.limit_start>0&&this.gwcnum<this.nowguige.limit_start&&(this.gwcnum=this.nowguige.limit_start),this.totalprice=parseFloat(parseFloat(this.nowguige.sell_price)+this.jlprice).toFixed(2)},jlchange:function(t){this.jialiaodata[t].active=1!=this.jialiaodata[t].active;for(var i=0,o="",e=[],a=0;a<this.jialiaodata.length;a++)this.jialiaodata[a].active&&(i+=parseFloat(this.jialiaodata[a].price),o+=","+this.jialiaodata[a].jltitle,e.push(this.jialiaodata[a]));this.jltitle=o,this.jlprice=i,this.totalprice=parseFloat(parseFloat(this.nowguige.sell_price)+i).toFixed(2),this.jlselected=e,this.jialiaodata=this.jialiaodata},tobuy:function(t){var i=this.ks,e=this.product.id,a=this.guigelist[i].id,r=this.guigelist[i].stock,s=this.gwcnum;if(s<1&&(s=1),r<s)o.error("库存不足");else{var l=e+","+a+","+s;if(this.showglass){var n=o.getCache("_glass_record_id");l+=","+n}this.$emit("buydialogChange"),"ApiTuanzhang"==this.controller?o.goto("/pagesExa/tuanzhang/buy?prodata="+l):"ApiSeckill"==this.controller?o.goto("/activity/seckill/buy?prodata="+l):"ApiSeckill2"==this.controller?o.goto("/activity/seckill2/buy?prodata="+l):"ApiRestaurantTakeaway"==this.controller?o.goto("/restaurant/takeaway/buy?prodata="+l):"ApiRestaurantShop"==this.controller&&o.goto("/restaurant/shop/buy?prodata="+l)}},addcart:function(){var t=this.ks,i=this.gwcnum,e=this.product.id,a=this.guigelist[t].id,r=this.guigelist[t].stock,s=this.tid;if(i<1&&(i=1),r<i)o.error("库存不足");else{var l=0;this.showglass&&(l=o.getCache("_glass_record_id")),this.needaddcart&&o.post(this.controller+"/addcart",{tid:s,proid:e,ggid:a,num:i,glass_record_id:l},(function(t){1==t.status?o.success("添加成功"):o.error(t.msg)})),this.$emit("addcart",{proid:e,ggid:a,num:i,jlprice:this.jlprice,jltitle:this.jltitle}),this.$emit("buydialogChange")}},gwcplus:function(t){var i=this.gwcnum+1,e=this.ks;return i>this.guigelist[e].stock?(o.error("库存不足"),1):this.product.perlimitdan>0&&i>this.product.perlimitdan?(o.error("每单限购"+this.product.perlimitdan+"件"),1):void(this.gwcnum=this.gwcnum+1)},gwcminus:function(t){var i=this.gwcnum-1;this.ks;if(this.nowguige.limit_start>0){if(i<=this.nowguige.limit_start-1)return void(this.nowguige.limit_start>1&&o.error("该规格"+this.nowguige.limit_start+"件起售"))}else if(i<=this.product.limit_start-1)return void(this.product.limit_start>1&&o.error("该商品"+this.product.limit_start+"件起售"));this.gwcnum=this.gwcnum-1},gwcinput:function(t){var i=this.ks,e=parseInt(t.detail.value);return e<1?1:e>this.guigelist[i].stock?this.guigelist[i].stock>0?this.guigelist[i].stock:1:(this.nowguige.limit_start>0?e<=this.nowguige.limit_start-1&&(this.nowguige.limit_start>1&&o.error("该规格"+this.nowguige.limit_start+"件起售"),e=this.nowguige.limit_start):e<=this.product.limit_start-1&&(this.product.limit_start>1&&o.error("该商品"+this.product.limit_start+"件起售"),e=this.product.limit_start),this.product.perlimitdan>0&&e>this.product.perlimitdan&&(o.error("每单限购"+this.product.perlimitdan+"件"),e=this.product.perlimitdan),void(this.gwcnum=e))},hidePriceLink:function(){o.goto(this.product.hide_price_link)}}};i.default=e}).call(this,o("df3c")["default"])},a0d7d:function(t,i,o){"use strict";o.d(i,"b",(function(){return a})),o.d(i,"c",(function(){return r})),o.d(i,"a",(function(){return e}));var e={loading:function(){return o.e("components/loading/loading").then(o.bind(null,"ceaa"))}},a=function(){var t=this,i=t.$createElement,o=(t._self._c,!t.isload||"ApiRestaurantShop"!=t.controller&&"ApiRestaurantTakeaway"!=t.controller?null:t.t("color1")),e=t.isload&&"ApiRestaurantShop"!=t.controller&&"ApiRestaurantTakeaway"!=t.controller&&0==t.product.is_member_yh&&1==t.product.is_newcustom&&(1!=t.product.price_type||t.nowguige.sell_price>0)?t.t("color1"):null,a=t.isload&&"ApiRestaurantShop"!=t.controller&&"ApiRestaurantTakeaway"!=t.controller&&(0!=t.product.is_member_yh||1!=t.product.is_newcustom)&&(1!=t.product.price_type||t.nowguige.sell_price>0)?t.t("color1"):null,r=t.isload&&t.nowguige.balance_price?t.t("color1"):null,s=t.isload?t.jialiaodata.length>0&&("ApiRestaurantShop"==t.controller||"ApiRestaurantTakeaway"==t.controller):null,l=t.isload&&1==t.product.price_type?t.t("color2"):null,n=t.isload&&1!=t.product.price_type&&t.shopset&&1==t.shopset.showcommission&&t.nowguige.commission>0?t.t("color1"):null,c=t.isload&&1!=t.product.price_type&&t.shopset&&1==t.shopset.showcommission&&t.nowguige.commission>0?t.t("佣金"):null,u=!t.isload||1==t.product.price_type||t.nowguige.stock<=0||1==t.product.hide_price||0!=t.btntype?null:t.t("color1"),d=!t.isload||1==t.product.price_type||t.nowguige.stock<=0||1==t.product.hide_price||1!=t.btntype?null:t.t("color2"),g=!t.isload||1==t.product.price_type||t.nowguige.stock<=0||1==t.product.hide_price||2!=t.btntype?null:t.t("color1"),p=!t.isload||1==t.product.price_type||t.nowguige.stock<=0||1!=t.product.hide_price?null:t.t("color1");t.$mp.data=Object.assign({},{$root:{m0:o,m1:e,m2:a,m3:r,g0:s,m4:l,m5:n,m6:c,m7:u,m8:d,m9:g,m10:p}})},r=[]},bc50:function(t,i,o){"use strict";o.r(i);var e=o("56dd"),a=o.n(e);for(var r in e)["default"].indexOf(r)<0&&function(t){o.d(i,t,(function(){return e[t]}))}(r);i["default"]=a.a},d121:function(t,i,o){"use strict";o.r(i);var e=o("a0d7d"),a=o("bc50");for(var r in a)["default"].indexOf(r)<0&&function(t){o.d(i,t,(function(){return a[t]}))}(r);o("2289");var s=o("828b"),l=Object(s["a"])(a["default"],e["b"],e["c"],!1,null,null,null,!1,e["a"],void 0);i["default"]=l.exports}}]);
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'pagesExa/tuanzhang/buydialog/buydialog-create-component',
    {
        'pagesExa/tuanzhang/buydialog/buydialog-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('df3c')['createComponent'](__webpack_require__("d121"))
        })
    },
    [['pagesExa/tuanzhang/buydialog/buydialog-create-component']]
]);
