<view><view class="container"><view class="user-info"><view class="avatar-container"><image class="avatar" src="{{headimg}}"></image><text class="nickname">{{nickname}}</text></view><view class="info-text"><view class="info-row"><text>{{"余额："+balance}}</text><text>{{"积分："+points}}</text></view><view class="info-row"><text>{{"佣金："+commission}}</text><text>{{"RMP："+heiscore}}</text></view></view></view><view class="big-land"><block wx:for="{{plots}}" wx:for-item="plot" wx:for-index="index" wx:key="index"><view class="plot" locked="{{plot.isPig?'true':'false'}}"><block wx:if="{{plot.isLocked}}"><image class="lock-icon" src="{{pre_url+'/static/img/reg-pwd.png'}}" data-event-opts="{{[['tap',[['handlePlotClick',[index]]]]]}}" bindtap="__e"></image></block><block wx:else><view class="unlocked-plot"><block wx:if="{{!plot.isPig}}"><view class="plot-content"><block wx:if="{{plot.status===0}}"><button data-event-opts="{{[['tap',[['handlePlotClick',[index]]]]]}}" class="unlock-button" bindtap="__e">开通地块</button></block><block wx:else><block wx:if="{{plot.status===1}}"><button data-event-opts="{{[['tap',[['joinQueue',['$0'],[[['plots','',index,'plot_id']]]]]]]}}" class="queue-button" bindtap="__e">排队</button><button data-event-opts="{{[['tap',[['buyPiglet',['$0'],[[['plots','',index,'plot_id']]]]]]]}}" class="buy-button" bindtap="__e">买猪仔</button></block></block></view></block></view></block></view></block></view><button data-event-opts="{{[['tap',[['goToPigFarm',['$event']]]]]}}" class="enter-farm-button" bindtap="__e">进入养猪场</button></view><block wx:if="{{loading}}"><loading vue-id="4c2f5c8f-1" bind:__l="__l"></loading></block><popmsg class="vue-ref" vue-id="4c2f5c8f-2" data-ref="popmsg" bind:__l="__l"></popmsg><dp-tabbar vue-id="4c2f5c8f-3" opt="{{opt}}" bind:__l="__l"></dp-tabbar></view>