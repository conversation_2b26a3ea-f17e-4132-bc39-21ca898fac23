require('../common/vendor.js');(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pagesB/dreamark/dialogue"],{"15ed":function(t,e,o){"use strict";o.d(e,"b",(function(){return n})),o.d(e,"c",(function(){return i})),o.d(e,"a",(function(){}));var n=function(){var t=this,e=t.$createElement,o=(t._self._c,t.showStartOverlay?t.t("color1"):null),n=t.showStartOverlay?t.t("color1rgb"):null,i=t.showStartOverlay?null:t.t("color1"),s=!t.showStartOverlay&&t.showInput?t.userInput.trim():null;t.$mp.data=Object.assign({},{$root:{m0:o,m1:n,m2:i,g0:s}})},i=[]},"19dc":function(t,e,o){},"55c8f":function(t,e,o){"use strict";var n=o("19dc"),i=o.n(n);i.a},"59f5":function(t,e,o){"use strict";o.r(e);var n=o("15ed"),i=o("d9bc");for(var s in i)["default"].indexOf(s)<0&&function(t){o.d(e,t,(function(){return i[t]}))}(s);o("55c8f");var a=o("828b"),r=Object(a["a"])(i["default"],n["b"],n["c"],!1,null,null,null,!1,n["a"],void 0);e["default"]=r.exports},6870:function(t,e,o){"use strict";(function(t){Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var o={data:function(){return{showStartOverlay:!0,canvasWidth:375,canvasHeight:667,currentDate:"",isAISpeaking:!1,messages:[],userInput:"",inputPlaceholder:"请输入您的回答...",inputFocus:!1,showInput:!1,scrollTop:0,scrollIntoView:"",currentStep:1,progressSteps:["姓名","年龄","梦想"],progressWidth:"33%",questions:[{text:"你好！我是来自2049年的AI助手。首先，请告诉我您的姓名？",key:"name",placeholder:"请输入您的姓名...",audioFile:"/static/MP3/jiaoshenme.mp3"},{text:"很高兴认识您！请问您现在多少岁了？",key:"age",placeholder:"请输入您的年龄...",audioFile:"/static/MP3/jisuile.mp3"},{text:"时光荏苒，请分享一下您最大的梦想是什么？",key:"dream",placeholder:"请输入您的梦想...",audioFile:"/static/MP3/mengxiangshishenme.mp3"}],currentQuestionIndex:0,dialogueData:{},typingTimer:null,pre_url:""}},onLoad:function(){console.log("对话页面加载");var t=getApp();this.pre_url=t.globalData.pre_url||"",this.initCanvas(),this.initCurrentDate(),this.checkExistingData()},onReady:function(){},onUnload:function(){this.clearTypingTimer()},methods:{initCanvas:function(){var e=t.getSystemInfoSync();this.canvasWidth=e.windowWidth,this.canvasHeight=e.windowHeight},initCurrentDate:function(){var t=new Date,e=t.getFullYear(),o=String(t.getMonth()+1).padStart(2,"0"),n=String(t.getDate()).padStart(2,"0");this.currentDate="".concat(e,".").concat(o,".").concat(n)},checkExistingData:function(){var e=this,o=t.getStorageSync("user_dialogue_data");if(console.log("检查已有数据:",o),o){if(this.dialogueData=o,this.currentQuestionIndex=Object.keys(o).length,console.log("当前问题索引:",this.currentQuestionIndex),this.currentQuestionIndex>=this.questions.length)return console.log("对话已完成，跳转到拍照页面"),void this.goToCamera();this.showStartOverlay=!1,this.loadExistingMessages()}else console.log("新用户，自动开始对话"),this.showStartOverlay=!1,setTimeout((function(){e.askNextQuestion()}),1e3)},loadExistingMessages:function(){this.messages=[];for(var t=0;t<this.currentQuestionIndex;t++){var e=this.questions[t];this.messages.push({type:"ai",text:e.text,time:this.getCurrentTime(),showPlayBtn:!0,audioFile:e.audioFile}),this.messages.push({type:"user",text:this.dialogueData[e.key],time:this.getCurrentTime()})}this.updateProgress(),this.askNextQuestion()},startDialogue:function(){var t=this;this.showStartOverlay=!1,setTimeout((function(){t.askNextQuestion()}),500)},askNextQuestion:function(){var t=this;if(console.log("询问下一个问题，当前索引:",this.currentQuestionIndex),this.currentQuestionIndex>=this.questions.length)return console.log("所有问题已完成"),void this.completeDialogue();var e=this.questions[this.currentQuestionIndex];console.log("当前问题:",e),this.inputPlaceholder=e.placeholder;var o={type:"ai",text:"",time:this.getCurrentTime(),typing:!0,showPlayBtn:!1,audioFile:e.audioFile};this.messages.push(o),this.scrollToBottom(),this.isAISpeaking=!0,this.typeMessage(e.text,o,(function(){t.isAISpeaking=!1,o.typing=!1,o.showPlayBtn=!0,t.autoPlayAudio(e.audioFile),t.showInput=!0,t.inputFocus=!0,t.$nextTick((function(){t.inputFocus=!1,setTimeout((function(){t.inputFocus=!0}),100)}))}))},typeMessage:function(t,e,o){var n=this;this.clearTypingTimer();var i=0;this.typingTimer=setInterval((function(){i<t.length?(e.text+=t[i],i++,n.scrollToBottom()):(n.clearTypingTimer(),o&&o())}),80)},onInputChange:function(t){this.userInput=t.detail.value},onInputFocus:function(){console.log("输入框获得焦点")},onInputBlur:function(){console.log("输入框失去焦点")},sendMessage:function(){var e=this,o=this.userInput.trim();if(o){this.messages.push({type:"user",text:o,time:this.getCurrentTime()}),this.$nextTick((function(){e.scrollToBottom()}));var n=this.questions[this.currentQuestionIndex];this.dialogueData[n.key]=o,this.saveDialogueData(),this.userInput="",this.showInput=!1,this.inputFocus=!1,this.currentQuestionIndex++,this.updateProgress(),setTimeout((function(){e.scrollToBottom()}),100),setTimeout((function(){e.askNextQuestion()}),1e3)}else t.showToast({title:"请输入内容",icon:"none"})},updateProgress:function(){this.currentStep=this.currentQuestionIndex+1;var t=Math.min(this.currentQuestionIndex/this.questions.length*100,100);this.progressWidth=t+"%"},completeDialogue:function(){var t=this;this.messages.push({type:"ai",text:"感谢您的分享！现在让我们进入下一个环节，通过AI技术预测您20年后的样子。",time:this.getCurrentTime(),showPlayBtn:!0}),this.scrollToBottom(),setTimeout((function(){t.goToCamera()}),3e3)},goToCamera:function(){t.navigateTo({url:"/pagesB/dreamark/camera-new"})},saveDialogueData:function(){try{t.setStorageSync("user_dialogue_data",this.dialogueData)}catch(e){console.error("保存对话数据失败:",e)}},getCurrentTime:function(){var t=new Date,e=String(t.getHours()).padStart(2,"0"),o=String(t.getMinutes()).padStart(2,"0");return"".concat(e,":").concat(o)},scrollToBottom:function(){var t=this;this.$nextTick((function(){t.scrollIntoView="scroll-bottom",console.log("滚动到底部锚点");var e=Date.now();setTimeout((function(){t.scrollTop=999999+e,console.log("备用滚动，scrollTop:",t.scrollTop)}),100),setTimeout((function(){t.scrollIntoView="",t.$nextTick((function(){t.scrollIntoView="scroll-bottom"}))}),200),setTimeout((function(){t.scrollTop=999999+e+1e3}),500),console.log("滚动到底部，消息数量:",t.messages.length)}))},clearTypingTimer:function(){this.typingTimer&&(clearInterval(this.typingTimer),this.typingTimer=null)},autoPlayAudio:function(e){if(e)try{var o=t.createInnerAudioContext();o.src=this.pre_url+e,o.onPlay((function(){console.log("自动播放音频:",e)})),o.onEnded((function(){console.log("音频播放完成"),o.destroy()})),o.onError((function(t){console.error("音频播放错误:",t),o.destroy()})),o.play()}catch(n){console.error("自动播放音频时发生错误:",n)}else console.log("没有音频文件，跳过播放")},playAudio:function(e){try{var o=this.messages[e];if(!o||"ai"!==o.type)return void t.showToast({title:"无法播放此消息",icon:"none"});var n=Math.floor(e/2),i=this.questions[n];if(!i||!i.audioFile)return void t.showToast({title:"音频文件不存在",icon:"none"});var s=t.createInnerAudioContext();s.src=this.pre_url+i.audioFile,s.onPlay((function(){console.log("开始播放音频:",i.audioFile),t.showToast({title:"正在播放语音",icon:"none",duration:1e3})})),s.onEnded((function(){console.log("音频播放完成"),s.destroy()})),s.onError((function(e){console.error("音频播放错误:",e),t.showToast({title:"音频播放失败",icon:"none"}),s.destroy()})),s.play()}catch(a){console.error("播放音频时发生错误:",a),t.showToast({title:"播放失败",icon:"none"})}},skipQuestion:function(){var t=this;if(this.currentQuestionIndex<this.questions.length){var e=this.questions[this.currentQuestionIndex];this.dialogueData[e.key]="跳过",this.saveDialogueData(),this.userInput="",this.showInput=!1,this.currentQuestionIndex++,this.updateProgress(),setTimeout((function(){t.askNextQuestion()}),500)}},showClearDialog:function(){var e=this;console.log("点击清空按钮"),t.showModal({title:"清空对话数据",content:"确定要清空所有对话数据吗？清空后将重新开始对话流程。",confirmText:"确定清空",cancelText:"取消",confirmColor:"#ff4444",success:function(t){t.confirm&&e.clearDialogueData()}})},onClearTouchStart:function(){console.log("清空按钮触摸开始")},onClearTouchEnd:function(){console.log("清空按钮触摸结束")},clearDialogueData:function(){var e=this;try{t.removeStorageSync("user_dialogue_data"),this.dialogueData={},this.messages=[],this.currentQuestionIndex=0,this.showInput=!1,this.userInput="",this.scrollTop=0,this.scrollIntoView="",t.showToast({title:"数据已清空",icon:"success",duration:2e3}),setTimeout((function(){e.askNextQuestion()}),2e3),console.log("对话数据已清空，重新开始")}catch(o){console.error("清空数据失败:",o),t.showToast({title:"清空失败",icon:"error"})}},goBack:function(){t.navigateBack()},clearAndRestart:function(){var e=this;t.showModal({title:"确认清空",content:"确定要清空所有对话记录重新开始吗？",success:function(t){t.confirm&&e.clearAllData()}})},clearAllData:function(){try{t.removeStorageSync("user_dialogue_data"),this.dialogueData={},this.messages=[],this.currentQuestionIndex=0,this.currentStep=1,this.progressWidth="33%",this.showStartOverlay=!0,this.showInput=!1,t.showToast({title:"已清空重新开始",icon:"success"})}catch(e){t.showToast({title:"清空失败",icon:"error"})}}}};e.default=o}).call(this,o("df3c")["default"])},92040:function(t,e,o){"use strict";(function(t,e){var n=o("47a9");o("06e9");n(o("3240"));var i=n(o("59f5"));t.__webpack_require_UNI_MP_PLUGIN__=o,e(i.default)}).call(this,o("3223")["default"],o("df3c")["createPage"])},d9bc:function(t,e,o){"use strict";o.r(e);var n=o("6870"),i=o.n(n);for(var s in n)["default"].indexOf(s)<0&&function(t){o.d(e,t,(function(){return n[t]}))}(s);e["default"]=i.a}},[["92040","common/runtime","common/vendor"]]]);