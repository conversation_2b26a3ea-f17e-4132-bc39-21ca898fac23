require('../common/vendor.js');(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["adminExt/order/addmember"],{2451:function(e,t,n){"use strict";n.r(t);var i=n("b962"),a=n.n(i);for(var r in i)["default"].indexOf(r)<0&&function(e){n.d(t,e,(function(){return i[e]}))}(r);t["default"]=a.a},"32b0":function(e,t,n){"use strict";var i=n("621d"),a=n.n(i);a.a},"621d":function(e,t,n){},"7c617":function(e,t,n){"use strict";(function(e,t){var i=n("47a9");n("06e9");i(n("3240"));var a=i(n("b460"));e.__webpack_require_UNI_MP_PLUGIN__=n,t(a.default)}).call(this,n("3223")["default"],n("df3c")["createPage"])},"9c9b":function(e,t,n){"use strict";n.d(t,"b",(function(){return a})),n.d(t,"c",(function(){return r})),n.d(t,"a",(function(){return i}));var i={loading:function(){return n.e("components/loading/loading").then(n.bind(null,"ceaa"))},popmsg:function(){return n.e("components/popmsg/popmsg").then(n.bind(null,"2bf2"))}},a=function(){var e=this,t=e.$createElement,n=(e._self._c,e.isload?e.pic.length:null),i=e.isload?e.pic.join(","):null,a=e.isload?e.t("color1"):null,r=e.isload?e.t("color1rgb"):null;e.$mp.data=Object.assign({},{$root:{g0:n,g1:i,m0:a,m1:r}})},r=[]},b460:function(e,t,n){"use strict";n.r(t);var i=n("9c9b"),a=n("2451");for(var r in a)["default"].indexOf(r)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(r);n("32b0");var d=n("828b"),o=Object(d["a"])(a["default"],i["b"],i["c"],!1,null,null,null,!1,i["a"],void 0);t["default"]=o.exports},b962:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i=getApp(),a={data:function(){return{isload:!1,loading:!1,pre_url:i.globalData.pre_url,info:{},pic:[],pics:[],cids:[],cids2:[],payTypeArr:[],payTypeIndex:0,addType:0,dejiArr:[],isdefault:""}},onLoad:function(e){this.opt=i.getopts(e),this.addType=e.type,this.getdata()},methods:{bindPickerChange:function(e){this.payTypeIndex=e.detail.value,this.paytype=this.payTypeArr[this.payTypeIndex],this.isdefault=this.dejiArr[this.payTypeIndex].id},getdata:function(){var e=this;e.loading=!0,i.post("ApiAdminMember/memberlevel",{},(function(t){var n=t.data.map((function(e){return e.name}));e.dejiArr=t.data,e.payTypeArr=n,e.isdefault=t.data[0].id,e.loading=!1,e.loaded()}))},subform:function(e){var t=this,n=e.detail.value;return n.nickname?n.tel?void i.post("ApiAdminMember/memberadd",{nickname:n.nickname,headimg:n.pic,tel:n.tel,levelid:t.isdefault,reg_pid:n.pid,pwd:n.pwd,repwd:n.repwd},(function(e){0==e.status?i.error(e.msg):(i.success(e.msg),setTimeout((function(){1==t.addType?i.goto("/adminExt/member/index"):i.goto("dkorder?mid="+e.member.id)}),500))})):i.error("请输入会员手机号"):i.error("请输入会员昵称")},uploadimg:function(e){var t=this,n=parseInt(e.currentTarget.dataset.pernum);n||(n=1);var a=e.currentTarget.dataset.field,r=t[a];r||(r=[]),i.chooseImage((function(e){for(var n=0;n<e.length;n++)r.push(e[n]);"pic"==a&&(t.pic=r),"pics"==a&&(t.pics=r)}),n)},removeimg:function(e){var t=e.currentTarget.dataset.index,n=e.currentTarget.dataset.field;if("pic"==n){var i=this.pic;i.splice(t,1),this.pic=i}else if("pics"==n){i=this.pics;i.splice(t,1),this.pics=i}}}};t.default=a}},[["7c617","common/runtime","common/vendor"]]]);