<view class="container"><block wx:if="{{isload}}"><block><form data-event-opts="{{[['submit',[['topay',['$event']]]]]}}" bindsubmit="__e"><block wx:if="{{needaddress==0}}"><view class="address-add"><view class="linkitem"><text class="f1">联 系 人：</text><input class="input" type="text" placeholder="请输入您的姓名" placeholder-style="color:#626262;font-size:28rpx;" data-event-opts="{{[['input',[['inputLinkman',['$event']]]]]}}" value="{{linkman}}" bindinput="__e"/></view><view class="linkitem"><text class="f1">联系电话：</text><input class="input" type="text" placeholder="请输入您的手机号" placeholder-style="color:#626262;font-size:28rpx;" data-event-opts="{{[['input',[['inputTel',['$event']]]]]}}" value="{{tel}}" bindinput="__e"/></view></view></block><block wx:else><view class="address-add flex-y-center" data-url="{{'/pages/address/address?fromPage=buy&type='+(havetongcheng==1?'1':'0')}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="f1"><image class="img" src="/static/img/address.png"></image></view><block wx:if="{{address.name}}"><view class="f2 flex1"><view style="font-weight:bold;color:#111111;font-size:30rpx;">{{address.name+" "+address.tel}}</view><view style="font-size:24rpx;">{{address.area+" "+address.address}}</view></view></block><block wx:else><view class="f2 flex1">请选择收货地址</view></block><image class="f3" src="/static/img/arrowright.png"></image></view></block><view class="buydata"><view class="btitle"><image class="img" src="/static/img/ico-shop.png"></image>{{business.name}}</view><view class="bcontent"><view class="product"><view class="item flex"><view class="img" data-url="{{'product?id='+product.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image class="img" src="{{product.pic}}"></image></view><view class="info flex1"><view class="f1">{{product.name}}</view><view class="f3">{{"￥"+product.sell_price}}<block wx:if="{{buytype!=1}}"><text class="collage_icon">团购</text></block>{{'× '+num}}</view></view></view></view><view class="freight"><view class="f1">配送方式</view><view class="freight-ul"><view class="flex" style="width:100%;overflow-y:hidden;overflow-x:scroll;"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="idx2" wx:key="idx2"><block><view class="freight-li" style="{{(freightkey==idx2?'color:'+item.m0+';background:rgba('+item.m1+',0.2)':'')}}" data-index="{{idx2}}" data-event-opts="{{[['tap',[['changeFreight',['$event']]]]]}}" bindtap="__e">{{item.$orig.name}}</view></block></block></view></view><block wx:if="{{freightList[freightkey].minpriceset==1&&freightList[freightkey].minprice>0&&freightList[freightkey].minprice*1>product_price*1}}"><view class="freighttips">{{"满"+freightList[freightkey].minprice+"元起送，还差"+$root.g0+"元"}}</view></block><block wx:if="{{freightList[freightkey].isoutjuli==1}}"><view class="freighttips">超出配送范围</view></block></view><block wx:if="{{freightList[freightkey].pstimeset==1}}"><view class="price"><view class="f1">{{(freightList[freightkey].pstype==1?'取货':'配送')+"时间"}}</view><view data-event-opts="{{[['tap',[['choosePstime',['$event']]]]]}}" class="f2" bindtap="__e">{{pstimetext==''?'请选择时间':pstimetext}}<text class="iconfont iconjiantou" style="color:#999;font-weight:normal;"></text></view></view></block><block wx:if="{{freightList[freightkey].pstype==1}}"><view class="storeitem"><view class="panel"><view class="f1">取货地点</view><view class="f2" data-freightkey="{{freightkey}}" data-storekey="{{freightList[freightkey].storekey}}" data-event-opts="{{[['tap',[['openMendian',['$event']]]]]}}" bindtap="__e"><text class="iconfont icondingwei"></text>{{freightList[freightkey].storedata[freightList[freightkey].storekey].name}}</view></view><block wx:for="{{$root.l1}}" wx:for-item="item" wx:for-index="idx" wx:key="idx"><block><block wx:if="{{idx<5||storeshowall==true}}"><view class="radio-item" data-index="{{idx}}" data-event-opts="{{[['tap',[['choosestore',['$event']]]]]}}" catchtap="__e"><view class="f1">{{item.$orig.name+''}}</view><text style="color:#f50;">{{item.$orig.juli}}</text><view class="radio" style="{{(freightList[freightkey].storekey==idx?'background:'+item.m2+';border:0':'')}}"><image class="radio-img" src="/static/img/checkd.png"></image></view></view></block></block></block><block wx:if="{{$root.g1}}"><view data-event-opts="{{[['tap',[['doStoreShowAll',['$event']]]]]}}" class="storeviewmore" bindtap="__e">- 查看更多 -</view></block></view></block><view class="price"><text class="f1">商品金额</text><text class="f2">{{"¥"+product_price}}</text></view><block wx:if="{{leveldk_money*1>0}}"><view class="price"><text class="f1">{{$root.m3+"折扣("+userinfo.discount+"折)"}}</text><text class="f2">{{"-¥"+leveldk_money}}</text></view></block><view class="price"><text class="f1">运费</text><text class="f2">{{"+¥"+freight_price}}</text></view><view class="price"><view class="f1">{{$root.m4}}</view><block wx:if="{{$root.g2>0}}"><view data-event-opts="{{[['tap',[['showCouponList',['$event']]]]]}}" class="f2" bindtap="__e"><text style="{{'color:#fff;padding:4rpx 16rpx;font-weight:normal;border-radius:8rpx;font-size:24rpx;'+('background:'+($root.m5)+';')}}">{{couponrid!=0?couponList[couponkey].couponname:$root.g3+'张可用'}}</text><text class="iconfont iconjiantou" style="color:#999;font-weight:normal;"></text></view></block><block wx:else><text class="f2" style="color:#999;">{{"无可用"+$root.m6}}</text></block></view><view style="display:none;">{{test}}</view><block wx:for="{{freightList[freightkey].formdata}}" wx:for-item="item" wx:for-index="idx" wx:key="id"><view class="form-item"><view class="label">{{item.val1}}<block wx:if="{{item.val3==1}}"><text style="color:red;">*</text></block></view><block wx:if="{{item.key=='input'}}"><block><input class="input" type="text" name="{{'form'+idx}}" placeholder="{{item.val2}}" placeholder-style="font-size:28rpx"/></block></block><block wx:if="{{item.key=='textarea'}}"><block><textarea class="textarea" name="{{'form'+idx}}" placeholder="{{item.val2}}" placeholder-style="font-size:28rpx"></textarea></block></block><block wx:if="{{item.key=='radio'}}"><block><radio-group class="radio-group" name="{{'form'+idx}}"><block wx:for="{{item.val2}}" wx:for-item="item1" wx:for-index="idx1" wx:key="id"><label class="flex-y-center"><radio class="radio" value="{{item1}}"></radio>{{item1+''}}</label></block></radio-group></block></block><block wx:if="{{item.key=='checkbox'}}"><block><checkbox-group class="checkbox-group" name="{{'form'+idx}}"><block wx:for="{{item.val2}}" wx:for-item="item1" wx:for-index="idx1" wx:key="id"><label class="flex-y-center"><checkbox class="checkbox" value="{{item1}}"></checkbox>{{item1+''}}</label></block></checkbox-group></block></block><block wx:if="{{item.key=='selector'}}"><block><picker class="picker" mode="selector" name="{{'form'+idx}}" value="" range="{{item.val2}}" data-idx="{{idx}}" data-event-opts="{{[['change',[['editorBindPickerChange',['$event']]]]]}}" bindchange="__e"><block wx:if="{{editorFormdata[idx]||editorFormdata[idx]===0}}"><view>{{''+item.val2[editorFormdata[idx]]}}</view></block><block wx:else><view>请选择</view></block></picker><text class="iconfont iconjiantou" style="color:#999;font-weight:normal;"></text></block></block><block wx:if="{{item.key=='time'}}"><block><picker class="picker" mode="time" name="{{'form'+idx}}" value="" start="{{item.val2[0]}}" end="{{item.val2[1]}}" range="{{item.val2}}" data-idx="{{idx}}" data-event-opts="{{[['change',[['editorBindPickerChange',['$event']]]]]}}" bindchange="__e"><block wx:if="{{editorFormdata[idx]}}"><view>{{editorFormdata[idx]}}</view></block><block wx:else><view>请选择</view></block></picker><text class="iconfont iconjiantou" style="color:#999;font-weight:normal;"></text></block></block><block wx:if="{{item.key=='date'}}"><block><picker class="picker" mode="date" name="{{'form'+idx}}" value="" start="{{item.val2[0]}}" end="{{item.val2[1]}}" range="{{item.val2}}" data-idx="{{idx}}" data-event-opts="{{[['change',[['editorBindPickerChange',['$event']]]]]}}" bindchange="__e"><block wx:if="{{editorFormdata[idx]}}"><view>{{editorFormdata[idx]}}</view></block><block wx:else><view>请选择</view></block></picker><text class="iconfont iconjiantou" style="color:#999;font-weight:normal;"></text></block></block><block wx:if="{{item.key=='upload'}}"><block><input style="display:none;" type="text" name="{{'form'+idx}}" value="{{editorFormdata[idx]}}"/><view class="flex" style="flex-wrap:wrap;padding-top:20rpx;"><block wx:if="{{editorFormdata[idx]}}"><view class="form-imgbox"><view class="form-imgbox-img"><image class="image" src="{{editorFormdata[idx]}}" data-url="{{editorFormdata[idx]}}" mode="widthFix" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image></view></view></block><view class="form-uploadbtn" style="{{'background:'+('url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 50rpx')+';'+('background-size:'+('80rpx 80rpx')+';')+('background-color:'+('#F3F3F3')+';')}}" data-idx="{{idx}}" data-event-opts="{{[['tap',[['editorChooseImage',['$event']]]]]}}" bindtap="__e"></view></view></block></block></view></block></view></view><block wx:if="{{userinfo.score2money>0&&(userinfo.scoremaxtype==0||userinfo.scoremaxtype==1&&userinfo.scoredkmaxmoney>0)}}"><view class="scoredk"><checkbox-group data-event-opts="{{[['change',[['scoredk',['$event']]]]]}}" class="flex" style="width:100%;" bindchange="__e"><view class="f1"><view>{{userinfo.score*1+" "+$root.m7+'可抵扣'}}<text style="color:#e94745;">{{userinfo.scoredk_money*1}}</text>元</view><block wx:if="{{userinfo.scoremaxtype==0&&userinfo.scoredkmaxpercent>0&&userinfo.scoredkmaxpercent<100}}"><view style="font-size:22rpx;color:#999;">{{'最多可抵扣订单金额的'+userinfo.scoredkmaxpercent+"%"}}</view></block><block wx:else><block wx:if="{{userinfo.scoremaxtype==1}}"><view style="font-size:22rpx;color:#999;">{{'最多可抵扣'+userinfo.scoredkmaxmoney+"元"}}</view></block></block></view><view class="f2">{{"使用"+$root.m8+'抵扣'}}<checkbox style="margin-left:6px;transform:scale(.8);" value="1"></checkbox></view></checkbox-group></view></block><view style="padding:5px 3%;font-size:22rpx;color:red;">注：商品金额并非实际成交的商品金额，活动结束后所有参与人均按照已达成的最低团购价格计算，多付的金额将全部退还</view><view style="width:100%;height:110rpx;"></view><view class="footer flex notabbarbot"><view class="text1 flex1">总计：<text style="font-weight:bold;font-size:36rpx;">{{"￥"+totalprice}}</text></view><button class="op" style="{{'background:'+('linear-gradient(-90deg,'+$root.m9+' 0%,rgba('+$root.m10+',0.8) 100%)')+';'}}" form-type="submit">提交订单</button></view></form><block wx:if="{{couponvisible}}"><view class="popup__container"><view data-event-opts="{{[['tap',[['handleClickMask',['$event']]]]]}}" class="popup__overlay" catchtap="__e"></view><view class="popup__modal"><view class="popup__title"><text class="popup__title-text">{{"请选择"+$root.m11}}</text><image class="popup__close" style="width:36rpx;height:36rpx;" src="/static/img/close.png" data-event-opts="{{[['tap',[['handleClickMask',['$event']]]]]}}" catchtap="__e"></image></view><view class="popup__content"><couponlist vue-id="02a75322-1" couponlist="{{couponList}}" choosecoupon="{{true}}" selectedrid="{{couponrid}}" bid="{{product.bid}}" data-event-opts="{{[['^chooseCoupon',[['chooseCoupon']]]]}}" bind:chooseCoupon="__e" bind:__l="__l"></couponlist></view></view></view></block><block wx:if="{{pstimeDialogShow}}"><view class="popup__container"><view data-event-opts="{{[['tap',[['hidePstimeDialog',['$event']]]]]}}" class="popup__overlay" catchtap="__e"></view><view class="popup__modal"><view class="popup__title"><text class="popup__title-text">{{"请选择"+(freightList[freightkey].pstype==1?'取货':'配送')+"时间"}}</text><image class="popup__close" style="width:36rpx;height:36rpx;" src="/static/img/close.png" data-event-opts="{{[['tap',[['hidePstimeDialog',['$event']]]]]}}" catchtap="__e"></image></view><view class="popup__content"><block wx:for="{{$root.l2}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="pstime-item" data-index="{{index}}" data-event-opts="{{[['tap',[['pstimeRadioChange',['$event']]]]]}}" bindtap="__e"><view class="flex1">{{item.$orig.title}}</view><view class="radio" style="{{(freight_time==item.$orig.value?'background:'+item.m12+';border:0':'')}}"><image class="radio-img" src="/static/img/checkd.png"></image></view></view></block></view></view></view></block></block></block><block wx:if="{{loading}}"><loading vue-id="02a75322-2" bind:__l="__l"></loading></block><dp-tabbar vue-id="02a75322-3" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="02a75322-4" data-ref="popmsg" bind:__l="__l"></popmsg></view>