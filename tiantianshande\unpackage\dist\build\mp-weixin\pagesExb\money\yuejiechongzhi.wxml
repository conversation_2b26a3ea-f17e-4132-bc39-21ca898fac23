<view class="container"><block wx:if="{{isload}}"><block><view class="mymoney" style="{{'background:'+($root.m0)+';'}}"><view class="f1">{{"我的"+$root.m1}}</view><view class="f2"><text style="font-size:26rpx;">￥</text>{{userinfo.arrears}}</view><view class="f3" data-url="/pagesExb/money/moneylog?st=16" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><text>充值记录</text><text class="iconfont iconjiantou" style="font-size:20rpx;"></text></view></view><view class="content2"><view class="item2"><view class="f1">还款金额(元)</view></view><block wx:if="{{caninput==1}}"><block><view class="item3"><view class="f1">￥</view><view class="f2"><input style="font-size:60rpx;" type="digit" name="money" placeholder="请输入金额" placeholder-style="color:#999;font-size:40rpx" data-event-opts="{{[['input',[['moneyinput',['$event']]]]]}}" value="{{money}}" bindinput="__e"/></view></view></block></block><block wx:if="{{$root.g0>0}}"><view class="giveset"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="{{['item',moneyduan==item.$orig.money?'active':'']}}" style="{{(moneyduan==item.$orig.money?'background:'+item.m2:'')}}" data-money="{{item.$orig.money}}" data-event-opts="{{[['tap',[['selectgiveset',['$event']]]]]}}" bindtap="__e"><text class="t1">{{(caninput==1?'满':'充')+item.$orig.money+"元"}}</text><block wx:if="{{item.$orig.give&&item.$orig.give_score}}"><text class="t2">{{"赠"+item.$orig.give+"元+"+item.$orig.give_score+item.m3}}</text></block><block wx:else><block wx:if="{{item.$orig.give&&!item.$orig.give_score}}"><text class="t2">{{"赠送"+item.$orig.give+"元"}}</text></block><block wx:else><block wx:if="{{!item.$orig.give&&item.$orig.give_score}}"><text class="t2">{{"赠送"+item.$orig.give_score+item.m4}}</text></block></block></block></view></block></view></block><block wx:if="{{shuoming}}"><view style="margin-top:40rpx;padding:0 30rpx;line-height:42rpx;"><parse vue-id="61c142fc-1" content="{{shuoming}}" data-event-opts="{{[['^navigate',[['navigate']]]]}}" bind:navigate="__e" bind:__l="__l"></parse></view></block></view><view class="op"><view data-event-opts="{{[['tap',[['topay',['$event']]]]]}}" class="btn" style="{{'background:'+($root.m5)+';'}}" bindtap="__e">去支付</view></view><block wx:if="{{transfer}}"><view class="op"><view class="btn" style="{{'background:'+($root.m6)+';'}}" data-url="rechargeToMember" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">转账</view></view></block></block></block><block wx:if="{{loading}}"><loading vue-id="61c142fc-2" bind:__l="__l"></loading></block><dp-tabbar vue-id="61c142fc-3" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="61c142fc-4" data-ref="popmsg" bind:__l="__l"></popmsg></view>