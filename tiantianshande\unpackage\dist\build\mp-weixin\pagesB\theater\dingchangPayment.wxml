<view><view class="head" style="{{('background:'+secondary_color)}}"><view class="openingMessage_t">{{book.eName}}</view><view class="openingMessage"><block wx:for="{{select}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="openingMessage_d">{{item.cen+"楼  "+item.y+"排"+item.x+"号  ￥"+item.price}}</view></block></view><view class="openingPrompt" style="{{('background:'+primary_color)}}"><view class="spaceBetween"><image class="openingPrompt_img" src="{{pre_url+'/static/img/mistake.png'}}"></image><view class="openingPrompt_txt2">不支持改签</view></view><view class="openingPrompt_txt3">订场必读></view></view></view><view class="content"><view style="padding-top:30rpx;"><view class="specialOffer"><view class="sl_title">优惠活动</view><view class="spaceBetween"><view class="sl_txt1_l">优惠券</view><view class="sl_txt1_r"><view class="sl_txt1_r_a">无可用</view><view class="sl_txt1_r_b">></view></view></view><view data-event-opts="{{[['tap',[['showList',['$event']]]]]}}" class="spaceBetween" style="margin-top:20rpx;border-top:1rpx solid #dcdcdc;padding-top:20rpx;" bindtap="__e"><view class="sl_txt1_l">套票活动</view><view class="sl_txt1_r"><view class="sl_txt1_r_a" style="color:#EB7471;">{{"-￥"+yh}}</view><view class="sl_txt1_r_b">></view></view></view></view></view></view><view class="bottom spaceBetween" style="padding:20rpx;"><view><view><text class="bottom_txt1">合计</text><text class="bottom_txt2" style="{{('color:'+primary_color)}}">{{money}}</text></view><view class="bottom_txt4">{{"已优惠"+yh}}</view></view><view data-event-opts="{{[['tap',[['submit',['$event']]]]]}}" class="bt" style="{{('background:'+primary_color)}}" bindtap="__e">立即支付</view></view><uni-popup class="vue-ref" vue-id="4509d206-1" id="dialogShowCategory" type="bottom" mask-click="{{true}}" data-ref="dialogShowCategory" bind:__l="__l" vue-slots="{{['default']}}"><view class="view-main"><view class="view-title">套票活动<image class="icon" src="{{pre_url+'/static/img/close.png'}}" data-event-opts="{{[['tap',[['hideDialog']]]]}}" bindtap="__e"></image></view><scroll-view style="height:600rpx;" scroll-y="{{true}}"><block wx:for="{{packs}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view><block wx:if="{{item.num>0}}"><view style="display:flex;justify-content:space-between;padding-top:10px;"><text>{{item.group_size+"人套餐 X "+item.num}}</text><text style="color:#EB7471;">{{"-￥"+item.total}}</text></view></block></view></block></scroll-view></view></uni-popup></view>