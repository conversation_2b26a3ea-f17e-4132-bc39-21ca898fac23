(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["zhaopin/partdetails"],{"057c":function(t,e,o){"use strict";(function(t,e){var n=o("47a9");o("06e9");n(o("3240"));var a=n(o("7bc5"));t.__webpack_require_UNI_MP_PLUGIN__=o,e(a.default)}).call(this,o("3223")["default"],o("df3c")["createPage"])},"216a7":function(t,e,o){"use strict";var n=o("63e6"),a=o.n(n);a.a},48078:function(t,e,o){"use strict";o.d(e,"b",(function(){return n})),o.d(e,"c",(function(){return a})),o.d(e,"a",(function(){}));var n=function(){var t=this,e=t.$createElement,o=(t._self._c,t.isload&&t.partJobVo.template&&2===t.partJobVo.template.templateId&&t.flag?t.t("color1"):null),n=!t.isload||t.partJobVo.template&&2===t.partJobVo.template.templateId||t.partJobVo.template&&3===t.partJobVo.template.templateId?null:t.moduleList&&t.moduleList.length>0,a=!t.isload||t.partJobVo.template&&2===t.partJobVo.template.templateId?null:t.t("color1");t.$mp.data=Object.assign({},{$root:{m0:o,g0:n,m1:a}})},a=[]},"63e6":function(t,e,o){},"767b":function(t,e,o){"use strict";o.r(e);var n=o("9e1c"),a=o.n(n);for(var i in n)["default"].indexOf(i)<0&&function(t){o.d(e,t,(function(){return n[t]}))}(i);e["default"]=a.a},"7bc5":function(t,e,o){"use strict";o.r(e);var n=o("48078"),a=o("767b");for(var i in a)["default"].indexOf(i)<0&&function(t){o.d(e,t,(function(){return a[t]}))}(i);o("216a7");var l=o("828b"),r=Object(l["a"])(a["default"],n["b"],n["c"],!1,null,"1a9ec30e",null,!1,n["a"],void 0);e["default"]=r.exports},"9e1c":function(t,e,o){"use strict";(function(t){var n=o("47a9");Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var a=n(o("34cf")),i=n(o("af34")),l=n(o("7ca3"));function r(t,e){var o=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),o.push.apply(o,n)}return o}function s(t){for(var e=1;e<arguments.length;e++){var o=null!=arguments[e]?arguments[e]:{};e%2?r(Object(o),!0).forEach((function(e){(0,l.default)(t,e,o[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(o)):r(Object(o)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(o,e))}))}return t}var c=getApp(),u={components:{jobList:function(){o.e("zhaopin/components/jobList/index").then(function(){return resolve(o("095d"))}.bind(null,o)).catch(o.oe)},applySureModal:function(){o.e("zhaopin/components/applySureModal/index").then(function(){return resolve(o("4dea"))}.bind(null,o)).catch(o.oe)},applyButton:function(){o.e("zhaopin/components/applyButton/applyButton").then(function(){return resolve(o("07fe"))}.bind(null,o)).catch(o.oe)},education:function(){o.e("zhaopin/templates/education/education").then(function(){return resolve(o("3d11"))}.bind(null,o)).catch(o.oe)},reportDialog:function(){o.e("zhaopin/components/reportDialog/index").then(function(){return resolve(o("0663"))}.bind(null,o)).catch(o.oe)},simpleModel:function(){o.e("zhaopin/components/simpleModel/simpleModel").then(function(){return resolve(o("d922"))}.bind(null,o)).catch(o.oe)},navigationBar:function(){o.e("zhaopin/components/navigationBar/index").then(function(){return resolve(o("4efb"))}.bind(null,o)).catch(o.oe)},regularItem:function(){o.e("zhaopin/components/regularItem/index").then(function(){return resolve(o("da08"))}.bind(null,o)).catch(o.oe)}},data:function(){var t;return t={opt:{},loading:!1,isload:!1,partJobId:"",visible:!1,moduleList:[],partJobVo:{id:0,aid:0,company_id:0,title:"",salary:"",province:"",city:"",district:"",address:"",education:"",experience:"",description:"",requirement:"",status:1,views:0,create_time:"",buttonStatus:6,hasApply:!1,company:{id:0,name:"",logo:"",introduction:"",scale:"",nature:"",industry:""},template:{templateId:1},labelList:{descLabels:[]}},hasToken:!1,authorizedKey:"",reportDialogText:"",shareUserId:"",diploma:{0:"学历不限",2:"限高中以上",3:"限大专以上",4:"限本科以上",6:"限硕士以上",7:"限博士以上"},flag:!0,showAll:!1,showRemind:!1,remainingApplyCount:"",userRemark:"",isSupportLifestyle:!1,positionId:"",source:"",famousList:[],recommendTips:"",isShowApplySure:!1,locationTownName:"",nowTownName:"",nowTownId:"",duration:"",routeType:"公交",routeMethods:[],current:0,navList:[{text:"详情",type:1}],navCurrent:0,navTop:148,navHeight:40,isFixed:!1,reportSwiper:[],computedFlag:!1,qtbPrdUrl:"",healthVisible:!1},(0,l.default)(t,"moduleList",[]),(0,l.default)(t,"recommendAllList",[]),(0,l.default)(t,"tuanContentPrompt",[]),(0,l.default)(t,"copyVisible",!1),(0,l.default)(t,"guideVisible",!1),(0,l.default)(t,"tabCurrent",0),(0,l.default)(t,"complaintPhotos",[]),(0,l.default)(t,"personalImageList",[]),(0,l.default)(t,"positionTags",[]),(0,l.default)(t,"hasLatitude",!1),(0,l.default)(t,"isInfoShowBtn",!0),(0,l.default)(t,"isComputedInfo",!1),(0,l.default)(t,"remindType",0),(0,l.default)(t,"hasEyeAuth",!1),(0,l.default)(t,"isShowAll",!1),(0,l.default)(t,"jobRequireList",[]),(0,l.default)(t,"bannerList",[]),(0,l.default)(t,"bannerCurrent",0),(0,l.default)(t,"fromRecommend",!1),(0,l.default)(t,"rate",0),(0,l.default)(t,"videoAutoplay",!1),(0,l.default)(t,"swiperAutoplay",!0),(0,l.default)(t,"gateWay",{}),(0,l.default)(t,"countdownDesc","00:00:00"),(0,l.default)(t,"payVisible",!1),(0,l.default)(t,"sendData",{}),(0,l.default)(t,"applyId",""),(0,l.default)(t,"successVisible",!1),(0,l.default)(t,"finishCount",0),(0,l.default)(t,"deg",0),(0,l.default)(t,"point",0),(0,l.default)(t,"redpacketVisible",!1),(0,l.default)(t,"guideReportVisible",!1),(0,l.default)(t,"scrollFlag",!1),(0,l.default)(t,"toastVisible",!1),(0,l.default)(t,"browserSeconds",10),(0,l.default)(t,"rewardMoney",0),(0,l.default)(t,"actId",""),(0,l.default)(t,"poinitTime",10),(0,l.default)(t,"coundownTimer",10),(0,l.default)(t,"guideReportDialogVisible",!1),(0,l.default)(t,"jobDetailJson",{baseInfo:{}}),(0,l.default)(t,"descMore",!1),(0,l.default)(t,"companyDescMore",!1),(0,l.default)(t,"classMore",!1),(0,l.default)(t,"busLine",""),(0,l.default)(t,"chosenList",[]),(0,l.default)(t,"isOnlineCourse",!1),(0,l.default)(t,"contactDialogShow",!1),(0,l.default)(t,"contactInfo",{}),(0,l.default)(t,"contactInfoTime",""),(0,l.default)(t,"onlineCourseIds","10465,10467,10468,10469,10470,10471,10472"),(0,l.default)(t,"agreementVo",{}),(0,l.default)(t,"agreementVisible",!1),(0,l.default)(t,"isDirect",!1),(0,l.default)(t,"collectImg","iconcollect"),t},onLoad:function(t){this.opt=c.getopts(t),this.getdata()},onPullDownRefresh:function(){this.getdata()},methods:{t:function(t){return getApp().globalData.initdata.textset[t]||t},getdata:function(){var e=this,o=e.opt.id;e.loading=!0,c.get("ApiZhaopin/getPositionDetail",{id:o},(function(o){if(e.loading=!1,1==o.status){var n=o.data;e.isload=!0,e.partJobId=String(n.id),e.getRecommendList(),e.collectImg=1===n.is_favorite?"iconcollect_fill":"iconcollect",console.log("初始化收藏状态:",e.collectImg),e.partJobVo=s(s({},n),{},{title:n.title,salary:n.salary,description:n.description,requirement:n.requirement,views:n.views,create_time:n.create_time,commission_detail:n.commission_detail||null,address:n.address,province:n.province,city:n.city,district:n.district,company:{id:n.company_id||0,name:n.company_name||"",logo:n.company_logo||"https://qiniu-image.qtshe.com/company_default_5.4.png",introduction:n.benefits||"",scale:n.scale||"",nature:n.nature?1===n.nature?"民营":2===n.nature?"国企":3===n.nature?"合资":4===n.nature?"外资":"其他":"",industry:""},template:n.template||{templateId:1},buttonStatus:n.buttonStatus||6,hasApply:n.hasApply||!1,jobFeeVO:n.jobFeeVO||{feeRushPrice:0,rushStatus:0},labelList:{descLabels:[{labelName:n.education},{labelName:n.experience},{labelName:n.scale||""},{labelName:n.nature?1===n.nature?"民营":2===n.nature?"国企":3===n.nature?"合资":4===n.nature?"外资":"其他":""}].concat((0,i.default)(n.formatted_options?Object.entries(n.formatted_options).map((function(t){var e=(0,a.default)(t,2),o=e[0],n=e[1];return n.map((function(t){return{labelName:t,labelType:o}}))})).flat():[])).filter((function(t){return t.labelName}))}}),e.jobDetailJson={baseInfo:n},e.famousList=n.famousList||[],t.setNavigationBarTitle({title:n.title||"职位详情"}),console.log("职位数据初始化完成:",e.partJobVo)}else c.alert(o.msg||"获取数据失败")}))},agreementCancelHandle:function(){},onLoginSuccess:function(){},confirmReport:function(){var e=this;console.log("点击报名按钮"),console.log("当前职位信息:",this.partJobVo),c.get("ApiZhaopin/getResumeDetail",{},(function(o){if(0===o.status&&o.need_resume)t.showModal({title:"提示",content:o.msg||"请先创建简历",confirmText:"去创建",cancelText:"取消",success:function(t){t.confirm&&c.goto(o.url||"/zhaopin/resume")}});else if(1==o.status)try{if(e.partJobId=String(e.partJobVo.id),console.log("设置职位ID:",e.partJobId),e.partJobVo.jobTime="".concat(e.partJobVo.work_time_start||""," - ").concat(e.partJobVo.work_time_end||"").trim(),e.partJobVo.jobDateDesc=e.partJobVo.work_time_type||"",e.partJobVo.addressDetail=e.partJobVo.work_address||e.partJobVo.address||"",e.partJobVo.requirement){var n=e.partJobVo.requirement||"";n=n.replace(/<\/?[^>]+(>|$)/g,""),n=n.replace(/\s+/g," ").trim(),n&&(e.partJobVo.requireList=e.partJobVo.requireList||[],e.partJobVo.requireList.includes(n)||e.partJobVo.requireList.push(n))}e.visible=!0,console.log("弹窗显示状态:",e.visible)}catch(a){console.error("处理报名信息时出错:",a),c.error("处理报名信息时出错，请稍后重试")}else c.error(o.msg||"获取简历信息失败")}))},closeReportDialog:function(){this.visible=!1,this.getdata()},onPayCancel:function(){},onPaySuccess:function(){},showPayDialog:function(){},onCancelBtn:function(){this.isShowApplySure=!1},onSureBtn:function(){},anchorTabHandle:function(t){if(console.log("切换tab:",t),t&&t.detail){var e=t.detail.target.dataset.i;this.tabCurrent=parseInt(e),this.getRecommendList()}},getRecommendList:function(e){var o=this;t.getLocation({type:"gcj02",success:function(t){c.get("ApiZhaopin/getRecommendList",{position_id:o.partJobId||"",latitude:t.latitude,longitude:t.longitude,limit:10},(function(t){1==t.status?(console.log("获取推荐列表成功:",t.data),o.moduleList=t.data.recommendList||[]):(console.log("获取推荐列表失败:",t.msg),o.moduleList=[])}))},fail:function(){c.get("ApiZhaopin/getRecommendList",{position_id:o.partJobId||"",limit:10},(function(t){1==t.status?(console.log("获取推荐列表成功:",t.data),o.moduleList=t.data.recommendList||[]):(console.log("获取推荐列表失败:",t.msg),o.moduleList=[])}))}})},handleCollect:function(){var e=this;console.log("handleCollect被调用"),console.log("当前职位ID:",this.partJobId),console.log("当前收藏状态:",this.collectImg);this.collectImg;c.post("apiZhaopin/favoritePosition",{position_id:this.partJobId},(function(o){console.log("收藏接口返回:",o),1===o.status?(e.collectImg=1===o.data.is_favorite?"iconcollect_fill":"iconcollect",t.showToast({title:o.msg,icon:"none"})):t.showToast({title:o.msg||"操作失败",icon:"none"})}))},goToCompany:function(){if(console.log("点击跳转公司",this.partJobVo.company),console.log("hasEyeAuth:",this.hasEyeAuth),this.partJobVo.company&&this.partJobVo.company.id){var e="/zhaopin/company?id="+this.partJobVo.company.id;console.log("跳转URL:",e),t.navigateTo({url:e,fail:function(o){console.error("跳转失败:",o),t.redirectTo({url:e})}})}else console.log("公司信息不完整，无法跳转")}}};e.default=u}).call(this,o("df3c")["default"])}},[["057c","common/runtime","common/vendor"]]]);