<view class="container"><block wx:if="{{!pageSwitch}}"><block><view class="search-container-search" style="{{(history_show?'height:100%;':'')}}"><view class="topsearch flex-y-center"><view class="f1 flex-y-center"><image class="img" src="{{pre_url+'/static/img/search_ico.png'}}"></image><input placeholder="搜索感兴趣的商品" placeholder-style="font-size:24rpx;color:#C2C2C2" data-event-opts="{{[['confirm',[['searchConfirm',['$event']]]],['input',[['searchChange',['$event']]]]]}}" value="{{keyword}}" bindconfirm="__e" bindinput="__e"/></view><view data-event-opts="{{[['tap',[['searchbtn',['$event']]]]]}}" class="search-btn" bindtap="__e"><text>搜索</text></view></view><view hidden="{{!(history_show)}}" class="search-history"><view><text class="search-history-title">最近搜索</text><view data-event-opts="{{[['tap',[['deleteSearchHistory',['$event']]]]]}}" class="delete-search-history" bindtap="__e"><image style="width:36rpx;height:36rpx;" src="{{pre_url+'/static/img/del.png'}}"></image></view></view><view class="search-history-list"><block wx:for="{{history_list}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="search-history-item" data-value="{{item}}" data-event-opts="{{[['tap',[['historyClick',['$event']]]]]}}" bindtap="__e">{{item+''}}</view></block><block wx:if="{{$root.g0}}"><view class="flex-y-center"><image style="width:36rpx;height:36rpx;margin-right:10rpx;" src="{{pre_url+'/static/img/tanhao.png'}}"></image>暂无记录</view></block></view></view></view><view class="product-container"><block wx:if="{{$root.g1}}"><block><view class="product-itemlist"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="id"><view class="item" data-type="{{item.$orig.type?item.$orig.type:0}}" data-id="{{item.$orig.id}}" data-event-opts="{{[['tap',[['toDetail',['$event']]]]]}}" bindtap="__e"><view class="product-pic"><image class="image" src="{{item.$orig.pic}}" mode="widthFix"></image></view><view class="product-info"><view class="p1"><text>{{item.$orig.name}}</text></view><view class="p2"><view class="t1" style="{{'color:'+(item.m0)+';'}}">{{item.$orig.sell_price}}<text style="font-size:24rpx;padding-left:2px;">{{"元/"+item.$orig.danwei}}</text></view><block wx:if="{{item.$orig.market_price*1>item.$orig.sell_price*1}}"><text class="t2">{{"￥"+item.$orig.market_price}}</text></block></view><view class="p3"><block wx:if="{{item.$orig.sales>0}}"><view class="p3-1"><text style="overflow:hidden;">{{"已售"+item.$orig.sales+"件"}}</text></view></block></view><view data-event-opts="{{[['tap',[['couponAddChange',['$0'],[[['datalist','id',item.$orig.id]]]]]]]}}" class="addbut" style="{{'background:'+('linear-gradient(270deg,'+item.m1+' 0%,rgba('+item.m2+',0.8) 100%)')+';'}}" bindtap="__e">添加</view></view></view></block></view></block></block><block wx:if="{{nomore}}"><nomore vue-id="1c4a2085-1" text="没有更多商品了" bind:__l="__l"></nomore></block><block wx:if="{{nodata}}"><nodata vue-id="1c4a2085-2" text="没有查找到相关商品" bind:__l="__l"></nodata></block></view></block></block><block wx:if="{{pageSwitch}}"><block><view data-event-opts="{{[['tap',[['goSearch',['$event']]]]]}}" class="search-container" catchtap="__e"><view class="search-box"><image class="img" src="{{pre_url+'/static/img/search_ico.png'}}"></image><view class="search-text">搜索感兴趣的商品</view></view></view><view class="content-container"><view class="nav_left"><view class="{{['nav_left_items '+(curIndex==-1?'active':'')]}}" data-index="-1" data-id="0" data-event-opts="{{[['tap',[['switchRightTab',['$event']]]]]}}" bindtap="__e"><view class="before" style="{{'background:'+($root.m3)+';'}}"></view>全部</view><block wx:for="{{$root.l1}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><view class="{{['nav_left_items '+(curIndex==index?'active':'')]}}" data-index="{{index}}" data-id="{{item.$orig.id}}" data-event-opts="{{[['tap',[['switchRightTab',['$event']]]]]}}" bindtap="__e"><view class="before" style="{{'background:'+(item.m4)+';'}}"></view>{{item.$orig.name}}</view></block></block></view><view class="nav_right"><view class="nav_right-content"><block wx:if="{{$root.g2}}"><view class="classify-ul"><view class="flex" style="width:100%;overflow-y:hidden;overflow-x:scroll;"><view class="classify-li" style="{{(curIndex2==-1?'color:'+$root.m5+';background:rgba('+$root.m6+',0.2)':'')}}" data-id="{{clist[curIndex].id}}" data-index="-1" data-event-opts="{{[['tap',[['changeCTab',['$event']]]]]}}" bindtap="__e">全部</view><block wx:for="{{$root.l2}}" wx:for-item="item" wx:for-index="idx2" wx:key="idx2"><block><view class="classify-li" style="{{(curIndex2==idx2?'color:'+item.m7+';background:rgba('+item.m8+',0.2)':'')}}" data-id="{{item.$orig.id}}" data-index="{{idx2}}" data-event-opts="{{[['tap',[['changeCTab',['$event']]]]]}}" bindtap="__e">{{item.$orig.name}}</view></block></block></view></view></block><scroll-view class="classify-box" scroll-y="true" data-event-opts="{{[['scrolltolower',[['scrolltolower',['$event']]]]]}}" bindscrolltolower="__e"><view class="product-itemlist"><block wx:for="{{$root.l3}}" wx:for-item="item" wx:for-index="index" wx:key="id"><view class="item" data-type="{{item.$orig.type?item.$orig.type:0}}" data-id="{{item.$orig.id}}" data-event-opts="{{[['tap',[['toDetail',['$event']]]]]}}" bindtap="__e"><view class="product-pic"><image class="image" src="{{item.$orig.pic}}" mode="widthFix"></image></view><view class="product-info"><view class="p1"><text>{{item.$orig.name}}</text></view><view class="p2"><view class="t1" style="{{'color:'+(item.m9)+';'}}">{{item.$orig.sell_price}}<text style="font-size:24rpx;padding-left:2px;">{{"元/"+item.$orig.danwei}}</text></view><block wx:if="{{item.$orig.market_price*1>item.$orig.sell_price*1}}"><text class="t2">{{"￥"+item.$orig.market_price}}</text></block></view><view class="p3"><block wx:if="{{item.$orig.sales>0}}"><view class="p3-1"><text style="overflow:hidden;">{{"已售"+item.$orig.sales+"件"}}</text></view></block></view><view data-event-opts="{{[['tap',[['couponAddChange',['$0'],[[['datalist','id',item.$orig.id]]]]]]]}}" class="addbut" style="{{'background:'+('linear-gradient(270deg,'+item.m10+' 0%,rgba('+item.m11+',0.8) 100%)')+';'}}" bindtap="__e">添加</view></view></view></block></view><block wx:if="{{nomore}}"><nomore vue-id="1c4a2085-3" text="没有更多商品了" bind:__l="__l"></nomore></block><block wx:if="{{nodata}}"><nodata vue-id="1c4a2085-4" text="暂无相关商品" bind:__l="__l"></nodata></block><view style="width:100%;height:100rpx;"></view></scroll-view></view></view></view><block wx:if="{{buydialogShow}}"><buydialog vue-id="1c4a2085-5" proid="{{proid}}" menuindex="{{menuindex}}" data-event-opts="{{[['^buydialogChange',[['buydialogChange']]]]}}" bind:buydialogChange="__e" bind:__l="__l"></buydialog></block></block></block><block wx:if="{{loading}}"><loading vue-id="1c4a2085-6" loadstyle="left:62.5%" bind:__l="__l"></loading></block><dp-tabbar vue-id="1c4a2085-7" opt="{{opt}}" data-event-opts="{{[['^getmenuindex',[['getmenuindex']]]]}}" bind:getmenuindex="__e" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="1c4a2085-8" data-ref="popmsg" bind:__l="__l"></popmsg></view>