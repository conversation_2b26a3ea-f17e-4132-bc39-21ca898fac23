<view class="container"><block wx:if="{{isload}}"><block><view class="ordertop" style="{{('background:url('+pre_url+'/static/img/ordertop.png);background-size:100%')}}"><block wx:if="{{detail.status==0}}"><view class="f1"><view class="t1">等待买家付款</view><block wx:if="{{djs}}"><view class="t2">{{"剩余时间："+djs}}</view></block></view></block><block wx:if="{{detail.status==1}}"><view class="f1"><view class="t1">{{detail.paytypeid==4?'已选择'+detail.paytype:'已成功付款'}}</view><block wx:if="{{detail.freight_type!=1}}"><view class="t2">我们会尽快为您发货</view></block><block wx:if="{{detail.freight_type==1}}"><view class="t2">请尽快前往自提地点取货</view></block></view></block><block wx:if="{{detail.status==2}}"><view class="f1"><view class="t1">订单已发货</view><block wx:if="{{detail.freight_type!=3}}"><text class="t2" user-select="true" selectable="true">{{"发货信息："+detail.express_com+" "+detail.express_no}}</text></block></view></block><block wx:if="{{detail.status==3}}"><view class="f1"><view class="t1">订单已完成</view></view></block><block wx:if="{{detail.status==4}}"><view class="f1"><view class="t1">订单已取消</view></view></block></view><view class="address"><view class="img"><image src="/static/img/address3.png"></image></view><view class="info"><text class="t1" user-select="true" selectable="true">{{detail.linkman+" "+detail.tel}}</text><block wx:if="{{detail.freight_type!=1&&detail.freight_type!=3}}"><text class="t2" user-select="true" selectable="true">{{"地址："+detail.area+detail.address}}</text></block><block wx:if="{{detail.freight_type==1}}"><text class="t2" data-storeinfo="{{storeinfo}}" data-latitude="{{storeinfo.latitude}}" data-longitude="{{storeinfo.longitude}}" user-select="true" selectable="true" data-event-opts="{{[['tap',[['openMendian',['$event']]]]]}}" bindtap="__e">{{"取货地点："+storeinfo.name+" - "+storeinfo.address}}</text></block></view></view><block wx:if="{{detail.bid>0}}"><view class="btitle flex-y-center"><image style="width:36rpx;height:36rpx;" src="{{detail.binfo.logo}}" data-url="{{'/pagesExt/business/index?id='+detail.bid}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"></image><text class="flex1" style="padding-left:16rpx;" decode="true" space="true" data-url="{{'/pagesExt/business/index?id='+detail.bid}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">{{detail.binfo.name}}</text></view></block><view class="product"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="idx" wx:key="idx"><view class="content"><view data-url="{{'product?id='+item.$orig.proid}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image src="{{item.$orig.pic}}"></image></view><view class="detail"><text class="t1">{{item.$orig.name}}</text><text class="t2">{{"市场价￥"+item.$orig.sell_price}}</text><view class="t3"><text class="x1 flex1"><block wx:if="{{item.$orig.money_price>0}}"><text>{{"￥"+item.$orig.money_price+"+"}}</text></block>{{item.$orig.score_price+item.m0}}</text><text class="x2">{{"×"+item.$orig.num}}</text></view></view></view></block></view><block wx:if="{{(detail.status==3||detail.status==2)&&(detail.freight_type==3||detail.freight_type==4)}}"><view class="orderinfo"><view class="item flex-col"><text class="t1" style="color:#111;">发货信息</text><text class="t2" style="text-align:left;margin-top:10rpx;padding:0 10rpx;" user-select="true" selectable="true">{{detail.freight_content}}</text></view></view></block><view class="orderinfo"><view class="item"><text class="t1">订单编号</text><text class="t2" user-select="true" selectable="true">{{detail.ordernum}}</text></view><view class="item"><text class="t1">下单时间</text><text class="t2">{{detail.createtime}}</text></view><block wx:if="{{detail.status>0&&detail.paytypeid!='4'&&detail.paytime}}"><view class="item"><text class="t1">支付时间</text><text class="t2">{{detail.paytime}}</text></view></block><block wx:if="{{detail.status>0&&detail.paytime}}"><view class="item"><text class="t1">支付方式</text><text class="t2">{{detail.paytype}}</text></view></block><block wx:if="{{detail.status>1&&detail.send_time}}"><view class="item"><text class="t1">发货时间</text><text class="t2">{{detail.send_time}}</text></view></block><block wx:if="{{detail.status==3&&detail.collect_time}}"><view class="item"><text class="t1">收货时间</text><text class="t2">{{detail.collect_time}}</text></view></block></view><view class="orderinfo"><view class="item"><text class="t1">商品金额</text><block wx:if="{{detail.totalmoney}}"><text class="t2 red">{{"¥"+detail.totalmoney+" + "+detail.totalscore+$root.m1}}</text></block><block wx:else><text class="t2 red">{{detail.totalscore+$root.m2}}</text></block></view><view class="item"><text class="t1">配送方式</text><text class="t2">{{detail.freight_text}}</text></view><block wx:if="{{detail.freight_type==1&&detail.freightprice>0}}"><view class="item"><text class="t1">服务费</text><text class="t2 red">{{"+¥"+detail.freight_price}}</text></view></block><block wx:if="{{detail.freight_time}}"><view class="item"><text class="t1">{{(detail.freight_type!=1?'配送':'提货')+"时间"}}</text><text class="t2">{{detail.freight_time}}</text></view></block><view class="item"><text class="t1">实付款</text><text class="t2 red">{{"¥"+detail.totalprice+" + "+detail.totalscore+$root.m3}}</text></view><view class="item"><text class="t1">订单状态</text><block wx:if="{{detail.status==0}}"><text class="t2">未付款</text></block><block wx:if="{{detail.status==1}}"><text class="t2">待发货</text></block><block wx:if="{{detail.status==2}}"><text class="t2">已发货</text></block><block wx:if="{{detail.status==3}}"><text class="t2">已收货</text></block><block wx:if="{{detail.status==4}}"><text class="t2">已关闭</text></block></view><block wx:if="{{detail.refund_status>0}}"><view class="item"><text class="t1">退款状态</text><block wx:if="{{detail.refund_status==1}}"><text class="t2 red">{{"审核中,¥"+detail.refund_money}}</text></block><block wx:if="{{detail.refund_status==2}}"><text class="t2 red">{{"已退款,¥"+detail.refund_money}}</text></block><block wx:if="{{detail.refund_status==3}}"><text class="t2 red">{{"已驳回,¥"+detail.refund_money}}</text></block></view></block><block wx:if="{{detail.refund_status>0}}"><view class="item"><text class="t1">退款原因</text><text class="t2 red">{{detail.refund_reason}}</text></view></block><block wx:if="{{detail.refund_checkremark}}"><view class="item"><text class="t1">审核备注</text><text class="t2 red">{{detail.refund_checkremark}}</text></view></block><block wx:if="{{detail.isfuwu&&detail.fuwuendtime>0}}"><view class="item"><text class="t1">到期时间</text><text class="t2 red">{{$root.g0}}</text></view></block></view><block wx:if="{{$root.g1>0}}"><view class="orderinfo"><block wx:for="{{detail.formdata}}" wx:for-item="item" wx:for-index="__i0__" wx:key="*this"><view class="item"><text class="t1">{{item[0]}}</text><block wx:if="{{item[2]=='upload'}}"><view class="t2"><image style="width:400rpx;height:auto;" src="{{item[1]}}" mode="widthFix" data-url="{{item[1]}}" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image></view></block><block wx:else><text class="t2" user-select="true" selectable="true">{{item[1]}}</text></block></view></block></view></block><view style="width:100%;height:160rpx;"></view><view class="bottom notabbarbot"><block wx:if="{{detail.status==0}}"><block><view class="btn2" data-id="{{detail.id}}" data-event-opts="{{[['tap',[['toclose',['$event']]]]]}}" bindtap="__e">关闭订单</view><view class="btn1" style="{{'background:'+($root.m4)+';'}}" data-url="{{'/pages/pay/pay?id='+detail.payorderid}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">去付款</view></block></block><block wx:if="{{detail.status==1}}"><block><block wx:if="{{detail.paytypeid!='4'}}"><block><block wx:if="{{detail.refund_status==0||detail.refund_status==3}}"><view class="btn2" data-url="{{'refund?orderid='+detail.id+'&price='+detail.totalprice+'&score='+detail.totalscore}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">申请退款</view></block></block></block><block wx:else><block></block></block></block></block><block wx:if="{{detail.status==2||detail.status==3}}"><block><block wx:if="{{detail.freight_type!=3&&detail.freight_type!=4}}"><view class="btn2" data-url="{{'/pagesExt/order/logistics?express_com='+detail.express_com+'&express_no='+detail.express_no}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">查看物流</view></block></block></block><block wx:if="{{detail.status==2}}"><block><block wx:if="{{detail.paytypeid!='4'}}"><block><block wx:if="{{detail.refund_status==0||detail.refund_status==3}}"><view class="btn2" data-url="{{'refund?orderid='+detail.id+'&price='+detail.totalprice+'&score='+detail.totalscore}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">申请退款</view></block></block></block><block wx:if="{{detail.paytypeid!='4'}}"><view class="btn1" style="{{'background:'+($root.m5)+';'}}" data-id="{{detail.id}}" data-event-opts="{{[['tap',[['orderCollect',['$event']]]]]}}" bindtap="__e">确认收货</view></block><block wx:else><block></block></block></block></block><block wx:if="{{(detail.status==1||detail.status==2)&&detail.freight_type==1}}"><block><view data-event-opts="{{[['tap',[['showhxqr',['$event']]]]]}}" class="btn2" bindtap="__e">核销码</view></block></block><block wx:if="{{detail.status==3||detail.status==4}}"><block><view class="btn2" data-id="{{detail.id}}" data-event-opts="{{[['tap',[['todel',['$event']]]]]}}" bindtap="__e">删除订单</view></block></block></view><uni-popup class="vue-ref" vue-id="c6cc3cc0-1" id="dialogHxqr" type="dialog" data-ref="dialogHxqr" bind:__l="__l" vue-slots="{{['default']}}"><view class="hxqrbox"><image class="img" src="{{detail.hexiao_qr}}" data-url="{{detail.hexiao_qr}}" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image><view class="txt">请出示核销码给核销员进行核销</view><view data-event-opts="{{[['tap',[['closeHxqr',['$event']]]]]}}" class="close" bindtap="__e"><image style="width:100%;height:100%;" src="/static/img/close2.png"></image></view></view></uni-popup></block></block><block wx:if="{{loading}}"><loading vue-id="c6cc3cc0-2" bind:__l="__l"></loading></block><dp-tabbar vue-id="c6cc3cc0-3" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="c6cc3cc0-4" data-ref="popmsg" bind:__l="__l"></popmsg></view>