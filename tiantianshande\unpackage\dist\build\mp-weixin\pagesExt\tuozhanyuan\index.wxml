<view><block wx:if="{{isload}}"><block><view class="banner" style="{{'background:'+('linear-gradient(180deg,'+$root.m0+' 0%,rgba('+$root.m1+',0) 100%)')+';'}}"><image src="{{userinfo.headimg}}" background-size="cover"></image><block wx:if="{{set&&set.parent_show==1}}"><view class="info"><text class="nickname">{{userinfo.nickname+"（拓展ID："+userinfo.id+")"}}</text><text>{{$root.m2+"："+(userinfo.pid>0?userinfo.pnickname:'无')}}</text><text>{{$root.m3+"："+userinfo.shenfen}}</text></view></block><block wx:else><view class="info" style="line-height:120rpx;padding-top:0;"><text class="nickname">{{userinfo.nickname}}</text></view></block></view><view class="contentdata"><view class="order"><view class="head"><text class="f1">{{"我的"+$root.m4}}</text><view data-event-opts="{{[['tap',[['tomoney',['$event']]]]]}}" class="f2" bindtap="__e"><text>{{"转到"+$root.m5+"账户"}}</text><image src="/static/img/arrowright.png"></image></view></view><view class="content"><view class="item" data-url="../order/shoporder?st=0" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><text class="t1">{{"￥"+userinfo.tuozhanfei}}</text><text class="t3">{{(comwithdraw==1?'可提现':'剩余')+$root.m6}}</text></view><view class="item"><text class="t1">{{"￥"+yihuo.tixiantuozhan}}</text><text class="t3">已提现拓展费</text></view></view></view><block wx:if="{{businessReward.show_reward}}"><view class="order"><view class="head"><text class="f1">商家入驻奖励</text><view class="f2" data-url="business_reward_detail" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><text>查看详情</text><image src="/static/img/arrowright.png"></image></view></view><view class="content"><view class="item"><text class="t1">{{"￥"+(businessReward.total_wait_reward||'0.00')}}</text><text class="t3">待发放奖励</text></view><view class="item"><text class="t1">{{"￥"+(businessReward.total_reward||'0.00')}}</text><text class="t3">累计已获奖励</text></view><view class="item"><text class="t1">{{businessReward.invite_count||0}}</text><text class="t3">成功邀请商家</text></view></view><block wx:if="{{businessReward.current_progress}}"><view class="content"><view class="item"><text class="t1">{{businessReward.current_progress.days_left||0}}</text><text class="t3">距奖励还需天数</text></view><view class="item"><text class="t1">{{"￥"+(businessReward.current_progress.reward_amount||'0.00')}}</text><text class="t3">即将获得奖励</text></view><view class="item"><text class="t1">{{businessReward.current_progress.progress_businesses||0}}</text><text class="t3">进行中商家数</text></view></view></block></view></block><block wx:if="{{hasteamfenhong&&(teamnum_show==1||teamyeji_show==1)}}"><view class="order"><view class="head"><text class="f1">{{$root.m7}}</text><view class="f2" data-url="tuozhanteam" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><text>查看详情</text><image src="/static/img/arrowright.png"></image></view></view><view class="content"><block wx:if="{{teamnum_show==1}}"><view class="item"><text class="t1">{{userinfo.teamnum}}</text><text class="t3">市场总用户数</text></view></block><view class="item"><block wx:if="{{teamyeji_show==1}}"><block><text class="t1">{{"￥"+userinfo.teamyeji}}</text><text class="t3">市场总业绩</text></block></block></view><view class="item"><block wx:if="{{gongxianfenhong_show==1}}"><block><text class="t1">{{"￥"+userinfo.gongxianfenhong}}</text><text class="t3">{{"预计"+(userinfo.gongxianfenhong_txt||'股东贡献量分红')}}</text></block></block></view></view></view></block><block wx:if="{{yihuo.yihuo_status==1}}"><view class="order"><view class="head"><text class="f1">{{$root.m8}}</text></view><view class="content"><view class="item"><text class="t1">{{yihuo.businesscount}}</text><text class="t3">已绑定商家数量</text></view><view class="item"><text class="t1">{{"￥"+yihuo.zongliushui}}</text><text class="t3">商家总流水</text></view><view class="item"><text class="t1">{{"￥"+yihuo.jinday}}</text><text class="t3">今日流水</text></view></view><view class="content"><view class="item"><text class="t1">{{"￥"+yihuo.zuoday}}</text><text class="t3">昨日流水</text></view><view class="item"><text class="t1">{{"￥"+yihuo.jinmonth}}</text><text class="t3">本月流水</text></view><view class="item"><text class="t1">{{"￥"+yihuo.chouyong}}</text><text class="t3">抽佣总和</text></view></view></view></block><block wx:if="{{userinfo.cityname!=0}}"><view class="order"><view class="head"><text class="f1">{{$root.m9}}</text></view><view class="content"><view class="item"><text class="t1">{{userinfo.cityname}}</text><text class="t3">代理城市</text></view></view><view class="content"><view class="item"><text class="t1">{{userinfo.businesscount}}</text><text class="t3">代理城市商户数量</text></view><view class="item"><text class="t1">{{"￥"+userinfo.cityliushui}}</text><text class="t3">代理城市流水</text></view></view></view></block><view class="list"><view class="item" data-url="mylist" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="f2">商家列表</view><text class="f3"></text><image class="f4" src="/static/img/arrowright.png"></image></view><block wx:if="{{businessReward.show_reward}}"><view class="item" data-url="business_reward_detail" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="f2">商家入驻奖励</view><text class="f3">{{"￥"+(businessReward.total_wait_reward||'0.00')}}</text><image class="f4" src="/static/img/arrowright.png"></image></view></block><block wx:if="{{set.commissionlog_show}}"><view class="item" data-url="tuozhanfeimingxi" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="f2">{{$root.m10+"明细"}}</view><text class="f3"></text><image class="f4" src="/static/img/arrowright.png"></image></view></block><block wx:if="{{set&&set.agent_card==1}}"><view class="item" data-url="/pagesExt/agent/cardEdit" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="f2">代理卡片</view><text class="f3"></text><image class="f4" src="/static/img/arrowright.png"></image></view></block></view></view><view style="width:100%;height:20rpx;"></view><uni-popup class="vue-ref" vue-id="5df05196-1" id="dialogInput" type="dialog" data-ref="dialogInput" bind:__l="__l" vue-slots="{{['default']}}"><uni-popup-dialog vue-id="{{('5df05196-2')+','+('5df05196-1')}}" mode="input" title="{{$root.m11+'转'+$root.m12}}" value="" placeholder="请输入转入金额" data-event-opts="{{[['^confirm',[['tomonenyconfirm']]]]}}" bind:confirm="__e" bind:__l="__l"></uni-popup-dialog></uni-popup></block></block><block wx:if="{{loading}}"><loading vue-id="5df05196-3" bind:__l="__l"></loading></block><dp-tabbar vue-id="5df05196-4" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="5df05196-5" data-ref="popmsg" bind:__l="__l"></popmsg></view>