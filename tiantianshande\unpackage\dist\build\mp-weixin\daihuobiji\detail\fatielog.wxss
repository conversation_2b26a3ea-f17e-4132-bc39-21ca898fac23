.page{background-color:#f0f0f0;width:750rpx;min-height:100vh}.text-wrapper_5{display:flex;justify-content:center;padding:15rpx 0;background-color:#fff;border-bottom:1rpx solid #999}.text-wrapper_5 text{font-size:30rpx;color:#999;cursor:pointer;padding:10rpx 20rpx;margin:0 40rpx;text-align:center;transition:color .3s,border-bottom .3s}.text-wrapper_5 text.active{color:#e3582f;font-weight:700;border-bottom:4rpx solid #e3582f}.text-wrapper_5 text:hover{color:#e3582f}.list{-webkit-column-count:2;column-count:2;box-sizing:initial;margin-top:10px}.pbl{width:100%;-webkit-column-break-inside:avoid;break-inside:avoid;overflow:hidden;border-radius:5px;margin-bottom:20rpx;background-color:#fff;box-sizing:border-box}.pbl:last-child{margin-bottom:10rpx}.pbl .image{width:100%;border-radius:5px;overflow:hidden}.pbl .image > image{width:100%;height:100%}.pbl .title{font-size:32rpx;margin-bottom:6rpx;display:-webkit-box;text-overflow:ellipsis;overflow:hidden;-webkit-box-orient:vertical;-webkit-line-clamp:2;padding:5px 10px;font-weight:700;word-break:break-word}.pbl .more{display:flex;justify-content:space-between;color:#9499aa;margin-bottom:6rpx;font-size:26rpx}.pbl .action-buttons{display:flex;justify-content:space-between;padding:5rpx 16rpx 16rpx;background-color:#fff}.pbl .action-buttons button{flex:1;margin:0 8rpx;padding:10rpx 0;font-size:22rpx;border:none;border-radius:8rpx;text-align:center;cursor:pointer;color:#fff;transition:opacity .2s}.pbl .action-buttons .edit-button{background-color:#ffab00}.pbl .action-buttons .delete-button{background-color:#ff4d4f}.pbl .action-buttons button:active{opacity:.8}.multi-line-omit{word-break:break-all;text-overflow:ellipsis;overflow:hidden;display:-webkit-box;-webkit-line-clamp:2;-webkit-box-orient:vertical}.one-line-omit{width:100%;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}