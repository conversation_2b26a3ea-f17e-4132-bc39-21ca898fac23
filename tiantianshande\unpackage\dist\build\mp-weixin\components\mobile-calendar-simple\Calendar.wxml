<transition vue-id="281c01cc-1" name="{{transition}}" class="data-v-6427de9a" bind:__l="__l" vue-slots="{{['default']}}"><block wx:if="{{isShow}}"><view class="{{['calendar-tz','_div','data-v-6427de9a',isFixed&&'fixed']}}"><slot name="header"></slot><view class="week-number _div data-v-6427de9a"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><label style="{{'color:'+(item.g0)+';'}}" class="_span data-v-6427de9a">{{item.$orig}}</label></block></view><block wx:if="{{title}}"><view class="tips _p data-v-6427de9a">{{title}}</view></block><view class="content _div data-v-6427de9a" id="scrollWrap"><block wx:for="{{$root.l2}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="con _div data-v-6427de9a" id="{{item.$orig.year+''+item.$orig.month}}"><view class="_h3 data-v-6427de9a">{{item.$orig.year+'年'+item.$orig.month+'月'}}</view><label class="month-bg _span data-v-6427de9a" style="{{'color:'+(getBetweenColor)+';'}}">{{item.$orig.month}}</label><view class="each-month _ul data-v-6427de9a"><block wx:for="{{item.l1}}" wx:for-item="day" wx:for-index="idx" wx:key="idx"><view data-event-opts="{{[['tap',[['chooseDate',['$0','$1','$2'],[[['calendar','',index],['dayList','',idx]],[['calendar','',index,'month']],[['calendar','',index,'year']]]]]]]}}" class="{{['each-day','_li','data-v-6427de9a',day.m0]}}" style="{{'background:'+(day.m1)+';'}}" bindtap="__e"><block wx:if="{{mode!=4}}"><view class="{{['_div','data-v-6427de9a',day.m2]}}" style="{{'background:'+(day.m3)+';'}}"><view class="day _p data-v-6427de9a">{{day.$orig?day.$orig:''}}</view><view class="recent _p data-v-6427de9a"><view class="_i data-v-6427de9a">{{day.m4}}</view></view></view></block><block wx:else><view class="{{['_div','data-v-6427de9a',day.m5]}}"><view class="day _p data-v-6427de9a">{{day.$orig?day.$orig:''}}</view><view class="recent _p data-v-6427de9a"><view class="_i data-v-6427de9a">{{day.m6}}</view></view></view></block></view></block></view></view></block></view><slot name="footer"></slot></view></block></transition>