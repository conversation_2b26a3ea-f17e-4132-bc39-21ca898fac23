<view class="dp-form" style="{{'color:'+(params.color)+';'+('background-color:'+(params.bgcolor)+';')+('margin:'+(params.margin_y*2.2+'rpx '+params.margin_x*2.2+'rpx')+';')+('padding:'+(params.padding_y*2.2+'rpx '+params.padding_x*2.2+'rpx')+';')+('font-size:'+(params.fontsize*2+'rpx')+';')}}"><form data-formcontent="{{data.content}}" data-tourl="{{params.hrefurl}}" data-formid="{{data.id}}" data-event-opts="{{[['submit',[['editorFormSubmit',['$event']]]]]}}" bindsubmit="__e"><view style="display:none;">{{test}}</view><block wx:if="{{xystatus==1&&xytitlePos=='top'}}"><block><view class="xycss1"><checkbox-group data-event-opts="{{[['change',[['isagreeChange',['$event']]]]]}}" style="display:inline-block;" bindchange="__e"><checkbox style="transform:scale(0.6);" value="1" checked="{{isagree}}" color="{{$root.m0}}"></checkbox></checkbox-group><text>我已阅读并同意</text><text data-event-opts="{{[['tap',[['showxieyiFun',['$event']]]]]}}" style="{{'color:'+($root.m1)+';'}}" bindtap="__e">{{xytitle}}</text></view></block></block><block wx:for="{{$root.l1}}" wx:for-item="item" wx:for-index="idx" wx:key="id"><block wx:if="{{!item.$orig.linkitem||item.$orig.linkshow}}"><view class="{{[params.style==1?'dp-form-item':'dp-form-item2']}}" style="{{'border-color:'+(params.linecolor)+';'+('background:'+(item.$orig.bgcolor?item.$orig.bgcolor:'transparent')+';')}}"><block wx:if="{{item.$orig.key=='separate'}}"><block><view class="{{['dp-form-separate',item.$orig.val8=='1'?'dp-form-blod':'']}}">{{item.$orig.val1}}</view></block></block><block wx:if="{{item.$orig.key!='separate'}}"><view class="{{['label',item.$orig.val8=='1'?'dp-form-blod':'']}}">{{item.$orig.val1}}<block wx:if="{{item.$orig.val3==1&&params.showmuststar}}"><text style="color:red;">*</text></block></view></block><block wx:if="{{item.$orig.key=='input'}}"><block><block wx:if="{{params.style==1}}"><block><block wx:if="{{item.$orig.val5}}"><text style="margin-right:10rpx;">{{item.$orig.val5}}</text></block><block wx:if="{{item.$orig.val4==2&&item.$orig.val6==1}}"><block><input class="input disabled" style="{{'border-color:'+(params.inputbordercolor)+';'+('background-color:'+('#efefef')+';')}}" type="{{item.$orig.val4==1||item.$orig.val4==2?'digit':'text'}}" disabled="true" name="{{'form'+idx}}" placeholder="{{item.$orig.val2}}" placeholder-style="font-size:28rpx" data-formidx="{{'form'+idx}}" data-event-opts="{{[['focus',[['inputFocus',['$event']]]],['blur',[['inputBlur',['$event']]]],['input',[['setfield',['$event']]]]]}}" value="{{formdata['form'+idx]}}" bindfocus="__e" bindblur="__e" bindinput="__e"/><button class="authtel" style="{{'background-color:'+(params.btnbgcolor)+';'+('color:'+(params.btncolor)+';')}}" open-type="getPhoneNumber" type="primary" data-idx="{{idx}}" data-event-opts="{{[['getphonenumber',[['getPhoneNumber',['$event']]]]]}}" bindgetphonenumber="__e">获取手机号码</button></block></block><block wx:else><block><input class="{{['input','form'+idx]}}" style="{{'border-color:'+(params.inputbordercolor)+';'+('background-color:'+(params.inputbgcolor)+';')}}" adjust-position="{{false}}" type="{{item.$orig.val4==1||item.$orig.val4==2?'digit':'text'}}" readonly="{{true}}" name="{{'form'+idx}}" placeholder="{{item.$orig.val2}}" placeholder-style="font-size:28rpx" data-formidx="{{'form'+idx}}" data-event-opts="{{[['focus',[['inputFocus',['$event']]]],['blur',[['inputBlur',['$event']]]],['input',[['setfield',['$event']]]]]}}" value="{{formdata['form'+idx]}}" bindfocus="__e" bindblur="__e" bindinput="__e"/></block></block></block></block><block wx:if="{{params.style==2}}"><view class="value"><block wx:if="{{item.$orig.val5}}"><text style="margin-right:10rpx;">{{item.$orig.val5}}</text></block><block wx:if="{{item.$orig.val4==2&&item.$orig.val6==1}}"><block><input class="input disabled" style="{{'border-color:'+(params.inputbordercolor)+';'+('background-color:'+('#efefef')+';')}}" type="{{item.$orig.val4==1||item.$orig.val4==2?'digit':'text'}}" disabled="true" name="{{'form'+idx}}" placeholder="{{item.$orig.val2}}" placeholder-style="font-size:28rpx" data-formidx="{{'form'+idx}}" data-event-opts="{{[['input',[['setfield',['$event']]]]]}}" value="{{formdata['form'+idx]}}" bindinput="__e"/><button class="authtel" style="{{'background-color:'+(params.btnbgcolor)+';'+('color:'+(params.btncolor)+';')}}" open-type="getPhoneNumber" type="primary" data-idx="{{idx}}" data-event-opts="{{[['getphonenumber',[['getPhoneNumber',['$event']]]]]}}" bindgetphonenumber="__e">获取手机号码</button></block></block><block wx:else><block><input class="input" style="{{'border-color:'+(params.inputbordercolor)+';'+('background-color:'+(params.inputbgcolor)+';')}}" type="{{item.$orig.val4==1||item.$orig.val4==2?'digit':'text'}}" readonly="{{true}}" name="{{'form'+idx}}" placeholder="{{item.$orig.val2}}" placeholder-style="font-size:28rpx" data-formidx="{{'form'+idx}}" data-event-opts="{{[['input',[['setfield',['$event']]]]]}}" value="{{formdata['form'+idx]}}" bindinput="__e"/></block></block></view></block></block></block><block wx:if="{{item.$orig.key=='textarea'}}"><block><textarea class="{{['textarea','form'+idx]}}" style="{{'border-color:'+(params.inputbordercolor)+';'+('background-color:'+(params.inputbgcolor)+';')}}" adjust-position="{{false}}" name="{{'form'+idx}}" placeholder="{{item.$orig.val2}}" placeholder-style="font-size:28rpx" data-formidx="{{'form'+idx}}" data-event-opts="{{[['focus',[['inputFocus',['$event']]]],['blur',[['inputBlur',['$event']]]],['input',[['setfield',['$event']]]]]}}" value="{{formdata['form'+idx]}}" bindfocus="__e" bindblur="__e" bindinput="__e"></textarea></block></block><block wx:if="{{item.$orig.key=='radio'}}"><block><radio-group class="{{[item.$orig.val10=='1'?'rowalone':'flex']}}" style="flex-wrap:wrap;" name="{{'form'+idx}}" data-formidx="{{'form'+idx}}" data-event-opts="{{[['change',[['setfield',['$event']]]]]}}" bindchange="__e"><block wx:for="{{item.$orig.val2}}" wx:for-item="item1" wx:for-index="idx1" wx:key="id"><label class="{{['flex-y-center',item.$orig.val11=='1'?'checkborder':'',item.$orig.val10=='1'?'':'rowmore']}}" style="{{'border-color:'+(params.inputbordercolor)+';'+('background-color:'+(params.inputbgcolor)+';')+('padding:'+('0 10rpx')+';')+('margin-top:'+('10rpx')+';')+('border-radius:'+('10rpx')+';')}}"><radio class="radio" value="{{item1}}" checked="{{formdata['form'+idx]&&formdata['form'+idx]==item1?true:false}}"></radio>{{item1+''}}</label></block></radio-group></block></block><block wx:if="{{item.$orig.key=='checkbox'}}"><block><checkbox-group class="{{[item.$orig.val4=='1'?'rowalone':'flex']}}" style="flex-wrap:wrap;" name="{{'form'+idx}}" data-formidx="{{'form'+idx}}" data-event-opts="{{[['change',[['setfield',['$event']]]]]}}" bindchange="__e"><block wx:for="{{item.l0}}" wx:for-item="item1" wx:for-index="idx1" wx:key="id"><label class="{{['flex-y-center',item.$orig.val9=='1'?'checkborder':'',item.$orig.val4=='1'?'':'rowmore']}}" style="{{'border-color:'+(params.inputbordercolor)+';'+('background-color:'+(params.inputbgcolor)+';')+('padding:'+('0 10rpx')+';')+('margin-top:'+('10rpx')+';')+('border-radius:'+('10rpx')+';')}}"><checkbox class="checkbox" value="{{item1.$orig}}" checked="{{item1.m2?true:false}}"></checkbox>{{item1.$orig+''}}</label></block></checkbox-group></block></block><block wx:if="{{item.$orig.key=='selector'}}"><block><picker class="picker" mode="selector" name="{{'form'+idx}}" value="{{editorFormdata[idx]}}" range="{{item.$orig.val2}}" data-idx="{{idx}}" data-formidx="{{'form'+idx}}" data-event-opts="{{[['change',[['editorBindPickerChange',['$event']]]]]}}" bindchange="__e"><block wx:if="{{editorFormdata[idx]||editorFormdata[idx]===0}}"><view class="flex-y-center flex-bt"><text>{{item.$orig.val2[editorFormdata[idx]]}}</text><view class="arrow-area"><view class="input-arrow"></view></view></view></block><block wx:else><view class="dp-form-normal flex-y-center flex-bt"><text>请选择</text><view class="arrow-area"><view class="input-arrow"></view></view></view></block></picker></block></block><block wx:if="{{item.$orig.key=='time'}}"><block><picker class="picker" mode="time" name="{{'form'+idx}}" value="{{formdata['form'+idx]}}" start="{{item.$orig.val2[0]}}" end="{{item.$orig.val2[1]}}" range="{{item.$orig.val2}}" data-idx="{{idx}}" data-formidx="{{'form'+idx}}" data-event-opts="{{[['change',[['editorBindPickerChange',['$event']]]]]}}" bindchange="__e"><block wx:if="{{editorFormdata[idx]}}"><view class="flex-y-center flex-bt"><text>{{editorFormdata[idx]}}</text><view class="arrow-area"><view class="input-arrow"></view></view></view></block><block wx:else><view class="dp-form-normal flex-y-center flex-bt"><text>请选择</text><view class="arrow-area"><view class="input-arrow"></view></view></view></block></picker></block></block><block wx:if="{{item.$orig.key=='date'}}"><block><picker class="picker" mode="date" name="{{'form'+idx}}" value="{{formdata['form'+idx]}}" start="{{item.$orig.val2[0]}}" end="{{item.$orig.val2[1]}}" range="{{item.$orig.val2}}" data-idx="{{idx}}" data-formidx="{{'form'+idx}}" data-event-opts="{{[['change',[['editorBindPickerChange',['$event']]]]]}}" bindchange="__e"><block wx:if="{{editorFormdata[idx]}}"><view class="flex-y-center flex-bt"><text>{{editorFormdata[idx]}}</text><view class="arrow-area"><view class="input-arrow"></view></view></view></block><block wx:else><view class="dp-form-normal flex-y-center flex-bt"><text>请选择</text><view class="arrow-area"><view class="input-arrow"></view></view></view></block></picker></block></block><block wx:if="{{item.$orig.key=='year'}}"><block><picker class="picker" name="{{'form'+idx}}" value="{{formdata['form'+idx]}}" data-idx="{{idx}}" range="{{yearList}}" data-formidx="{{'form'+idx}}" data-event-opts="{{[['change',[['yearChange',['$event']]]]]}}" bindchange="__e"><block wx:if="{{editorFormdata[idx]}}"><view class="flex-y-center flex-bt"><text>{{editorFormdata[idx]}}</text><view class="arrow-area"><view class="input-arrow"></view></view></view></block><block wx:else><view class="dp-form-normal flex-y-center flex-bt"><text>请选择</text><view class="arrow-area"><view class="input-arrow"></view></view></view></block></picker></block></block><block wx:if="{{item.$orig.key=='region'}}"><block><uni-data-picker style="flex:1;width:100%;" vue-id="{{'d8175b94-1-'+idx}}" localdata="{{items}}" popup-title="请选择省市区" placeholder="{{formdata['form'+idx]||'请选择省市区'}}" data-formidx="{{'form'+idx}}" data-event-opts="{{[['^change',[['onchange']]]]}}" bind:change="__e" bind:__l="__l"></uni-data-picker><input style="display:none;" type="text" name="{{'form'+idx}}" value="{{regiondata?regiondata:formdata['form'+idx]}}"/></block></block><block wx:if="{{item.$orig.key=='upload'}}"><block><input style="display:none;" type="text" name="{{'form'+idx}}" value="{{editorFormdata[idx]}}"/><view class="flex" style="flex-wrap:wrap;padding-top:20rpx;"><block wx:if="{{editorFormdata[idx]}}"><view class="dp-form-imgbox"><view class="dp-form-imgbox-close" data-idx="{{idx}}" data-formidx="{{'form'+idx}}" data-event-opts="{{[['tap',[['removeimg',['$event']]]]]}}" bindtap="__e"><image class="image" src="/static/img/ico-del.png"></image></view><view class="dp-form-imgbox-img"><image class="image" src="{{editorFormdata[idx]}}" data-url="{{editorFormdata[idx]}}" mode="aspectFit" data-idx="{{idx}}" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image></view></view></block><block wx:else><view class="dp-form-uploadbtn" style="{{'background:'+('url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 60rpx')+';'+('background-size:'+('80rpx 80rpx')+';')+('background-color:'+('#F3F3F3')+';')}}" data-idx="{{idx}}" data-formidx="{{'form'+idx}}" data-event-opts="{{[['tap',[['editorChooseImage',['$event']]]]]}}" bindtap="__e"></view></block></view></block></block><block wx:if="{{item.$orig.key=='upload_file'}}"><block><input style="display:none;" type="text" name="{{'form'+idx}}" value="{{editorFormdata[idx]}}"/><view class="flex-y-center" style="flex-wrap:wrap;padding-top:20rpx;"><block wx:if="{{editorFormdata[idx]}}"><view class="dp-form-imgbox"><view class="dp-form-imgbox-close" data-idx="{{idx}}" data-formidx="{{'form'+idx}}" data-event-opts="{{[['tap',[['removeimg',['$event']]]]]}}" bindtap="__e"><image class="image" src="/static/img/ico-del.png"></image></view><view style="overflow:hidden;white-space:pre-wrap;word-wrap:break-word;color:#4786BC;width:530rpx;" data-file="{{editorFormdata[idx]}}" data-event-opts="{{[['tap',[['download',['$event']]]]]}}" bindtap="__e">文件已上传成功</view></view></block><block wx:else><view class="dp-form-uploadbtn" style="{{'margin-right:20rpx;'+('background:'+('url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 60rpx')+';')+('background-size:'+('80rpx 80rpx')+';')+('background-color:'+('#F3F3F3')+';')}}" data-idx="{{idx}}" data-formidx="{{'form'+idx}}" data-event-opts="{{[['tap',[['chooseFile',['$event']]]]]}}" bindtap="__e"></view></block><block wx:if="{{item.$orig.val2}}"><view style="color:#999;">{{item.$orig.val2}}</view></block></view></block></block><block wx:if="{{item.$orig.key=='upload_video'}}"><block><input style="display:none;" type="text" name="{{'form'+idx}}" value="{{editorFormdata[idx]}}"/><view class="flex-y-center" style="flex-wrap:wrap;padding-top:20rpx;"><block wx:if="{{editorFormdata[idx]}}"><view class="dp-form-imgbox"><view class="dp-form-imgbox-close" data-idx="{{idx}}" data-formidx="{{'form'+idx}}" data-event-opts="{{[['tap',[['removeimg',['$event']]]]]}}" bindtap="__e"><image class="image" src="/static/img/ico-del.png"></image></view><view style="overflow:hidden;white-space:pre-wrap;word-wrap:break-word;color:#4786BC;width:430rpx;"><video style="width:100%;" src="{{editorFormdata[idx]}}"></video></view></view></block><block wx:else><view class="dp-form-uploadbtn" style="{{'margin-right:20rpx;'+('background:'+('url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 60rpx')+';')+('background-size:'+('80rpx 80rpx')+';')+('background-color:'+('#F3F3F3')+';')}}" data-idx="{{idx}}" data-formidx="{{'form'+idx}}" data-event-opts="{{[['tap',[['upVideo',['$event']]]]]}}" bindtap="__e"></view></block><block wx:if="{{item.$orig.val2}}"><view style="color:#999;">{{item.$orig.val2}}</view></block></view></block></block><block wx:if="{{item.$orig.key=='map'}}"><block><input style="display:none;" type="text" name="{{'form'+idx}}" value="{{editorFormdata[idx]}}"/><view class="flex-y-center" style="flex-wrap:wrap;padding-top:20rpx;"><text class="flex1" style="{{'text-align:right;'+(area?'':'color:#BBBBBB')}}" data-idx="{{idx}}" data-formidx="{{'form'+idx}}" data-event-opts="{{[['tap',[['selectzuobiao',['$event']]]]]}}" bindtap="__e">{{area?area:'请选择您的位置'}}</text></view></block></block><block wx:if="{{item.$orig.key=='upload_pics'}}"><block><input style="display:none;" type="text" name="{{'form'+idx}}" maxlength="-1" value="{{editorFormdata&&editorFormdata[idx]?item.g0:''}}"/><view class="flex" style="flex-wrap:wrap;padding-top:20rpx;"><block wx:for="{{editorFormdata[idx]}}" wx:for-item="item2" wx:for-index="index2" wx:key="index2"><view class="dp-form-imgbox"><view class="dp-form-imgbox-close" data-index="{{index2}}" data-type="pics" data-idx="{{idx}}" data-formidx="{{'form'+idx}}" data-event-opts="{{[['tap',[['removeimg',['$event']]]]]}}" bindtap="__e"><image class="image" src="/static/img/ico-del.png"></image></view><view class="dp-form-imgbox-img" style="margin-bottom:10rpx;"><image class="image" src="{{item2}}" data-url="{{item2}}" mode="aspectFit" data-idx="{{idx}}" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image></view></view></block><view class="dp-form-uploadbtn" style="{{'background:'+('url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 60rpx')+';'+('background-size:'+('80rpx 80rpx')+';')+('background-color:'+('#F3F3F3')+';')+('margin-bottom:'+('10rpx')+';')}}" data-idx="{{idx}}" data-formidx="{{'form'+idx}}" data-type="pics" data-event-opts="{{[['tap',[['editorChooseImage',['$event']]]]]}}" bindtap="__e"></view></view></block></block></view></block></block><block wx:if="{{data.payset==1}}"><block><block wx:if="{{!data.is_other_fee||data.is_other_fee==0}}"><view class="dp-form-item"><text class="label" style="font-weight:bold;">{{data.price_text!=''?data.price_text:'支付金额：'}}</text><block wx:if="{{data.priceedit==1}}"><input class="input" type="text" name="price" data-formidx="price" data-event-opts="{{[['input',[['setfield',['$event']]]]]}}" value="{{data.price}}" bindinput="__e"/></block><block wx:if="{{data.priceedit==0}}"><text>{{data.price}}</text></block><text style="padding-left:10rpx;">元</text></view></block><block wx:if="{{!data.is_other_fee||data.is_other_fee==0}}"><view class="dp-form-item"><block wx:if="{{data.pay_remark!=''}}"><text class="label" style="margin-left:12rpx;color:#828282;white-space:pre-wrap;width:100%;float:left;text-align:left;">{{data.pay_remark}}</text></block></view></block><block wx:if="{{data.is_other_fee==1}}"><block><view class="{{[params.style==1?'dp-form-item-fee':'dp-form-item-fee2']}}" style="border:none;"><view class="dp-form-label">费用明细</view><view class="dp-form-feelist"><checkbox-group name="feelist"><block wx:for="{{$root.l2}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="dp-fee-item"><view class="dp-fee-name">{{item.$orig.name}}</view><view class="dp-fee-money">{{"￥"+item.$orig.money}}</view><view class="dp-fee-check"><checkbox style="transform:scale(0.7);" data-index="{{index}}" value="{{''+index}}" checked="{{item.$orig.checked?true:false}}" color="{{item.m3}}" data-event-opts="{{[['tap',[['feeChange',['$event']]]]]}}" bindtap="__e"></checkbox></view></view></block></checkbox-group><view class="dp-fee-item sum"><view class="dp-fee-name">合计</view><view class="dp-fee-money">￥<text>{{feetotal}}</text></view><view class="dp-fee-check"></view></view></view></view></block></block></block></block><block wx:if="{{xystatus==1&&xytitlePos=='bottom'}}"><block><view class="xycss1 dp-form-item"><checkbox-group data-event-opts="{{[['change',[['isagreeChange',['$event']]]]]}}" style="display:inline-block;" bindchange="__e"><checkbox style="transform:scale(0.6);" value="1" checked="{{isagree}}" color="{{$root.m4}}"></checkbox></checkbox-group><text>我已阅读并同意</text><text data-event-opts="{{[['tap',[['showxieyiFun',['$event']]]]]}}" style="{{'color:'+($root.m5)+';'}}" bindtap="__e">{{xytitle}}</text></view></block></block><block wx:if="{{showxieyi}}"><view class="xieyibox"><view class="xieyibox-content"><view style="overflow:scroll;height:100%;"><parse vue-id="d8175b94-2" content="{{xycontent}}" data-event-opts="{{[['^navigate',[['navigate']]]]}}" bind:navigate="__e" bind:__l="__l"></parse></view><view data-event-opts="{{[['tap',[['hidexieyi',['$event']]]]]}}" style="{{'position:absolute;z-index:9999;bottom:10px;left:0;right:0;margin:0 auto;text-align:center;width:50%;height:60rpx;line-height:60rpx;color:#fff;border-radius:8rpx;'+('background:'+('linear-gradient(90deg,'+$root.m6+' 0%,rgba('+$root.m7+',0.8) 100%)')+';')}}" bindtap="__e">{{agree_button}}</view></view></view></block><block wx:if="{{data!=''}}"><button class="dp-form-btn flex-xy-center" style="{{'background-color:'+(params.btnbgcolor)+';'+('border:'+('1px solid '+params.btnbordercolor)+';')+('font-size:'+(params.btnfontsize*2+'rpx')+';')+('color:'+(params.btncolor)+';')+('width:'+(params.btnwidth*2.2+'rpx')+';')+('height:'+(params.btnheight*2.2+'rpx')+';')+('line-height:'+(params.btnheight*2.2+'rpx')+';')+('border-radius:'+(params.btnradius*2.2+'rpx')+';')}}" data-formcontent="{{data.content}}" data-tourl="{{params.hrefurl}}" data-formid="{{data.id}}" data-event-opts="{{[['tap',[['editorFormSubmit',['$event']]]]]}}" bindtap="__e">{{params.btntext}}</button></block><view style="{{'height:'+(''+keyboardHeight+'px')+';'}}"></view></form></view>