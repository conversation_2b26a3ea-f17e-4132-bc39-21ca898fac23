require('../common/vendor.js');(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["admin/freight/region-select"],{"012b9":function(e,t,n){"use strict";var i=n("3550"),r=n.n(i);r.a},"015a":function(e,t,n){"use strict";n.d(t,"b",(function(){return i})),n.d(t,"c",(function(){return r})),n.d(t,"a",(function(){}));var i=function(){var e=this,t=e.$createElement,n=(e._self._c,e.selectedRegions.length),i=n>0?e.selectedRegions.length:null,r=0===e.currentTab?e.__map(e.filteredProvinces,(function(t,n){var i=e.__get_orig(t),r=e.isProvinceFullySelected(t);return{$orig:i,m0:r}})):null,c=1===e.currentTab?e.__map(e.filteredCities,(function(t,n){var i=e.__get_orig(t),r=e.isCitySelected(t);return{$orig:i,m1:r}})):null;e.$mp.data=Object.assign({},{$root:{g0:n,g1:i,l0:r,l1:c}})},r=[]},"1ba86":function(e,t,n){"use strict";n.r(t);var i=n("015a"),r=n("3686");for(var c in r)["default"].indexOf(c)<0&&function(e){n.d(t,e,(function(){return r[e]}))}(c);n("012b9");var s=n("828b"),o=Object(s["a"])(r["default"],i["b"],i["c"],!1,null,null,null,!1,i["a"],void 0);t["default"]=o.exports},"265b":function(e,t,n){"use strict";(function(e){Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=getApp(),i={data:function(){return{searchKey:"",selectedRegions:[],tabs:["省份","城市"],currentTab:0,currentProvince:null,provinces:[],cities:[]}},computed:{filteredProvinces:function(){var e=this;return this.searchKey?this.provinces.filter((function(t){return t.name.includes(e.searchKey)})):this.provinces},filteredCities:function(){var e=this;if(!this.currentProvince)return[];var t=this.currentProvince.children||[];return this.searchKey?t.filter((function(t){return t.name.includes(e.searchKey)})):t}},onLoad:function(e){var t=this;this.opt=n.getopts(e),this.getProvinces();var i=this.getOpenerEventChannel();i&&i.on("currentSelected",(function(e){t.selectedRegions=e.selected||[]}))},methods:{getProvinces:function(){var e=this;n.post("ApiAdminProduct/getAreas",{pid:0},(function(t){1==t.status?e.provinces=t.data:n.error(t.msg)}))},selectProvince:function(e){this.currentProvince=e,this.currentTab=1,this.getCities(e.id)},selectCity:function(e){var t=this,n={province:this.currentProvince,city:e,name:"".concat(this.currentProvince.name).concat(e.name)},i=this.selectedRegions.findIndex((function(n){return n.province.id===t.currentProvince.id&&n.city.id===e.id}));i>-1?this.selectedRegions.splice(i,1):this.selectedRegions.push(n)},switchTab:function(e){1!==e||this.currentProvince?this.currentTab=e:n.error("请先选择省份")},removeRegion:function(e){var t=this.selectedRegions.findIndex((function(t){return t.code===e.code}));t>-1&&this.selectedRegions.splice(t,1)},cancel:function(){e.navigateBack()},confirm:function(){var t=this.getOpenerEventChannel();t&&t.emit("onSelected",this.selectedRegions),e.navigateBack()},selectAllInProvince:function(e){this.isProvinceFullySelected(e)?this.selectedRegions=this.selectedRegions.filter((function(t){return t.province.id!==e.id})):this.selectedRegions.push({province:e,city:{name:"全部地区",id:"all"},name:"".concat(e.name,"全部地区")})},isProvinceFullySelected:function(e){return this.selectedRegions.some((function(t){return t.province.id===e.id&&"全部地区"===t.city.name}))},isCitySelected:function(e){var t=this;return this.selectedRegions.some((function(n){return n.province.id===t.currentProvince.id&&n.city.id===e.id}))}}};t.default=i}).call(this,n("df3c")["default"])},3550:function(e,t,n){},3686:function(e,t,n){"use strict";n.r(t);var i=n("265b"),r=n.n(i);for(var c in i)["default"].indexOf(c)<0&&function(e){n.d(t,e,(function(){return i[e]}))}(c);t["default"]=r.a},"588f":function(e,t,n){"use strict";(function(e,t){var i=n("47a9");n("06e9");i(n("3240"));var r=i(n("1ba86"));e.__webpack_require_UNI_MP_PLUGIN__=n,t(r.default)}).call(this,n("3223")["default"],n("df3c")["createPage"])}},[["588f","common/runtime","common/vendor"]]]);