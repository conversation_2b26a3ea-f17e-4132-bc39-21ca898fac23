<view class="container"><block><form data-event-opts="{{[['submit',[['formSubmit',['$event']]]]]}}" bindsubmit="__e"><view class="content1"><view class="top flex"><view class="f1"><image src="{{pre_url+'/static/img/exp_ji.png'}}"></image></view><block wx:if="{{address.id}}"><view class="f2" data-url="address?fromPage=mail&mailtype=1" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="t1">{{address.name+" "+address.tel}}</view><view class="t2">{{address.area+" "+address.address+''}}</view></view></block><block wx:else><view class="f2" data-url="addressadd?fromPage=mail&mailtype=1" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="t1">寄件人信息</view><view class="t2">点击填写寄件地址,自动智能填写</view></view></block><view class="f3" data-url="address?fromPage=mail&mailtype=1" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view><image src="/static/img/exp_txl.png"></image></view><view class="t3">地址薄</view></view></view><view class="top2 flex"><view class="f1"><image src="{{pre_url+'/static/img/exp_shou.png'}}"></image></view><block wx:if="{{address2.id}}"><view class="f2" data-url="address?fromPage=mail&mailtype=2" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="t1">{{address2.name+" "+address2.tel}}</view><view class="t2">{{address2.area+" "+address2.address+''}}</view></view></block><block wx:else><view class="f2" data-url="addressadd?fromPage=mail&mailtype=2" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="t1">收件人信息</view><view class="t2">复制完整信息，自动智能填写</view></view></block><view class="f3" data-url="address?fromPage=mail&mailtype=2" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view><image src="/static/img/exp_txl.png"></image></view><view class="t3">地址薄</view></view></view></view><view class="form"><view class="form-item"><text class="label">物品信息</text><input class="input" type="text" placeholder="请输入物品信息" placeholder-style="font-size:28rpx;color:#BBBBBB" name="cargo"/></view><view class="form-item"><text class="label">预估重量</text><view class="addnum"><view class="minus"><image class="img" src="/static/img/cart-minus.png" data-event-opts="{{[['tap',[['minus',['$event']]]]]}}" bindtap="__e"></image></view><input class="input" type="number" value="{{this.weight}}"/>KG<view class="plus"><image class="img" src="/static/img/cart-plus.png" data-event-opts="{{[['tap',[['plus',['$event']]]]]}}" bindtap="__e"></image></view></view></view><view class="form-item"><text class="label">选择配送公司</text><block wx:if="{{expressdata}}"><picker style="font-size:28rpx;padding:10rpx;height:70rpx;border-radius:4px;flex:1;" value="{{express_index}}" range="{{expressdata}}" data-event-opts="{{[['change',[['expresschange',['$event']]]]]}}" bindchange="__e"><view data-event-opts="{{[['tap',[['changeyl',['$event']]]]]}}" class="picker" style="height:70rpx;" bindtap="__e">{{expressdata[express_index]?expressdata[express_index]:'请选择配送公司'}}</view></picker></block><block wx:else><view data-event-opts="{{[['tap',[['changeyl',['$event']]]]]}}" class="picker" style="height:70rpx;" bindtap="__e">{{expressdata[express_index]?expressdata[express_index]:'请选择配送公司'}}</view></block><image class="icon" src="/static/img/arrowright.png"></image></view><view class="form-item"><text class="label">期望上门时间</text><view class="input" data-bid="{{0}}" data-event-opts="{{[['tap',[['choosePstime',['$event']]]]]}}" bindtap="__e">{{''+(pstimetext==''?'请选择上门时间':pstimetext)+''}}</view><image class="icon" src="/static/img/arrowright.png"></image></view><view class="form-item"><text class="label">给快递员留言</text><input class="input" type="text" placeholder="选填" placeholder-style="font-size:28rpx;color:#BBBBBB" name="remark"/></view></view><block wx:if="{{showxieyi}}"><view class="xieyibox"><view class="xieyibox-content"><view style="overflow:scroll;height:100%;"><parse bind:navigate="__e" vue-id="790b33ae-1" data-event-opts="{{[['^navigate',[['navigate']]]]}}" bind:__l="__l"></parse></view><view data-event-opts="{{[['tap',[['hidexieyi',['$event']]]]]}}" style="position:absolute;z-index:9999;bottom:10px;left:0;right:0;margin:0 auto;text-align:center;width:50%;height:60rpx;line-height:60rpx;color:#fff;border-radius:8rpx;" bindtap="__e">已阅读并同意</view></view></view></block><view class="bottom flex"><view class="left"><view><block wx:if="{{price}}"><text class="t1">{{"￥"+price}}</text></block><block wx:else><text class="t1">￥--</text></block><text class="t2">预估运费</text></view><view class="t3">注：费用以实际寄件为准</view></view><view><button class="tobuy" style="{{'background:'+($root.m0)+';'}}" form-type="submit">下单</button></view></view></form></block><block wx:if="{{loading}}"><loading vue-id="790b33ae-2" bind:__l="__l"></loading></block><dp-tabbar vue-id="790b33ae-3" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="790b33ae-4" data-ref="popmsg" bind:__l="__l"></popmsg><block wx:if="{{pstimeDialogShow}}"><view class="popup__container"><view data-event-opts="{{[['tap',[['hidePstimeDialog',['$event']]]]]}}" class="popup__overlay" catchtap="__e"></view><view class="popup__modal"><view class="popup__title"><text class="popup__title-text">请选择上门时间</text><image class="popup__close" style="width:36rpx;height:36rpx;" src="/static/img/close.png" data-event-opts="{{[['tap',[['hidePstimeDialog',['$event']]]]]}}" catchtap="__e"></image></view><view class="popup__content"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="pstime-item" data-index="{{index}}" data-event-opts="{{[['tap',[['pstimeRadioChange',['$event']]]]]}}" bindtap="__e"><view class="flex1">{{item.$orig.title}}</view><view class="radio" style="{{(sm_time==item.$orig.value?'background:'+item.m1+';border:0':'')}}"><image class="radio-img" src="/static/img/checkd.png"></image></view></view></block></view></view></view></block></view>