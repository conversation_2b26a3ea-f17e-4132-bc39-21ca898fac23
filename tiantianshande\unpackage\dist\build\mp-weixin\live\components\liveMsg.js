require('../common/vendor.js');(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["live/components/liveMsg"],{"1e942":function(e,s,t){},"28f2":function(e,s,t){"use strict";t.d(s,"b",(function(){return n})),t.d(s,"c",(function(){return i})),t.d(s,"a",(function(){}));var n=function(){var e=this.$createElement;this._self._c},i=[]},"316b":function(e,s,t){"use strict";t.r(s);var n=t("356b"),i=t.n(n);for(var a in n)["default"].indexOf(a)<0&&function(e){t.d(s,e,(function(){return n[e]}))}(a);s["default"]=i.a},"356b":function(e,s,t){"use strict";(function(e){var n=t("47a9");Object.defineProperty(s,"__esModule",{value:!0}),s.default=void 0;var i=n(t("af34")),a={data:function(){return{msgMask:null,messageList:[],cachedMessages:[],scrollTop:0}},methods:{scrollEvent:function(e){this.msgMask=e.detail.scrollTop>0},sendLiveMsg:function(e){console.log(e);var s=e.split("-");"like"===s[2]||"out"===s[2]||"join"===s[2]?this.messageList.push({nickname:"",message:s[1]}):this.messageList.push({nickname:s[0],message:s[1]}),this.scrollToLower()},scrollToLower:function(){var s=this;setTimeout((function(){var t=e.createSelectorQuery().in(s);t.select("#msg-area").boundingClientRect((function(e){s.scrollTop=e.height})).exec()}),50)},clearMessages:function(){this.cachedMessages=(0,i.default)(this.messageList),this.messageList=[]},restoreMessages:function(){this.cachedMessages.length>0&&(this.messageList=(0,i.default)(this.cachedMessages),this.scrollToLower())}}};s.default=a}).call(this,t("df3c")["default"])},3607:function(e,s,t){"use strict";t.r(s);var n=t("28f2"),i=t("316b");for(var a in i)["default"].indexOf(a)<0&&function(e){t.d(s,e,(function(){return i[e]}))}(a);t("7b17");var c=t("828b"),o=Object(c["a"])(i["default"],n["b"],n["c"],!1,null,"3afee289",null,!1,n["a"],void 0);s["default"]=o.exports},"7b17":function(e,s,t){"use strict";var n=t("1e942"),i=t.n(n);i.a}}]);
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'live/components/liveMsg-create-component',
    {
        'live/components/liveMsg-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('df3c')['createComponent'](__webpack_require__("3607"))
        })
    },
    [['live/components/liveMsg-create-component']]
]);
