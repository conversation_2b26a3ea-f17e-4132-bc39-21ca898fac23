<view class="container"><block wx:if="{{isload}}"><block><view class="stats-container"><view class="stats-card"><view class="stats-item"><text class="stats-number">{{statistics.total_merchants}}</text><text class="stats-label">总商户数</text></view><view class="stats-item"><text class="stats-number">{{statistics.active_merchants}}</text><text class="stats-label">活跃商户</text></view><view class="stats-item"><text class="stats-number">{{statistics.today_new_merchants}}</text><text class="stats-label">今日新增</text></view></view><view class="amount-card"><view class="amount-item"><text class="amount-label">总交易额</text><text class="amount-value">{{"¥"+statistics.total_amount}}</text></view><view class="amount-item"><text class="amount-label">本月交易额</text><text class="amount-value">{{"¥"+statistics.month_amount}}</text></view></view></view><view class="filter-container"><view class="filter-row"><view class="search-box"><input class="search-input" type="text" placeholder="搜索商户名称" data-event-opts="{{[['confirm',[['onSearch',['$event']]]],['input',[['__set_model',['','keyword','$event',[]]]]]]}}" value="{{keyword}}" bindconfirm="__e" bindinput="__e"/><text data-event-opts="{{[['tap',[['onSearch',['$event']]]]]}}" class="iconfont iconsousuo search-icon" bindtap="__e"></text></view></view><view class="filter-row"><picker value="{{statusIndex}}" range="{{statusList}}" range-key="name" data-event-opts="{{[['change',[['onStatusChange',['$event']]]]]}}" bindchange="__e"><view class="filter-item"><text>{{statusList[statusIndex].name}}</text><text class="iconfont iconjiantou"></text></view></picker><picker value="{{typeIndex}}" range="{{typeList}}" range-key="name" data-event-opts="{{[['change',[['onTypeChange',['$event']]]]]}}" bindchange="__e"><view class="filter-item"><text>{{typeList[typeIndex].name}}</text><text class="iconfont iconjiantou"></text></view></picker></view></view><view class="merchant-list"><block wx:for="{{datalist}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="merchant-item" data-url="{{'merchant_detail?merchant_id='+item.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="merchant-header"><view class="merchant-avatar"><image class="avatar-img" src="{{item.logo||'/static/img/default_merchant.png'}}"></image></view><view class="merchant-info"><view class="merchant-name">{{item.name}}</view><view class="merchant-category">{{item.category_name||'未分类'}}</view><view class="merchant-address"><text class="iconfont iconweizhi"></text><text class="address-text">{{item.address||'暂无地址'}}</text></view></view><view class="merchant-status"><text class="status-badge" style="{{'background:'+(item.status_color)+';'}}">{{''+item.status_text+''}}</text></view></view><view class="merchant-stats"><view class="stat-item"><text class="stat-label">订单数</text><text class="stat-value">{{item.order_count}}</text></view><view class="stat-item"><text class="stat-label">交易额</text><text class="stat-value">{{"¥"+item.order_amount}}</text></view><view class="stat-item"><text class="stat-label">商品数</text><text class="stat-value">{{item.product_count}}</text></view><view class="stat-item"><text class="stat-label">余额</text><text class="stat-value">{{"¥"+item.money}}</text></view></view><view class="merchant-actions"><view class="action-btn" data-phone="{{item.phone}}" data-event-opts="{{[['tap',[['callMerchant',['$event']]]]]}}" catchtap="__e"><text class="iconfont icondianhua"></text><text>电话</text></view><view class="action-btn" data-id="{{item.id}}" data-event-opts="{{[['tap',[['gotoBusinessManage',['$event']]]]]}}" catchtap="__e"><text class="iconfont iconguanli"></text><text>详情</text></view><view class="action-btn" data-longitude="{{item.longitude}}" data-latitude="{{item.latitude}}" data-name="{{item.name}}" data-event-opts="{{[['tap',[['viewLocation',['$event']]]]]}}" catchtap="__e"><text class="iconfont iconweizhi"></text><text>位置</text></view></view></view></block></view></block></block><block wx:if="{{nodata}}"><nodata vue-id="2a285916-1" bind:__l="__l"></nodata></block><block wx:if="{{nomore}}"><nomore vue-id="2a285916-2" bind:__l="__l"></nomore></block><block wx:if="{{loading}}"><loading vue-id="2a285916-3" bind:__l="__l"></loading></block><dp-tabbar vue-id="2a285916-4" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="2a285916-5" data-ref="popmsg" bind:__l="__l"></popmsg></view>