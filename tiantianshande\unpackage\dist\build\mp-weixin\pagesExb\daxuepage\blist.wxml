<view class="container"><block wx:if="{{isload}}"><block><view class="search-container"><view class="topsearch flex-y-center"><view class="f1 flex-y-center"><image class="img" src="/static/img/search_ico.png"></image><input placeholder="搜索你感兴趣的大学" placeholder-style="font-size:24rpx;color:#C2C2C2" confirm-type="search" data-event-opts="{{[['confirm',[['search',['$event']]]]]}}" value="{{keyword}}" bindconfirm="__e"/></view></view><view class="search-navbar"><h-m-filter-dropdown vue-id="9a02745e-1" menuTop="{{88}}" filterData="{{filterData}}" defaultSelected="{{defaultSelected}}" updateMenuName="{{true}}" dataFormat="Object" data-event-opts="{{[['^confirm',[['confirm']]]]}}" bind:confirm="__e" bind:__l="__l" vue-slots="{{['body']}}"><view style="width:100%;" slot="body"><view data-event-opts="{{[['tap',[['showDrawer',['showRight']]]]]}}" class="search-navbar-item flex-x-center flex-y-center" catchtap="__e">筛选<text class="{{['iconfont iconshaixuan '+(showfilter?'active':'')]}}"></text></view></view></h-m-filter-dropdown></view></view><uni-drawer class="vue-ref" vue-id="9a02745e-2" mode="right" width="{{280}}" data-ref="showRight" data-event-opts="{{[['^change',[['change',['$event','showRight']]]]]}}" bind:change="__e" bind:__l="__l" vue-slots="{{['default']}}"><view class="filter-scroll-view"><scroll-view class="filter-scroll-view-box" scroll-y="true"><view class="search-filter"><view class="filter-title">筛选</view><view class="filter-content-title">选择专业</view><view class="search-filter-content"><view class="filter-item" style="{{(catchecid==oldcid?'color:'+$root.m0+';background:rgba('+$root.m1+',0.1)':'')}}" data-cid="{{oldcid}}" data-event-opts="{{[['tap',[['cateClick',['$event']]]]]}}" catchtap="__e">全部</view><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><view class="filter-item" style="{{(catchecid==item.$orig.id?'color:'+item.m2+';background:rgba('+item.m3+',0.1)':'')}}" data-cid="{{item.$orig.id}}" data-event-opts="{{[['tap',[['cateClick',['$event']]]]]}}" catchtap="__e">{{item.$orig.name}}</view></block></block></view><view class="search-filter-btn"><view data-event-opts="{{[['tap',[['filterReset',['$event']]]]]}}" class="btn" bindtap="__e">重置</view><view data-event-opts="{{[['tap',[['filterConfirm',['$event']]]]]}}" class="btn2" style="{{'background:'+($root.m4)+';'}}" bindtap="__e">确定</view></view></view></scroll-view></view></uni-drawer><view class="ind_business"><view class="ind_buslist" id="datalist"><block wx:for="{{datalist}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><view data-url="{{'/pagesExa/daxuepage/index?id='+item.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="ind_busbox flex1 flex-row"><view class="ind_buspic flex0"><image src="{{item.logo}}"></image></view><view class="flex1"><view class="bus_title">{{item.name}}</view><view class="tags-container"><block wx:for="{{item.type_names}}" wx:for-item="type" wx:for-index="index" wx:key="index"><block><view class="tag-item">{{type}}</view></block></block><block wx:for="{{item.school_nature}}" wx:for-item="nature" wx:for-index="index"><block><view class="tag-item">{{nature}}</view></block></block><block wx:for="{{item.enrollment_type}}" wx:for-item="enrollType" wx:for-index="index"><block><view class="tag-item">{{enrollType}}</view></block></block><block wx:for="{{item.biaoqian_names}}" wx:for-item="tag" wx:for-index="index"><block><view class="tag-item">{{tag}}</view></block></block></view><view class="bus_sales">{{"收藏："+item.sales}}</view><block wx:if="{{item.address}}"><view class="bus_address" data-latitude="{{item.latitude}}" data-longitude="{{item.longitude}}" data-company="{{item.name}}" data-address="{{item.address}}" data-event-opts="{{[['tap',[['openLocation',['$event']]]]]}}" catchtap="__e"><image style="width:26rpx;height:26rpx;margin-right:10rpx;" src="/static/img/b_addr.png"></image><text class="x1">{{item.address}}</text><text class="x2">{{item.juli}}</text></view></block></view></view></view></block></block><block wx:if="{{nomore}}"><nomore vue-id="9a02745e-3" bind:__l="__l"></nomore></block><block wx:if="{{nodata}}"><nodata vue-id="9a02745e-4" bind:__l="__l"></nodata></block></view></view><block wx:if="{{buydialogShow}}"><buydialog vue-id="9a02745e-5" proid="{{proid}}" menuindex="{{menuindex}}" data-event-opts="{{[['^addcart',[['addcart']]],['^buydialogChange',[['buydialogChange']]]]}}" bind:addcart="__e" bind:buydialogChange="__e" bind:__l="__l"></buydialog></block></block></block><block wx:if="{{loading}}"><loading vue-id="9a02745e-6" bind:__l="__l"></loading></block><dp-tabbar vue-id="9a02745e-7" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="9a02745e-8" data-ref="popmsg" bind:__l="__l"></popmsg></view>