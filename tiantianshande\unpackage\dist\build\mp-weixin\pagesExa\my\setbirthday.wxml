<view class="container"><block wx:if="{{isload}}"><block><form data-event-opts="{{[['submit',[['formSubmit',['$event']]]],['reset',[['formReset',['$event']]]]]}}" bindsubmit="__e" bindreset="__e"><view class="form"><view class="form-item"><text class="label">生日</text><picker class="picker" mode="date" value="" start="1900-01-01" end="{{$root.m0}}" data-event-opts="{{[['change',[['bindDateChange',['$event']]]]]}}" bindchange="__e"><block wx:if="{{birthday}}"><view>{{birthday}}</view></block><block wx:else><view>请选择生日</view></block></picker></view></view><button class="set-btn" style="{{'background:'+('linear-gradient(90deg,'+$root.m1+' 0%,rgba('+$root.m2+',0.8) 100%)')+';'}}" form-type="submit">保 存</button></form></block></block><block wx:if="{{loading}}"><loading vue-id="e39151fa-1" bind:__l="__l"></loading></block><dp-tabbar vue-id="e39151fa-2" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="e39151fa-3" data-ref="popmsg" bind:__l="__l"></popmsg></view>