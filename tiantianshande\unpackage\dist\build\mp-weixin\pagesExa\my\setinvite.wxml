<view class="container"><block wx:if="{{isload}}"><block><form data-event-opts="{{[['submit',[['formSubmit',['$event']]]],['reset',[['formReset',['$event']]]]]}}" bindsubmit="__e" bindreset="__e"><view class="form"><view class="form-item"><input class="input" type="text" placeholder="{{inviteCodePlaceholder}}" placeholder-style="color:#BBBBBB;font-size:28rpx" name="invite_code" data-event-opts="{{[['input',[['__set_model',['','invite_code','$event',[]]],['inviteCodeInput',['$event']]]]]}}" value="{{invite_code}}" bindinput="__e"/></view><block wx:if="{{parentInfo.nickname}}"><view class="form-item" style="border:0;padding:20rpx 0;"><view style="color:#666;font-size:28rpx;"><view style="margin-bottom:10rpx;">邀请人信息：</view><view style="display:flex;align-items:center;"><image style="width:60rpx;height:60rpx;border-radius:50%;margin-right:20rpx;" src="{{parentInfo.headimg}}"></image><view><view style="font-size:30rpx;color:#333;">{{parentInfo.nickname}}</view><block wx:if="{{parentInfo.tel}}"><view style="font-size:26rpx;color:#999;">{{parentInfo.tel}}</view></block></view></view></view></view></block></view><button class="set-btn" style="{{'background:'+('linear-gradient(90deg,'+$root.m0+' 0%,rgba('+$root.m1+',0.8) 100%)')+';'}}" form-type="submit">确认绑定</button></form></block></block><block wx:if="{{loading}}"><loading vue-id="11485677-1" bind:__l="__l"></loading></block><dp-tabbar vue-id="11485677-2" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="11485677-3" data-ref="popmsg" bind:__l="__l"></popmsg></view>