<view class="container"><block><view class="search-container"><view class="topsearch flex-y-center"><view class="f1 flex-y-center"><image class="img" src="/static/img/search_ico.png"></image><input placeholder="搜索你感兴趣的大学" placeholder-style="font-size:24rpx;color:#C2C2C2" confirm-type="search" data-event-opts="{{[['confirm',[['search',['$event']]]]]}}" value="{{keyword}}" bindconfirm="__e"/></view></view><view class="search-navbar"></view></view><view class="ind_business"><view class="ind_buslist" id="datalist"><block wx:for="{{datalist}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><view data-url="{{'/pagesExa/daxuepage/index?id='+item.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="ind_busbox flex1 flex-row"><view class="ind_buspic flex0"><image src="{{item.logo}}"></image></view><view class="flex1"><view class="bus_title">{{item.name}}</view><view class="tags-container"><block wx:for="{{item.type_names}}" wx:for-item="type" wx:for-index="index"><block><view class="tag-item">{{type}}</view></block></block><block wx:for="{{item.school_nature}}" wx:for-item="nature" wx:for-index="index"><block><view class="tag-item">{{nature}}</view></block></block><block wx:for="{{item.enrollment_type}}" wx:for-item="enrollType" wx:for-index="index"><block><view class="tag-item">{{enrollType}}</view></block></block><block wx:for="{{item.biaoqian_names}}" wx:for-item="tag" wx:for-index="index"><block><view class="tag-item">{{tag}}</view></block></block></view><view class="bus_sales">{{"收藏："+item.sales}}</view><block wx:if="{{item.address}}"><view class="bus_address" data-latitude="{{item.latitude}}" data-longitude="{{item.longitude}}" data-company="{{item.name}}" data-address="{{item.address}}" data-event-opts="{{[['tap',[['openLocation',['$event']]]]]}}" catchtap="__e"><image style="width:26rpx;height:26rpx;margin-right:10rpx;" src="/static/img/b_addr.png"></image><text class="x1">{{item.address}}</text><text class="x2">{{item.juli}}</text></view></block></view></view></view></block></block><view class="result-description"><text>结果仅供参考</text></view><block wx:if="{{nomore}}"><nomore vue-id="5d1fa5cc-1" bind:__l="__l"></nomore></block><block wx:if="{{nodata}}"><nodata vue-id="5d1fa5cc-2" bind:__l="__l"></nodata></block></view></view><block wx:if="{{buydialogShow}}"><buydialog vue-id="5d1fa5cc-3" proid="{{proid}}" menuindex="{{menuindex}}" data-event-opts="{{[['^addcart',[['addcart']]],['^buydialogChange',[['buydialogChange']]]]}}" bind:addcart="__e" bind:buydialogChange="__e" bind:__l="__l"></buydialog></block></block><block wx:if="{{loading}}"><loading vue-id="5d1fa5cc-4" bind:__l="__l"></loading></block><dp-tabbar vue-id="5d1fa5cc-5" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="5d1fa5cc-6" data-ref="popmsg" bind:__l="__l"></popmsg></view>