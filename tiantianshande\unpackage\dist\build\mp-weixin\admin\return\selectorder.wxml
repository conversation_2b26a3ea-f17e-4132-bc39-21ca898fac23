<view class="container"><view class="search-bar"><view class="search-input"><image class="search-icon" src="/static/img/search_ico.png"></image><input type="text" placeholder="输入进货订单号搜索" data-event-opts="{{[['confirm',[['searchOrders',['$event']]]],['input',[['__set_model',['$0','orderNo','$event',[]],['search']]]]]}}" value="{{search.orderNo}}" bindconfirm="__e" bindinput="__e"/></view><view data-event-opts="{{[['tap',[['searchOrders',['$event']]]]]}}" class="search-btn" bindtap="__e">搜索</view></view><block wx:if="{{$root.g0>0}}"><view class="order-list"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['selectOrder',['$0'],[[['orderList','',index]]]]]]]}}" class="order-item" bindtap="__e"><view class="order-header"><text class="order-no">{{"订单号: "+item.$orig.order_no}}</text><text class="order-status" style="{{'color:'+(item.m0)+';'}}">{{item.m1}}</text></view><view class="order-info"><view class="info-row"><text class="info-label">总金额:</text><text class="info-value">{{"￥"+item.$orig.total_price}}</text><text class="info-label">下单时间:</text><text class="info-value">{{item.$orig.create_time}}</text></view><view class="info-row"><text class="info-label">商品数量:</text><text class="info-value">{{item.$orig.product_count+"种"}}</text><text class="arrow-right">></text></view></view></view></block></view></block><block wx:if="{{$root.g1}}"><nodata vue-id="1ac4f0e5-1" bind:__l="__l"></nodata></block><block wx:if="{{loading}}"><loading vue-id="1ac4f0e5-2" bind:__l="__l"></loading></block><block wx:if="{{nomore}}"><nomore vue-id="1ac4f0e5-3" bind:__l="__l"></nomore></block><popmsg class="vue-ref" vue-id="1ac4f0e5-4" data-ref="popmsg" bind:__l="__l"></popmsg></view>