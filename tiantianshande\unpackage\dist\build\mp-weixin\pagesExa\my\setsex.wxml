<view class="container"><block wx:if="{{isload}}"><block><form data-event-opts="{{[['submit',[['formSubmit',['$event']]]],['reset',[['formReset',['$event']]]]]}}" bindsubmit="__e" bindreset="__e"><view class="form"><view class="form-item"><text class="label">性别</text><radio-group class="radio-group" name="sex"><label class="radio"><radio value="1"></radio>男</label><label class="radio"><radio value="2"></radio>女</label></radio-group></view></view><button class="set-btn" style="{{'background:'+('linear-gradient(90deg,'+$root.m0+' 0%,rgba('+$root.m1+',0.8) 100%)')+';'}}" form-type="submit">保 存</button></form></block></block><block wx:if="{{loading}}"><loading vue-id="9ea498d4-1" bind:__l="__l"></loading></block><dp-tabbar vue-id="9ea498d4-2" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="9ea498d4-3" data-ref="popmsg" bind:__l="__l"></popmsg></view>