<block wx:if="{{currentIndex>-1&&!isHidden}}"><view class="data-v-514cd9e3"><block wx:if="{{$root.g0}}"><view class="dp-tabbar data-v-514cd9e3"><view class="dp-tabbar-bot data-v-514cd9e3"></view><block wx:if="{{$root.g1==5}}"><view class="dp-tabbar-module data-v-514cd9e3"><view class="dp-tabbar-cut data-v-514cd9e3" style="{{'background-color:'+(menudata.backgroundColor)+';'}}"></view><view class="dp-tabbar-sideL dp-tabbar-sideLP data-v-514cd9e3" style="{{'background-color:'+(menudata.backgroundColor)+';'}}"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="id"><block wx:if="{{index<2}}"><view class="dp-tabbar-item data-v-514cd9e3" data-url="{{item.$orig.pagePath}}" data-index="{{index}}" data-opentype="{{item.m0?'':opentype}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="dp-tabbar-image-box data-v-514cd9e3"><block wx:if="{{currentIndex===index}}"><image class="dp-tabbar-icon data-v-514cd9e3" src="{{item.$orig.selectedIconPath}}"></image></block><block wx:else><image class="dp-tabbar-icon data-v-514cd9e3" src="{{item.$orig.iconPath}}"></image></block></view><view class="dp-tabbar-text data-v-514cd9e3" style="{{'color:'+(item.$orig.color)+';'}}">{{item.$orig.text}}</view></view></block></block></view><block wx:for="{{$root.l1}}" wx:for-item="item" wx:for-index="index" wx:key="id"><block wx:if="{{index==2}}"><view class="dp-tabbar-center data-v-514cd9e3" style="{{('background-image: radial-gradient(circle at top, rgba(0,0,0,0) 55rpx, '+menudata.backgroundColor+' 55rpx);')}}" data-url="{{item.$orig.pagePath}}" data-index="{{index}}" data-opentype="{{item.m1?'':opentype}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image src="{{item.$orig.iconPath}}" class="data-v-514cd9e3"></image><view style="{{'color:'+(item.$orig.color)+';'}}" class="data-v-514cd9e3">{{item.$orig.text}}</view></view></block></block><view class="dp-tabbar-sideR dp-tabbar-sideRP data-v-514cd9e3" style="{{'background-color:'+(menudata.backgroundColor)+';'}}"><block wx:for="{{$root.l2}}" wx:for-item="item" wx:for-index="index" wx:key="id"><block wx:if="{{index>=3}}"><view class="dp-tabbar-item data-v-514cd9e3" data-url="{{item.$orig.pagePath}}" data-index="{{index}}" data-opentype="{{item.m2?'':opentype}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="dp-tabbar-image-box data-v-514cd9e3"><block wx:if="{{currentIndex===index}}"><image class="dp-tabbar-icon data-v-514cd9e3" src="{{item.$orig.selectedIconPath}}"></image></block><block wx:else><image class="dp-tabbar-icon data-v-514cd9e3" src="{{item.$orig.iconPath}}"></image></block></view><view class="dp-tabbar-text data-v-514cd9e3" style="{{'color:'+(item.$orig.color)+';'}}">{{item.$orig.text}}</view></view></block></block></view></view></block><block wx:if="{{$root.g2==3}}"><view class="dp-tabbar-module data-v-514cd9e3"><view class="dp-tabbar-cut data-v-514cd9e3" style="{{'background-color:'+(menudata.backgroundColor)+';'}}"></view><view class="dp-tabbar-sideL data-v-514cd9e3" style="{{'background-color:'+(menudata.backgroundColor)+';'}}"><block wx:for="{{$root.l3}}" wx:for-item="item" wx:for-index="index" wx:key="id"><block wx:if="{{index<1}}"><view class="dp-tabbar-item data-v-514cd9e3" data-url="{{item.$orig.pagePath}}" data-index="{{index}}" data-opentype="{{item.m3?'':opentype}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="dp-tabbar-image-box data-v-514cd9e3"><block wx:if="{{currentIndex===index}}"><image class="dp-tabbar-icon data-v-514cd9e3" src="{{item.$orig.selectedIconPath}}"></image></block><block wx:else><image class="dp-tabbar-icon data-v-514cd9e3" src="{{item.$orig.iconPath}}"></image></block></view><view class="dp-tabbar-text data-v-514cd9e3" style="{{'color:'+(item.$orig.color)+';'}}">{{item.$orig.text}}</view></view></block></block></view><block wx:for="{{$root.l4}}" wx:for-item="item" wx:for-index="index" wx:key="id"><block wx:if="{{index==1}}"><view class="dp-tabbar-center data-v-514cd9e3" style="{{('background-image: radial-gradient(circle at top, rgba(0,0,0,0) 55rpx, '+menudata.backgroundColor+' 55rpx);')}}" data-url="{{item.$orig.pagePath}}" data-index="{{index}}" data-opentype="{{item.m4?'':opentype}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image src="{{item.$orig.iconPath}}" class="data-v-514cd9e3"></image><view style="{{'color:'+(item.$orig.color)+';'}}" class="data-v-514cd9e3">{{item.$orig.text}}</view></view></block></block><view class="dp-tabbar-sideR data-v-514cd9e3" style="{{'background-color:'+(menudata.backgroundColor)+';'}}"><block wx:for="{{$root.l5}}" wx:for-item="item" wx:for-index="index" wx:key="id"><block wx:if="{{index>=2}}"><view class="dp-tabbar-item data-v-514cd9e3" data-url="{{item.$orig.pagePath}}" data-index="{{index}}" data-opentype="{{item.m5?'':opentype}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="dp-tabbar-image-box data-v-514cd9e3"><block wx:if="{{currentIndex===index}}"><image class="dp-tabbar-icon data-v-514cd9e3" src="{{item.$orig.selectedIconPath}}"></image></block><block wx:else><image class="dp-tabbar-icon data-v-514cd9e3" src="{{item.$orig.iconPath}}"></image></block></view><view class="dp-tabbar-text data-v-514cd9e3" style="{{'color:'+(item.$orig.color)+';'}}">{{item.$orig.text}}</view></view></block></block></view></view></block></view></block><block wx:else><view class="dp-tabbar data-v-514cd9e3"><view class="dp-tabbar-bot data-v-514cd9e3"></view><view class="dp-tabbar-bar data-v-514cd9e3" style="{{'background-color:'+(menudata.backgroundColor)+';'}}"><block wx:for="{{$root.l6}}" wx:for-item="item" wx:for-index="index" wx:key="id"><view class="dp-tabbar-item data-v-514cd9e3" data-url="{{item.$orig.pagePath}}" data-index="{{index}}" data-opentype="{{item.m6?'':opentype}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="dp-tabbar-image-box data-v-514cd9e3"><block wx:if="{{currentIndex===index}}"><image class="dp-tabbar-icon data-v-514cd9e3" src="{{item.$orig.selectedIconPath}}"></image></block><block wx:else><image class="dp-tabbar-icon data-v-514cd9e3" src="{{item.$orig.iconPath}}"></image></block></view><view class="dp-tabbar-text data-v-514cd9e3" style="{{'color:'+(item.$orig.color)+';'}}">{{item.$orig.text}}</view></view></block></view></view></block></view></block>