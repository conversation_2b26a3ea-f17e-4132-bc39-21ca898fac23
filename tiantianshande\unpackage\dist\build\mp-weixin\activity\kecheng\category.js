(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["activity/kecheng/category"],{"10fb":function(t,e,i){"use strict";i.d(e,"b",(function(){return a})),i.d(e,"c",(function(){return r})),i.d(e,"a",(function(){return n}));var n={nodata:function(){return i.e("components/nodata/nodata").then(i.bind(null,"101c"))},loading:function(){return i.e("components/loading/loading").then(i.bind(null,"ceaa"))},dpTabbar:function(){return i.e("components/dp-tabbar/dp-tabbar").then(i.bind(null,"b875"))},popmsg:function(){return i.e("components/popmsg/popmsg").then(i.bind(null,"2bf2"))}},a=function(){var t=this,e=t.$createElement,i=(t._self._c,t.isload?t.__map(t.data,(function(e,i){var n=t.__get_orig(e),a=t.__map(i===t.currentActiveIndex?t.clist:[],(function(e,i){var n=t.__get_orig(e),a=1!=e.ishide?t.courseList.length:null,r=1!=e.ishide&&a>0?t.__map(t.courseList,(function(e,i){var n=t.__get_orig(e),a=t.getJoinNum(e);return{$orig:n,m0:a}})):null;return{$orig:n,g0:a,l0:r}}));return{$orig:n,l1:a}})):null);t.$mp.data=Object.assign({},{$root:{l2:i}})},r=[]},"436b":function(t,e,i){"use strict";(function(t,e){var n=i("47a9");i("06e9");n(i("3240"));var a=n(i("9ef2"));t.__webpack_require_UNI_MP_PLUGIN__=i,e(a.default)}).call(this,i("3223")["default"],i("df3c")["createPage"])},"571f":function(t,e,i){"use strict";var n=i("eb29"),a=i.n(n);a.a},"9ef2":function(t,e,i){"use strict";i.r(e);var n=i("10fb"),a=i("dc03");for(var r in a)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(r);i("571f");var o=i("828b"),s=Object(o["a"])(a["default"],n["b"],n["c"],!1,null,null,null,!1,n["a"],void 0);e["default"]=s.exports},dc03:function(t,e,i){"use strict";i.r(e);var n=i("f0cb"),a=i.n(n);for(var r in n)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(r);e["default"]=a.a},eb29:function(t,e,i){},f0cb:function(t,e,i){"use strict";(function(t){var n=i("47a9");Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var a=n(i("7ca3"));function r(t,e){var i=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),i.push.apply(i,n)}return i}function o(t){for(var e=1;e<arguments.length;e++){var i=null!=arguments[e]?arguments[e]:{};e%2?r(Object(i),!0).forEach((function(e){(0,a.default)(t,e,i[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(i)):r(Object(i)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(i,e))}))}return t}var s=getApp(),c={data:function(){return{opt:{},loading:!1,isload:!1,menuindex:-1,data:[],currentActiveIndex:0,animation:!0,clist:[],bid:"",test:"",nodata:!1,courseList:[],pagenum:1,hasMore:!0,currentCid:"",scrollLeft:0,tabsData:{},swiperHeight:500}},onLoad:function(t){this.opt=s.getopts(t),this.bid=this.opt.bid?this.opt.bid:"",this.getdata()},onPullDownRefresh:function(){this.getdata()},onReady:function(){this.setContainerHeight()},onShow:function(){this.setContainerHeight()},methods:{getJoinNum:function(t){return t.join_num||t.join_nums||t.study_num||0},setContainerHeight:function(){var e=this,i=t.getSystemInfoSync(),n=i.windowHeight;t.createSelectorQuery().in(e).select(".search-container").boundingClientRect().select(".top-nav").boundingClientRect().exec((function(t){if(t&&t[0]&&t[1]){var i=t[0].height||94,a=t[1].height||80,r=n-i-a;e.menuindex>-1&&(r-=100),e.swiperHeight=r}}))},getdata:function(){var t=this;t.loading=!0,t.isload=!1,t.clist=[],t.courseList=[],t.nodata=!1,t.tabsData={},s.get("ApiKecheng/category5",{bid:t.bid},(function(e){if(t.data=e.data||[],t.data.length>0){var i=function(){if(a++,a===n){if((!t.clist||0===t.clist.length)&&t.courseList&&t.courseList.length>0){var e=t.data.find((function(e){return e.id===t.currentCid})),i=e?e.name:"课程";t.clist=[{name:i,id:"default_section_"+t.currentCid,ishide:0}]}t.clist&&0!==t.clist.length||t.courseList&&0!==t.courseList.length?t.nodata=!1:t.nodata=!0,t.loading=!1,t.isload=!0,t.tabsData[t.currentCid]={clist:JSON.parse(JSON.stringify(t.clist)),courseList:JSON.parse(JSON.stringify(t.courseList)),nodata:t.nodata},t.$nextTick((function(){t.setContainerHeight()}))}};t.currentActiveIndex=0,t.currentCid=t.data[0].id;var n=2,a=0;s.post("ApiKecheng/getdownclist3",{id:t.currentCid,bid:t.bid},(function(e){e&&e.data&&Array.isArray(e.data)?t.clist=e.data.map((function(t){return o(o({},t),{},{ishide:1===t.ishide?1:0})})):t.clist=[],i()})),s.post("ApiKecheng/getlist",{pagenum:1,keyword:"",field:"",order:"",cid:t.currentCid,cpid:0,bid:t.bid},(function(e){1==e.status&&e.data&&Array.isArray(e.data)?(t.courseList=e.data,t.pagenum=1,t.hasMore=e.data.length>0):(t.courseList=[],t.hasMore=!1),i()}))}else t.nodata=!0,t.loading=!1,t.isload=!0}))},loadTabData:function(t,e){var i=this;if(i.tabsData[t])return i.clist=i.tabsData[t].clist,i.courseList=i.tabsData[t].courseList,i.nodata=i.tabsData[t].nodata,void(i.loading=!1);i.loading=!0,i.clist=[],i.courseList=[],i.nodata=!1;var n=0;function a(){if(n++,2===n){if((!i.clist||0===i.clist.length)&&i.courseList&&i.courseList.length>0){var e=i.data.find((function(e){return e.id===t})),a=e?e.name:"课程";i.clist=[{name:a,id:"default_section_"+t,ishide:0}]}i.clist&&0!==i.clist.length||i.courseList&&0!==i.courseList.length?i.nodata=!1:i.nodata=!0,i.loading=!1,i.tabsData[t]={clist:JSON.parse(JSON.stringify(i.clist)),courseList:JSON.parse(JSON.stringify(i.courseList)),nodata:i.nodata}}}s.post("ApiKecheng/getdownclist3",{id:t,bid:i.bid},(function(t){t&&t.data&&Array.isArray(t.data)?i.clist=t.data.map((function(t){return o(o({},t),{},{ishide:1===t.ishide?1:0})})):i.clist=[],a()})),s.post("ApiKecheng/getlist",{pagenum:1,keyword:"",field:"",order:"",cid:t,cpid:0,bid:i.bid},(function(t){1==t.status&&t.data&&Array.isArray(t.data)?(i.courseList=t.data,i.pagenum=1,i.hasMore=t.data.length>0):(i.courseList=[],i.hasMore=!1),a()}))},onSwiperChange:function(t){var e=t.detail.current;this.currentActiveIndex=e,this.currentCid=this.data[e].id,this.updateNavPosition(e),this.loadTabData(this.currentCid,e)},onNavScroll:function(t){},updateNavPosition:function(e){var i=this,n=t.createSelectorQuery().in(i);n.select("#nav-item-"+e).boundingClientRect(),n.selectViewport().scrollOffset(),n.exec((function(e){if(e&&e[0]){var n=e[0].width||0,a=e[0].left||0,r=t.getSystemInfoSync().windowWidth;i.scrollLeft=a-r/2+n/2}}))},clickRootItem:function(t){var e=t.currentTarget.dataset;this.currentActiveIndex=e.rootItemIndex;var i=e.rootItemId;this.currentCid=i,this.loadTabData(i,e.rootItemIndex)},gotoCatproductPage:function(t){var e=t.currentTarget.dataset;s.goto("/activity/kecheng/list?cid="+e.id)},hideshow:function(t){var e=t.currentTarget.dataset.key;1==this.clist[e].ishide?this.clist[e].ishide=0:this.clist[e].ishide=1,this.test=Math.random(),this.tabsData[this.currentCid]&&(this.tabsData[this.currentCid].clist=JSON.parse(JSON.stringify(this.clist)))},getmenuindex:function(t){var e=this;this.menuindex=t,this.$nextTick((function(){e.setContainerHeight()}))},goto:function(t){var e=t.currentTarget.dataset.url;e&&s.goto(e)},loaded:function(){this.isload=!0}}};e.default=c}).call(this,i("df3c")["default"])}},[["436b","common/runtime","common/vendor"]]]);