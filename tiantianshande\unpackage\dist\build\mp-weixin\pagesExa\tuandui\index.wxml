<view><block wx:if="{{isload}}"><block><view class="container"><view class="header"><text class="title">团队业绩奖励</text></view><block wx:if="{{userRewardInfo&&userRewardInfo.activity_info}}"><view class="reward-info-box"><view class="section-title">我的奖励信息</view><view class="reward-card"><view class="activity-title">{{userRewardInfo.activity_info.title}}</view><block wx:if="{{userRewardInfo.activity_info.algorithm_type}}"><view class="algorithm-info"><text class="{{['algorithm-tag',userRewardInfo.activity_info.algorithm_type==='layered_reduction'?'algorithm-layered':'algorithm-standard']}}">{{''+(userRewardInfo.activity_info.algorithm_type==='layered_reduction'?'分层递减算法':'传统算法')+''}}</text></view></block><view class="performance-info"><view class="perf-type">{{"业绩类型："+(userRewardInfo.activity_info.performance_type==1?'团队金额':'团队数量')}}</view><view class="reward-type">{{"奖励类型："+(userRewardInfo.activity_info.reward_type==1?'比例奖励':'固定金额')}}</view></view><block wx:if="{{userRewardInfo.user_performance&&userRewardInfo.team_structure}}"><view class="team-performance"><view class="perf-value">{{"团队业绩："+userRewardInfo.user_performance.team_performance+(userRewardInfo.activity_info.performance_type==1?'元':'件')}}</view><view class="team-count">{{"团队成员："+userRewardInfo.team_structure.total_members+"人"}}</view></view></block><block wx:if="{{userRewardInfo.current_stage}}"><view class="stage-info"><view class="current-stage">{{"当前阶段："+userRewardInfo.current_stage.achievement+" ("+userRewardInfo.current_stage.reward_value+(userRewardInfo.activity_info.reward_type==1?'%':'元')+")"}}</view><block wx:if="{{userRewardInfo.next_stage}}"><view class="next-stage">{{"下一阶段："+userRewardInfo.next_stage.achievement+" ("+userRewardInfo.next_stage.reward_value+(userRewardInfo.activity_info.reward_type==1?'%':'元')+")"}}</view></block></view></block><block wx:if="{{userRewardInfo.user_performance}}"><view class="reward-summary"><view class="theoretical-reward">{{"理论奖励：¥"+userRewardInfo.user_performance.theoretical_reward}}</view><block wx:if="{{userRewardInfo.activity_info.algorithm_type==='layered_reduction'}}"><view class="actual-reward">{{"实际奖励：¥"+userRewardInfo.user_performance.actual_reward}}</view></block><block wx:if="{{userRewardInfo.user_performance.calculation_detail}}"><view class="calculation-detail">{{''+userRewardInfo.user_performance.calculation_detail+''}}</view></block></view></block></view></view></block><block wx:if="{{userRewardInfo&&userRewardInfo.reward_details&&userRewardInfo.activity_info}}"><view class="reward-details-box"><view class="section-title">奖励详情</view><view class="reward-details"><block wx:for="{{userRewardInfo.reward_details}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><view class="detail-item"><view class="detail-header"><view class="achievement-target">{{"目标业绩："+item.achievement_target}}</view><view class="reward-value">{{"奖励："+item.reward_value+(userRewardInfo.activity_info.reward_type==1?'%':'元')}}</view></view><view class="detail-content"><view class="reward-amount-info"><view class="theoretical-amount">{{"理论奖励：¥"+item.theoretical_reward_amount}}</view><block wx:if="{{userRewardInfo.activity_info.algorithm_type==='layered_reduction'&&item.actual_reward_amount!==item.theoretical_reward_amount}}"><view class="actual-amount">{{'实际奖励：¥'+item.actual_reward_amount+''}}</view></block></view><view class="status-info"><text class="{{['status',item.is_achieved?item.is_paid?'status-paid':item.is_claimed?'status-claimed':'status-achieved':'status-not-achieved']}}">{{item.is_achieved?item.is_paid?'已发放':item.is_claimed?'已领取':'已达成':'未达成'}}</text><block wx:if="{{item.is_achieved&&!item.is_claimed&&!item.is_paid}}"><view class="action-btn"><text class="claim-btn" data-activity="{{item.activity_id}}" data-level="{{item.achievement_level}}" data-event-opts="{{[['tap',[['claimReward',['$event']]]]]}}" bindtap="__e">立即领取</text></view></block></view></view><block wx:if="{{item.calculation}}"><view class="calculation">{{item.calculation}}</view></block></view></block></block></view></view></block><view class="activity-list-box"><view class="section-title">所有活动</view><view class="activity-list"><block wx:for="{{activityList}}" wx:for-item="item" wx:for-index="index" wx:key="id"><block><view class="activity-item" data-id="{{item.id}}" data-event-opts="{{[['tap',[['selectActivity',['$event']]]]]}}" bindtap="__e"><view class="activity-header"><text class="activity-title">{{item.title}}</text><text class="activity-time">{{item.createtime}}</text></view><view class="activity-info"><view class="type-info"><text class="perf-type">{{item.performance_name||(item.performance_type==1?'团队金额':'团队数量')}}</text><text class="reward-type">{{item.reward_name||(item.reward_type==1?'比例奖励':'固定金额')}}</text><text class="{{['algorithm-type',item.algorithm_type==='layered_reduction'?'algorithm-layered':'algorithm-standard']}}">{{''+(item.algorithm_name||(item.algorithm_type==='layered_reduction'?'分层递减算法':'传统算法'))+''}}</text></view><block wx:if="{{item.user_participated}}"><view class="participation-info"><text class="user-performance">{{"我的业绩："+item.user_performance}}</text><text class="user-reward">{{"我的奖励：¥"+item.user_reward}}</text></view></block></view><view class="activity-footer"><view class="activity-status"><text class="{{['status-text',item.status==1?'status-active':'status-ended']}}">{{item.status_text||(item.status==1?'进行中':'已结束')}}</text></view><view class="activity-actions"><text class="detail-btn" data-id="{{item.id}}" data-event-opts="{{[['tap',[['gotoDetail',['$event']]]]]}}" catchtap="__e">查看详情</text><block wx:if="{{selectedActivityId!=item.id}}"><text class="switch-btn" data-id="{{item.id}}" data-event-opts="{{[['tap',[['switchActivity',['$event']]]]]}}" catchtap="__e">切换</text></block><block wx:if="{{selectedActivityId==item.id}}"><text class="current-btn">当前</text></block></view></view></view></block></block></view></view><view class="records-entry"><view data-event-opts="{{[['tap',[['gotoRecords',['$event']]]]]}}" class="entry-btn" bindtap="__e"><text>查看奖励记录</text><image class="arrow-icon" src="/static/img/arrow-right.png"></image></view></view></view></block></block><block wx:if="{{loading}}"><loading vue-id="6473ef10-1" bind:__l="__l"></loading></block><dp-tabbar vue-id="6473ef10-2" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="6473ef10-3" data-ref="popmsg" bind:__l="__l"></popmsg></view>