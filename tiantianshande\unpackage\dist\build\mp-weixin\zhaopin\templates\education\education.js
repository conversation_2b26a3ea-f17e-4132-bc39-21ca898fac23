(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["zhaopin/templates/education/education"],{"0365":function(e,t,o){"use strict";o.d(t,"b",(function(){return n})),o.d(t,"c",(function(){return i})),o.d(t,"a",(function(){}));var n=function(){var e=this,t=e.$createElement,o=(e._self._c,e.partJobVo.commission_detail&&e.partJobVo.commission_detail.calculation?e.t("color1"):null),n=e.partJobVo.formatted_options?Object.values(e.partJobVo.formatted_options).flat():null,i=e.t("color1"),r=e.t("color2"),c=e.t("color2"),a=void 0!==e.partJobVo.gender_requirement?e.getGenderText(e.partJobVo.gender_requirement):null,u=void 0!==e.partJobVo.housing_provided?e.getHousingProvidedText(e.partJobVo.housing_provided):null,s=e.formatRichText(e.partJobVo.benefits||"暂无其他福利"),l=e.formatRichText(e.partJobVo.description||"暂无描述"),p=e.partJobVo.requirement?e.formatRichText(e.partJobVo.requirement):null,f=e.partJobVo.company_introduction&&1===e.partJobVo.company_show?e.formatRichText(e.partJobVo.company_introduction):null;e.$mp.data=Object.assign({},{$root:{m0:o,l0:n,m1:i,m2:r,m3:c,m4:a,m5:u,m6:s,m7:l,m8:p,m9:f}})},i=[]},"274f":function(e,t,o){"use strict";(function(e){Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o={props:{partJobVo:{type:Object,default:function(){return{title:"",salary:"",description:"",requirement:"",address:"",province:"",city:"",district:"",jobDesc:"",company:{name:"",logo:"",industry:"",scale:"",introduction:""},labelList:{descLabels:[]},requireList:[],work_mode:"",work_intensity:"",work_time_type:"",payment:"",education:"",experience:"",formatted_options:{},company_show:0}}},chosenList:{type:Array,default:function(){return[]}},tabCurrent:{type:Number,default:0},hasEyeAuth:{type:Boolean,default:!1},isShowAll:{type:Boolean,default:!1},agreementVo:{type:Object,default:function(){return{}}}},data:function(){return{isInfoShowBtn:!0,isComputedInfo:!1,isDescShowBtn:!0,isComputedDesc:!1,isReqShowBtn:!0,isComputedReq:!1,isCompanyShowBtn:!0,isComputedCompany:!1,healthVisible:!1,isBenefitsShowBtn:!0,isComputedBenefits:!1}},created:function(){var e=this;console.log("education组件初始化",this.partJobVo),this.$nextTick((function(){e.getInfoHeight(),e.getDescHeight(),e.getReqHeight(),e.getCompanyHeight(),e.getBenefitsHeight()}))},methods:{getGenderText:function(e){switch(e){case 0:return"不限";case 1:return"男";case 2:return"女";default:return"不限"}},getHousingProvidedText:function(e){return 1===e?"包住":2===e?"有偿住宿":"不包住"},jumpToCompany:function(){this.hasEyeAuth&&this.partJobVo.company&&this.partJobVo.company.id&&e.navigateTo({url:"/pagesExa/zhaopin/company?id="+this.partJobVo.company.id})},formatRichText:function(e){if(!e)return"";var t=e;try{t=t.replace(/&nbsp;/g," ").replace(/&lt;/g,"<").replace(/&gt;/g,">").replace(/&amp;/g,"&").replace(/&quot;/g,'"').replace(/&#39;/g,"'").replace(/&middot;/g,"·").replace(/&ldquo;/g,'"').replace(/&rdquo;/g,'"').replace(/&hellip;/g,"..."),t=t.replace(/\n/g,"<br>"),t=t.replace(/<p[^>]*>/g,"<p>").replace(/<\/p>/g,"</p><br>"),t=t.replace(/<ul[^>]*>/g,"<ul>").replace(/<ol[^>]*>/g,"<ol>").replace(/<li[^>]*>/g,"<li>"),t=t.replace(/<strong[^>]*>/g,"<b>").replace(/<\/strong>/g,"</b>").replace(/<em[^>]*>/g,"<i>").replace(/<\/em>/g,"</i>"),t=t.replace(/<a[^>]*href="([^"]*)"[^>]*>/g,'<a href="$1">').replace(/<\/a>/g,"</a>"),t=t.replace(/<([^>]+)>/g,(function(e,t){var o=t.split(" ")[0];return"<".concat(o,">")})),t=String(t).trim(),t=t.replace(/(<br>){3,}/g,"<br><br>")}catch(o){return console.error("格式化富文本出错:",o),e.replace(/<[^>]*>/g,"")}return t},getDescHeight:function(){var t=this;setTimeout((function(){var o=e.createSelectorQuery().in(t);o.select(".common-box:nth-child(4) .detail-info-text").boundingClientRect((function(e){e&&e.height?(t.isDescShowBtn=e.height>176,t.isComputedDesc=!0):(t.isDescShowBtn=!1,t.isComputedDesc=!0)})).exec()}),500)},getReqHeight:function(){var t=this;setTimeout((function(){var o=e.createSelectorQuery().in(t);o.select(".common-box:nth-child(6) .detail-info-text").boundingClientRect((function(e){e&&e.height?(t.isReqShowBtn=e.height>176,t.isComputedReq=!0):(t.isReqShowBtn=!1,t.isComputedReq=!0)})).exec()}),500)},getInfoHeight:function(){var t=this;setTimeout((function(){var o=e.createSelectorQuery().in(t);o.select(".common-box:nth-child(8) .detail-info-text").boundingClientRect((function(e){e&&e.height?(t.isInfoShowBtn=e.height>176,t.isComputedInfo=!0):(t.isInfoShowBtn=!1,t.isComputedInfo=!0)})).exec()}),500)},getCompanyHeight:function(){var t=this;this.partJobVo.company&&this.partJobVo.company.introduction&&setTimeout((function(){var o=e.createSelectorQuery().in(t);o.select(".common-box:nth-child(12) .detail-info-text").boundingClientRect((function(e){e&&e.height?(t.isCompanyShowBtn=e.height>176,t.isComputedCompany=!0):(t.isCompanyShowBtn=!1,t.isComputedCompany=!0)})).exec()}),500)},getBenefitsHeight:function(){var t=this;setTimeout((function(){var o=e.createSelectorQuery().in(t);o.select(".common-box:nth-child(10) .detail-info-text").boundingClientRect((function(e){e&&e.height?(t.isBenefitsShowBtn=e.height>176,t.isComputedBenefits=!0):(t.isBenefitsShowBtn=!1,t.isComputedBenefits=!0)})).exec()}),500)},descBtnTap:function(){this.isDescShowBtn=!1},reqBtnTap:function(){this.isReqShowBtn=!1},infoBtnTap:function(){this.isInfoShowBtn=!1},companyBtnTap:function(){this.isCompanyShowBtn=!1},benefitsBtnTap:function(){this.isBenefitsShowBtn=!1}}};t.default=o}).call(this,o("df3c")["default"])},"2b53":function(e,t,o){"use strict";o.r(t);var n=o("274f"),i=o.n(n);for(var r in n)["default"].indexOf(r)<0&&function(e){o.d(t,e,(function(){return n[e]}))}(r);t["default"]=i.a},"3d11":function(e,t,o){"use strict";o.r(t);var n=o("0365"),i=o("2b53");for(var r in i)["default"].indexOf(r)<0&&function(e){o.d(t,e,(function(){return i[e]}))}(r);o("4bcf6");var c=o("828b"),a=Object(c["a"])(i["default"],n["b"],n["c"],!1,null,null,null,!1,n["a"],void 0);t["default"]=a.exports},"4bcf6":function(e,t,o){"use strict";var n=o("b503"),i=o.n(n);i.a},b503:function(e,t,o){}}]);
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'zhaopin/templates/education/education-create-component',
    {
        'zhaopin/templates/education/education-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('df3c')['createComponent'](__webpack_require__("3d11"))
        })
    },
    [['zhaopin/templates/education/education-create-component']]
]);
