<view class="container"><block wx:if="{{isload}}"><block><view class="ordertop" style="{{('background:url('+pre_url+'/static/img/ordertop.png);background-size:100%')}}"><block wx:if="{{detail.status==0}}"><view class="f1"><view class="t1">等待买家付款</view><block wx:if="{{djs}}"><view class="t2">{{"剩余时间："+djs}}</view></block></view></block><block wx:if="{{detail.status==1}}"><view class="f1"><view class="t1">{{detail.paytypeid==4?'已选择'+detail.paytype:'已成功付款'}}</view><view class="t2">请尽快接单</view></view></block><block wx:if="{{detail.status==12}}"><view class="f1"><view class="t1">{{detail.paytypeid==4?'已选择'+detail.paytype:'已成功付款'}}</view><block wx:if="{{detail.freight_type!=1}}"><view class="t2">请尽快发货</view></block><block wx:if="{{detail.freight_type==1}}"><view class="t2">待提货</view></block></view></block><block wx:if="{{detail.status==2}}"><view class="f1"><view class="t1">订单已发货</view><block wx:if="{{detail.freight_type!=3&&detail.freight_type!=2}}"><view class="t2">{{"发货信息："+detail.express+" "+detail.express_no}}</view></block></view></block><block wx:if="{{detail.status==3}}"><view class="f1"><view class="t1">订单已完成</view></view></block><block wx:if="{{detail.status==4}}"><view class="f1"><view class="t1">订单已取消</view></view></block></view><view class="address"><view class="img"><image src="{{pre_url+'/static/img/address3.png'}}"></image></view><view class="info"><text class="t1">{{detail.linkman+" "+detail.tel}}</text><block wx:if="{{detail.freight_type!=1&&detail.freight_type!=3}}"><text class="t2">{{"地址："+detail.area+detail.address}}</text></block><block wx:if="{{detail.freight_type==1}}"><text class="t2" data-address="{{storeinfo.address}}" data-latitude="{{storeinfo.latitude}}" data-longitude="{{storeinfo.longitude}}" data-event-opts="{{[['tap',[['openLocation',['$event']]]]]}}" bindtap="__e">{{"取货地点："+storeinfo.name+" - "+storeinfo.address}}</text></block></view></view><view class="product"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="idx" wx:key="idx"><view class="content"><view><image src="{{item.$orig.pic}}"></image></view><view class="detail"><text class="t1">{{item.$orig.name}}</text><text class="t2">{{item.$orig.ggname+(item.$orig.jltitle?item.$orig.jltitle:'')}}</text><view class="t3"><block wx:if="{{item.$orig.jlprice}}"><text class="x1 flex1">{{"￥"+item.g0}}</text></block><block wx:else><text class="x1 flex1">{{"￥"+item.$orig.sell_price}}</text></block><block wx:if="{{goods_hexiao_status&&(detail.status==1||detail.status==2||detail.status==12)&&detail.freight_type==1&&item.$orig.hexiao_code&&item.$orig.num>0}}"><block><view><block wx:if="{{item.$orig.status==3}}"><view style="color:#999;">已核销</view></block><block wx:else><view style="color:#f60;">未核销</view></block></view></block></block><text class="x2">{{"×"+item.$orig.num}}</text></view></view></view></block></view><block wx:if="{{(detail.status==3||detail.status==2)&&(detail.freight_type==3||detail.freight_type==4)}}"><view class="orderinfo"><view class="item flex-col"><text class="t1" style="color:#111;">发货信息</text><text class="t2" style="text-align:left;margin-top:10rpx;padding:0 10rpx;" user-select="true" selectable="true">{{detail.freight_content}}</text></view></view></block><view class="orderinfo"><view class="item"><text class="t1">下单人</text><text class="flex1"></text><image style="width:80rpx;height:80rpx;margin-right:8rpx;" src="{{detail.headimg}}"></image><text style="height:80rpx;line-height:80rpx;">{{detail.nickname}}</text></view><view class="item"><text class="t1">{{$root.m0+"ID"}}</text><text class="t2">{{detail.mid}}</text></view></view><block wx:if="{{detail.remark}}"><view class="orderinfo"><view class="item"><text class="t1">备注</text><text class="t2">{{detail.remark}}</text></view></view></block><view class="orderinfo"><view class="item"><text class="t1">订单编号</text><text class="t2" user-select="true" selectable="true">{{detail.ordernum}}</text></view><view class="item"><text class="t1">下单时间</text><text class="t2">{{detail.createtime}}</text></view><block wx:if="{{detail.status>0&&detail.paytypeid!='4'&&detail.paytime}}"><view class="item"><text class="t1">支付时间</text><text class="t2">{{detail.paytime}}</text></view></block><block wx:if="{{detail.status>0&&detail.paytime}}"><view class="item"><text class="t1">支付方式</text><text class="t2">{{detail.paytype}}</text></view></block><block wx:if="{{detail.status>1&&detail.send_time}}"><view class="item"><text class="t1">发货时间</text><text class="t2">{{detail.send_time}}</text></view></block><block wx:if="{{detail.status==3&&detail.collect_time}}"><view class="item"><text class="t1">收货时间</text><text class="t2">{{detail.collect_time}}</text></view></block></view><view class="orderinfo"><view class="item"><text class="t1">商品金额</text><text class="t2 red">{{"¥"+detail.product_price}}</text></view><block wx:if="{{detail.leveldk_money>0}}"><view class="item"><text class="t1">{{$root.m1+"折扣"}}</text><text class="t2 red">{{"-¥"+detail.leveldk_money}}</text></view></block><block wx:if="{{detail.jianmoney>0}}"><view class="item"><text class="t1">满减活动</text><text class="t2 red">{{"-¥"+detail.manjian_money}}</text></view></block><view class="item"><text class="t1">配送方式</text><text class="t2">{{detail.freight_text}}</text></view><block wx:if="{{detail.freight_type==1&&detail.freightprice>0}}"><view class="item"><text class="t1">服务费</text><text class="t2 red">{{"+¥"+detail.freight_price}}</text></view></block><block wx:if="{{detail.freight_time}}"><view class="item"><text class="t1">{{(detail.freight_type!=1?'配送':'提货')+"时间"}}</text><text class="t2">{{detail.freight_time}}</text></view></block><block wx:if="{{detail.couponmoney>0}}"><view class="item"><text class="t1">{{$root.m2+"抵扣"}}</text><text class="t2 red">{{"-¥"+detail.coupon_money}}</text></view></block><block wx:if="{{detail.scoredk>0}}"><view class="item"><text class="t1">{{$root.m3+"抵扣"}}</text><text class="t2 red">{{"-¥"+detail.scoredk_money}}</text></view></block><view class="item"><text class="t1">{{(detail.status==0?'应付':'实付')+"款"}}</text><text class="t2 red">{{"¥"+detail.totalprice}}</text></view><view class="item"><text class="t1">订单状态</text><block wx:if="{{detail.status==0}}"><text class="t2">未付款</text></block><block wx:if="{{detail.status==1}}"><text class="t2">已付款</text></block><block wx:if="{{detail.status==2}}"><text class="t2">已发货</text></block><block wx:if="{{detail.status==3}}"><text class="t2">已收货</text></block><block wx:if="{{detail.status==4}}"><text class="t2">已关闭</text></block></view><block wx:if="{{detail.refund_status>0}}"><view class="item"><text class="t1">退款状态</text><block wx:if="{{detail.refund_status==1}}"><text class="t2 red">{{"审核中,¥"+detail.refund_money}}</text></block><block wx:if="{{detail.refund_status==2}}"><text class="t2 red">{{"已退款,¥"+detail.refund_money}}</text></block><block wx:if="{{detail.refund_status==3}}"><text class="t2 red">{{"已驳回,¥"+detail.refund_money}}</text></block></view></block><block wx:if="{{detail.refund_status>0}}"><view class="item"><text class="t1">退款原因</text><text class="t2 red">{{detail.refund_reason}}</text></view></block><block wx:if="{{detail.refund_checkremark}}"><view class="item"><text class="t1">审核备注</text><text class="t2 red">{{detail.refund_checkremark}}</text></view></block><view class="item"><text class="t1">备注</text><text class="t2 red">{{detail.message?detail.message:'无'}}</text></view><block wx:if="{{detail.field1}}"><view class="item"><text class="t1">{{detail.field1data[0]}}</text><text class="t2 red">{{detail.field1data[1]}}</text></view></block><block wx:if="{{detail.field2}}"><view class="item"><text class="t1">{{detail.field2data[0]}}</text><text class="t2 red">{{detail.field2data[1]}}</text></view></block><block wx:if="{{detail.field3}}"><view class="item"><text class="t1">{{detail.field3data[0]}}</text><text class="t2 red">{{detail.field3data[1]}}</text></view></block><block wx:if="{{detail.field4}}"><view class="item"><text class="t1">{{detail.field4data[0]}}</text><text class="t2 red">{{detail.field4data[1]}}</text></view></block><block wx:if="{{detail.field5}}"><view class="item"><text class="t1">{{detail.field5data[0]}}</text><text class="t2 red">{{detail.field5data[1]}}</text></view></block><block wx:if="{{(detail.status==1||detail.status==12||detail.status==2)&&detail.freight_type==1}}"><view class="item flex-col"><text class="t1">核销码</text><view class="flex-x-center"><image style="width:400rpx;height:400rpx;" src="{{detail.hexiao_qr}}" data-url="{{detail.hexiao_qr}}" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image></view></view></block></view><view style="width:100%;height:120rpx;"></view><view class="bottom"><block wx:if="{{detail.refund_status==1}}"><view class="btn2" data-id="{{detail.id}}" data-event-opts="{{[['tap',[['refundnopass',['$event']]]]]}}" bindtap="__e">退款驳回</view></block><block wx:if="{{detail.refund_status==1}}"><view class="btn2" data-id="{{detail.id}}" data-event-opts="{{[['tap',[['refundpass',['$event']]]]]}}" bindtap="__e">退款通过</view></block><block wx:if="{{detail.status==0}}"><view class="btn2" data-id="{{detail.id}}" data-event-opts="{{[['tap',[['closeOrder',['$event']]]]]}}" bindtap="__e">关闭订单</view></block><block wx:if="{{detail.status==0&&detail.bid==0}}"><view class="btn2" data-id="{{detail.id}}" data-event-opts="{{[['tap',[['ispay',['$event']]]]]}}" bindtap="__e">改为已支付</view></block><block wx:if="{{detail.status==1}}"><view class="btn2" data-id="{{detail.id}}" data-event-opts="{{[['tap',[['refund',['$event']]]]]}}" bindtap="__e">拒单退款</view></block><block wx:if="{{detail.status==1}}"><view class="btn2" data-id="{{detail.id}}" data-event-opts="{{[['tap',[['jiedan',['$event']]]]]}}" bindtap="__e">接单</view></block><block wx:if="{{detail.status==12}}"><view class="btn2" data-id="{{detail.id}}" data-event-opts="{{[['tap',[['fahuo',['$event']]]]]}}" bindtap="__e">发货</view></block><block wx:if="{{detail.status==12&&detail.canpeisong}}"><block><block wx:if="{{detail.express_wx_status}}"><view class="btn2" data-id="{{detail.id}}" data-event-opts="{{[['tap',[['peisongWx',['$event']]]]]}}" bindtap="__e">即时配送</view></block><block wx:else><block wx:if="{{detail.myt_status}}"><view class="btn2" data-id="{{detail.id}}" data-event-opts="{{[['tap',[['peisongMyt',['$event']]]]]}}" bindtap="__e">麦芽田配送</view></block><block wx:else><view class="btn2" data-id="{{detail.id}}" data-event-opts="{{[['tap',[['peisong',['$event']]]]]}}" bindtap="__e">配送</view></block></block></block></block><block wx:if="{{detail.status==2||detail.status==3}}"><view class="btn2" data-url="{{'/pagesExt/order/logistics?express_com='+detail.express_com+'&express_no='+detail.express_no+'&type='+detail.express_type}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">查物流</view></block><block wx:if="{{detail.status==4}}"><view class="btn2" data-id="{{detail.id}}" data-event-opts="{{[['tap',[['delOrder',['$event']]]]]}}" bindtap="__e">删除</view></block><view class="btn2" data-id="{{detail.id}}" data-event-opts="{{[['tap',[['setremark',['$event']]]]]}}" bindtap="__e">设置备注</view><view class="btn2" data-id="{{detail.id}}" data-event-opts="{{[['tap',[['print',['$event']]]]]}}" bindtap="__e">打印小票</view><block wx:if="{{shopset.is_refund&&(detail.status==1||detail.status==2||detail.status==3||detail.status==12)}}"><view class="btn2" data-id="{{detail.id}}" data-event-opts="{{[['tap',[['refundinit',['$event']]]]]}}" bindtap="__e">退款</view></block></view><uni-popup class="vue-ref" vue-id="46672bef-1" id="dialogSetremark" type="dialog" data-ref="dialogSetremark" bind:__l="__l" vue-slots="{{['default']}}"><uni-popup-dialog vue-id="{{('46672bef-2')+','+('46672bef-1')}}" mode="input" title="设置备注" value="{{detail.remark}}" placeholder="请输入备注" data-event-opts="{{[['^confirm',[['setremarkconfirm']]]]}}" bind:confirm="__e" bind:__l="__l"></uni-popup-dialog></uni-popup><uni-popup class="vue-ref" vue-id="46672bef-3" id="dialogExpress" type="dialog" data-ref="dialogExpress" bind:__l="__l" vue-slots="{{['default']}}"><view class="uni-popup-dialog"><view class="uni-dialog-title"><text class="uni-dialog-title-text">发货</text></view><view class="uni-dialog-content"><view><view class="flex-y-center flex-x-center" style="margin:20rpx 20rpx;"><text style="font-size:28rpx;color:#000;">快递公司：</text><picker style="font-size:28rpx;border:1px #eee solid;padding:10rpx;height:70rpx;border-radius:4px;flex:1;" value="{{express_index}}" range="{{expressdata}}" data-event-opts="{{[['change',[['expresschange',['$event']]]]]}}" bindchange="__e"><view class="picker">{{expressdata[express_index]}}</view></picker></view><view class="flex-y-center flex-x-center" style="margin:20rpx 20rpx;"><view style="font-size:28rpx;color:#555;">快递单号：</view><input style="border:1px #eee solid;padding:10rpx;height:70rpx;border-radius:4px;flex:1;font-size:28rpx;" type="text" placeholder="请输入快递单号" data-event-opts="{{[['input',[['setexpressno',['$event']]]]]}}" bindinput="__e"/></view></view></view><view class="uni-dialog-button-group"><view data-event-opts="{{[['tap',[['dialogExpressClose',['$event']]]]]}}" class="uni-dialog-button" bindtap="__e"><text class="uni-dialog-button-text">取消</text></view><view data-event-opts="{{[['tap',[['confirmfahuo',['$event']]]]]}}" class="uni-dialog-button uni-border-left" bindtap="__e"><text class="uni-dialog-button-text uni-button-color">确定</text></view></view></view></uni-popup><uni-popup class="vue-ref" vue-id="46672bef-4" id="dialogPeisong" type="dialog" data-ref="dialogPeisong" bind:__l="__l" vue-slots="{{['default']}}"><view class="uni-popup-dialog"><view class="uni-dialog-title"><text class="uni-dialog-title-text">请选择配送员</text></view><view class="uni-dialog-content"><view><picker style="font-size:28rpx;border:1px #eee solid;padding:10rpx;height:70rpx;border-radius:4px;flex:1;" value="{{index2}}" range="{{peisonguser2}}" data-event-opts="{{[['change',[['peisongChange',['$event']]]]]}}" bindchange="__e"><view class="picker">{{peisonguser2[index2]}}</view></picker></view></view><view class="uni-dialog-button-group"><view data-event-opts="{{[['tap',[['dialogPeisongClose',['$event']]]]]}}" class="uni-dialog-button" bindtap="__e"><text class="uni-dialog-button-text">取消</text></view><view data-event-opts="{{[['tap',[['confirmPeisong',['$event']]]]]}}" class="uni-dialog-button uni-border-left" bindtap="__e"><text class="uni-dialog-button-text uni-button-color">确定</text></view></view></view></uni-popup><uni-popup class="vue-ref" vue-id="46672bef-5" id="dialogExpress11" type="dialog" data-ref="dialogExpress11" bind:__l="__l" vue-slots="{{['default']}}"><view class="uni-popup-dialog"><view class="uni-dialog-title"><text class="uni-dialog-title-text">配送设置</text></view><view class="uni-dialog-content" style="display:block;"><block wx:if="{{mytdata}}"><block><block wx:if="{{!mytdata.msg}}"><block><scroll-view style="width:100%;max-height:500rpx;" scroll-Y="true"><block wx:for="{{mytdata.detail}}" wx:for-item="item" wx:for-index="__i0__" wx:key="*this"><block><view class="flex-y-center flex-x-center" style="margin:20rpx 20rpx;"><view style="font-size:28rpx;color:#555;width:130rpx;">{{item.name+"："}}</view><view style="padding:10rpx;flex:1;font-size:28rpx;line-height:40rpx;"><block wx:if="{{!item.error_message}}"><block><view>{{"配送费："+item.amount+"元"}}</view><view>{{"距离："+item.distance+"米"}}</view></block></block><block wx:else><block><view>{{"计价失败原因："+item.error_message}}</view></block></block></view></view></block></block></scroll-view><block wx:if="{{detail.myt_shop}}"><view class="flex-y-center flex-x-center" style="margin:20rpx 20rpx;"><view style="font-size:28rpx;color:#555;width:130rpx;">门店：</view><picker style="font-size:28rpx;border:1px #eee solid;padding:0 10rpx;height:70rpx;border-radius:4px;flex:1;line-height:52rpx;" value="{{mytindex}}" range="{{detail.myt_shoplist}}" range-key="name" data-event-opts="{{[['change',[['mytshopChange',['$event']]]]]}}" bindchange="__e"><view class="picker">{{detail.myt_shoplist[mytindex]['name']}}</view></picker></view></block><view class="flex-y-center flex-x-center" style="margin:20rpx 20rpx;"><view style="font-size:28rpx;color:#555;width:130rpx;">重量：</view><input style="border:1px #eee solid;padding:0 10rpx;height:70rpx;border-radius:4px;flex:1;font-size:28rpx;" type="text" placeholder="请输入重量(选填)" data-event-opts="{{[['input',[['mytWeight',['$event']]]]]}}" value="{{myt_weight}}" bindinput="__e"/>kg</view><view class="flex-y-center flex-x-center" style="margin:20rpx 20rpx;"><view style="font-size:28rpx;color:#555;width:130rpx;">备注：</view><input style="border:1px #eee solid;padding:0 10rpx;height:70rpx;border-radius:4px;flex:1;font-size:28rpx;" type="text" placeholder="请输入备注(选填)" data-event-opts="{{[['input',[['mytRemark',['$event']]]]]}}" bindinput="__e"/></view></block></block><block wx:else><block><view class="flex-y-center flex-x-center" style="margin:20rpx 20rpx;"><view style="font-size:28rpx;color:#555;">错误信息：</view><view style="padding:10rpx;flex:1;font-size:28rpx;line-height:40rpx;">{{mytdata.msg}}</view></view></block></block></block></block><block wx:else><block><view class="flex-y-center flex-x-center" style="margin:20rpx 20rpx;"><view style="font-size:28rpx;color:#555;">错误：</view><view style="padding:10rpx;height:70rpx;flex:1;font-size:28rpx;">无数据返回</view></view></block></block></view><view class="uni-dialog-button-group"><view data-event-opts="{{[['tap',[['dialogExpress11Close',['$event']]]]]}}" class="uni-dialog-button" bindtap="__e"><text class="uni-dialog-button-text">取消</text></view><view data-event-opts="{{[['tap',[['confirmfahuo11',['$event']]]]]}}" class="uni-dialog-button uni-border-left" bindtap="__e"><text class="uni-dialog-button-text uni-button-color">确定</text></view></view></view></uni-popup><uni-popup class="vue-ref" vue-id="46672bef-6" id="dialogRefund" type="dialog" mask-click="{{false}}" data-ref="dialogRefund" bind:__l="__l" vue-slots="{{['default']}}"><view class="uni-popup-dialog"><view class="uni-dialog-title"><text class="uni-dialog-title-text">退款</text></view><view class="uni-dialog-content"><view><view class="product" style="width:100%;margin:0;padding:0;"><scroll-view class="popup-content" style="max-height:600rpx;overflow:hidden;" scroll-y="true"><block wx:for="{{returnProlist}}" wx:for-item="item" wx:for-index="idx" wx:key="idx"><view class="box"><view class="content"><view data-url="{{'/pages/shop/product?id='+item.proid}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image style="width:110rpx;height:110rpx;" src="{{item.pic}}"></image></view><view class="detail"><text class="t1">{{item.name}}</text><text class="t2">{{item.ggname}}</text><view class="t3"><text class="x1 flex1">{{"￥"+item.sell_price+"×"+item.num}}</text><view style="color:#888;font-size:24rpx;display:flex;"><text>退货数量</text><input class="retundNum" style="border:1px #eee solid;width:80rpx;margin-left:10rpx;text-align:center;" type="number" data-max="{{item.can_num}}" data-ogid="{{item.id}}" data-event-opts="{{[['input',[['retundInput',['$event']]]]]}}" value="{{item.can_num}}" bindinput="__e"/></view></view></view></view></view></block></scroll-view></view><view class="flex-y-center flex-x-center" style="margin:20rpx 20rpx;height:80rpx;"><view style="font-size:28rpx;color:#555;">退款原因：</view><input style="border:1px #eee solid;height:70rpx;border-radius:4px;flex:1;font-size:28rpx;padding:0 10rpx;" type="text" placeholder="请输入退款原因" adjust-position="false" data-event-opts="{{[['input',[['refundMoneyReason',['$event']]]]]}}" bindinput="__e"/></view><view class="flex-y-center flex-x-center" style="margin:20rpx 20rpx;height:80rpx;"><view style="font-size:28rpx;color:#555;">退款金额：</view><input style="border:1px #eee solid;height:70rpx;border-radius:4px;flex:1;font-size:28rpx;padding:0 10rpx;" type="text" placeholder="请输入退款金额" adjust-position="false" data-event-opts="{{[['input',[['refundMoney',['$event']]]]]}}" value="{{refundTotalprice}}" bindinput="__e"/></view></view></view><view class="uni-dialog-button-group"><view data-event-opts="{{[['tap',[['dialogRefundClose',['$event']]]]]}}" class="uni-dialog-button" bindtap="__e"><text class="uni-dialog-button-text">取消</text></view><view data-event-opts="{{[['tap',[['gotoRefundMoney']]]]}}" class="uni-dialog-button uni-border-left" bindtap="__e"><text class="uni-dialog-button-text uni-button-color">确定</text></view></view></view></uni-popup></block></block><block wx:if="{{loading}}"><loading vue-id="46672bef-7" bind:__l="__l"></loading></block><dp-tabbar vue-id="46672bef-8" opt="{{opt}}" bind:__l="__l"></dp-tabbar></view>