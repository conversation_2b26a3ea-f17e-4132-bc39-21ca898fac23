<view class="container"><canvas class="particles-canvas" style="{{'width:'+(canvasWidth+'px')+';'+('height:'+(canvasHeight+'px')+';')}}" canvas-id="particlesCanvas"></canvas><view class="bg-grid"></view><view class="bg-circles"></view><view class="voice-chat-console"><view class="header-section"><view class="connection-status"><view class="{{['status-indicator',(isConnected)?'connected':'']}}"><view class="status-dot"></view><text class="status-text">{{connectionStatus}}</text></view></view><view class="title-info"><text class="main-title" style="{{'color:'+($root.m0)+';'}}">时空语音通话</text><text class="subtitle">与20年后的自己对话</text></view><view class="time-display"><text class="year">2049</text><text class="date">{{currentDate}}</text></view></view><view class="hologram-section"><view class="hologram-container"><view class="{{['hologram-avatar',(isAISpeaking)?'speaking':'']}}"><block wx:if="{{futureAvatarUrl}}"><image class="avatar-image" src="{{futureAvatarUrl}}" mode="aspectFit"></image></block><block wx:else><view class="default-avatar"><text class="avatar-icon">🤖</text></view></block><block wx:if="{{isAISpeaking}}"><view class="voice-waves"><view class="wave wave-1"></view><view class="wave wave-2"></view><view class="wave wave-3"></view></view></block></view><view class="avatar-info"><text class="avatar-name" style="{{'color:'+($root.m1)+';'}}">{{userName+" (2049年)"}}</text><text class="avatar-status">{{avatarStatus}}</text></view></view></view><view class="dialogue-history"><view class="history-header"><text class="header-icon">💬</text><text class="header-text">对话记录</text><view data-event-opts="{{[['tap',[['clearHistory',['$event']]]]]}}" class="clear-btn" bindtap="__e"><text class="clear-icon">🗑️</text></view></view><scroll-view class="messages-list" scroll-y="true" scroll-top="{{scrollTop}}"><block wx:for="{{messages}}" wx:for-item="message" wx:for-index="index" wx:key="index"><view class="{{['message-item',message.type==='user'?'user-message':'ai-message']}}"><view class="message-time"><text>{{message.time}}</text></view><view class="message-content"><text class="message-text">{{message.text}}</text><block wx:if="{{message.type==='ai'}}"><view data-event-opts="{{[['tap',[['replayMessage',[index]]]]]}}" class="replay-btn" bindtap="__e"><text class="replay-icon">🔊</text></view></block></view></view></block></scroll-view></view><view class="voice-controls"><view class="voice-input-section"><view data-event-opts="{{[['touchstart',[['startRecording',['$event']]]],['touchend',[['stopRecording',['$event']]]]]}}" class="{{['voice-btn',(isRecording)?'recording':'',(!isConnected)?'disabled':'']}}" bindtouchstart="__e" bindtouchend="__e"><view class="{{['btn-glow',(isRecording)?'active':'']}}"></view><text class="voice-icon">{{isRecording?'🎙️':'🎤'}}</text><text class="voice-text">{{voiceButtonText}}</text><block wx:if="{{isRecording}}"><view class="recording-waves"><view class="recording-wave recording-wave-1"></view><view class="recording-wave recording-wave-2"></view><view class="recording-wave recording-wave-3"></view></view></block></view><view class="voice-status"><text class="status-text">{{voiceStatus}}</text></view></view><view class="control-buttons"><view data-event-opts="{{[['tap',[['toggleMute',['$event']]]]]}}" class="control-btn" bindtap="__e"><text class="btn-icon">{{isMuted?'🔇':'🔊'}}</text><text class="btn-text">{{isMuted?'取消静音':'静音'}}</text></view><view data-event-opts="{{[['tap',[['showSettings',['$event']]]]]}}" class="control-btn" bindtap="__e"><text class="btn-icon">⚙</text><text class="btn-text">设置</text></view><view data-event-opts="{{[['tap',[['endCall',['$event']]]]]}}" class="control-btn" bindtap="__e"><text class="btn-icon">📞</text><text class="btn-text">结束通话</text></view></view></view></view><block wx:if="{{showSettingsModal}}"><view data-event-opts="{{[['tap',[['hideSettings',['$event']]]]]}}" class="modal" bindtap="__e"><view data-event-opts="{{[['tap',[['',['$event']]]]]}}" class="modal-content" catchtap="__e"><view class="modal-header"><text class="modal-title">⚙ 语音设置</text><view data-event-opts="{{[['tap',[['hideSettings',['$event']]]]]}}" class="modal-close" bindtap="__e"><text class="close-icon">×</text></view></view><view class="modal-body"><view class="setting-item"><text class="setting-label">语言设置:</text><picker value="{{languageIndex}}" range="{{languageOptions}}" data-event-opts="{{[['change',[['onLanguageChange',['$event']]]]]}}" bindchange="__e"><view class="picker-input"><text>{{languageOptions[languageIndex]}}</text><text class="picker-arrow">▼</text></view></picker></view><view class="setting-item"><text class="setting-label">语速设置:</text><slider value="{{speechSpeed}}" min="0.5" max="2" step="0.1" activeColor="#00f7ff" backgroundColor="rgba(255,255,255,0.3)" data-event-opts="{{[['change',[['onSpeedChange',['$event']]]]]}}" bindchange="__e"></slider><text class="speed-value">{{speechSpeed+"x"}}</text></view><view class="setting-item"><text class="setting-label">音效开关:</text><switch checked="{{soundEffectEnabled}}" color="#00f7ff" data-event-opts="{{[['change',[['onSoundEffectChange',['$event']]]]]}}" bindchange="__e"></switch></view></view><view class="modal-footer"><view data-event-opts="{{[['tap',[['saveSettings',['$event']]]]]}}" class="modal-btn" bindtap="__e"><text class="btn-icon">💾</text><text class="btn-text">保存设置</text></view></view></view></view></block><view class="footer"><text class="footer-text">时空语音通话系统 v2049.1 | 量子通信加密 | 与未来自己的深度对话</text></view></view>