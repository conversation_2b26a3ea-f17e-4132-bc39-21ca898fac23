require('../common/vendor.js');(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["adminExt/restaurant/shoporderEdit"],{2988:function(t,e,o){"use strict";o.r(e);var n=o("8b90"),i=o.n(n);for(var r in n)["default"].indexOf(r)<0&&function(t){o.d(e,t,(function(){return n[t]}))}(r);e["default"]=i.a},3175:function(t,e,o){"use strict";o.r(e);var n=o("a291"),i=o("2988");for(var r in i)["default"].indexOf(r)<0&&function(t){o.d(e,t,(function(){return i[t]}))}(r);o("51ce");var a=o("828b"),d=Object(a["a"])(i["default"],n["b"],n["c"],!1,null,null,null,!1,n["a"],void 0);e["default"]=d.exports},"51ce":function(t,e,o){"use strict";var n=o("6b39"),i=o.n(n);i.a},"6b39":function(t,e,o){},"8b90":function(t,e,o){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n=getApp(),i={data:function(){return{isload:!1,loading:!1,pre_url:n.globalData.pre_url,info:{},ordergoods:[]}},onLoad:function(t){this.opt=n.getopts(t),this.getdata()},methods:{getdata:function(){var t=this,e=t.opt.id?t.opt.id:"";t.loading=!0,n.get("ApiAdminRestaurantShopOrder/edit",{id:e},(function(e){t.loading=!1,t.info=e.info,t.ordergoods=e.order_goods,t.loaded()}))},ginput:function(t){var e=t.detail.value,o=t.currentTarget.dataset.index,i=t.currentTarget.dataset.field,r=this.ordergoods,a=0;if(e<0||""==e)n.error("请输入正确的数值");else{for(var d in r[o][i]=e,r)r[d].type&&1==r[d].type?a+=r[d].weigh*r[d].sell_price:a+=r[d].num*r[d].sell_price;this.info.product_price=a;var u=a-this.info.leveldk_money-this.info.coupon_money-this.info.scoredk_money;u=parseFloat(u),u=u.toFixed(2),u<0?n.error("总金额不能小于0"):this.info.totalprice=u}},input:function(t){var e=t.detail.value,o=t.currentTarget.dataset.field;if(e<0||""==e)n.error("请输入正确的金额");else{this.info[o]=e;var i=this.info.product_price-this.info.leveldk_money-this.info.coupon_money-this.info.scoredk_money;i=parseFloat(i),i=i.toFixed(2),i<0?n.error("总金额不能小于0"):this.info.totalprice=i}},subform:function(t){var e=this,o=t.detail.value;if(e.info.totalprice<0)n.error("总金额不能小于0");else{var i=e.opt.id?e.opt.id:"";n.post("ApiAdminRestaurantShopOrder/edit",{id:i,info:o,goods:e.ordergoods},(function(t){0==t.status?n.error(t.msg):(n.success(t.msg),setTimeout((function(){n.goto("tableWaiterDetail?id="+e.info.tableid,"redirect")}),1e3))}))}}}};e.default=i},"902d":function(t,e,o){"use strict";(function(t,e){var n=o("47a9");o("06e9");n(o("3240"));var i=n(o("3175"));t.__webpack_require_UNI_MP_PLUGIN__=o,e(i.default)}).call(this,o("3223")["default"],o("df3c")["createPage"])},a291:function(t,e,o){"use strict";o.d(e,"b",(function(){return i})),o.d(e,"c",(function(){return r})),o.d(e,"a",(function(){return n}));var n={loading:function(){return o.e("components/loading/loading").then(o.bind(null,"ceaa"))}},i=function(){var t=this,e=t.$createElement,o=(t._self._c,t.isload?t.__map(t.ordergoods,(function(e,o){var n=t.__get_orig(e),i=e.ggtext&&e.ggtext.length;return{$orig:n,g0:i}})):null),n=t.isload?t.t("color1"):null,i=t.isload?t.t("color1rgb"):null;t.$mp.data=Object.assign({},{$root:{l0:o,m0:n,m1:i}})},r=[]}},[["902d","common/runtime","common/vendor"]]]);