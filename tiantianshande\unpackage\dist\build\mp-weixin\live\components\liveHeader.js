require('../common/vendor.js');(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["live/components/liveHeader"],{"67d49":function(n,t,e){"use strict";e.r(t);var i=e("97e4"),o=e.n(i);for(var a in i)["default"].indexOf(a)<0&&function(n){e.d(t,n,(function(){return i[n]}))}(a);t["default"]=o.a},"74e5":function(n,t,e){"use strict";e.r(t);var i=e("e5dd"),o=e("67d49");for(var a in o)["default"].indexOf(a)<0&&function(n){e.d(t,n,(function(){return o[n]}))}(a);e("9e8d");var u=e("828b"),s=Object(u["a"])(o["default"],i["b"],i["c"],!1,null,"8bb51a9c",null,!1,i["a"],void 0);t["default"]=s.exports},"97e4":function(n,t,e){"use strict";(function(n){Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var e={props:{data:{type:Object,default:function(){return{anchor:{},online:0,online_count:0,is_silence:0,online_users:[]}}}},data:function(){return{defaultAvatar:"@/static/live/video/1-poster.png",isFollowed:!1,onlineCount:1}},computed:{getAnchorHeadimg:function(){return this.data.anchor&&this.data.anchor.headimg?this.data.anchor.headimg:this.defaultAvatar},getAnchorNickname:function(){return this.data.anchor&&this.data.anchor.nickname?this.data.anchor.nickname:"主播"}},methods:{handleFollow:function(){this.isFollowed=!this.isFollowed,n.showToast({title:this.isFollowed?"已关注":"已取消关注",icon:"none"})},handleShare:function(){this.$emit("onShare")},handleUserEnter:function(n){this.data.online_users||this.$set(this.data,"online_users",[]),this.data.online_users.push(n),n.onlineCount&&(this.onlineCount=n.onlineCount)},handleUserLeave:function(n){if(this.data.online_users){var t=this.data.online_users.findIndex((function(t){return t.id===n}));t>-1&&this.data.online_users.splice(t,1)}}},watch:{"data.online_count":{immediate:!0,handler:function(n){console.log("watch online_count变化:",{newVal:n,currentOnlineCount:this.onlineCount}),n&&(this.onlineCount=n)}}}};t.default=e}).call(this,e("df3c")["default"])},"9e8d":function(n,t,e){"use strict";var i=e("9f14"),o=e.n(i);o.a},"9f14":function(n,t,e){},e5dd:function(n,t,e){"use strict";e.d(t,"b",(function(){return i})),e.d(t,"c",(function(){return o})),e.d(t,"a",(function(){}));var i=function(){var n=this.$createElement;this._self._c},o=[]}}]);
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'live/components/liveHeader-create-component',
    {
        'live/components/liveHeader-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('df3c')['createComponent'](__webpack_require__("74e5"))
        })
    },
    [['live/components/liveHeader-create-component']]
]);
