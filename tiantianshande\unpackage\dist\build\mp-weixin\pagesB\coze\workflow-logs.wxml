<view class="container"><block wx:if="{{isload}}"><block><view class="header"><view class="header-title">工作流执行记录</view></view><view class="filter-bar"><view data-event-opts="{{[['tap',[['showWorkflowFilter',['$event']]]]]}}" class="filter-item" bindtap="__e"><text>{{selectedWorkflow?selectedWorkflow.name:'全部工作流'}}</text><text class="iconfont iconxiala"></text></view></view><view class="logs-list"><block wx:for="{{$root.l0}}" wx:for-item="log" wx:for-index="index" wx:key="index"><block><view class="log-item" data-log="{{log.g0}}" data-event-opts="{{[['tap',[['showLogDetail',['$event']]]]]}}" bindtap="__e"><view class="log-header"><view class="log-workflow">{{log.$orig.workflow_name||log.$orig.workflow_id}}</view><view class="{{['log-status',log.$orig.status?'success':'failed']}}"><text class="{{['iconfont',log.$orig.status?'iconzhengchang':'iconcuowu']}}"></text><text>{{log.$orig.status?'成功':'失败'}}</text></view></view><view class="log-time">{{log.$orig.create_time_text}}</view><block wx:if="{{log.$orig.parameters_preview}}"><view class="log-params"><text class="label">参数：</text><text class="value">{{log.$orig.parameters_preview}}</text></view></block></view></block></block><block wx:if="{{nomore}}"><nomore vue-id="89a7ff24-1" bind:__l="__l"></nomore></block><block wx:if="{{nodata}}"><nodata vue-id="89a7ff24-2" bind:__l="__l"></nodata></block></view><uni-popup class="vue-ref" vue-id="89a7ff24-3" type="bottom" data-ref="workflowFilterPopup" bind:__l="__l" vue-slots="{{['default']}}"><view class="filter-popup"><view class="popup-header"><view class="popup-title">选择工作流</view><view data-event-opts="{{[['tap',[['closeWorkflowFilter',['$event']]]]]}}" class="popup-close" bindtap="__e"><text class="iconfont iconguanbi"></text></view></view><scroll-view class="filter-options" scroll-y="true"><view class="filter-option" data-workflow data-event-opts="{{[['tap',[['selectWorkflowFilter',['$event']]]]]}}" bindtap="__e"><text>全部工作流</text><block wx:if="{{!selectedWorkflow}}"><text class="iconfont iconduihao" style="{{'color:'+($root.m0)+';'}}"></text></block></view><block wx:for="{{$root.l1}}" wx:for-item="workflow" wx:for-index="index" wx:key="index"><block><view class="filter-option" data-workflow="{{workflow.g1}}" data-event-opts="{{[['tap',[['selectWorkflowFilter',['$event']]]]]}}" bindtap="__e"><text>{{workflow.$orig.name}}</text><block wx:if="{{selectedWorkflow&&selectedWorkflow.workflow_id===workflow.$orig.workflow_id}}"><text class="iconfont iconduihao" style="{{'color:'+(workflow.m1)+';'}}"></text></block></view></block></block></scroll-view></view></uni-popup><uni-popup class="vue-ref" vue-id="89a7ff24-4" type="center" data-ref="logDetailPopup" bind:__l="__l" vue-slots="{{['default']}}"><view class="detail-popup"><view class="popup-header"><view class="popup-title">执行详情</view><view data-event-opts="{{[['tap',[['closeLogDetail',['$event']]]]]}}" class="popup-close" bindtap="__e"><text class="iconfont iconguanbi"></text></view></view><scroll-view class="detail-content" scroll-y="true"><view class="detail-section"><view class="section-title">工作流信息</view><view class="detail-item"><text class="label">工作流ID：</text><text class="value">{{currentLog.workflow_id}}</text></view><view class="detail-item"><text class="label">执行状态：</text><text class="{{['value',currentLog.status?'success':'failed']}}">{{''+(currentLog.status?'成功':'失败')+''}}</text></view><view class="detail-item"><text class="label">执行时间：</text><text class="value">{{currentLog.create_time_text}}</text></view></view><block wx:if="{{currentLog.parameters}}"><view class="detail-section"><view class="section-title">输入参数</view><view class="code-block"><text>{{$root.m2}}</text></view></view></block><block wx:if="{{currentLog.result}}"><view class="detail-section"><view class="section-title">执行结果</view><view class="code-block"><text>{{$root.m3}}</text></view></view></block></scroll-view><view class="popup-footer"><view data-event-opts="{{[['tap',[['closeLogDetail',['$event']]]]]}}" class="btn-confirm" style="{{'background:'+($root.m4)+';'}}" bindtap="__e">确定</view></view></view></uni-popup></block></block><block wx:if="{{loading}}"><loading vue-id="89a7ff24-5" bind:__l="__l"></loading></block></view>