<view class="dp-tab" style="{{'margin:'+(params.margin_y*2.2+'rpx '+params.margin_x*2.2+'rpx 0')+';'+('border-radius:'+(params.borderradius*2.2+'rpx')+';')}}"><block wx:if="{{needfixed==1}}"><view style="width:100%;height:90rpx;"></view></block><view class="{{['dsn-tab-box',needfixed==1?'fixed':'']}}" style="{{'background:'+(params.bgcolor)+';'+('padding:'+(params.padding_y*2.2+'rpx '+params.padding_x*2.2+'rpx')+';')}}"><scroll-view style="overflow:visible !important;" scroll-x="true" scroll-into-view="{{'tab-item-'+tabindex}}" scroll-with-animation="{{true}}"><view class="dsn-tab-box-content"><block wx:for="{{data}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="{{['dp-tab-item',(tabindex==index)?'active':'']}}" style="{{'font-size:'+(tabindex==index?params.fontsize1*2.5+'rpx':params.fontsize1*2+'rpx')+';'+('color:'+(tabindex==index?params.color2:params.color1)+';')+('min-width:'+((params.max_width||80)+'px')+';')+('flex-basis:'+('auto')+';')}}" id="{{'tab-item-'+index}}" data-index="{{index}}" data-event-opts="{{[['tap',[['changetab',['$event']]]]]}}" bindtap="__e"><block wx:if="{{params.showpic==1}}"><view class="tab-item-content"><block wx:if="{{item.imgurl}}"><image class="tab-item-icon" mode="heightFix" src="{{item.imgurl}}"></image></block><view class="tab-item-text">{{item.name}}</view></view></block><block wx:else><view class="tab-item-content-alt"><block wx:if="{{item.imgurl}}"><image class="tab-item-image" mode="widthFix" src="{{item.imgurl}}"></image></block><block wx:else><view>{{item.name}}</view></block></view></block><block wx:if="{{params.arrowshow==1&&tabindex==index}}"><view class="dp-tab-item-after" style="{{'background:'+(params.arrowcolor)+';'}}"></view></block></view></block></view></scroll-view></view><view data-event-opts="{{[['touchstart',[['touchStart',['$event']]]],['touchmove',[['touchMove',['$event']]]],['touchend',[['touchEnd',['$event']]]],['touchcancel',[['touchEnd',['$event']]]]]}}" class="{{['dp-tab-content',(isSwiping)?'swiping':'',(swipeAnimating)?'animating':'']}}" style="{{'background:'+(params.bgcolor2)+';'+('padding:'+(params.padding_y*2.2+'rpx '+params.padding_x*2.2+'rpx')+';')+('transform:'+(contentTransform)+';')}}" bindtouchstart="__e" bindtouchmove="__e" bindtouchend="__e" bindtouchcancel="__e"><block wx:if="{{showSwipeIndicator}}"><view class="{{['swipe-indicator',swipeDirection==='left'?'right-indicator':'left-indicator']}}"><view class="{{['indicator-arrow',swipeDirection==='left'?'right-arrow':'left-arrow']}}"></view></view></block><view class="dp-tab-content-inner"><transition vue-id="4130bfc2-1" name="tab-content-fade" mode="out-in" bind:__l="__l" vue-slots="{{['default']}}"><view class="tab-content-wrapper"><block wx:for="{{pagecontent}}" wx:for-item="setData" wx:for-index="index" wx:key="index"><block><block wx:if="{{setData.temp=='notice'}}"><block><dp-notice vue-id="{{('4130bfc2-2-'+index)+','+('4130bfc2-1')}}" params="{{setData.params}}" data="{{setData.data}}" bind:__l="__l"></dp-notice></block></block><block wx:if="{{setData.temp=='banner'}}"><block><dp-banner vue-id="{{('4130bfc2-3-'+index)+','+('4130bfc2-1')}}" params="{{setData.params}}" data="{{setData.data}}" bind:__l="__l"></dp-banner></block></block><block wx:if="{{setData.temp=='search'}}"><block><dp-search vue-id="{{('4130bfc2-4-'+index)+','+('4130bfc2-1')}}" params="{{setData.params}}" data="{{setData.data}}" bind:__l="__l"></dp-search></block></block><block wx:if="{{setData.temp=='text'}}"><block><dp-text vue-id="{{('4130bfc2-5-'+index)+','+('4130bfc2-1')}}" params="{{setData.params}}" data="{{setData.data}}" bind:__l="__l"></dp-text></block></block><block wx:if="{{setData.temp=='title'}}"><block><dp-title vue-id="{{('4130bfc2-6-'+index)+','+('4130bfc2-1')}}" params="{{setData.params}}" data="{{setData.data}}" bind:__l="__l"></dp-title></block></block><block wx:if="{{setData.temp=='dhlist'}}"><block><dp-dhlist vue-id="{{('4130bfc2-7-'+index)+','+('4130bfc2-1')}}" params="{{setData.params}}" data="{{setData.data}}" bind:__l="__l"></dp-dhlist></block></block><block wx:if="{{setData.temp=='line'}}"><block><dp-line vue-id="{{('4130bfc2-8-'+index)+','+('4130bfc2-1')}}" params="{{setData.params}}" data="{{setData.data}}" bind:__l="__l"></dp-line></block></block><block wx:if="{{setData.temp=='blank'}}"><block><dp-blank vue-id="{{('4130bfc2-9-'+index)+','+('4130bfc2-1')}}" params="{{setData.params}}" data="{{setData.data}}" bind:__l="__l"></dp-blank></block></block><block wx:if="{{setData.temp=='menu'}}"><block><dp-menu vue-id="{{('4130bfc2-10-'+index)+','+('4130bfc2-1')}}" params="{{setData.params}}" data="{{setData.data}}" bind:__l="__l"></dp-menu></block></block><block wx:if="{{setData.temp=='map'}}"><block><dp-map vue-id="{{('4130bfc2-11-'+index)+','+('4130bfc2-1')}}" params="{{setData.params}}" data="{{setData.data}}" bind:__l="__l"></dp-map></block></block><block wx:if="{{setData.temp=='cube'}}"><block><dp-cube vue-id="{{('4130bfc2-12-'+index)+','+('4130bfc2-1')}}" params="{{setData.params}}" data="{{setData.data}}" bind:__l="__l"></dp-cube></block></block><block wx:if="{{setData.temp=='picture'}}"><block><dp-picture vue-id="{{('4130bfc2-13-'+index)+','+('4130bfc2-1')}}" params="{{setData.params}}" data="{{setData.data}}" bind:__l="__l"></dp-picture></block></block><block wx:if="{{setData.temp=='pictures'}}"><block><dp-pictures vue-id="{{('4130bfc2-14-'+index)+','+('4130bfc2-1')}}" params="{{setData.params}}" data="{{setData.data}}" bind:__l="__l"></dp-pictures></block></block><block wx:if="{{setData.temp=='video'}}"><block><dp-video vue-id="{{('4130bfc2-15-'+index)+','+('4130bfc2-1')}}" params="{{setData.params}}" data="{{setData.data}}" bind:__l="__l"></dp-video></block></block><block wx:if="{{setData.temp=='tab'}}"><block><dp-tab vue-id="{{('4130bfc2-16-'+index)+','+('4130bfc2-1')}}" params="{{setData.params}}" data="{{setData.data}}" tabid="{{setData.id}}" menuindex="{{menuindex}}" bind:__l="__l"></dp-tab></block></block><block wx:if="{{setData.temp=='shop'}}"><block><dp-shop vue-id="{{('4130bfc2-17-'+index)+','+('4130bfc2-1')}}" params="{{setData.params}}" data="{{setData.data}}" shopinfo="{{setData.shopinfo}}" bind:__l="__l"></dp-shop></block></block><block wx:if="{{setData.temp=='product'}}"><block><dp-product vue-id="{{('4130bfc2-18-'+index)+','+('4130bfc2-1')}}" params="{{setData.params}}" data="{{setData.data}}" menuindex="{{menuindex}}" bind:__l="__l"></dp-product></block></block><block wx:if="{{setData.temp=='collage'}}"><block><dp-collage vue-id="{{('4130bfc2-19-'+index)+','+('4130bfc2-1')}}" params="{{setData.params}}" data="{{setData.data}}" menuindex="{{menuindex}}" bind:__l="__l"></dp-collage></block></block><block wx:if="{{setData.temp=='luckycollage'}}"><block><dp-luckycollage vue-id="{{('4130bfc2-20-'+index)+','+('4130bfc2-1')}}" params="{{setData.params}}" data="{{setData.data}}" bind:__l="__l"></dp-luckycollage></block></block><block wx:if="{{setData.temp=='kanjia'}}"><block><dp-kanjia vue-id="{{('4130bfc2-21-'+index)+','+('4130bfc2-1')}}" params="{{setData.params}}" data="{{setData.data}}" menuindex="{{menuindex}}" bind:__l="__l"></dp-kanjia></block></block><block wx:if="{{setData.temp=='yuyue'}}"><block><dp-yuyue vue-id="{{('4130bfc2-22-'+index)+','+('4130bfc2-1')}}" params="{{setData.params}}" data="{{setData.data}}" menuindex="{{menuindex}}" bind:__l="__l"></dp-yuyue></block></block><block wx:if="{{setData.temp=='seckill'}}"><block><dp-seckill vue-id="{{('4130bfc2-23-'+index)+','+('4130bfc2-1')}}" params="{{setData.params}}" data="{{setData.data}}" menuindex="{{menuindex}}" bind:__l="__l"></dp-seckill></block></block><block wx:if="{{setData.temp=='scoreshop'}}"><block><dp-scoreshop vue-id="{{('4130bfc2-24-'+index)+','+('4130bfc2-1')}}" params="{{setData.params}}" data="{{setData.data}}" menuindex="{{menuindex}}" bind:__l="__l"></dp-scoreshop></block></block><block wx:if="{{setData.temp=='tuangou'}}"><block><dp-tuangou vue-id="{{('4130bfc2-25-'+index)+','+('4130bfc2-1')}}" params="{{setData.params}}" data="{{setData.data}}" menuindex="{{menuindex}}" bind:__l="__l"></dp-tuangou></block></block><block wx:if="{{setData.temp=='kecheng'}}"><block><dp-kecheng vue-id="{{('4130bfc2-26-'+index)+','+('4130bfc2-1')}}" params="{{setData.params}}" data="{{setData.data}}" menuindex="{{menuindex}}" bind:__l="__l"></dp-kecheng></block></block><block wx:if="{{setData.temp=='restaurant_product'}}"><block><dp-restaurant-product vue-id="{{('4130bfc2-27-'+index)+','+('4130bfc2-1')}}" params="{{setData.params}}" data="{{setData.data}}" menuindex="{{menuindex}}" bind:__l="__l"></dp-restaurant-product></block></block><block wx:if="{{setData.temp=='coupon'}}"><block><dp-coupon vue-id="{{('4130bfc2-28-'+index)+','+('4130bfc2-1')}}" params="{{setData.params}}" data="{{setData.data}}" bind:__l="__l"></dp-coupon></block></block><block wx:if="{{setData.temp=='article'}}"><block><dp-article vue-id="{{('4130bfc2-29-'+index)+','+('4130bfc2-1')}}" params="{{setData.params}}" data="{{setData.data}}" bind:__l="__l"></dp-article></block></block><block wx:if="{{setData.temp=='business'}}"><block><dp-business vue-id="{{('4130bfc2-30-'+index)+','+('4130bfc2-1')}}" params="{{setData.params}}" data="{{setData.data}}" bind:__l="__l"></dp-business></block></block><block wx:if="{{setData.temp=='shortvideo'}}"><block><dp-shortvideo vue-id="{{('4130bfc2-31-'+index)+','+('4130bfc2-1')}}" params="{{setData.params}}" data="{{setData.data}}" bind:__l="__l"></dp-shortvideo></block></block><block wx:if="{{setData.temp=='daihuobiji'}}"><block><dp-daihuobiji vue-id="{{('4130bfc2-32-'+index)+','+('4130bfc2-1')}}" params="{{setData.params}}" data="{{setData.data}}" bind:__l="__l"></dp-daihuobiji></block></block><block wx:if="{{setData.temp=='liveroom'}}"><block><dp-liveroom vue-id="{{('4130bfc2-33-'+index)+','+('4130bfc2-1')}}" params="{{setData.params}}" data="{{setData.data}}" bind:__l="__l"></dp-liveroom></block></block><block wx:if="{{setData.temp=='button'}}"><block><dp-button vue-id="{{('4130bfc2-34-'+index)+','+('4130bfc2-1')}}" params="{{setData.params}}" data="{{setData.data}}" bind:__l="__l"></dp-button></block></block><block wx:if="{{setData.temp=='hotspot'}}"><block><dp-hotspot vue-id="{{('4130bfc2-35-'+index)+','+('4130bfc2-1')}}" params="{{setData.params}}" data="{{setData.data}}" bind:__l="__l"></dp-hotspot></block></block><block wx:if="{{setData.temp=='cover'}}"><block><dp-cover vue-id="{{('4130bfc2-36-'+index)+','+('4130bfc2-1')}}" params="{{setData.params}}" data="{{setData.data}}" bind:__l="__l"></dp-cover></block></block><block wx:if="{{setData.temp=='richtext'}}"><block><dp-richtext vue-id="{{('4130bfc2-37-'+index)+','+('4130bfc2-1')}}" params="{{setData.params}}" data="{{setData.data}}" content="{{setData.content}}" bind:__l="__l"></dp-richtext></block></block><block wx:if="{{setData.temp=='form'}}"><block><dp-form vue-id="{{('4130bfc2-38-'+index)+','+('4130bfc2-1')}}" params="{{setData.params}}" data="{{setData.data}}" content="{{setData.content}}" bind:__l="__l"></dp-form></block></block><block wx:if="{{setData.temp=='form-log'}}"><block><dp-form-log vue-id="{{('4130bfc2-39-'+index)+','+('4130bfc2-1')}}" params="{{setData.params}}" data="{{setData.data}}" bind:__l="__l"></dp-form-log></block></block><block wx:if="{{setData.temp=='userinfo'}}"><block><dp-userinfo vue-id="{{('4130bfc2-40-'+index)+','+('4130bfc2-1')}}" params="{{setData.params}}" data="{{setData.data}}" content="{{setData.content}}" bind:__l="__l"></dp-userinfo></block></block><block wx:if="{{setData.temp=='wxad'}}"><block><dp-wxad vue-id="{{('4130bfc2-41-'+index)+','+('4130bfc2-1')}}" params="{{setData.params}}" data="{{setData.data}}" bind:__l="__l"></dp-wxad></block></block><block wx:if="{{setData.temp=='jidian'}}"><block><dp-jidian vue-id="{{('4130bfc2-42-'+index)+','+('4130bfc2-1')}}" params="{{setData.params}}" data="{{setData.data}}" bind:__l="__l"></dp-jidian></block></block><block wx:if="{{setData.temp=='zhaopin'}}"><block><dp-zhaopin vue-id="{{('4130bfc2-43-'+index)+','+('4130bfc2-1')}}" params="{{setData.params}}" data="{{setData.data}}" bind:__l="__l"></dp-zhaopin></block></block><block wx:if="{{setData.temp=='qiuzhi'}}"><block><dp-qiuzhi vue-id="{{('4130bfc2-44-'+index)+','+('4130bfc2-1')}}" params="{{setData.params}}" data="{{setData.data}}" bind:__l="__l"></dp-qiuzhi></block></block><block wx:if="{{setData.temp=='xixie'}}"><block><dp-xixie vue-id="{{('4130bfc2-45-'+index)+','+('4130bfc2-1')}}" params="{{setData.params}}" data="{{setData.data}}" data-event-opts="{{[['^getdata',[['getIndexdata']]]]}}" bind:getdata="__e" bind:__l="__l"></dp-xixie></block></block></block></block></view></transition></view></view><block wx:if="{{loading&&!fastLoading}}"><loading class="{{[(fastLoading)?'fast-loading':'']}}" vue-id="4130bfc2-46" bind:__l="__l"></loading></block></view>