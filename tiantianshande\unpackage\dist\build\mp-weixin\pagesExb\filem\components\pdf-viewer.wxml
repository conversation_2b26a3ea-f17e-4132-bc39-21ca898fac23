<view class="pdf-container"><block wx:if="{{loading}}"><view class="loading"><text>PDF文件加载中...</text></view></block><block wx:else><block wx:if="{{error}}"><view class="error"><text>{{error}}</text></view></block><block wx:else><view class="pdf-viewer"><view class="pdf-controls"><view class="page-info">{{currentPage+" / "+totalPages}}</view><view class="pdf-buttons"><button class="btn" disabled="{{currentPage<=1}}" data-event-opts="{{[['tap',[['prevPage',['$event']]]]]}}" bindtap="__e">上一页</button><button class="btn" disabled="{{currentPage>=totalPages}}" data-event-opts="{{[['tap',[['nextPage',['$event']]]]]}}" bindtap="__e">下一页</button></view></view><scroll-view class="pdf-content" scroll-y="{{true}}"><canvas class="pdf-canvas" style="{{'width:'+(canvasWidth+'px')+';'+('height:'+(canvasHeight+'px')+';')}}" canvas-id="pdf-canvas"></canvas></scroll-view></view></block></block></view>