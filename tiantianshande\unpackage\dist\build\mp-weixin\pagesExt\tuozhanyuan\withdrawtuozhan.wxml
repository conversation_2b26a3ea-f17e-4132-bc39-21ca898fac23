<view class="container"><block wx:if="{{isload}}"><block><form data-event-opts="{{[['submit',[['formSubmit',['$event']]]]]}}" bindsubmit="__e"><view class="mymoney" style="{{'background:'+($root.m0)+';'}}"><view class="f1">{{"我的可提现"+$root.m1}}</view><view class="f2"><text style="font-size:26rpx;">￥</text>{{userinfo.commission}}</view><view class="f3" data-url="commissionlog?st=1" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><text>提现记录</text><text class="iconfont iconjiantou" style="font-size:20rpx;"></text></view></view><view class="content2"><view class="item2"><view class="f1">提现金额(元)</view></view><view class="item3"><view class="f1">￥</view><view class="f2"><input class="input" type="digit" name="money" value="" placeholder="请输入提现金额" placeholder-style="color:#999;font-size:40rpx" data-event-opts="{{[['input',[['moneyinput',['$event']]]]]}}" bindinput="__e"/></view></view><block wx:if="{{sysset.comwithdrawfee>0||sysset.comwithdrawmin>0}}"><view class="item4"><block wx:if="{{sysset.comwithdrawmin>0}}"><text style="margin-right:10rpx;">{{"最低提现金额"+sysset.comwithdrawmin+'元'}}</text></block><block wx:if="{{sysset.comwithdrawfee>0}}"><text>{{"提现手续费"+sysset.comwithdrawfee+'%'}}</text></block></view></block><block wx:if="{{sysset.comwithdrawbl&&sysset.comwithdrawbl!=100}}"><view style="width:94%;margin:0 3%;color:#8C8C8C;font-size:28rpx;margin-bottom:30rpx;">{{"提现金额的"+(100-sysset.comwithdrawbl)+'%将直接转到余额用于复购'}}</view></block></view><view class="withdrawtype"><view class="f1">选择提现方式：</view><view class="f2"><block wx:if="{{sysset.withdraw_weixin==1}}"><view class="item" data-paytype="微信钱包" data-event-opts="{{[['tap',[['changeradio',['$event']]]]]}}" catchtap="__e"><view class="t1"><image class="img" src="/static/img/withdraw-weixin.png"></image>微信钱包</view><view class="radio" style="{{(paytype=='微信钱包'?'background:'+$root.m2+';border:0':'')}}"><image class="radio-img" src="/static/img/checkd.png"></image></view></view></block><block wx:if="{{sysset.withdraw_aliaccount==1}}"><label class="item" data-paytype="支付宝" data-event-opts="{{[['tap',[['changeradio',['$event']]]]]}}" catchtap="__e"><view class="t1"><image class="img" src="/static/img/withdraw-alipay.png"></image>支付宝</view><view class="radio" style="{{(paytype=='支付宝'?'background:'+$root.m3+';border:0':'')}}"><image class="radio-img" src="/static/img/checkd.png"></image></view></label></block><block wx:if="{{sysset.withdraw_bankcard==1}}"><label class="item" data-paytype="银行卡" data-event-opts="{{[['tap',[['changeradio',['$event']]]]]}}" catchtap="__e"><view class="t1"><image class="img" src="/static/img/withdraw-cash.png"></image>银行卡</view><view class="radio" style="{{(paytype=='银行卡'?'background:'+$root.m4+';border:0':'')}}"><image class="radio-img" src="/static/img/checkd.png"></image></view></label></block></view></view><button data-event-opts="{{[['tap',[['formSubmit',['$event']]]]]}}" class="btn" style="{{'background:'+($root.m5)+';'}}" bindtap="__e">立即提现</button><block wx:if="{{paytype=='支付宝'}}"><view style="width:100%;margin-top:40rpx;text-align:center;color:#999;display:flex;align-items:center;justify-content:center;" data-url="/pagesExa/my/setaliaccount" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">设置支付宝账户<image style="width:30rpx;height:30rpx;" src="/static/img/arrowright.png"></image></view></block><block wx:if="{{paytype=='银行卡'}}"><view style="width:100%;margin-top:40rpx;text-align:center;color:#999;display:flex;align-items:center;justify-content:center;" data-url="/pagesExa/my/setbankinfo" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">设置银行卡账户<image style="width:30rpx;height:30rpx;" src="/static/img/arrowright.png"></image></view></block></form></block></block><block wx:if="{{loading}}"><loading vue-id="2638d448-1" bind:__l="__l"></loading></block><dp-tabbar vue-id="2638d448-2" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="2638d448-3" data-ref="popmsg" bind:__l="__l"></popmsg></view>