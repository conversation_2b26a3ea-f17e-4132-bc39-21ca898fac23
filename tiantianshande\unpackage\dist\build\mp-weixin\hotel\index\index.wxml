<view><block wx:if="{{isload}}"><block><view class="dp-banner"><swiper class="dp-banner-swiper" autoplay="{{true}}" indicator-dots="{{false}}" current="{{0}}" circular="{{true}}" interval="{{3000}}"><block wx:for="{{set.pics}}" wx:for-item="item" wx:for-index="index"><block><swiper-item><view data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image class="dp-banner-swiper-img" src="{{item}}" mode="widthFix"></image></view></swiper-item></block></block></swiper></view><view class="position-view"><view class="banner-poster"></view><view class="reserve-view"><view class="tab-view"><scroll-view class="scroll-class" scroll-x="{{true}}"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index"><block><view data-event-opts="{{[['tap',[['tabChange',[index]]]]]}}" class="{{[index==0?'options-tab-active':'',index==item.g0-1?'options-tab-active2':'','options-tab']}}" bindtap="__e">{{''+item.$orig.name+''}}<block wx:if="{{tabindex==index&&tabindex==0}}"><view class="active-class-show" style="{{'color:'+(item.m0)+';'}}"><view class="text-view">{{item.$orig.name}}</view><view class="color-view" style="{{'background-color:'+(item.m1)+';'}}"></view></view></block><block wx:if="{{item.g1}}"><view class="active-class-show-last" style="{{'color:'+(item.m2)+';'}}"><view class="text-view">{{item.$orig.name}}</view><view class="color-view" style="{{'background-color:'+(item.m3)+';'}}"></view></view></block><block wx:if="{{item.g2}}"><view class="active-class-show-s" style="{{'color:'+(item.m4)+';'}}"><view class="text-view">{{item.$orig.name}}</view><view class="color-view" style="{{'background-color:'+(item.m5)+';'}}"></view></view></block></view></block></block></scroll-view></view><view style="height:120rpx;background:#ebeef5;border-radius:20rpx 20rpx 0rpx 0rpx;overflow:hidden;"></view><view data-event-opts="{{[['tap',[['selectDate',['$event']]]]]}}" class="reserve-time-view" bindtap="__e"><view class="time-view"><view class="time-title">入住时间</view><view class="flex flex-y-center" style="margin-top:15rpx;align-items:flex-end;"><view class="date-time">{{startDate}}</view><view class="time-title">{{startWeek}}</view></view></view><view class="statistics-view"><view class="statistics-date"><view class="content-decorate left-c-d"></view>{{''+dayCount+'晚'}}<view class="content-decorate right-c-d"></view></view><view class="color-line"></view></view><view class="time-view"><view class="time-title">离店时间</view><view class="flex flex-y-center" style="margin-top:15rpx;align-items:flex-end;"><view class="date-time">{{endDate}}</view><view class="time-title">{{endWeek}}</view></view></view></view><view class="search-view"><input class="input-class" placeholder="输入位置/关键字" placeholder-style="color: rgba(123, 128, 133, 0.6);font-size: 28rpx;" data-event-opts="{{[['input',[['searchChange',['$event']]]]]}}" value="{{keyword}}" bindinput="__e"/><image src="{{pre_url+'/static/img/arrowright.png'}}"></image></view><view data-event-opts="{{[['tap',[['search',['$event']]]]]}}" class="search-but" style="{{'background-color:'+($root.m6)+';'}}" bindtap="__e">{{''+text['酒店']+'查询'}}</view></view><view class="menu-view"><block wx:if="{{$root.g3>3}}"><scroll-view class="scroll-view-class" scroll-x="{{true}}"><block wx:for="{{catelist}}" wx:for-item="item" wx:for-index="index"><block><view style="display:inline-block;width:21.5%;margin-right:30rpx;"><view class="menu-options-scroll" data-url="{{'hotellist?cateid='+item.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image src="{{item.pic}}"></image><view class="menu-text-view">{{item.name}}</view></view></view></block></block></scroll-view></block><block wx:else><block wx:for="{{catelist}}" wx:for-item="item" wx:for-index="index"><block><view class="menu-options" data-url="{{'hotellist?cateid='+item.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image src="{{item.pic}}"></image><view class="menu-text-view">{{item.name}}</view></view></block></block></block></view><view class="hotels-list"><view class="hottitle">{{"热门"+text['酒店']}}</view><block wx:for="{{$root.l2}}" wx:for-item="item" wx:for-index="index"><block><view class="hotels-options" data-url="{{'hoteldetails?id='+item.$orig.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="hotel-img"><image src="{{item.$orig.pic}}" mode="aspectFill"></image></view><view class="hotel-info"><view class="hotel-title">{{item.$orig.name}}</view><view class="hotel-address">{{item.$orig.address}}</view><view class="hotel-characteristic"><block wx:for="{{item.l1}}" wx:for-item="items" wx:for-index="indexs"><block><block wx:if="{{indexs<6}}"><view class="characteristic-options" style="{{('background:rgba('+items.m7+',0.05);color:'+items.m8)}}">{{items.$orig+''}}</view></block></block></block></view><view class="hotel-but-view"><view class="make-info"><block wx:if="{{item.$orig.min_daymoney}}"><view class="hotel-price" style="{{'color:'+(item.m9)+';'}}"><view class="hotel-price-num">{{item.$orig.min_daymoney+moneyunit}}</view><view>/晚起</view></view></block><block wx:else><view class="hotel-price" style="{{'color:'+(item.m10)+';'}}"><view>￥</view><view class="hotel-price-num">{{item.$orig.min_price}}</view><view>起</view></view></block><view class="hotel-text">{{item.$orig.sales+'人已预定'}}</view></view><view class="hotel-make" style="{{('background:rgba('+item.m11+',0.8);color:#FFF')}}">预约</view></view></view></view></block></block></view><view style="height:160rpx;"></view></view><block wx:if="{{calendarvisible}}"><view class="popup__container"><view data-event-opts="{{[['tap',[['handleClickMask',['$event']]]]]}}" class="popup__overlay" catchtap="__e"></view><view class="popup__modal"><view class="popup__title"><text class="popup__title-text">选择日期</text><image class="popup__close" style="width:56rpx;height:56rpx;top:20rpx;right:20rpx;" src="{{pre_url+'/static/img/hotel/popupClose2.png'}}" data-event-opts="{{[['tap',[['handleClickMask',['$event']]]]]}}" catchtap="__e"></image></view><view class="popup__content"><view class="reserve-time-view"><view class="time-view"><view class="time-title">入住</view><view class="flex flex-y-center" style="margin-top:15rpx;align-items:flex-end;"><view class="date-time">{{startDate}}</view><view class="time-title">{{startWeek}}</view></view></view><view class="statistics-view"><view class="statistics-date"><view class="content-decorate left-c-d"></view>{{'共'+dayCount+'晚'}}<view class="content-decorate right-c-d"></view></view><view class="color-line"></view></view><view class="time-view"><view class="time-title">离店</view><view class="flex flex-y-center" style="margin-top:15rpx;align-items:flex-end;"><view class="date-time">{{endDate}}</view><view class="time-title">{{endWeek}}</view></view></view></view><view class="calendar-view"><calendar vue-id="cf02a8ac-1" is-show="{{true}}" isFixed="{{false}}" showstock="0" start-date="{{starttime}}" end-date="{{endtime}}" mode="2" themeColor="{{$root.m12}}" maxdays="{{maxdays}}" data-event-opts="{{[['^callback',[['getDate']]]]}}" bind:callback="__e" bind:__l="__l"></calendar></view><view data-event-opts="{{[['tap',[['popupClose',['$event']]]]]}}" class="choose-but-class" style="{{('background: linear-gradient(90deg,rgba('+$root.m13+',1) 0%,rgba('+$root.m14+',1) 100%)')}}" bindtap="__e">{{'确认'+dayCount+'晚'}}</view></view></view></view></block></block></block><block wx:if="{{loading}}"><loading vue-id="cf02a8ac-2" bind:__l="__l"></loading></block><dp-tabbar vue-id="cf02a8ac-3" opt="{{opt}}" data-event-opts="{{[['^getmenuindex',[['getmenuindex']]]]}}" bind:getmenuindex="__e" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="cf02a8ac-4" data-ref="popmsg" bind:__l="__l"></popmsg></view>