<view class="container"><block wx:if="{{isload}}"><block><view class="topsearch flex-y-center"><view class="f1 flex-y-center"><image class="img" src="/static/img/search_ico.png"></image><input placeholder="输入昵称/姓名/手机号搜索" placeholder-style="font-size:24rpx;color:#C2C2C2" data-event-opts="{{[['confirm',[['searchConfirm',['$event']]]],['input',[['searchChange',['$event']]]]]}}" value="{{keyword}}" bindconfirm="__e" bindinput="__e"/></view></view><block wx:if="{{$root.g0}}"><view class="content"><view class="label"><text class="t1">成员信息</text><text class="t2"></text></view><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><view class="item"><view class="f1"><image src="{{item.$orig.headimg}}"></image><view class="t2"><text class="x1">{{item.$orig.nickname}}</text><text class="x2">{{item.m0}}</text><text class="x2">{{"等级："+item.$orig.levelname}}</text><block wx:if="{{item.$orig.tel}}"><text class="x2">{{"手机号："+item.$orig.tel}}</text></block></view></view><view class="f2"><view class="t3"><block wx:if="{{userlevel&&userlevel.team_givemoney==1}}"><view class="x1" data-id="{{item.$orig.id}}" data-event-opts="{{[['tap',[['givemoneyshow',['$event']]]]]}}" bindtap="__e">{{"转"+item.m1}}</view></block><block wx:if="{{userlevel&&userlevel.team_givescore==1}}"><view class="x1" data-id="{{item.$orig.id}}" data-event-opts="{{[['tap',[['givescoreshow',['$event']]]]]}}" bindtap="__e">{{"转"+item.m2}}</view></block></view></view></view></block></block></view></block><uni-popup class="vue-ref" vue-id="56e2984e-1" id="dialogmoneyInput" type="dialog" data-ref="dialogmoneyInput" bind:__l="__l" vue-slots="{{['default']}}"><uni-popup-dialog vue-id="{{('56e2984e-2')+','+('56e2984e-1')}}" mode="input" title="转账金额" value="" placeholder="请输入转账金额" data-event-opts="{{[['^confirm',[['givemoney']]]]}}" bind:confirm="__e" bind:__l="__l"></uni-popup-dialog></uni-popup><uni-popup class="vue-ref" vue-id="56e2984e-3" id="dialogscoreInput" type="dialog" data-ref="dialogscoreInput" bind:__l="__l" vue-slots="{{['default']}}"><uni-popup-dialog vue-id="{{('56e2984e-4')+','+('56e2984e-3')}}" mode="input" title="转账数量" value="" placeholder="请输入转账数量" data-event-opts="{{[['^confirm',[['givescore']]]]}}" bind:confirm="__e" bind:__l="__l"></uni-popup-dialog></uni-popup></block></block><block wx:if="{{nodata}}"><nodata vue-id="56e2984e-5" bind:__l="__l"></nodata></block><block wx:if="{{nomore}}"><nomore vue-id="56e2984e-6" bind:__l="__l"></nomore></block><block wx:if="{{loading}}"><loading vue-id="56e2984e-7" bind:__l="__l"></loading></block><dp-tabbar vue-id="56e2984e-8" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="56e2984e-9" data-ref="popmsg" bind:__l="__l"></popmsg></view>