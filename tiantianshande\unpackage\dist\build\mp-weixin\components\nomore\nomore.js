(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["components/nomore/nomore"],{3892:function(e,t,n){"use strict";n.r(t);var u=n("6a6e"),o=n("e3d3");for(var c in o)["default"].indexOf(c)<0&&function(e){n.d(t,e,(function(){return o[e]}))}(c);n("e879");var r=n("828b"),a=Object(r["a"])(o["default"],u["b"],u["c"],!1,null,null,null,!1,u["a"],void 0);t["default"]=a.exports},"6a6e":function(e,t,n){"use strict";n.d(t,"b",(function(){return u})),n.d(t,"c",(function(){return o})),n.d(t,"a",(function(){}));var u=function(){var e=this.$createElement;this._self._c},o=[]},afc8:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;t.default={props:{text:{default:"没有更多数据了"},textcolor:{default:"#999"},linecolor:{default:"#eee"}}}},e3c3:function(e,t,n){},e3d3:function(e,t,n){"use strict";n.r(t);var u=n("afc8"),o=n.n(u);for(var c in u)["default"].indexOf(c)<0&&function(e){n.d(t,e,(function(){return u[e]}))}(c);t["default"]=o.a},e879:function(e,t,n){"use strict";var u=n("e3c3"),o=n.n(u);o.a}}]);
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'components/nomore/nomore-create-component',
    {
        'components/nomore/nomore-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('df3c')['createComponent'](__webpack_require__("3892"))
        })
    },
    [['components/nomore/nomore-create-component']]
]);
