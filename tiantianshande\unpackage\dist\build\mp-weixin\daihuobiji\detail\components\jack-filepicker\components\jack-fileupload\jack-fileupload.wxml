<view><view><view class="container"><swiper style="{{'margin-bottom:'+('10rpx')+';'}}" interval="3000" disable-touch="{{isEdit}}" current="{{currentIndex}}" duration="500" circular="{{true}}" data-event-opts="{{[['change',[['onChange',['$event']]]]]}}" bindchange="__e"><block wx:for="{{images}}" wx:for-item="image" wx:for-index="index" wx:key="index"><swiper-item style="text-align:center;" catchtouchmove="{{isEdit}}"><view class="content-box" style="{{'overflow:'+(notMoveCanvas?'hidden':'auto')+';'}}"><view class="canvas" style="{{'height:'+(canvasHeight[index]+'px')+';'}}" id="{{'canvas-'+index}}"><canvas style="{{'width:'+(itemCanvasInfo.width+'px')+';'+('height:'+(itemCanvasInfo.height+'px')+';')}}" canvas-id="{{'newCanvas-'+index}}"></canvas><canvas class="canvasDom vue-ref-in-for" style="{{'width:'+(itemCanvasInfo.width+'px')+';'+('height:'+(itemCanvasInfo.height+'px')+';')}}" canvas-id="{{'itemCanvas-'+index}}" data-ref="{{'itemCanvas-'+index}}"></canvas><canvas class="canvasDom" style="{{'width:'+('100%')+';'+('height:'+(canvasHeight[index]+'px')+';')}}" canvas-id="{{'imgCanvas-'+index}}"></canvas><canvas class="canvasDom" style="{{'width:'+('100%')+';'+('height:'+(canvasHeight[index]+'px')+';')}}" canvas-id="{{'drawCanvas-'+index}}" data-event-opts="{{[['touchmove',[['touchmove',['$event']]]],['touchstart',[['touchstart',['$event']]]],['touchend',[['touchend',['$event']]]]]}}" bindtouchmove="__e" bindtouchstart="__e" bindtouchend="__e"></canvas><canvas class="canvasDom" style="{{'width:'+('100%')+';'+('height:'+(canvasHeight[index]+'px')+';')+('z-index:'+(99999)+';')}}" canvas-id="{{'timeCanvas-'+index}}" data-event-opts="{{[['touchmove',[['touchmove',['$event']]]],['touchstart',[['touchstart',['$event']]]],['touchend',[['touchend',['$event']]]]]}}" bindtouchmove="__e" bindtouchstart="__e" bindtouchend="__e"></canvas><canvas class="canvasDom" style="{{'width:'+('100%')+';'+('height:'+(canvasHeight[index]+'px')+';')+('z-index:'+(99999)+';')}}" canvas-id="{{'textCanvas-'+index}}" data-event-opts="{{[['touchmove',[['touchmove',['$event']]]],['touchstart',[['touchstart',['$event']]]],['touchend',[['touchend',['$event']]]]]}}" bindtouchmove="__e" bindtouchstart="__e" bindtouchend="__e"></canvas><canvas class="canvasDom" style="{{'width:'+('100%')+';'+('height:'+(canvasHeight[index]+'px')+';')}}" canvas-id="{{'clipCanvas-'+index}}" data-event-opts="{{[['touchmove',[['touchmove',['$event']]]],['touchstart',[['touchstart',['$event']]]],['touchend',[['touchend',['$event']]]]]}}" bindtouchmove="__e" bindtouchstart="__e" bindtouchend="__e"></canvas></view></view></swiper-item></block></swiper><view><scroll-view class="scroll-view" style="white-space:nowrap;text-align:center;" scroll-x="true" scroll-left="0"><block wx:for="{{images}}" wx:for-item="image" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['switchImg',[index]]]]]}}" style="display:inline-block;text-align:center;margin-right:20rpx;" bindtap="__e"><view class="{{['img-box',index==currentIndex?'img-box-active':'img-box']}}" style="{{'margin:'+(0)+';'+('transform:'+(index!=currentIndex?'translateY(-22px)':'translateY(0rpx)')+';')}}"><image style="height:100%;width:100%;" src="{{image.url}}" mode="scaleToFill"></image></view><block wx:if="{{index==currentIndex}}"><view><view data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" class="{{[!btnCanotUse?'':'disabled']}}" bindtap="__e"><text style="color:#fff;font-size:20rpx;">{{!isEdit?'编辑':'保存'}}</text></view></view></block></view></block><view data-event-opts="{{[['tap',[['chooseFile',['$event']]]]]}}" class="add-img" style="{{'transform:'+($root.g0==0?'translateY(0rpx)':'translateY(-50rpx)')+';'}}" bindtap="__e">+</view></scroll-view></view></view></view><cover-view class="mini-btn" size="mini" data-event-opts="{{[['tap',[['e1',['$event']]]]]}}" bindtap="__e">{{!isEdit?'打开编辑模式':'关闭并保存'}}</cover-view><view><view class="tools"><view data-event-opts="{{[['tap',[['e2',['$event']]]]]}}" class="{{['tools_item',!btnCanotUse?'':'disabled']}}" bindtap="__e"><image src="/static/fileupload/time.svg"></image><block wx:if="{{!dateTimeInfo.hash}}"><text>当前时间</text></block><block wx:if="{{dateTimeInfo.hash}}"><text>清除时间</text></block></view><view data-event-opts="{{[['tap',[['e3',['$event']]]]]}}" class="{{['tools_item',!btnCanotUse?'':'disabled']}}" bindtap="__e"><image src="/static/fileupload/text.svg"></image><text>文字</text></view><view data-event-opts="{{[['tap',[['e4',['$event']]]]]}}" class="{{['tools_item',!btnCanotUse?'':'disabled']}}" bindtap="__e"><image src="/static/fileupload/refresh.svg"></image><text>旋转</text></view><view data-event-opts="{{[['tap',[['e5',['$event']]]]]}}" class="{{['tools_item',canDraw?'disabled':'']}}" bindtap="__e"><image src="/static/fileupload/tailor.svg"></image><block wx:if="{{!isClip}}"><text>裁剪</text></block><block wx:if="{{isClip}}"><text>确认裁剪</text></block></view><view class="tools_item"><block wx:if="{{canDraw}}"><view class="tools_item_strokesetting"><view class="tools_item_strokesetting_item"><view class="tools_item_strokesetting_item_selector" style="height:40rpx;"><block wx:for="{{colorList}}" wx:for-item="item" wx:for-index="__i0__"><view data-event-opts="{{[['tap',[['e6',['$event']]]]]}}" data-event-params="{{({item})}}" class="{{['colorbox',item==strokeInfo.color?'tools_item_strokesetting_item_selector_active':'']}}" style="{{'background:'+(item!='clear'?item:'radial-gradient(#555555 1px, white 1px) repeat')+';'+('background-size:'+('4px 4px')+';')}}" bindtap="__e"></view></block></view><view style="height:40rpx;"><slider value="{{strokeInfo.weight}}" min="{{2}}" max="{{15}}" block-size="18" show-value="{{true}}" data-event-opts="{{[['change',[['sliderChange',['$event']]]]]}}" bindchange="__e"></slider></view></view></view></block><image class="{{[isClip?'disabledClip':'']}}" src="/static/fileupload/brush.svg" data-event-opts="{{[['tap',[['e7',['$event']]]]]}}" bindtap="__e"></image><text data-event-opts="{{[['tap',[['e8',['$event']]]]]}}" class="{{[isClip?'disabledClip':'']}}" bindtap="__e">{{canDraw?'停止涂鸦':'涂鸦'}}</text></view><view data-event-opts="{{[['tap',[['e9',['$event']]]]]}}" class="{{['tools_item',!btnCanotUse?'':'disabled']}}" bindtap="__e"><image src="/static/fileupload/right.svg"></image><text>{{!isEdit?'编辑':'保存'}}</text></view><view data-event-opts="{{[['tap',[['e10',['$event']]]]]}}" class="{{['tools_item',!btnCanotUse?'':'disabled']}}" bindtap="__e"><image src="/static/fileupload/close.svg"></image><text>删除</text></view></view></view><uni-popup class="vue-ref" style="z-index:2000;" vue-id="17ad4f1e-1" type="bottom" border-radius="20rpx 20rpx 0 0" background-color="rgb(230 230 230)" data-ref="popup" bind:__l="__l" vue-slots="{{['default']}}"><view style="height:240rpx;border-radius:20rpx 20rpx 0 0;background-color:rgb(230 230 230);padding-top:20rpx;position:relative;"><view class="tools_item_strokesetting_item_selector" style="height:40rpx;"><block wx:for="{{colorList}}" wx:for-item="item" wx:for-index="__i1__"><view data-event-opts="{{[['tap',[['e11',['$event']]]]]}}" data-event-params="{{({item})}}" class="{{['colorbox',item==textInfo.color?'tools_item_strokesetting_item_selector_active':'']}}" style="{{'background:'+(item!='clear'?item:'radial-gradient(#555555 1px, white 1px) repeat')+';'+('background-size:'+('4px 4px')+';')}}" bindtap="__e"></view></block></view><input style="padding-left:30rpx;margin-top:30rpx;" type="text" placeholder="请输入文字" data-event-opts="{{[['input',[['__set_model',['$0','text','$event',[]],['textInfo']]]]]}}" value="{{textInfo.text}}" bindinput="__e"/><view style="display:flex;justify-content:space-between;position:absolute;bottom:20rpx;width:100%;padding:0 30rpx;"><view></view><view><image style="width:40rpx;" src="/static/fileupload/match.svg" mode="widthFix" data-event-opts="{{[['tap',[['confirmText',['$event']]]]]}}" bindtap="__e"></image></view></view></view></uni-popup></view>