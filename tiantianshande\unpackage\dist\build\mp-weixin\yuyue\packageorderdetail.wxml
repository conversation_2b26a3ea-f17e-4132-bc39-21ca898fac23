<block wx:if="{{!loading&&orderDetail.id}}"><view class="container"><view class="status-section" style="{{'background:'+('linear-gradient(90deg,'+themeColor+' 0%,rgba('+themeColorRgb+',0.8) 100%)')+';'}}"><view class="status-text">{{orderDetail.status_text}}</view><block wx:if="{{orderDetail.status===0}}"><view class="status-desc">请在指定时间内完成支付</view></block><block wx:if="{{orderDetail.status===1&&orderDetail.expires_time_format}}"><view class="status-desc">{{"有效期至: "+orderDetail.expires_time_format}}</view></block><block wx:if="{{orderDetail.status===1&&orderDetail.status_text==='已过期'}}"><view class="status-desc">此套餐已过期，无法使用</view></block><block wx:if="{{orderDetail.status===1&&!orderDetail.expires_time_format&&orderDetail.status_text!=='已过期'}}"><view class="status-desc">{{'套餐有效期: '+(orderDetail.valid_days||365)+'天'}}</view></block></view><block wx:if="{{$root.g0}}"><view class="items-card"><view class="card-title">{{"套餐包含服务 (共 "+$root.g1+" 项)"}}</view><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="service-item-detail"><image class="service-pic-detail" src="{{item.m0}}" mode="aspectFill"></image><view class="service-info-detail"><view class="service-name-detail">{{item.$orig.service_name||item.$orig.product_name}}</view><view class="service-times-detail"><text>{{"总次数: "+(item.$orig.buy_times||item.$orig.total_num)}}</text><text class="remain-detail" style="{{((item.$orig.remain_times||item.$orig.remain_num)<=0?'color:#ccc':'color:#ff9900')}}">{{"剩余: "+(item.$orig.remain_times||item.$orig.remain_num)}}</text></view></view><block wx:if="{{item.m1}}"><button class="use-btn" style="{{'background:'+(themeColor)+';'}}" size="mini" data-event-opts="{{[['tap',[['gotoUseService',[item.$orig.service_id||item.$orig.product_id]]]]]}}" bindtap="__e">去使用</button></block><block wx:else><block wx:if="{{orderDetail.status===1&&(item.$orig.remain_times||item.$orig.remain_num)<=0}}"><button class="use-btn disabled" size="mini">已用完</button></block><block wx:else><block wx:if="{{orderDetail.status===1&&orderDetail.status_text==='已过期'}}"><button class="use-btn disabled" size="mini">已过期</button></block></block></block></view></block></view></block><view class="info-card"><view class="card-title">订单信息</view><view class="info-item"><text class="label">订单编号:</text><text class="value">{{orderDetail.ordernum||orderDetail.id}}</text><button class="copy-btn" size="mini" data-event-opts="{{[['tap',[['copyText',[orderDetail.ordernum||orderDetail.id]]]]]}}" bindtap="__e">复制</button></view><view class="info-item"><text class="label">下单时间:</text><text class="value">{{orderDetail.createtime}}</text></view><block wx:if="{{orderDetail.pay_time}}"><view class="info-item"><text class="label">支付时间:</text><text class="value">{{orderDetail.pay_time}}</text></view></block><view class="info-item"><text class="label">联系方式:</text><text class="value">{{orderDetail.linkman+" "+orderDetail.tel}}</text></view><view class="info-item"><text class="label">支付金额:</text><text class="value" style="{{'color:'+(themeColor)+';'}}">{{"￥"+orderDetail.total_price}}</text></view><block wx:if="{{orderDetail.remark}}"><view class="info-item"><text class="label">订单备注:</text><text class="value remark-value">{{orderDetail.remark}}</text></view></block></view><block wx:if="{{$root.g2}}"><view class="records-card"><view class="card-title">使用记录</view><block wx:for="{{orderDetail.use_records}}" wx:for-item="record" wx:for-index="index" wx:key="index"><view class="record-item"><view class="record-info"><view class="record-service-name">{{record.product_name||'未知服务'}}</view><view class="record-time">{{record.use_time}}</view></view><view class="record-status">已使用</view></view></block></view></block><block wx:if="{{orderDetail.refund_status&&orderDetail.refund_status>0}}"><view class="refund-card"><view class="card-title">退款信息</view><view class="info-item"><text class="label">退款状态:</text><text class="value">{{orderDetail.refund_status_text}}</text></view><block wx:if="{{orderDetail.refund_reason}}"><view class="info-item"><text class="label">退款原因:</text><text class="value">{{orderDetail.refund_reason}}</text></view></block><block wx:if="{{orderDetail.refund_time}}"><view class="info-item"><text class="label">申请时间:</text><text class="value">{{orderDetail.refund_time}}</text></view></block><block wx:if="{{orderDetail.refund_check_time}}"><view class="info-item"><text class="label">审核时间:</text><text class="value">{{orderDetail.refund_check_time}}</text></view></block><block wx:if="{{orderDetail.refund_check_reason}}"><view class="info-item"><text class="label">审核备注:</text><text class="value">{{orderDetail.refund_check_reason}}</text></view></block><block wx:if="{{orderDetail.refund_money}}"><view class="info-item"><text class="label">退款金额:</text><text class="value" style="{{'color:'+(themeColor)+';'}}">{{"￥"+orderDetail.refund_money}}</text></view></block></view></block><view class="bottom-actions"><block wx:if="{{orderDetail.status===0}}"><button data-event-opts="{{[['tap',[['cancelOrder',['$0'],['orderDetail.id']]]]]}}" class="action-button plain" bindtap="__e">取消订单</button></block><block wx:if="{{orderDetail.status===0}}"><button data-event-opts="{{[['tap',[['gotoPay',['$0','$1'],['orderDetail.payorderid','orderDetail.id']]]]]}}" class="action-button primary" style="{{'background:'+(themeColor)+';'}}" bindtap="__e">立即支付</button></block><block wx:if="{{$root.m2}}"><button data-event-opts="{{[['tap',[['applyRefund',['$0','$1'],['orderDetail.id','orderDetail.total_price']]]]]}}" class="action-button plain" bindtap="__e">申请退款</button></block><block wx:if="{{$root.m3}}"><button data-event-opts="{{[['tap',[['deleteOrder',['$0'],['orderDetail.id']]]]]}}" class="action-button plain" bindtap="__e">删除订单</button></block></view><uni-popup class="vue-ref" vue-id="5b21c846-1" type="dialog" data-ref="refundPopup" bind:__l="__l" vue-slots="{{['default']}}"><uni-popup-dialog vue-id="{{('5b21c846-2')+','+('5b21c846-1')}}" mode="input" title="申请退款" placeholder="请输入退款原因(选填)" before-close="{{true}}" data-event-opts="{{[['^confirm',[['confirmRefund']]],['^close',[['closeRefundPopup']]]]}}" bind:confirm="__e" bind:close="__e" bind:__l="__l"></uni-popup-dialog></uni-popup></view></block><block wx:else><block wx:if="{{loading}}"><view class="loading-container"><text>加载中...</text></view></block><block wx:else><view class="empty-container"><text>未找到订单信息</text></view></block></block>