<view class="container"><block wx:if="{{isload}}"><block><view class="expressinfo"><view class="head"><view class="f1"><image src="{{pre_url+'/static/img/feiji.png'}}"></image></view><view class="f2"><view class="t1">快递公司：<text style="color:#333;">{{express_com}}</text></view><view class="t2">快递单号：<text style="color:#333;">{{express_no}}</text></view></view></view><view class="content"><block wx:for="{{datalist}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="{{['item '+(index==0?'on':'')]}}"><view class="f1"><image src="{{'/static/img/dot'+(index==0?'2':'1')+'.png'}}"></image></view><view class="f2"><text class="t2">{{item.time}}</text><text class="t1">{{item.context}}</text></view></view></block><block wx:if="{{nodata}}"><nodata vue-id="03a36404-1" text="暂未查找到物流信息" bind:__l="__l"></nodata></block></view></view></block></block><view class="tobuy flex-x-center flex-y-center" style="{{'background:'+($root.m0)+';'}}" data-url="mail" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">我要寄件</view><block wx:if="{{loading}}"><loading vue-id="03a36404-2" bind:__l="__l"></loading></block><dp-tabbar vue-id="03a36404-3" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="03a36404-4" data-ref="popmsg" bind:__l="__l"></popmsg></view>