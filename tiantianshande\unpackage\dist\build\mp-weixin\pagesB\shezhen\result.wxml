<view class="result-container data-v-8042e254"><view class="result-header data-v-8042e254"><view class="header-content data-v-8042e254"><view class="result-image-section data-v-8042e254"><view class="image-wrapper data-v-8042e254"><image class="result-image data-v-8042e254" src="{{resultData.image}}" mode="aspectFit" data-event-opts="{{[['error',[['onImageError',['$event']]]]]}}" binderror="__e"></image><view class="analysis-badge data-v-8042e254"><text class="badge-text data-v-8042e254">AI分析完成</text></view></view></view><view class="result-summary data-v-8042e254"><view class="summary-header data-v-8042e254"><text class="summary-title data-v-8042e254">舌诊分析报告</text><text class="summary-date data-v-8042e254">{{currentDate}}</text></view><view class="health-score data-v-8042e254"><view class="score-container data-v-8042e254"><view class="score-circle data-v-8042e254"><text class="score-number data-v-8042e254">{{resultData.score}}</text><text class="score-label data-v-8042e254">分</text></view><view class="score-details data-v-8042e254"><text class="score-desc data-v-8042e254">综合健康评分</text><view class="{{['score-level','data-v-8042e254',scoreLevelClass]}}"><text class="level-text data-v-8042e254">{{scoreLevelText}}</text></view></view></view></view></view></view></view><view class="diagnosis-section data-v-8042e254"><view class="section-header data-v-8042e254"><text class="section-title data-v-8042e254">主要诊断结果</text></view><view class="diagnosis-cards data-v-8042e254"><block wx:for="{{diagnosisCards}}" wx:for-item="card" wx:for-index="index" wx:key="index"><view class="diagnosis-card data-v-8042e254"><view class="card-content data-v-8042e254"><view class="card-header data-v-8042e254"><text class="card-icon data-v-8042e254">{{card.icon}}</text><text class="card-title data-v-8042e254">{{card.title}}</text></view><view class="card-body data-v-8042e254"><text class="card-value data-v-8042e254">{{card.value}}</text><text class="card-desc data-v-8042e254">{{card.description}}</text></view><view class="{{['card-indicator','data-v-8042e254',card.status]}}"></view></view></view></block></view></view><view class="analysis-section data-v-8042e254"><view class="section-header data-v-8042e254"><text class="section-title data-v-8042e254">详细分析报告</text></view><view class="analysis-tabs data-v-8042e254"><view data-event-opts="{{[['tap',[['switchTab',['features']]]]]}}" class="{{['tab-item','data-v-8042e254',(activeTab==='features')?'active':'']}}" bindtap="__e"><text class="tab-text data-v-8042e254">舌象特征</text></view><view data-event-opts="{{[['tap',[['switchTab',['health']]]]]}}" class="{{['tab-item','data-v-8042e254',(activeTab==='health')?'active':'']}}" bindtap="__e"><text class="tab-text data-v-8042e254">健康状况</text></view><view data-event-opts="{{[['tap',[['switchTab',['tcm']]]]]}}" class="{{['tab-item','data-v-8042e254',(activeTab==='tcm')?'active':'']}}" bindtap="__e"><text class="tab-text data-v-8042e254">中医理论</text></view></view><view class="tab-content-wrapper data-v-8042e254"><block wx:if="{{activeTab==='features'}}"><view class="content-panel features-panel data-v-8042e254"><view class="feature-grid data-v-8042e254"><block wx:for="{{tongueFeatures}}" wx:for-item="feature" wx:for-index="index" wx:key="index"><view class="feature-card data-v-8042e254"><view class="feature-header data-v-8042e254"><text class="feature-icon data-v-8042e254">{{feature.icon}}</text><text class="feature-name data-v-8042e254">{{feature.name}}</text><text class="feature-value data-v-8042e254">{{feature.value}}</text></view><text class="feature-desc data-v-8042e254">{{feature.description}}</text><view class="feature-progress data-v-8042e254"><view class="progress-bar data-v-8042e254"><view class="progress-fill data-v-8042e254" style="{{'width:'+(feature.percentage+'%')+';'}}"></view></view><text class="progress-text data-v-8042e254">{{feature.percentage+"%"}}</text></view></view></block></view></view></block><block wx:if="{{activeTab==='health'}}"><view class="content-panel health-panel data-v-8042e254"><view class="health-indicators data-v-8042e254"><block wx:for="{{healthIndicators}}" wx:for-item="indicator" wx:for-index="index" wx:key="index"><view class="indicator-card data-v-8042e254"><view class="indicator-header data-v-8042e254"><text class="indicator-icon data-v-8042e254">{{indicator.icon}}</text><view class="indicator-info data-v-8042e254"><text class="indicator-name data-v-8042e254">{{indicator.name}}</text><text class="indicator-level data-v-8042e254">{{indicator.levelText}}</text></view></view><view class="indicator-progress data-v-8042e254"><view class="progress-track data-v-8042e254"><view class="progress-fill data-v-8042e254" style="{{'width:'+(indicator.percentage+'%')+';'}}"></view></view><text class="progress-text data-v-8042e254">{{indicator.percentage+"%"}}</text></view><text class="indicator-desc data-v-8042e254">{{indicator.description}}</text></view></block></view></view></block><block wx:if="{{activeTab==='tcm'}}"><view class="content-panel tcm-panel data-v-8042e254"><view class="tcm-analysis data-v-8042e254"><block wx:for="{{tcmAnalysis}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="tcm-card data-v-8042e254"><view class="tcm-header data-v-8042e254"><text class="tcm-icon data-v-8042e254">{{item.icon}}</text><text class="tcm-title data-v-8042e254">{{item.title}}</text></view><view class="tcm-content data-v-8042e254"><text class="tcm-text data-v-8042e254">{{item.content}}</text></view><view class="tcm-tags data-v-8042e254"><block wx:for="{{item.tags}}" wx:for-item="tag" wx:for-index="tagIndex" wx:key="tagIndex"><view class="tcm-tag data-v-8042e254"><text class="tag-text data-v-8042e254">{{tag}}</text></view></block></view></view></block></view></view></block></view></view><view class="suggestions-section data-v-8042e254"><view class="section-header data-v-8042e254"><text class="section-title data-v-8042e254">个性化调理建议</text></view><view class="suggestions-grid data-v-8042e254"><block wx:for="{{suggestionCategories}}" wx:for-item="suggestion" wx:for-index="index" wx:key="index"><view class="suggestion-card data-v-8042e254"><view class="suggestion-header data-v-8042e254"><text class="suggestion-icon data-v-8042e254">{{suggestion.icon}}</text><text class="suggestion-title data-v-8042e254">{{suggestion.title}}</text></view><view class="suggestion-content data-v-8042e254"><block wx:for="{{suggestion.items}}" wx:for-item="item" wx:for-index="itemIndex" wx:key="itemIndex"><view class="suggestion-item data-v-8042e254"><text class="suggestion-text data-v-8042e254">{{"• "+item}}</text></view></block></view><view class="suggestion-footer data-v-8042e254"><text class="suggestion-priority data-v-8042e254">{{suggestion.priority}}</text></view></view></block></view></view><view class="action-section data-v-8042e254"><view class="action-container data-v-8042e254"><view class="action-buttons data-v-8042e254"><button data-event-opts="{{[['tap',[['goBack',['$event']]]]]}}" class="action-btn outline data-v-8042e254" bindtap="__e"><text class="btn-text data-v-8042e254">返回</text></button><button data-event-opts="{{[['tap',[['retakeTest',['$event']]]]]}}" class="{{['action-btn','outline','data-v-8042e254',(isRetaking)?'loading':'']}}" bindtap="__e"><text class="btn-text data-v-8042e254">{{isRetaking?'拍摄中...':'重新拍照'}}</text></button><button data-event-opts="{{[['tap',[['shareReport',['$event']]]]]}}" class="{{['action-btn','primary','data-v-8042e254',(isSharing)?'loading':'']}}" bindtap="__e"><view class="btn-content data-v-8042e254"><text class="btn-icon data-v-8042e254">📤</text><text class="btn-text data-v-8042e254">{{isSharing?'分享中...':'分享'}}</text></view></button></view></view></view></view>