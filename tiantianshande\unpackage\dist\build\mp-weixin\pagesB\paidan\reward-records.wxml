<view class="container"><block wx:if="{{isload}}"><block><view class="header-container"><view class="config-selector"><picker value="{{configIndex}}" range="{{configOptions}}" range-key="name" data-event-opts="{{[['change',[['onConfigChange',['$event']]]]]}}" bindchange="__e"><view class="picker-display"><text>{{selectedConfig.name||'全部活动'}}</text><text class="picker-arrow">▼</text></view></picker></view><view class="filter-tabs"><block wx:for="{{statusTabs}}" wx:for-item="tab" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['changeStatus',['$0'],[[['statusTabs','',index,'value']]]]]]]}}" class="{{['tab-item',(currentStatus==tab.value)?'active':'']}}" bindtap="__e"><text>{{tab.label}}</text></view></block></view></view><view class="content-container"><scroll-view class="scroll-view" scroll-y="true" refresher-enabled="true" refresher-triggered="{{refreshing}}" data-event-opts="{{[['scrolltolower',[['loadmore',['$event']]]],['refresherrefresh',[['onRefresh',['$event']]]]]}}" bindscrolltolower="__e" bindrefresherrefresh="__e"><view class="record-list"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="record-item"><view class="record-header"><view class="activity-info"><text class="activity-name">{{item.$orig.config_name}}</text><text class="record-time">{{item.$orig.createtime_text}}</text></view><view class="{{['status-badge',item.$orig.status==0?'pending':item.$orig.status==1?'claimed':'expired']}}"><text>{{item.m0}}</text></view></view><view class="record-content"><view class="reward-info"><view class="reward-item"><text class="label">奖励类型：</text><text class="value">{{item.$orig.reward_type_text}}</text></view><view class="reward-item"><text class="label">奖励金额：</text><text class="value amount" style="color:#FF5722;">{{item.$orig.reward_amount}}</text></view><block wx:if="{{item.$orig.deduct_contribution>0}}"><view class="reward-item"><text class="label">扣除贡献值：</text><text class="value deduct">{{"-"+item.$orig.deduct_contribution}}</text></view></block><block wx:if="{{item.$orig.position_info}}"><view class="reward-item"><text class="label">点位信息：</text><text class="value">{{item.$orig.position_info}}</text></view></block></view><block wx:if="{{item.$orig.status==0}}"><view class="action-buttons"><view data-event-opts="{{[['tap',[['claimReward',['$0'],[[['recordList','',index]]]]]]]}}" class="btn-claim" style="background:linear-gradient(90deg, #FF5722 0%, rgba(255,87,34,0.8) 100%);" bindtap="__e"><text>立即领取</text></view></view></block></view></view></block></view><block wx:if="{{$root.g0}}"><view class="empty-state"><image class="empty-icon" src="/static/img/empty-reward.png"></image><view class="empty-text">暂无奖励记录</view></view></block><block wx:if="{{$root.g1>0}}"><view class="load-more"><block wx:if="{{loading}}"><text>加载中...</text></block><block wx:else><block wx:if="{{!hasMore}}"><text>没有更多了</text></block><block wx:else><text>上拉加载更多</text></block></block></view></block></scroll-view></view></block></block><block wx:if="{{loading}}"><loading vue-id="5b5f0daf-1" loadstyle="left:62.5%" bind:__l="__l"></loading></block><dp-tabbar vue-id="5b5f0daf-2" opt="{{opt}}" data-event-opts="{{[['^getmenuindex',[['getmenuindex']]]]}}" bind:getmenuindex="__e" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="5b5f0daf-3" data-ref="popmsg" bind:__l="__l"></popmsg></view>