<view class="container"><block wx:if="{{isload}}"><block><view class="{{['order-status-block','status-bg-'+info.status]}}"><view class="status-text">{{info.status_text}}</view><block wx:if="{{info.status==1}}"><view class="status-desc">{{"下次服务日期："+(info.next_service_date||'暂无安排')}}</view></block><block wx:else><block wx:if="{{info.status==2}}"><view class="status-desc">服务已暂停，可随时恢复</view></block><block wx:else><block wx:if="{{info.status==3}}"><view class="status-desc">您的周期服务已全部完成</view></block><block wx:else><block wx:if="{{info.status==4}}"><view class="status-desc">服务已取消</view></block></block></block></block></view><view class="detail-card"><view class="card-title"><text>服务地址</text></view><view class="address-content"><view class="user-info">{{info.linkman+" "+info.tel}}</view><view class="address-text">{{info.address}}</view></view></view><view class="detail-card"><view class="card-title"><text>服务信息</text><view class="more-link" data-url="{{'/yuyue/cycle/productDetail?id='+info.product_id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">查看详情 ></view></view><view class="service-header"><image class="service-image" src="{{info.product_pic}}" mode="aspectFill"></image><view class="service-title">{{info.product_name}}</view></view><view class="info-list"><view class="info-item"><text class="label">单期价格</text><text class="value price">{{"￥"+info.price}}</text></view><view class="info-item"><text class="label">订单总价</text><text class="value price">{{"￥"+(info.total_price||info.price*info.total_period)}}</text></view><view class="info-item"><text class="label">总期数</text><text class="value">{{info.total_period+"期"}}</text></view><view class="info-item"><text class="label">已完成</text><text class="value">{{info.served_period+"期"}}</text></view><view class="info-item"><text class="label">服务周期</text><text class="value">{{info.period_type_text||'-'}}</text></view><view class="info-item"><text class="label">开始日期</text><text class="value">{{info.start_date||'-'}}</text></view><block wx:if="{{info.end_date}}"><view class="info-item"><text class="label">结束日期</text><text class="value">{{info.end_date}}</text></view></block></view></view><view class="detail-card"><view class="card-title"><text>服务计划</text><block wx:if="{{$root.g0>3}}"><view class="more-link" data-url="{{'/yuyue/cycle/planList?id='+info.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">查看全部</view></block></view><view class="stage-list"><block wx:for="{{$root.l0}}" wx:for-item="stage" wx:for-index="index" wx:key="index"><block><view class="stage-item"><view class="stage-header"><view class="stage-number">{{"第 "+(stage.period_num||index+1)+" 期"}}</view><view class="{{['stage-status','status-'+stage.status]}}">{{stage.status_text}}</view></view><view class="stage-content"><block wx:if="{{stage.scheduled_date}}"><view class="stage-date">{{"计划日期："+stage.scheduled_date}}</view></block><block wx:if="{{stage.stage_worker_name}}"><view class="stage-worker">{{"服务人员："+stage.stage_worker_name}}</view></block></view></view></block></block><block wx:if="{{$root.g1===0}}"><view class="no-data">暂无服务计划</view></block></view></view><view class="detail-card"><view class="card-title"><text>订单信息</text></view><view class="info-list"><view class="info-item"><text class="label">订单编号</text><view class="value-group"><text class="value order-num">{{info.ordernum}}</text><text class="copy-btn" data-text="{{info.ordernum}}" data-event-opts="{{[['tap',[['copy',['$event']]]]]}}" bindtap="__e">复制</text></view></view><block wx:if="{{info.createtime}}"><view class="info-item"><text class="label">下单时间</text><text class="value">{{$root.m0}}</text></view></block><block wx:if="{{info.pay_status==1}}"><view class="info-item"><text class="label">支付方式</text><text class="value">{{info.pay_type||'线上支付'}}</text></view></block><block wx:if="{{info.pay_time}}"><view class="info-item"><text class="label">支付时间</text><text class="value">{{$root.m1}}</text></view></block><block wx:if="{{info.remark}}"><view class="info-item"><text class="label">订单备注</text><text class="value">{{info.remark}}</text></view></block></view></view><block wx:if="{{info.status==1||info.status==2}}"><view class="bottom-bar"><view class="{{['btn',info.status==1?'btn-pause':'btn-resume']}}" data-id="{{info.id}}" data-event-opts="{{[['tap',[['togglePause',['$event']]]]]}}" catchtap="__e">{{''+(info.status==1?'暂停服务':'恢复服务')+''}}</view><view class="btn btn-cancel" data-id="{{info.id}}" data-event-opts="{{[['tap',[['cancelOrder',['$event']]]]]}}" catchtap="__e">取消服务</view></view></block></block></block><block wx:if="{{loading}}"><loading vue-id="06026ea0-1" bind:__l="__l"></loading></block><dp-tabbar vue-id="06026ea0-2" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="06026ea0-3" data-ref="popmsg" bind:__l="__l"></popmsg></view>