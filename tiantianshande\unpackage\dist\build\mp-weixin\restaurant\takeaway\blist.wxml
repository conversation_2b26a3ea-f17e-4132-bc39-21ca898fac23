<view class="container"><block wx:if="{{isload}}"><block><view class="search-container"><view class="topsearch flex-y-center"><view class="f1 flex-y-center"><image class="img" src="/static/img/search_ico.png"></image><input placeholder="搜索你感兴趣的商家" placeholder-style="font-size:24rpx;color:#C2C2C2" confirm-type="search" data-event-opts="{{[['confirm',[['search',['$event']]]]]}}" value="{{keyword}}" bindconfirm="__e"/></view></view><view class="search-navbar"><view class="search-navbar-item" style="{{(field=='juli'?'color:'+$root.m0:'')}}" data-field="juli" data-order="asc" data-event-opts="{{[['tap',[['sortClick',['$event']]]]]}}" catchtap="__e">距离最近</view><view class="search-navbar-item" style="{{(field=='comment_score'?'color:'+$root.m1:'')}}" data-field="comment_score" data-order="desc" data-event-opts="{{[['tap',[['sortClick',['$event']]]]]}}" catchtap="__e">评分排序</view><view class="search-navbar-item" data-field="sales" data-order="{{order=='asc'?'desc':'asc'}}" data-event-opts="{{[['tap',[['sortClick',['$event']]]]]}}" catchtap="__e"><text style="{{(field=='sales'?'color:'+$root.m2:'')}}">销量排序</text><text class="iconfont iconshangla" style="{{(field=='sales'&&order=='asc'?'color:'+$root.m3:'')}}"></text><text class="iconfont icondaoxu" style="{{(field=='sales'&&order=='desc'?'color:'+$root.m4:'')}}"></text></view><view data-event-opts="{{[['tap',[['showDrawer',['showRight']]]]]}}" class="search-navbar-item flex-x-center flex-y-center" catchtap="__e">筛选<text class="{{['iconfont iconshaixuan '+(showfilter?'active':'')]}}"></text></view></view></view><uni-drawer class="vue-ref" vue-id="0b548b6c-1" mode="right" width="{{280}}" data-ref="showRight" data-event-opts="{{[['^change',[['change',['$event','showRight']]]]]}}" bind:change="__e" bind:__l="__l" vue-slots="{{['default']}}"><view class="filter-scroll-view"><scroll-view class="filter-scroll-view-box" scroll-y="true"><view class="search-filter"><view class="filter-title">筛选</view><view class="filter-content-title">商家分类</view><view class="search-filter-content"><view class="filter-item" style="{{(catchecid==oldcid?'color:'+$root.m5+';background:rgba('+$root.m6+',0.1)':'')}}" data-cid="{{oldcid}}" data-event-opts="{{[['tap',[['cateClick',['$event']]]]]}}" catchtap="__e">全部</view><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><view class="filter-item" style="{{(catchecid==item.$orig.id?'color:'+item.m7+';background:rgba('+item.m8+',0.1)':'')}}" data-cid="{{item.$orig.id}}" data-event-opts="{{[['tap',[['cateClick',['$event']]]]]}}" catchtap="__e">{{item.$orig.name}}</view></block></block></view><view class="search-filter-btn"><view data-event-opts="{{[['tap',[['filterReset',['$event']]]]]}}" class="btn" bindtap="__e">重置</view><view data-event-opts="{{[['tap',[['filterConfirm',['$event']]]]]}}" class="btn2" style="{{'background:'+($root.m9)+';'}}" bindtap="__e">确定</view></view></view></scroll-view></view></uni-drawer><view class="ind_business"><view class="ind_buslist" id="datalist"><block wx:for="{{$root.l1}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><view data-url="{{'index?bid='+item.$orig.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="ind_busbox flex1 flex-row"><view class="ind_buspic flex0"><image src="{{item.$orig.logo}}"></image></view><view><view class="bus_title">{{item.$orig.name}}</view><view class="bus_sales">{{"销量："+item.$orig.sales}}</view><view class="bus_score"><block wx:for="{{[0,1,2,3,4]}}" wx:for-item="item2" wx:for-index="index2" wx:key="index2"><image class="img" src="{{'/static/img/star'+(item.$orig.comment_score>item2?'2':'')+'.png'}}"></image></block><view class="txt">{{item.$orig.comment_score+"分"}}</view></view><view class="bus_address"><text class="x1">{{item.$orig.address}}</text><text class="x2">{{item.$orig.juli}}</text></view><block wx:if="{{item.g0>0}}"><view class="prolist"><block wx:for="{{item.$orig.prolist}}" wx:for-item="item2" wx:for-index="index2" wx:key="index2"><view class="product" data-url="{{'product?id='+item2.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e"><image class="f1" src="{{item2.pic}}"></image><view class="f2">{{"￥"+item2.sell_price}}</view></view></block></view></block></view></view></view></block></block><block wx:if="{{nomore}}"><nomore vue-id="0b548b6c-2" bind:__l="__l"></nomore></block><block wx:if="{{nodata}}"><nodata vue-id="0b548b6c-3" bind:__l="__l"></nodata></block></view></view></block></block><block wx:if="{{loading}}"><loading vue-id="0b548b6c-4" bind:__l="__l"></loading></block><dp-tabbar vue-id="0b548b6c-5" opt="{{opt}}" bind:__l="__l"></dp-tabbar></view>