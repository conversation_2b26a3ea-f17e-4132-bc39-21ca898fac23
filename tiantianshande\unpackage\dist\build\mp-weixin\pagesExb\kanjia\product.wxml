<view><block wx:if="{{isload}}"><block><view class="container"><view class="swiper-container"><swiper class="swiper" indicator-dots="{{false}}" autoplay="{{true}}" interval="{{5000}}" data-event-opts="{{[['change',[['swiperChange',['$event']]]]]}}" bindchange="__e"><block wx:for="{{product.pics}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><swiper-item class="swiper-item"><view class="swiper-item-view"><image class="img" src="{{item}}" mode="widthFix"></image></view></swiper-item></block></block></swiper><view class="imageCount">{{current+1+"/"+$root.g0}}</view></view><view class="kanjia_title"><view class="f1"><view class="t1">砍价最低</view><view class="t2"><text style="font-size:40rpx;font-weight:bold;">{{product.min_price}}</text>元可拿</view><view class="t3">{{product.sales+"人已砍价成功"}}</view></view><view class="f3"><view class="t1">{{product.starttime>nowtime?'距活动开始还有':'距活动结束还剩'}}</view><view class="t2" id="djstime"><text class="djsspan">{{djsday}}</text>天<text class="djsspan">{{djshour}}</text>:<text class="djsspan">{{djsmin}}</text>:<text class="djsspan">{{djssec}}</text></view></view></view><view class="header"><view class="title"><view class="lef"><text>{{product.name}}</text></view><view data-event-opts="{{[['tap',[['shareClick',['$event']]]]]}}" class="share" bindtap="__e"><image src="/static/img/share.png"></image><text>分享</text></view></view><view class="sales_stock"><view class="f1">{{"原价：￥"+product.sell_price+''}}</view><view class="f2">{{"已砍走"+product.sales+"件 剩余"+product.stock+"件"}}</view></view></view><block wx:if="{{product.saleing>0}}"><view class="joinlist"><block wx:for="{{joinlist}}" wx:for-item="join" wx:for-index="index" wx:key="index"><view class="t1"><image src="{{join.headimg}}"></image></view></block><block wx:if="{{product.saleing>7}}"><view class="t1"><image src="/static/img/moreuser.png"></image></view></block><view class="t2">{{product.saleing+"人正在参加"}}</view></view></block><block wx:if="{{shopset.showjd==1}}"><view class="shop"><image class="p1" src="{{business.logo}}"></image><view class="p2 flex1"><view class="t1">{{business.name}}</view><view class="t2">{{business.desc}}</view></view><button class="p4" style="{{'background:'+('linear-gradient(90deg,'+$root.m0+' 0%,rgba('+$root.m1+',0.8) 100%)')+';'}}" data-url="{{product.bid==0?'/pages/index/index':'/pagesExt/business/index?id='+product.bid}}" data-opentype="reLaunch" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">进入店铺</button></view></block><view class="detail_title"><view class="t1"></view><view class="t2"></view><view class="t0">商品描述</view><view class="t2"></view><view class="t1"></view></view><view class="detail"><dp vue-id="03eb5c36-1" pagecontent="{{pagecontent}}" bind:__l="__l"></dp></view><view style="width:100%;height:70px;"></view><block wx:if="{{product.status==1}}"><view class="{{['bottombar','flex-row',menuindex>-1?'tabbarbot':'notabbarbot']}}"><view class="f1"><block wx:if="{{kfurl!='contact::'}}"><view class="item" data-url="{{kfurl}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image class="img" src="/static/img/kefu.png"></image><view class="t1">客服</view></view></block><block wx:else><button class="item" open-type="contact"><image class="img" src="/static/img/kefu.png"></image><view class="t1">客服</view></button></block><view data-event-opts="{{[['tap',[['shareClick',['$event']]]]]}}" class="item flex1" bindtap="__e"><image class="img" src="/static/img/share2.png"></image><view class="t1">分享</view></view><view data-event-opts="{{[['tap',[['addfavorite',['$event']]]]]}}" class="item" bindtap="__e"><image class="img" src="/static/img/shoucang.png"></image><view class="t1">{{isfavorite?'已收藏':'收藏'}}</view></view></view><view class="op"><block wx:if="{{imJoin==0}}"><block><block wx:if="{{product.starttime>nowtime}}"><view class="tobuy" style="{{'background:#aaa;'+('background:'+($root.m2)+';')}}">活动未开始</view></block><block wx:else><block wx:if="{{product.endtime<nowtime}}"><view class="tobuy" style="{{'background:#aaa;'+('background:'+($root.m3)+';')}}">活动已结束</view></block><block wx:else><view data-event-opts="{{[['tap',[['joinin',['$event']]]]]}}" class="tobuy" style="{{'background:'+($root.m4)+';'}}" bindtap="__e">立即参与砍价</view></block></block></block></block><block wx:else><block><view data-event-opts="{{[['tap',[['joinin',['$event']]]]]}}" class="tobuy" style="{{'background:'+($root.m5)+';'}}" bindtap="__e">查看我的砍价</view></block></block></view></view></block></view><block wx:if="{{sharetypevisible}}"><view class="popup__container"><view data-event-opts="{{[['tap',[['handleClickMask',['$event']]]]]}}" class="popup__overlay" catchtap="__e"></view><view class="popup__modal" style="height:320rpx;min-height:320rpx;"><view class="popup__content"><view class="sharetypecontent"><block wx:if="{{$root.m6=='app'}}"><view data-event-opts="{{[['tap',[['shareapp',['$event']]]]]}}" class="f1" bindtap="__e"><image class="img" src="/static/img/weixin.png"></image><text class="t1">分享给好友</text></view></block><block wx:else><block wx:if="{{$root.m7=='mp'}}"><view data-event-opts="{{[['tap',[['sharemp',['$event']]]]]}}" class="f1" bindtap="__e"><image class="img" src="/static/img/weixin.png"></image><text class="t1">分享给好友</text></view></block><block wx:else><block wx:if="{{$root.m8=='h5'}}"><view data-event-opts="{{[['tap',[['sharemp',['$event']]]]]}}" class="f1" bindtap="__e"><image class="img" src="/static/img/weixin.png"></image><text class="t1">分享给好友</text></view></block><block wx:else><button class="f1" open-type="share"><image class="img" src="/static/img/weixin.png"></image><text class="t1">分享给好友</text></button></block></block></block><view data-event-opts="{{[['tap',[['showPoster',['$event']]]]]}}" class="f2" bindtap="__e"><image class="img" src="/static/img/sharepic.png"></image><text class="t1">生成分享图片</text></view></view></view></view></view></block><block wx:if="{{showposter}}"><view class="posterDialog"><view class="main"><view data-event-opts="{{[['tap',[['posterDialogClose',['$event']]]]]}}" class="close" bindtap="__e"><image class="img" src="/static/img/close.png"></image></view><view class="content"><image class="img" src="{{posterpic}}" mode="widthFix" data-url="{{posterpic}}" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image></view></view></view></block></block></block><block wx:if="{{loading}}"><loading vue-id="03eb5c36-2" bind:__l="__l"></loading></block><dp-tabbar vue-id="03eb5c36-3" opt="{{opt}}" data-event-opts="{{[['^getmenuindex',[['getmenuindex']]]]}}" bind:getmenuindex="__e" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="03eb5c36-4" data-ref="popmsg" bind:__l="__l"></popmsg></view>