<view class="container"><block wx:if="{{isload}}"><block><form report-submit="true" data-event-opts="{{[['submit',[['formSubmit',['$event']]]],['reset',[['formReset',['$event']]]]]}}" bindsubmit="__e" bindreset="__e"><view class="form-content"><view class="form-item"><text class="label">退款商品</text></view><view class="product"><block wx:for="{{prolist}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="content"><view data-url="{{'/shopPackage/shop/product?id='+item.proid}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image src="{{item.pic}}"></image></view><view class="detail"><text class="t1">{{item.name}}</text><text class="t2">{{item.ggname}}</text><view class="t3"><text class="x1 flex1">{{"￥"+item.sell_price}}</text><text class="x2">{{"×"+item.num}}</text></view></view></view></block></view></view><view class="card-view"><view class="card-wrap"><view class="card-title">选择类型</view><view class="info-item mt" data-url="{{'refund?orderid='+detail.id+'&type=refund'}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="t1 flex1 flex-col"><view>我要退款(无需退货)</view><view class="desc">没收到货，或与卖家协商一致不用退货只退款</view></view><image class="t3" src="/static/img/arrowright.png"></image></view><view class="info-item" data-url="{{'refund?orderid='+detail.id+'&type=return'}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="t1 flex1 flex-col"><view>我要退货退款</view><view class="desc">已收到货，需要退还收到的货物</view></view><image class="t3" src="/static/img/arrowright.png"></image></view></view></view><view class="card-view"><view class="card-wrap"><view class="info-item mt" data-url="{{'refundlist?orderid='+detail.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="t1 flex1"><view>查看本订单退款记录</view></view><image class="t3" src="/static/img/arrowright.png"></image></view></view></view></form></block></block><block wx:if="{{loading}}"><loading vue-id="5baece7d-1" bind:__l="__l"></loading></block><dp-tabbar vue-id="5baece7d-2" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="5baece7d-3" data-ref="popmsg" bind:__l="__l"></popmsg></view>