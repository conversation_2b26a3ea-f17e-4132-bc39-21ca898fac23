<view class="container"><block wx:if="{{isload}}"><block><view class="head-bg"><view class="text-center _h1">{{detail.name}}</view><block wx:if="{{detail.status==2&&order.status!=3}}"><view class="title flex _h2"><image class="image" src="{{pre_url+'/static/img/adminExt/fork.png'}}"></image><view>用餐中</view></view></block><block wx:else><block wx:if="{{detail.status==0}}"><view class="title flex _h2"><image class="image" src="{{pre_url+'/static/img/adminExt/fork.png'}}"></image><view>空闲中</view></view></block><block wx:else><block wx:if="{{order.status==3}}"><view class="title flex _h2"><image class="image" src="{{pre_url+'/static/img/adminExt/fork.png'}}"></image><view>已结算，待清台</view></view></block><block wx:else><block wx:if="{{detail.status==3}}"><view class="title flex _h2"><image class="image" src="{{pre_url+'/static/img/adminExt/fork.png'}}"></image><view>清台中</view></view></block></block></block></block></view><form data-event-opts="{{[['submit',[['subform',['$event']]]]]}}" bindsubmit="__e"><block wx:if="{{detail.status==0}}"><view class="card-view"><view class="card-wrap"><view class="card-title">餐桌信息</view><view class="info-item"><view class="t1">桌台</view><view class="t2">{{detail.name}}</view></view><view class="info-item"><view class="t1">座位数</view><view class="t2">{{detail.seat}}</view></view></view><view class="card-wrap"><view class="card-title">用餐信息</view><view class="info-item"><view class="t1">人数</view><view class="t2"><picker value="{{nindex}}" range="{{numArr}}" name="renshu" data-event-opts="{{[['change',[['numChange',['$event']]]]]}}" bindchange="__e"><view class="picker">{{numArr[nindex]}}</view></picker></view></view><view class="info-item"><view class="t1">顾客姓名</view><view class="t2"><input type="text" data-name="linkman" name="linkman" placeholder="选填"/></view></view><view class="info-item"><view class="t1">手机号</view><view class="t2"><input type="number" data-name="tel" name="tel" placeholder="选填"/></view></view><view class="info-item info-textarea"><view class="remark"><view class="t1">备注</view><view><textarea data-name="message" name="message" placeholder="如您有其他需求请填写" placeholder-style="color:#ABABABFF"></textarea></view></view></view></view></view></block><view class="btn-view button-sp-area"><block wx:if="{{detail.status==0}}"><button type="primary" form-type="submit">开始用餐</button></block></view></form><block wx:if="{{detail.status==2}}"><view class="card-view"><view class="card-wrap"><view class="content"><block wx:if="{{orderGoodsSum==0&&order.status!=3}}"><view class="item" data-url="{{'/restaurant/shop/index?type=admin&tableId='+detail.id+'&bid='+detail.bid}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image class="image" src="{{pre_url+'/static/img/adminExt/dish.png'}}"></image><text class="t3">点餐</text></view></block><block wx:if="{{orderGoodsSum>0&&order.status!=3}}"><view class="item" data-url="{{'/restaurant/shop/index?type=admin&tableId='+detail.id+'&bid='+detail.bid}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image class="image" src="{{pre_url+'/static/img/adminExt/dish.png'}}"></image><text class="t3">加菜</text></view></block><block wx:if="{{orderGoodsSum>0&&order.status!=3}}"><view class="item" data-url="{{'tableWaiterPay?id='+detail.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image class="image" src="{{pre_url+'/static/img/adminExt/money.png'}}"></image><text class="t3">结算</text></view></block><block wx:if="{{order.status!=3}}"><view class="item" data-url="{{'tableWaiter?operate=change&origin='+detail.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image class="image" src="{{pre_url+'/static/img/adminExt/change.png'}}"></image><text class="t3">换桌</text></view></block><view data-event-opts="{{[['tap',[['clean',['$event']]]]]}}" class="item" bindtap="__e"><image class="image" src="{{pre_url+'/static/img/adminExt/clean.png'}}"></image><text class="t3">清台</text></view><block wx:if="{{order.status!=3}}"><view data-event-opts="{{[['tap',[['close',['$event']]]]]}}" class="item" bindtap="__e"><image class="image" src="{{pre_url+'/static/img/adminExt/close.png'}}"></image><text class="t3">关闭</text></view></block></view></view><view class="card-wrap"><view class="card-title">餐桌信息</view><view class="info-item"><view class="t1">桌台</view><view class="t2">{{detail.name}}</view></view><view class="info-item"><view class="t1">人数/座位数</view><view class="t2">{{order.renshu+"/"+detail.seat}}</view></view><view class="info-item"><view class="t1">客户信息</view><view class="t2">{{order.linkman+" "+order.tel}}</view></view><view class="info-item"><view class="t1">时间</view><view class="t2">{{$root.m0}}</view></view><view class="info-item info-textarea"><view class="t1">备注信息</view><view class="t2">{{order.message}}</view></view></view><block wx:if="{{detail.timing_fee_type&&detail.timing_fee_type>0}}"><view class="card-wrap"><view class="card-title">计时收费</view><block wx:if="{{detail.is_start==0}}"><image class="img" src="{{pre_url+'/static/img/adminExt/start.png'}}" data-status="{{1}}" data-event-opts="{{[['tap',[['startpause',['$event']]]]]}}" bindtap="__e"></image></block><block wx:else><image class="img" src="{{pre_url+'/static/img/adminExt/pause.png'}}" data-status="{{0}}" data-event-opts="{{[['tap',[['startpause',['$event']]]]]}}" bindtap="__e"></image></block><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index"><block wx:if="{{item.g0>0}}"><view class="info-item" style="justify-content:center;"><view class="t1">{{item.$orig.start_time+" ~ "+item.$orig.end_time}}</view><view class="t2" style="flex:0.3;">{{item.$orig.num+" 分钟"}}</view></view></block></block><view class="info-item info-textarea"><view class="t1">{{detail.timing_fee_text}}</view><view class="t2">{{"￥"+detail.timing_money}}</view></view></view></block><block wx:if="{{$root.g1>0}}"><view class="card-wrap card-goods"><view class="flex"><view class="card-title">{{"已点菜品("+orderGoodsSum+")"}}</view><view class="flex1"></view><view class="btn-text" data-url="{{'shoporderEdit?id='+order.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">修改菜品</view></view><block wx:for="{{$root.l1}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="info-item"><view class="t1"><view style="line-height:60rpx;">{{item.$orig.name}}<block wx:if="{{item.$orig.ggname}}"><text>{{"["+item.$orig.ggname+(item.$orig.jltitle?item.$orig.jltitle:'')+"]"}}</text></block></view><block wx:if="{{item.g2}}"><view class="flex-col"><block wx:for="{{item.$orig.ggtext}}" wx:for-item="item2" wx:for-index="index"><block><text class="ggtext">{{item2}}</text></block></block></view></block></view><view class="t2">x<text>{{item.$orig.num}}<block wx:if="{{item.$orig.product_type&&item.$orig.product_type==1}}"><text style="font-size:20rpx;">斤</text></block></text></view><view class="t3">{{"￥"+item.$orig.real_totalprice}}</view></view></block><view class="info-item"><view class="t1">合计</view><view class="t2">{{"x"+orderGoodsSum}}</view><view class="t3">{{"￥"+order.totalprice}}</view></view></view></block><block wx:else><view class="card-wrap"><view class="info-item"><view class="t1">还未点菜</view></view></view></block></view></block><view class="btn-view button-sp-area mb"><block wx:if="{{detail.status==3}}"><button type="primary" data-event-opts="{{[['tap',[['cleanOver',['$event']]]]]}}" bindtap="__e">清理完成</button></block></view><view class="btn-view button-sp-area mb"><button class="btn-default" type="default" data-url="tableWaiter" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">返回餐桌列表</button></view></block></block><block wx:if="{{loading}}"><loading vue-id="d9bf7f50-1" bind:__l="__l"></loading></block><dp-tabbar vue-id="d9bf7f50-2" opt="{{opt}}" bind:__l="__l"></dp-tabbar></view>