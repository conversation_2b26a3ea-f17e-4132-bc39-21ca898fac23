<view class="container"><block wx:if="{{orderInfo&&orderInfo.product}}"><view class="order-info"><view class="product-info"><image src="{{orderInfo.product.pic}}" mode="aspectFill"></image><view class="info"><text class="name">{{orderInfo.product.name}}</text><text class="price" style="{{'color:'+($root.m0)+';'}}">{{"￥"+orderInfo.product.sell_price}}</text></view></view><view class="order-detail"><text>{{"订单号："+orderInfo.order_no}}</text><text>{{"支付时间："+orderInfo.pay_time}}</text></view></view></block><view class="appoint-section"><view class="section-title">选择预约时间</view><view class="date-selector"><view class="date-header"><text class="date-title">选择日期</text><text class="date-subtitle">请选择您方便的日期</text></view><view class="date-scroll-container"><scroll-view class="date-scroll" scroll-x="{{true}}" show-scrollbar="false"><block wx:for="{{$root.l0}}" wx:for-item="date" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['selectDate',['$0'],[[['dateList','',index]]]]]]]}}" class="{{['date-item',(selectedDate===date.$orig.value)?'date-active':'']}}" style="{{(selectedDate===date.$orig.value?'background:'+date.m1+'; box-shadow: 0 4rpx 10rpx rgba('+date.m2+',0.2)':'')}}" bindtap="__e"><text class="date-weekday" style="{{(selectedDate===date.$orig.value?'color:#fff':'')}}">{{date.$orig.weekday}}</text><text class="date-day" style="{{(selectedDate===date.$orig.value?'color:#fff':'')}}">{{date.$orig.day}}</text><text class="date-month" style="{{(selectedDate===date.$orig.value?'color:#fff':'')}}">{{date.$orig.month+"月"}}</text></view></block></scroll-view></view></view><block wx:if="{{selectedDate}}"><view class="time-selector"><view class="time-header"><text class="time-title">选择时间段</text><text class="time-subtitle">请选择您方便的时间</text></view><view class="time-period-tabs"><block wx:for="{{$root.l1}}" wx:for-item="period" wx:for-index="idx" wx:key="idx"><view data-event-opts="{{[['tap',[['switchPeriod',['$0'],[[['timePeriods','',idx,'value']]]]]]]}}" class="{{['time-period-tab',(activePeriod===period.$orig.value)?'period-active':'']}}" style="{{(activePeriod===period.$orig.value?'background:'+period.m3+'; box-shadow: 0 2rpx 6rpx rgba('+period.m4+',0.2); color: #fff':'')}}" bindtap="__e">{{''+period.$orig.label+''}}</view></block></view><view class="time-list"><block wx:for="{{$root.l2}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['selectTime',['$0'],[[['filteredTimeList','',index]]]]]]]}}" class="{{['time-item',(!item.$orig.available)?'disabled':'']}}" style="{{(selectedTime===item.$orig.time?'background:'+item.m5+'; transform: scale(1.05); box-shadow: 0 4rpx 10rpx rgba('+item.m6+',0.2)':'')}}" bindtap="__e"><view class="time-content"><text class="time-text" style="{{(selectedTime===item.$orig.time?'color:#fff':'')}}">{{item.$orig.time}}</text><text class="time-status" style="{{(selectedTime===item.$orig.time?'color:#fff':'')}}"><block wx:if="{{item.$orig.available}}">可预约</block><block wx:else>{{item.$orig.reason||'已约满'}}</block></text></view></view></block></view></view></block></view><view class="footer"><block wx:if="{{selectedDate&&selectedTime}}"><view class="selected-info"><text>{{"已选："+formattedSelectedDate+" "+selectedTime}}</text></view></block><button class="submit-btn" style="{{(canSubmit?'background:linear-gradient(90deg,'+$root.m7+' 0%,rgba('+$root.m8+',0.8) 100%); box-shadow: 0 4rpx 10rpx rgba('+$root.m9+',0.2)':'background:#ccc')}}" disabled="{{!canSubmit}}" data-event-opts="{{[['tap',[['submitAppoint',['$event']]]]]}}" bindtap="__e">确认预约</button></view></view>