<?php /*a:4:{s:83:"D:\qianhouduankaifabao\shangchengquan\shangcheng\app\home\coze\workflow_params.html";i:1754026148;s:73:"D:\qianhouduankaifabao\shangchengquan\shangcheng\app\home\public\css.html";i:1745486434;s:72:"D:\qianhouduankaifabao\shangchengquan\shangcheng\app\home\public\js.html";i:1745486434;s:79:"D:\qianhouduankaifabao\shangchengquan\shangcheng\app\home\public\copyright.html";i:1745486434;}*/ ?>
<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>工作流参数配置</title>
  <meta name="renderer" content="webkit">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
  <link rel="stylesheet" type="text/css" href="/static/admin/layui/css/layui.css?v=20200516" media="all">
<link rel="stylesheet" type="text/css" href="/static/admin/layui/css/modules/formSelects-v4.css?v=20200516" media="all">
<link rel="stylesheet" type="text/css" href="/static/admin/css/admin.css?v=20210826" media="all">
<link rel="stylesheet" type="text/css" href="/static/admin/css/font-awesome.min.css?v=20200516" media="all">
<link rel="stylesheet" type="text/css" href="/static/admin/webuploader/webuploader.css?v=<?php echo time(); ?>" media="all">
<link rel="stylesheet" type="text/css" href="/static/imgsrc/designer.css?v=20220803" media="all">
<link rel="stylesheet" type="text/css" href="/static/fonts/iconfont.css?v=20201218" media="all">
</head>
<body>
  <div class="layui-fluid">
    <div class="layui-row layui-col-space15">
        <div class="layui-card layui-col-md12">
          <div class="layui-card-header">
            <i class="fa fa-cogs"></i> 工作流参数配置 - <?php echo $workflow['name']; ?>
            <div class="layui-btn-group" style="float: right;">
              <button class="layui-btn layui-btn-sm" onclick="addParam()">
                <i class="layui-icon layui-icon-add-1"></i>添加参数
              </button>
              <button class="layui-btn layui-btn-primary layui-btn-sm" onclick="previewForm()">
                <i class="layui-icon layui-icon-survey"></i>预览表单
              </button>
            </div>
          </div>
          <div class="layui-card-body" pad15>
            
            <!-- 工具栏 -->
            <div class="layui-form layui-border-box layui-table-view" lay-filter="LAY-table-1">
                <div class="layui-table-tool">
                    <div class="layui-table-tool-temp">
                        <div class="layui-inline">
                            <button class="layui-btn layui-btn-primary layui-btn-sm" id="refreshTable">
                                <i class="layui-icon layui-icon-refresh"></i>刷新
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 数据表格 -->
            <table class="layui-hide" id="table" lay-filter="table"></table>
            
          </div>
        </div>
    </div>
  </div>
  
  <script type="text/javascript" src="/static/admin/layui/layui.all.js?v=20210222"></script>
<script type="text/javascript" src="/static/admin/layui/lay/modules/formSelects-v4.js"></script>
<script type="text/javascript" src="/static/admin/js/jquery-ui.min.js?v=20200228"></script>
<script type="text/javascript" src="/static/admin/ueditor/ueditor.js?v=20220707"></script>
<script type="text/javascript" src="/static/admin/ueditor/135editor.js?v=20200228"></script>
<script type="text/javascript" src="/static/admin/webuploader/webuploader.js?v=20200620"></script>
<script type="text/javascript" src="/static/admin/js/qrcode.min.js?v=20200228"></script>
<script type="text/javascript" src="/static/admin/js/dianda.js?v=2022"></script>
  
  <script type="text/html" id="table-useradmin-admin">
    <div class="layui-btn-container">
        <a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
        <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">删除</a>
    </div>
  </script>

  <script>
  layui.use(['table', 'form', 'layer'], function(){
      var table = layui.table;
      var form = layui.form;
      var layer = layui.layer;
      var $ = layui.jquery;
      
      // 渲染表格
      var tableIns = table.render({
          elem: '#table',
          url: '<?php echo url("workflowParams"); ?>/workflow_id/<?php echo $workflowId; ?>',
          toolbar: '#toolbarDemo',
          defaultToolbar: ['filter', 'exports', 'print'],
          cols: [[
              {field: 'id', title: 'ID', width: 80, sort: true},
              {field: 'param_key', title: '参数键名', width: 150},
              {field: 'param_name', title: '显示名称', width: 150},
              {field: 'param_type_text', title: '参数类型', width: 120},
              {field: 'default_value', title: '默认值', width: 150},
              {field: 'is_required_text', title: '必填', width: 80},
              {field: 'sort_order', title: '排序', width: 80},
              {field: 'status_text', title: '状态', width: 80},
              {field: 'description', title: '说明', width: 200},
              {title: '操作', width: 120, align: 'center', toolbar: '#table-useradmin-admin'}
          ]],
          page: true,
          limit: 20,
          limits: [10, 20, 50, 100],
          loading: true,
          text: {
              none: '暂无参数配置'
          }
      });

      // 监听工具条
      table.on('tool(table)', function(obj){
          var data = obj.data;
          if(obj.event === 'edit'){
              editParam(data);
          } else if(obj.event === 'del'){
              deleteParam(data);
          }
      });

      // 刷新表格
      $('#refreshTable').on('click', function(){
          tableIns.reload();
      });

      // 全局函数
      window.addParam = function() {
          showParamForm();
      };

      window.editParam = function(data) {
          showParamForm(data);
      };

      window.deleteParam = function(data) {
          layer.confirm('确定要删除这个参数配置吗？', {icon: 3, title:'提示'}, function(index){
              var loadIndex = layer.load(2, {content: '正在删除...'});
              
              $.post('<?php echo url("delWorkflowParam"); ?>', {id: data.id}, function(res){
                  layer.close(loadIndex);
                  if(res.code == 1){
                      layer.msg('删除成功', {icon: 1});
                      tableIns.reload();
                  } else {
                      layer.msg(res.msg, {icon: 2});
                  }
                  layer.close(index);
              });
          });
      };

      window.previewForm = function() {
          // 获取参数配置并预览表单
          $.get('<?php echo url("getWorkflowParams"); ?>/workflow_id/<?php echo $workflowId; ?>', function(res){
              if(res.code == 1){
                  showPreviewForm(res.data);
              } else {
                  layer.msg(res.msg, {icon: 2});
              }
          });
      };

      // 显示参数配置表单
      function showParamForm(data) {
          var title = data ? '编辑参数' : '添加参数';
          var content = buildParamFormHtml(data);
          
          layer.open({
              type: 1,
              title: title,
              content: content,
              area: ['600px', '700px'],
              btn: ['保存', '取消'],
              yes: function(index) {
                  saveParam(index, data);
              }
          });
          
          // 重新渲染表单
          form.render();
      }

      // 构建参数表单HTML
      function buildParamFormHtml(data) {
          data = data || {};
          var html = '<form class="layui-form" style="padding: 20px;">';
          
          if(data.id) {
              html += '<input type="hidden" name="id" value="' + data.id + '">';
          }
          
          html += '<div class="layui-form-item">';
          html += '<label class="layui-form-label">参数键名</label>';
          html += '<div class="layui-input-block">';
          html += '<input type="text" name="param_key" lay-verify="required" placeholder="如：user_input" class="layui-input" value="' + (data.param_key || '') + '">';
          html += '</div></div>';
          
          html += '<div class="layui-form-item">';
          html += '<label class="layui-form-label">显示名称</label>';
          html += '<div class="layui-input-block">';
          html += '<input type="text" name="param_name" lay-verify="required" placeholder="如：用户输入" class="layui-input" value="' + (data.param_name || '') + '">';
          html += '</div></div>';
          
          html += '<div class="layui-form-item">';
          html += '<label class="layui-form-label">参数类型</label>';
          html += '<div class="layui-input-block">';
          html += '<select name="param_type" lay-verify="required">';
          html += '<option value="">请选择类型</option>';
          html += '<option value="text"' + (data.param_type == 'text' ? ' selected' : '') + '>文本</option>';
          html += '<option value="number"' + (data.param_type == 'number' ? ' selected' : '') + '>数字</option>';
          html += '<option value="textarea"' + (data.param_type == 'textarea' ? ' selected' : '') + '>多行文本</option>';
          html += '<option value="select"' + (data.param_type == 'select' ? ' selected' : '') + '>选择</option>';
          html += '<option value="image"' + (data.param_type == 'image' ? ' selected' : '') + '>图片</option>';
          html += '<option value="file"' + (data.param_type == 'file' ? ' selected' : '') + '>文件</option>';
          html += '<option value="date"' + (data.param_type == 'date' ? ' selected' : '') + '>日期</option>';
          html += '<option value="switch"' + (data.param_type == 'switch' ? ' selected' : '') + '>开关</option>';
          html += '</select>';
          html += '</div></div>';
          
          html += '<div class="layui-form-item">';
          html += '<label class="layui-form-label">默认值</label>';
          html += '<div class="layui-input-block">';
          html += '<input type="text" name="default_value" placeholder="默认值" class="layui-input" value="' + (data.default_value || '') + '">';
          html += '</div></div>';
          
          html += '<div class="layui-form-item">';
          html += '<label class="layui-form-label">输入提示</label>';
          html += '<div class="layui-input-block">';
          html += '<input type="text" name="placeholder" placeholder="输入提示文字" class="layui-input" value="' + (data.placeholder || '') + '">';
          html += '</div></div>';
          
          html += '<div class="layui-form-item">';
          html += '<label class="layui-form-label">参数说明</label>';
          html += '<div class="layui-input-block">';
          html += '<textarea name="description" placeholder="参数说明" class="layui-textarea">' + (data.description || '') + '</textarea>';
          html += '</div></div>';
          
          html += '<div class="layui-form-item">';
          html += '<label class="layui-form-label">排序</label>';
          html += '<div class="layui-input-block">';
          html += '<input type="number" name="sort_order" placeholder="排序数字，越小越靠前" class="layui-input" value="' + (data.sort_order || 0) + '">';
          html += '</div></div>';
          
          html += '<div class="layui-form-item">';
          html += '<div class="layui-input-block">';
          html += '<input type="checkbox" name="is_required" title="必填"' + (data.is_required ? ' checked' : '') + '>';
          html += '<input type="checkbox" name="status" title="启用"' + (data.status !== 0 ? ' checked' : '') + '>';
          html += '</div></div>';
          
          html += '</form>';
          return html;
      }

      // 保存参数
      function saveParam(index, data) {
          var formData = {};
          $('.layui-form input, .layui-form select, .layui-form textarea').each(function(){
              var name = $(this).attr('name');
              var value = $(this).val();
              if($(this).attr('type') == 'checkbox'){
                  value = $(this).prop('checked') ? 1 : 0;
              }
              if(name) formData[name] = value;
          });
          
          var loadIndex = layer.load(2, {content: '正在保存...'});
          
          $.post('<?php echo url("saveWorkflowParam"); ?>', {
              info: formData,
              workflow_id: '<?php echo $workflowId; ?>'
          }, function(res){
              layer.close(loadIndex);
              if(res.code == 1){
                  layer.msg('保存成功', {icon: 1});
                  layer.close(index);
                  tableIns.reload();
              } else {
                  layer.msg(res.msg, {icon: 2});
              }
          });
      }

      // 显示预览表单
      function showPreviewForm(params) {
          var html = '<div style="padding: 20px;">';
          html += '<h3>表单预览</h3>';
          html += '<form class="layui-form">';
          
          params.forEach(function(param){
              html += '<div class="layui-form-item">';
              html += '<label class="layui-form-label">' + param.param_name;
              if(param.is_required) html += '<span style="color:red;">*</span>';
              html += '</label>';
              html += '<div class="layui-input-block">';
              
              switch(param.param_type) {
                  case 'text':
                  case 'number':
                      html += '<input type="' + param.param_type + '" placeholder="' + (param.placeholder || '') + '" class="layui-input" value="' + (param.default_value || '') + '">';
                      break;
                  case 'textarea':
                      html += '<textarea placeholder="' + (param.placeholder || '') + '" class="layui-textarea">' + (param.default_value || '') + '</textarea>';
                      break;
                  case 'select':
                      html += '<select>';
                      html += '<option value="">请选择</option>';
                      if(param.param_options && param.param_options.options) {
                          param.param_options.options.forEach(function(option){
                              html += '<option value="' + option.value + '"' + (option.value == param.default_value ? ' selected' : '') + '>' + option.label + '</option>';
                          });
                      }
                      html += '</select>';
                      break;
                  case 'image':
                      html += '<input type="text" placeholder="' + (param.placeholder || '图片链接') + '" class="layui-input" value="' + (param.default_value || '') + '">';
                      html += '<button type="button" class="layui-btn layui-btn-primary">上传图片</button>';
                      break;
                  case 'switch':
                      html += '<input type="checkbox" lay-skin="switch"' + (param.default_value ? ' checked' : '') + '>';
                      break;
                  default:
                      html += '<input type="text" placeholder="' + (param.placeholder || '') + '" class="layui-input" value="' + (param.default_value || '') + '">';
              }
              
              html += '</div>';
              if(param.description) {
                  html += '<div class="layui-form-mid layui-word-aux">' + param.description + '</div>';
              }
              html += '</div>';
          });
          
          html += '</form>';
          html += '</div>';
          
          layer.open({
              type: 1,
              title: '表单预览',
              content: html,
              area: ['600px', '500px'],
              btn: ['关闭']
          });
      }
  });
  </script>
	
</body>
</html>
