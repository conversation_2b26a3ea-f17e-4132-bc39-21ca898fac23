<view class="container"><block wx:if="{{isload}}"><block><view class="topfix"><view class="toplabel"><text class="t1">{{$root.m0+"（"+count+"）"}}</text><text class="t2">{{"预计：+"+commissionyj+"元"}}</text></view><dd-tab vue-id="022aee1c-1" itemdata="{{['所有订单','待付款','已付款','已完成','退款/售后']}}" itemst="{{['0','1','2','3','5']}}" st="{{st}}" isfixed="{{false}}" data-event-opts="{{[['^changetab',[['changetab']]]]}}" bind:changetab="__e" bind:__l="__l"></dd-tab></view><view style="margin-top:190rpx;"></view><block wx:if="{{$root.g0}}"><block><view class="content"><block wx:for="{{datalist}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="item"><view class="f1 flex"><text class="flex1">{{item.ordernum}}</text><block wx:if="{{item.dannum&&item.dannum>0}}"><text style="color:#a55;">{{"第"+item.dannum+"单"}}</text></block></view><view class="f2"><view class="t1"><text class="x1">{{item.name+" ×"+item.num}}</text><text class="x2">{{item.createtime}}</text><view class="x3"><image src="{{item.headimg}}"></image>{{item.nickname}}</view><block wx:if="{{item.order_info}}"><view class="x3"><view class="btn2" data-url="{{'/pagesExt/order/detail?id='+item.order_info.id+'&fromfenxiao=1'}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">详情</view><view data-event-opts="{{[['tap',[['logistics',['$0'],[[['datalist','',index,'order_info']]]]]]]}}" class="btn2" catchtap="__e">查看物流</view></view></block></view><view class="t2"><text class="x1">{{"+"+item.commission}}</text><block wx:if="{{item.status==1||item.status==2}}"><text class="dior-sp6 yfk">已付款</text></block><block wx:if="{{item.status==0}}"><text class="dior-sp6 dfk">待付款</text></block><block wx:if="{{item.status==3}}"><text class="dior-sp6 ywc">已完成</text></block><block wx:if="{{item.status==4}}"><text class="dior-sp6 ygb">已关闭</text></block><block wx:if="{{item.refund_money>0}}"><text class="dior-sp6 ygb">退款/售后</text></block><block wx:if="{{item.tel}}"><view data-event-opts="{{[['tap',[['contactCustomer',['$0'],[[['datalist','',index]]]]]]]}}" class="contact-btn" bindtap="__e"><image class="contact-icon" src="/static/img/phone.png"></image><text>联系客户</text></view></block></view></view></view></block></view><uni-popup class="vue-ref" vue-id="022aee1c-2" id="dialogSelectExpress" type="dialog" data-ref="dialogSelectExpress" bind:__l="__l" vue-slots="{{['default']}}"><block wx:if="{{express_content}}"><view style="background:#fff;padding:20rpx 30rpx;border-radius:10rpx;width:600rpx;"><block wx:for="{{express_content}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="sendexpress-item" style="display:flex;border-bottom:1px solid #f5f5f5;padding:20rpx 0;" data-url="{{'/pagesExt/order/logistics?express_com='+item.express_com+'&express_no='+item.express_no}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="flex1">{{item.express_com+" - "+item.express_no}}</view><image style="width:30rpx;height:30rpx;" src="/static/img/arrowright.png"></image></view></block></view></block></uni-popup></block></block></block></block><block wx:if="{{nodata}}"><nodata vue-id="022aee1c-3" bind:__l="__l"></nodata></block><block wx:if="{{nomore}}"><nomore vue-id="022aee1c-4" bind:__l="__l"></nomore></block><block wx:if="{{loading}}"><loading vue-id="022aee1c-5" bind:__l="__l"></loading></block><dp-tabbar vue-id="022aee1c-6" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="022aee1c-7" data-ref="popmsg" bind:__l="__l"></popmsg></view>