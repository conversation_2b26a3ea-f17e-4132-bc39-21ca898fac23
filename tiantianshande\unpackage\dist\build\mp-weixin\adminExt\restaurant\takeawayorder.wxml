<view class="container"><block wx:if="{{isload}}"><block><block wx:if="{{!goods_hexiao_status}}"><dd-tab vue-id="02644720-1" itemdata="{{['全部','待付款','待发货','待收货','已完成','退款']}}" itemst="{{['all','0','1','2','3','10']}}" st="{{st}}" isfixed="{{true}}" data-event-opts="{{[['^changetab',[['changetab']]]]}}" bind:changetab="__e" bind:__l="__l"></dd-tab></block><block wx:else><dd-tab vue-id="02644720-2" itemdata="{{['全部','待付款','待发货','已接单','待收货','已完成','退款']}}" itemst="{{['all','0','1','12','2','3','10']}}" st="{{st}}" isfixed="{{true}}" data-event-opts="{{[['^changetab',[['changetab']]]]}}" bind:changetab="__e" bind:__l="__l"></dd-tab></block><view style="width:100%;height:100rpx;"></view><view class="topsearch flex-y-center"><view class="f1 flex-y-center"><image class="img" src="{{pre_url+'/static/img/search_ico.png'}}"></image><input placeholder="输入关键字搜索" placeholder-style="font-size:24rpx;color:#C2C2C2" data-event-opts="{{[['confirm',[['searchConfirm',['$event']]]]]}}" value="{{keyword}}" bindconfirm="__e"/></view></view><view class="order-content"><block wx:for="{{$root.l1}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><view class="order-box" data-url="{{'takeawayorderdetail?id='+item.$orig.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="head"><view>{{"订单号："+item.$orig.ordernum}}</view><view class="flex1"></view><block wx:if="{{item.$orig.status==0}}"><text class="st0">待付款</text></block><block wx:if="{{item.$orig.status==1&&item.$orig.freight_type!=1}}"><text class="st1">待发货</text></block><block wx:if="{{item.$orig.status==1&&item.$orig.freight_type==1}}"><text class="st1">待提货</text></block><block wx:if="{{item.$orig.status==2}}"><text class="st2">待收货</text></block><block wx:if="{{item.$orig.status==3}}"><text class="st3">已完成</text></block><block wx:if="{{item.$orig.status==4}}"><text class="st4">已关闭</text></block><block wx:if="{{item.$orig.status==12}}"><text class="st1">已接单</text></block></view><block wx:for="{{item.l0}}" wx:for-item="item2" wx:for-index="idx" wx:key="idx"><block><view class="content" style="{{(idx+1==item.$orig.procount?'border-bottom:none':'')}}"><view data-url="{{'/restaurant/shop/product?id='+item2.$orig.proid}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e"><image src="{{item2.$orig.pic}}"></image></view><view class="detail"><text class="t1">{{item2.$orig.name}}</text><text class="t2">{{item2.$orig.ggname+(item2.$orig.jltitle?item2.$orig.jltitle:'')}}</text><view class="t3"><block wx:if="{{item2.$orig.jlprice}}"><text class="x1 flex1">{{"￥"+item2.g0}}</text></block><block wx:else><text class="x1 flex1">{{"￥"+item2.$orig.sell_price}}</text></block><block wx:if="{{goods_hexiao_status&&(item.$orig.status==1||item.$orig.status==2||item.$orig.status==12)&&item.$orig.freight_type==1&&item2.$orig.hexiao_code&&item2.$orig.num>0}}"><text style="color:#999;"><block wx:if="{{item2.$orig.status==3}}"><block style="color:#999;">已核销</block></block><block wx:else><block style="color:#f60;">未核销</block></block></text></block><text class="x2">{{"×"+item2.$orig.num}}</text></view></view></view></block></block><view class="bottom"><text>{{"共计"+item.$orig.procount+"个菜品 实付:￥"+item.$orig.totalprice}}</text><block wx:if="{{item.$orig.refund_status==1}}"><text style="color:red;">{{'退款中￥'+item.$orig.refund_money}}</text></block><block wx:if="{{item.$orig.refund_status==2}}"><text style="color:red;">{{'已退款￥'+item.$orig.refund_money}}</text></block><block wx:if="{{item.$orig.refund_status==3}}"><text style="color:red;">退款申请已驳回</text></block></view><view class="bottom flex-y-center"><image style="width:40rpx;height:40rpx;border-radius:50%;margin-right:10rpx;" src="{{item.$orig.member.headimg}}"></image><text style="font-weight:bold;color:#333;margin-right:8rpx;">{{item.$orig.member.nickname}}</text>{{'(ID:'+item.$orig.mid+')'}}</view><block wx:if="{{goods_hexiao_status&&(item.$orig.status==12||item.$orig.status==2)&&item.$orig.tel}}"><view style="width:150rpx;text-align:center;line-height:60rpx;border-radius:8rpx;border:2rpx solid #333;" data-phone="{{item.$orig.tel}}" data-event-opts="{{[['tap',[['callphone',['$event']]]]]}}" catchtap="__e">拨打电话</view></block></view></block></block></view><block wx:if="{{nomore}}"><nomore vue-id="02644720-3" bind:__l="__l"></nomore></block><block wx:if="{{nodata}}"><nodata vue-id="02644720-4" bind:__l="__l"></nodata></block></block></block><block wx:if="{{loading}}"><loading vue-id="02644720-5" bind:__l="__l"></loading></block></view>