<view class="{{['popmsg',popmsg==1?'popmsg-show':popmsg==2?'popmsg-hide':'']}}" data-url="{{tourl}}" data-event-opts="{{[['tap',[['gotourl',['$event']]]]]}}" bindtap="__e"><block wx:if="{{logo}}"><image class="popmsg-pic flex0" src="{{logo}}"></image></block><view class="popmsg-content"><view class="popmsg-content-title">{{title}}</view><block wx:if="{{desc}}"><view class="popmsg-content-desc">{{desc}}</view></block></view><view data-event-opts="{{[['tap',[['close',['$event']]]]]}}" class="popmsg-close" catchtap="__e"><image class="popmsg-close-img" src="/static/img/close2.png"></image></view></view>