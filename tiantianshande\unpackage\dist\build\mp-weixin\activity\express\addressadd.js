(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["activity/express/addressadd"],{"40ab":function(t,e,a){"use strict";var n=a("b3d8"),i=a.n(n);i.a},"6cd4":function(t,e,a){"use strict";a.r(e);var n=a("d4ad"),i=a("c036");for(var o in i)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(o);a("40ab");var d=a("828b"),s=Object(d["a"])(i["default"],n["b"],n["c"],!1,null,null,null,!1,n["a"],void 0);e["default"]=s.exports},"9bb6":function(t,e,a){"use strict";(function(t){Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var a=getApp(),n={data:function(){return{opt:{},loading:!1,isload:!1,menuindex:-1,name:"",tel:"",area:"",address:"",longitude:"",latitude:"",regiondata:"",type:0,addressxx:"",company:"",items:[],showCompany:!1}},onLoad:function(e){this.opt=a.getopts(e),this.type=this.opt.type||0;var n=this;a.get("ApiIndex/getCustom",{},(function(e){var i=a.globalData.pre_url+"/static/area.json";e.data.includes("plug_zhiming")&&(i=a.globalData.pre_url+"/static/area_gaoxin.json"),t.request({url:i,data:{},method:"GET",header:{"content-type":"application/json"},success:function(t){n.items=t.data}})})),this.getdata()},onPullDownRefresh:function(){this.getdata()},methods:{getdata:function(){var t=this,e=t.opt.id||"";a.get("ApiIndex/getCustom",{},(function(e){e.data.includes("plug_xiongmao")&&(t.showCompany=!0)})),e?(t.loading=!0,a.get("ApiExpress/addressadd",{id:e,type:t.type},(function(e){if(t.loading=!1,t.name=e.data.name,t.tel=e.data.tel,t.area=e.data.area,t.address=e.data.address,t.longitude=e.data.longitude,t.latitude=e.data.latitude,t.company=e.data.company,e.data.province)var a=e.data.province+","+e.data.city+","+e.data.district;else a="";t.regiondata=a,t.loaded()}))):t.loaded()},regionchange:function(t){var e=t.detail.value;console.log(e[0].text+","+e[1].text+","+e[2].text),this.regiondata=e[0].text+","+e[1].text+","+e[2].text},selectzuobiao:function(){var e=this;t.chooseLocation({success:function(t){console.log(t),e.area=t.address,e.address=t.name,e.latitude=t.latitude,e.longitude=t.longitude},fail:function(e){console.log(e),"chooseLocation:fail auth deny"==e.errMsg&&a.confirm("获取位置失败，请在设置中开启位置信息",(function(){t.openSetting({})}))}})},formSubmit:function(t){var e=t.detail.value,n=this.opt.id||"",i=e.name,o=e.tel,d=this.regiondata,s=this.opt.mailtype||"";if(1==this.type){var r=this.area;if(""==r)return void a.error("请选择位置")}else{r=d;if(""==r)return void a.error("请选择省市区")}var u=e.address,c=this.longitude,l=this.latitude,p=e.company;""!=i&&""!=o&&""!=u?(a.showLoading("提交中"),a.post("ApiExpress/addressadd",{mailtype:s,type:this.type,addressid:n,name:i,tel:o,area:r,address:u,latitude:l,longitude:c,company:p},(function(t){a.showLoading(!1),0!=t.status?(a.success("保存成功"),setTimeout((function(){a.goback(!0)}),1e3)):a.alert(t.msg)}))):a.error("请填写完整信息")},delAddress:function(){var t=this.opt.id;a.confirm("确定要删除该收获地址吗?",(function(){a.showLoading("删除中"),a.post("ApiExpress/del",{addressid:t},(function(){a.showLoading(!1),a.success("删除成功"),setTimeout((function(){a.goback(!0)}),1e3)}))}))},bindPickerChange:function(t){var e=t.detail.value;this.regiondata=e},setaddressxx:function(t){this.addressxx=t.detail.value},shibie:function(){var t=this,e=t.addressxx;a.post("ApiExpress/shibie",{addressxx:e},(function(e){var n=0;e.province&&(n=1,t.regiondata=e.province+","+e.city+","+e.county),e.detail&&(n=1,t.address=e.detail),e.person&&(n=1,t.name=e.person),e.phonenum&&(n=1,t.tel=e.phonenum),0==n?a.error("识别失败"):a.success("识别完成")}))}}};e.default=n}).call(this,a("df3c")["default"])},b3d8:function(t,e,a){},c036:function(t,e,a){"use strict";a.r(e);var n=a("9bb6"),i=a.n(n);for(var o in n)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(o);e["default"]=i.a},d4ad:function(t,e,a){"use strict";a.d(e,"b",(function(){return i})),a.d(e,"c",(function(){return o})),a.d(e,"a",(function(){return n}));var n={uniDataPicker:function(){return Promise.all([a.e("common/vendor"),a.e("components/uni-data-picker/uni-data-picker")]).then(a.bind(null,"8157"))},loading:function(){return a.e("components/loading/loading").then(a.bind(null,"ceaa"))},dpTabbar:function(){return a.e("components/dp-tabbar/dp-tabbar").then(a.bind(null,"b875"))},popmsg:function(){return a.e("components/popmsg/popmsg").then(a.bind(null,"2bf2"))}},i=function(){var t=this.$createElement,e=(this._self._c,this.isload?this.t("color1"):null),a=this.isload?this.t("color1rgb"):null;this.$mp.data=Object.assign({},{$root:{m0:e,m1:a}})},o=[]},dc41:function(t,e,a){"use strict";(function(t,e){var n=a("47a9");a("06e9");n(a("3240"));var i=n(a("6cd4"));t.__webpack_require_UNI_MP_PLUGIN__=a,e(i.default)}).call(this,a("3223")["default"],a("df3c")["createPage"])}},[["dc41","common/runtime","common/vendor"]]]);