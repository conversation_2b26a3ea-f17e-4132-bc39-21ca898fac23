<view><view class="container" id="datalist"><view><view class="info-item"><block wx:if="{{couponShow}}"><view class="t1">分类名称</view></block><block wx:else><view class="t1">菜品分类</view></block><block wx:if="{{couponShow}}"><block><view class="t2">店内</view><view class="t2">外卖</view><view class="t2">预定</view><view class="t2">排序</view></block></block></view><block wx:if="{{couponShow}}"><block><block wx:for="{{datalist}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="info-item"><view class="t1">{{item.name}}<block wx:if="{{item.status==0}}"><text style="color:#DBAA83;">(隐藏)</text></block></view><view class="t2"><block wx:if="{{item.is_shop==1}}"><text>开</text></block><block wx:else><text>关</text></block></view><view class="t2"><block wx:if="{{item.is_takeaway==1}}"><text>开</text></block><block wx:else><text>关</text></block></view><view class="t2"><block wx:if="{{item.is_booking==1}}"><text>开</text></block><block wx:else><text>关</text></block></view><view class="t2">{{item.sort}}</view><image class="t3" data-url="{{'edit?id='+item.id}}" src="{{pre_url+'/static/img/arrowright.png'}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"></image></view></block></block></block><block wx:else><block><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="info-item flex" style="justify-content:space-between;"><view class="t1">{{item.$orig.name}}<block wx:if="{{item.$orig.status==0}}"><text style="color:#DBAA83;">(隐藏)</text></block></view><view data-event-opts="{{[['tap',[['couponAddChange',['$0'],[[['datalist','',index]]]]]]]}}" class="addbut" style="{{'background:'+('linear-gradient(270deg,'+item.m0+' 0%,rgba('+item.m1+',0.8) 100%)')+';'}}" bindtap="__e">添加</view></view></block></block></block></view></view><block wx:if="{{nomore}}"><nomore vue-id="8f5eaf3c-1" bind:__l="__l"></nomore></block><block wx:if="{{nodata}}"><nodata vue-id="8f5eaf3c-2" bind:__l="__l"></nodata></block></view>