<view class="box data-v-8ed1b5da"><view class="flex data-v-8ed1b5da"><view class="data-v-8ed1b5da"><image style="height:30rpx;" src="/static/img/arrow-left.png" mode="heightFix" data-event-opts="{{[['tap',[['goback',['$event']]]]]}}" bindtap="__e" class="data-v-8ed1b5da"></image></view><view data-event-opts="{{[['tap',[['goToFatie',['$event']]]]]}}" class="nextButton data-v-8ed1b5da" bindtap="__e">下一步</view></view><jack-fileupload vue-id="0fdb5bd8-1" data-ref="jackFileupload" class="data-v-8ed1b5da vue-ref" bind:__l="__l"></jack-fileupload></view>