(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["zhaopin/components/navigationBar/index"],{"0c3fb":function(t,e,a){"use strict";var n=a("60a3"),i=a.n(n);i.a},"327f":function(t,e,a){"use strict";a.r(e);var n=a("b776"),i=a.n(n);for(var o in n)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(o);e["default"]=i.a},"4efb":function(t,e,a){"use strict";a.r(e);var n=a("c1ab"),i=a("327f");for(var o in i)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(o);a("0c3fb");var r=a("828b"),u=Object(r["a"])(i["default"],n["b"],n["c"],!1,null,null,null,!1,n["a"],void 0);e["default"]=u.exports},"60a3":function(t,e,a){},b776:function(t,e,a){"use strict";(function(t){Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var a={data:function(){return{height:44,paddingTop:20,showHomeButton:!1,show:!0,navigationBarTextStyle:"black"}},props:{back:{type:Boolean,default:!0},hasHei:{type:Boolean,default:!1},zIndex:{type:Number,default:99},background:{type:String,default:"#ffffff"},placeholderBg:{type:String,default:"transparent"},color:{type:String,default:"#000000"},fontSize:{type:String,default:"16px"},title:{type:String,default:"none"},fixed:{type:Boolean,default:!0},backEvent:{type:Boolean,default:!1},backHomeEvent:{type:Boolean,default:!1}},beforeMount:function(e){var a=getCurrentPages(),n=!1;a.length<2&&a[0].route!=__wxConfig.pages[0]&&(n=!0);var i,o,r=t.getSystemInfoSync(),u=t.getMenuButtonBoundingClientRect();i=r.statusBarHeight,o=u.top+u.bottom-2*r.statusBarHeight;var f=__wxConfig.global.window.navigationBarTextStyle;this.setData({height:o,paddingTop:i,showHomeButton:n,navigationBarTextStyle:f})},methods:{navigateBack:function(){this.backEvent?this.$emit("back"):this.runBack()},runBack:function(){var e=getCurrentPages();e.length<2&&e[0].route!=__wxConfig.pages[0]?t.reLaunch({url:"/"+__wxConfig.pages[0]}):t.navigateBack({delta:1})},navigateBackHome:function(){this.backHomeEvent?this.$emit("backHome"):this.runBackHome()},runBackHome:function(){getCurrentPages()[0].route===__wxConfig.pages[0]?t.navigateBack({delta:10}):t.reLaunch({url:"/"+__wxConfig.pages[0]})},toggleShow:function(){this.show||this.setData({show:!0})},toggleHide:function(){this.show&&this.setData({show:!1})}},watch:{zIndex:{handler:function(t,e,a){if(!t){var n={};n[a[0]]=e,this.setData(n)}},immediate:!0},background:{handler:function(t,e,a){if(!t){var n={};n[a[0]]=e,this.setData(n)}},immediate:!0},placeholderBg:{handler:function(t,e,a){if(!t){var n={};n[a[0]]=e,this.setData(n)}},immediate:!0},color:{handler:function(t,e,a){if(!t){var n={};n[a[0]]=e,this.setData(n)}},immediate:!0},fontSize:{handler:function(t,e,a){if(!t){var n={};n[a[0]]=e,this.setData(n)}},immediate:!0},title:{handler:function(t,e,a){if(!t){var n={};n[a[0]]=e,this.setData(n)}},immediate:!0},fixed:{handler:function(t,e,a){if(!1!==t&&!0!==t){var n={};n[a[0]]=e,this.setData(n)}},immediate:!0},backEvent:{handler:function(t,e,a){if(!1!==t&&!0!==t){var n={};n[a[0]]=e,this.setData(n)}},immediate:!0},backHomeEvent:{handler:function(t,e,a){if(!1!==t&&!0!==t){var n={};n[a[0]]=e,this.setData(n)}},immediate:!0}}};e.default=a}).call(this,a("df3c")["default"])},c1ab:function(t,e,a){"use strict";a.d(e,"b",(function(){return n})),a.d(e,"c",(function(){return i})),a.d(e,"a",(function(){}));var n=function(){var t=this.$createElement;this._self._c},i=[]}}]);
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'zhaopin/components/navigationBar/index-create-component',
    {
        'zhaopin/components/navigationBar/index-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('df3c')['createComponent'](__webpack_require__("4efb"))
        })
    },
    [['zhaopin/components/navigationBar/index-create-component']]
]);
