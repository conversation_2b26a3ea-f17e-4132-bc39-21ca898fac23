require('../common/vendor.js');(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pagesExt/cityagent/coverage"],{"166c":function(t,n,e){"use strict";e.d(n,"b",(function(){return o})),e.d(n,"c",(function(){return a})),e.d(n,"a",(function(){return i}));var i={loading:function(){return e.e("components/loading/loading").then(e.bind(null,"ceaa"))},popmsg:function(){return e.e("components/popmsg/popmsg").then(e.bind(null,"2bf2"))}},o=function(){var t=this.$createElement,n=(this._self._c,this.provinces.length),e=this.districts.length,i=this.getColor("color1")||"#4CAF50",o=0===this.provinces.length&&0===this.districts.length;this.$mp.data=Object.assign({},{$root:{g0:n,g1:e,m0:i,g2:o}})},a=[]},"490c":function(t,n,e){"use strict";var i=e("729a"),o=e.n(i);o.a},"729a":function(t,n,e){},b265:function(t,n,e){"use strict";e.r(n);var i=e("d714"),o=e.n(i);for(var a in i)["default"].indexOf(a)<0&&function(t){e.d(n,t,(function(){return i[t]}))}(a);n["default"]=o.a},c074:function(t,n,e){"use strict";e.r(n);var i=e("166c"),o=e("b265");for(var a in o)["default"].indexOf(a)<0&&function(t){e.d(n,t,(function(){return o[t]}))}(a);e("490c");var c=e("828b"),s=Object(c["a"])(o["default"],i["b"],i["c"],!1,null,null,null,!1,i["a"],void 0);n["default"]=s.exports},cedd:function(t,n,e){"use strict";(function(t,n){var i=e("47a9");e("06e9");i(e("3240"));var o=i(e("c074"));t.__webpack_require_UNI_MP_PLUGIN__=e,n(o.default)}).call(this,e("3223")["default"],e("df3c")["createPage"])},d714:function(t,n,e){"use strict";(function(t){Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;var e=getApp(),i={data:function(){return{loading:!1,provinces:[],districts:[],expandedProvinceIndex:-1,coverage_stats:{province_count:0,city_count:0,district_count:0,orders_count:0},performance_data:[]}},onLoad:function(){this.loadCoverageData()},onPullDownRefresh:function(){this.loadCoverageData()},methods:{getColor:function(t){try{return"function"===typeof this.t?this.t(t):null}catch(n){return console.log("获取颜色失败:",n),null}},loadCoverageData:function(){var n=this;n.loading=!0,e.get("ApiCityAgent/getCoverageData",{},(function(i){n.loading=!1,t.stopPullDownRefresh(),1===i.status?(n.provinces=i.provinces||[],n.districts=i.districts||[],n.coverage_stats=i.coverage_stats||{},n.performance_data=i.performance_data||[],t.setNavigationBarTitle({title:"区域管理"})):e.error(i.msg)}))},toggleProvince:function(t){this.expandedProvinceIndex===t?(this.expandedProvinceIndex=-1,this.provinces[t].expanded=!1):(this.expandedProvinceIndex>=0&&(this.provinces[this.expandedProvinceIndex].expanded=!1),this.expandedProvinceIndex=t,this.provinces[t].expanded=!0,this.provinces[t].cities||this.loadCityData(this.provinces[t].id,t))},loadCityData:function(t,n){var i=this;e.get("ApiCityAgent/getCityData",{province_id:t},(function(t){1===t.status&&i.$set(i.provinces[n],"cities",t.cities)}))},viewCityDetail:function(t){e.goto("/pagesExt/cityagent/city-detail?city_id="+t.id)},viewDistrictDetail:function(t){e.goto("/pagesExt/cityagent/district-detail?district_id="+t.id)},applyExpansion:function(){e.goto("/pagesExt/cityagent/apply-expansion")},viewApplications:function(){e.goto("/pagesExt/cityagent/expansion-applications")},contactAdmin:function(){t.makePhoneCall({phoneNumber:"************",success:function(){console.log("拨号成功")},fail:function(){e.error("拨号失败")}})}}};n.default=i}).call(this,e("df3c")["default"])}},[["cedd","common/runtime","common/vendor"]]]);