require('../common/vendor.js');(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["adminExt/finance/transfermendianmoney"],{"0de8":function(e,t,n){"use strict";(function(e){Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=getApp(),i={data:function(){return{opt:{},loading:!1,isload:!1,menuindex:-1,userinfo:{},moneyList:[],mymoney:0,moneySelected:"",paypwd:"",paycheck:!1,mid:"",mobile:"",tourl:"/pages/my/usercenter",member_info:{},member_info2:{},pre_url:n.globalData.pre_url,money_transfer_type:[]}},onLoad:function(t){this.opt=n.getopts(t),this.mid=this.opt.mid||"",this.opt.tourl&&(this.tourl=decodeURIComponent(this.opt.tourl));e.setNavigationBarTitle({title:"转账"}),this.getdata()},onPullDownRefresh:function(){this.getdata()},methods:{switchMember:function(){this.member_info={},this.mid=""},switchMember2:function(){this.member_info2={},this.mobile=""},memberInput:function(e){this.mid=e.detail.value},changeQuery:function(e){var t=this;if(!e)return n.error("请输入会员ID");t.loading=!0,n.get("ApiMy/getMemberBase",{mid:t.mid},(function(e){t.loading=!1,1==e.status?t.member_info=e.data:n.error("未查询到此会员！")}))},changeQuery2:function(e){var t=this;if(!e)return n.error("请输入手机号");t.loading=!0,n.get("ApiMy/getMemberBase",{tel:t.mobile},(function(e){t.loading=!1,1==e.status?t.member_info2=e.data:n.error("未查询到此会员！")}))},getdata:function(){var e=this;e.loading=!0,n.get("ApiAdminFinance/transferMendianMoney",{mid:e.mid},(function(t){e.loading=!1,0!=t.status?(1==t.status&&(e.mymoney=t.mymoney,e.moneyList=t.moneyList),1==t.paycheck&&(e.paycheck=!0),t.money_transfer_type&&0==t.money_transfer_type.length&&n.alert("未设置可用的转账方式，请联系客服"),e.money_transfer_type=t.money_transfer_type,e.loaded()):n.alert(t.msg)}))},selectMoney:function(e){var t=e.currentTarget.dataset.money;this.moneySelected=t},mobileinput:function(e){var t=e.detail.value;this.mobile=t},moneyinput:function(e){parseFloat(e.detail.value)},changeradio:function(e){var t=e.currentTarget.dataset.paytype;this.paytype=t},getpwd:function(e){var t=e.detail.value;this.paypwd=t},formSubmit:function(t){var i=this,o=parseFloat(t.detail.value.money);if(i.mid>0)var a=i.mid;else a="undefined"!=typeof a?parseInt(t.detail.value.mid):t.detail.value.mid;if(""!=i.mobile)var r=i.mobile;else r=t.detail.value.mobile;var u=t.detail.value.paypwd;return"undefined"==typeof r||"undefined"==typeof a||""!=r||""!=a&&0!=a&&!isNaN(a)?("undefined"==typeof r||"undefined"!=typeof a||n.isPhone(r))&&("undefined"==typeof r||""==r||n.isPhone(r))?"undefined"==typeof a||"undefined"!=typeof r||""!=a&&0!=a&&!isNaN(a)?"undefined"!=typeof a&&a==n.globalData.mid?(n.error("不能转账给自己"),!1):isNaN(o)||o<=0?void n.error("转账金额必须大于0"):this.paycheck&&""==u?(n.error("请输入支付密码"),!1):void(o<0?n.error("转账金额必须大于0"):o>i.mymoney?n.error(this.t("余额")+"不足"):n.confirm("确定要转账吗？",(function(){n.showLoading(),n.post("ApiAdminFinance/transferMendianMoney",{money:o,mobile:r,mid:a,paypwd:u},(function(t){if(n.showLoading(!1),0!=t.status)n.success(t.msg),i.subscribeMessage((function(){setTimeout((function(){n.goto(t.url)}),1e3)}));else if(n.error(t.msg),1==t.set_paypwd)var o=setTimeout((function(){clearTimeout(o),e.navigateTo({url:"/pagesExt/my/paypwd"})}),2e3)}),"提交中")}))):(n.error("请输入接收人ID"),!1):(n.error("手机号码有误，请重填"),!1):(n.error("请输入手机号码或接收人ID"),!1)}}};t.default=i}).call(this,n("df3c")["default"])},1309:function(e,t,n){"use strict";n.r(t);var i=n("0de8"),o=n.n(i);for(var a in i)["default"].indexOf(a)<0&&function(e){n.d(t,e,(function(){return i[e]}))}(a);t["default"]=o.a},"21bf":function(e,t,n){},"5c17":function(e,t,n){"use strict";n.d(t,"b",(function(){return o})),n.d(t,"c",(function(){return a})),n.d(t,"a",(function(){return i}));var i={loading:function(){return n.e("components/loading/loading").then(n.bind(null,"ceaa"))},dpTabbar:function(){return n.e("components/dp-tabbar/dp-tabbar").then(n.bind(null,"b875"))}},o=function(){var e=this,t=e.$createElement,n=(e._self._c,e.isload&&e.member_info.id?e.t("color1"):null),i=e.isload&&!e.member_info.id?e.t("color1"):null,o=e.isload?e.t("余额"):null,a=e.isload?e.t("color1"):null;e.$mp.data=Object.assign({},{$root:{m0:n,m1:i,m2:o,m3:a}})},a=[]},"64e8":function(e,t,n){"use strict";n.r(t);var i=n("5c17"),o=n("1309");for(var a in o)["default"].indexOf(a)<0&&function(e){n.d(t,e,(function(){return o[e]}))}(a);n("84b9");var r=n("828b"),u=Object(r["a"])(o["default"],i["b"],i["c"],!1,null,null,null,!1,i["a"],void 0);t["default"]=u.exports},"84b9":function(e,t,n){"use strict";var i=n("21bf"),o=n.n(i);o.a},c494:function(e,t,n){"use strict";(function(e,t){var i=n("47a9");n("06e9");i(n("3240"));var o=i(n("64e8"));e.__webpack_require_UNI_MP_PLUGIN__=n,t(o.default)}).call(this,n("3223")["default"],n("df3c")["createPage"])}},[["c494","common/runtime","common/vendor"]]]);