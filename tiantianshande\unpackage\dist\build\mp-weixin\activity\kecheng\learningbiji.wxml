<view class="container"><block wx:if="{{isload}}"><block><view class="user-profile"><view class="user-info"><view class="avatar-container"><image class="user-avatar" src="{{userInfo.headimg||pre_url+'/static/img/default_avatar.png'}}" mode="aspectFill"></image></view><view class="user-text"><view class="user-name">{{userInfo.nickname||'微信昵称'}}</view><view class="user-welcome">欢迎回来，继续学习吧</view></view></view><view class="page-title">我的笔记</view></view><view class="search-sort-area"><view class="search-box"><input class="search-input" type="text" placeholder="搜索笔记内容/课程/章节" data-event-opts="{{[['confirm',[['onSearch',['$event']]]],['input',[['__set_model',['','keyword','$event',[]]]]]]}}" value="{{keyword}}" bindconfirm="__e" bindinput="__e"/><text data-event-opts="{{[['tap',[['onSearch',['$event']]]]]}}" class="iconfont icon-sousuo search-icon" bindtap="__e"></text></view></view><view class="notes-container"><view class="notes-list"><block wx:if="{{$root.g0}}"><block><block wx:for="{{allNotes}}" wx:for-item="note" wx:for-index="index" wx:key="id"><view data-event-opts="{{[['tap',[['viewNoteDetail',['$0'],[[['allNotes','id',note.id,'id']]]]]]]}}" class="note-item" bindtap="__e"><view class="note-header"><view class="course-info"><image class="course-pic" src="{{note.kecheng_pic}}" mode="aspectFill"></image><view class="course-detail"><text class="course-name">{{note.kecheng_name}}</text><text class="note-type">{{note.note_type_name}}</text></view></view><block wx:if="{{note.chapter_name}}"><view class="chapter-name">{{note.chapter_name}}</view></block></view><view class="note-content"><rich-text nodes="{{note.content}}"></rich-text></view><view class="note-study-info"><block wx:if="{{note.kechengset&&note.kechengset.notes_need_progress==1}}"><text class="progress">{{"学习进度: "+note.study_progress+"%"}}</text></block><block wx:if="{{note.note_time}}"><text class="time-point">{{"时间点: "+note.note_time}}</text></block></view><view class="note-footer"><view class="note-time">{{note.createtime_format}}</view><view class="note-actions"><view data-event-opts="{{[['tap',[['editNote',['$0'],[[['allNotes','id',note.id]]]]]]]}}" class="action-btn edit" catchtap="__e"><text class="iconfont icon-bianji"></text><text>编辑</text></view><view data-event-opts="{{[['tap',[['deleteNote',['$0'],[[['allNotes','id',note.id,'id']]]]]]]}}" class="action-btn delete" catchtap="__e"><text class="iconfont icon-shanchu"></text><text>删除</text></view></view></view></view></block></block></block><block wx:if="{{notesNomore}}"><nomore vue-id="b3d6eaa0-1" text="没有更多笔记了" bind:__l="__l"></nomore></block><block wx:if="{{notesNodata}}"><nodata vue-id="b3d6eaa0-2" text="暂无笔记记录" bind:__l="__l" vue-slots="{{['img']}}"><image slot="img" src="{{pre_url+'/static/img/nodata.png'}}" mode="aspectFit"></image></nodata></block></view></view></block></block><block wx:if="{{loading}}"><loading vue-id="b3d6eaa0-3" bind:__l="__l"></loading></block><dp-tabbar vue-id="b3d6eaa0-4" opt="{{opt}}" data-event-opts="{{[['^getmenuindex',[['getmenuindex']]]]}}" bind:getmenuindex="__e" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="b3d6eaa0-5" data-ref="popmsg" bind:__l="__l"></popmsg></view>