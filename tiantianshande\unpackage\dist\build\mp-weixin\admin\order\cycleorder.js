require('../common/vendor.js');(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["admin/order/cycleorder"],{"08a1":function(t,n,e){"use strict";(function(t){Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;var e=getApp(),o={data:function(){return{opt:{},loading:!1,isload:!1,menuindex:-1,st:"all",datalist:[],pagenum:1,nomore:!1,nodata:!1,keyword:""}},onLoad:function(t){this.opt=e.getopts(t),this.getdata()},onShow:function(){this.getdata()},onPullDownRefresh:function(){this.getdata()},onReachBottom:function(){this.nodata||this.nomore||(this.pagenum=this.pagenum+1,this.getdata(!0))},onNavigationBarSearchInputConfirmed:function(t){this.searchConfirm({detail:{value:t.text}})},methods:{toCycleList:function(t){var n=t.currentTarget.dataset.id,o="cycleplanlist?id="+n;e.goto(o)},toDetail:function(t){var n=t.currentTarget.dataset.id;e.goto("cycleorderdetail?id="+n)},changetab:function(n){console.log(n,"st"),this.st=n,t.pageScrollTo({scrollTop:0,duration:0}),this.getdata()},getdata:function(t){t||(this.pagenum=1,this.datalist=[]);var n=this,o=n.pagenum,a=n.st;n.nodata=!1,n.nomore=!1,n.loading=!0,e.post("ApiAdminOrder/cycleorder",{st:a,pagenum:o,keyword:n.keyword},(function(t){n.loading=!1;var e=t.datalist;if(1==o)n.datalist=e,0==e.length&&(n.nodata=!0),n.loaded();else if(0==e.length)n.nomore=!0;else{var a=n.datalist,i=a.concat(e);n.datalist=i}}))},toclose:function(t){var n=this,o=t.currentTarget.dataset.id;e.confirm("确定要关闭该订单吗?",(function(){e.showLoading("提交中"),e.post("ApiCycle/closeOrder",{orderid:o},(function(t){e.showLoading(!1),e.success(t.msg),setTimeout((function(){n.getdata()}),1e3)}))}))},todel:function(t){var n=this,o=t.currentTarget.dataset.id;e.confirm("确定要删除该订单吗?",(function(){e.showLoading("删除中"),e.post("ApiCycle/delOrder",{orderid:o},(function(t){e.showLoading(!1),e.success(t.msg),setTimeout((function(){n.getdata()}),1e3)}))}))},orderCollect:function(t){var n=this,o=t.currentTarget.dataset.id;e.confirm("确定要收货吗?",(function(){e.showLoading("提交中"),e.post("ApiCycle/orderCollect",{orderid:o},(function(t){e.showLoading(!1),e.success(t.msg),setTimeout((function(){n.getdata()}),1e3)}))}))},searchConfirm:function(t){this.keyword=t.detail.value,this.getdata(!1)}}};n.default=o}).call(this,e("df3c")["default"])},"271b":function(t,n,e){},"6df0":function(t,n,e){"use strict";e.r(n);var o=e("08a1"),a=e.n(o);for(var i in o)["default"].indexOf(i)<0&&function(t){e.d(n,t,(function(){return o[t]}))}(i);n["default"]=a.a},"7e7c":function(t,n,e){"use strict";e.d(n,"b",(function(){return a})),e.d(n,"c",(function(){return i})),e.d(n,"a",(function(){return o}));var o={ddTab:function(){return e.e("components/dd-tab/dd-tab").then(e.bind(null,"caa1"))},nomore:function(){return e.e("components/nomore/nomore").then(e.bind(null,"3892"))},nodata:function(){return e.e("components/nodata/nodata").then(e.bind(null,"101c"))},loading:function(){return e.e("components/loading/loading").then(e.bind(null,"ceaa"))},dpTabbar:function(){return e.e("components/dp-tabbar/dp-tabbar").then(e.bind(null,"b875"))},popmsg:function(){return e.e("components/popmsg/popmsg").then(e.bind(null,"2bf2"))}},a=function(){var t=this.$createElement;this._self._c},i=[]},8890:function(t,n,e){"use strict";e.r(n);var o=e("7e7c"),a=e("6df0");for(var i in a)["default"].indexOf(i)<0&&function(t){e.d(n,t,(function(){return a[t]}))}(i);e("c853");var r=e("828b"),d=Object(r["a"])(a["default"],o["b"],o["c"],!1,null,null,null,!1,o["a"],void 0);n["default"]=d.exports},c853:function(t,n,e){"use strict";var o=e("271b"),a=e.n(o);a.a},d837:function(t,n,e){"use strict";(function(t,n){var o=e("47a9");e("06e9");o(e("3240"));var a=o(e("8890"));t.__webpack_require_UNI_MP_PLUGIN__=e,n(a.default)}).call(this,e("3223")["default"],e("df3c")["createPage"])}},[["d837","common/runtime","common/vendor"]]]);