<view class="container"><block wx:if="{{isload}}"><block><block wx:if="{{psorder.status!=4}}"><map class="map" longitude="{{binfo.longitude}}" latitude="{{binfo.latitude}}" scale="14" markers="{{[{id:0,latitude:binfo.latitude,longitude:binfo.longitude,iconPath:'/static/peisong/marker_business.png',width:'44',height:'54'},{id:1,latitude:orderinfo.latitude,longitude:orderinfo.longitude,iconPath:'/static/peisong/marker_kehu.png',width:'44',height:'54'},{id:2,latitude:psuser.latitude,longitude:psuser.longitude,iconPath:'/static/peisong/marker_qishou.png',width:'44',height:'54'}]}}"></map></block><block wx:else><map class="map" longitude="{{binfo.longitude}}" latitude="{{binfo.latitude}}" scale="14" markers="{{[{id:0,latitude:binfo.latitude,longitude:binfo.longitude,iconPath:'/static/peisong/marker_business.png',width:'44',height:'54'},{id:1,latitude:orderinfo.latitude,longitude:orderinfo.longitude,iconPath:'/static/peisong/marker_kehu.png',width:'44',height:'54'}]}}"></map></block><view class="order-box"><view class="head"><block wx:if="{{psorder.status==4}}"><view class="f1"><image class="img" src="/static/peisong/ps_time.png"></image>已送达</view></block><block wx:else><block wx:if="{{psorder.leftminute>0}}"><view class="f1"><image class="img" src="/static/peisong/ps_time.png"></image><text class="t1">{{psorder.leftminute+"分钟内"}}</text>送达</view></block><block wx:else><view class="f1"><image class="img" src="/static/peisong/ps_time.png"></image>已超时<text class="t1" style="margin-left:10rpx;">{{-psorder.leftminute+"分钟"}}</text></view></block></block><view class="flex1"></view><view class="f2"><text class="t1">{{psorder.ticheng}}</text>元</view></view><view class="content" style="border-bottom:0;"><view class="f1"><view class="t1"><text class="x1">{{psorder.juli}}</text><text class="x2">{{psorder.juli_unit}}</text></view><view class="t2"><image class="img" src="/static/peisong/ps_juli.png"></image></view><view class="t3"><text class="x1">{{psorder.juli2}}</text><text class="x2">{{psorder.juli2_unit}}</text></view></view><view class="f2"><view class="t1">{{binfo.name}}</view><view class="t2">{{binfo.address}}</view><view class="t3">{{orderinfo.address}}</view><view class="t2">{{orderinfo.area}}</view></view><view data-event-opts="{{[['tap',[['daohang',['$event']]]]]}}" class="f3" catchtap="__e"><image class="img" src="{{pre_url+'/static/img/peisong/ps_daohang.png'}}"></image></view></view></view><view class="orderinfo"><view class="box-title">{{"商品清单("+orderinfo.procount+")"}}</view><block wx:for="{{prolist}}" wx:for-item="item" wx:for-index="idx" wx:key="idx"><view class="item"><text class="t1 flex1">{{item.name+" "+item.ggname}}</text><text class="t2 flex0">{{"￥"+item.sell_price+" ×"+item.num+''}}</text></view></block></view><block wx:if="{{psorder.status!=0}}"><view class="orderinfo"><view class="box-title">配送信息</view><view class="item"><text class="t1">接单时间</text><text class="t2">{{$root.m0}}</text></view><block wx:if="{{psorder.daodiantime}}"><view class="item"><text class="t1">到店时间</text><text class="t2">{{$root.m1}}</text></view></block><block wx:if="{{psorder.quhuotime}}"><view class="item"><text class="t1">取货时间</text><text class="t2">{{$root.m2}}</text></view></block><block wx:if="{{psorder.endtime}}"><view class="item"><text class="t1">送达时间</text><text class="t2">{{$root.m3}}</text></view></block></view></block><view class="orderinfo"><view class="box-title">订单信息</view><view class="item"><text class="t1">订单编号</text><text class="t2" user-select="true" selectable="true">{{orderinfo.ordernum}}</text></view><view class="item"><text class="t1">下单时间</text><text class="t2">{{$root.m4}}</text></view><view class="item"><text class="t1">支付时间</text><text class="t2">{{$root.m5}}</text></view><view class="item"><text class="t1">支付方式</text><text class="t2">{{orderinfo.paytype}}</text></view><view class="item"><text class="t1">商品金额</text><text class="t2 red">{{"¥"+orderinfo.product_price}}</text></view><view class="item"><text class="t1">实付款</text><text class="t2 red">{{"¥"+orderinfo.totalprice}}</text></view><view class="item"><text class="t1">备注</text><text class="t2 red">{{orderinfo.message?orderinfo.message:'无'}}</text></view><block wx:if="{{orderinfo.qr}}"><view class="item qr-section"><text class="t1">付款二维码</text><image class="qr-image" src="{{orderinfo.qr}}" data-event-opts="{{[['tap',[['openQR',['$event']]]]]}}" bindtap="__e"></image></view></block><block wx:if="{{orderinfo.qr}}"><view class="confirm-pay-section"><button data-event-opts="{{[['tap',[['confirmOfflinePayment',['$event']]]]]}}" class="confirm-pay-btn" bindtap="__e">确认线下支付</button></view></block><block wx:if="{{!orderinfo.qr}}"><view class="confirm-pay-section"><button class="confirm-pay-btn">本单已支付请和客户核对确认</button></view></block><view class="apply_box"><view class="apply_item" style="border-bottom:0;"><text>上传凭证(1-5张)<text style="color:red;">*</text></text></view><view class="flex" style="flex-wrap:wrap;padding-bottom:20rpx;"><block wx:for="{{pics}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="layui-imgbox"><view class="layui-imgbox-close" data-index="{{index}}" data-field="pics" data-event-opts="{{[['tap',[['removeimg',['$event']]]]]}}" bindtap="__e"><image src="/static/img/ico-del.png"></image></view><view class="layui-imgbox-img"><image src="{{item}}" data-url="{{item}}" mode="widthFix" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image></view></view></block><block wx:if="{{$root.g0<5}}"><view class="uploadbtn" style="{{('background:url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 60rpx;background-size:80rpx 80rpx;background-color:#F3F3F3;')}}" data-field="pics" data-event-opts="{{[['tap',[['uploadimg',['$event']]]]]}}" bindtap="__e"></view></block></view><input type="text" hidden="true" name="pics" maxlength="-1" value="{{$root.g1}}"/></view></view><view style="width:100%;height:180rpx;"></view><view class="bottom notabbarbot"><block wx:if="{{psorder.status!=0}}"><view class="f1" data-tel="{{orderinfo.tel}}" data-event-opts="{{[['tap',[['call',['$event']]]]]}}" bindtap="__e"><image class="img" src="/static/peisong/tel1.png"></image>联系顾客</view></block><block wx:if="{{psorder.status!=0}}"><view data-event-opts="{{[['tap',[['tuikuang']]]]}}" class="f1" bindtap="__e"><image class="img" src="/static/img/exp_shou.png"></image>操作退框</view></block><block wx:if="{{psorder.status==0}}"><view class="btn1" data-id="{{psorder.id}}" data-event-opts="{{[['tap',[['qiangdan',['$event']]]]]}}" bindtap="__e">立即抢单</view></block><block wx:if="{{psorder.status==1}}"><view class="btn1" data-id="{{psorder.id}}" data-st="4" data-event-opts="{{[['tap',[['setst',['$event']]]]]}}" bindtap="__e">我已送达</view></block><block wx:if="{{psorder.status==2}}"><view class="btn1" data-id="{{psorder.id}}" data-st="4" data-event-opts="{{[['tap',[['setst',['$event']]]]]}}" bindtap="__e">我已送达</view></block><block wx:if="{{psorder.status==3}}"><view class="btn1" data-id="{{psorder.id}}" data-st="4" data-event-opts="{{[['tap',[['setst',['$event']]]]]}}" bindtap="__e">我已送达</view></block><block wx:if="{{psorder.status==4}}"><view class="btn1" data-id="{{psorder.id}}" data-st="4" data-event-opts="{{[['tap',[['setpic',['$event']]]]]}}" bindtap="__e">一键上传</view></block></view></block></block><block wx:if="{{loading}}"><loading vue-id="1636ebe8-1" bind:__l="__l"></loading></block><dp-tabbar vue-id="1636ebe8-2" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="1636ebe8-3" data-ref="popmsg" bind:__l="__l"></popmsg></view>