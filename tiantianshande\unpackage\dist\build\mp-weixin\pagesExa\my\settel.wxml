<view class="container"><block wx:if="{{isload}}"><block><form data-event-opts="{{[['submit',[['formSubmit',['$event']]]],['reset',[['formReset',['$event']]]]]}}" bindsubmit="__e" bindreset="__e"><view class="form"><view class="form-item"><input class="input" type="text" placeholder="请输入手机号" placeholder-style="color:#BBBBBB;font-size:28rpx" name="tel" data-event-opts="{{[['input',[['__set_model',['','tel','$event',[]]],['telinput',['$event']]]]]}}" value="{{tel}}" bindinput="__e"/></view><block wx:if="{{needsms}}"><view class="form-item"><input class="input" type="text" placeholder="请输入验证码" placeholder-style="font-size:30rpx;color:#B2B5BE" name="smscode" value=""/><view data-event-opts="{{[['tap',[['smscode',['$event']]]]]}}" class="code" bindtap="__e">{{smsdjs||'获取验证码'}}</view></view></block></view><button class="set-btn" style="{{'background:'+('linear-gradient(90deg,'+$root.m0+' 0%,rgba('+$root.m1+',0.8) 100%)')+';'}}" form-type="submit">保 存</button></form></block></block><block wx:if="{{loading}}"><loading vue-id="0414b901-1" bind:__l="__l"></loading></block><dp-tabbar vue-id="0414b901-2" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="0414b901-3" data-ref="popmsg" bind:__l="__l"></popmsg></view>