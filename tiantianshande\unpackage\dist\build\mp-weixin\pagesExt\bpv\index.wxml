<view class="container"><block wx:if="{{isload}}"><block><view class="yellow-points-header"><view class="title">{{"我的"+$root.m0}}</view></view><view class="yellow-points-card" style="{{'background:'+('linear-gradient(90deg,'+$root.m1+' 0%, rgba('+$root.m2+',0.8) 100%)')+';'}}"><view class="card-item"><view class="label">当前持有</view><view class="value">{{current_amount+" "+$root.m3}}</view></view><view class="card-item"><view class="label">当前价格</view><view class="value">{{current_price+" "+$root.m4}}</view></view><view class="card-item"><view class="label">{{"我的"+$root.m5}}</view><view class="value">{{contribution}}</view></view></view><view class="yellow-points-info"><view class="info-item"><view class="info-label">兑换比例</view><view class="info-value">{{exchange_ratio+" "+$root.m6+" = 1 "+$root.m7}}</view></view><block wx:if="{{exchange_deadline>0}}"><view class="info-item"><view class="info-label">兑换截止时间</view><view class="info-value">{{exchange_deadline_format}}</view></view></block></view><view class="yellow-points-progress"><view class="progress-title"><label class="_span">奖金池进度</label><label class="_span">{{bonus_pool+"/"+target_amount+$root.m8}}</label></view><view class="progress-bar"><view class="progress-inner" style="{{'width:'+(progressPercent+'%')+';'+('background:'+($root.m9)+';')}}"></view></view></view><view class="yellow-points-actions"><button class="btn-exchange" style="{{'background:'+($root.m10)+';'}}" disabled="{{!is_exchangeable}}" data-event-opts="{{[['tap',[['exchangeYellowPoints',['$event']]]]]}}" bindtap="__e">{{"兑换"+$root.m11}}</button><button data-event-opts="{{[['tap',[['cashoutYellowPoints',['$event']]]]]}}" class="btn-cashout" style="{{'background:'+($root.m12)+';'}}" bindtap="__e">{{"提现"+$root.m13}}</button></view><view class="yellow-points-records"><view class="record-tabs"><view data-event-opts="{{[['tap',[['switchTab',['exchange']]]]]}}" class="{{['tab-item '+(activeTab==='exchange'?'active':'')]}}" style="{{(activeTab==='exchange'?'color:'+$root.m14+';border-bottom-color:'+$root.m15:'')}}" bindtap="__e">兑换记录</view><view data-event-opts="{{[['tap',[['switchTab',['cashout']]]]]}}" class="{{['tab-item '+(activeTab==='cashout'?'active':'')]}}" style="{{(activeTab==='cashout'?'color:'+$root.m16+';border-bottom-color:'+$root.m17:'')}}" bindtap="__e">提现记录</view></view><block wx:if="{{activeTab==='exchange'}}"><view class="record-list"><block wx:if="{{$root.g0>0}}"><block><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="record-item"><view class="record-left"><view class="record-title">{{item.m18+"兑换"}}</view><view class="record-time">{{item.$orig.exchange_time_format}}</view></view><view class="record-right"><view class="record-amount">{{"+"+item.$orig.amount+" "+item.m19}}</view><view class="record-desc">{{"消耗"+item.m20+": "+item.$orig.contribution}}</view></view></view></block></block></block><block wx:if="{{$root.g1===0}}"><nodata vue-id="2e3d323f-1" bind:__l="__l"></nodata></block><block wx:if="{{exchangeNomore}}"><nomore vue-id="2e3d323f-2" bind:__l="__l"></nomore></block></view></block><block wx:if="{{activeTab==='cashout'}}"><view class="record-list"><block wx:if="{{$root.g2>0}}"><block><block wx:for="{{$root.l1}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="record-item"><view class="record-left"><view class="record-title">{{item.m21+"提现"}}</view><view class="record-time">{{item.$orig.cashout_time_format}}</view></view><view class="record-right"><view class="record-amount">{{"-"+item.$orig.amount+" "+item.m22}}</view><view class="record-desc"><text>{{"提现金额: "+item.$orig.money+item.m23}}</text><text class="{{['status','status-'+item.$orig.status]}}">{{item.$orig.status_text}}</text></view></view></view></block></block></block><block wx:if="{{$root.g3===0}}"><nodata vue-id="2e3d323f-3" bind:__l="__l"></nodata></block><block wx:if="{{cashoutNomore}}"><nomore vue-id="2e3d323f-4" bind:__l="__l"></nomore></block></view></block></view></block></block><block wx:if="{{showExchangePopup}}"><view data-event-opts="{{[['tap',[['closeExchangePopup',['$event']]]]]}}" class="popup-mask" bindtap="__e"></view></block><block wx:if="{{showExchangePopup}}"><view class="exchange-popup"><view class="popup-header"><text class="popup-title">{{"兑换"+$root.m24}}</text><text data-event-opts="{{[['tap',[['closeExchangePopup',['$event']]]]]}}" class="popup-close" bindtap="__e">×</text></view><view class="popup-body"><view class="info-row"><text>{{"当前"+$root.m25+":"}}</text><text>{{contribution}}</text></view><view class="info-row"><text>兑换比例:</text><text>{{exchange_ratio+" "+$root.m26+" = 1 "+$root.m27}}</text></view><view class="input-row"><text>兑换数量:</text><input type="number" min="1" data-event-opts="{{[['input',[['__set_model',['','exchangeAmount','$event',[]]],['calcContribution',['$event']]]]]}}" value="{{exchangeAmount}}" bindinput="__e"/></view><view class="info-row highlight"><text>{{"需要"+$root.m28+":"}}</text><text>{{needContribution}}</text></view></view><view class="popup-footer"><button data-event-opts="{{[['tap',[['closeExchangePopup',['$event']]]]]}}" class="btn-cancel" bindtap="__e">取消</button><button class="btn-confirm" style="{{'background:'+($root.m29)+';'}}" disabled="{{!canExchange}}" data-event-opts="{{[['tap',[['confirmExchange',['$event']]]]]}}" bindtap="__e">确认兑换</button></view></view></block><block wx:if="{{showCashoutPopup}}"><view data-event-opts="{{[['tap',[['closeCashoutPopup',['$event']]]]]}}" class="popup-mask" bindtap="__e"></view></block><block wx:if="{{showCashoutPopup}}"><view class="cashout-popup"><view class="popup-header"><text class="popup-title">{{"提现"+$root.m30}}</text><text data-event-opts="{{[['tap',[['closeCashoutPopup',['$event']]]]]}}" class="popup-close" bindtap="__e">×</text></view><view class="popup-body"><view class="info-row"><text>当前持有:</text><text>{{current_amount+" "+$root.m31}}</text></view><view class="info-row"><text>当前价格:</text><text>{{current_price+" "+$root.m32+"/"+$root.m33}}</text></view><view class="input-row"><text>提现数量:</text><input type="number" min="1" max="{{current_amount}}" data-event-opts="{{[['input',[['__set_model',['','cashoutAmount','$event',[]]],['calcCashoutMoney',['$event']]]]]}}" value="{{cashoutAmount}}" bindinput="__e"/></view><view class="info-row highlight"><text>提现金额:</text><text>{{cashoutMoney+" "+$root.m34}}</text></view><view class="note"><text>提示: 提现申请需要管理员审核，审核通过后将发放到您的账户</text></view></view><view class="popup-footer"><button data-event-opts="{{[['tap',[['closeCashoutPopup',['$event']]]]]}}" class="btn-cancel" bindtap="__e">取消</button><button class="btn-confirm" style="{{'background:'+($root.m35)+';'}}" disabled="{{!canCashout}}" data-event-opts="{{[['tap',[['confirmCashout',['$event']]]]]}}" bindtap="__e">确认提现</button></view></view></block><block wx:if="{{loading}}"><loading vue-id="2e3d323f-5" bind:__l="__l"></loading></block><popmsg class="vue-ref" vue-id="2e3d323f-6" data-ref="popmsg" bind:__l="__l"></popmsg></view>