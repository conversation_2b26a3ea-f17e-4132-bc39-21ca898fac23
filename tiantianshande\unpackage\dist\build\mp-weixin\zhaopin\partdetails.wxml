<view class="data-v-1a9ec30e"><block wx:if="{{isload}}"><block class="data-v-1a9ec30e"><block wx:if="{{partJobVo.template&&partJobVo.template.templateId===2}}"><block class="data-v-1a9ec30e"><view class="data-v-1a9ec30e"><image class="allmessimage data-v-1a9ec30e" lazyLoad="{{true}}" mode="widthFix" src="{{partJobVo.qualityBackground}}"></image><view class="resourse data-v-1a9ec30e"><parser vue-id="43b630eb-1" html="{{partJobVo.targetUrlContent}}" class="data-v-1a9ec30e" bind:__l="__l"></parser></view></view><block wx:if="{{flag}}"><apply-button style="{{'background-color:'+($root.m0)+';'}}" vue-id="43b630eb-2" agreementVo="{{agreementVo}}" collectImg="{{collectImg}}" hasToken="{{hasToken}}" partJobVo="{{partJobVo}}" data-event-opts="{{[['^cancel',[['agreementCancelHandle']]],['^initData',[['initData']]],['^loginSuccess',[['onLoginSuccess']]],['^promptly',[['confirmReport']]]]}}" bind:cancel="__e" bind:initData="__e" bind:loginSuccess="__e" bind:promptly="__e" class="data-v-1a9ec30e" bind:__l="__l"></apply-button></block></block></block><block wx:else><block class="data-v-1a9ec30e"><block wx:if="{{!partJobVo.template||partJobVo.template.templateId!==3}}"><view class="container data-v-1a9ec30e"><view class="education-wrap data-v-1a9ec30e"><education vue-id="43b630eb-3" partJobVo="{{partJobVo}}" chosenList="{{chosenList}}" tabCurrent="{{tabCurrent}}" hasEyeAuth="{{hasEyeAuth}}" isShowAll="{{isShowAll}}" isInfoShowBtn="{{isInfoShowBtn}}" isComputedInfo="{{isComputedInfo}}" agreementVo="{{agreementVo}}" data-event-opts="{{[['^clickCompany',[['goToCompany']]]]}}" bind:clickCompany="__e" class="data-v-1a9ec30e" bind:__l="__l"></education><block wx:if="{{$root.g0}}"><job-list vue-id="43b630eb-4" isTabList="{{true}}" recommendList="{{moduleList}}" tabCurrent="{{tabCurrent}}" data-event-opts="{{[['^tabChange',[['anchorTabHandle']]]]}}" bind:tabChange="__e" class="data-v-1a9ec30e" bind:__l="__l"></job-list></block></view></view></block><apply-button style="{{'background-color:'+($root.m1)+';'}}" vue-id="43b630eb-5" agreementVo="{{agreementVo}}" baseInfo="{{jobDetailJson.baseInfo}}" collectImg="{{collectImg}}" hasToken="{{hasToken}}" partJobVo="{{partJobVo}}" data-event-opts="{{[['^cancel',[['agreementCancelHandle']]],['^collect',[['handleCollect']]],['^initData',[['initData']]],['^loginSuccess',[['onLoginSuccess']]],['^pay',[['showPayDialog']]],['^promptly',[['confirmReport']]]]}}" bind:cancel="__e" bind:collect="__e" bind:initData="__e" bind:loginSuccess="__e" bind:pay="__e" bind:promptly="__e" class="data-v-1a9ec30e" bind:__l="__l"></apply-button></block></block></block></block><block wx:if="{{visible}}"><report-dialog vue-id="43b630eb-6" agreementVo="{{agreementVo}}" authorizedKey="{{authorizedKey}}" famousJobList="{{famousList}}" fromRecommend="{{fromRecommend}}" hasLatitude="{{hasLatitude}}" isDirect="{{isDirect}}" isPartDetails="{{true}}" partJobId="{{partJobId}}" partJobVo="{{partJobVo}}" recommendTips="{{recommendTips}}" reportDialogText="{{reportDialogText}}" shareUserId="{{shareUserId}}" source="{{source}}" visible="{{visible}}" data-event-opts="{{[['^close',[['closeReportDialog']]],['^payCancel',[['onPayCancel']]],['^paySuccess',[['onPaySuccess']]]]}}" bind:close="__e" bind:payCancel="__e" bind:paySuccess="__e" class="data-v-1a9ec30e" bind:__l="__l"></report-dialog></block><block wx:if="{{isShowApplySure}}"><apply-sure-modal vue-id="43b630eb-7" locationTownName="{{locationTownName}}" nowTownName="{{nowTownName}}" data-event-opts="{{[['^onCancelBtn',[['onCancelBtn']]],['^onSureBtn',[['onSureBtn']]]]}}" bind:onCancelBtn="__e" bind:onSureBtn="__e" class="data-v-1a9ec30e" bind:__l="__l"></apply-sure-modal></block></view>