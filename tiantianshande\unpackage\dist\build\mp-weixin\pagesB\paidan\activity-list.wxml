<view class="container"><block wx:if="{{isload}}"><block><view class="search-container"><view class="title-box"><view>排单活动列表</view></view></view><view class="content-container"><scroll-view class="activity-box" scroll-y="true" data-event-opts="{{[['scrolltolower',[['scrolltolower',['$event']]]]]}}" bindscrolltolower="__e"><view class="activity-itemlist"><block wx:for="{{datalist}}" wx:for-item="item" wx:for-index="index" wx:key="id"><view class="item" data-id="{{item.id}}" data-event-opts="{{[['tap',[['toActivityDetail',['$event']]]]]}}" bindtap="__e"><view class="activity-header"><view class="activity-name">{{item.name}}</view><view class="activity-status" style="color:#FF5722;">进行中</view></view><view class="activity-info"><view class="info-row"><view class="label">复制模式：</view><view class="value">{{item.copy_mode_text}}</view></view><view class="info-row"><view class="label">财富点位：</view><view class="value">{{item.wealth_position_text}}</view></view><view class="info-row"><view class="label">奖励金额：</view><view class="value" style="color:#FF5722;">{{item.wealth_reward_amount+"元"}}</view></view></view><view class="activity-stats"><view class="stat-item"><view class="stat-value">{{item.participant_count}}</view><view class="stat-label">参与人数</view></view><view class="stat-item"><view class="stat-value" style="color:#FF5722;">{{item.my_position_count}}</view><view class="stat-label">我的点位</view></view><view class="action-btn" style="background:linear-gradient(90deg, #FF5722 0%, rgba(255,87,34,0.8) 100%);">查看详情</view></view><view class="activity-time">{{"创建时间："+item.createtime_text}}</view></view></block></view><block wx:if="{{nomore}}"><nomore vue-id="c2010b66-1" text="没有更多活动了" bind:__l="__l"></nomore></block><block wx:if="{{nodata}}"><nodata vue-id="c2010b66-2" text="暂无排单活动" bind:__l="__l"></nodata></block><view style="width:100%;height:100rpx;"></view></scroll-view></view></block></block><block wx:if="{{loading}}"><loading vue-id="c2010b66-3" loadstyle="left:62.5%" bind:__l="__l"></loading></block><dp-tabbar vue-id="c2010b66-4" opt="{{opt}}" data-event-opts="{{[['^getmenuindex',[['getmenuindex']]]]}}" bind:getmenuindex="__e" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="c2010b66-5" data-ref="popmsg" bind:__l="__l"></popmsg></view>