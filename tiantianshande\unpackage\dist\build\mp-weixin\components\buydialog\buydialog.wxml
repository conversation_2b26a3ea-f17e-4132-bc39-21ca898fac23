<view><block wx:if="{{isload}}"><view><view data-event-opts="{{[['tap',[['buydialogChange',['$event']]]]]}}" class="buydialog-mask" bindtap="__e"></view><view class="{{['buydialog',menuindex>-1?'tabbarbot':'notabbarbot']}}"><view data-event-opts="{{[['tap',[['buydialogChange',['$event']]]]]}}" class="close" bindtap="__e"><image class="image" src="/static/img/close.png"></image></view><view class="title"><image class="img" src="{{nowguige.pic||product.pic}}" data-url="{{nowguige.pic||product.pic}}" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image><block wx:if="{{controller=='ApiRestaurantShop'||controller=='ApiRestaurantTakeaway'}}"><view><view class="price" style="{{'color:'+($root.m0)+';'}}">{{"￥"+totalprice}}</view></view></block><block wx:else><block wx:if="{{product.is_member_yh==0&&product.is_newcustom==1}}"><view><block wx:if="{{product.price_type!=1||nowguige.sell_price>0}}"><view class="price" style="{{'color:'+($root.m1)+';'}}">{{"￥"+product.yh_price}}<block wx:if="{{nowguige.market_price>nowguige.sell_price}}"><text class="t2">{{"￥"+nowguige.market_price}}</text></block></view></block></view></block><block wx:else><view><view class="price" style="{{'color:'+($root.m2)+';'}}">{{'￥'+nowguige.sell_price}}<block wx:if="{{nowguige.market_price>nowguige.sell_price}}"><text class="t2">{{"￥"+nowguige.market_price}}</text></block></view></view></block></block><block wx:if="{{product.limit_start>1}}"><text class="choosename">{{''+product.limit_start+"件起售"}}</text></block><block wx:if="{{!shopset||shopset.hide_stock!=1}}"><view class="stock">{{"库存："+nowguige.stock}}</view></block><block wx:if="{{product.limit_start<=1}}"><view class="choosename">{{"已选规格: "+nowguige.name+jltitle}}</view></block></view><block wx:if="{{showglass}}"><block><block wx:if="{{!glassrecord.id}}"><view><view class="glassinfo" data-url="/pagesExt/glass/add" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view>完善你的视力档案</view><view class="flex flex-e"><image src="../../static/img/arrowright.png"></image></view></view></view></block><block wx:else><view class="glassinfo" data-url="{{'/pagesExt/glass/index?c=1&sid='+glassrecord.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view>视力档案</view><view class="flex flex-e"><text>{{(glassrecord.type==1?'近视':'远视')+"，右眼"+glassrecord.degress_right+"度，左眼"+glassrecord.degress_left+"度"}}</text><image src="../../static/img/arrowright.png"></image></view></view></block></block></block><block wx:if="{{nowguige.balance_price}}"><view style="{{'width:94%;margin:10rpx 3%;font-size:24rpx;'+('color:'+($root.m3)+';')}}">{{"首付款金额："+nowguige.advance_price+"元，尾款金额："+nowguige.balance_price+"元"}}</view></block><view style="max-height:50vh;overflow:scroll;"><block wx:for="{{$root.l1}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="guigelist flex-col"><view class="name">{{item.$orig.title}}</view><view class="item flex flex-y-center"><block wx:for="{{item.l0}}" wx:for-item="item2" wx:for-index="index2" wx:key="index2"><block><block wx:if="{{item2.m4}}"><view class="{{['item2 '+(ggselected[item.$orig.k]==item2.$orig.k?'on':'')]}}" data-itemk="{{item.$orig.k}}" data-idx="{{item2.$orig.k}}" data-event-opts="{{[['tap',[['ggchange',['$event']]]]]}}" bindtap="__e">{{''+item2.$orig.title+''}}</view></block></block></block></view></view></block></view><block wx:if="{{$root.g0}}"><view style="max-height:50vh;overflow:scroll;"><view class="guigelist flex-col"><view class="name">加料</view><view class="item flex flex-y-center"><block wx:for="{{jialiaodata}}" wx:for-item="jlitem" wx:for-index="jlindex" wx:key="jlindex"><view data-event-opts="{{[['tap',[['jlchange',[jlindex]]]]]}}" class="{{['item2',jlitem.active?'on':'']}}" bindtap="__e">{{jlitem.jltitle}}</view></block></view></view></view></block><block wx:if="{{product.price_type==1}}"><block><button data-event-opts="{{[['tap',[['showLinkChange',['$event']]]]]}}" class="addcart" style="{{'background-color:'+($root.m5)+';'}}" bindtap="__e">{{product.xunjia_text?product.xunjia_text:'联系TA'}}</button></block></block><block wx:else><block><view class="buynum flex flex-y-center"><view class="flex1">购买数量：</view><view class="addnum"><view data-event-opts="{{[['tap',[['gwcminus',['$event']]]]]}}" class="minus" bindtap="__e"><image class="img" src="/static/img/cart-minus.png"></image></view><input class="input" type="number" data-event-opts="{{[['input',[['gwcinput',['$event']]]]]}}" value="{{gwcnum}}" bindinput="__e"/><view data-event-opts="{{[['tap',[['gwcplus',['$event']]]]]}}" class="plus" bindtap="__e"><image class="img" src="/static/img/cart-plus.png"></image></view></view></view><block wx:if="{{shopset&&shopset.showcommission==1&&nowguige.commission>0}}"><view class="tips-text" style="{{'color:'+($root.m6)+';'}}">{{"分享好友购买预计可得"+$root.m7+"："}}<text style="font-weight:bold;padding:0 2px;">{{nowguige.commission}}</text>{{nowguige.commission_desc}}</view></block><view class="op"><block wx:if="{{nowguige.stock<=0}}"><block><button class="nostock">库存不足</button></block></block><block wx:else><block><block wx:if="{{product.hide_price!=1&&btntype==0&&canaddcart}}"><button data-event-opts="{{[['tap',[['addcart',['$event']]]]]}}" class="addcart" style="{{'background-color:'+($root.m8)+';'}}" bindtap="__e">加入购物车</button></block><block wx:if="{{product.hide_price!=1&&btntype==0}}"><button data-event-opts="{{[['tap',[['tobuy',['$event']]]]]}}" class="tobuy" style="{{'background-color:'+($root.m9)+';'}}" bindtap="__e">立即购买</button></block><block wx:if="{{product.hide_price!=1&&btntype==1}}"><button data-event-opts="{{[['tap',[['addcart',['$event']]]]]}}" class="addcart" style="{{'background-color:'+($root.m10)+';'}}" bindtap="__e">确 定</button></block><block wx:if="{{product.hide_price!=1&&btntype==2}}"><button data-event-opts="{{[['tap',[['tobuy',['$event']]]]]}}" class="tobuy" style="{{'background-color:'+($root.m11)+';'}}" bindtap="__e">确 定</button></block><block wx:if="{{product.hide_price==1}}"><button data-event-opts="{{[['tap',[['hidePriceLink',['$event']]]]]}}" class="tobuy" style="{{'background-color:'+($root.m12)+';'}}" bindtap="__e">{{product.hide_price_detail_text}}</button></block></block></block></view></block></block></view></view></block><block wx:if="{{loading}}"><loading vue-id="99f8b638-1" bind:__l="__l"></loading></block></view>