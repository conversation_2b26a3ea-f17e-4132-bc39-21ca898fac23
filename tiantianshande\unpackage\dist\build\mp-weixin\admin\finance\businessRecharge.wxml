<view><block wx:if="{{isload}}"><block><view class="content"><view class="form"><view class="group"><view class="title">充值金额</view><view class="input"><input type="digit" placeholder="请输入充值金额" placeholder-style="color:#C0C4CC" data-event-opts="{{[['input',[['__set_model',['','amount','$event',[]]]]]]}}" value="{{amount}}" bindinput="__e"/></view></view><view class="group"><view class="title">支付方式</view><view class="pay-type-list"><block wx:for="{{payTypes}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['selectPayType',['$0'],[[['payTypes','',index,'value']]]]]]]}}" class="{{['pay-type-item',(pay_type===item.value)?'active':'']}}" bindtap="__e"><image class="pay-type-icon" src="{{pre_url+'/static/img/'+item.icon}}"></image><text class="pay-type-name">{{item.name}}</text></view></block></view></view><view class="group"><view class="title">支付凭证</view><view class="upload-box"><block wx:if="{{pay_evidence}}"><view data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" class="upload-item" bindtap="__e"><image class="uploaded-img" src="{{pay_evidence}}" mode="aspectFill"></image><view data-event-opts="{{[['tap',[['removeImage',['$event']]]]]}}" class="delete-btn" catchtap="__e">×</view></view></block><block wx:else><view data-event-opts="{{[['tap',[['chooseImage',['$event']]]]]}}" class="upload-btn" bindtap="__e"><image class="add-icon" src="/static/img/add.png"></image><text>上传凭证</text></view></block></view></view><view class="group"><view class="title">备注说明(选填)</view><view class="input"><textarea placeholder="请输入备注说明" placeholder-style="color:#C0C4CC" data-event-opts="{{[['input',[['__set_model',['','remark','$event',[]]]]]]}}" value="{{remark}}" bindinput="__e"></textarea></view></view></view><view class="tips"><view class="tip-item">1. 请确保填写的信息准确无误</view><view class="tip-item">2. 上传真实有效的支付凭证</view><view class="tip-item">3. 充值申请提交后将等待平台审核</view></view><view data-event-opts="{{[['tap',[['submitRecharge',['$event']]]]]}}" class="btn-submit" bindtap="__e">提交充值申请</view></view></block></block><block wx:if="{{loading}}"><loading vue-id="00fc605c-1" bind:__l="__l"></loading></block><popmsg class="vue-ref" vue-id="00fc605c-2" data-ref="popmsg" bind:__l="__l"></popmsg></view>