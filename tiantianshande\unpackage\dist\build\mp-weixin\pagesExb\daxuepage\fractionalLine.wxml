<view class="box data-v-fd177a12"><view style="font-size:30rpx;font-weight:bold;margin-bottom:30rpx;text-align:center;" class="data-v-fd177a12">选择学校分类</view><radio-group data-event-opts="{{[['change',[['radioChange',['$event']]]]]}}" class="item-box data-v-fd177a12" bindchange="__e"><view class="item data-v-fd177a12"><view class="justify-between data-v-fd177a12"><view class="data-v-fd177a12">高考</view><view class="data-v-fd177a12"><radio value="option1" checked="{{option1Checked}}" activeBorderColor="#8799fa" activeBackgroundColor="#8799fa" class="data-v-fd177a12"></radio></view></view><block wx:if="{{option1Checked}}"><view class="data-v-fd177a12"><view class="data-v-fd177a12"><view style="height:36px;line-height:36px;" class="data-v-fd177a12">请选择(二选一)</view><view class="tag-selector data-v-fd177a12"><block wx:for="{{$root.l0}}" wx:for-item="tag" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['toggleTag',['$0','1'],[[['tags','',index,'value']]]]]]]}}" class="{{['tag-item','data-v-fd177a12',(tag.g0)?'selected':'']}}" bindtap="__e">{{''+tag.$orig.text+''}}</view></block></view></view><view style="padding:14rpx;border:1px solid #ccc;background-color:#fff;border-radius:8rpx;" class="data-v-fd177a12"><input class="custom-input data-v-fd177a12" type="text" placeholder="请输入分数" data-event-opts="{{[['input',[['__set_model',['','score1','$event',[]]]]]]}}" value="{{score1}}" bindinput="__e"/></view></view></block></view><view class="item data-v-fd177a12"><view class="justify-between data-v-fd177a12"><view class="data-v-fd177a12">单招</view><view class="data-v-fd177a12"><radio value="option2" checked="{{option2Checked}}" activeBorderColor="#8799fa" activeBackgroundColor="#8799fa" class="data-v-fd177a12"></radio></view></view><block wx:if="{{option2Checked}}"><view class="data-v-fd177a12"><picker value="{{index}}" range="{{array}}" data-event-opts="{{[['change',[['pickerChange',['$event','1']]]]]}}" bindchange="__e" class="data-v-fd177a12"><view class="picker-text data-v-fd177a12">{{school?school:'请选择'}}</view></picker><view style="padding:14rpx;border:1px solid #ccc;background-color:#fff;border-radius:8rpx;" class="data-v-fd177a12"><input class="custom-input data-v-fd177a12" type="text" placeholder="请输入分数" data-event-opts="{{[['input',[['__set_model',['','score2','$event',[]]]]]]}}" value="{{score2}}" bindinput="__e"/></view></view></block></view><view class="item data-v-fd177a12"><view class="justify-between data-v-fd177a12"><view class="data-v-fd177a12">专升本</view><view class="data-v-fd177a12"><radio value="option3" checked="{{option3Checked}}" activeBorderColor="#8799fa" activeBackgroundColor="#8799fa" class="data-v-fd177a12"></radio></view></view><block wx:if="{{option3Checked}}"><view class="data-v-fd177a12"><view style="padding:14rpx;border:1px solid #ccc;background-color:#fff;border-radius:8rpx;" class="data-v-fd177a12"><input class="custom-input data-v-fd177a12" type="text" placeholder="请输入分数" data-event-opts="{{[['input',[['__set_model',['','score3','$event',[]]]]]]}}" value="{{score3}}" bindinput="__e"/></view></view></block></view></radio-group><button data-event-opts="{{[['tap',[['search',['$event']]]]]}}" class="gradient-button data-v-fd177a12" bindtap="__e">查询</button></view>