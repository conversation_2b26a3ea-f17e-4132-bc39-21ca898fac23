<view class="dp-text" style="{{'text-align:'+(params.align)+';'+('color:'+(params.color)+';')+('background:'+(params.bgpic?'url('+params.bgpic+')':params.bgcolor)+';')+('background-size:'+('100%')+';')+('margin:'+(params.margin_y*2.2+'rpx '+params.margin_x*2.2+'rpx 0')+';')+('padding:'+(params.padding_y*2.2+'rpx '+params.padding_x*2.2+'rpx')+';')+('font-weight:'+(params.fontbold?'bold':'normal')+';')}}" data-url="{{params.hrefurl}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><text style="{{'white-space:'+('pre-wrap')+';'+('letter-spacing:'+(params.letter_spacing?params.letter_spacing+'px':'normal')+';')+('font-size:'+(params.fontsize*2+'rpx')+';')+('line-height:'+(params.lineheight*2+'rpx')+';')}}">{{params.showcontent||'文本内容'}}</text></view>