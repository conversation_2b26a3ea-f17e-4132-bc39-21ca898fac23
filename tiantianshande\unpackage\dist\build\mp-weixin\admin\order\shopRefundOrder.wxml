<view class="container"><block wx:if="{{isload}}"><block><dd-tab vue-id="3ab2a665-1" itemdata="{{['全部','待审核','已同意','驳回']}}" itemst="{{['all','1','2','3']}}" st="{{st}}" isfixed="{{true}}" data-event-opts="{{[['^changetab',[['changetab']]]]}}" bind:changetab="__e" bind:__l="__l"></dd-tab><view style="width:100%;height:100rpx;"></view><view class="topsearch flex-y-center"><view class="f1 flex-y-center"><image class="img" src="/static/img/search_ico.png"></image><input placeholder="输入关键字搜索" placeholder-style="font-size:24rpx;color:#C2C2C2" data-event-opts="{{[['confirm',[['searchConfirm',['$event']]]]]}}" value="{{keyword}}" bindconfirm="__e"/></view></view><view class="order-content"><block wx:for="{{datalist}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><view class="order-box" data-url="{{'shopRefundOrderDetail?id='+item.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="head"><view>{{"退款单号："+item.refund_ordernum}}</view><view class="flex1"></view><block wx:if="{{item.refund_status==0}}"><text class="st3">取消</text></block><block wx:if="{{item.refund_status==1}}"><text class="st0">待审核</text></block><block wx:if="{{item.refund_status==2}}"><text class="st4">通过</text></block><block wx:if="{{item.refund_status==3}}"><text class="st2">驳回</text></block><block wx:if="{{item.refund_status==4}}"><text class="st2">通过,待退货</text></block></view><block wx:for="{{item.prolist}}" wx:for-item="item2" wx:for-index="idx" wx:key="idx"><block><view class="content" style="{{(idx+1==item.procount?'border-bottom:none':'')}}"><view data-url="{{'/shopPackage/shop/product?id='+item2.proid}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e"><image src="{{item2.pic}}"></image></view><view class="detail"><text class="t1">{{item2.name}}</text><text class="t2">{{item2.ggname}}</text><view class="t3"><text class="x1 flex1">{{"￥"+item2.sell_price}}</text><text class="x2">{{"×"+item2.refund_num}}</text></view></view></view></block></block><view class="bottom"><text style="margin-right:10rpx;">{{item.refund_type_label}}</text><block wx:if="{{item.refund_status==0}}"><text style="color:grey;">{{'申请退款￥'+item.refund_money}}</text></block><block wx:if="{{item.refund_status==1}}"><text style="color:red;">{{'退款中￥'+item.refund_money}}</text></block><block wx:if="{{item.refund_status==2}}"><text style="color:red;">{{'已退款￥'+item.refund_money}}</text></block><block wx:if="{{item.refund_status==3}}"><text style="color:red;">退款申请已驳回</text></block><block wx:if="{{item.refund_status==4}}"><text style="color:red;">审核通过,待退货</text></block></view><view class="bottom flex-y-center"><image style="width:40rpx;height:40rpx;border-radius:50%;margin-right:10rpx;" src="{{item.member.headimg}}"></image><text style="font-weight:bold;color:#333;margin-right:8rpx;">{{item.member.nickname}}</text>{{"(ID:"+item.mid+')'}}</view></view></block></block></view><block wx:if="{{nomore}}"><nomore vue-id="3ab2a665-2" bind:__l="__l"></nomore></block><block wx:if="{{nodata}}"><nodata vue-id="3ab2a665-3" bind:__l="__l"></nodata></block></block></block><block wx:if="{{loading}}"><loading vue-id="3ab2a665-4" bind:__l="__l"></loading></block><popmsg class="vue-ref" vue-id="3ab2a665-5" data-ref="popmsg" bind:__l="__l"></popmsg></view>