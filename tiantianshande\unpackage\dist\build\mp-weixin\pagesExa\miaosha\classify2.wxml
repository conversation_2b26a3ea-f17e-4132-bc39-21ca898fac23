<view class="container"><block wx:if="{{isload}}"><block><view hidden="{{!(!history_show)}}" class="search-navbar"><view class="search-navbar-item" style="{{(!field||field=='id'?'color:'+$root.m0:'')}}" data-field="id" data-order="desc" data-event-opts="{{[['tap',[['sortClick',['$event']]]]]}}" catchtap="__e">综合</view><view class="search-navbar-item" data-field="price" data-order="{{order=='asc'?'desc':'asc'}}" data-event-opts="{{[['tap',[['sortClick',['$event']]]]]}}" catchtap="__e"><text style="{{(field=='price'?'color:'+$root.m1:'')}}">价格</text><text class="iconfont iconshangla" style="{{(field=='price'&&order=='asc'?'color:'+$root.m2:'')}}"></text><text class="iconfont icondaoxu" style="{{(field=='price'&&order=='desc'?'color:'+$root.m3:'')}}"></text></view></view><view class="content-container"><view class="nav_right" style="width:100%;"><view class="nav_right-content"><block wx:if="{{$root.g0}}"><view class="classify-ul"><view class="flex" style="width:100%;overflow-y:hidden;overflow-x:scroll;"><view class="classify-li" style="{{(curIndex2==-1?'color:'+$root.m4+';background:rgba('+$root.m5+',0.2)':'')}}" data-id="{{clist[curTopIndex].child[curIndex].id}}" data-index="-1" data-event-opts="{{[['tap',[['changeCTab',['$event']]]]]}}" bindtap="__e">全部</view><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="idx2" wx:key="idx2"><block><view class="classify-li" style="{{(curIndex2==idx2?'color:'+item.m6+';background:rgba('+item.m7+',0.2)':'')}}" data-id="{{item.$orig.id}}" data-index="{{idx2}}" data-event-opts="{{[['tap',[['changeCTab',['$event']]]]]}}" bindtap="__e">{{item.$orig.name}}</view></block></block></view></view></block><scroll-view class="classify-box" scroll-y="true" data-event-opts="{{[['scrolltolower',[['scrolltolower',['$event']]]]]}}" bindscrolltolower="__e"><view class="product-itemlist"><block wx:for="{{$root.l1}}" wx:for-item="item" wx:for-index="index" wx:key="id"><view class="item" data-url="{{'/pagesExa/miaosha/product?id='+item.$orig.id+'&ccid='+ccid}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><block wx:if="{{item.$orig.status2==2}}"><view class="product-pic"><image class="image" style="height:150px;" src="{{item.$orig.pic}}"></image></view></block><block wx:else><block wx:if="{{item.$orig.status2!=2}}"><view class="product-pic"><image class="image" style="height:150px;" src="{{item.$orig.pic}}"></image><view style="background-color:rgba(0,0,0,.5);position:absolute;width:60%;height:60%;border-radius:50%;top:20%;left:20%;"><view style="color:#fff;text-align:center;transform:translateY(100%);font-size:26rpx;">售罄</view></view></view></block></block><view class="product-info"><view class="p1"><text>{{item.$orig.name}}</text></view><view class="t2" style="{{'color:'+(item.m8)+';'}}"></view><block wx:if="{{item.$orig.price_type!=1||item.$orig.sell_price>0}}"><view class="p2"><text class="t1" style="{{'color:'+(item.m9)+';'}}"><text style="font-size:20rpx;padding-right:1px;">￥</text>{{item.$orig.price}}</text><block wx:if="{{item.$orig.market_price*1>item.$orig.sell_price*1}}"><text class="t2">{{"￥"+item.$orig.market_price}}</text></block></view></block><block wx:if="{{item.$orig.xunjia_text&&item.$orig.price_type==1&&item.$orig.sell_price<=0}}"><view class="p2" style="height:50rpx;line-height:44rpx;"><text class="t1" style="{{'color:'+(item.m10)+';'+('font-size:'+('30rpx')+';')}}">询价</text><block wx:if="{{item.$orig.xunjia_text&&item.$orig.price_type==1}}"><block><view class="lianxi" style="{{'background:'+(item.m11)+';'}}" data-lx_name="{{item.$orig.lx_name}}" data-lx_bid="{{item.$orig.lx_bid}}" data-lx_bname="{{item.$orig.lx_bname}}" data-lx_tel="{{item.$orig.lx_tel}}" data-btntype="2" data-event-opts="{{[['tap',[['showLinkChange',['$event']]]]]}}" catchtap="__e">{{item.$orig.xunjia_text?item.$orig.xunjia_text:'联系TA'}}</view></block></block></view></block><block wx:if="{{item.$orig.merchant_name}}"><view class="p1" style="color:#666;font-size:24rpx;white-space:nowrap;text-overflow:ellipsis;margin-top:6rpx;height:30rpx;line-height:30rpx;font-weight:normal;"><text>{{item.$orig.merchant_name}}</text></view></block><block wx:if="{{item.$orig.main_business}}"><view class="p1" style="color:#666;font-size:24rpx;margin-top:4rpx;font-weight:normal;"><text>{{item.$orig.main_business}}</text></view></block><view class="p3"><block wx:if="{{item.$orig.sales>0}}"><view class="p3-1"><text style="overflow:hidden;">{{"已售"+item.$orig.sales+"件"}}</text></view></block></view><block wx:if="{{item.$orig.sales<=0&&item.$orig.merchant_name}}"><view style="height:44rpx;"></view></block></view></view></block></view><block wx:if="{{nomore}}"><nomore vue-id="3696b15c-1" text="没有更多商品了" bind:__l="__l"></nomore></block><block wx:if="{{nodata}}"><nodata vue-id="3696b15c-2" text="没有查找到相关商品" bind:__l="__l"></nodata></block></scroll-view></view></view></view></block></block><block wx:if="{{buydialogShow}}"><buydialog vue-id="3696b15c-3" proid="{{proid}}" menuindex="{{menuindex}}" data-event-opts="{{[['^buydialogChange',[['buydialogChange']]]]}}" bind:buydialogChange="__e" bind:__l="__l"></buydialog></block><block wx:if="{{showLinkStatus}}"><view class="posterDialog linkDialog"><view class="main"><view data-event-opts="{{[['tap',[['showLinkChange',['$event']]]]]}}" class="close" bindtap="__e"><image class="img" src="/static/img/close.png"></image></view><view class="content"><view class="title">{{lx_name}}</view><block wx:if="{{lx_bid>0}}"><view class="row"><view class="f1">店铺名称</view><view class="f2" data-url="{{'/pagesExt/business/index?id='+lx_bid}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">{{lx_bname}}<image class="image" src="/static/img/arrowright.png"></image></view></view></block><block wx:if="{{lx_tel}}"><view class="row"><view class="f1">联系电话</view><view class="f2" style="{{'color:'+($root.m12)+';'}}" data-url="{{'tel::'+lx_tel}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">{{lx_tel}}<image class="copyicon" src="/static/img/copy.png" data-text="{{lx_tel}}" data-event-opts="{{[['tap',[['copy',['$event']]]]]}}" catchtap="__e"></image></view></view></block></view></view></view></block><block wx:if="{{loading}}"><loading vue-id="3696b15c-4" bind:__l="__l"></loading></block><dp-tabbar vue-id="3696b15c-5" opt="{{opt}}" data-event-opts="{{[['^getmenuindex',[['getmenuindex']]]]}}" bind:getmenuindex="__e" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="3696b15c-6" data-ref="popmsg" bind:__l="__l"></popmsg></view>