<view><block wx:if="{{isload}}"><block><block wx:if="{{info.id&&info.status==2}}"><view style="color:red;padding:10rpx 30rpx;margin-top:20rpx;"><parse vue-id="2931a780-1" content="{{bset.verify_reject||'审核未通过：'}}" bind:__l="__l"></parse>{{info.reason+'，请修改后重新提交'}}</view></block><block wx:if="{{info.id&&info.status==0}}"><view style="color:red;padding:10rpx 30rpx;margin-top:20rpx;"><parse vue-id="2931a780-2" content="{{bset.verify_notice||'您的团长申请已提交成功，请耐心等待审核，平台将于7个工作日内联系您核实信息，请留意来电'}}" bind:__l="__l"></parse></view></block><block wx:if="{{info.id&&info.status==1}}"><view style="color:red;padding:10rpx 30rpx;margin-top:20rpx;"><parse vue-id="2931a780-3" content="{{bset.verify_success||'恭喜您审核通过！'}}" bind:__l="__l"></parse></view></block><block wx:if="{{!info.id||info.status==2||info.status==0}}"><view style="color:red;padding:10rpx 30rpx;margin-top:20rpx;"><parse vue-id="2931a780-4" content="{{bset.verify_normal||'温馨提示：审核通过后可完成入驻'}}" bind:__l="__l"></parse></view></block><form data-event-opts="{{[['submit',[['subform',['$event']]]]]}}" bindsubmit="__e"><view class="apply_box"><view class="apply_item"><view>团长名称<text style="color:red;">*</text></view><view class="flex-y-center"><input type="text" name="name" placeholder="请输入团长名称" value="{{info.name}}"/></view></view><view class="apply_item"><view>团长电话<text style="color:red;">*</text></view><view class="flex-y-center"><input type="text" name="tel" placeholder="请填写手机号码" value="{{info.tel}}"/></view></view><view class="apply_item"><view>团长微信<text style="color:red;">*</text></view><view class="flex-y-center"><input type="text" name="wxid" placeholder="请填写微信号" value="{{info.wxid}}"/></view></view></view><view class="apply_box"><view class="apply_item" style="border-bottom:0;"><text>团长头像<text style="color:red;">*</text></text></view><view class="flex" style="flex-wrap:wrap;padding-bottom:20rpx;"><block wx:for="{{pic}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="layui-imgbox"><view class="layui-imgbox-close" data-index="{{index}}" data-field="pic" data-event-opts="{{[['tap',[['removeimg',['$event']]]]]}}" bindtap="__e"><image src="/static/img/ico-del.png"></image></view><view class="layui-imgbox-img"><image src="{{item}}" data-url="{{item}}" mode="widthFix" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image></view></view></block><block wx:if="{{$root.g0==0}}"><view class="uploadbtn" style="{{('background:url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 60rpx;background-size:80rpx 80rpx;background-color:#F3F3F3;')}}" data-field="pic" data-event-opts="{{[['tap',[['uploadimg',['$event']]]]]}}" bindtap="__e"></view></block></view><input type="text" hidden="true" name="pic" maxlength="-1" value="{{$root.g1}}"/></view><view class="apply_box"><view class="apply_item" style="border-bottom:0;"><text>申请条件<text style="color:red;">*</text></text></view><checkbox-group data-event-opts="{{[['change',[['conditionChange',['$event']]]]]}}" bindchange="__e"><view class="condition-item"><label class="flex-y-center"><checkbox value="1" checked="{{conditions.hasGroup}}"></checkbox><text>自有微信群50人以上</text></label><block wx:if="{{conditions.hasGroup}}"><view class="group-proof"><view class="flex" style="flex-wrap:wrap;padding-bottom:20rpx;"><block wx:for="{{groupPics}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="layui-imgbox"><view class="layui-imgbox-close" data-index="{{index}}" data-field="groupPics" data-event-opts="{{[['tap',[['removeimg',['$event']]]]]}}" bindtap="__e"><image src="/static/img/ico-del.png"></image></view><view class="layui-imgbox-img"><image src="{{item}}" data-url="{{item}}" mode="widthFix" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image></view></view></block><block wx:if="{{$root.g2<3}}"><view class="uploadbtn" style="{{('background:url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 60rpx;background-size:80rpx 80rpx;background-color:#F3F3F3;')}}" data-field="groupPics" data-event-opts="{{[['tap',[['uploadimg',['$event']]]]]}}" bindtap="__e"></view></block></view></view></block></view><view class="condition-item"><label class="flex-y-center"><checkbox value="2" checked="{{conditions.hasOnlineShop}}"></checkbox><text>自有线上店铺</text></label><block wx:if="{{conditions.hasOnlineShop}}"><view><view class="apply_item"><view>店铺链接<text style="color:red;">*</text></view><view class="flex-y-center"><input type="text" name="shopUrl" placeholder="请填写店铺链接" placeholder-class="placeholder-style" value="{{info.shop_url}}"/></view></view></view></block></view><view class="condition-item"><label class="flex-y-center"><checkbox value="3" checked="{{conditions.hasOfflineShop}}"></checkbox><text>自有线下店铺</text></label><block wx:if="{{conditions.hasOfflineShop}}"><view><view class="apply_item"><view>店铺地址<text style="color:red;">*</text></view><view class="flex-y-center"><input type="text" name="shopAddress" placeholder="请填写店铺地址" placeholder-class="placeholder-style" value="{{info.shop_address}}"/></view></view><view>上传营业执照<text style="color:red;">*</text><view class="flex" style="flex-wrap:wrap;padding-bottom:20rpx;"><block wx:for="{{licensePics}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="layui-imgbox"><view class="layui-imgbox-close" data-index="{{index}}" data-field="licensePics" data-event-opts="{{[['tap',[['removeimg',['$event']]]]]}}" bindtap="__e"><image src="/static/img/ico-del.png"></image></view><view class="layui-imgbox-img"><image src="{{item}}" data-url="{{item}}" mode="widthFix" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image></view></view></block><block wx:if="{{$root.g3<2}}"><view class="uploadbtn" style="{{('background:url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 60rpx;background-size:80rpx 80rpx;background-color:#F3F3F3;')}}" data-field="licensePics" data-event-opts="{{[['tap',[['uploadimg',['$event']]]]]}}" bindtap="__e"></view></block></view></view></view></block></view></checkbox-group><input type="text" hidden="true" name="groupPics" maxlength="-1" value="{{$root.g4}}"/><input type="text" hidden="true" name="licensePics" maxlength="-1" value="{{$root.g5}}"/></view><view class="apply_box"><view class="apply_item"><text>登录用户名<text style="color:red;">*</text></text><view class="flex-y-center"><input type="text" name="un" placeholder="请填写登录账号" autocomplete="off" value="{{info.un}}"/></view></view><view class="apply_item"><text>登录密码<text style="color:red;">*</text></text><view class="flex-y-center"><input type="password" name="pwd" placeholder="请填写登录密码" autocomplete="off" value="{{info.pwd}}"/></view></view><view class="apply_item"><text>确认密码<text style="color:red;">*</text></text><view class="flex-y-center"><input type="password" name="repwd" placeholder="请再次填写密码" value="{{info.repwd}}"/></view></view></view><block wx:if="{{bset.xieyi_show==1}}"><block><block wx:if="{{!info.id||info.status==2}}"><view class="flex-y-center" style="margin-left:20rpx;color:#999;"><checkbox-group data-event-opts="{{[['change',[['isagreeChange',['$event']]]]]}}" bindchange="__e"><label class="flex-y-center"><checkbox value="1" checked="{{isagree}}"></checkbox>阅读并同意</label></checkbox-group><text data-event-opts="{{[['tap',[['showxieyiFun',['$event']]]]]}}" style="color:#666;" bindtap="__e">《团长入驻协议》</text></view></block></block></block><view style="padding:30rpx 0;"><block wx:if="{{!info.id||info.status==2}}"><button class="set-btn" style="{{('background:linear-gradient(90deg,'+$root.m0+' 0%,rgba('+$root.m1+',0.8) 100%)')}}" form-type="submit">提交申请</button></block></view></form><view style="width:100%;height:100%;position:fixed;top:0;left:0;z-index:99;background:rgba(0,0,0,0.7);" id="xieyi" hidden="{{!showxieyi}}"><view style="width:90%;margin:0 auto;height:85%;margin-top:10%;background:#fff;color:#333;padding:5px 10px 50px 10px;position:relative;border-radius:2px;"><view style="overflow:scroll;height:100%;"><parse vue-id="2931a780-5" content="{{bset.xieyi}}" bind:__l="__l"></parse></view><view data-event-opts="{{[['tap',[['hidexieyi',['$event']]]]]}}" style="position:absolute;z-index:9999;bottom:10px;left:0;right:0;margin:0 auto;text-align:center;" bindtap="__e">已阅读并同意</view></view></view></block></block><block wx:if="{{loading}}"><loading vue-id="2931a780-6" bind:__l="__l"></loading></block><dp-tabbar vue-id="2931a780-7" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="2931a780-8" data-ref="popmsg" bind:__l="__l"></popmsg></view>