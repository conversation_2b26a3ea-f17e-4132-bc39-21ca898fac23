<view><block wx:if="{{isload}}"><block><view class="container"><block wx:if="{{$root.g0>0}}"><swiper class="swiper" indicator-dots="{{pics[1]?true:false}}" autoplay="true" interval="{{5000}}"><block wx:for="{{pics}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><swiper-item class="swiper-item"><image src="{{item}}" mode="widthFix"></image></swiper-item></block></block></swiper></block><block wx:if="{{$root.g1>0}}"><scroll-view class="category" scroll-x="true"><block wx:for="{{clist}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><view class="item" data-st="{{item.id}}" data-event-opts="{{[['tap',[['changetab',['$event']]]]]}}" bindtap="__e"><image src="{{item.pic}}"></image><text class="t1">{{item.name}}</text></view></block></block><view class="item" data-st="{{true}}" data-event-opts="{{[['tap',[['changetab',['$event']]]]]}}" bindtap="__e"><image style="border-radius:0;" src="{{pre_url+'/static/img/all.png'}}"></image><text class="t1">全部</text></view></scroll-view></block><view class="datalist"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><view class="collage-product" data-url="{{'product?id='+item.$orig.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="product-pic"><image src="{{item.$orig.pic}}" mode="widthFix"></image></view><view class="product-info"><view class="p1">{{item.$orig.name}}</view><view class="p2"><view class="p2-1"><text class="t1" style="{{'color:'+(item.m0)+';'}}"><text style="font-size:24rpx;">￥</text>{{item.$orig.sell_price}}</text><block wx:if="{{item.$orig.market_price*1>item.$orig.sell_price*1}}"><text class="t2">{{"￥"+item.$orig.market_price}}</text></block></view></view><view class="p3"><view class="t1">已拼成<text style="font-size:32rpx;color:#f40;padding:0 2rpx;">{{item.$orig.sales}}</text>件</view><view class="t2" style="{{'border-color:'+(item.m1)+';'+('color:'+(item.m2)+';')}}"><text class="x1">{{item.$orig.teamnum+"人团"}}</text><text class="x2" style="{{'background-color:'+(item.m3)+';'}}">去拼团</text></view></view></view></view></block></block></view><block wx:if="{{nomore}}"><nomore vue-id="3d643a22-1" bind:__l="__l"></nomore></block><block wx:if="{{nodata}}"><nodata vue-id="3d643a22-2" bind:__l="__l"></nodata></block></view><button class="covermy" data-url="orderlist" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">我的拼团</button></block></block><block wx:if="{{loading}}"><loading vue-id="3d643a22-3" bind:__l="__l"></loading></block><dp-tabbar vue-id="3d643a22-4" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="3d643a22-5" data-ref="popmsg" bind:__l="__l"></popmsg></view>