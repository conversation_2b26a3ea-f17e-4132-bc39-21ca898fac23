<view class="container"><block wx:if="{{isload}}"><block><view class="title">地理位置</view><view class="ip-info"><view class="ip-item"><text>IP地址：</text><text>{{ipAddress||'未获取'}}</text></view><view class="ip-item"><text>地理位置：</text><text>{{fullAddress||'未获取'}}</text></view><view class="ip-item"><text>最后更新时间：</text><text>{{ipLastUpdateTime||'未获取'}}</text></view><block wx:if="{{ipCaptureEnabled}}"><button data-event-opts="{{[['tap',[['updateIpAddress',['$event']]]]]}}" class="update-ip-btn" style="{{'background:'+('linear-gradient(90deg,'+$root.m0+' 0%,rgba('+$root.m1+',0.8) 100%)')+';'}}" bindtap="__e">更新IP地址</button></block></view><form data-event-opts="{{[['submit',[['formSubmit',['$event']]]],['reset',[['formReset',['$event']]]]]}}" bindsubmit="__e" bindreset="__e"><view class="form"><view class="form-item"><view class="label">国家</view><input class="input" type="text" placeholder="请输入国家" placeholder-style="color:#BBBBBB;font-size:28rpx" name="country" data-event-opts="{{[['input',[['__set_model',['$0','country','$event',[]],['addressInfo']]]]]}}" value="{{addressInfo.country}}" bindinput="__e"/></view><view class="form-item"><view class="label">省份</view><input class="input" type="text" placeholder="请输入省份" placeholder-style="color:#BBBBBB;font-size:28rpx" name="province" data-event-opts="{{[['input',[['__set_model',['$0','province','$event',[]],['addressInfo']]]]]}}" value="{{addressInfo.province}}" bindinput="__e"/></view><view class="form-item"><view class="label">城市</view><input class="input" type="text" placeholder="请输入城市" placeholder-style="color:#BBBBBB;font-size:28rpx" name="city" data-event-opts="{{[['input',[['__set_model',['$0','city','$event',[]],['addressInfo']]]]]}}" value="{{addressInfo.city}}" bindinput="__e"/></view><view class="form-item"><view class="label">地区</view><input class="input" type="text" placeholder="请输入地区" placeholder-style="color:#BBBBBB;font-size:28rpx" name="area" data-event-opts="{{[['input',[['__set_model',['$0','area','$event',[]],['addressInfo']]]]]}}" value="{{addressInfo.area}}" bindinput="__e"/></view></view><block wx:if="{{addressEditRemain>0}}"><view class="tips">{{"剩余修改次数："+addressEditRemain+"次"}}</view></block><button class="set-btn" style="{{'background:'+('linear-gradient(90deg,'+$root.m2+' 0%,rgba('+$root.m3+',0.8) 100%)')+';'}}" form-type="submit">保 存</button></form></block></block><block wx:if="{{loading}}"><loading vue-id="28eb2c18-1" bind:__l="__l"></loading></block><dp-tabbar vue-id="28eb2c18-2" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="28eb2c18-3" data-ref="popmsg" bind:__l="__l"></popmsg></view>