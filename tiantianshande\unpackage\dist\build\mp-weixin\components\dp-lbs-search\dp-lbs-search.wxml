<view class="dp-search" style="{{'background-color:'+(params.bgcolor)+';'+('margin:'+(params.margin_y*2.2+'rpx '+params.margin_x*2.2+'rpx')+';')+('padding:'+(params.padding_y*2.2+'rpx '+params.padding_x*2.2+'rpx')+';')}}"><view style="display:flex;background:#fff;"><view style="display:flex;align-items:center;justify-content:center;padding:0 20rpx;"><uni-data-picker vue-id="41bebdce-1" localdata="{{arealist}}" popup-title="地区" placeholder="地区" data-event-opts="{{[['^change',[['areachange']]]]}}" bind:change="__e" bind:__l="__l" vue-slots="{{['default']}}"><view class="{{[showarea?'':'hui']}}" style="display:flex;align-items:center;"><view style="flex-shrink:0;">{{showarea?showarea:'地区'}}</view><image style="width:25rpx;flex-shrink:0;margin-left:10rpx;" mode="widthFix" src="/static/img/hdsanjiao.png"></image></view></uni-data-picker></view><view class="dp-search-search flex1" style="{{'border-color:'+(params.bordercolor)+';'+('border-radius:'+(params.borderradius+'px')+';')}}"><view class="dp-search-search-f1"></view><view class="dp-search-search-f2"><input class="dp-search-search-input" style="{{(params.color?'color:'+params.color:'')}}" data-url="{{data_hrefurl}}" name="keyword" placeholder="{{data_placeholder?data_placeholder:params.placeholder||'输入关键字进行搜索s'}}" placeholder-style="color:#aaa;font-size:28rpx" data-event-opts="{{[['confirm',[['searchgoto',['$event']]]],['input',[['inputKeyword',['$event']]]]]}}" bindconfirm="__e" bindinput="__e"/></view><block wx:if="{{params.image_search==1}}"><view class="dp-search-search-f3" style="{{('background-image:url('+pre_url+'/static/img/camera.png)')}}" data-url="{{data_hrefurl}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"></view></block></view><block wx:if="{{params.btn==1}}"><view><view style="width:100rpx;text-align:center;line-height:72rpx;" data-url="{{data_hrefurl}}" data-event-opts="{{[['tap',[['searchgoto',['$event']]]]]}}" bindtap="__e">搜索</view></view></block></view></view>