<view><block wx:if="{{isload}}"><block><form data-event-opts="{{[['submit',[['subform',['$event']]]]]}}" bindsubmit="__e"><view class="form-box"><view class="form-item"><view class="f1">餐桌名称<text style="color:red;">*</text></view><view class="f2"><input type="text" name="name" placeholder="请填写餐桌名称" placeholder-style="color:#888" value="{{info.name}}"/></view></view><view class="form-item"><view class="f1">分类</view><view class="f2"><picker value="{{nindex}}" range="{{clist}}" name="cid" data-event-opts="{{[['change',[['pickerCate',['$event']]]]]}}" bindchange="__e"><view class="picker">{{clist[nindex]}}</view></picker></view></view><view class="form-item"><view class="f1">座位数</view><view class="f2"><input type="number" name="seat" placeholder placeholder-style="color:#888" value="{{info.seat}}"/></view></view><view class="form-item"><text>支持预定<text style="color:red;">*</text></text><view><radio-group class="radio-group" name="canbook" data-event-opts="{{[['change',[['',['$event']]]]]}}" bindchange="__e"><label><radio value="1" checked="{{info.canbook==1?true:false}}"></radio>支持</label><label><radio value="0" checked="{{!info||info.canbook==0?true:false}}"></radio>不支持</label></radio-group></view></view></view><view class="form-box"><view class="form-item"><view class="f1">排序</view><view class="f2"><input type="text" name="sort" placeholder="用于排序,越大越靠前" placeholder-style="color:#888" value="{{info.sort}}"/></view></view><view class="form-item"><text>状态<text style="color:red;">*</text></text><view><radio-group class="radio-group" name="status" data-event-opts="{{[['change',[['',['$event']]]]]}}" bindchange="__e"><label><radio value="-1" checked="{{info.status==-1?true:false}}"></radio>禁用</label><label><radio value="2" checked="{{info.status==2?true:false}}"></radio>用餐</label><label><radio value="1" checked="{{info.status==1?true:false}}"></radio>预定</label><label><radio value="0" checked="{{!info||info.status==0?true:false}}"></radio>空闲</label></radio-group></view></view></view><button class="savebtn" style="{{('background:linear-gradient(90deg,'+$root.m0+' 0%,rgba('+$root.m1+',0.8) 100%)')}}" form-type="submit">提交</button><block wx:if="{{info.id}}"><button data-event-opts="{{[['tap',[['todel',['$event']]]]]}}" class="button text-btn" bindtap="__e">删除</button></block><view style="height:50rpx;"></view></form></block></block><block wx:if="{{loading}}"><loading vue-id="6b5c1112-1" bind:__l="__l"></loading></block></view>