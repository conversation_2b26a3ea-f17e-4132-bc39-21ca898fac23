<view><block wx:if="{{params.style==1}}"><view class="dp-shop-1"><image class="shop1-img" src="{{params.bgimg}}"></image><block wx:if="{{params.menu==1}}"><view class="shop1-menu" style="{{'color:'+(params.navcolor)+';'}}"><block wx:for="{{data}}" wx:for-item="item" wx:for-index="__i0__" wx:key="id"><view data-url="{{item.hrefurl}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="shop1-nav"><image style="width:36rpx;height:36rpx;" src="{{item.imgurl}}"></image><view>{{item.text}}</view></view></view></block></view></block><block wx:if="{{params.name==1}}"><view class="shop1-shopname"><view class="shop1-name">{{shopinfo.name}}</view></view></block><block wx:if="{{params.logo==1}}"><view class="shop1-shoplogo"><view class="shop1-shoplogo-img"><image class="shop1-shoplogo-img-img" src="{{shopinfo.logo}}"></image></view></view></block></view></block><block wx:if="{{params.style==2}}"><view class="dp-shop-2"><image class="shop2-img" src="{{params.bgimg}}"></image><block wx:if="{{params.logo==1}}"><view class="shop2-shoplogo"><image class="shop2-shoplogo-img" src="{{shopinfo.logo}}"></image></view></block><block wx:if="{{params.name==1}}"><view class="shop2-shopname">{{shopinfo.name}}</view></block><block wx:if="{{params.menu==1}}"><view class="shop2-menu" style="{{'color:'+(params.navcolor)+';'}}"><block wx:for="{{data}}" wx:for-item="item" wx:for-index="__i1__" wx:key="id"><view class="shop2-nav" data-url="{{item.hrefurl}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image style="width:36rpx;height:36rpx;" src="{{item.imgurl}}"></image><view style="font-size:24rpx;">{{item.text}}</view></view></block></view></block></view></block></view>