<view><block wx:if="{{isload}}"><block><form data-event-opts="{{[['submit',[['subform',['$event']]]]]}}" bindsubmit="__e"><view class="form-box"><block wx:if="{{pageType}}"><view class="form-item flex-col"><view class="f1">{{$root.m0+"类型"}}</view><view class="f2"><radio-group name="type" data-event-opts="{{[['change',[['bindTypeChange',['$event']]]]]}}" bindchange="__e"><view class="radio-group-view"><label><radio value="1" checked="{{info.type==1?true:false}}"></radio>代金券</label><label><radio value="10" checked="{{!info||info.type==10?true:false}}"></radio>折扣券</label><label><radio value="2" checked="{{info.type==2?true:false}}"></radio>礼品券</label><label><radio value="3" checked="{{info.type==3?true:false}}"></radio>计次券</label><label><radio value="4" checked="{{info.type==4?true:false}}"></radio>运费抵扣券</label></view></radio-group></view></view></block><view class="form-item"><block wx:if="{{pageType}}"><view class="f1">{{$root.m1+"名称"}}<text style="color:red;">*</text></view></block><block wx:else><view class="f1">优惠券名称<text style="color:red;">*</text></view></block><view class="f2"><input type="text" name="name" placeholder="请填写名称" placeholder-style="color:#888" value="{{info.name}}"/></view></view><block wx:if="{{pageType}}"><block><block wx:if="{{info.type==1||info.type==10||info.type==20||info.type==11}}"><block><block wx:if="{{info.type!=10&&info.type!=11}}"><view class="form-item"><view class="f1">优惠金额(元)<text style="color:red;">*</text></view><view class="f2"><input type="digit" name="money" placeholder="请填写优惠金额(元)" placeholder-style="color:#888" value="{{info.money}}"/></view></view></block><block wx:if="{{info.type==10}}"><view class="form-item"><view class="f1">折扣比例<text style="color:red;">*</text></view><view class="f2"><input style="margin-right:10rpx;" type="digit" name="discount" placeholder="例如9折则填写90" placeholder-style="color:#888" value="{{info.discount}}"/>%</view></view></block><block wx:if="{{info.type!=11}}"><view class="form-item"><view class="f1" style="width:228rpx;">最低消费金额(元)<text style="color:red;"></text></view><view class="f2"><input type="text" name="minprice" placeholder="请填写最低消费金额(元)" placeholder-style="color:#888" value="{{info.minprice}}"/></view></view></block><view class="form-item flex-col"><view class="f1">适用范围</view><view class="f2"><radio-group class="radio-group" name="fwtype" data-event-opts="{{[['change',[['scopeApplication',['$event']]]]]}}" bindchange="__e"><view class="radio-group-view"><label><radio value="0" checked="{{info.fwtype==0?true:false}}"></radio>所有商品</label><label><radio value="1" checked="{{info.fwtype==1?true:false}}"></radio>指定类目</label><block wx:if="{{bid}}"><label><radio value="6" checked="{{info.fwtype==6?true:false}}"></radio>指定商家类目</label></block><label><radio value="2" checked="{{info.fwtype==2?true:false}}"></radio>指定商品</label><block wx:if="{{restaurant}}"><label><radio value="3" checked="{{info.fwtype==3?true:false}}"></radio>指定菜品</label></block><block wx:if="{{auth.yuyue}}"><label><radio value="4" checked="{{info.fwtype==4?true:false}}"></radio>指定服务商品</label></block></view></radio-group></view></view><block wx:if="{{info.fwtype==1||info.fwtype==6}}"><view class="form-item"><view class="flex flex-col addshow-list-view"><view class="flex title-view"><view>指定类目</view><view data-event-opts="{{[['tap',[['addshopClass',['$0'],['info.fwtype']]]]]}}" class="but-class" style="{{'background:'+('linear-gradient(-90deg,'+$root.m2+' 0%,rgba('+$root.m3+',0.8) 100%)')+';'}}" bindtap="__e">添加类目</view></view><block wx:if="{{$root.g0}}"><view class="product"><block wx:for="{{categorydata}}" wx:for-item="item" wx:for-index="index2" wx:key="index2"><block><view class="item flex"><view class="img-view"><block wx:if="{{item.pic}}"><image src="{{item.pic}}"></image></block><block wx:else><view class="img-view-empty"></view></block></view><view class="info"><view class="f1">{{item.name}}</view><view></view></view><view data-event-opts="{{[['tap',[['clearShopClass',['$0'],[[['categorydata','',index2,'id']]]]]]]}}" class="del-view flex-y-center" catchtap="__e"><image style="width:24rpx;height:24rpx;margin-right:6rpx;" src="{{pre_url+'/static/img/del.png'}}"></image></view></view></block></block></view></block></view></view></block><block wx:if="{{info.fwtype==2}}"><view class="form-item"><view class="flex flex-col addshow-list-view"><view class="flex title-view"><view>指定商品列表</view><view data-event-opts="{{[['tap',[['addshop',[0]]]]]}}" class="but-class" style="{{'background:'+('linear-gradient(-90deg,'+$root.m4+' 0%,rgba('+$root.m5+',0.8) 100%)')+';'}}" bindtap="__e">添加商品</view></view><block wx:if="{{$root.g1}}"><view class="product"><block wx:for="{{productdata}}" wx:for-item="item" wx:for-index="index2" wx:key="index2"><block><view class="item flex"><view class="img-view"><block wx:if="{{item.pic}}"><image src="{{item.pic}}"></image></block><block wx:else><view class="img-view-empty"></view></block></view><view class="info"><view class="f1">{{item.name}}</view><view></view></view><view data-event-opts="{{[['tap',[['clearShopCartFn',['$0',0],[[['productdata','',index2,'id']]]]]]]}}" class="del-view flex-y-center" style="color:#999999;font-size:24rpx;" catchtap="__e"><image style="width:24rpx;height:24rpx;margin-right:6rpx;" src="{{pre_url+'/static/img/del.png'}}"></image></view></view></block></block></view></block></view></view></block><block wx:if="{{info.fwtype==4}}"><view class="form-item"><view class="flex flex-col addshow-list-view"><view class="flex title-view"><view>指定服务商品列表</view><view data-event-opts="{{[['tap',[['addshopProgive',[0]]]]]}}" class="but-class" style="{{'background:'+('linear-gradient(-90deg,'+$root.m6+' 0%,rgba('+$root.m7+',0.8) 100%)')+';'}}" bindtap="__e">添加商品</view></view><block wx:if="{{$root.g2}}"><view class="product"><block wx:for="{{yuyue_product}}" wx:for-item="item" wx:for-index="index2" wx:key="index2"><block><view class="item flex"><view class="img-view"><block wx:if="{{item.pic}}"><image src="{{item.pic}}"></image></block><block wx:else><view class="img-view-empty"></view></block></view><view class="info"><view class="f1">{{item.name}}</view></view><view data-event-opts="{{[['tap',[['clearShopCartFn2',['$0',0],[[['yuyue_product','',index2,'id']]]]]]]}}" class="del-view flex-y-center" style="color:#999999;font-size:24rpx;" catchtap="__e"><image style="width:24rpx;height:24rpx;margin-right:6rpx;" src="{{pre_url+'/static/img/del.png'}}"></image></view></view></block></block></view></block></view></view></block></block></block></block></block><block wx:else><block><block wx:if="{{info.type!=51}}"><view class="form-item"><view class="f1">优惠金额(元)<text style="color:red;">*</text></view><view class="f2"><input type="digit" name="money" placeholder="请填写优惠金额(元)" placeholder-style="color:#888" value="{{info.money}}"/></view></view></block><block wx:if="{{info.type==10}}"><view class="form-item"><view class="f1">折扣比例<text style="color:red;">*</text></view><view class="f2"><input type="digit" name="discount" placeholder="例如9折则填写90" placeholder-style="color:#888" value="{{info.discount}}"/>%</view></view></block><block wx:if="{{info.type!=51}}"><view class="form-item"><view class="f1" style="width:228rpx;">最低消费金额(元)<text style="color:red;"></text></view><view class="f2"><input type="text" name="minprice" placeholder="请填写最低消费金额(元)" placeholder-style="color:#888" value="{{info.minprice}}"/></view></view></block><view class="form-item flex-col"><view class="f1">适用范围<text style="color:red;">*</text></view><view class="f2"><radio-group class="radio-group" name="fwtype" data-event-opts="{{[['change',[['scopeApplicationRes',['$event']]]]]}}" bindchange="__e"><label><radio value="0" checked="{{info.fwtype==0?true:false}}"></radio>全场通用</label><label style="margin-left:20rpx;"><radio value="1" checked="{{info.fwtype==1?true:false}}"></radio>指定类目</label><label style="margin-left:20rpx;"><radio value="2" checked="{{info.fwtype==2?true:false}}"></radio>指定菜品</label></radio-group></view></view><block wx:if="{{info.fwtype==1}}"><view class="form-item"><view class="flex flex-col addshow-list-view"><view class="flex title-view"><view>指定类目</view><view data-event-opts="{{[['tap',[['restaurantClass']]]]}}" class="but-class" style="{{'background:'+('linear-gradient(-90deg,'+$root.m8+' 0%,rgba('+$root.m9+',0.8) 100%)')+';'}}" bindtap="__e">添加类目</view></view><block wx:if="{{$root.g3}}"><view class="product"><block wx:for="{{categorydata}}" wx:for-item="item" wx:for-index="index2" wx:key="index2"><block><view class="item flex" style="align-items:center;"><view class="info" style="height:40rpx;"><view class="f1">{{item.name}}</view></view><view data-event-opts="{{[['tap',[['clearShopClass',['$0'],[[['categorydata','',index2,'id']]]]]]]}}" class="del-view-class flex-y-center" catchtap="__e"><image style="width:24rpx;height:24rpx;margin-right:6rpx;" src="{{pre_url+'/static/img/del.png'}}"></image></view></view></block></block></view></block></view></view></block><block wx:if="{{info.fwtype==2}}"><view class="form-item"><view class="flex flex-col addshow-list-view"><view class="flex title-view"><view>指定菜品</view><view data-event-opts="{{[['tap',[['restaurantShop']]]]}}" class="but-class" style="{{'background:'+('linear-gradient(-90deg,'+$root.m10+' 0%,rgba('+$root.m11+',0.8) 100%)')+';'}}" bindtap="__e">添加菜品</view></view><block wx:if="{{$root.g4}}"><view class="product"><block wx:for="{{productdata}}" wx:for-item="item" wx:for-index="index2" wx:key="index2"><block><view class="item flex"><view class="img-view"><block wx:if="{{item.pic}}"><image src="{{item.pic}}"></image></block><block wx:else><view class="img-view-empty"></view></block></view><view class="info"><view class="f1">{{item.name}}</view><view></view></view><view data-event-opts="{{[['tap',[['clearRestaurant',['$0'],[[['productdata','',index2,'id']]]]]]]}}" class="del-view flex-y-center" style="color:#999999;font-size:24rpx;" catchtap="__e"><image style="width:24rpx;height:24rpx;margin-right:6rpx;" src="{{pre_url+'/static/img/del.png'}}"></image></view></view></block></block></view></block></view></view></block></block></block><block wx:if="{{info.type==3}}"><block><view class="form-item"><view class="f1">可用次数<text style="color:red;"></text></view><view class="f2"><input type="text" name="limit_count" placeholder="每张券可以使用多少次" placeholder-style="color:#888" value="{{info.limit_count}}"/></view></view><view class="form-item"><view class="f1">每天可用<text style="color:red;"></text></view><view class="f2"><input type="text" name="limit_perday" placeholder="每张券每天可以用多少次" placeholder-style="color:#888" value="{{info.limit_perday}}"/></view></view></block></block><view class="form-item flex-col"><view class="f1">使用说明<text style="color:red;"></text></view><textarea name="usetips" placeholder="请输入使用说明" value="{{info.usetips}}"></textarea></view></view><view class="form-box"><view class="form-item flex-col"><view class="f1">有效期<text style="color:red;"></text></view><view class="f2"><radio-group class="radio-group" name="yxqtype" data-event-opts="{{[['change',[['bindYxqtypeChange',['$event']]]]]}}" bindchange="__e"><view class="radio-group-view"><label><radio value="1" checked="{{info.yxqtype==1?true:false}}"></radio>固定时间范围</label><label><radio value="2" checked="{{info.yxqtype==2?true:false}}"></radio>领取后时长</label><label><radio value="3" checked="{{info.yxqtype==3?true:false}}"></radio>领取后时长（次日起）</label></view></radio-group></view></view><block wx:if="{{info.yxqtype==1}}"><view class="form-item flex-col"><view class="f1">有效期时间</view><view class="f2" style="line-height:30px;"><picker mode="date" value="{{start_time1}}" data-event-opts="{{[['change',[['bindStartTime1Change',['$event']]]]]}}" bindchange="__e"><view class="picker">{{start_time1}}</view></picker><picker mode="time" value="{{start_time2}}" data-event-opts="{{[['change',[['bindStartTime2Change',['$event']]]]]}}" bindchange="__e"><view class="picker" style="padding-left:10rpx;">{{start_time2}}</view></picker><view style="padding:0 10rpx;color:#222;font-weight:bold;">到</view><picker mode="date" value="{{end_time1}}" data-event-opts="{{[['change',[['bindEndTime1Change',['$event']]]]]}}" bindchange="__e"><view class="picker">{{end_time1}}</view></picker><picker mode="time" value="{{end_time2}}" data-event-opts="{{[['change',[['bindEndTime2Change',['$event']]]]]}}" bindchange="__e"><view class="picker" style="padding-left:10rpx;">{{end_time2}}</view></picker></view></view></block><block wx:if="{{info.yxqtype==2}}"><view class="form-item"><view class="f1" style="width:228rpx;">领取后几天有效<text style="color:red;">*</text></view><view class="f2"><input type="text" name="yxqdate2" placeholder="领取后几天有效(天)" placeholder-style="color:#888" value="{{info.yxqdate}}"/></view></view></block><block wx:if="{{info.yxqtype==3}}"><view class="form-item"><view class="f1" style="width:228rpx;">领取后几天有效<text style="color:red;">*</text></view><view class="f2"><input type="text" name="yxqdate3" placeholder="次日0点开始计算有效期" placeholder-style="color:#888" value="{{info.yxqdate}}"/></view></view></block><view class="form-item flex-col"><block wx:if="{{pageType}}"><view class="f1">领取条件</view></block><block wx:else><view class="f1">参与条件</view></block><view class="f2" style="line-height:30px;"><checkbox-group class="radio-group" name="gettj" data-event-opts="{{[['change',[['collectionConditions',['$event']]]]]}}" bindchange="__e"><label><checkbox value="-1" checked="{{$root.m12?true:false}}"></checkbox>所有人</label><label><checkbox value="0" checked="{{$root.m13?true:false}}"></checkbox>关注用户</label><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="id"><label><checkbox value="{{item.$orig.id}}" checked="{{item.m14?true:false}}"></checkbox>{{''+item.$orig.name+''}}</label></block></checkbox-group></view></view><view class="form-item"><view class="f1" style="width:228rpx;">所需金额(元)<text style="color:red;"></text></view><view class="f2"><input type="text" name="price" placeholder="需要消耗多少钱购买(元)" placeholder-style="color:#888" value="{{info.price}}"/></view></view><view class="form-item"><view class="f1" style="width:228rpx;">所需积分<text style="color:red;"></text></view><view class="f2"><input type="text" name="score" placeholder="需要消耗多少积分兑换" placeholder-style="color:#888" value="{{info.score}}"/></view></view><view class="form-item"><view class="f1" style="width:228rpx;">库存<text style="color:red;"></text></view><view class="f2"><input type="text" name="stock" placeholder placeholder-style="color:#888" value="{{info.stock}}"/></view></view><view class="form-item"><view class="f1" style="width:228rpx;">每人可领取数<text style="color:red;"></text></view><view class="f2"><input type="text" name="perlimit" placeholder="每人最多可领取多少张" placeholder-style="color:#888" value="{{info.perlimit}}"/></view></view><view class="form-item"><view class="f1" style="width:228rpx;">开始时间<text style="color:red;"></text></view><view class="f2"><picker mode="date" value="{{starttime}}" data-event-opts="{{[['change',[['bindStartTimeChange',['$event']]]]]}}" bindchange="__e"><view class="picker">{{starttime}}</view></picker><picker mode="time" value="{{starttime2}}" data-event-opts="{{[['change',[['bindStartTimeChange2',['$event']]]]]}}" bindchange="__e"><view class="picker" style="padding-left:10rpx;">{{starttime2}}</view></picker></view></view><view class="form-item"><view class="f1" style="width:228rpx;">结束时间<text style="color:red;"></text></view><view class="f2"><picker mode="date" value="{{endtime}}" data-event-opts="{{[['change',[['bindEndTimeChange',['$event']]]]]}}" bindchange="__e"><view class="picker">{{endtime}}</view></picker><picker mode="time" value="{{endtime2}}" data-event-opts="{{[['change',[['bindEndTimeChange2',['$event']]]]]}}" bindchange="__e"><view class="picker" style="padding-left:10rpx;">{{endtime2}}</view></picker></view></view><view class="form-item"><view class="f1" style="width:228rpx;">序号<text style="color:red;"></text></view><view class="f2"><input type="text" name="sort" placeholder="用于排序,越大越靠前" placeholder-style="color:#888" value="{{info.sort}}"/></view></view></view><view class="form-box"><block wx:if="{{pageType}}"><view class="form-item"><view class="f1">领券中心显示<text style="color:red;"></text></view><view class="f2"><radio-group class="radio-group" name="tolist" data-event-opts="{{[['change',[['couponCenterChange',['$event']]]]]}}" bindchange="__e"><label><radio value="1" checked="{{info.tolist==1?true:false}}"></radio>是</label><label style="margin-left:20rpx;"><radio value="0" checked="{{info.tolist==0?true:false}}"></radio>否</label></radio-group></view></view></block><block wx:if="{{info.tolist==1&&pageType}}"><view class="form-item flex-col"><view class="f1">显示条件</view><view class="f2" style="line-height:30px;"><checkbox-group class="radio-group" name="showtj" data-event-opts="{{[['change',[['displayConditions',['$event']]]]]}}" bindchange="__e"><label><checkbox value="-1" checked="{{$root.m15?true:false}}"></checkbox>所有人</label><label><checkbox value="0" checked="{{$root.m16?true:false}}"></checkbox>关注用户</label><block wx:for="{{$root.l1}}" wx:for-item="item" wx:for-index="__i0__" wx:key="id"><label><checkbox value="{{''+item.$orig.id}}" checked="{{item.m17?true:false}}"></checkbox>{{''+item.$orig.name}}</label></block></checkbox-group></view></view></block><block wx:if="{{!pageType}}"><view class="form-item"><view class="f1">可直接领取<text style="color:red;"></text></view><view class="f2"><radio-group class="radio-group" name="tolist" data-event-opts="{{[['change',[['couponCenterChange',['$event']]]]]}}" bindchange="__e"><label><radio value="1" checked="{{info.tolist==1?true:false}}"></radio>是</label><label style="margin-left:20rpx;"><radio value="0" checked="{{info.tolist==0?true:false}}"></radio>否</label></radio-group></view></view></block><view class="form-item flex-col"><view class="f1">使用范围<text style="color:red;"></text></view><view class="f2"><radio-group class="radio-group" name="isgive" data-event-opts="{{[['change',[['bindStatusChange',['$event']]]]]}}" bindchange="__e"><label><radio value="0" checked="{{info.isgive==0?true:false}}"></radio>仅自用</label><label style="margin-left:20rpx;"><radio value="1" checked="{{info.isgive==1?true:false}}"></radio>自用+转赠</label><label style="margin-left:20rpx;"><radio value="2" checked="{{info.isgive==2?true:false}}"></radio>仅转赠</label></radio-group></view></view><view class="form-item"><view class="f1">支付后赠送<text style="color:red;"></text></view><view class="f2"><radio-group class="radio-group" name="paygive" data-event-opts="{{[['change',[['bindPaygiveChange',['$event']]]]]}}" bindchange="__e"><label><radio value="0" checked="{{info.paygive==0?true:false}}"></radio>关闭</label><label style="margin-left:20rpx;"><radio value="1" checked="{{info.paygive==1?true:false}}"></radio>开启</label></radio-group></view></view><block wx:if="{{info.paygive==1}}"><view class="form-item flex-col"><view class="f1">支付赠送场景</view><block wx:if="{{pageType}}"><view class="f2" style="line-height:30px;"><checkbox-group class="radio-group" name="paygive_scene" data-event-opts="{{[['change',[['paymentGiftChange',['$event']]]]]}}" bindchange="__e"><label><checkbox value="shop" checked="{{$root.m18?true:false}}"></checkbox>商城</label><label><checkbox value="scoreshop" checked="{{$root.m19?true:false}}"></checkbox>兑换</label><label><checkbox value="collage" checked="{{$root.m20?true:false}}"></checkbox>拼团</label><label><checkbox value="kanjia" checked="{{$root.m21?true:false}}"></checkbox>砍价</label><label><checkbox value="seckill" checked="{{$root.m22?true:false}}"></checkbox>秒杀</label><label><checkbox value="tuangou" checked="{{$root.m23?true:false}}"></checkbox>团购</label><label><checkbox value="lucky_collage" checked="{{$root.m24?true:false}}"></checkbox>幸运拼团</label><label><checkbox value="recharge" checked="{{$root.m25?true:false}}"></checkbox>充值</label><label><checkbox value="maidan" checked="{{$root.m26?true:false}}"></checkbox>买单收款</label></checkbox-group></view></block><block wx:else><view class="f2" style="line-height:30px;"><checkbox-group class="radio-group" name="paygive_scene" data-event-opts="{{[['change',[['paymentGiftChange',['$event']]]]]}}" bindchange="__e"><label><checkbox value="restaurant" checked="{{$root.m27?true:false}}"></checkbox>下单</label><label><checkbox value="recharge" checked="{{$root.m28?true:false}}"></checkbox>充值</label></checkbox-group></view></block></view></block><block wx:if="{{info.paygive==1}}"><view class="form-item"><view class="f1">支付金额范围</view><view class="amount-range-view flex"><input style="text-align:right;" type="text" name="paygive_minprice" placeholder="请输入金额" placeholder-style="color:#888" value="{{info.paygive_minprice}}"/><view style="padding:0px 20rpx;">-</view><input type="text" name="paygive_maxprice" placeholder="请输入金额" placeholder-style="color:#888" value="{{info.paygive_maxprice}}"/>元</view></view></block><block wx:if="{{pageType}}"><view class="form-item"><view class="f1">购买商品赠送<text style="color:red;"></text></view><view class="f2"><radio-group class="radio-group" name="buyprogive" data-event-opts="{{[['change',[['bindBuyprogiveChange',['$event']]]]]}}" bindchange="__e"><label><radio value="0" checked="{{info.buyprogive==0?true:false}}"></radio>关闭</label><label style="margin-left:20rpx;"><radio value="1" checked="{{info.buyprogive==1?true:false}}"></radio>开启</label></radio-group></view></view></block><block wx:if="{{info.buyprogive==1}}"><view class="form-item"><view class="flex flex-col addshow-list-view"><view class="flex title-view"><view>商品列表</view><view data-event-opts="{{[['tap',[['addshop',[1]]]]]}}" class="but-class" style="{{'background:'+('linear-gradient(-90deg,'+$root.m29+' 0%,rgba('+$root.m30+',0.8) 100%)')+';'}}" bindtap="__e">添加商品</view></view><block wx:if="{{$root.g5}}"><view class="product"><block wx:for="{{giftProductsList}}" wx:for-item="item" wx:for-index="index2" wx:key="index2"><block><view class="item flex"><view class="img-view"><block wx:if="{{item.pic}}"><image src="{{item.pic}}"></image></block><block wx:else><view class="img-view-empty"></view></block></view><view class="info"><view class="f1">{{item.name}}</view><view class="modify-price flex-y-center"><view class="f2">赠送数量：</view><input class="inputPrice" type="digit" data-event-opts="{{[['input',[['__set_model',['$0','give_num','$event',[]],[[['giftProductsList','',index2]]]],['inputNumChange',['$event',0]]]]]}}" value="{{item.give_num}}" bindinput="__e"/></view></view><view data-event-opts="{{[['tap',[['clearShopCartFn',['$0',1],[[['giftProductsList','',index2,'id']]]]]]]}}" class="del-view flex-y-center" style="color:#999999;font-size:24rpx;" catchtap="__e"><image style="width:24rpx;height:24rpx;margin-right:6rpx;" src="{{pre_url+'/static/img/del.png'}}"></image></view></view></block></block></view></block></view></view></block><block wx:if="{{pageType}}"><view class="form-item"><view class="f1" style="width:250rpx;">购买服务商品赠送<text style="color:red;"></text></view><view class="f2"><radio-group class="radio-group" name="buyyuyueprogive" data-event-opts="{{[['change',[['bindBuyyuyueprogiveChange',['$event']]]]]}}" bindchange="__e"><label><radio value="0" checked="{{info.buyyuyueprogive==0?true:false}}"></radio>关闭</label><label style="margin-left:20rpx;"><radio value="1" checked="{{info.buyyuyueprogive==1?true:false}}"></radio>开启</label></radio-group></view></view></block><block wx:if="{{info.buyyuyueprogive==1}}"><view class="form-item"><view class="flex flex-col addshow-list-view"><view class="flex title-view"><view>服务商品列表</view><view data-event-opts="{{[['tap',[['addshopProgive',[1]]]]]}}" class="but-class" style="{{'background:'+('linear-gradient(-90deg,'+$root.m31+' 0%,rgba('+$root.m32+',0.8) 100%)')+';'}}" bindtap="__e">添加商品</view></view><block wx:if="{{$root.g6}}"><view class="product"><block wx:for="{{giftProductsLists}}" wx:for-item="item" wx:for-index="index2" wx:key="index2"><block><view class="item flex"><view class="img-view"><block wx:if="{{item.pic}}"><image src="{{item.pic}}"></image></block><block wx:else><view class="img-view-empty"></view></block></view><view class="info"><view class="f1">{{item.name}}</view><view class="modify-price flex-y-center"><view class="f2">赠送数量：</view><input class="inputPrice" type="digit" data-event-opts="{{[['input',[['__set_model',['$0','give_num','$event',[]],[[['giftProductsLists','',index2]]]],['inputNumChange',['$event',1]]]]]}}" value="{{item.give_num}}" bindinput="__e"/></view></view><view data-event-opts="{{[['tap',[['clearShopCartFn2',['$0',1],[[['giftProductsLists','',index2,'id']]]]]]]}}" class="del-view flex-y-center" style="color:#999999;font-size:24rpx;" catchtap="__e"><image style="width:24rpx;height:24rpx;margin-right:6rpx;" src="{{pre_url+'/static/img/del.png'}}"></image></view></view></block></block></view></block></view></view></block></view><block wx:if="{{pageType}}"><view class="form-box"><view class="form-item"><view class="f1" style="width:228rpx;">字体颜色<text style="color:red;"></text></view><view class="f2"><input type="text" name="font_color" placeholder="如：#2B2B2B" placeholder-style="color:#888" value="{{info.font_color}}"/></view></view><view class="form-item"><view class="f1" style="width:228rpx;">标题颜色<text style="color:red;"></text></view><view class="f2"><input type="text" name="title_color" placeholder="如：#2B2B2B" placeholder-style="color:#888" value="{{info.title_color}}"/></view></view><view class="form-item"><view class="f1" style="width:228rpx;">背景颜色<text style="color:red;"></text></view><view class="f2"><input type="text" name="bg_color" placeholder="如：#FFFFFF" placeholder-style="color:#888" value="{{info.bg_color}}"/></view></view></view></block><button class="savebtn" style="{{('background:linear-gradient(90deg,'+$root.m33+' 0%,rgba('+$root.m34+',0.8) 100%)')}}" form-type="submit">提交</button><view style="height:50rpx;"></view></form></block></block><block wx:if="{{loading}}"><loading vue-id="3b8bdbe2-1" bind:__l="__l"></loading></block></view>