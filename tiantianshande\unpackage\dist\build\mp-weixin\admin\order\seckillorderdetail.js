require('../common/vendor.js');(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["admin/order/seckillorderdetail"],{"37b0":function(e,s,t){},5510:function(e,s,t){"use strict";t.r(s);var i=t("f960"),n=t("c4e2");for(var r in n)["default"].indexOf(r)<0&&function(e){t.d(s,e,(function(){return n[e]}))}(r);t("d304");var o=t("828b"),a=Object(o["a"])(n["default"],i["b"],i["c"],!1,null,null,null,!1,i["a"],void 0);s["default"]=a.exports},"98a8":function(e,s,t){"use strict";(function(e,s){var i=t("47a9");t("06e9");i(t("3240"));var n=i(t("5510"));e.__webpack_require_UNI_MP_PLUGIN__=t,s(n.default)}).call(this,t("3223")["default"],t("df3c")["createPage"])},c4e2:function(e,s,t){"use strict";t.r(s);var i=t("e4ad"),n=t.n(i);for(var r in i)["default"].indexOf(r)<0&&function(e){t.d(s,e,(function(){return i[e]}))}(r);s["default"]=n.a},d304:function(e,s,t){"use strict";var i=t("37b0"),n=t.n(i);n.a},e4ad:function(e,s,t){"use strict";Object.defineProperty(s,"__esModule",{value:!0}),s.default=void 0;var i=getApp(),n={data:function(){return{opt:{},loading:!1,isload:!1,menuindex:-1,pre_url:i.globalData.pre_url,expressdata:[],express_index:0,express_no:"",prodata:"",detail:"",team:{},prolist:"",shopset:"",storeinfo:"",lefttime:"",peisonguser:[],peisonguser2:[],index2:0,express_pic:"",express_fhname:"",express_fhaddress:"",express_shname:"",express_shaddress:"",express_remark:""}},onLoad:function(e){this.opt=i.getopts(e),this.getdata()},onPullDownRefresh:function(){this.getdata()},onUnload:function(){clearInterval(null)},methods:{getdata:function(){var e=this;e.loading=!0,i.get("ApiAdminOrder/seckillorderdetail",{id:e.opt.id},(function(s){e.loading=!1,e.expressdata=s.expressdata,e.detail=s.detail,e.team=s.team,e.storeinfo=s.storeinfo,e.loaded()}))},setremark:function(){this.$refs.dialogSetremark.open()},setremarkconfirm:function(e,s){this.$refs.dialogSetremark.close();var t=this;i.post("ApiAdminOrder/setremark",{type:"seckill",orderid:t.detail.id,content:s},(function(e){i.success(e.msg),setTimeout((function(){t.getdata()}),1e3)}))},fahuo:function(){10==this.detail.freight_type?this.$refs.dialogExpress10.open():this.$refs.dialogExpress.open()},dialogExpressClose:function(){this.$refs.dialogExpress.close()},dialogExpress10Close:function(){this.$refs.dialogExpress10.close()},expresschange:function(e){this.express_index=e.detail.value},setexpressno:function(e){this.express_no=e.detail.value},confirmfahuo:function(){this.$refs.dialogExpress.close();var e=this,s=this.expressdata[this.express_index];i.post("ApiAdminOrder/sendExpress",{type:"seckill",orderid:e.detail.id,express_no:e.express_no,express_com:s},(function(s){i.success(s.msg),setTimeout((function(){e.getdata()}),1e3)}))},setexpress_pic:function(e){this.express_pic=e.detail.value},setexpress_fhname:function(e){this.express_fhname=e.detail.value},setexpress_fhaddress:function(e){this.express_fhaddress=e.detail.value},setexpress_shname:function(e){this.express_shname=e.detail.value},setexpress_shaddress:function(e){this.express_shaddress=e.detail.value},setexpress_remark:function(e){this.express_remark=e.detail.value},confirmfahuo10:function(){this.$refs.dialogExpress10.close();var e=this;this.expressdata[this.express_index];i.post("ApiAdminOrder/sendExpress",{type:"seckill",orderid:e.detail.id,pic:e.express_pic,fhname:e.express_fhname,fhaddress:e.express_fhaddress,shname:e.express_shname,shaddress:e.express_shaddress,remark:e.express_remark},(function(s){i.success(s.msg),setTimeout((function(){e.getdata()}),1e3)}))},ispay:function(e){var s=this,t=e.currentTarget.dataset.id;i.confirm("确定要改为已支付吗?",(function(){i.showLoading("提交中"),i.post("ApiAdminOrder/ispay",{type:"seckill",orderid:t},(function(e){i.showLoading(!1),i.success(e.msg),setTimeout((function(){s.getdata()}),1e3)}))}))},hexiao:function(e){var s=this,t=e.currentTarget.dataset.id;i.confirm("确定要核销并改为已完成状态吗?",(function(){i.showLoading("提交中"),i.post("ApiAdminOrder/hexiao",{type:"seckill",orderid:t},(function(e){i.showLoading(!1),i.success(e.msg),setTimeout((function(){s.getdata()}),1e3)}))}))},delOrder:function(e){var s=e.currentTarget.dataset.id;i.showLoading("删除中"),i.confirm("确定要删除该订单吗?",(function(){i.post("ApiAdminOrder/delOrder",{type:"seckill",orderid:s},(function(e){i.showLoading(!1),i.success(e.msg),setTimeout((function(){i.goto("shoporder")}),1e3)}))}))},closeOrder:function(e){var s=this,t=e.currentTarget.dataset.id;i.confirm("确定要关闭该订单吗?",(function(){i.showLoading("提交中"),i.post("ApiAdminOrder/closeOrder",{type:"seckill",orderid:t},(function(e){i.showLoading(!1),i.success(e.msg),setTimeout((function(){s.getdata()}),1e3)}))}))},refundnopass:function(e){var s=this,t=e.currentTarget.dataset.id;i.confirm("确定要驳回退款申请吗?",(function(){i.showLoading("提交中"),i.post("ApiAdminOrder/refundnopass",{type:"seckill",orderid:t},(function(e){i.showLoading(!1),i.success(e.msg),setTimeout((function(){s.getdata()}),1e3)}))}))},refundpass:function(e){var s=this,t=e.currentTarget.dataset.id;i.confirm("确定要审核通过并退款吗?",(function(){i.showLoading("提交中"),i.post("ApiAdminOrder/refundpass",{type:"seckill",orderid:t},(function(e){i.showLoading(!1),i.success(e.msg),setTimeout((function(){s.getdata()}),1e3)}))}))},peisong:function(){var e=this;e.loading=!0,i.post("ApiAdminOrder/getpeisonguser",{type:"seckill_order",orderid:e.detail.id},(function(s){e.loading=!1;var t=s.peisonguser,n=s.paidantype,r=s.psfee,o=s.ticheng,a=[];for(var d in t)a.push(t[d].title);if(e.peisonguser=s.peisonguser,e.peisonguser2=a,1==n)e.$refs.dialogPeisong.open();else{if(0==e.detail.bid)var u="选择配送员配送，订单将发布到抢单大厅由配送员抢单，配送员提成￥"+o+"，确定要配送员配送吗？";else u="选择配送员配送，订单将发布到抢单大厅由配送员抢单，需扣除配送费￥"+r+"，确定要配送员配送吗？";if(2==n)var c="-1";else c="0";i.confirm(u,(function(){i.post("ApiAdminOrder/peisong",{type:"seckill_order",orderid:e.detail.id,psid:c},(function(s){i.success(s.msg),setTimeout((function(){e.getdata()}),1e3)}))}))}}))},dialogPeisongClose:function(){this.$refs.dialogPeisong.close()},peisongChange:function(e){this.index2=e.detail.value},confirmPeisong:function(){var e=this,s=this.peisonguser[this.index2].id;i.post("ApiAdminOrder/peisong",{type:"seckill_order",orderid:e.detail.id,psid:s},(function(s){i.success(s.msg),e.$refs.dialogPeisong.close(),setTimeout((function(){e.getdata()}),1e3)}))},uploadimg:function(e){var s=this,t=parseInt(e.currentTarget.dataset.pernum);t||(t=1);var n=e.currentTarget.dataset.field,r=s[n];r||(r=[]),i.chooseImage((function(e){for(var t=0;t<e.length;t++)r.push(e[t]);"express_pic"==n&&(s.express_pic=r[0])}),t)},removeimg:function(e){e.currentTarget.dataset.index;var s=e.currentTarget.dataset.field;"express_pic"==s&&(this.express_pic="")}}};s.default=n},f960:function(e,s,t){"use strict";t.d(s,"b",(function(){return n})),t.d(s,"c",(function(){return r})),t.d(s,"a",(function(){return i}));var i={uniPopup:function(){return Promise.all([t.e("common/vendor"),t.e("components/uni-popup/uni-popup")]).then(t.bind(null,"ca44a"))},uniPopupDialog:function(){return t.e("components/uni-popup-dialog/uni-popup-dialog").then(t.bind(null,"267c"))},loading:function(){return t.e("components/loading/loading").then(t.bind(null,"ceaa"))},dpTabbar:function(){return t.e("components/dp-tabbar/dp-tabbar").then(t.bind(null,"b875"))},popmsg:function(){return t.e("components/popmsg/popmsg").then(t.bind(null,"2bf2"))}},n=function(){var e=this,s=e.$createElement,t=(e._self._c,e.isload?e.t("会员"):null),i=e.isload&&e.detail.disprice>0?e.t("会员"):null,n=e.isload&&e.detail.couponmoney>0?e.t("优惠券"):null,r=e.isload&&e.detail.scoredk>0?e.t("积分"):null,o=e.isload&&e.detail.isfuwu&&e.detail.fuwuendtime>0?e._.dateFormat(e.detail.fuwuendtime,"Y-m-d H:i"):null,a=e.isload?e.detail.formdata.length:null;e.$mp.data=Object.assign({},{$root:{m0:t,m1:i,m2:n,m3:r,g0:o,g1:a}})},r=[]}},[["98a8","common/runtime","common/vendor"]]]);