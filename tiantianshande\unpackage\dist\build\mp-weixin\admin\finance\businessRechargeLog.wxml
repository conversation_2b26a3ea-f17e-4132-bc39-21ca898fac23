<view><block wx:if="{{isload}}"><block><view class="topsearch flex-y-center"><picker class="date-picker" mode="date" value="{{start_date}}" data-event-opts="{{[['change',[['startDateChange',['$event']]]]]}}" bindchange="__e"><view class="picker-text">{{start_date||'开始日期'}}</view></picker><text class="date-separator">至</text><picker class="date-picker" mode="date" value="{{end_date}}" data-event-opts="{{[['change',[['endDateChange',['$event']]]]]}}" bindchange="__e"><view class="picker-text">{{end_date||'结束日期'}}</view></picker><view data-event-opts="{{[['tap',[['getdata',['$event']]]]]}}" class="search-btn" bindtap="__e">查询</view></view><view class="tab-nav"><block wx:for="{{tabs}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['changeTab',['$0'],[[['tabs','',index,'value']]]]]]]}}" class="{{['tab-item',(status===item.value)?'active':'']}}" bindtap="__e">{{''+item.name+''}}</view></block></view><view class="statistics-bar"><view class="statistics-item"><text class="label">充值总金额：</text><text class="value">{{"￥"+(statistics.total_amount||'0.00')}}</text></view><view class="statistics-item"><text class="label">已通过金额：</text><text class="value">{{"￥"+(statistics.approved_amount||'0.00')}}</text></view></view><block wx:if="{{$root.g0}}"><view class="content"><view class="label"><text class="t1">{{"充值记录（共"+total+"条）"}}</text></view><block wx:for="{{datalist}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['gotoDetail',['$0'],[[['datalist','',index,'id']]]]]]]}}" class="item" bindtap="__e"><view class="item-header"><text class="amount">{{"￥"+item.amount}}</text><text class="{{['status','status-'+item.status]}}">{{item.status_text}}</text></view><view class="item-body"><view class="item-row"><text class="label">支付方式：</text><text class="value">{{item.pay_type_text}}</text></view><view class="item-row"><text class="label">申请时间：</text><text class="value">{{item.create_time}}</text></view><block wx:if="{{item.status!=0}}"><view class="item-row"><text class="label">审核时间：</text><text class="value">{{item.audit_time||'-'}}</text></view></block><block wx:if="{{item.remark}}"><view class="item-row"><text class="label">备注说明：</text><text class="value">{{item.remark}}</text></view></block></view></view></block></view></block><block wx:if="{{nodata}}"><nodata vue-id="1e0f7f1c-1" bind:__l="__l"></nodata></block><block wx:if="{{nomore}}"><nomore vue-id="1e0f7f1c-2" bind:__l="__l"></nomore></block><view data-event-opts="{{[['tap',[['gotoRecharge',['$event']]]]]}}" class="floating-btn" bindtap="__e"><image class="add-icon" src="/static/img/add.png"></image><text>申请充值</text></view></block></block><block wx:if="{{loading}}"><loading vue-id="1e0f7f1c-3" bind:__l="__l"></loading></block><popmsg class="vue-ref" vue-id="1e0f7f1c-4" data-ref="popmsg" bind:__l="__l"></popmsg></view>