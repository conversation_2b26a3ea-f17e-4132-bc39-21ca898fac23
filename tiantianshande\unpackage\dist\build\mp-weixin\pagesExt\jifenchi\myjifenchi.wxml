<view class="container"><block wx:if="{{isload}}"><block><view class="content"><view class="info-item" style="height:136rpx;line-height:136rpx;"><view class="t1" style="flex:1;">我的积分池详情</view></view></view><view class="content"><view class="info-item" data-url="jingtaiZong" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="t1">积分总额</view><view class="t2">{{userinfo.zongscore}}</view><image class="t3" src="/static/img/arrowright.png"></image></view></view><view class="content"><view class="info-item" style="height:136rpx;line-height:136rpx;"><view class="t1" style="flex:1;">积分自动转余额</view></view></view><view class="content"><view class="info-item" data-url="usercenter" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><text class="t1">规则</text><text class="t2">{{userinfo.guize}}</text><image class="t3" src="/static/img/arrowright.png"></image></view><view class="info-item" data-url="jingtaiA" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><text class="t1">静态已转入余额</text><text class="t2">{{userinfo.countA}}</text><image class="t3" src="/static/img/arrowright.png"></image></view><view class="info-item" data-url="jingtaiB" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><text class="t1">动态已转入余额</text><text class="t2">{{userinfo.countB}}</text><image class="t3" src="/static/img/arrowright.png"></image></view></view></block></block><block wx:if="{{loading}}"><loading vue-id="0f4e208f-1" bind:__l="__l"></loading></block><dp-tabbar vue-id="0f4e208f-2" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="0f4e208f-3" data-ref="popmsg" bind:__l="__l"></popmsg></view>