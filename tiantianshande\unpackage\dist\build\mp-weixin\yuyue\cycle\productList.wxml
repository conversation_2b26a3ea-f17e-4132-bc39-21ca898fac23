<view class="container"><block wx:if="{{isload}}"><block><view class="topsearch flex-y-center"><view class="f1 flex-y-center"><image class="img" src="/static/img/search_ico.png"></image><input placeholder="输入服务名称搜索" placeholder-style="font-size:24rpx;color:#C2C2C2" data-event-opts="{{[['confirm',[['searchConfirm',['$event']]]]]}}" value="{{keyword}}" bindconfirm="__e"/></view></view><view class="product-list"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><view class="product-item flex" data-url="{{'/yuyue/cycle/productDetail?id='+item.$orig.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image class="product-pic" src="{{item.$orig.pic}}"></image><view class="product-info flex1"><view class="p1">{{item.$orig.name}}</view><view class="p2"><text class="ps_title flex-y-center" style="{{'background:'+('rgba('+item.m0+',0.12)')+';'+('color:'+(item.m1)+';')}}">{{"共"+item.$orig.total_period+"期"}}</text><block wx:if="{{item.$orig.min_period>1}}"><text class="ps_title flex-y-center" style="{{'background:'+('rgba('+item.m2+',0.12)')+';'+('color:'+(item.m3)+';')}}">{{'| '+item.$orig.min_period+"期起购"}}</text></block></view><view class="p3 flex"><view class="price flex1"><text style="font-size:24rpx;">￥</text>{{item.$orig.sell_price}}<text style="font-size:24rpx;">/期</text></view><view class="sales">{{"已售 "+item.$orig.sales}}</view></view></view></view></block></block></view><block wx:if="{{nomore}}"><nomore vue-id="46a6a482-1" bind:__l="__l"></nomore></block><block wx:if="{{nodata}}"><nodata vue-id="46a6a482-2" bind:__l="__l"></nodata></block></block></block><block wx:if="{{loading}}"><loading vue-id="46a6a482-3" bind:__l="__l"></loading></block><dp-tabbar vue-id="46a6a482-4" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="46a6a482-5" data-ref="popmsg" bind:__l="__l"></popmsg></view>