<view class="container"><block wx:if="{{isload}}"><block><block wx:for="{{datalist}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="paytype"><view class="payment-info"><view class="payment-row"><view class="payment-label">{{"付款 "+(index+1)}}</view><block wx:if="{{item.status==0}}"><text class="status pending">待上传</text></block><block wx:if="{{item.status==1}}"><text class="status uploaded">已上传</text></block><block wx:if="{{item.status==2}}"><text class="status uploaded">已确认</text></block></view><view class="payment-row"><view class="payment-label">付款人:</view><text class="info">{{item.fukuanmember.nickname}}</text></view><view class="payment-row"><view class="payment-label">需支付金额:</view><text class="amount">{{item.trade_amount+"元"}}</text></view></view><block wx:if="{{item.payimg}}"><view class="voucher-container"><view class="voucher-text">支付凭证</view><image class="voucher-image" src="{{item.payimg}}" data-event-opts="{{[['tap',[['previewImage',['$0'],[[['datalist','',index,'payimg']]]]]]]}}" bindtap="__e"></image></view></block><block wx:if="{{item.payimg&&item.status==1}}"><view><view class="voucher-text"><view data-event-opts="{{[['tap',[['queren',['$0'],[[['datalist','',index,'id']]]]]]]}}" class="voucher-text" style="text-align:center;font-size:40rpx;padding-bottom:20rpx;" bindtap="__e">确认收款</view></view></view></block></view></block></block></block><block wx:if="{{invite_status&&invite_free}}"><view data-event-opts="{{[['tap',[['closeInvite',['$event']]]]]}}" style="width:100%;height:100%;background-color:#000;position:fixed;opacity:0.5;z-index:99;top:0;" bindtap="__e"></view></block><block wx:if="{{invite_status&&invite_free}}"><view style="width:700rpx;margin:0 auto;position:fixed;top:10%;left:25rpx;z-index:100;"><view data-event-opts="{{[['tap',[['gotoInvite',['$event']]]]]}}" style="background-color:#fff;border-radius:20rpx;overflow:hidden;width:100%;min-height:700rpx;" bindtap="__e"><image style="width:100%;height:auto;" src="{{invite_free.pic}}" mode="widthFix"></image></view><block wx:if="{{invite_status&&invite_free}}"><view data-event-opts="{{[['tap',[['closeInvite',['$event']]]]]}}" style="width:80rpx;height:80rpx;line-height:80rpx;text-align:center;font-size:30rpx;background-color:#fff;margin:0 auto;border-radius:50%;margin-top:20rpx;" bindtap="__e">X</view></block></view></block><block wx:if="{{loading}}"><loading vue-id="3c95ce32-1" bind:__l="__l"></loading></block><dp-tabbar vue-id="3c95ce32-2" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="3c95ce32-3" data-ref="popmsg" bind:__l="__l"></popmsg></view>