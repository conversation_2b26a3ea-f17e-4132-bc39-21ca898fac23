<view class="container"><block wx:if="{{isload}}"><block><view class="stats-card"><view class="stats-item"><text class="stats-number">{{"¥"+statistics.total_income}}</text><text class="stats-label">总收益</text></view><view class="stats-item"><text class="stats-number">{{"¥"+statistics.today_income}}</text><text class="stats-label">今日收益</text></view><view class="stats-item"><text class="stats-number">{{"¥"+statistics.month_income}}</text><text class="stats-label">本月收益</text></view></view><dd-tab vue-id="170c27f4-1" itemdata="{{['收益明细','转换记录']}}" itemst="{{['0','1']}}" st="{{st}}" isfixed="{{false}}" data-event-opts="{{[['^changetab',[['changetab']]]]}}" bind:changetab="__e" bind:__l="__l"></dd-tab><block wx:if="{{st==0}}"><view class="filter-container"><view class="filter-row"><picker value="{{incomeTypeIndex}}" range="{{incomeTypeList}}" range-key="name" data-event-opts="{{[['change',[['onIncomeTypeChange',['$event']]]]]}}" bindchange="__e"><view class="filter-item"><text>{{incomeTypeList[incomeTypeIndex].name}}</text><text class="iconfont iconjiantou"></text></view></picker></view><view class="filter-row"><picker mode="date" value="{{startDate}}" data-event-opts="{{[['change',[['onStartDateChange',['$event']]]]]}}" bindchange="__e"><view class="filter-item"><text>{{"开始日期："+(startDate||'请选择')}}</text></view></picker><picker mode="date" value="{{endDate}}" data-event-opts="{{[['change',[['onEndDateChange',['$event']]]]]}}" bindchange="__e"><view class="filter-item"><text>{{"结束日期："+(endDate||'请选择')}}</text></view></picker></view></view></block><view class="content"><block wx:if="{{st==0}}"><block><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="income-item"><view class="item-header"><text class="item-title">{{item.$orig.description||'订单佣金'}}</text><text class="{{['item-amount',item.$orig.amount>0?'positive':'negative']}}">{{''+(item.$orig.amount>0?'+':'')+"¥"+item.$orig.amount+''}}</text></view><view class="item-content"><view class="item-info"><text class="info-label">收益类型：</text><text class="info-value">{{item.m0}}</text></view><block wx:if="{{item.$orig.order_sn}}"><view class="item-info"><text class="info-label">订单号：</text><text class="info-value">{{item.$orig.order_sn}}</text></view></block><block wx:if="{{item.$orig.order_amount>0}}"><view class="item-info"><text class="info-label">订单金额：</text><text class="info-value">{{"¥"+item.$orig.order_amount}}</text></view></block><view class="item-info"><text class="info-label">佣金比例：</text><text class="info-value">{{item.$orig.commission_rate+"%"}}</text></view><view class="item-info"><text class="info-label">获得时间：</text><text class="info-value">{{item.$orig.createtime_format}}</text></view></view></view></block></block></block><block wx:if="{{st==1}}"><block><block wx:for="{{$root.l1}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="withdraw-item"><view class="item-header"><text class="item-title">转换申请</text><text class="item-amount">{{"¥"+item.$orig.amount}}</text></view><view class="item-content"><view class="item-info"><text class="info-label">转换方式：</text><text class="info-value">{{item.m1}}</text></view><view class="item-info"><text class="info-label">手续费：</text><text class="info-value">{{"¥"+item.$orig.fee}}</text></view><view class="item-info"><text class="info-label">实际到账：</text><text class="info-value">{{"¥"+item.$orig.actual_amount}}</text></view><view class="item-info"><text class="info-label">申请时间：</text><text class="info-value">{{item.$orig.createtime_format}}</text></view><block wx:if="{{item.$orig.remark}}"><view class="item-info"><text class="info-label">备注：</text><text class="info-value">{{item.$orig.remark}}</text></view></block></view><view class="item-status"><text class="status-text" style="{{'color:'+(item.$orig.status_color)+';'}}">{{item.$orig.status_text}}</text></view></view></block></block></block></view></block></block><block wx:if="{{nodata}}"><nodata vue-id="170c27f4-2" bind:__l="__l"></nodata></block><block wx:if="{{nomore}}"><nomore vue-id="170c27f4-3" bind:__l="__l"></nomore></block><block wx:if="{{loading}}"><loading vue-id="170c27f4-4" bind:__l="__l"></loading></block><dp-tabbar vue-id="170c27f4-5" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="170c27f4-6" data-ref="popmsg" bind:__l="__l"></popmsg></view>