require('../common/vendor.js');(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pagesExt/lipin/prodh"],{"12c0":function(t,e,i){"use strict";var a=i("6ce8"),r=i.n(a);r.a},"13fb":function(t,e,i){"use strict";(function(t){Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var i=getApp(),a={data:function(){return{opt:{},loading:!1,isload:!1,menuindex:-1,pre_url:i.globalData.pre_url,test:"test",havetongcheng:0,address:[],usescore:0,scoredk_money:0,totalprice:"0.00",bid:0,nowbid:0,needaddress:1,linkman:"",tel:"",userinfo:{},pstimeDialogShow:!1,pstimeIndex:-1,manjian_money:0,cxid:0,latitude:"",longitude:"",allbuydata:"",alltotalprice:"",needChoose:!1,chooseIndex:"",chooseInfo:""}},onLoad:function(t){this.opt=i.getopts(t),this.getdata()},onPullDownRefresh:function(){this.getdata()},methods:{getdata:function(){var t=this;i.get("ApiLipin/prodh",{dhcode:t.opt.dhcode},(function(e){0!=e.status?(t.havetongcheng=e.havetongcheng,t.address=e.address,t.linkman=e.linkman,t.tel=e.tel,t.userinfo=e.userinfo,t.allbuydata=e.allbuydata,t.needLocation=e.needLocation,t.calculatePrice(),t.loaded(),1==e.needLocation&&i.getLocation((function(e){var i=e.latitude,a=e.longitude;t.latitude=i,t.longitude=a;var r=t.allbuydata;for(var o in r){var n=r[o].freightList;for(var s in n)if(1==n[s].pstype){var l=n[s].storedata;if(l){for(var d in l)if(i&&a&&l[d].latitude&&l[d].longitude){var u=t.getDistance(i,a,l[d].latitude,l[d].longitude);l[d].juli=u}for(var d in l.sort((function(t,e){return t["juli"]-e["juli"]})),l)l[d].juli&&(l[d].juli=l[d].juli+"千米");r[o].freightList[s].storedata=l}}}t.allbuydata=r})),1==e.hdinfo.num_type&&(t.needChoose=!0)):e.msg?i.alert(e.msg,(function(){e.url?i.goto(e.url):i.goback()})):e.url?i.goto(e.url):i.alert("您没有权限购买该商品")}))},chooseClick:function(t,e){this.chooseIndex=e,this.chooseInfo=t},scoredk:function(t){var e=t.detail.value[0];e||(e=0),this.usescore=e,this.calculatePrice()},inputLinkman:function(t){this.linkman=t.detail.value},inputTel:function(t){this.tel=t.detail.value},inputfield:function(t){var e=this.allbuydata,i=t.currentTarget.dataset.bid,a=t.currentTarget.dataset.field;e[i][a]=t.detail.value,this.allbuydata=e},chooseAddress:function(){i.goto("/pages/address/address?fromPage=buy&type="+(1==this.havetongcheng?"1":"0"))},calculatePrice:function(){this.address;var t=this.allbuydata,e=0,i=0;for(var a in t){var r=parseFloat(t[a].product_price),o=(parseFloat(t[a].manjian_money),parseFloat(t[a].coupon_money),t[a].freightList[t[a].freightkey]),n=o["freight_price"];1!=o.pstype&&3!=o.pstype&&4!=o.pstype&&(i=1),4==t[a].coupontype&&(n=0,0);var s=r;s<0&&(s=0),s+=n,t[a].freight_price=n.toFixed(2),t[a].totalprice=s.toFixed(2),e+=s,n}this.needaddress=i,e<0&&(e=0),e=e.toFixed(2),this.alltotalprice=e,this.allbuydata=t},changeFreight:function(t){var e=this.allbuydata,i=t.currentTarget.dataset.bid,a=t.currentTarget.dataset.index;e[i].freightList;e[i].freightkey=a,this.allbuydata=e,this.calculatePrice(),this.allbuydata[i].editorFormdata=[]},chooseFreight:function(e){var i=this,a=i.allbuydata,r=e.currentTarget.dataset.bid;console.log(r),console.log(a);for(var o=a[r].freightList,n=[],s=0;s<o.length;s++)n.push(o[s].name);t.showActionSheet({itemList:n,success:function(t){t.tapIndex>=0&&(a[r].freightkey=t.tapIndex,i.allbuydata=a,i.calculatePrice())}})},choosePstime:function(t){for(var e=this.allbuydata,a=t.currentTarget.dataset.bid,r=e[a].freightkey,o=e[a].freightList,n=(o[r],o[r].pstimeArr),s=[],l=0;l<n.length;l++)s.push(n[l].title);0!=s.length?(this.nowbid=a,this.pstimeDialogShow=!0,this.pstimeIndex=-1):i.alert("当前没有可选"+(1==o[r].pstype?"取货":"配送")+"时间段")},pstimeRadioChange:function(t){var e=this.allbuydata,i=t.currentTarget.dataset.index;console.log(i);var a=this.nowbid,r=e[a].freightkey,o=e[a].freightList,n=(o[r],o[r].pstimeArr),s=n[i];e[a].pstimetext=s.title,e[a].freight_time=s.value,this.allbuydata=e,this.pstimeDialogShow=!1},hidePstimeDialog:function(){this.pstimeDialogShow=!1},choosestore:function(t){var e=t.currentTarget.dataset.bid,i=t.currentTarget.dataset.index,a=this.allbuydata,r=a[e],o=r.freightkey;a[e].freightList[o].storekey=i,this.allbuydata=a},topay:function(t){var e=this.needaddress,a=this.address.id,r=this.linkman,o=this.tel,n=this.usescore,s=this.opt.frompage?this.opt.frompage:"",l=this.allbuydata;if(0==e&&(a=0),1!=e||void 0!=a)if(this.needChoose&&""==this.chooseInfo)i.error("请选择商品");else{var d=[];for(var u in l){var c=l[u].freightkey;if(1==l[u].freightList[c].pstimeset&&""==l[u].freight_time)return void i.error("请选择"+(1==l[u].freightList[c].pstype?"取货":"配送")+"时间");if(1==l[u].freightList[c].pstype)var h=l[u].freightList[c].storekey,f=l[u].freightList[c].storedata[h].id;else f=0;for(var g=l[u].freightList[c].formdata,p=t.detail.value,m={},b=0;b<g.length;b++){var v="form"+l[u].bid+"_"+b;if(1==g[b].val3&&(""===p[v]||void 0===p[v]||0==p[v].length))return void i.alert(g[b].val1+" 必填");"selector"==g[b].key&&(p[v]=g[b].val2[p[v]]),m["form"+b]=p[v]}var y=l[u].prodatastr;if(this.needChoose){var _=this.chooseInfo.product.id+","+this.chooseInfo.guige.id+","+this.chooseInfo.num;y=_}d.push({bid:l[u].bid,prodata:y,cuxiaoid:l[u].cuxiaoid,couponrid:l[u].couponrid,freight_id:l[u].freightList[c].id,freight_time:l[u].freight_time,storeid:f,formdata:m})}i.showLoading("提交中"),i.post("ApiLipin/createOrder",{dhcode:this.opt.dhcode,frompage:s,buydata:d,addressid:a,linkman:r,tel:o,usescore:n},(function(t){i.showLoading(!1),0!=t.status?i.alert(t.msg,(function(){i.goto("/pages/my/usercenter","reLaunch")})):i.error(t.msg)}))}else i.error("请选择收货地址")},handleClickMask:function(){},openLocation:function(e){var i=this.allbuydata,a=e.currentTarget.dataset.bid,r=e.currentTarget.dataset.freightkey,o=e.currentTarget.dataset.storekey,n=i[a].freightList[r],s=n.storedata[o];console.log(s);var l=parseFloat(s.latitude),d=parseFloat(s.longitude),u=s.name;t.openLocation({latitude:l,longitude:d,name:u,scale:13})},editorChooseImage:function(t){var e=this,a=t.currentTarget.dataset.bid,r=t.currentTarget.dataset.idx,o=e.allbuydata[a].editorFormdata;o||(o=[]),i.chooseImage((function(t){o[r]=t[0],console.log(o),e.allbuydata[a].editorFormdata=o,e.test=Math.random()}))},editorBindPickerChange:function(t){var e=t.currentTarget.dataset.bid,i=t.currentTarget.dataset.idx,a=t.detail.value,r=this.allbuydata[e].editorFormdata;r||(r=[]),r[i]=a,console.log(r),this.allbuydata[e].editorFormdata=r,this.test=Math.random()}}};e.default=a}).call(this,i("df3c")["default"])},"1c52":function(t,e,i){"use strict";i.d(e,"b",(function(){return r})),i.d(e,"c",(function(){return o})),i.d(e,"a",(function(){return a}));var a={loading:function(){return i.e("components/loading/loading").then(i.bind(null,"ceaa"))},dpTabbar:function(){return i.e("components/dp-tabbar/dp-tabbar").then(i.bind(null,"b875"))},popmsg:function(){return i.e("components/popmsg/popmsg").then(i.bind(null,"2bf2"))}},r=function(){var t=this,e=t.$createElement,i=(t._self._c,t.isload?t.__map(t.allbuydata,(function(e,i){var a=t.__get_orig(e),r=t.__map(e.prodata,(function(e,i){var a=t.__get_orig(e),r=t.needChoose&&t.chooseIndex===i?t.t("color1"):null;return{$orig:a,m0:r}})),o=t.__map(e.freightList,(function(i,a){var r=t.__get_orig(i),o=e.freightkey==a?t.t("color1"):null,n=e.freightkey==a?t.t("color1rgb"):null;return{$orig:r,m1:o,m2:n}})),n=1==e.freightList[e.freightkey].minpriceset&&e.freightList[e.freightkey].minprice>0&&e.freightList[e.freightkey].minprice>e.product_price?(e.freightList[e.freightkey].minprice-e.product_price).toFixed(2):null,s=1==e.freightList[e.freightkey].pstype?t.__map(e.freightList[e.freightkey].storedata,(function(i,a){var r=t.__get_orig(i),o=e.freightList[e.freightkey].storekey==a?t.t("color1"):null;return{$orig:r,m3:o}})):null;return{$orig:a,l0:r,l1:o,g0:n,l2:s}})):null),a=t.isload?t.t("color1"):null,r=t.isload?t.t("color1rgb"):null,o=t.isload&&t.pstimeDialogShow?t.__map(t.allbuydata[t.nowbid].freightList[t.allbuydata[t.nowbid].freightkey].pstimeArr,(function(e,i){var a=t.__get_orig(e),r=t.allbuydata[t.nowbid].freight_time==e.value?t.t("color1"):null;return{$orig:a,m6:r}})):null;t.$mp.data=Object.assign({},{$root:{l3:i,m4:a,m5:r,l4:o}})},o=[]},"521b":function(t,e,i){"use strict";i.r(e);var a=i("1c52"),r=i("a0be");for(var o in r)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return r[t]}))}(o);i("12c0");var n=i("828b"),s=Object(n["a"])(r["default"],a["b"],a["c"],!1,null,null,null,!1,a["a"],void 0);e["default"]=s.exports},"5c16":function(t,e,i){"use strict";(function(t,e){var a=i("47a9");i("06e9");a(i("3240"));var r=a(i("521b"));t.__webpack_require_UNI_MP_PLUGIN__=i,e(r.default)}).call(this,i("3223")["default"],i("df3c")["createPage"])},"6ce8":function(t,e,i){},a0be:function(t,e,i){"use strict";i.r(e);var a=i("13fb"),r=i.n(a);for(var o in a)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(o);e["default"]=r.a}},[["5c16","common/runtime","common/vendor"]]]);