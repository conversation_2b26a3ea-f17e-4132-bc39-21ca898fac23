<view class="container"><block wx:if="{{isload}}"><block><form data-event-opts="{{[['submit',[['formSubmit',['$event']]]]]}}" bindsubmit="__e"><view class="mymoney" style="{{'background:'+($root.m0)+';'}}"><view class="f1">{{"我的可转化"+$root.m1}}</view><view class="f2"><text style="font-size:26rpx;">￥</text>{{userinfo.money}}</view><view class="f3" data-url="/pages/money/moneylog" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><text>余额明细</text><text class="iconfont iconjiantou" style="font-size:20rpx;"></text></view></view><view class="content2"><view class="item2"><view class="f1">转化金额(元)</view></view><view class="item3"><view class="f1">￥</view><view class="f2"><input class="input" type="digit" name="money" value="" placeholder="请输入转化金额" placeholder-style="color:#999;font-size:40rpx" data-event-opts="{{[['input',[['moneyinput',['$event']]]]]}}" bindinput="__e"/></view></view></view><view class="content2"><view class="item2"><view class="f1">{{'转化手续费：'+sysset.heishouxu+'%'}}</view></view></view><button data-event-opts="{{[['tap',[['formSubmit',['$event']]]]]}}" class="btn" style="{{'background:'+($root.m2)+';'}}" bindtap="__e">立即转入</button></form></block></block><block wx:if="{{loading}}"><loading vue-id="17fe4610-1" bind:__l="__l"></loading></block><dp-tabbar vue-id="17fe4610-2" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="17fe4610-3" data-ref="popmsg" bind:__l="__l"></popmsg></view>