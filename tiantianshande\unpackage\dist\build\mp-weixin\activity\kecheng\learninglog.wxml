<view class="container"><block wx:if="{{isload}}"><block><view class="user-profile"><view class="user-info"><view class="avatar-container"><image class="user-avatar" src="{{userInfo.headimg||pre_url+'/static/img/default_avatar.png'}}" mode="aspectFill"></image></view><view class="user-text"><view class="user-name">{{userInfo.nickname||'微信昵称'}}</view><view class="user-welcome">欢迎回来，继续学习吧</view></view></view><view class="page-title">学习中心</view></view><view class="function-buttons"><view data-event-opts="{{[['tap',[['navTo',['/activity/kecheng/orderlist']]]]]}}" class="function-button" bindtap="__e"><image src="{{pre_url+'/static/img/icon_bought.png'}}" mode="aspectFit"></image><text>已购</text></view><view data-event-opts="{{[['tap',[['navTo',['/pagesExa/my/favorite']]]]]}}" class="function-button" bindtap="__e"><image src="{{pre_url+'/static/img/icon_collect.png'}}" mode="aspectFit"></image><text>收藏</text></view><view data-event-opts="{{[['tap',[['navTo',['/activity/kecheng/learningbiji']]]]]}}" class="function-button" bindtap="__e"><image src="{{pre_url+'/static/img/icon_notes.png'}}" mode="aspectFit"></image><text>笔记</text></view><view data-event-opts="{{[['tap',[['navTo',['/pagesExb/training/index']]]]]}}" class="function-button" bindtap="__e"><image src="{{pre_url+'/static/img/icon_training.png'}}" mode="aspectFit"></image><text>训练营</text></view></view><view class="section-title"><text>学习历史</text></view><view class="filter-tabs"><view data-event-opts="{{[['tap',[['changeFilter',['all']]]]]}}" class="{{['tab-item',(filterType==='all')?'active':'']}}" bindtap="__e"><text>全部</text><block wx:if="{{counts.all>0}}"><text class="count">{{"("+counts.all+")"}}</text></block></view><view data-event-opts="{{[['tap',[['changeFilter',['purchased']]]]]}}" class="{{['tab-item',(filterType==='purchased')?'active':'']}}" bindtap="__e"><text>已购买</text><block wx:if="{{counts.purchased>0}}"><text class="count">{{"("+counts.purchased+")"}}</text></block></view><view data-event-opts="{{[['tap',[['changeFilter',['unpurchased']]]]]}}" class="{{['tab-item',(filterType==='unpurchased')?'active':'']}}" bindtap="__e"><text>未购买</text><block wx:if="{{counts.unpurchased>0}}"><text class="count">{{"("+counts.unpurchased+")"}}</text></block></view></view><view class="learning-log-container"><block wx:if="{{$root.g0}}"><block><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['gotoCourseOrDetail',['$0'],[[['filteredDatalist','',index]]]]]]]}}" class="log-item" catchtap="__e"><view class="course-info"><view class="course-pic-container" style="{{('background: conic-gradient(#1484fa 0deg '+item.$orig.deg+'deg'+',#eee '+item.$orig.deg+'deg '+item.$orig.degc+'deg);')}}"><image class="course-pic" src="{{item.$orig.course_pic}}" mode="aspectFill"></image></view><text class="pl">{{item.$orig.overall_progress_percent+"%"}}</text><view class="course-details"><view class="course-name-container"><view class="course-name">{{item.$orig.course_name}}</view><block wx:if="{{item.$orig.is_purchased===0}}"><view class="price-display"><block wx:if="{{item.$orig.course_price==0}}"><view class="free-price"><text>会员免费</text></view></block><block wx:else><view class="current-price"><text>{{"￥"+item.$orig.course_price}}</text></view><block wx:if="{{item.$orig.is_member_price}}"><view class="member-tag"><text>会员价</text></view></block></block></view></block></view><view class="course-stats"><text>{{"总 "+item.$orig.total_chapters+" 讲 | 已学 "+item.$orig.completed_chapters+" 讲"}}</text></view></view></view><view data-event-opts="{{[['tap',[['toggleChapter',[index]]]]]}}" class="{{['chapter-toggle',(item.$orig.showChapter)?'active':'']}}" catchtap="__e"><text>{{item.$orig.showChapter?'收起章节':'查看章节'}}</text><text class="{{['toggle-icon',(item.$orig.showChapter)?'rotate':'']}}">›</text></view><block wx:if="{{item.$orig.showChapter&&item.$orig.target_chapter_id}}"><view class="target-chapter-info"><view class="chapter-title">{{"应学章节："+item.$orig.target_chapter_name}}</view><view class="chapter-meta"><block wx:if="{{item.$orig.target_chapter_type===1}}"><text>类型：图文</text></block><block wx:if="{{item.$orig.target_chapter_type===2}}"><text>类型：音频</text></block><block wx:if="{{item.$orig.target_chapter_type===3}}"><text>类型：视频</text></block><block wx:if="{{item.$orig.target_chapter_duration&&(item.$orig.target_chapter_type===2||item.$orig.target_chapter_type===3)}}"><text>{{'| 时长：'+item.m0}}</text></block></view><view class="chapter-progress"><text>进度：</text><block wx:if="{{item.$orig.target_chapter_log_status===1}}"><block><text class="status-completed">已学完</text></block></block><block wx:else><block><block wx:if="{{item.$orig.target_chapter_type===1}}"><text>{{item.$orig.target_chapter_log_jindu==='已学完'?'已学完':'未学习'}}</text></block><block wx:else><text>{{item.$orig.target_chapter_log_jindu+"%"}}</text></block><block wx:if="{{item.$orig.target_chapter_current_time>0&&(item.$orig.target_chapter_type===2||item.$orig.target_chapter_type===3)}}"><text>{{'(已播：'+item.m1+")"}}</text></block></block></block></view></view></block><block wx:else><block wx:if="{{item.$orig.showChapter}}"><view class="target-chapter-info"><view class="chapter-title">暂无学习章节</view></view></block></block></view></block></block></block><block wx:if="{{showNoData}}"><nodata vue-id="17c37e62-1" text="{{$root.m2}}" bind:__l="__l" vue-slots="{{['img']}}"><image slot="img" src="{{pre_url+'/static/img/nodata.png'}}" mode="aspectFit"></image></nodata></block></view></block></block><block wx:if="{{loading}}"><loading vue-id="17c37e62-2" bind:__l="__l"></loading></block><dp-tabbar vue-id="17c37e62-3" opt="{{opt}}" data-event-opts="{{[['^getmenuindex',[['getmenuindex']]]]}}" bind:getmenuindex="__e" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="17c37e62-4" data-ref="popmsg" bind:__l="__l"></popmsg></view>