<view class="container"><block wx:if="{{isload}}"><block><view class="ordertop" style="{{('background:url('+pre_url+'/static/img/ordertop.png);background-size:100%')}}"></view><view class="address"><view class="info"><text class="t1" user-select="true" selectable="true">{{detail.title}}</text></view></view><block wx:if="{{detail.is_servic==1}}"><view class="product"><view class="content"><view data-url="{{'product?id='+detail.proid}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image src="{{detail.userInfo.headimg}}"></image></view><view class="detail"><text class="t1">{{detail.userInfo.nickname}}</text></view></view></view></block><block wx:if="{{(detail.status==3||detail.status==2)&&(detail.freight_type==3||detail.freight_type==4)}}"><view class="orderinfo"><view class="item flex-col"><text class="t1" style="color:#111;">发货信息</text><text class="t2" style="text-align:left;margin-top:10rpx;padding:0 10rpx;" user-select="true" selectable="true">{{detail.freight_content}}</text></view></view></block><block wx:if="{{$root.g0>0}}"><view class="orderinfo"><block wx:for="{{detail.formContent}}" wx:for-item="item" wx:for-index="__i0__" wx:key="id"><view class="item"><text class="t1">{{item.val1}}</text><text class="t2">{{item.value}}</text></view></block></view></block><view class="orderinfo"><view class="item"><text class="t1">编号</text><text class="t2" user-select="true" selectable="true">{{detail.ordernum}}</text></view><view class="item"><text class="t1">提交时间</text><text class="t2">{{detail.createtime}}</text></view><block wx:if="{{detail.status>0&&detail.paytime}}"><view class="item"><text class="t1">支付时间</text><text class="t2">{{detail.paytime}}</text></view></block><block wx:if="{{detail.status>1&&detail.send_time}}"><view class="item"><text class="t1">发货时间</text><text class="t2">{{detail.send_time}}</text></view></block><block wx:if="{{detail.status==3&&detail.collect_time}}"><view class="item"><text class="t1">收货时间</text><text class="t2">{{detail.collect_time}}</text></view></block></view><view class="orderinfo"><view class="item"><text class="t1">状态</text><block wx:if="{{detail.already_replied==0}}"><text class="t2">待回复</text></block><block wx:if="{{detail.already_replied==1}}"><text class="t2">已回复</text></block></view><view class="item" data-url="{{'/pagesExt/electricityForm/recordReplyList?id='+detail.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><text class="t1">回复内容</text><text class="t2">点击查看</text></view></view><block wx:if="{{!show_reply}}"><view class="orderinfo"><view data-event-opts="{{[['tap',[['showReply',['$event']]]]]}}" class="btn2" bindtap="__e">回复</view></view></block><block wx:if="{{show_reply}}"><view class="orderinfo"><textarea class="textarea" name="reply" placeholder="请输入内容" placeholder-style="font-size:28rpx" maxlength="1000" data-event-opts="{{[['input',[['__set_model',['','reply_textarea','$event',[]]],['reply_textarea_input',['$event']]]]]}}" value="{{reply_textarea}}" bindinput="__e"></textarea><view data-event-opts="{{[['tap',[['postReply',['$event']]]]]}}" class="btn2" bindtap="__e">提交</view></view></block><view style="width:100%;height:160rpx;"></view><block wx:if="{{detail.status!=3}}"><view class="bottom notabbarbot"><block wx:if="{{detail.status==3}}"><block><view class="btn2">已完成</view></block></block></view></block></block></block><block wx:if="{{loading}}"><loading vue-id="421096d4-1" bind:__l="__l"></loading></block><dp-tabbar vue-id="421096d4-2" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="421096d4-3" data-ref="popmsg" bind:__l="__l"></popmsg></view>