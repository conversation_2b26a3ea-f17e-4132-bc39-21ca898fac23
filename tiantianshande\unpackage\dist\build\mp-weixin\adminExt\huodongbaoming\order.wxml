<view class="container"><block wx:if="{{isload}}"><block><dd-tab vue-id="45b68bc9-1" itemdata="{{['全部','待付款','待使用','已完成','已取消']}}" itemst="{{['all','0','1','3','4']}}" st="{{st}}" isfixed="{{true}}" data-event-opts="{{[['^changetab',[['changetab']]]]}}" bind:changetab="__e" bind:__l="__l"></dd-tab><view style="width:100%;height:100rpx;"></view><view class="topsearch flex-y-center"><view class="f1 flex-y-center"><image class="img" src="{{pre_url+'/static/img/search_ico.png'}}"></image><input placeholder="输入关键字搜索" placeholder-style="font-size:24rpx;color:#C2C2C2" data-event-opts="{{[['confirm',[['searchConfirm',['$event']]]]]}}" value="{{keyword}}" bindconfirm="__e"/></view></view><view class="order-content"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index"><block><view class="order-box" data-url="{{'orderdetail?id='+item.$orig.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="head"><view>{{"订单号："+item.$orig.ordernum}}</view><view class="flex1"></view><block wx:if="{{item.$orig.status==0}}"><text class="st0">待付款</text></block><block wx:if="{{item.$orig.status==1}}"><text class="st0">待使用</text></block><block wx:if="{{item.$orig.status==3}}"><text class="st4">已完成</text></block><block wx:if="{{item.$orig.status==4}}"><text class="st4">订单已关闭</text></block></view><view class="content"><view data-url="{{'/pagesB/huodongbaoming/orderdetail?id='+item.$orig.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e"><image src="{{item.$orig.propic}}"></image></view><view class="detail"><text class="t1">{{item.$orig.proname}}</text><text class="t1"><block wx:if="{{item.$orig.ggname}}"><text>{{item.$orig.ggname+";"}}</text></block><block wx:if="{{item.$orig.num}}"><text>{{"数量:"+item.$orig.num}}</text></block></text><view class="t3"><block wx:if="{{item.$orig.totalprice>0&&item.$orig.totalscore>0}}"><block><text class="x1 flex1">{{"实付金额：￥"+item.$orig.totalprice+" + "+item.$orig.totalscore+''}}</text></block></block><block wx:if="{{item.$orig.totalprice>0&&item.$orig.totalscore==0}}"><block><text class="x1 flex1">{{"实付金额：￥"+item.$orig.totalprice+''}}</text></block></block><block wx:if="{{item.$orig.totalprice==0&&item.$orig.totalscore>0}}"><block><text class="x1 flex1">{{"实付金额："+item.$orig.totalscore+" "+item.m0}}</text></block></block></view></view></view><block wx:if="{{item.$orig.refund_status>0}}"><view class="bottom"><block wx:if="{{item.$orig.refund_status==1}}"><text style="color:red;">{{'退款中￥'+item.$orig.refund_money}}</text></block><block wx:if="{{item.$orig.refund_status==2}}"><text style="color:red;">{{'已退款￥'+item.$orig.refund_money}}</text></block><block wx:if="{{item.$orig.refund_status==3}}"><text style="color:red;">退款申请已驳回</text></block></view></block><block wx:if="{{item.$orig.status==1}}"><view class="op"><view class="btn2" data-id="{{item.$orig.id}}" data-event-opts="{{[['tap',[['hexiao',['$event']]]]]}}" catchtap="__e">确认使用</view></view></block><view class="bottom flex-y-center"><image style="width:40rpx;height:40rpx;border-radius:50%;margin-right:10rpx;" src="{{item.$orig.member.headimg}}"></image><text style="font-weight:bold;color:#333;margin-right:8rpx;">{{item.$orig.member.nickname}}</text>{{"(ID:"+item.$orig.mid+')'}}</view></view></block></block></view><block wx:if="{{nomore}}"><nomore vue-id="45b68bc9-2" bind:__l="__l"></nomore></block><block wx:if="{{nodata}}"><nodata vue-id="45b68bc9-3" bind:__l="__l"></nodata></block></block></block><block wx:if="{{loading}}"><loading vue-id="45b68bc9-4" bind:__l="__l"></loading></block><popmsg class="vue-ref" vue-id="45b68bc9-5" data-ref="popmsg" bind:__l="__l"></popmsg></view>