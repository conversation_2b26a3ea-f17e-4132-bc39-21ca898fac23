<view class="container"><block wx:if="{{isload}}"><block><view class="header-container"><view class="config-selector"><picker value="{{configIndex}}" range="{{configOptions}}" range-key="name" data-event-opts="{{[['change',[['onConfigChange',['$event']]]]]}}" bindchange="__e"><view class="picker-display"><text>{{selectedConfig.name||'请选择活动配置'}}</text><text class="picker-arrow">▼</text></view></picker></view><block wx:if="{{selectedConfig.id}}"><view class="quick-actions"><view data-event-opts="{{[['tap',[['showAddPositionModal',['$event']]]]]}}" class="action-btn" style="background:#FF5722;" bindtap="__e">?? 增加点位</view><view data-event-opts="{{[['tap',[['refreshTree',['$event']]]]]}}" class="action-btn outline" bindtap="__e">?? 刷新树图</view><view data-event-opts="{{[['tap',[['showBatchAddModal',['$event']]]]]}}" class="action-btn" style="background:#ff9500;" bindtap="__e">?? 批量测试</view><view data-event-opts="{{[['tap',[['showDebugInfo',['$event']]]]]}}" class="action-btn" style="background:#666;" bindtap="__e">?? 调试信息</view></view></block><view class="tree-legend"><view class="legend-item"><view class="legend-dot wealth"></view><text>财富点位</text></view><view class="legend-item"><view class="legend-dot normal"></view><text>普通点位</text></view></view></view><block wx:if="{{selectedConfig.id}}"><view class="tree-container"><scroll-view class="tree-scroll" scroll-y="true" scroll-x="true"><view class="tree-content"><block wx:for="{{treeData}}" wx:for-item="layer" wx:for-index="layerIndex" wx:key="layerIndex"><view class="tree-layer"><view class="layer-title">{{"第"+(layerIndex+1)+"层"}}</view><view class="layer-nodes"><block wx:for="{{layer}}" wx:for-item="node" wx:for-index="nodeIndex" wx:key="id"><view data-event-opts="{{[['tap',[['toggleNode',['$0'],[[['treeData','',layerIndex],['','id',node.id]]]]]],['longpress',[['showNodeActions',['$0'],[[['treeData','',layerIndex],['','id',node.id]]]]]]]}}" class="{{['tree-node',(node.is_wealth_position)?'wealth':'',(node.has_children)?'expandable':'']}}" bindtap="__e" bindlongpress="__e"><view class="node-avatar"><image class="avatar" src="{{node.headimg||'/static/img/default-avatar.png'}}"></image><block wx:if="{{node.is_wealth_position}}"><view class="wealth-badge">财</view></block></view><view class="node-info"><view class="node-name">{{node.nickname}}</view><view class="node-position">{{node.position_text}}</view><view class="node-queue">{{node.queue_text}}</view><view class="node-relation"><text class="referrer-info">{{node.referrer_text}}</text><text class="parent-info">{{node.parent_text}}</text></view><view class="node-time">{{node.createtime_text}}</view></view><block wx:if="{{node.has_children}}"><view class="expand-icon"><text class="{{[(node.expanded)?'expanded':'']}}">▼</text></view></block></view></block></view></view></block><view class="tree-lines"><block wx:for="{{connectionLines}}" wx:for-item="line" wx:for-index="index" wx:key="index"><view class="line" style="{{(line.style)}}"></view></block></view></view></scroll-view></view></block><block wx:else><view class="empty-state"><image class="empty-icon" src="/static/img/empty-tree.png"></image><view class="empty-text">请选择活动配置查看排单树</view></view></block><block wx:if="{{treeStats.total>0}}"><view class="tree-stats"><view class="stats-item"><text class="stats-label">总点位：</text><text class="stats-value" style="color:#FF5722;">{{treeStats.total}}</text></view><view class="stats-item"><text class="stats-label">财富点位：</text><text class="stats-value" style="color:#FF5722;">{{treeStats.wealth}}</text></view><view class="stats-item"><text class="stats-label">层数：</text><text class="stats-value" style="color:#FF5722;">{{treeStats.layers}}</text></view></view></block><block wx:if="{{showAddModal}}"><view data-event-opts="{{[['tap',[['hideAddPositionModal',['$event']]]]]}}" class="modal-overlay" bindtap="__e"><view data-event-opts="{{[['tap',[['',['$event']]]]]}}" class="modal-content" catchtap="__e"><view class="modal-header"><text class="modal-title">添加新点位</text><text data-event-opts="{{[['tap',[['hideAddPositionModal',['$event']]]]]}}" class="modal-close" bindtap="__e">×</text></view><view class="modal-body"><view class="form-item"><text class="form-label">推荐人ID（可选）：</text><input class="form-input" type="number" placeholder="真实推荐关系的用户ID" data-event-opts="{{[['input',[['__set_model',['$0','referrer_id','$event',[]],['addForm']]]]]}}" value="{{addForm.referrer_id}}" bindinput="__e"/></view><view class="form-item"><text class="form-label">父点位ID（可选）：</text><input class="form-input" type="number" placeholder="不填则按公排算法自动分配" data-event-opts="{{[['input',[['__set_model',['$0','parent_id','$event',[]],['addForm']]]]]}}" value="{{addForm.parent_id}}" bindinput="__e"/></view><view class="form-item"><text class="form-label">订单ID（可选）：</text><input class="form-input" type="number" placeholder="关联的订单ID" data-event-opts="{{[['input',[['__set_model',['$0','order_id','$event',[]],['addForm']]]]]}}" value="{{addForm.order_id}}" bindinput="__e"/></view><view class="form-tips"><text>?? 提示：</text><text>• 推荐人ID：记录真实的推荐关系</text><text>• 父点位ID：不填将按公排算法自动分配</text><text>• 系统会自动计算层级和排序位置</text><text>• 财富点位根据活动配置自动判断</text><text>• 树形图会显示"推荐人"和"排在XX下"信息</text></view></view><view class="modal-footer"><view data-event-opts="{{[['tap',[['hideAddPositionModal',['$event']]]]]}}" class="modal-btn cancel" bindtap="__e">取消</view><view data-event-opts="{{[['tap',[['confirmAddPosition',['$event']]]]]}}" class="modal-btn confirm" style="background:#FF5722;" bindtap="__e">确认添加</view></view></view></view></block><block wx:if="{{showNodeModal}}"><view data-event-opts="{{[['tap',[['hideNodeActions',['$event']]]]]}}" class="modal-overlay" bindtap="__e"><view data-event-opts="{{[['tap',[['',['$event']]]]]}}" class="modal-content" catchtap="__e"><view class="modal-header"><text>节点操作</text><text data-event-opts="{{[['tap',[['hideNodeActions',['$event']]]]]}}" class="close-btn" bindtap="__e">×</text></view><block wx:if="{{selectedNode}}"><view class="modal-body"><view class="node-info"><text>{{"用户："+selectedNode.nickname}}</text><text>{{selectedNode.layer_text+selectedNode.position_text}}</text><text>{{selectedNode.wealth_position_text}}</text></view><view class="action-buttons"><view data-event-opts="{{[['tap',[['addChildNode',['$event']]]]]}}" class="action-button primary" bindtap="__e"><text>添加子点位</text></view><view data-event-opts="{{[['tap',[['viewNodeDetail',['$event']]]]]}}" class="action-button secondary" bindtap="__e"><text>查看详情</text></view></view></view></block></view></view></block><block wx:if="{{showBatchModal}}"><view data-event-opts="{{[['tap',[['hideBatchAddModal',['$event']]]]]}}" class="modal-overlay" bindtap="__e"><view data-event-opts="{{[['tap',[['',['$event']]]]]}}" class="modal-content" catchtap="__e"><view class="modal-header"><text>批量添加测试点位</text><text data-event-opts="{{[['tap',[['hideBatchAddModal',['$event']]]]]}}" class="close-btn" bindtap="__e">×</text></view><view class="modal-body"><view class="form-group"><text class="label">添加数量：</text><input class="input" type="number" placeholder="请输入数量（1-20）" data-event-opts="{{[['input',[['__set_model',['$0','count','$event',[]],['batchForm']]]]]}}" value="{{batchForm.count}}" bindinput="__e"/></view><view class="form-tips"><text>⚠️ 此功能仅用于开发测试</text><text>?? 将按公排算法自动分配层级和位置</text><text>?? 财富点位将根据配置自动判断</text></view><view class="modal-buttons"><view data-event-opts="{{[['tap',[['hideBatchAddModal',['$event']]]]]}}" class="modal-button secondary" bindtap="__e"><text>取消</text></view><view data-event-opts="{{[['tap',[['confirmBatchAdd',['$event']]]]]}}" class="modal-button primary" bindtap="__e"><text>确认添加</text></view></view></view></view></view></block></block></block><block wx:if="{{loading}}"><loading vue-id="04c6e4e7-1" loadstyle="left:62.5%" bind:__l="__l"></loading></block><dp-tabbar vue-id="04c6e4e7-2" opt="{{opt}}" data-event-opts="{{[['^getmenuindex',[['getmenuindex']]]]}}" bind:getmenuindex="__e" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="04c6e4e7-3" data-ref="popmsg" bind:__l="__l"></popmsg></view>