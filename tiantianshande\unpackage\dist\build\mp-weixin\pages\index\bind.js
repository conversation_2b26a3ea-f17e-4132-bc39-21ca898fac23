(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages/index/bind"],{"4f38":function(n,t,e){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var a=getApp(),i={data:function(){return{opt:{},loading:!1,isload:!1,menuindex:-1,url:""}},onLoad:function(n){this.opt=a.getopts(n),this.getdata()},methods:{getdata:function(){var n=this;n.loading=!0,a.post("ApiIndex/bind",{id:n.opt.id,token:n.opt.token},(function(t){n.loading=!1,1==t.status?a.alert(t.msg,(function(){a.goto("/admin/index/index")})):a.alert(t.msg)}))}}};t.default=i},5961:function(n,t,e){"use strict";e.r(t);var a=e("cc5b"),i=e("7720");for(var o in i)["default"].indexOf(o)<0&&function(n){e.d(t,n,(function(){return i[n]}))}(o);var u=e("828b"),r=Object(u["a"])(i["default"],a["b"],a["c"],!1,null,null,null,!1,a["a"],void 0);t["default"]=r.exports},7720:function(n,t,e){"use strict";e.r(t);var a=e("4f38"),i=e.n(a);for(var o in a)["default"].indexOf(o)<0&&function(n){e.d(t,n,(function(){return a[n]}))}(o);t["default"]=i.a},cc5b:function(n,t,e){"use strict";e.d(t,"b",(function(){return i})),e.d(t,"c",(function(){return o})),e.d(t,"a",(function(){return a}));var a={loading:function(){return e.e("components/loading/loading").then(e.bind(null,"ceaa"))}},i=function(){var n=this.$createElement;this._self._c},o=[]},ebaf:function(n,t,e){"use strict";(function(n,t){var a=e("47a9");e("06e9");a(e("3240"));var i=a(e("5961"));n.__webpack_require_UNI_MP_PLUGIN__=e,t(i.default)}).call(this,e("3223")["default"],e("df3c")["createPage"])}},[["ebaf","common/runtime","common/vendor"]]]);