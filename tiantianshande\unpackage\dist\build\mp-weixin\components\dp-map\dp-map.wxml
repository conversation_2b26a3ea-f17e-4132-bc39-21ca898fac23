<view class="dp-map" style="{{'background-color:'+(params.bgcolor)+';'+('margin:'+(params.margin_y*2.2+'rpx '+params.margin_x*2.2+'rpx 0')+';')+('padding:'+(params.padding_y*2.2+'rpx '+params.padding_x*2.2+'rpx')+';')}}"><map style="{{'width:'+('100%')+';'+('height:'+(params.height*2.2+'rpx')+';')}}" longitude="{{params.longitude}}" latitude="{{params.latitude}}" markers="{{[{label:{content:params.address,fontSize:'14px',borderRadius:'4px',color:'#000',anchorX:'-10px',anchorY:'-62px',padding:'3px',textAlign:'center'},anchor:{x:0.17,y:1},id:0,latitude:params.latitude,longitude:params.longitude,iconPath:'/static/img/marker.png',width:'73',height:'33'}]}}" data-longitude="{{params.longitude}}" data-latitude="{{params.latitude}}" data-address="{{params.address}}" data-event-opts="{{[['tap',[['openLocation',['$event']]]]]}}" bindtap="__e"></map></view>