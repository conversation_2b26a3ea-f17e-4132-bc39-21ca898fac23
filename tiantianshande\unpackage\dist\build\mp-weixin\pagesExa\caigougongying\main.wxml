<view class="page flex-col justify-end"><view class="block_5 flex-col"><text class="text_11">询价市场</text><view class="block_7 flex-row justify-between"><view class="text-group_6 flex-col justify-between"><text class="text_12">{{total_gongying}}</text><text class="text_13">累计供应单总数</text></view><view class="text-group_7 flex-col justify-between"><text class="text_14">{{total_qiugou}}</text><text class="text_15">累计求购单总数</text></view></view><view class="group_5 flex-row"><view class="text-group_8 flex-col justify-between" data-url="fatielog?type=2" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><text class="text_16">{{my_gongying}}</text><text class="text_17">我的供应单</text></view><view class="text-group_9 flex-col justify-between" data-url="fatielog?type=1" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><text class="text_18">{{my_qiugou}}</text><text class="text_19">我的求购数</text></view><view class="text-group_10 flex-col justify-between"><text class="text_20">{{my_baojia}}</text><text class="text_21">我的报价数</text></view></view><view class="list-container"><block wx:for="{{loopData0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="list-item flex-col"><view class="item-header flex-row justify-between"><text class="title"><rich-text nodes="{{item.lanhutext0}}"></rich-text></text><view class="more flex-row" data-url="{{'ltlist?type='+(index===0?'1':'2')}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><text class="more-text"><rich-text nodes="{{item.lanhutext1}}"></rich-text></text><image class="more-icon" referrerpolicy="no-referrer" src="{{item.lanhuimage0}}"></image></view></view><view class="list-content"><block wx:for="{{item.list}}" wx:for-item="subItem" wx:for-index="subIndex" wx:key="subIndex"><view class="list-row" data-url="{{'detail?id='+subItem.id+'&type='+(index===0?'1':'2')}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="row-left"><text class="row-title">{{subItem.title}}</text><text class="row-type">{{subItem.type}}</text></view><view class="row-right"><text class="row-status">{{subItem.status}}</text><text class="row-time">{{subItem.time}}</text></view></view></block></view><view class="item-content flex-row justify-between"><view class="tag" style="{{'background:'+(item.lanhuBg4)+';'}}"><text class="tag-text" style="{{'color:'+(item.lanhufontColor2)+';'}}"><rich-text nodes="{{item.lanhutext2}}"></rich-text></text></view><view class="tag" style="{{'background:'+(item.lanhuBg5)+';'}}"><text class="tag-text" style="{{'color:'+(item.lanhufontColor3)+';'}}"><rich-text nodes="{{item.lanhutext3}}"></rich-text></text></view></view></view></block></view></view><view class="block_3 flex-row justify-between"><view class="text-wrapper_4 flex-col" data-url="fatie?type=qiugou" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><text class="text_7">我要求购</text></view><view class="text-wrapper_5 flex-col" data-url="fatie?type=gongying" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><text class="text_8">我要供应</text></view></view><block wx:if="{{loading}}"><loading vue-id="66e33a61-1" bind:__l="__l"></loading></block><dp-tabbar vue-id="66e33a61-2" opt="{{opt}}" data-event-opts="{{[['^getmenuindex',[['getmenuindex']]]]}}" bind:getmenuindex="__e" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="66e33a61-3" data-ref="popmsg" bind:__l="__l"></popmsg></view>