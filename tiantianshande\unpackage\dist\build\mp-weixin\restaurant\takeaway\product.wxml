<view><block wx:if="{{isload}}"><block><block wx:if="{{isplay==0}}"><view class="swiper-container"><swiper class="swiper" indicator-dots="{{false}}" autoplay="{{true}}" interval="{{500000}}" data-event-opts="{{[['change',[['swiperChange',['$event']]]]]}}" bindchange="__e"><block wx:for="{{product.pics}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><swiper-item class="swiper-item"><view class="swiper-item-view"><image class="img" src="{{item}}" mode="widthFix"></image></view></swiper-item></block></block></swiper><view class="imageCount">{{current+1+"/"+$root.g0}}</view><block wx:if="{{product.video}}"><view data-event-opts="{{[['tap',[['payvideo',['$event']]]]]}}" class="provideo" bindtap="__e"><image src="/static/img/video.png"></image><view class="txt">{{product.video_duration}}</view></view></block></view></block><block wx:if="{{isplay==1}}"><view class="videobox"><video class="video" autoplay="true" id="video" src="{{product.video}}"></video><view data-event-opts="{{[['tap',[['parsevideo',['$event']]]]]}}" class="parsevideo" bindtap="__e">退出播放</view></view></block><view class="header"><view class="price_share"><view class="price"><view class="f1"><text style="font-size:36rpx;">￥</text>{{product.sell_price}}</view><block wx:if="{{product.market_price*1>product.sell_price*1}}"><view class="f2">{{"￥"+product.market_price}}</view></block><block wx:if="{{product.pack_fee>0}}"><view class="f3">{{"打包费￥"+product.pack_fee+"/份"}}</view></block></view><view data-event-opts="{{[['tap',[['shareClick',['$event']]]]]}}" class="share" bindtap="__e"><image class="img" src="/static/img/share.png"></image><text class="txt">分享</text></view></view><view class="title">{{product.name}}</view><view class="sales_stock"><view class="f1">{{"销量："+product.sales+''}}</view><block wx:if="{{product.limit_start>0}}"><view class="f1">{{"起售："+product.limit_start+''}}</view></block><block wx:if="{{product.limit_per>0}}"><view class="f1">{{"限购："+product.limit_per+''}}</view></block><view class="f2">{{"库存："+product.stock}}</view></view><block wx:if="{{shopset.showcommission==1&&product.commission>0}}"><view class="commission" style="{{'background:'+('rgba('+$root.m0+',0.1)')+';'+('color:'+($root.m1)+';')}}">{{"分享好友购买可得"+$root.m2+"："}}<text style="font-weight:bold;padding:0 2px;">{{product.commission}}</text>{{product.commission_desc}}</view></block></view><view class="choose" data-btntype="1" data-event-opts="{{[['tap',[['buydialogChange',['$event']]]]]}}" bindtap="__e"><view class="f1 flex1">请选择商品规格及数量</view><image class="f2" src="/static/img/arrowright.png"></image></view><block wx:if="{{product.givescore>0}}"><view class="cuxiaodiv"><view class="cuxiaopoint"><view class="f0">{{"送"+$root.m3}}</view><view class="f1" style="font-size:26rpx;">{{"购买可得"+$root.m4+product.givescore+"个"}}</view></view></view></block><block wx:if="{{$root.g1}}"><view class="cuxiaodiv"><block wx:if="{{$root.g2>0}}"><view class="cuxiaopoint"><view class="f0">促销</view><view data-event-opts="{{[['tap',[['showcuxiaodetail',['$event']]]]]}}" class="f1" bindtap="__e"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="t" style="{{'background:'+('rgba('+item.m5+',0.1)')+';'+('color:'+(item.m6)+';')}}"><text class="t0">{{item.$orig.tip}}</text><text class="t1">{{item.$orig.name}}</text></view></block></view><view data-event-opts="{{[['tap',[['showcuxiaodetail',['$event']]]]]}}" class="f2" bindtap="__e"><image class="img" src="/static/img/arrowright.png"></image></view></view></block><block wx:if="{{$root.g3>0}}"><view class="cuxiaopoint"><view class="f0">优惠</view><view data-event-opts="{{[['tap',[['showcuxiaodetail',['$event']]]]]}}" class="f1" bindtap="__e"><block wx:for="{{$root.l1}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="t" style="{{'background:'+('rgba('+item.m7+',0.1)')+';'+('color:'+(item.m8)+';')}}"><text class="t0" style="padding:0 6px;">券</text><text class="t1">{{item.$orig.name}}</text></view></block></view><view data-event-opts="{{[['tap',[['showcuxiaodetail',['$event']]]]]}}" class="f2" bindtap="__e"><image class="img" src="/static/img/arrowright.png"></image></view></view></block></view></block><block wx:if="{{showcuxiaodialog}}"><view class="popup__container"><view data-event-opts="{{[['tap',[['hidecuxiaodetail',['$event']]]]]}}" class="popup__overlay" catchtap="__e"></view><view class="popup__modal"><view class="popup__title"><text class="popup__title-text">优惠促销</text><image class="popup__close" style="width:36rpx;height:36rpx;" src="/static/img/close.png" data-event-opts="{{[['tap',[['hidecuxiaodetail',['$event']]]]]}}" catchtap="__e"></image></view><view class="popup__content"><block wx:for="{{cuxiaolist}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="service-item"><view class="suffix"><view class="type-name"><text style="border-radius:4px;border:1px solid #f05423;color:#ff550f;font-size:20rpx;padding:2px 5px;">{{item.tip}}</text><text style="color:#333;margin-left:20rpx;">{{item.name}}</text></view></view></view></block><couponlist vue-id="64f0b746-1" couponlist="{{couponlist}}" data-event-opts="{{[['^getcoupon',[['getcoupon']]]]}}" bind:getcoupon="__e" bind:__l="__l"></couponlist></view></view></view></block><block wx:if="{{shopset.takeaway_comment==1&&commentcount>0}}"><view class="commentbox"><view class="title"><view class="f1">{{"评价("+commentcount+")"}}</view><view class="f2" data-url="{{'commentlist?proid='+product.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">好评度<text style="{{'color:'+($root.m9)+';'}}">{{product.comment_haopercent+"%"}}</text><image style="width:32rpx;height:32rpx;" src="/static/img/arrowright.png"></image></view></view><view class="comment"><block wx:if="{{$root.g4>0}}"><view class="item"><view class="f1"><image class="t1" src="{{commentlist[0].headimg}}"></image><view class="t2">{{commentlist[0].nickname}}</view><view class="flex1"></view><view class="t3"><block wx:for="{{[0,1,2,3,4]}}" wx:for-item="item2" wx:for-index="index2" wx:key="index2"><image class="img" src="{{'/static/img/star'+(commentlist[0].score>item2?'2':'')+'.png'}}"></image></block></view></view><view class="f2"><text class="t1">{{commentlist[0].content}}</text><view class="t2"><block wx:if="{{commentlist[0].content_pic!=''}}"><block><block wx:for="{{commentlist[0].content_pic}}" wx:for-item="itemp" wx:for-index="index" wx:key="index"><block><view data-url="{{itemp}}" data-urls="{{commentlist[0].content_pic}}" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"><image src="{{itemp}}" mode="widthFix"></image></view></block></block></block></block></view></view><view class="f3" data-url="{{'commentlist?proid='+product.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">查看全部评价</view></view></block><block wx:else><view class="nocomment">暂无评价~</view></block></view></view></block><view class="detail_title"><view class="t1"></view><view class="t2"></view><view class="t0">商品描述</view><view class="t2"></view><view class="t1"></view></view><view class="detail"><dp vue-id="64f0b746-2" pagecontent="{{pagecontent}}" bind:__l="__l"></dp></view><view style="width:100%;height:140rpx;"></view><block wx:if="{{product.status==1}}"><view class="{{['bottombar','flex-row',menuindex>-1?'tabbarbot':'']}}"><view class="f1"><block wx:if="{{kfurl!='contact::'}}"><view class="item" data-url="{{kfurl}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image class="img" src="/static/img/kefu.png"></image><view class="t1">客服</view></view></block><block wx:else><button class="item" open-type="contact"><image class="img" src="/static/img/kefu.png"></image><view class="t1">客服</view></button></block><view class="item flex1" data-url="{{'index?bid='+product.bid}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image class="img" src="/static/img/gwc.png"></image><view class="t1">购物车</view><block wx:if="{{cartnum>0}}"><view class="cartnum" style="{{'background:'+('rgba('+$root.m10+',0.8)')+';'}}">{{cartnum}}</view></block></view><view data-event-opts="{{[['tap',[['addfavorite',['$event']]]]]}}" class="item" bindtap="__e"><image class="img" src="/static/img/shoucang.png"></image><view class="t1">{{isfavorite?'已收藏':'收藏'}}</view></view></view><block wx:if="{{product.stock>0&&product.stock_daily>product.sales_daily}}"><view class="op"><block wx:if="{{product.freighttype!=3&&product.freighttype!=4}}"><view class="tocart flex-x-center flex-y-center" style="{{'background:'+($root.m11)+';'}}" data-btntype="1" data-event-opts="{{[['tap',[['buydialogChange',['$event']]]]]}}" bindtap="__e">加入购物车</view></block></view></block><block wx:else><view class="op"><view class="tobuy flex-x-center flex-y-center" style="background-color:#ccc;" data-btntype="2">已售罄</view></view></block></view></block><block wx:if="{{buydialogShow}}"><buydialog vue-id="64f0b746-3" proid="{{product.id}}" btntype="{{btntype}}" menuindex="{{menuindex}}" controller="ApiRestaurantTakeaway" data-event-opts="{{[['^buydialogChange',[['buydialogChange']]],['^addcart',[['addcart']]]]}}" bind:buydialogChange="__e" bind:addcart="__e" bind:__l="__l"></buydialog></block><scrolltop vue-id="64f0b746-4" isshow="{{scrolltopshow}}" bind:__l="__l"></scrolltop><block wx:if="{{sharetypevisible}}"><view class="popup__container"><view data-event-opts="{{[['tap',[['handleClickMask',['$event']]]]]}}" class="popup__overlay" catchtap="__e"></view><view class="popup__modal" style="height:320rpx;min-height:320rpx;"><view class="popup__content"><view class="sharetypecontent"><block wx:if="{{$root.m12=='app'}}"><view data-event-opts="{{[['tap',[['shareapp',['$event']]]]]}}" class="f1" bindtap="__e"><image class="img" src="/static/img/weixin.png"></image><text class="t1">分享给好友</text></view></block><block wx:else><block wx:if="{{$root.m13=='mp'}}"><view data-event-opts="{{[['tap',[['sharemp',['$event']]]]]}}" class="f1" bindtap="__e"><image class="img" src="/static/img/weixin.png"></image><text class="t1">分享给好友</text></view></block><block wx:else><block wx:if="{{$root.m14=='h5'}}"><view data-event-opts="{{[['tap',[['sharemp',['$event']]]]]}}" class="f1" bindtap="__e"><image class="img" src="/static/img/weixin.png"></image><text class="t1">分享给好友</text></view></block><block wx:else><button class="f1" open-type="share"><image class="img" src="/static/img/weixin.png"></image><text class="t1">分享给好友</text></button></block></block></block><view data-event-opts="{{[['tap',[['showPoster',['$event']]]]]}}" class="f2" bindtap="__e"><image class="img" src="/static/img/sharepic.png"></image><text class="t1">生成分享图片</text></view></view></view></view></view></block><block wx:if="{{showposter}}"><view class="posterDialog"><view class="main"><view data-event-opts="{{[['tap',[['posterDialogClose',['$event']]]]]}}" class="close" bindtap="__e"><image class="img" src="/static/img/close.png"></image></view><view class="content"><image class="img" src="{{posterpic}}" mode="widthFix" data-url="{{posterpic}}" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image></view></view></view></block></block></block><block wx:if="{{loading}}"><loading vue-id="64f0b746-5" bind:__l="__l"></loading></block><dp-tabbar vue-id="64f0b746-6" opt="{{opt}}" data-event-opts="{{[['^getmenuindex',[['getmenuindex']]]]}}" bind:getmenuindex="__e" bind:__l="__l"></dp-tabbar></view>