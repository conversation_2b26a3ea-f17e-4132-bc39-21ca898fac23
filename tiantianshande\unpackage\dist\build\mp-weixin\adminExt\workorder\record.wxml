<view class="container"><block wx:if="{{isload}}"><block><dd-tab vue-id="914d31f8-1" itemdata="{{['全部','待处理','处理中 ','已完成','待支付']}}" itemst="{{['all','0','1','2','10']}}" st="{{st}}" isfixed="{{true}}" data-event-opts="{{[['^changetab',[['changetab']]]]}}" bind:changetab="__e" bind:__l="__l"></dd-tab><view style="width:100%;height:90rpx;"></view><view class="topsearch flex-y-center"><view class="f1 flex-y-center"><image class="img" src="{{pre_url+'/static/img/search_ico.png'}}"></image><input placeholder="输入关键字搜索" placeholder-style="font-size:24rpx;color:#C2C2C2" data-event-opts="{{[['input',[['searchInput',['$event']]]],['confirm',[['searchConfirm',['$event']]]]]}}" value="{{keyword}}" bindinput="__e" bindconfirm="__e"/></view><view style="margin:0 20rpx;"><picker style="height:80rpx;line-height:80rpx;border-bottom:1px solid #EEEEEE;" value="{{cindex}}" range="{{cateArr}}" range-key="name" data-event-opts="{{[['change',[['pickerChange',['$event']]]]]}}" bindchange="__e"><view class="picker">{{cindex==-1?'请选择工单':cateArr[cindex].name}}</view></picker></view></view><view class="searchbox"><view class="date"><view class="begindate"><picker mode="date" value="{{begindate}}" start="{{startDate}}" end="{{endDate}}" data-event-opts="{{[['change',[['bindDateChange',['$event']]]]]}}" bindchange="__e"><view class="uni-input">{{begindate?begindate:'选择开始日期'}}</view></picker></view>-<view class="enddate"><picker mode="date" value="{{enddate}}" start="{{startDate}}" end="{{endDate}}" data-event-opts="{{[['change',[['bindDateChange2',['$event']]]]]}}" bindchange="__e"><view class="uni-input">{{''+(enddate?enddate:'选择结束日期')}}</view></picker></view></view><view data-event-opts="{{[['tap',[['search',['$event']]]]]}}" class="searchbtn" style="{{'background:'+($root.m0)+';'}}" bindtap="__e">搜索</view></view><view class="content" id="datalist"><block wx:for="{{datalist}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="item"><view class="f1" data-url="{{'formdetail?id='+item.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e"><view class="itembox"><view style="justify-content:space-between;"><view class="t1">{{"用户昵称："+item.nickname}}</view><view class="t1">{{"工单类型："+item.cname}}</view><text class="t1">{{"工单名称："+item.title}}</text></view><view class="f2"><block wx:if="{{item.status==0&&(!item.payorderid||item.paystatus==1)}}"><text class="t1" style="color:#88e;" data-url="{{'jindu?id='+item.id+'&cid='+item.cid}}" data-status="{{item.status}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e">{{item.clname}}</text></block><block wx:if="{{item.status==0&&item.payorderid&&item.paystatus==0}}"><text class="t1" style="color:red;">待支付</text></block><block wx:if="{{item.status==1}}"><text class="t1" style="color:green;" data-url="{{'jindu?id='+item.id+'&cid='+item.cid}}" data-status="{{item.status}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e">{{item.clname}}</text></block><block wx:if="{{item.status==2}}"><text class="t1" style="color:green;">已完成</text></block><block wx:if="{{item.status==-1}}"><text class="t1" style="color:red;">已驳回</text></block></view></view><view class="flex" style="justify-content:space-between;margin-top:20rpx;"><view data-url="{{'formdetail?id='+item.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e"><text class="t2">{{"提交时间："+item.createtime}}</text><block wx:if="{{item.paynum}}"><text class="t2" user-select="true" selectable="true">{{item.paynum}}</text></block></view><view class="jindu" data-url="{{'jindu?id='+item.id+'&cid='+item.cid}}" data-status="{{item.status}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e">查看进度</view></view></view></view></block></view><block wx:if="{{ishowjindu}}"><view class="modal"><view class="modal_jindu"><view data-event-opts="{{[['tap',[['closejd',['$event']]]]]}}" class="close" bindtap="__e"><image src="{{pre_url+'/static/img/close.png'}}"></image></view><block wx:if="{{$root.g0>0}}"><block><block wx:for="{{$root.l1}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="item" style="display:flex;"><view class="f1"><image src="{{'/static/img/jindu'+(index==0?'2':'1')+'.png'}}"></image></view><view class="f2"><text class="t2">{{'时间：'+item.$orig.time}}</text><text class="t1">{{item.$orig.desc+"("+item.$orig.remark+')'}}</text><block wx:for="{{item.$orig.content_pic}}" wx:for-item="pic" wx:for-index="ind"><block wx:if="{{item.g1>0}}"><view><view class="layui-imgbox-img"><image src="{{pic}}" data-url="{{pic}}" mode="widthFix" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image></view></view></block></block><block wx:for="{{item.l0}}" wx:for-item="hf" wx:for-index="hfindex" wx:key="hfindex"><view><block wx:if="{{hf.$orig.hfremark}}"><view class="t3">{{"用户回复："+hf.$orig.hfremark+''}}</view></block><block wx:if="{{hf.$orig.hftime}}"><view class="t4">{{"回复时间："+hf.$orig.hftime+''}}</view></block><block wx:for="{{hf.$orig.hfcontent_pic}}" wx:for-item="pic2" wx:for-index="ind2"><block wx:if="{{hf.g2>0}}"><view><view class="layui-imgbox-img"><image src="{{pic2}}" data-url="{{pic2}}" mode="widthFix" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image></view></view></block></block></view></block></view></view></block></block></block><block wx:else><block wx:if="{{statuss==-1}}"><block><view style="font-size:14px;color:#f05555;padding:10px;">工单已驳回</view></block></block><block wx:else><block><view style="font-size:14px;color:#f05555;padding:10px;">等待处理</view></block></block></block></view></view></block></block></block><block wx:if="{{nodata}}"><nodata vue-id="914d31f8-2" bind:__l="__l"></nodata></block><block wx:if="{{nomore}}"><nomore vue-id="914d31f8-3" bind:__l="__l"></nomore></block><block wx:if="{{loading}}"><loading vue-id="914d31f8-4" bind:__l="__l"></loading></block><dp-tabbar vue-id="914d31f8-5" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="914d31f8-6" data-ref="popmsg" bind:__l="__l"></popmsg></view>