(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["components/nodata/nodata"],{"101c":function(t,n,e){"use strict";e.r(n);var u=e("b80a"),a=e("1813");for(var c in a)["default"].indexOf(c)<0&&function(t){e.d(n,t,(function(){return a[t]}))}(c);e("9e3d");var i=e("828b"),o=Object(i["a"])(a["default"],u["b"],u["c"],!1,null,null,null,!1,u["a"],void 0);n["default"]=o.exports},1171:function(t,n,e){},1813:function(t,n,e){"use strict";e.r(n);var u=e("602d"),a=e.n(u);for(var c in u)["default"].indexOf(c)<0&&function(t){e.d(n,t,(function(){return u[t]}))}(c);n["default"]=a.a},"602d":function(t,n,e){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;n.default={props:{pic:{default:"/static/img/empty.png"},text:{default:"没有相关信息"},type:{default:"normal"}}}},"9e3d":function(t,n,e){"use strict";var u=e("1171"),a=e.n(u);a.a},b80a:function(t,n,e){"use strict";e.d(n,"b",(function(){return u})),e.d(n,"c",(function(){return a})),e.d(n,"a",(function(){}));var u=function(){var t=this.$createElement;this._self._c},a=[]}}]);
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'components/nodata/nodata-create-component',
    {
        'components/nodata/nodata-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('df3c')['createComponent'](__webpack_require__("101c"))
        })
    },
    [['components/nodata/nodata-create-component']]
]);
