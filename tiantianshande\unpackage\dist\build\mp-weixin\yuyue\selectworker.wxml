<view class="container"><block wx:if="{{orderInfo&&orderInfo.product}}"><view class="order-info"><view class="product-info"><image src="{{orderInfo.product.pic}}" mode="aspectFill"></image><view class="info"><text class="name">{{orderInfo.product.name}}</text><text class="price" style="{{('color:'+$root.m0)}}">{{"￥"+orderInfo.product.sell_price}}</text></view></view><view class="order-detail"><text>{{"订单号："+(orderInfo.order_no||'')}}</text><text>{{"预约时间："+(orderInfo.yy_time||'')}}</text></view></view></block><view class="worker-list-section"><view class="section-title">选择服务人员</view><block wx:if="{{$root.g0>0}}"><view class="worker-list"><block wx:for="{{$root.l0}}" wx:for-item="worker" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['selectWorker',['$0'],[[['workerList','',index]]]]]]]}}" class="{{['worker-item',(selectedWorkerId===worker.$orig.id)?'active':'']}}" style="{{(selectedWorkerId===worker.$orig.id?'background: rgba('+worker.m1+',0.1)':'')}}" bindtap="__e"><image class="avatar" src="{{worker.$orig.avatar||'/static/img/default-avatar.png'}}" mode="aspectFill"></image><view class="worker-info"><text class="name">{{worker.$orig.name}}</text><view class="stats"><text>{{"评分："+(worker.$orig.rating||'5.0')+"分"}}</text><text>{{"已完成："+(worker.$orig.order_count||'0')+"单"}}</text></view></view><block wx:if="{{selectedWorkerId===worker.$orig.id}}"><view class="select-icon" style="{{('background:'+worker.m2)}}"><text class="iconfont iconcheck"></text></view></block></view></block></view></block><block wx:else><view class="no-worker"><text>当前预约时间暂无可用服务人员</text><text>请返回选择其他时间段</text></view></block></view><view class="footer"><button class="{{['submit-btn',(selectedWorkerId)?'shadow-box':'']}}" style="{{(selectedWorkerId?'background:linear-gradient(90deg,'+$root.m3+' 0%,rgba('+$root.m4+',0.8) 100%)':'background:#ccc')}}" disabled="{{!selectedWorkerId}}" data-event-opts="{{[['tap',[['submitAssign',['$event']]]]]}}" bindtap="__e">确认选择</button></view></view>