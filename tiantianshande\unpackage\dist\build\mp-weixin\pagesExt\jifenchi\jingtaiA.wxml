<view class="container"><block wx:if="{{isload}}"><block><view class="content"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="item"><view class="f2"><view class="t1"><text class="x1">{{item.$orig.remark}}</text><block wx:if="{{item.$orig.zscore>0}}"><text class="x1">{{"余额加入:"+item.$orig.zscore+"元"}}</text></block><text class="x2">{{"产生时间："+item.m0}}</text></view><view class="t2"><text class="x1">{{item.$orig.score}}</text></view></view></view></block></view></block></block><block wx:if="{{nodata}}"><nodata vue-id="377e1df2-1" bind:__l="__l"></nodata></block><block wx:if="{{nomore}}"><nomore vue-id="377e1df2-2" bind:__l="__l"></nomore></block><block wx:if="{{loading}}"><loading vue-id="377e1df2-3" bind:__l="__l"></loading></block><dp-tabbar vue-id="377e1df2-4" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="377e1df2-5" data-ref="popmsg" bind:__l="__l"></popmsg></view>