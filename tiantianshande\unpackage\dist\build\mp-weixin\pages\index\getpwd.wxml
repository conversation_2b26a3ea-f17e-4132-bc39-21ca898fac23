<view class="container"><block wx:if="{{isload}}"><block><form data-event-opts="{{[['submit',[['formSubmit',['$event']]]],['reset',[['formReset',['$event']]]]]}}" bindsubmit="__e" bindreset="__e"><view class="title">重置密码</view><view class="regform"><view class="form-item"><image class="img" src="/static/img/reg-tel.png"></image><input class="input" type="text" placeholder="请输入手机号" placeholder-style="font-size:30rpx;color:#B2B5BE" name="tel" value="" data-event-opts="{{[['input',[['telinput',['$event']]]]]}}" bindinput="__e"/></view><view class="form-item"><image class="img" src="/static/img/reg-code.png"></image><input class="input" type="text" placeholder="请输入验证码" placeholder-style="font-size:30rpx;color:#B2B5BE" name="smscode" value=""/><view data-event-opts="{{[['tap',[['smscode',['$event']]]]]}}" class="code" style="{{'color:'+($root.m0)+';'}}" bindtap="__e">{{smsdjs||'获取验证码'}}</view></view><view class="form-item"><image class="img" src="/static/img/reg-pwd.png"></image><input class="input" type="text" placeholder="6-16为字母数字组合密码" placeholder-style="font-size:30rpx;color:#B2B5BE" name="pwd" value="" password="{{true}}"/></view><view class="form-item"><image class="img" src="/static/img/reg-pwd.png"></image><input class="input" type="text" placeholder="再次输入登录密码" placeholder-style="font-size:30rpx;color:#B2B5BE" name="repwd" value="" password="{{true}}"/></view><button class="form-btn" style="{{'background:'+('linear-gradient(90deg,'+$root.m1+' 0%,rgba('+$root.m2+',0.8) 100%)')+';'}}" form-type="submit">确定</button></view></form></block></block><block wx:if="{{loading}}"><loading vue-id="f170ecae-1" bind:__l="__l"></loading></block><dp-tabbar vue-id="f170ecae-2" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="f170ecae-3" data-ref="popmsg" bind:__l="__l"></popmsg></view>