<view><block wx:if="{{isload}}"><block><view class="screen-view"><view class="screen-view-left"><view data-event-opts="{{[['tap',[['alertClick',[1]]]]]}}" class="screen-options" style="{{(order!='suiji'?'background:rgba('+$root.m0+',0.9);color:#fff':'')}}" bindtap="__e">{{ordername}}<image src="{{pre_url+'/static/img/arrowdown.png'}}"></image></view><view data-event-opts="{{[['tap',[['alertClick',[2]]]]]}}" class="screen-options" style="{{(wname1!='全城'?'background:rgba('+$root.m1+',0.9);color:#fff':'')}}" bindtap="__e">{{wname1}}<image src="{{pre_url+'/static/img/arrowdown.png'}}"></image></view><view data-event-opts="{{[['tap',[['alertClick',[3]]]]]}}" class="screen-options" style="{{($root.g0>0?'background:rgba('+$root.m2+',0.9);color:#fff':'')}}" bindtap="__e">{{starname+($root.g1?$root.g2:'')}}<image src="{{pre_url+'/static/img/arrowdown.png'}}"></image></view><view data-event-opts="{{[['tap',[['alertClick',[5]]]]]}}" class="screen-options" style="{{(emptyroom!='状态'?'background:rgba('+$root.m3+',0.9);color:#fff':'')}}" bindtap="__e">{{emptyroom}}<image src="{{pre_url+'/static/img/arrowdown.png'}}"></image></view></view><view data-event-opts="{{[['tap',[['alertClick',[4]]]]]}}" class="right-screen" style="{{($root.g3>0?'color:'+$root.m4:'')}}" bindtap="__e">{{'筛选'+($root.g4?$root.g5:'')}}<image src="{{pre_url+'/static/img/hotel/screenicon.png'}}"></image></view></view><block wx:if="{{alertState}}"><view data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" class="alert" bindtap="__e"></view></block><block wx:if="{{alertState=='1'}}"><view class="alert_module"><radio-group><label class="sort flex-y-center flex-bt" data-value="suiji" data-name="智能排序" data-event-opts="{{[['tap',[['sortChange',['$event']]]]]}}" bindtap="__e"><view>智能排序</view><radio class="sort_icon" color="#fac428" checked="{{order=='suiji'?true:false}}"></radio></label><label class="sort flex-y-center flex-bt" data-value="juli" data-name="距离优先" data-event-opts="{{[['tap',[['sortChange',['$event']]]]]}}" bindtap="__e"><view>距离优先</view><radio class="sort_icon" color="#fac428" checked="{{order=='juli'?true:false}}"></radio></label><label class="sort flex-y-center flex-bt" data-value="comment_haopercent" data-name="评价最高" data-event-opts="{{[['tap',[['sortChange',['$event']]]]]}}" bindtap="__e"><view>评价最高</view><radio class="sort_icon" color="#fac428" checked="{{order=='comment_haopercent'?true:false}}"></radio></label><label class="sort flex-y-center flex-bt" data-value="totalnum" data-name="销量优先" data-event-opts="{{[['tap',[['sortChange',['$event']]]]]}}" bindtap="__e"><view>销量优先</view><radio class="sort_icon" color="#fac428" checked="{{order=='totalnum'?true:false}}"></radio></label><label class="sort flex-y-center flex-bt" data-value="priceasc" data-name="低价优先" data-event-opts="{{[['tap',[['sortChange',['$event']]]]]}}" bindtap="__e"><view>低价优先</view><radio class="sort_icon" color="#fac428" checked="{{order=='priceasc'?true:false}}"></radio></label><label class="sort flex-y-center flex-bt" data-value="pricedesc" data-name="高价优先" data-event-opts="{{[['tap',[['sortChange',['$event']]]]]}}" bindtap="__e"><view>高价优先</view><radio class="sort_icon" color="#fac428" checked="{{order=='pricedesc'?true:false}}"></radio></label></radio-group></view></block><block wx:if="{{alertState=='2'}}"><view class="alert_module"><radio-group><label class="sort flex-y-center flex-bt" data-value="all" data-name="全城" data-event-opts="{{[['tap',[['whereChange',['$event']]]]]}}" bindtap="__e"><view>全城</view><radio class="sort_icon" color="#fac428" checked="{{wherevalue=='all'?true:false}}"></radio></label><label class="sort flex-y-center flex-bt" data-value="1" data-name="1km" data-event-opts="{{[['tap',[['whereChange',['$event']]]]]}}" bindtap="__e"><view>1km</view><radio class="sort_icon" color="#fac428" checked="{{wherevalue=='1'?true:false}}"></radio></label><label class="sort flex-y-center flex-bt" data-value="3" data-name="3km" data-event-opts="{{[['tap',[['whereChange',['$event']]]]]}}" bindtap="__e"><view>3km</view><radio class="sort_icon" color="#fac428" checked="{{wherevalue=='3'?true:false}}"></radio></label><label class="sort flex-y-center flex-bt" data-value="5" data-name="5km" data-event-opts="{{[['tap',[['whereChange',['$event']]]]]}}" bindtap="__e"><view>5km</view><radio class="sort_icon" color="#fac428" checked="{{wherevalue=='5'?true:false}}"></radio></label><label class="sort flex-y-center flex-bt" data-value="10" data-name="10km" data-event-opts="{{[['tap',[['whereChange',['$event']]]]]}}" bindtap="__e"><view>10km</view><radio class="sort_icon" color="#fac428" checked="{{wherevalue=='10'?true:false}}"></radio></label></radio-group></view></block><block wx:if="{{alertState=='3'}}"><view class="alert_module"><view class="alert_title">星级</view><view class="flex flex-wp"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index"><block><view class="{{['alert_tag ']}}" style="{{(item.m5?'background:'+item.m6+';border:0;color:#fff':'')}}" data-value="{{index}}" data-event-opts="{{[['tap',[['startypeChange',['$event']]]]]}}" bindtap="__e">{{item.$orig}}</view></block></block></view><view class="alert_opt flex-y-center flex-bt"><view data-event-opts="{{[['tap',[['cancel',['$event']]]]]}}" class="alert_btn flex-xy-center" bindtap="__e">重置</view><view data-event-opts="{{[['tap',[['submitSearch',['$event']]]]]}}" class="alert_btn flex-xy-center" style="{{('background:rgba('+$root.m7+',0.8);color:#FFF')}}" bindtap="__e">确定</view></view></view></block><block wx:if="{{alertState=='4'}}"><view class="alert_module"><view class="alert_title">{{text['酒店']+"类型"}}</view><view class="flex flex-wp"><block wx:for="{{$root.l1}}" wx:for-item="item" wx:for-index="index"><block><view class="{{['alert_tag ']}}" style="{{(item.m8?'background:'+item.m9+';border:0;color:#fff':'')}}" data-id="{{item.$orig.id}}" data-event-opts="{{[['tap',[['catetypeChange',['$event']]]]]}}" bindtap="__e">{{item.$orig.name}}</view></block></block></view><view class="alert_opt flex-y-center flex-bt"><view data-event-opts="{{[['tap',[['cancel',['$event']]]]]}}" class="alert_btn flex-xy-center" bindtap="__e">重置</view><view data-event-opts="{{[['tap',[['submitSearch',['$event']]]]]}}" class="alert_btn flex-xy-center" style="{{('background:rgba('+$root.m10+',0.8);color:#FFF')}}" bindtap="__e">确定</view></view></view></block><block wx:if="{{alertState=='5'}}"><view class="alert_module"><radio-group><label class="sort flex-y-center flex-bt" data-value="0" data-name="状态" data-event-opts="{{[['tap',[['emptyChange',['$event']]]]]}}" bindtap="__e"><view>全部</view><radio class="sort_icon" color="#fac428" checked="{{emptystatus=='0'?true:false}}"></radio></label><label class="sort flex-y-center flex-bt" data-value="1" data-name="空房" data-event-opts="{{[['tap',[['emptyChange',['$event']]]]]}}" bindtap="__e"><view>空房</view><radio class="sort_icon" color="#fac428" checked="{{emptystatus=='1'?true:false}}"></radio></label><label class="sort flex-y-center flex-bt" data-value="2" data-name="已满" data-event-opts="{{[['tap',[['emptyChange',['$event']]]]]}}" bindtap="__e"><view>已满</view><radio class="sort_icon" color="#fac428" checked="{{emptystatus=='2'?true:false}}"></radio></label></radio-group></view></block><view class="hotels-list"><block wx:for="{{$root.l2}}" wx:for-item="item" wx:for-index="index"><block><view class="hotels-options" data-url="{{'hoteldetails?id='+item.$orig.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="hotel-img"><image src="{{item.$orig.pic}}"></image></view><view class="hotel-info"><view class="hotel-title">{{item.$orig.name}}</view><view class="hotel-address">{{item.$orig.address}}</view><view class="hotel-characteristic"><block wx:for="{{item.$orig.tag}}" wx:for-item="items" wx:for-index="indexs"><block><view class="characteristic-options" style="{{('background:rgba('+item.m11+',0.05);color:'+item.m12)}}">{{items}}</view></block></block></view><view class="hotel-but-view"><view class="make-info"><block wx:if="{{item.$orig.min_daymoney}}"><view class="hotel-price" style="{{'color:'+(item.m13)+';'}}"><view class="hotel-price-num">{{item.$orig.min_daymoney+moneyunit}}</view><view>/晚起</view></view></block><block wx:else><view class="hotel-price" style="{{'color:'+(item.m14)+';'}}"><view>￥</view><view class="hotel-price-num">{{item.$orig.min_price}}</view><view>起</view></view></block><view class="hotel-text">{{item.$orig.sales+"人预定"}}</view></view><view class="hotel-make" style="{{('background:rgba('+item.m15+',0.8);color:#FFF')}}">预约</view></view></view></view></block></block></view><block wx:if="{{nodata}}"><nodata vue-id="294315ac-1" bind:__l="__l"></nodata></block><block wx:if="{{nomore}}"><nomore vue-id="294315ac-2" bind:__l="__l"></nomore></block><block wx:if="{{loading}}"><loading vue-id="294315ac-3" bind:__l="__l"></loading></block></block></block></view>