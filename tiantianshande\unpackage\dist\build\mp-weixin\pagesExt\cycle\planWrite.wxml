<view class="data-v-0093be47"><block wx:if="{{isload}}"><view class="page data-v-0093be47"><view class="head data-v-0093be47"><image class="head_img _img data-v-0093be47" src="{{nowguige.pic||product.pic}}" data-url="{{nowguige.pic||product.pic}}" alt data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image><view class="data-v-0093be47"><view class="head_title data-v-0093be47">{{product.name}}</view><view class="head_text data-v-0093be47">{{nowguige.name+" | "+product.ps_cycle_title}}</view><view class="head_price data-v-0093be47"><text class="head_icon data-v-0093be47">￥</text><block wx:if="{{nowguige.sell_price>0}}"><text class="data-v-0093be47">{{nowguige.sell_price}}</text></block><block wx:else><text class="data-v-0093be47">{{product.sell_price}}</text></block></view></view></view><view class="body data-v-0093be47"><block wx:for="{{guigedata}}" wx:for-item="item" wx:for-index="index"><view class="body_item data-v-0093be47"><view class="body_title flex flex-bt data-v-0093be47">{{item.title}}<text class="body_text data-v-0093be47">请选择周期购计划</text></view><view class="body_content data-v-0093be47"><block wx:for="{{item.items}}" wx:for-item="item2" wx:for-index="index2" wx:key="*this"><view class="{{['body_tag','data-v-0093be47',ggselected[item.k]==item2.k?'body_active':'']}}" data-itemk="{{item.k}}" data-idx="{{item2.k}}" data-event-opts="{{[['tap',[['ggchange',['$event']]]]]}}" bindtap="__e">{{item2.title}}</view></block></view></view></block><view class="body_item data-v-0093be47"><view class="body_title flex flex-bt data-v-0093be47">配送时间<text class="body_text data-v-0093be47">{{product.ps_cycle_title}}</text></view><block wx:if="{{product.ps_cycle==1}}"><view class="body_content data-v-0093be47"><block wx:for="{{rateList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['rateClick',[index]]]]]}}" class="{{['body_tag','data-v-0093be47',rateIndex===index?'body_active':'']}}" bindtap="__e">{{item.label}}</view></block></view></block></view><view class="body_item data-v-0093be47"><view class="body_title flex flex-bt data-v-0093be47"><label class="_span data-v-0093be47">开始时间</label><view data-event-opts="{{[['tap',[['toCheckDate',['$event']]]]]}}" class="body_data data-v-0093be47" bindtap="__e">{{week+" "+(startDate?startDate:'请选择开始时间')+''}}<image class="body_detail _img data-v-0093be47" src="{{pre_url+'/static/img/week/week_detail.png'}}"></image></view></view></view><view class="body_item data-v-0093be47"><view class="body_title flex flex-bt data-v-0093be47"><text class="data-v-0093be47">配送期数</text><view class="flex-y-center data-v-0093be47"><block wx:if="{{min_qsnum!=1}}"><text class="body_notice data-v-0093be47">{{min_qsnum+"期起订"}}</text></block><view class="body_data data-v-0093be47"><block wx:if="{{qsnumState}}"><image class="{{['body_opt','_img','data-v-0093be47',qsnum<=min_qsnum?'body_disabled':'']}}" src="{{pre_url+'/static/img/week/week_cut.png'}}" data-event-opts="{{[['tap',[['qsminus',['$event']]]]]}}" bindtap="__e"></image></block><block wx:if="{{!qsnumState}}"><image class="body_opt body_disabled _img data-v-0093be47" src="{{pre_url+'/static/img/week/week_cut.png'}}"></image></block><input class="body_num data-v-0093be47" type="number" data-event-opts="{{[['blur',[['getQsTotal',['$event']]]]]}}" value="{{qsnum}}" bindblur="__e"/><image class="body_opt _img data-v-0093be47" src="{{pre_url+'/static/img/week/week_add.png'}}" data-event-opts="{{[['tap',[['qsplus',['$event']]]]]}}" bindtap="__e"></image></view></view></view></view><view class="body_item data-v-0093be47"><view class="body_title flex flex-bt data-v-0093be47"><text class="data-v-0093be47">每期数量</text><view class="flex-y-center data-v-0093be47"><block wx:if="{{min_num!=1}}"><text class="body_notice data-v-0093be47">{{min_num+"件起订"}}</text></block><view class="body_data data-v-0093be47"><block wx:if="{{numState}}"><image class="{{['body_opt','_img','data-v-0093be47',num<=min_num?'body_disabled':'']}}" src="{{pre_url+'/static/img/week/week_cut.png'}}" data-event-opts="{{[['tap',[['gwcminus',['$event']]]]]}}" bindtap="__e"></image></block><block wx:if="{{!numState}}"><image class="body_opt body_disabled _img data-v-0093be47" src="{{pre_url+'/static/img/week/week_cut.png'}}"></image></block><input class="body_num data-v-0093be47" type="number" data-event-opts="{{[['blur',[['getTotal',['$event']]]]]}}" value="{{num}}" bindblur="__e"/><image class="body_opt _img data-v-0093be47" src="{{pre_url+'/static/img/week/week_add.png'}}" data-event-opts="{{[['tap',[['gwcplus',['$event']]]]]}}" bindtap="__e"></image></view></view></view></view></view></view></block><block wx:if="{{product.status==1}}"><view class="{{['bottombar','flex-row','flex-xy-center','data-v-0093be47',menuindex>-1?'tabbarbot':'notabbarbot']}}"><view class="operate_data data-v-0093be47"><view class="operate_text data-v-0093be47">配送<text class="operate_color data-v-0093be47">{{!qsnum?'':qsnum}}</text>期，共<text class="operate_color data-v-0093be47">{{!num||!qsnum?'':num*qsnum}}</text>件商品</view><view class="operate_price data-v-0093be47"><text class="operate_lable data-v-0093be47">总价：</text><text class="operate_tag data-v-0093be47">￥</text><text class="operate_num data-v-0093be47">{{!num||!qsnum?'':totalprice}}</text></view></view><view data-event-opts="{{[['tap',[['tobuy',['$event']]]]]}}" class="tobuy flex1 data-v-0093be47" style="{{'background:'+($root.m0)+';'}}" bindtap="__e"><text class="data-v-0093be47">去结算</text></view></view></block><block wx:if="{{loading}}"><loading vue-id="41e6841d-1" class="data-v-0093be47" bind:__l="__l"></loading></block></view>