<view class="container"><block wx:if="{{isload}}"><block><view style="width:100%;height:80rpx;text-align:center;line-height:80rpx;"><view class="search-text" style="height:80rpx;line-height:80rpx;font-size:35rpx;">我发起的代购单</view></view><dd-tab style="margin-top:75rpx;" vue-id="534174b6-1" itemdata="{{['全部','待付款','待发货','待收货','已完成']}}" itemst="{{['all','0','1','2','3']}}" st="{{st}}" isfixed="{{true}}" data-event-opts="{{[['^changetab',[['changetab']]]]}}" bind:changetab="__e" bind:__l="__l"></dd-tab><view class="order-content" style="margin-top:85rpx;"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><view class="order-box" data-url="{{'/shopPackage/shop/daikebuy?id='+item.$orig.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="head"><view class="flex1"></view><block wx:if="{{item.$orig.status==0}}"><text class="st0">待付款</text></block><block wx:if="{{item.$orig.status==1&&item.$orig.freight_type!=1}}"><text class="st1">待发货</text></block><block wx:if="{{item.$orig.status==1&&item.$orig.freight_type==1}}"><text class="st1">待提货</text></block><block wx:if="{{item.$orig.status==2}}"><text class="st2">待收货</text></block><block wx:if="{{item.$orig.status==3}}"><text class="st3">已完成</text></block><block wx:if="{{item.$orig.status==4}}"><text class="st4">已关闭</text></block></view><block wx:for="{{item.$orig.product}}" wx:for-item="item2" wx:for-index="idx" wx:key="idx"><block><view class="content" style="{{('margin-bottom:20rpx')}}"><view data-url="{{'/shopPackage/shop/product?id='+item2.proid}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e"><image src="{{item2.pic}}"></image></view><view class="detail"><text class="t1">{{item2.name}}</text><text class="t2">{{item2.ggname}}</text><view class="t3"><text class="x1 flex1">{{"￥"+item2.sell_price}}</text><text class="x2">{{"×"+item2.num}}</text></view><view class="t3"></view></view></view></block></block><view class="bottom"><text class="t2">{{item.$orig.typenamename}}</text></view><view class="bottom"><text>{{"共计"+item.$orig.procount+"件商品 实付:￥"+item.$orig.totalprice+''}}<block wx:if="{{item.$orig.balance_price>0&&item.$orig.balance_pay_status==0}}"><label style="display:block;float:right;" class="_span">{{"尾款：￥"+item.$orig.balance_price}}</label></block></text><block wx:if="{{item.$orig.refund_status==1}}"><text style="color:red;padding-left:6rpx;">{{"退款中￥"+item.$orig.refund_money}}</text></block><block wx:if="{{item.$orig.refund_status==2}}"><text style="color:red;padding-left:6rpx;">{{"已退款￥"+item.$orig.refund_money}}</text></block><block wx:if="{{item.$orig.refund_status==3}}"><text style="color:red;padding-left:6rpx;">退款申请已驳回</text></block></view><view class="bottom"><text>{{"收货人："+item.$orig.name+" "+item.$orig.address_tel}}</text></view><view class="bottom"><text>{{"地址："+item.$orig.province+item.$orig.city+item.$orig.district+item.$orig.area+item.$orig.default_address}}</text></view><block wx:if="{{item.$orig.tips!=''}}"><view class="bottom"><text style="color:red;">{{item.$orig.tips}}</text></view></block><view class="op"><block wx:if="{{item.g0}}"><block><view class="btn2" data-url="{{'invoice?type=shop&orderid='+item.$orig.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e">发票</view></block></block><view class="btn2" data-url="{{'/pagesExa/daike/daikebuy?id='+item.$orig.id+'&mid='+item.$orig.mid}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e">详情</view><block wx:if="{{item.$orig.status==0}}"><block><view class="btn2" data-id="{{item.$orig.id}}" data-event-opts="{{[['tap',[['toclose',['$event']]]]]}}" catchtap="__e">关闭订单</view><block wx:if="{{item.$orig.paytypeid==5}}"><block><block wx:if="{{item.$orig.transfer_check==1}}"><view class="btn1" style="{{'background:'+(item.m0)+';'}}" data-url="{{'/pages/pay/transfer?id='+item.$orig.payorderid}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e">付款凭证</view></block><block wx:else><view class="btn1" style="{{'background:'+(item.m1)+';'}}"><block wx:if="{{item.$orig.transfer_check==0}}"><text>转账待审核</text></block><block wx:if="{{item.$orig.transfer_check==-1}}"><text>转账已驳回</text></block></view></block></block></block></block></block><block wx:if="{{item.$orig.status==1}}"><block><block wx:if="{{item.$orig.paytypeid!='4'}}"><block><block wx:if="{{canrefund==1&&item.$orig.refundnum<item.$orig.procount}}"><view class="btn2" data-url="{{'refundSelect?orderid='+item.$orig.id+'&price='+item.$orig.totalprice}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e">退款</view></block></block></block><block wx:else><block></block></block></block></block><block wx:if="{{item.$orig.status==2}}"><block><block wx:if="{{item.$orig.paytypeid!='4'}}"><block><block wx:if="{{canrefund==1&&item.$orig.refundnum<item.$orig.procount}}"><view class="btn2" data-url="{{'refundSelect?orderid='+item.$orig.id+'&price='+item.$orig.totalprice}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e">退款</view></block></block></block><block wx:else><block></block></block><block wx:if="{{item.$orig.freight_type!=3&&item.$orig.freight_type!=4}}"><block><block wx:if="{{item.$orig.express_type=='express_wx'}}"><view class="btn2" data-index="{{index}}" data-event-opts="{{[['tap',[['logistics',['$event']]]]]}}" catchtap="__e">订单跟踪</view></block><block wx:else><view class="btn2" data-index="{{index}}" data-event-opts="{{[['tap',[['logistics',['$event']]]]]}}" catchtap="__e">查看物流</view></block></block></block><block wx:if="{{item.$orig.balance_pay_status==0&&item.$orig.balance_price>0}}"><view class="btn1" style="{{'background:'+(item.m2)+';'}}" data-url="{{'/pages/pay/pay?id='+item.$orig.balance_pay_orderid}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e">支付尾款</view></block><block wx:if="{{item.$orig.paytypeid!='4'&&(item.$orig.balance_pay_status==1||item.$orig.balance_price==0)}}"><view class="btn1" style="{{'background:'+(item.m3)+';'}}" data-id="{{item.$orig.id}}" data-event-opts="{{[['tap',[['orderCollect',['$event']]]]]}}" catchtap="__e">确认收货</view></block></block></block><block wx:if="{{(item.$orig.status==1||item.$orig.status==2)&&item.$orig.freight_type==1}}"><block><view class="btn2" data-hexiao_qr="{{item.$orig.hexiao_qr}}" data-event-opts="{{[['tap',[['showhxqr',['$event']]]]]}}" catchtap="__e">核销码</view></block></block><block wx:if="{{item.$orig.refundCount}}"><view class="btn2" data-url="{{'refundlist?orderid='+item.$orig.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e">查看退款</view></block><block wx:if="{{item.$orig.status==3||item.$orig.status==4}}"><block><view class="btn2" data-id="{{item.$orig.id}}" data-event-opts="{{[['tap',[['todel',['$event']]]]]}}" catchtap="__e">删除订单</view></block></block><block wx:if="{{item.$orig.bid>0&&item.$orig.status==3}}"><block><block wx:if="{{item.$orig.iscommentdp==0}}"><view class="btn1" style="{{'background:'+(item.m4)+';'}}" data-url="{{'/pagesExt/order/commentdp?orderid='+item.$orig.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e">评价店铺</view></block></block></block></view></view></block></block></view><block wx:if="{{nomore}}"><nomore vue-id="534174b6-2" bind:__l="__l"></nomore></block><block wx:if="{{nodata}}"><nodata vue-id="534174b6-3" bind:__l="__l"></nodata></block><uni-popup class="vue-ref" vue-id="534174b6-4" id="dialogHxqr" type="dialog" data-ref="dialogHxqr" bind:__l="__l" vue-slots="{{['default']}}"><view class="hxqrbox"><image class="img" src="{{hexiao_qr}}" data-url="{{hexiao_qr}}" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image><view class="txt">请出示核销码给核销员进行核销</view><view data-event-opts="{{[['tap',[['closeHxqr',['$event']]]]]}}" class="close" bindtap="__e"><image style="width:100%;height:100%;" src="/static/img/close2.png"></image></view></view></uni-popup><uni-popup class="vue-ref" vue-id="534174b6-5" id="dialogSelectExpress" type="dialog" data-ref="dialogSelectExpress" bind:__l="__l" vue-slots="{{['default']}}"><block wx:if="{{express_content}}"><view style="background:#fff;padding:20rpx 30rpx;border-radius:10rpx;width:600rpx;"><block wx:for="{{express_content}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="sendexpress" style="border-bottom:1px solid #f5f5f5;padding:20rpx 0;"><view class="sendexpress-item" style="display:flex;" data-url="{{'/pagesExt/order/logistics?express_com='+item.express_com+'&express_no='+item.express_no}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="flex1" style="color:#121212;">{{item.express_com+" - "+item.express_no}}</view><image style="width:30rpx;height:30rpx;" src="/static/img/arrowright.png"></image></view><block wx:if="{{item.express_oglist}}"><view style="margin-top:20rpx;"><block wx:for="{{item.express_oglist}}" wx:for-item="item2" wx:for-index="index2" wx:key="index2"><view class="oginfo-item" style="display:flex;align-items:center;margin-bottom:10rpx;"><image style="width:50rpx;height:50rpx;margin-right:10rpx;flex-shrink:0;" src="{{item2.pic}}"></image><view class="flex1" style="color:#555;">{{item2.name+"("+item2.ggname+")"}}</view></view></block></view></block></view></block></view></block></uni-popup></block></block><dp-tabbar vue-id="534174b6-6" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="534174b6-7" data-ref="popmsg" bind:__l="__l"></popmsg><uni-popup class="vue-ref" vue-id="534174b6-8" type="center" data-ref="more_one" bind:__l="__l" vue-slots="{{['default']}}"><uni-popup-dialog vue-id="{{('534174b6-9')+','+('534174b6-8')}}" mode="input" message="成功消息" duration="{{2000}}" valueType="number" before-close="{{true}}" data-event-opts="{{[['^close',[['close']]],['^confirm',[['confirm']]]]}}" bind:close="__e" bind:confirm="__e" bind:__l="__l"></uni-popup-dialog></uni-popup></view>