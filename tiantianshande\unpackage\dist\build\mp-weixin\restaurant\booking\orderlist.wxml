<view class="container"><block wx:if="{{isload}}"><block><dd-tab vue-id="18bb5e54-1" itemdata="{{['全部','待付款','待审核','已完成','退款']}}" itemst="{{['all','0','1','3','10']}}" st="{{st}}" isfixed="{{true}}" data-event-opts="{{[['^changetab',[['changetab']]]]}}" bind:changetab="__e" bind:__l="__l"></dd-tab><view class="order-content"><block><block wx:for="{{datalist}}" wx:for-item="item2" wx:for-index="idx" wx:key="idx"><view class="order-box"><block><view class="content" data-url="{{'detail?id='+item2.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="pic"><image class="img" src="{{item2.binfo.logo}}"></image></view><view class="detail"><text class="t1">{{item2.binfo.name}}</text><text class="t2">{{"预约桌台："+item2.tableName}}</text><text class="t2">{{"预约时间："+item2.booking_time}}</text></view></view></block></view></block></block></view><block wx:if="{{nomore}}"><nomore vue-id="18bb5e54-2" bind:__l="__l"></nomore></block><block wx:if="{{nodata}}"><nodata vue-id="18bb5e54-3" bind:__l="__l"></nodata></block></block></block><block wx:if="{{loading}}"><loading vue-id="18bb5e54-4" bind:__l="__l"></loading></block><dp-tabbar vue-id="18bb5e54-5" opt="{{opt}}" bind:__l="__l"></dp-tabbar></view>