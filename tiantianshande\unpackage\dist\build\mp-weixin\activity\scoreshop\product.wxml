<view><block wx:if="{{isload}}"><block><view class="swiper-container"><swiper class="swiper" indicator-dots="{{false}}" autoplay="{{true}}" interval="{{5000}}" data-event-opts="{{[['change',[['swiperChange',['$event']]]]]}}" bindchange="__e"><block wx:for="{{product.pics}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><swiper-item class="swiper-item"><view class="swiper-item-view"><image class="img" src="{{item}}" mode="widthFix"></image></view></swiper-item></block></block></swiper><view class="imageCount">{{current+1+"/"+$root.g0}}</view></view><view class="header"><view class="price_share"><view class="price"><view class="f1" style="{{'color:'+($root.m0)+';'}}">{{product.score_price+$root.m1}}<block wx:if="{{product.money_price*1>0}}"><text>{{"+"+product.money_price+"元"}}</text></block></view></view><view data-event-opts="{{[['tap',[['shareClick',['$event']]]]]}}" class="share" bindtap="__e"><image class="img" src="/static/img/share.png"></image><text class="txt">分享</text></view></view><view class="title">{{product.name}}</view><view class="sales_stock"><view class="f1">{{"已兑换"+product.sales+"件"}}</view><view class="f2">{{"库存："+product.stock}}</view></view><block wx:if="{{shopset.showcommission==1&&(product.commission>0||product.commission_score>0)}}"><view class="commission" style="{{'background:'+('rgba('+$root.m2+',0.1)')+';'+('color:'+($root.m3)+';')}}">{{'分享好友购买预计可得'+$root.m4+'：'}}<block wx:if="{{product.commission>0}}"><block><text style="font-weight:bold;padding:0 2px;">{{product.commission}}</text>{{product.commission_desc}}</block></block><block wx:if="{{product.commission>0&&product.commission_score>0}}"><text>+</text></block><block wx:if="{{product.commission_score>0}}"><block><text style="font-weight:bold;padding:0 2px;">{{product.commission_score}}</text>{{product.commission_score_desc}}</block></block></view></block></view><block wx:if="{{shopset.showjd==1}}"><view class="shop"><image class="p1" src="{{sysset.logo}}"></image><view class="p2 flex1"><view class="t1">{{sysset.name}}</view><view class="t2">{{sysset.desc}}</view></view><button class="p4" style="{{'background:'+('linear-gradient(90deg,'+$root.m5+' 0%,rgba('+$root.m6+',0.8) 100%)')+';'}}" data-url="/pages/index/index" data-opentype="reLaunch" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">进入店铺</button></view></block><view class="detail_title"><view class="t1"></view><view class="t2"></view><view class="t0">商品描述</view><view class="t2"></view><view class="t1"></view></view><view class="detail"><dp vue-id="67308450-1" pagecontent="{{pagecontent}}" bind:__l="__l"></dp></view><view style="width:100%;height:140rpx;"></view><block wx:if="{{product.status==1}}"><view class="{{['bottombar','flex-row',menuindex>-1?'tabbarbot':'notabbarbot']}}"><view class="f1"><block wx:if="{{kfurl!='contact::'}}"><view class="item" data-url="{{kfurl}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image class="img" src="/static/img/kefu.png"></image><view class="t1">客服</view></view></block><block wx:else><button class="item" open-type="contact"><image class="img" src="/static/img/kefu.png"></image><view class="t1">客服</view></button></block><view class="item flex1" data-url="cart" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image class="img" src="/static/img/gwc.png"></image><view class="t1">购物车</view><block wx:if="{{cartnum>0}}"><view class="cartnum">{{cartnum}}</view></block></view><view data-event-opts="{{[['tap',[['addfavorite',['$event']]]]]}}" class="item" bindtap="__e"><image class="img" src="/static/img/shoucang.png"></image><view class="t1">{{isfavorite?'已收藏':'收藏'}}</view></view></view><view class="op"><view data-event-opts="{{[['tap',[['addcart',['$event']]]]]}}" class="tocart flex-x-center flex-y-center" style="{{'background:'+($root.m7)+';'}}" bindtap="__e">加入购物车</view><view data-event-opts="{{[['tap',[['tobuy',['$event']]]]]}}" class="tobuy flex-x-center flex-y-center" style="{{'background:'+($root.m8)+';'}}" bindtap="__e">立即兑换</view></view></view></block><scrolltop vue-id="67308450-2" isshow="{{scrolltopshow}}" bind:__l="__l"></scrolltop><block wx:if="{{sharetypevisible}}"><view class="popup__container"><view data-event-opts="{{[['tap',[['handleClickMask',['$event']]]]]}}" class="popup__overlay" catchtap="__e"></view><view class="popup__modal" style="height:320rpx;min-height:320rpx;"><view class="popup__content"><view class="sharetypecontent"><block wx:if="{{$root.m9=='app'}}"><view data-event-opts="{{[['tap',[['shareapp',['$event']]]]]}}" class="f1" bindtap="__e"><image class="img" src="/static/img/weixin.png"></image><text class="t1">分享给好友</text></view></block><block wx:else><block wx:if="{{$root.m10=='mp'}}"><view data-event-opts="{{[['tap',[['sharemp',['$event']]]]]}}" class="f1" bindtap="__e"><image class="img" src="/static/img/weixin.png"></image><text class="t1">分享给好友</text></view></block><block wx:else><block wx:if="{{$root.m11=='h5'}}"><view data-event-opts="{{[['tap',[['sharemp',['$event']]]]]}}" class="f1" bindtap="__e"><image class="img" src="/static/img/weixin.png"></image><text class="t1">分享给好友</text></view></block><block wx:else><button class="f1" open-type="share"><image class="img" src="/static/img/weixin.png"></image><text class="t1">分享给好友</text></button></block></block></block><view data-event-opts="{{[['tap',[['showPoster',['$event']]]]]}}" class="f2" bindtap="__e"><image class="img" src="/static/img/sharepic.png"></image><text class="t1">生成分享图片</text></view></view></view></view></view></block><block wx:if="{{showposter}}"><view class="posterDialog"><view class="main"><view data-event-opts="{{[['tap',[['posterDialogClose',['$event']]]]]}}" class="close" bindtap="__e"><image class="img" src="/static/img/close.png"></image></view><view class="content"><image class="img" src="{{posterpic}}" mode="widthFix" data-url="{{posterpic}}" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image></view></view></view></block></block></block><block wx:if="{{loading}}"><loading vue-id="67308450-3" bind:__l="__l"></loading></block><dp-tabbar vue-id="67308450-4" opt="{{opt}}" data-event-opts="{{[['^getmenuindex',[['getmenuindex']]]]}}" bind:getmenuindex="__e" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="67308450-5" data-ref="popmsg" bind:__l="__l"></popmsg></view>