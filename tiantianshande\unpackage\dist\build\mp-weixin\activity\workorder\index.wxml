<view class="container"><form data-formcontent="{{formdata.content}}" data-formid="{{cateid}}" data-event-opts="{{[['submit',[['editorFormSubmit',['$event']]]]]}}" bindsubmit="__e"><view class="content"><view class="form-item flex"><view class="title">工单类型:</view><view class="inputbox flex"><picker class="picker" mode="selector" range="{{cate}}" value="{{index}}" range-key="name" data-event-opts="{{[['change',[['BindPickerChange',['$event']]]]]}}" bindchange="__e"><view class="uni-input">{{catename}}</view></picker><text class="iconfont iconjiantou" style="color:#ccc;font-weight:normal;"></text></view></view><block wx:for="{{formdata.content}}" wx:for-item="item" wx:for-index="idx" wx:key="id"><view class="form-item"><view class="label">{{item.val1}}<block wx:if="{{item.val3==1}}"><text style="color:red;">*</text></block></view><block wx:if="{{item.key=='input'}}"><block><input class="input" type="text" name="{{'form'+idx}}" placeholder="{{item.val2}}" data-formidx="{{'form'+idx}}" placeholder-style="font-size:28rpx"/></block></block><block wx:if="{{item.key=='textarea'}}"><block><textarea class="textarea" name="{{'form'+idx}}" placeholder="{{item.val2}}" data-formidx="{{'form'+idx}}" placeholder-style="font-size:28rpx"></textarea></block></block><block wx:if="{{item.key=='radio'}}"><block><radio-group class="radio-group" name="{{'form'+idx}}" data-formidx="{{'form'+idx}}"><block wx:for="{{item.val2}}" wx:for-item="item1" wx:for-index="idx1" wx:key="id"><label class="flex-y-center"><radio class="radio" value="{{item1}}"></radio>{{item1+''}}</label></block></radio-group></block></block><block wx:if="{{item.key=='checkbox'}}"><block><checkbox-group class="checkbox-group" name="{{'form'+idx}}" data-formidx="{{'form'+idx}}"><block wx:for="{{item.val2}}" wx:for-item="item1" wx:for-index="idx1" wx:key="id"><label class="flex-y-center"><checkbox class="checkbox" value="{{item1}}"></checkbox>{{item1+''}}</label></block></checkbox-group></block></block><block wx:if="{{item.key=='selector'}}"><block><picker class="picker" mode="selector" name="{{'form'+idx}}" range="{{item.val2}}" data-idx="{{idx}}" data-formidx="{{'form'+idx}}" data-event-opts="{{[['change',[['editorBindPickerChange',['$event']]]]]}}" bindchange="__e"><block wx:if="{{editorFormdata[idx]||editorFormdata[idx]===0}}"><view>{{''+item.val2[editorFormdata[idx]]}}</view></block><block wx:else><view>请选择</view></block></picker><text class="iconfont iconjiantou" style="color:#999;font-weight:normal;"></text></block></block><block wx:if="{{item.key=='time'}}"><block><picker class="picker" mode="time" name="{{'form'+idx}}" value="" start="{{item.val2[0]}}" end="{{item.val2[1]}}" range="{{item.val2}}" data-bid="{{formdata}}" data-idx="{{idx}}" data-formidx="{{'form'+idx}}" data-event-opts="{{[['change',[['editorBindPickerChange',['$event']]]]]}}" bindchange="__e"><block wx:if="{{editorFormdata[idx]}}"><view>{{editorFormdata[idx]}}</view></block><block wx:else><view>请选择</view></block></picker><text class="iconfont iconjiantou" style="color:#999;font-weight:normal;"></text></block></block><block wx:if="{{item.key=='date'}}"><block><picker class="picker" mode="date" name="{{'form'+idx}}" value="" start="{{item.val2[0]}}" end="{{item.val2[1]}}" range="{{item.val2}}" data-bid="{{formdata}}" data-idx="{{idx}}" data-formidx="{{'form'+idx}}" data-event-opts="{{[['change',[['editorBindPickerChange',['$event']]]]]}}" bindchange="__e"><block wx:if="{{editorFormdata[idx]}}"><view>{{editorFormdata[idx]}}</view></block><block wx:else><view>请选择</view></block></picker><text class="iconfont iconjiantou" style="color:#999;font-weight:normal;"></text></block></block><block wx:if="{{item.key=='region'}}"><block><uni-data-picker vue-id="{{'5325ad18-1-'+idx}}" localdata="{{items}}" popup-title="请选择省市区" placeholder="{{formdata['form'+idx]||'请选择省市区'}}" data-formidx="{{'form'+idx}}" data-event-opts="{{[['^change',[['onchange']]]]}}" bind:change="__e" bind:__l="__l"></uni-data-picker><input style="display:none;" type="text" name="{{'form'+idx}}" value="{{regiondata?regiondata:formdata['form'+idx]}}"/></block></block><block wx:if="{{item.key=='upload'}}"><block><input style="display:none;" type="text" name="{{'form'+idx}}" value="{{editorFormdata['content_pics'+idx]}}"/><view class="flex" style="flex-wrap:wrap;padding-top:20rpx;"><block wx:for="{{editorFormdata['content_pic'+idx]}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block wx:if="{{editorFormdata['content_pic'+idx]}}"><view class="form-imgbox"><view class="form-imgbox-close" data-pindex="{{index}}" data-field="{{'content_pic'+idx}}" data-idx="{{idx}}" data-event-opts="{{[['tap',[['removeimg',['$event']]]]]}}" bindtap="__e"><image src="/static/img/ico-del.png"></image></view><view class="form-imgbox-img"><image src="{{item}}" data-url="{{item}}" mode="widthFix" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image></view></view></block></block><view class="form-uploadbtn" style="{{'background:'+('url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 50rpx')+';'+('background-size:'+('80rpx 80rpx')+';')+('background-color:'+('#F3F3F3')+';')}}" data-field="{{'content_pic'+idx}}" data-idx="{{idx}}" data-formidx="{{'form'+idx}}" data-event-opts="{{[['tap',[['editorChooseImage',['$event']]]]]}}" bindtap="__e"></view></view></block></block></view></block><block wx:if="{{formdata.payset==1}}"><view class="dp-form-item"><text class="label">支付金额：</text><block wx:if="{{formdata.priceedit==1}}"><input class="input" type="text" name="price" data-formidx="price" data-event-opts="{{[['input',[['setfield',['$event']]]]]}}" value="{{formdata.price}}" bindinput="__e"/></block><block wx:if="{{formdata.priceedit==0}}"><text>{{formdata.price}}</text></block><text style="padding-left:10rpx;">元</text></view></block></view><view style="height:100rpx;"></view><view class="btnbox"><button class="dp-form-btn" style="{{'background:'+($root.m0)+';'}}" form-type="submit" data-formcontent="{{formdata.content}}" data-formid="{{cateid}}">提交</button></view></form><view class="myworkorder" data-url="record" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">我的工单</view><block wx:if="{{nodata}}"><nodata vue-id="5325ad18-2" bind:__l="__l"></nodata></block><block wx:if="{{nomore}}"><nomore vue-id="5325ad18-3" bind:__l="__l"></nomore></block><block wx:if="{{loading}}"><loading vue-id="5325ad18-4" bind:__l="__l"></loading></block><dp-tabbar vue-id="5325ad18-5" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="5325ad18-6" data-ref="popmsg" bind:__l="__l"></popmsg></view>