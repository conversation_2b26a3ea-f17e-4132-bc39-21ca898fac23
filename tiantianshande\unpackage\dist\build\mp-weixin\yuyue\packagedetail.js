(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["yuyue/packagedetail"],{1299:function(t,e,a){"use strict";(function(t,e){var o=a("47a9");a("06e9");o(a("3240"));var n=o(a("829f"));t.__webpack_require_UNI_MP_PLUGIN__=a,e(n.default)}).call(this,a("3223")["default"],a("df3c")["createPage"])},"829f":function(t,e,a){"use strict";a.r(e);var o=a("ad1c"),n=a("e7bb");for(var i in n)["default"].indexOf(i)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(i);a("dc48");var c=a("828b"),r=Object(c["a"])(n["default"],o["b"],o["c"],!1,null,null,null,!1,o["a"],void 0);e["default"]=r.exports},ad1c:function(t,e,a){"use strict";a.d(e,"b",(function(){return o})),a.d(e,"c",(function(){return n})),a.d(e,"a",(function(){}));var o=function(){var t=this,e=t.$createElement,a=(t._self._c,!t.loading&&t.packageDetail.id?t.t("color1"):null),o=!t.loading&&t.packageDetail.id?t.packageDetail.items&&t.packageDetail.items.length>0:null,n=!t.loading&&t.packageDetail.id?t.t("color1"):null,i=!t.loading&&t.packageDetail.id?t.t("color1"):null,c=!t.loading&&t.packageDetail.id?t.t("color1"):null;t.$mp.data=Object.assign({},{$root:{m0:a,g0:o,m1:n,m2:i,m3:c}})},n=[]},c47a:function(t,e,a){},dc48:function(t,e,a){"use strict";var o=a("c47a"),n=a.n(o);n.a},e7bb:function(t,e,a){"use strict";a.r(e);var o=a("f6d7"),n=a.n(o);for(var i in o)["default"].indexOf(i)<0&&function(t){a.d(e,t,(function(){return o[t]}))}(i);e["default"]=n.a},f6d7:function(t,e,a){"use strict";(function(t){var o=a("47a9");Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n=o(a("7ca3"));function i(t,e){var a=Object.keys(t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);e&&(o=o.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),a.push.apply(a,o)}return a}function c(t){for(var e=1;e<arguments.length;e++){var a=null!=arguments[e]?arguments[e]:{};e%2?i(Object(a),!0).forEach((function(e){(0,n.default)(t,e,a[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(a)):i(Object(a)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(a,e))}))}return t}var r=getApp(),l={data:function(){return{packageId:null,packageDetail:{},loading:!0}},onLoad:function(e){e.id?(this.packageId=e.id,this.getDetail()):(r.error("缺少套餐ID",(function(){r.goback()})),this.loading=!1),t.setNavigationBarTitle({title:"套餐详情"})},methods:{getDetail:function(){var t=this;t.loading=!0,r.post("ApiYuyuePackage/getDetail",{id:t.packageId},(function(e){if(console.log("ApiYuyuePackage/getDetail 返回:",JSON.stringify(e)),t.loading=!1,1==e.status&&e.package)try{var a=JSON.parse(JSON.stringify(e.package)),o=e.business?JSON.parse(JSON.stringify(e.business)):null;if(console.log("提取的 packageData:",JSON.stringify(a)),console.log("提取的 businessData:",JSON.stringify(o)),a.pic&&a.pic.startsWith("https://localhost")&&(console.log("清理套餐图片前缀"),a.pic=a.pic.substring("https://localhost".length)),o&&o.logo&&o.logo.startsWith("https://localhost")&&(console.log("清理商家Logo前缀"),o.logo=o.logo.substring("https://localhost".length)),a.items&&Array.isArray(a.items)&&a.items.forEach((function(t){t.product_pic&&t.product_pic.startsWith("https://localhost")&&(console.log("清理服务项图片前缀:",t.product_pic),t.product_pic=t.product_pic.substring("https://localhost".length))})),a.content&&"[]"!==a.content){console.log("格式化富文本内容");try{a.content=r.formatRichText(a.content)}catch(i){console.error("formatRichText 出错:",i),a.content="内容加载失败"}}else"[]"===a.content&&(console.log("富文本内容为 [], 置空处理"),a.content="");var n=c(c({},a),{},{business:o,title:e.title,isfavorite:e.isfavorite});console.log("最终赋值给 packageDetail 的对象:",JSON.stringify(n)),t.packageDetail=n,t.packageDetail&&t.packageDetail.id||(console.error("赋值后 packageDetail 或其 id 无效:",t.packageDetail),r.error("获取套餐详情失败(数据处理异常)"),t.packageDetail={})}catch(l){console.error("处理套餐详情数据时出错:",l),r.error("加载套餐信息异常"),t.packageDetail={}}else console.error("获取套餐详情失败，status 或 package 结构不符:",e),r.error(e.msg||"获取套餐详情失败",(function(){r.goback()}))}),(function(){t.loading=!1,r.error("请求失败",(function(){r.goback()}))}))},gotoBuy:function(){r.goto("/yuyue/packagebuy?package_id="+this.packageId)},gotoBusiness:function(t){t&&r.goto("/pages/business/index?id="+t)}}};e.default=l}).call(this,a("df3c")["default"])}},[["1299","common/runtime","common/vendor"]]]);