<view><view style="padding-bottom:50px;"><view class="option"><view data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" class="{{['option_bt',selectedOption=='0'?'option_get':'']}}" bindtap="__e">全部</view><view data-event-opts="{{[['tap',[['e1',['$event']]]]]}}" class="{{['option_bt',selectedOption=='1'?'option_get':'']}}" bindtap="__e">待评价</view></view><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="ll"><view data-event-opts="{{[['tap',[['goDetail',['$0'],[[['list','',index,'id']]]]]]]}}" bindtap="__e"><view class="ll-tit"><text style="font-size:34rpx;font-weight:bold;">{{item.$orig.venues_title}}</text><block wx:if="{{item.$orig.status==2}}"><text>已完成</text></block><block wx:else><block wx:if="{{item.$orig.status==3}}"><text>已关闭</text></block><block wx:else><block wx:if="{{item.$orig.status==1}}"><text>已支付</text></block><block wx:else><text>未支付</text></block></block></block></view><view class="ll-txt"><text>{{item.$orig.order_detail[0].field_name}}</text><text>{{"总价:"+item.$orig.total_price}}</text></view><view class="ll-txt"><text>{{item.$orig.order_detail[0].region_name}}</text><text>{{''+item.g0+"个场次"}}</text></view><view class="ll-txt"><text>{{item.$orig.days}}</text><text>{{"开始时间:"+item.$orig.order_detail[0].charges_list[0].time}}</text></view></view><view class="line"></view><view class="ll-btn"><block wx:if="{{item.$orig.status==0}}"><view data-event-opts="{{[['tap',[['goPay',['$0'],[[['list','',index,'id']]]]]]]}}" class="btn2" bindtap="__e">去支付</view></block></view></view></block></view></view>