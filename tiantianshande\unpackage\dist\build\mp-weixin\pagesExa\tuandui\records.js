require('../common/vendor.js');(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pagesExa/tuandui/records"],{"063d":function(t,e,i){"use strict";var a=i("51cf"),n=i.n(a);n.a},"51cf":function(t,e,i){},"8eca":function(t,e,i){"use strict";(function(t){Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var i=getApp(),a={data:function(){return{opt:{},loading:!1,isload:!1,recordsList:[],statsData:null,activityList:[],activityOptions:[],selectedActivityIndex:-1,selectedActivityId:0,statusOptions:[{value:"",text:"全部状态"},{value:0,text:"待发放"},{value:1,text:"已发放"},{value:2,text:"已拒绝"}],selectedStatusIndex:0,selectedStatus:"",pagenum:1,limit:20,nodata:!1,nomore:!1}},onLoad:function(t){this.opt=i.getopts(t),this.getdata()},onPullDownRefresh:function(){this.refreshData()},onReachBottom:function(){this.nodata||this.nomore||this.loading||(this.pagenum=this.pagenum+1,this.getRecordsList())},methods:{getdata:function(){var t=this;t.loading=!0,i.get("ApiTuandui/getActivityList",{},(function(e){1==e.status&&(t.activityList=e.data||[],t.activityOptions=[{title:"全部活动",id:0}].concat(t.activityList),t.selectedActivityIndex=0),t.getStatsData(),t.getRecordsList()}))},refreshData:function(){this.pagenum=1,this.recordsList=[],this.nodata=!1,this.nomore=!1,this.getStatsData(),this.getRecordsList()},getStatsData:function(){var t=this,e={};t.selectedActivityId>0&&(e.activity_id=t.selectedActivityId),i.get("ApiTuandui/getRewardStats",e,(function(e){1==e.status&&(t.statsData=e.data)}))},getRecordsList:function(){var e=this,a={page:e.pagenum,limit:e.limit};e.selectedActivityId>0&&(a.activity_id=e.selectedActivityId),""!==e.selectedStatus&&(a.status=e.selectedStatus),e.loading=!0,i.get("ApiTuandui/getRewardRecords",a,(function(a){if(e.loading=!1,e.isload=!0,1==a.status){var n=a.data||[];0==n.length?1==e.pagenum?e.nodata=!0:e.nomore=!0:1==e.pagenum?e.recordsList=n:e.recordsList=e.recordsList.concat(n),t.setNavigationBarTitle({title:"奖励记录"})}else i.alert(a.msg||"获取记录失败");t.stopPullDownRefresh()}))},onActivityChange:function(t){var e=t.detail.value;this.selectedActivityIndex=e,this.selectedActivityId=this.activityOptions[e].id,this.refreshData()},onStatusChange:function(t){var e=t.detail.value;this.selectedStatusIndex=e,this.selectedStatus=this.statusOptions[e].value,this.refreshData()}}};e.default=a}).call(this,i("df3c")["default"])},"930e":function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return s})),i.d(e,"a",(function(){return a}));var a={loading:function(){return i.e("components/loading/loading").then(i.bind(null,"ceaa"))},dpTabbar:function(){return i.e("components/dp-tabbar/dp-tabbar").then(i.bind(null,"b875"))},popmsg:function(){return i.e("components/popmsg/popmsg").then(i.bind(null,"2bf2"))}},n=function(){var t=this.$createElement,e=(this._self._c,this.loading&&0===this.recordsList.length);this.$mp.data=Object.assign({},{$root:{g0:e}})},s=[]},"93d9":function(t,e,i){"use strict";i.r(e);var a=i("930e"),n=i("a784");for(var s in n)["default"].indexOf(s)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(s);i("063d");var o=i("828b"),c=Object(o["a"])(n["default"],a["b"],a["c"],!1,null,null,null,!1,a["a"],void 0);e["default"]=c.exports},a784:function(t,e,i){"use strict";i.r(e);var a=i("8eca"),n=i.n(a);for(var s in a)["default"].indexOf(s)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(s);e["default"]=n.a},c62f:function(t,e,i){"use strict";(function(t,e){var a=i("47a9");i("06e9");a(i("3240"));var n=a(i("93d9"));t.__webpack_require_UNI_MP_PLUGIN__=i,e(n.default)}).call(this,i("3223")["default"],i("df3c")["createPage"])}},[["c62f","common/runtime","common/vendor"]]]);