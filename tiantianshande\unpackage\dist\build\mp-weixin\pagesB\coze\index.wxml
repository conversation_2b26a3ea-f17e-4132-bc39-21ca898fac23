<view class="container"><block wx:if="{{isload}}"><block><view class="header"><view class="header-title">扣子AI助手</view><view class="header-actions"><view class="action-btn" data-url="/pagesB/coze/history" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><text class="iconfont iconlishi"></text><text>历史记录</text></view></view></view><view class="feature-cards"><view class="feature-card chat-card" data-url="/pagesB/coze/chat" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="card-icon"><text class="iconfont iconliaotian" style="{{'color:'+($root.m0)+';'}}"></text></view><view class="card-content"><view class="card-title">AI智能聊天</view><view class="card-desc">与AI机器人进行智能对话，获取专业解答</view></view><view class="card-arrow"><text class="iconfont iconjiantou-you"></text></view></view><view class="feature-card workflow-card" data-url="/pagesB/coze/workflow" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="card-icon"><text class="iconfont icongongzuoliu" style="{{'color:'+('#52c41a')+';'}}"></text></view><view class="card-content"><view class="card-title">智能工作流</view><view class="card-desc">运行自动化工作流，提升工作效率</view></view><view class="card-arrow"><text class="iconfont iconjiantou-you"></text></view></view><view class="feature-card file-card" data-url="/pagesB/coze/file-upload" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="card-icon"><text class="iconfont iconwenjian" style="{{'color:'+('#1890ff')+';'}}"></text></view><view class="card-content"><view class="card-title">文件处理</view><view class="card-desc">上传文件进行AI分析和处理</view></view><view class="card-arrow"><text class="iconfont iconjiantou-you"></text></view></view><view data-event-opts="{{[['tap',[['showStats',['$event']]]]]}}" class="feature-card stats-card" bindtap="__e"><view class="card-icon"><text class="iconfont icontongji" style="{{'color:'+('#722ed1')+';'}}"></text></view><view class="card-content"><view class="card-title">使用统计</view><view class="card-desc">查看API调用统计和使用情况</view></view><view class="card-arrow"><text class="iconfont iconjiantou-you"></text></view></view></view><view class="quick-actions"><view class="section-title">快速入口</view><view class="action-grid"><view class="action-item" data-url="/pagesB/coze/chat" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="action-icon"><text class="iconfont iconkuaisuliaotian"></text></view><view class="action-text">快速聊天</view></view><view class="action-item" data-url="/pagesB/coze/workflow-logs" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="action-icon"><text class="iconfont iconjilu"></text></view><view class="action-text">执行记录</view></view><view data-event-opts="{{[['tap',[['showHelp',['$event']]]]]}}" class="action-item" bindtap="__e"><view class="action-icon"><text class="iconfont iconbangzhu"></text></view><view class="action-text">使用帮助</view></view><view data-event-opts="{{[['tap',[['showSettings',['$event']]]]]}}" class="action-item" bindtap="__e"><view class="action-icon"><text class="iconfont iconshezhi"></text></view><view class="action-text">设置</view></view></view></view><uni-popup class="vue-ref" vue-id="714df0b9-1" type="center" data-ref="statsPopup" bind:__l="__l" vue-slots="{{['default']}}"><view class="stats-popup"><view class="popup-header"><view class="popup-title">使用统计</view><view data-event-opts="{{[['tap',[['closeStats',['$event']]]]]}}" class="popup-close" bindtap="__e"><text class="iconfont iconguanbi"></text></view></view><view class="stats-content"><view class="stats-item"><view class="stats-label">总请求次数</view><view class="stats-value" style="{{'color:'+($root.m1)+';'}}">{{statsData.total_requests||0}}</view></view><view class="stats-item"><view class="stats-label">成功次数</view><view class="stats-value" style="color:#52c41a;">{{statsData.success_requests||0}}</view></view><view class="stats-item"><view class="stats-label">失败次数</view><view class="stats-value" style="color:#ff4d4f;">{{statsData.failed_requests||0}}</view></view><view class="stats-item"><view class="stats-label">成功率</view><view class="stats-value" style="{{'color:'+($root.m2)+';'}}">{{(statsData.success_rate||0)+"%"}}</view></view></view><view class="popup-footer"><view data-event-opts="{{[['tap',[['closeStats',['$event']]]]]}}" class="btn-confirm" style="{{'background:'+($root.m3)+';'}}" bindtap="__e">确定</view></view></view></uni-popup><uni-popup class="vue-ref" vue-id="714df0b9-2" type="bottom" data-ref="helpPopup" bind:__l="__l" vue-slots="{{['default']}}"><view class="help-popup"><view class="popup-header"><view class="popup-title">使用帮助</view><view data-event-opts="{{[['tap',[['closeHelp',['$event']]]]]}}" class="popup-close" bindtap="__e"><text class="iconfont iconguanbi"></text></view></view><scroll-view class="help-content" scroll-y="true"><view class="help-section"><view class="help-title">AI智能聊天</view><view class="help-text">• 选择合适的AI机器人进行对话</view><view class="help-text">• 输入问题或需求，获取智能回答</view><view class="help-text">• 支持上下文对话，记忆对话历史</view></view><view class="help-section"><view class="help-title">智能工作流</view><view class="help-text">• 选择预设的工作流模板</view><view class="help-text">• 配置工作流参数</view><view class="help-text">• 支持同步和异步执行</view></view><view class="help-section"><view class="help-title">文件处理</view><view class="help-text">• 上传文档、图片等文件</view><view class="help-text">• AI自动分析文件内容</view><view class="help-text">• 获取处理结果和建议</view></view></scroll-view></view></uni-popup></block></block><block wx:if="{{loading}}"><loading vue-id="714df0b9-3" bind:__l="__l"></loading></block></view>