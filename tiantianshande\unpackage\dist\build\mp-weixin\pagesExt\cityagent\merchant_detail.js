require('../common/vendor.js');(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pagesExt/cityagent/merchant_detail"],{"058e":function(t,n,e){},"094d":function(t,n,e){"use strict";e.r(n);var a=e("bbcc"),r=e.n(a);for(var o in a)["default"].indexOf(o)<0&&function(t){e.d(n,t,(function(){return a[t]}))}(o);n["default"]=r.a},"7d11":function(t,n,e){"use strict";var a=e("058e"),r=e.n(a);r.a},bbcc:function(t,n,e){"use strict";(function(t){Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;var e=getApp(),a={data:function(){return{opt:{},loading:!1,isload:!1,merchant_id:0,merchant:{},statistics:{today_orders:0,today_amount:"0.00",month_orders:0,month_amount:"0.00",total_orders:0,total_amount:"0.00"},recent_orders:[]}},onLoad:function(t){this.opt=e.getopts(t),this.merchant_id=t.merchant_id||0,this.merchant_id?this.getdata():e.error("参数错误")},onPullDownRefresh:function(){this.getdata()},methods:{getdata:function(){var n=this;n.loading=!0,e.get("ApiCityAgent/getMerchantDetail",{merchant_id:n.merchant_id},(function(a){n.loading=!1,t.stopPullDownRefresh(),0!=a.status?(t.setNavigationBarTitle({title:a.merchant.name||"商户详情"}),n.merchant=a.merchant,n.statistics=a.statistics,n.recent_orders=a.recent_orders||[],n.loaded()):e.error(a.msg)}))},callMerchant:function(){var n=this.merchant.phone;n?t.showModal({title:"拨打电话",content:"确认拨打电话："+n+"？",success:function(a){a.confirm&&t.makePhoneCall({phoneNumber:n,fail:function(){e.error("拨打电话失败")}})}}):e.error("该商户未设置联系电话")},viewLocation:function(){var n=parseFloat(this.merchant.longitude),a=parseFloat(this.merchant.latitude),r=this.merchant.name;n&&a?t.openLocation({longitude:n,latitude:a,name:r,address:this.merchant.address,scale:18,fail:function(){e.error("打开地图失败")}}):e.error("该商户未设置位置信息")},goto:function(t){var n=t.currentTarget.dataset.url;n&&(n=n.replace("{{merchant.id}}",this.merchant.id),e.goto("/pagesExt/cityagent/"+n))},loaded:function(){this.isload=!0}}};n.default=a}).call(this,e("df3c")["default"])},c977:function(t,n,e){"use strict";e.d(n,"b",(function(){return r})),e.d(n,"c",(function(){return o})),e.d(n,"a",(function(){return a}));var a={loading:function(){return e.e("components/loading/loading").then(e.bind(null,"ceaa"))},dpTabbar:function(){return e.e("components/dp-tabbar/dp-tabbar").then(e.bind(null,"b875"))},popmsg:function(){return e.e("components/popmsg/popmsg").then(e.bind(null,"2bf2"))}},r=function(){var t=this.$createElement,n=(this._self._c,this.isload?this.recent_orders.length:null),e=this.isload?this.recent_orders.length:null;this.$mp.data=Object.assign({},{$root:{g0:n,g1:e}})},o=[]},e6365:function(t,n,e){"use strict";(function(t,n){var a=e("47a9");e("06e9");a(e("3240"));var r=a(e("e9ce"));t.__webpack_require_UNI_MP_PLUGIN__=e,n(r.default)}).call(this,e("3223")["default"],e("df3c")["createPage"])},e9ce:function(t,n,e){"use strict";e.r(n);var a=e("c977"),r=e("094d");for(var o in r)["default"].indexOf(o)<0&&function(t){e.d(n,t,(function(){return r[t]}))}(o);e("7d11");var i=e("828b"),c=Object(i["a"])(r["default"],a["b"],a["c"],!1,null,null,null,!1,a["a"],void 0);n["default"]=c.exports}},[["e6365","common/runtime","common/vendor"]]]);