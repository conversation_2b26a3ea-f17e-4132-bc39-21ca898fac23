<block wx:if="{{pageState}}"><view class="data-v-af81bdf0"><calendar vue-id="0a1cdd06-1" is-show="{{true}}" between-start="{{disabledStartDate}}" ys-num="{{opt.ys}}" choose-type="{{opt.type}}" start-date="{{startDate}}" end-date="{{endDate}}" tip-data="{{t_data}}" mode="1" data-event-opts="{{[['^callback',[['getDate']]]]}}" bind:callback="__e" class="data-v-af81bdf0" bind:__l="__l"></calendar><block wx:if="{{noticeState}}"><view class="date-notice data-v-af81bdf0">点击日期修改开始时间</view></block><view data-event-opts="{{[['tap',[['toDetail',['$event']]]]]}}" class="date-footer data-v-af81bdf0" bindtap="__e">确定</view></view></block>