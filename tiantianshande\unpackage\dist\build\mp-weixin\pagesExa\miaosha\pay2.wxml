<view class="container"><block wx:if="{{isload}}"><block><block wx:for="{{datalist}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="paytype"><view class="payment-info"><view class="payment-row"><view class="payment-label">{{'收款 '+(index+1)+''}}</view><block wx:if="{{item.status==0}}"><text class="status pending">待上传</text></block><block wx:if="{{item.status==1}}"><text class="status uploaded">已上传</text></block></view><view class="payment-row"><view class="payment-label">收款人:</view><text class="info">{{item.outmember.nickname}}</text></view><view class="payment-row"><view class="payment-label">需支付金额:</view><text class="amount">{{item.trade_amount+"元"}}</text></view></view><view class="f2 payment-prompt"><text class="payment-text">付款信息：</text></view><block wx:if="{{item.outmember.wximg}}"><view class="payment-method"><view class="payment-row"><text class="payment-label">微信收款信息</text><image class="payment-image" src="{{item.outmember.wximg}}" data-event-opts="{{[['tap',[['previewImage',['$0'],[[['datalist','',index,'outmember.wximg']]]]]]]}}" bindtap="__e"></image></view></view></block><block wx:if="{{item.outmember.zfbimg}}"><view class="payment-method"><view class="payment-row"><text class="payment-label">支付宝收款信息</text><image class="payment-image" src="{{item.outmember.zfbimg}}" data-event-opts="{{[['tap',[['previewImage',['$0'],[[['datalist','',index,'outmember.zfbimg']]]]]]]}}" bindtap="__e"></image></view></view></block><block wx:if="{{item.outmember.bankname&&item.outmember.bankcarduser&&item.outmember.bankcardnum}}"><view class="payment-method"><view class="payment-row"><text class="payment-field">银行名称：</text><text class="payment-value">{{item.outmember.bankname}}</text><image class="copy-icon" src="/path/to/copy-icon.png" data-bankname="{{item.outmember.bankname}}" data-event-opts="{{[['tap',[['copybankname',['$event']]]]]}}" bindtap="__e"></image></view><view class="payment-row"><text class="payment-field">所属支行：</text><text class="payment-value">{{item.outmember.bankaddress}}</text><image class="copy-icon" src="/path/to/copy-icon.png" data-bankaddress="{{item.outmember.bankaddress}}" data-event-opts="{{[['tap',[['copybankaddress',['$event']]]]]}}" bindtap="__e"></image></view><view class="payment-row"><text class="payment-field">开户人：</text><text class="payment-value">{{item.outmember.bankcarduser}}</text><image class="copy-icon" src="/path/to/copy-icon.png" data-bankcarduser="{{item.outmember.bankcarduser}}" data-event-opts="{{[['tap',[['copybankcarduser',['$event']]]]]}}" bindtap="__e"></image></view><view class="payment-row"><text class="payment-field">卡号：</text><text class="payment-value">{{item.outmember.bankcardnum}}</text><image class="copy-icon" src="/path/to/copy-icon.png" data-bankcardnum="{{item.outmember.bankcardnum}}" data-event-opts="{{[['tap',[['copybankcardnum',['$event']]]]]}}" bindtap="__e"></image></view></view></block><view class="payment-method"><view class="payment-row"><text class="payment-field">U地址：</text><text class="payment-value">{{item.outmember.usd}}</text><image class="copy-icon" src="/path/to/copy-icon.png" data-usd="{{item.outmember.usd}}" data-event-opts="{{[['tap',[['copyusd',['$event']]]]]}}" bindtap="__e"></image></view></view><view data-event-opts="{{[['tap',[['goToTransfer',['$0'],[[['datalist','',index]]]]]]]}}" class="transfer-btn-container" bindtap="__e"><view class="transfer-btn"><text class="transfer-text">直接转账给收款人</text><image class="transfer-arrow" src="/static/img/arrowright.png"></image></view></view><block wx:if="{{!item.payimg}}"><view data-event-opts="{{[['tap',[['uploadpayimg',['$0'],[[['datalist','',index,'id']]]]]]]}}" class="upload-container" bindtap="__e"><view class="upload-text">上传支付凭证</view><image class="upload-icon" src="/path/to/upload-icon.png"></image><image class="arrow-icon" src="/static/img/arrowright.png"></image></view></block><block wx:if="{{item.payimg}}"><view class="voucher-container"><view class="voucher-text">支付凭证</view><image class="voucher-image" src="{{item.payimg}}" data-event-opts="{{[['tap',[['previewImage',['$0'],[[['datalist','',index,'payimg']]]]]]]}}" bindtap="__e"></image></view></block></view></block><view></view></block></block><block wx:if="{{invite_status&&invite_free}}"><view data-event-opts="{{[['tap',[['closeInvite',['$event']]]]]}}" style="width:100%;height:100%;background-color:#000;position:fixed;opacity:0.5;z-index:99;top:0;" bindtap="__e"></view></block><block wx:if="{{invite_status&&invite_free}}"><view style="width:700rpx;margin:0 auto;position:fixed;top:10%;left:25rpx;z-index:100;"><view data-event-opts="{{[['tap',[['gotoInvite',['$event']]]]]}}" style="background-color:#fff;border-radius:20rpx;overflow:hidden;width:100%;min-height:700rpx;" bindtap="__e"><image style="width:100%;height:auto;" src="{{invite_free.pic}}" mode="widthFix"></image></view><block wx:if="{{invite_status&&invite_free}}"><view data-event-opts="{{[['tap',[['closeInvite',['$event']]]]]}}" style="width:80rpx;height:80rpx;line-height:80rpx;text-align:center;font-size:30rpx;background-color:#fff;margin:0 auto;border-radius:50%;margin-top:20rpx;" bindtap="__e">X</view></block></view></block><block wx:if="{{loading}}"><loading vue-id="3fff7f70-1" bind:__l="__l"></loading></block><dp-tabbar vue-id="3fff7f70-2" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="3fff7f70-3" data-ref="popmsg" bind:__l="__l"></popmsg></view>