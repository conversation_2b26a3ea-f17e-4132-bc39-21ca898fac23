<view class="container"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="item"><block wx:if="{{item.$orig.type=='zhaopin'}}"><view class="product-item2" data-url="{{'/zhaopin/zhaopin/detail?id='+item.$orig.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="product-pic"><image src="{{item.$orig.product.thumb}}" mode="widthFix"></image></view><view class="product-info"><view class="p2">{{item.$orig.product.title}}</view><view class="p3"><text class="t1">{{item.$orig.product.cname}}</text></view><view class="p3"><text class="t1">{{item.$orig.product.salary}}</text></view></view></view></block><block wx:else><block wx:if="{{item.$orig.type=='qiuzhi'}}"><view class="product-item2" data-url="{{'/zhaopin/qiuzhi/detail?id='+item.$orig.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="product-pic"><image src="{{item.$orig.product.thumb}}" mode="widthFix"></image></view><view class="product-info"><view class="p2">{{item.$orig.product.title}}</view><view class="p3"><text class="t1">{{item.$orig.product.name+"/"+item.$orig.product.age+"岁/"+(item.$orig.product.sex==1?'男':'女')+"/"+(item.$orig.product.has_job==1?'在职':'离职')}}</text></view><view class="p3"><text class="t1">{{item.$orig.product.cnames}}</text></view></view></view></block><block wx:else><view class="product-item2" data-url="{{(item.$orig.type=='shop'?'/pages/':'/activity/')+item.$orig.type+'/product?id='+item.$orig.proid}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="product-pic"><image src="{{item.$orig.product.pic}}" mode="widthFix"></image></view><block wx:if="{{item.$orig.type=='scoreshop'}}"><view class="product-info"><view class="p1">{{item.$orig.product.name}}</view><view class="p2"><block wx:if="{{item.$orig.product.score_price}}"><text class="t1" style="{{'color:'+(item.m0)+';'}}">{{item.$orig.product.score_price+item.m1}}</text></block><block wx:if="{{item.$orig.product.sell_price}}"><text class="t2">{{"市场价￥"+item.$orig.product.sell_price}}</text></block></view><view class="p3"><block wx:if="{{item.$orig.product.sales>0}}"><text class="t1">已兑换<text style="font-size:24rpx;color:#f40;padding:0 2rpx;">{{item.$orig.product.sales}}</text>件</text></block><block wx:else><block wx:if="{{item.$orig.product.sellpoint}}"><text class="t2">{{item.$orig.product.sellpoint}}</text></block></block></view></view></block><block wx:else><view class="product-info"><view class="p1">{{item.$orig.product.name}}</view><view class="p2"><block wx:if="{{item.$orig.product.sell_price}}"><text class="t1" style="{{'color:'+(item.m2)+';'}}"><text style="font-size:22rpx;">￥</text>{{item.$orig.product.sell_price}}</text></block><block wx:if="{{item.$orig.product.market_price&&item.$orig.product.market_price*1>item.$orig.product.sell_price*1}}"><text class="t2">{{"￥"+item.$orig.product.market_price}}</text></block></view><view class="p3"><block wx:if="{{item.$orig.product.sales>0}}"><text class="t1">已售<text style="font-size:24rpx;color:#f40;padding:0 2rpx;">{{item.$orig.product.sales}}</text>件</text></block><block wx:else><block wx:if="{{item.$orig.product.sellpoint}}"><text class="t2">{{item.$orig.product.sellpoint}}</text></block></block></view></view></block></view></block></block><view class="foot"><text class="flex1">{{"浏览时间："+item.$orig.createtime}}</text><text class="btn" data-id="{{item.$orig.proid}}" data-type="{{item.$orig.type}}" data-event-opts="{{[['tap',[['favoritedel',['$event']]]]]}}" bindtap="__e">删除</text></view></view></block><block wx:if="{{nomore}}"><nomore vue-id="5151426e-1" bind:__l="__l"></nomore></block><block wx:if="{{nodata}}"><nodata vue-id="5151426e-2" bind:__l="__l"></nodata></block><block wx:if="{{loading}}"><loading vue-id="5151426e-3" bind:__l="__l"></loading></block><dp-tabbar vue-id="5151426e-4" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="5151426e-5" data-ref="popmsg" bind:__l="__l"></popmsg></view>