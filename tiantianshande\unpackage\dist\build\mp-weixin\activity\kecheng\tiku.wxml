<view><block wx:if="{{isload}}"><block><view class="wrap"><view class="top flex"><block wx:if="{{tkdata.type==1&&tkdata.rightcount==1}}"><view class="f1">单选题</view></block><block wx:if="{{tkdata.type==1&&tkdata.rightcount==2}}"><view class="f1">多选题</view></block><block wx:if="{{tkdata.type==2}}"><view class="f1">填空题</view></block><view class="f2">{{"倒计时："+djs}}</view><view class="f3">{{hasnum+"/"+nums}}</view></view><view class="question"><view class="title">{{''+hasnum+"."+tkdata.title+''}}</view><block wx:if="{{tkdata.type==1&&tkdata.rightcount==1}}"><block><view class="option_group"><block wx:for="{{tkdata.option}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="{{['option flex '+(index==currentindex?'on':'')]}}" data-index="{{index}}" data-event-opts="{{[['tap',[['selectOption',['$event']]]]]}}" bindtap="__e">{{''+tkdata.sorts[index]+''}}<view class="after"></view><view class="t1">{{item}}</view></view></block></view></block></block><block wx:if="{{tkdata.type==1&&tkdata.rightcount>1}}"><block><view class="option_group"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="{{['option flex '+(item.g0!=-1?'on':'')]}}" data-index="{{index}}" data-event-opts="{{[['tap',[['selectOption',['$event']]]]]}}" bindtap="__e">{{''+tkdata.sorts[index]+''}}<view class="after"></view><view class="t1">{{item.$orig}}</view></view></block></view></block></block><block wx:if="{{tkdata.type==2}}"><block><view class="option_group"><view class="uni-textarea"><textarea placeholder-style="color:#222" placeholder="答:" data-event-opts="{{[['blur',[['bindTextAreaBlur',['$event']]]]]}}" value="{{right_option}}" bindblur="__e"></textarea></view></view></block></block></view></view><view class="bottom flex"><block wx:if="{{hasnum==1}}"><block><button class="upbut flex-x-center flex-y-center hui">上一题</button></block></block><block wx:if="{{hasnum>1}}"><block><button class="upbut flex-x-center flex-y-center" style="{{'background:'+($root.m0)+';'}}" data-dttype="up" data-event-opts="{{[['tap',[['prevquestion',['$event']]]]]}}" bindtap="__e">上一题</button></block></block><block wx:if="{{nums==hasnum}}"><button class="downbtn flex-x-center flex-y-center" style="{{'background:'+($root.m1)+';'}}" data-dttype="down" data-event-opts="{{[['tap',[['finish',['$event']]]]]}}" bindtap="__e">交卷</button></block><block wx:else><button class="downbtn flex-x-center flex-y-center" style="{{'background:'+($root.m2)+';'}}" data-dttype="down" data-event-opts="{{[['tap',[['nextquestion',['$event']]]]]}}" bindtap="__e">下一题</button></block></view></block></block><block wx:if="{{loading}}"><loading vue-id="3d76da56-1" bind:__l="__l"></loading></block><dp-tabbar vue-id="3d76da56-2" opt="{{opt}}" data-event-opts="{{[['^getmenuindex',[['getmenuindex']]]]}}" bind:getmenuindex="__e" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="3d76da56-3" data-ref="popmsg" bind:__l="__l"></popmsg></view>