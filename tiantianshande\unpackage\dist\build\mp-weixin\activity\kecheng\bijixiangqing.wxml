<view class="container"><block wx:if="{{isload}}"><block><view class="note-detail-card"><view class="course-header"><view class="course-info"><image class="course-pic" src="{{noteDetail.kecheng_pic}}" mode="aspectFill"></image><view class="course-detail"><text class="course-name">{{noteDetail.kecheng_name}}</text><block wx:if="{{noteDetail.chapter_name}}"><text class="chapter-name">{{noteDetail.chapter_name}}</text></block></view></view></view><view class="note-content"><rich-text nodes="{{noteDetail.content}}"></rich-text></view><view class="note-info"><block wx:if="{{noteDetail.note_time}}"><view class="info-item"><text class="info-label">时间点:</text><text class="info-value">{{noteDetail.note_time}}</text></view></block><block wx:if="{{noteDetail.kechengset&&noteDetail.kechengset.notes_need_progress==1}}"><view class="info-item"><text class="info-label">学习进度:</text><text class="info-value">{{noteDetail.study_progress+"%"}}</text></view></block><view class="info-item"><text class="info-label">创建时间:</text><text class="info-value">{{noteDetail.createtime_format}}</text></view><block wx:if="{{noteDetail.updatetime&&noteDetail.updatetime!=noteDetail.createtime}}"><view class="info-item"><text class="info-label">更新时间:</text><text class="info-value">{{noteDetail.updatetime_format}}</text></view></block></view><view class="user-info"><image class="user-avatar" src="{{noteDetail.headimg||pre_url+'/static/img/default_avatar.png'}}" mode="aspectFill"></image><text class="user-nickname">{{noteDetail.nickname||'微信昵称'}}</text></view><view class="note-actions"><view data-event-opts="{{[['tap',[['editNote',['$event']]]]]}}" class="action-btn edit" bindtap="__e"><text class="iconfont icon-kecheng"></text><text>前往课程页面</text></view><view data-event-opts="{{[['tap',[['deleteNote',['$event']]]]]}}" class="action-btn delete" bindtap="__e"><text class="iconfont icon-shanchu"></text><text>删除</text></view><view data-event-opts="{{[['tap',[['goBack',['$event']]]]]}}" class="action-btn back" bindtap="__e"><text class="iconfont icon-fanhui"></text><text>返回</text></view></view></view></block></block><block wx:if="{{loading}}"><loading vue-id="5d30c274-1" bind:__l="__l"></loading></block><popmsg class="vue-ref" vue-id="5d30c274-2" data-ref="popmsg" bind:__l="__l"></popmsg></view>