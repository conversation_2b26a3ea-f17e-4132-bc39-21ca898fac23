<view class="container"><block wx:if="{{isload}}"><block><view class="header"><view class="header-title">扣子工作流</view><view class="header-actions"><view class="action-btn" data-url="/pagesB/coze/workflow-logs" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><text class="iconfont iconlishi"></text><text>执行记录</text></view></view></view><view class="workflow-list"><block wx:for="{{$root.l0}}" wx:for-item="workflow" wx:for-index="index" wx:key="index"><block><view class="workflow-item" data-workflow="{{workflow.g0}}" data-event-opts="{{[['tap',[['selectWorkflow',['$event']]]]]}}" bindtap="__e"><view class="workflow-header"><view class="workflow-name">{{workflow.$orig.name}}</view><view class="workflow-status" style="{{'color:'+(workflow.m0)+';'}}"><text class="iconfont iconzhengchang"></text></view></view><block wx:if="{{workflow.$orig.description}}"><view class="workflow-desc">{{workflow.$orig.description}}</view></block><view class="workflow-id">{{"ID: "+workflow.$orig.workflow_id}}</view></view></block></block><block wx:if="{{nodata}}"><nodata vue-id="5d77fb12-1" bind:__l="__l"></nodata></block></view><uni-popup class="vue-ref" vue-id="5d77fb12-2" type="bottom" data-ref="paramPopup" bind:__l="__l" vue-slots="{{['default']}}"><view class="param-popup"><view class="popup-header"><view class="popup-title">{{selectedWorkflow.name}}</view><view data-event-opts="{{[['tap',[['closeParamPopup',['$event']]]]]}}" class="popup-close" bindtap="__e"><text class="iconfont iconguanbi"></text></view></view><scroll-view class="param-content" scroll-y="true"><view class="param-form"><block wx:if="{{paramMode==='custom'}}"><block><block wx:for="{{$root.l1}}" wx:for-item="param" wx:for-index="index" wx:key="index"><block><view class="form-item"><view class="form-label">{{''+param.$orig.param_name+''}}<block wx:if="{{param.$orig.is_required}}"><text class="required">*</text></block></view><block wx:if="{{param.$orig.param_type==='text'}}"><view class="form-input"><input placeholder="{{param.$orig.placeholder||'请输入'+param.$orig.param_name}}" placeholder-style="color:#999" data-event-opts="{{[['input',[['__set_model',['$0','$1','$event',[]],['paramValues',[['paramList','',index,'param_key']]]]]]]}}" value="{{paramValues[param.$orig.param_key]}}" bindinput="__e"/></view></block><block wx:else><block wx:if="{{param.$orig.param_type==='number'}}"><view class="form-input"><input type="number" placeholder="{{param.$orig.placeholder||'请输入'+param.$orig.param_name}}" placeholder-style="color:#999" data-event-opts="{{[['input',[['__set_model',['$0','$1','$event',[]],['paramValues',[['paramList','',index,'param_key']]]]]]]}}" value="{{paramValues[param.$orig.param_key]}}" bindinput="__e"/></view></block><block wx:else><block wx:if="{{param.$orig.param_type==='select'}}"><view class="form-select" data-param="{{param.g1}}" data-event-opts="{{[['tap',[['showSelectOptions',['$event']]]]]}}" bindtap="__e"><text class="{{[paramValues[param.$orig.param_key]?'':'placeholder']}}">{{''+(paramValues[param.$orig.param_key]||'请选择'+param.$orig.param_name)+''}}</text><text class="iconfont iconxiala"></text></view></block><block wx:else><block wx:if="{{param.$orig.param_type==='textarea'}}"><view class="form-textarea"><textarea placeholder="{{param.$orig.placeholder||'请输入'+param.$orig.param_name}}" placeholder-style="color:#999" auto-height="{{true}}" data-event-opts="{{[['input',[['__set_model',['$0','$1','$event',[]],['paramValues',[['paramList','',index,'param_key']]]]]]]}}" value="{{paramValues[param.$orig.param_key]}}" bindinput="__e"></textarea></view></block><block wx:else><block wx:if="{{param.$orig.param_type==='file'}}"><view class="form-file"><view class="file-upload-btn" data-param-key="{{param.$orig.param_key}}" data-event-opts="{{[['tap',[['chooseFile',['$event']]]]]}}" bindtap="__e"><text class="iconfont iconshangchuan"></text><text>选择文件</text></view><block wx:if="{{paramValues[param.$orig.param_key]}}"><view class="file-name">{{''+paramValues[param.$orig.param_key].name+''}}</view></block></view></block></block></block></block></block><block wx:if="{{param.$orig.param_desc}}"><view class="form-desc">{{param.$orig.param_desc}}</view></block></view></block></block></block></block><block wx:else><block><view class="form-item"><view class="form-label">参数配置</view><view class="form-textarea"><textarea placeholder="请输入JSON格式的参数" placeholder-style="color:#999" auto-height="{{true}}" data-event-opts="{{[['input',[['__set_model',['','jsonParams','$event',[]]]]]]}}" value="{{jsonParams}}" bindinput="__e"></textarea></view><view class="form-desc">请输入有效的JSON格式参数</view></view></block></block><view class="form-item"><view data-event-opts="{{[['tap',[['toggleAsync',['$event']]]]]}}" class="form-checkbox" bindtap="__e"><text class="{{['iconfont '+(isAsync?'iconduihao':'iconweixuanzhong')]}}" style="{{(isAsync?{color:$root.m1}:{})}}"></text><text>异步执行</text></view><view class="form-desc">异步执行不会立即返回结果，需要通过执行记录查看结果</view></view></view></scroll-view><view class="popup-footer"><view data-event-opts="{{[['tap',[['closeParamPopup',['$event']]]]]}}" class="btn-cancel" bindtap="__e">取消</view><view data-event-opts="{{[['tap',[['runWorkflow',['$event']]]]]}}" class="btn-confirm" style="{{'background:'+($root.m2)+';'}}" bindtap="__e">运行工作流</view></view></view></uni-popup><uni-popup class="vue-ref" vue-id="5d77fb12-3" type="bottom" data-ref="selectPopup" bind:__l="__l" vue-slots="{{['default']}}"><view class="select-popup"><view class="popup-header"><view class="popup-title">{{currentSelectParam.param_name}}</view><view data-event-opts="{{[['tap',[['closeSelectPopup',['$event']]]]]}}" class="popup-close" bindtap="__e"><text class="iconfont iconguanbi"></text></view></view><scroll-view class="select-options" scroll-y="true"><block wx:for="{{$root.l2}}" wx:for-item="option" wx:for-index="index" wx:key="index"><block><view class="select-option" data-value="{{option.$orig.value}}" data-param-key="{{currentSelectParam.param_key}}" data-event-opts="{{[['tap',[['selectOption',['$event']]]]]}}" bindtap="__e"><text>{{option.$orig.label}}</text><block wx:if="{{paramValues[currentSelectParam.param_key]===option.$orig.value}}"><text class="iconfont iconduihao" style="{{'color:'+(option.m3)+';'}}"></text></block></view></block></block></scroll-view></view></uni-popup><uni-popup class="vue-ref" vue-id="5d77fb12-4" type="center" data-ref="resultPopup" bind:__l="__l" vue-slots="{{['default']}}"><view class="result-popup"><view class="popup-header"><view class="popup-title">执行结果</view><view data-event-opts="{{[['tap',[['closeResultPopup',['$event']]]]]}}" class="popup-close" bindtap="__e"><text class="iconfont iconguanbi"></text></view></view><scroll-view class="result-content" scroll-y="true"><view class="result-text">{{workflowResult}}</view></scroll-view><view class="popup-footer"><view data-event-opts="{{[['tap',[['closeResultPopup',['$event']]]]]}}" class="btn-confirm" style="{{'background:'+($root.m4)+';'}}" bindtap="__e">确定</view></view></view></uni-popup></block></block><block wx:if="{{loading}}"><loading vue-id="5d77fb12-5" bind:__l="__l"></loading></block></view>