<view class="container"><block wx:if="{{isload}}"><block><form data-event-opts="{{[['submit',[['formSubmit',['$event']]]]]}}" bindsubmit="__e"><view class="form-item1"><view class="label">商品信息</view><view class="product flex"><view class="img"><image src="{{og.propic}}"></image></view><view class="info flex1"><view class="f1">{{og.proname}}</view><view class="f2">{{og.ggname}}</view><view class="f3">{{"￥"+og.sell_price}}</view></view></view></view><view class="form-item2 flex flex-y-center"><view class="label">您的打分</view><view data-event-opts="{{[['touchmove',[['handleTouchMove',['$event']]]]]}}" class="i-rate" bindtouchmove="__e"><input class="i-rate-hide-input" type="text" name="score" value="{{score}}"/><block wx:for="{{5}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="{{['i-rate-star',index<score?'i-rate-current':'']}}" data-index="{{index}}" data-event-opts="{{[['tap',[['handleClick',['$event']]]]]}}" bindtap="__e"><block wx:if="{{index<score}}"><image src="/static/img/star2.png"></image></block><block wx:else><image src="/static/img/star.png"></image></block></view></block><view class="i-rate-text"></view></view></view><view class="form-item3 flex-col"><view class="label">您的评价</view><textarea style="height:200rpx;" placeholder="输入您的评价内容" placeholder-style="color:#ccc;" name="content" disabled="{{comment.id?true:false}}" value="{{comment.content}}"></textarea></view><view class="form-item4 flex-col"><view class="label">上传图片</view><view class="flex" style="flex-wrap:wrap;padding-top:20rpx;" id="content_picpreview"><block wx:for="{{content_pic}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="layui-imgbox"><block wx:if="{{!comment.id}}"><view class="layui-imgbox-close" data-index="{{index}}" data-field="content_pic" data-event-opts="{{[['tap',[['removeimg',['$event']]]]]}}" bindtap="__e"><image src="/static/img/ico-del.png"></image></view></block><view class="layui-imgbox-img"><image src="{{item}}" data-url="{{item}}" mode="widthFix" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image></view></view></block><block wx:if="{{$root.g0}}"><view class="uploadbtn" style="{{('background:url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 60rpx;background-size:80rpx 80rpx;background-color:#F3F3F3;')}}" data-field="content_pic" data-event-opts="{{[['tap',[['uploadimg',['$event']]]]]}}" bindtap="__e"></view></block></view></view><block wx:if="{{!comment.id}}"><button class="subbtn" form-type="submit">确定</button></block></form></block></block><block wx:if="{{loading}}"><loading vue-id="284d15e9-1" bind:__l="__l"></loading></block><dp-tabbar vue-id="284d15e9-2" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="284d15e9-3" data-ref="popmsg" bind:__l="__l"></popmsg></view>