<view class="container"><block wx:if="{{isload}}"><block><view class="search-container"><view class="topsearch flex-y-center"><view class="f1 flex-y-center"><image class="img" src="{{pre_url+'/static/img/search_ico.png'}}"></image><input placeholder="搜索订单号、商家名称" placeholder-style="font-size:24rpx;color:#C2C2C2" confirm-type="search" data-event-opts="{{[['confirm',[['searchConfirm',['$event']]]]]}}" value="{{keyword}}" bindconfirm="__e"/></view></view><view class="filter-navbar"><view class="filter-item" style="{{(status==''?'color:'+$root.m0:'')}}" data-status data-event-opts="{{[['tap',[['filterClick',['$event']]]]]}}" bindtap="__e">全部</view><view class="filter-item" style="{{(status=='1'?'color:'+$root.m1:'')}}" data-status="1" data-event-opts="{{[['tap',[['filterClick',['$event']]]]]}}" bindtap="__e">已完成</view><view class="filter-item" style="{{(status=='0'?'color:'+$root.m2:'')}}" data-status="0" data-event-opts="{{[['tap',[['filterClick',['$event']]]]]}}" bindtap="__e">待处理</view><view class="filter-item" data-field="money" data-order="{{order=='asc'?'desc':'asc'}}" data-event-opts="{{[['tap',[['sortClick',['$event']]]]]}}" bindtap="__e"><text style="{{(field=='money'?'color:'+$root.m3:'')}}">金额排序</text><text class="iconfont iconshangla" style="{{(field=='money'&&order=='asc'?'color:'+$root.m4:'')}}"></text><text class="iconfont icondaoxu" style="{{(field=='money'&&order=='desc'?'color:'+$root.m5:'')}}"></text></view></view></view><view class="order-list" id="datalist"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><view class="order-item" data-url="{{'maidandetail?id='+item.$orig.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="order-header"><view class="order-time">{{item.$orig.paytime}}</view><view class="order-status" style="{{'color:'+(item.$orig.status==1?item.m6:'#999')+';'}}">{{''+(item.$orig.status==1?'已完成':'待处理')+''}}</view></view><view class="order-body"><view class="order-info"><view class="order-title">{{item.$orig.title}}</view><view class="order-detail"><text class="detail-item">{{"订单号："+item.$orig.ordernum}}</text><text class="detail-item">{{"支付方式："+item.$orig.paytype}}</text></view><block wx:if="{{item.m7}}"><view class="reward-info"><text class="reward-label">排队奖励：</text><text class="reward-amount" style="{{'color:'+(item.m8)+';'}}">{{"+￥"+item.$orig.paidui_jiang}}</text></view></block></view><view class="order-amount"><text class="amount-text">{{"-￥"+item.$orig.money}}</text><text class="amount-label">支付金额</text></view></view></view></block></block></view></block></block><block wx:if="{{nodata}}"><nodata vue-id="055b4fbd-1" bind:__l="__l"></nodata></block><block wx:if="{{nomore}}"><nomore vue-id="055b4fbd-2" bind:__l="__l"></nomore></block><block wx:if="{{loading}}"><loading vue-id="055b4fbd-3" bind:__l="__l"></loading></block><dp-tabbar vue-id="055b4fbd-4" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="055b4fbd-5" data-ref="popmsg" bind:__l="__l"></popmsg></view>