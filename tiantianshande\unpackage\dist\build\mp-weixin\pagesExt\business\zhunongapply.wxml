<view><block wx:if="{{isload}}"><block><block wx:if="{{info.id&&info.status==2}}"><view style="color:red;padding:10rpx 30rpx;margin-top:20rpx;"><parse vue-id="08e644e4-1" content="{{bset.verify_reject||'审核未通过：'}}" bind:__l="__l"></parse>{{info.reason+'，请修改后重新提交'}}</view></block><block wx:if="{{info.id&&info.status==0}}"><view style="color:red;padding:10rpx 30rpx;margin-top:20rpx;"><parse vue-id="08e644e4-2" content="{{bset.verify_notice||'您的入驻申请已提交成功，请耐心等待审核，平台将于7个工作日内联系您核实信息，请留意来电'}}" bind:__l="__l"></parse></view></block><block wx:if="{{info.id&&info.status==1}}"><view style="color:red;padding:10rpx 30rpx;margin-top:20rpx;"><parse vue-id="08e644e4-3" content="{{bset.verify_success||'恭喜您审核通过！'}}" bind:__l="__l"></parse></view></block><view style="color:red;padding:10rpx 30rpx;margin-top:20rpx;"><parse vue-id="08e644e4-4" content="{{bset.verify_normal||'温馨提示：审核通过后需缴纳保证金可完成入驻'}}" bind:__l="__l"></parse></view><form data-event-opts="{{[['submit',[['subform',['$event']]]]]}}" bindsubmit="__e"><view class="apply_box"><view class="apply_item"><view>联系人姓名<text style="color:red;">*</text></view><view class="flex-y-center"><input type="text" name="linkman" placeholder="请填写姓名" value="{{info.linkman}}"/></view></view><view class="apply_item"><view>联系人电话<text style="color:red;">*</text></view><view class="flex-y-center"><input type="text" name="linktel" placeholder="请填写手机号码" value="{{info.linktel}}"/></view></view><view class="apply_item"><view>联系人微信<text style="color:red;">*</text></view><view class="flex-y-center"><input type="text" name="wxid" placeholder="请填写微信号" value="{{info.wxid}}"/></view></view><block wx:if="{{info.zhanshi==1}}"><view class="apply_item"><view>拓展人邀请码</view><view class="flex-y-center"><input type="text" name="tuozhanid" placeholder="请填写拓展员邀请码" value="{{info.tuozhanid}}"/></view></view></block></view><view class="apply_box"><view class="apply_item"><view>商家名称<text style="color:red;">*</text></view><view class="flex-y-center"><input type="text" name="name" placeholder="请输入商家名称" value="{{info.name}}"/></view></view><view class="apply_item"><view>商家描述<text style="color:red;">*</text></view><view class="flex-y-center"><input type="text" name="desc" placeholder="请输入商家描述" value="{{info.desc}}"/></view></view><view class="apply_item"><view>主营类目<text style="color:red;">*</text></view><view><picker value="{{cindex}}" range="{{cateArr}}" data-event-opts="{{[['change',[['cateChange',['$event']]]]]}}" bindchange="__e"><view class="picker">{{cateArr[cindex]}}</view></picker></view></view><view class="apply_item"><view>商户费率<text style="color:red;">*</text></view><view><picker value="{{rateIndex}}" range="{{rateArr}}" data-event-opts="{{[['change',[['rateChange',['$event']]]]]}}" bindchange="__e"><view class="picker">{{rateArr[rateIndex]}}</view></picker></view></view><view class="apply_item"><view>店铺坐标<text style="color:red;"></text></view><view class="flex-y-center"><input type="text" disabled="{{true}}" placeholder="请选择坐标" name="zuobiao" data-event-opts="{{[['tap',[['locationSelect',['$event']]]]]}}" value="{{latitude?latitude+','+longitude:''}}" bindtap="__e"/></view></view><view class="apply_item"><view>店铺地址<text style="color:red;">*</text></view><view class="flex-y-center"><input type="text" name="address" placeholder="请输入商家详细地址" value="{{address}}"/></view></view><view class="apply_item"><view>生产地址</view><view class="flex-y-center"><input type="text" name="production_address" placeholder="请输入生产地址(选填)" value="{{info.production_address}}"/></view></view><view class="apply_item"><view>实体店地址</view><view class="flex-y-center"><input type="text" name="shop_address" placeholder="请输入实体店地址(选填)" value="{{info.shop_address}}"/></view></view><view class="apply_item"><view>线上链接</view><view class="flex-y-center"><input type="text" name="shop_url" placeholder="请输入线上店铺链接(选填)" value="{{info.shop_url}}"/></view></view><input type="text" hidden="true" name="latitude" value="{{latitude}}"/><input type="text" hidden="true" name="longitude" value="{{longitude}}"/><view class="apply_item" style="line-height:50rpx;"><textarea name="content" placeholder="请输入商家简介" value="{{info.content}}"></textarea></view></view><view class="apply_box"><view class="apply_item" style="border-bottom:0;"><text>商家头像<text style="color:red;">*</text></text></view><view class="flex" style="flex-wrap:wrap;padding-bottom:20rpx;"><block wx:for="{{pic}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="layui-imgbox"><view class="layui-imgbox-close" data-index="{{index}}" data-field="pic" data-event-opts="{{[['tap',[['removeimg',['$event']]]]]}}" bindtap="__e"><image src="/static/img/ico-del.png"></image></view><view class="layui-imgbox-img"><image src="{{item}}" data-url="{{item}}" mode="widthFix" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image></view></view></block><block wx:if="{{$root.g0==0}}"><view class="uploadbtn" style="{{('background:url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 60rpx;background-size:80rpx 80rpx;background-color:#F3F3F3;')}}" data-field="pic" data-event-opts="{{[['tap',[['uploadimg',['$event']]]]]}}" bindtap="__e"></view></block></view><input type="text" hidden="true" name="pic" maxlength="-1" value="{{$root.g1}}"/></view><view class="apply_box"><view class="apply_item" style="border-bottom:0;"><text>主要产品图片(3-5张)<text style="color:red;">*</text></text></view><view class="flex" style="flex-wrap:wrap;padding-bottom:20rpx;"><block wx:for="{{pics}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="layui-imgbox"><view class="layui-imgbox-close" data-index="{{index}}" data-field="pics" data-event-opts="{{[['tap',[['removeimg',['$event']]]]]}}" bindtap="__e"><image src="/static/img/ico-del.png"></image></view><view class="layui-imgbox-img"><image src="{{item}}" data-url="{{item}}" mode="widthFix" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image></view></view></block><block wx:if="{{$root.g2<5}}"><view class="uploadbtn" style="{{('background:url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 60rpx;background-size:80rpx 80rpx;background-color:#F3F3F3;')}}" data-field="pics" data-event-opts="{{[['tap',[['uploadimg',['$event']]]]]}}" bindtap="__e"></view></block></view><input type="text" hidden="true" name="pics" maxlength="-1" value="{{$root.g3}}"/></view><view class="apply_box"><view class="apply_item" style="border-bottom:0;"><text>商家身份证正反面，营业执照，及相关资质<text style="color:red;">*</text></text></view><view class="flex" style="flex-wrap:wrap;padding-bottom:20rpx;"><block wx:for="{{zhengming}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="layui-imgbox"><view class="layui-imgbox-close" data-index="{{index}}" data-field="zhengming" data-event-opts="{{[['tap',[['removeimg',['$event']]]]]}}" bindtap="__e"><image src="/static/img/ico-del.png"></image></view><view class="layui-imgbox-img"><image src="{{item}}" data-url="{{item}}" mode="widthFix" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image></view></view></block><view class="uploadbtn" style="{{('background:url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 60rpx;background-size:80rpx 80rpx;background-color:#F3F3F3;')}}" data-field="zhengming" data-event-opts="{{[['tap',[['uploadimg',['$event']]]]]}}" bindtap="__e"></view></view><input type="text" hidden="true" name="zhengming" maxlength="-1" value="{{$root.g4}}"/></view><view class="apply_box"><view class="apply_item"><text>登录用户名<text style="color:red;">*</text></text><view class="flex-y-center"><input type="text" name="un" placeholder="请填写登录账号" autocomplete="off" value="{{info.un}}"/></view></view><view class="apply_item"><text>登录密码<text style="color:red;">*</text></text><view class="flex-y-center"><input type="password" name="pwd" placeholder="请填写登录密码" autocomplete="off" value="{{info.pwd}}"/></view></view><view class="apply_item"><text>确认密码<text style="color:red;">*</text></text><view class="flex-y-center"><input type="password" name="repwd" placeholder="请再次填写密码" value="{{info.repwd}}"/></view></view></view><block wx:if="{{bset.xieyi_show==1}}"><block><block wx:if="{{!info.id||info.status==2}}"><view class="flex-y-center" style="margin-left:20rpx;color:#999;"><checkbox-group data-event-opts="{{[['change',[['isagreeChange',['$event']]]]]}}" bindchange="__e"><label class="flex-y-center"><checkbox value="1" checked="{{isagree}}"></checkbox>阅读并同意</label></checkbox-group><text data-event-opts="{{[['tap',[['showxieyiFun',['$event']]]]]}}" style="color:#666;" bindtap="__e">《商户入驻协议》</text></view></block></block></block><view style="padding:30rpx 0;"><block wx:if="{{!info.id||info.status==2}}"><button class="set-btn" style="{{('background:linear-gradient(90deg,'+$root.m0+' 0%,rgba('+$root.m1+',0.8) 100%)')}}" form-type="submit">提交申请</button></block></view></form><view class="content"><view data-event-opts="{{[['tap',[['goBack',['$event']]]]]}}" class="back-button" bindtap="__e"><text class="t1">返回</text></view></view><view style="width:100%;height:100%;position:fixed;top:0;left:0;z-index:99;background:rgba(0,0,0,0.7);" id="xieyi" hidden="{{!showxieyi}}"><view style="width:90%;margin:0 auto;height:85%;margin-top:10%;background:#fff;color:#333;padding:5px 10px 50px 10px;position:relative;border-radius:2px;"><view style="overflow:scroll;height:100%;"><parse vue-id="08e644e4-5" content="{{bset.xieyi}}" bind:__l="__l"></parse></view><view data-event-opts="{{[['tap',[['hidexieyi',['$event']]]]]}}" style="position:absolute;z-index:9999;bottom:10px;left:0;right:0;margin:0 auto;text-align:center;" bindtap="__e">已阅读并同意</view></view></view></block></block><block wx:if="{{loading}}"><loading vue-id="08e644e4-6" bind:__l="__l"></loading></block><dp-tabbar vue-id="08e644e4-7" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="08e644e4-8" data-ref="popmsg" bind:__l="__l"></popmsg></view>