<view class="dp-cube" style="{{'background:'+(params.bgcolor)+';'+('height:'+(params.maxheight*187.5+'rpx')+';')+('margin:'+(params.margin_y/340*750+'rpx '+params.margin_x/340*750+'rpx 0')+';')+('padding:'+(params.padding_y/340*750+'rpx '+params.padding_x/340*750+'rpx')+';')}}"><block wx:for="{{params.layout}}" wx:for-item="row" wx:for-index="idx"><block><block wx:for="{{row}}" wx:for-item="col" wx:for-index="idx2"><block><block wx:if="{{!col.isempty}}"><view style="{{'position:'+('absolute')+';'+('display:'+('flex')+';')+('align-items:'+('center')+';')+('top:'+(idx*(750-params.margin_y/340*750*2-params.padding_y/340*750*2)/4+params.padding_y/340*750*1+'rpx')+';')+('left:'+(idx2*(750-params.margin_x/340*750*2-params.padding_x/340*750*2)/4+params.padding_x/340*750*1+'rpx')+';')+('width:'+(col.cols*(750-params.margin_x/340*750*2-params.padding_x/340*750*2)/4+'rpx')+';')+('height:'+(col.rows*(750-params.margin_y/340*750*2-params.padding_y/340*750*2)/4+'rpx')+';')+('line-height:'+(col.rows*(750-params.margin_y/340*750*2-params.padding_y/340*750*2)/4+'rpx')+';')}}" data-url="{{col.hrefurl}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image style="width:100%;max-height:100%;" src="{{col.imgurl}}" mode="widthFix"></image></view></block></block></block></block></block></view>