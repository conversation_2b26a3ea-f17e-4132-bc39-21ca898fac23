<view class="dp-cover"><button class="dp-cover-cover" style="{{'z-index:'+(10)+';'+('top:'+(params.top+'vh')+';')+('left:'+(params.left+'vw')+';')+('color:'+(params.color)+';')+('background-color:'+(params.bgcolor)+';')+('width:'+(params.width*2.2+'rpx')+';')+('height:'+(params.height*2.2+'rpx')+';')+('margin:'+(params.margin_y*2.2+'rpx '+params.margin_x*2.2+'rpx')+';')+('padding:'+(params.padding_y*2.2+'rpx '+params.padding_x*2.2+'rpx')+';')+('font-size:'+(params.fontsize*2.2+'rpx')+';')+('border:'+(params.border*2.2+'rpx solid '+params.bordercolor)+';')+('border-radius:'+(params.radius*2.2+'rpx')+';')}}" data-url="{{params.hrefurl}}" open-type="{{params.hrefurl=='contact::'?'contact':params.hrefurl=='share::'?'share':''}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><block wx:if="{{params.style==1}}"><text style="padding:0 4rpx;">{{params.text}}</text></block><block wx:if="{{params.style==2}}"><image style="{{'width:'+(params.picwidth*2.2+'rpx')+';'+('height:'+(params.picheight*2.2+'rpx')+';')}}" src="{{params.pic}}"></image></block></button></view>