<view style="width:100%;"><view class="dp-product-zhaopin"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="id"><view class="job-card" data-url="{{'/zhaopin/partdetails?id='+item.$orig[zwid]}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><block wx:if="{{item.$orig.apply_tag}}"><view class="card-desc"><view class="desc-gradient"></view><view class="desc-content"><image src="/static/img/verify.png"></image><text class="desc-text ellipsis">担保企业</text></view></view></block><view class="card-header"><text class="job-title ellipsis" style="{{'color:'+($root.m0)+';'}}">{{item.$orig.title}}</text><view class="job-salary"><text class="salary-num" style="{{'color:'+($root.m1)+';'}}">{{item.$orig.salary}}</text></view><view class="collect-btn"><image class="collect-icon" src="{{item.$orig.is_favorite?'/static/img/star2.png':'/static/img/star.png'}}"></image></view></view><view class="tag-list"><block wx:if="{{item.$orig.descripti}}"><view class="tag-item">{{item.$orig.descripti}}</view></block><block wx:if="{{item.$orig.numbers}}"><view class="tag-item">{{item.$orig.numbers+"人"}}</view></block><block wx:if="{{item.$orig.experience}}"><view class="tag-item">{{item.$orig.experience}}</view></block><block wx:if="{{item.$orig.education}}"><view class="tag-item">{{item.$orig.education}}</view></block></view><block wx:if="{{showaddress&&item.$orig.work_address}}"><view class="location-info"><image class="location-icon" src="../../static/img/address3.png"></image><text class="address ellipsis">{{item.$orig.work_address}}</text></view></block><view class="company-info"><view class="company-left"><view class="company-logo"><block wx:if="{{item.$orig.company_logo}}"><image src="{{item.$orig.company_logo}}" mode="aspectFit"></image></block><block wx:else><image src="/static/img/default-company-logo.png" mode="aspectFit"></image></block></view><text class="company-name ellipsis">{{item.$orig.company_name}}</text></view><view class="apply-btn" style="{{'background:'+($root.m2)+';'}}">立即报名</view></view><block wx:if="{{item.g0}}"><view class="welfare-list"><block wx:for="{{item.$orig.welfare}}" wx:for-item="wf" wx:for-index="wk" wx:key="wk"><view class="tag-item">{{wf}}</view></block></view></block></view></block></view></view>