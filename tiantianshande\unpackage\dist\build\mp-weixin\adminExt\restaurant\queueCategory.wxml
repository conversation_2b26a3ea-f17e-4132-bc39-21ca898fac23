<view><view class="container" id="datalist"><view><view class="info-item"><view class="t1">队列名称</view><view class="t2">前缀</view><view class="t2">座位数</view><view class="t2">排序</view></view><block wx:for="{{datalist}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="info-item"><view class="t1">{{item.name}}<block wx:if="{{item.status==0}}"><text style="color:#DBAA83;">(隐藏)</text></block></view><view class="t2">{{item.code}}</view><view class="t2">{{item.seat_min+"-"+item.seat_max}}</view><view class="t2">{{item.sort}}</view><image class="t3" data-url="{{'queueCategoryEdit?id='+item.id}}" src="{{pre_url+'/static/img/arrowright.png'}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"></image></view></block></view><view style="margin-top:40rpx;"><view style="text-align:center;">排队开关：<block wx:if="{{set.status==1}}"><text style="color:#008000;">开启</text></block><block wx:else><text style="color:#CA2428;">关闭</text></block></view><block wx:if="{{set.status==1}}"><button class="btn1" style="{{('background:linear-gradient(90deg,'+$root.m0+' 0%,rgba('+$root.m1+',0.8) 100%)')}}" data-st="0" data-event-opts="{{[['tap',[['setst',['$event']]]]]}}" bindtap="__e">关闭</button></block><block wx:else><button class="btn1" style="{{('background:linear-gradient(90deg,'+$root.m2+' 0%,rgba('+$root.m3+',0.8) 100%)')}}" data-st="1" data-event-opts="{{[['tap',[['setst',['$event']]]]]}}" bindtap="__e">开启</button></block></view><button class="savebtn" style="{{('background:linear-gradient(90deg,'+$root.m4+' 0%,rgba('+$root.m5+',0.8) 100%)')}}" data-url="queueCategoryEdit" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">添加</button></view><block wx:if="{{nomore}}"><nomore vue-id="69bb27ac-1" bind:__l="__l"></nomore></block><block wx:if="{{nodata}}"><nodata vue-id="69bb27ac-2" bind:__l="__l"></nodata></block></view>