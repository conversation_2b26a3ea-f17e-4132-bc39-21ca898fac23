<view class="container"><block wx:if="{{isload}}"><block><dd-tab vue-id="13960dcc-1" itemdata="{{[$root.m0+'明细']}}" itemst="{{['14']}}" st="{{st}}" isfixed="{{true}}" data-event-opts="{{[['^changetab',[['changetab']]]]}}" bind:changetab="__e" bind:__l="__l"></dd-tab><view class="content"><block wx:if="{{st==14}}"><block><block wx:for="{{datalist}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="item"><view class="f1"><text class="t1">{{item.remark}}</text><text class="t2">{{item.createtime}}</text></view><view class="f2"><block wx:if="{{item.commission>0}}"><text class="t1">{{"+"+item.commission}}</text></block><block wx:else><text class="t2">{{item.commission}}</text></block></view></view></block></block></block></view><block wx:if="{{nomore}}"><nomore vue-id="13960dcc-2" bind:__l="__l"></nomore></block><block wx:if="{{nodata}}"><nodata vue-id="13960dcc-3" bind:__l="__l"></nodata></block></block></block><block wx:if="{{loading}}"><loading vue-id="13960dcc-4" bind:__l="__l"></loading></block><dp-tabbar vue-id="13960dcc-5" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="13960dcc-6" data-ref="popmsg" bind:__l="__l"></popmsg></view>