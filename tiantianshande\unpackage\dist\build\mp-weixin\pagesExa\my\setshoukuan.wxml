<view class="container"><block wx:if="{{isload}}"><block><view class="content"><view class="info-item" style="height:136rpx;line-height:136rpx;"><view class="t1" style="flex:1;">微信二维码</view><image style="width:88rpx;height:88rpx;" src="{{userinfo.wximg}}" data-event-opts="{{[['tap',[['uploadwximg',['$event']]]]]}}" bindtap="__e"></image><image class="t3" src="/static/img/arrowright.png"></image></view><view class="info-item" style="height:156rpx;line-height:156rpx;margin-right:100rpx;"><view class="t1" style="flex:1;">支付宝二维码</view><image style="width:88rpx;height:88rpx;" src="{{userinfo.zfbimg}}" data-event-opts="{{[['tap',[['uploadzfbimg',['$event']]]]]}}" bindtap="__e"></image><image class="t3" src="/static/img/arrowright.png"></image></view><view class="info-item" data-url="setaliaccount" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="t1">支付宝账号</view><view class="t2">{{userinfo.aliaccount}}</view><image class="t3" src="/static/img/arrowright.png"></image></view><view class="info-item" data-url="setbankinfo" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><text class="t1">银行卡</text><text class="t2">{{userinfo.bankname?'已设置':''}}</text><image class="t3" src="/static/img/arrowright.png"></image></view></view><view class="content"><view data-event-opts="{{[['tap',[['fanhui',['$event']]]]]}}" class="info-item" bindtap="__e"><view class="t1" style="background-color:red;color:#fff;width:100%;text-align:center;border-radius:10rpx;">返回</view></view></view></block></block><block wx:if="{{loading}}"><loading vue-id="22d0720e-1" bind:__l="__l"></loading></block><dp-tabbar vue-id="22d0720e-2" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="22d0720e-3" data-ref="popmsg" bind:__l="__l"></popmsg></view>