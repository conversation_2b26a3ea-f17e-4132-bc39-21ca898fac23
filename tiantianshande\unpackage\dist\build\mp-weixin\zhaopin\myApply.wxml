<view><block wx:if="{{isload}}"><block><view class="apply-page"><scroll-view class="filter-bar" scroll-x="{{true}}" scroll-with-animation="{{true}}" scroll-into-view="{{'tab-'+currentTab}}"><block wx:for="{{$root.l0}}" wx:for-item="tab" wx:for-index="__i0__" wx:key="$orig"><view class="{{['filter-item',(currentTab===tab.$orig)?'active':'']}}" style="{{'background:'+(currentTab===tab.$orig?'linear-gradient(135deg, '+tab.m0+', '+tab.m1+'dd)':'#f8f9fa')+';'+('color:'+(currentTab===tab.$orig?'#fff':'#666')+';')+('box-shadow:'+(currentTab===tab.$orig?'0 4rpx 12rpx rgba('+tab.m2+', 0.2)':'0 2rpx 6rpx rgba(0, 0, 0, 0.02)')+';')+('border:'+(currentTab===tab.$orig?'1rpx solid rgba('+tab.m3+', 0.1)':'1rpx solid rgba(0, 0, 0, 0.03)')+';')}}" id="{{'tab-'+tab.$orig}}" data-event-opts="{{[['tap',[['switchTab',['$0'],[[['statusList','',__i0__]]]]]]]}}" bindtap="__e">{{''+(tab.$orig==='all'?'全部':tab.$orig==='1'?'已投递':tab.$orig==='2'?'已查看':tab.$orig==='3'?'初筛通过':tab.$orig==='4'?'待面试':tab.$orig==='5'?'面试通过':tab.$orig==='6'?'已入职':'已结束')+''}}</view></block></scroll-view><swiper class="swiper-content" style="{{'height:'+('calc(100vh - 120rpx)')+';'}}" current="{{swiperCurrent}}" data-event-opts="{{[['change',[['swiperChange',['$event']]]]]}}" bindchange="__e"><block wx:for="{{$root.l2}}" wx:for-item="status" wx:for-index="index" wx:key="index"><swiper-item><scroll-view class="job-list" scroll-y="{{true}}" refresher-enabled="{{true}}" refresher-triggered="{{isRefreshing}}" data-event-opts="{{[['scrolltolower',[['loadMore',['$event']]]],['refresherrefresh',[['onRefresh',['$event']]]]]}}" bindscrolltolower="__e" bindrefresherrefresh="__e"><block wx:if="{{status.g0>0}}"><block><block wx:for="{{status.l1}}" wx:for-item="job" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" data-event-params="{{({job:job.$orig})}}" class="job-card" bindtap="__e"><view class="job-info"><view class="job-header"><text class="job-title">{{job.$orig.title}}</text><text class="job-salary">{{job.$orig.salary}}</text></view><view class="company-info"><image class="company-logo" src="{{job.$orig.companyLogo}}" mode="aspectFit"></image><text class="company-name">{{job.$orig.companyName}}</text><text class="apply-time">{{job.$orig.applyTime}}</text></view></view><view class="job-footer"><view class="job-tags"><block wx:for="{{job.$orig.tags}}" wx:for-item="tag" wx:for-index="tagIndex" wx:key="tagIndex"><text class="tag">{{''+tag+''}}</text></block></view><view class="{{['status-tag',job.$orig.status]}}">{{''+job.m4+''}}</view></view></view></block></block></block><block wx:else><view class="empty-state"><image class="empty-icon" src="/static/icons/empty-apply.png" mode="aspectFit"></image><text class="empty-text">暂无投递记录</text><button data-event-opts="{{[['tap',[['goToJobList',['$event']]]]]}}" class="browse-btn" style="{{'background:'+('linear-gradient(135deg, '+status.m5+', '+status.m6+'dd)')+';'+('box-shadow:'+('0 8rpx 20rpx rgba('+status.m7+', 0.25)')+';')}}" bindtap="__e">去浏览职位</button></view></block><block wx:if="{{status.g1}}"><view class="loading-more">正在加载更多...</view></block><block wx:if="{{status.g2}}"><view class="no-more-data">没有更多数据了</view></block></scroll-view></swiper-item></block></swiper></view></block></block><block wx:if="{{loading}}"><loading vue-id="7c909498-1" bind:__l="__l"></loading></block><dp-tabbar vue-id="7c909498-2" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="7c909498-3" data-ref="popmsg" bind:__l="__l"></popmsg></view>