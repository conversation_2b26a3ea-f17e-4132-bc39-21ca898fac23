<block wx:if="{{isload}}"><view class="page" style="{{'height:'+(pageHeight+'px')+';'}}"><swiper class="swiper" style="{{'height:'+(pageHeight+'px')+';'}}" circular="{{circular}}" vertical="{{true}}" data-event-opts="{{[['change',[['onSwiperChange',['$event']]]]]}}" bindchange="__e"><block wx:for="{{videoList}}" wx:for-item="item" wx:for-index="__i0__" wx:key="id"><swiper-item><video class="video vue-ref-in-for" style="{{'height:'+(pageHeight+'px')+';'}}" id="{{item.id}}" src="{{item.src}}" controls="{{false}}" loop="{{true}}" show-center-play-btn="{{false}}" data-ref="{{item.id}}" data-event-opts="{{[['tap',[['playpause',['$event']]]],['play',[['onplay',['$event']]]]]}}" bindtap="__e" bindplay="__e"></video></swiper-item></block></swiper><block wx:if="{{!isalipay}}"><view data-event-opts="{{[['tap',[['goback',['$event']]]]]}}" class="goback" style="{{'top:'+(gobacktopHeight+'px')+';'}}" bindtap="__e"><image class="goback-img" src="{{pre_url+'/static/img/goback.png'}}"></image></view></block><block wx:if="{{!isplayst}}"><view data-event-opts="{{[['tap',[['playClick',['$event']]]]]}}" class="playbox" catchtap="__e"><image class="playbox-img" src="{{pre_url+'/static/img/shortvideo_playnum.png'}}"></image></view></block><view class="logo" data-url="{{videoDataList[_videoDataIndex].bid==0?'/pages/index/index':'/pages/business/index?id='+videoDataList[_videoDataIndex].bid}}" data-event-opts="{{[['tap',[['gotourl',['$event']]]]]}}" bindtap="__e"><image class="logo-img" src="{{videoDataList[_videoDataIndex].logo}}"></image></view><view class="viewnum"><image class="viewnum-img" src="{{pre_url+'/static/img/shortvideo_view.png'}}"></image><text class="viewnum-txt">{{videoDataList[_videoDataIndex].view_num}}</text></view><view data-event-opts="{{[['tap',[['dozan',['$event']]]]]}}" class="likenum" bindtap="__e"><image class="likenum-img" src="{{videoDataList[_videoDataIndex].iszan?pre_url+'/static/img/shortvideo_like2.png':pre_url+'/static/img/shortvideo_like.png'}}"></image><text class="likenum-txt">{{videoDataList[_videoDataIndex].zan_num}}</text></view><block wx:if="{{videoDataList[_videoDataIndex].comment==1}}"><view data-event-opts="{{[['tap',[['commentClick',['$event']]]]]}}" class="commentnum" bindtap="__e"><image class="commentnum-img" src="{{pre_url+'/static/img/shortvideo_comment.png'}}"></image><text class="commentnum-txt">{{videoDataList[_videoDataIndex].commentnum}}</text></view></block><view data-event-opts="{{[['tap',[['shareClick',['$event']]]]]}}" class="sharenum" bindtap="__e"><image class="sharenum-img" src="{{pre_url+'/static/img/shortvideo_share.png'}}"></image></view><block wx:if="{{videoDataList[_videoDataIndex].linkurl}}"><view class="linkurl" data-url="{{videoDataList[_videoDataIndex].linkurl}}" data-event-opts="{{[['tap',[['gotourl',['$event']]]]]}}" bindtap="__e"><text class="linkurl-txt">{{videoDataList[_videoDataIndex].linkname}}</text><image class="linkurl-img" src="{{pre_url+'/static/img/shortvideo_arrowright.png'}}"></image></view></block><block wx:if="{{videoDataList[_videoDataIndex].pronum>0}}"><view data-event-opts="{{[['tap',[['proboxClick',['$event']]]]]}}" class="cart" style="{{(menuindex>-1?'bottom:150rpx':'')}}" bindtap="__e"><image class="cart-img" src="{{pre_url+'/static/img/shortvideo_cart.png'}}"></image><text class="cart-txt">{{videoDataList[_videoDataIndex].pronum}}</text></view></block><block wx:if="{{!videoDataList[_videoDataIndex].linkurl&&prodialogshow&&videoDataList[_videoDataIndex].proid>0}}"><view class="prodialog" style="{{(menuindex>-1?'bottom:230rpx':'')}}" data-url="{{'/shopPackage/shop/product?id='+videoDataList[_videoDataIndex].proid}}" data-event-opts="{{[['tap',[['gotourl',['$event']]]]]}}" bindtap="__e"><image class="prodialog-bgimg" src="{{pre_url+'/static/img/shortvideo_probg.png'}}"></image><view class="prodialog-content"><image class="prodialog-content-img" src="{{videoDataList[_videoDataIndex].propic}}"></image><view class="prodialog-content-info"><text class="prodialog-content-name">{{videoDataList[_videoDataIndex].proname}}</text><text class="prodialog-content-sales">{{videoDataList[_videoDataIndex].prosales+"人购买"}}</text><text class="prodialog-content-price">{{"￥"+videoDataList[_videoDataIndex].prosell_price}}</text></view></view><image class="prodialog-close" src="{{pre_url+'/static/img/close.png'}}" data-event-opts="{{[['tap',[['prodialogClose',['$event']]]]]}}" catchtap="__e"></image></view></block><view class="title" style="{{(menuindex>-1?'bottom:270rpx':'')}}"><text class="title-txt">{{videoDataList[_videoDataIndex].name}}</text></view><view class="description" style="{{(menuindex>-1?'bottom:170rpx':'')}}"><text class="description-txt">{{videoDataList[_videoDataIndex].description}}</text></view><block wx:if="{{videoDataList[_videoDataIndex].songjifen&&videoDataList[_videoDataIndex].songjifen!='0'}}"><view class="jifenbox" style="{{(menuindex>-1?'bottom:120rpx':'')}}"><image class="jifenbox-icon" src="{{pre_url+'/static/img/kanjia-hb.png'}}"></image><text class="jifenbox-txt">{{"观看可得"+videoDataList[_videoDataIndex].songjifen+"积分"}}</text></view></block><block wx:if="{{loading}}"><loading vue-id="7def6912-1" bind:__l="__l"></loading></block><dp-tabbar vue-id="7def6912-2" opt="{{opt}}" data-event-opts="{{[['^getmenuindex',[['getmenuindex']]]]}}" bind:getmenuindex="__e" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="7def6912-3" data-ref="popmsg" bind:__l="__l"></popmsg><block wx:if="{{sharetypevisible}}"><view class="popupshare__content" style="{{(menuindex>-1?'bottom:110rpx':'')}}"><image class="popupshare__close2" src="{{pre_url+'/static/img/close.png'}}" data-event-opts="{{[['tap',[['handleClickMask',['$event']]]]]}}" catchtap="__e"></image><view class="sharetypecontent"><block wx:if="{{platform=='app'}}"><view data-event-opts="{{[['tap',[['shareapp',['$event']]]]]}}" class="sharetypecontent-f1" bindtap="__e"><image class="sharetypecontent-f1-img" src="{{pre_url+'/static/img/weixin.png'}}"></image><text class="sharetypecontent-f1-t1">分享给好友</text></view></block><block wx:else><block wx:if="{{platform=='mp'}}"><view data-event-opts="{{[['tap',[['sharemp',['$event']]]]]}}" class="sharetypecontent-f1" bindtap="__e"><image class="sharetypecontent-f1-img" src="{{pre_url+'/static/img/weixin.png'}}"></image><text class="sharetypecontent-f1-t1">分享给好友</text></view></block><block wx:else><block wx:if="{{platform=='h5'}}"><view data-event-opts="{{[['tap',[['sharemp',['$event']]]]]}}" class="sharetypecontent-f1" bindtap="__e"><image class="sharetypecontent-f1-img" src="{{pre_url+'/static/img/weixin.png'}}"></image><text class="sharetypecontent-f1-t1">分享给好友</text></view></block><block wx:else><button class="sharetypecontent-f1" open-type="share"><image class="sharetypecontent-f1-img" src="{{pre_url+'/static/img/weixin.png'}}"></image><text class="sharetypecontent-f1-t1">分享给好友</text></button></block></block></block><view data-event-opts="{{[['tap',[['showPoster',['$event']]]]]}}" class="sharetypecontent-f2" bindtap="__e"><image class="sharetypecontent-f2-img" src="{{pre_url+'/static/img/sharepic.png'}}"></image><text class="sharetypecontent-f2-t1">生成分享图片</text></view></view></view></block><block wx:if="{{showposter}}"><view class="posterDialog"><view class="posterDialog-main"><view data-event-opts="{{[['tap',[['posterDialogClose',['$event']]]]]}}" class="posterDialog-close" bindtap="__e"><image class="posterDialog-img" src="{{pre_url+'/static/img/close.png'}}"></image></view><view class="posterDialog-content"><image class="posterDialog-content-img" src="{{posterpic}}" mode="widthFix" data-url="{{posterpic}}" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image></view></view></view></block><block wx:if="{{showcomment}}"><view class="plbox" style="{{(menuindex>-1?'bottom:110rpx':'bottom:0rpx')}}"><view class="plbox_title"><text class="plbox_title-t1">评论</text><text class="plbox_title-t2">{{"("+videoDataList[_videoDataIndex].commentnum+")"}}</text></view><image class="plbox-close" src="{{pre_url+'/static/img/close.png'}}" data-event-opts="{{[['tap',[['commentClick',['$event']]]]]}}" catchtap="__e"></image><scroll-view class="plbox_content" scroll-y="true" data-event-opts="{{[['scrolltolower',[['getmorecomment',['$event']]]]]}}" bindscrolltolower="__e"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="idx" wx:key="id"><block><view class="plbox_content-item"><view class="plbox_content-item-f1 flex0"><image class="plbox_content-item-f1-img" src="{{item.$orig.headimg}}"></image></view><view class="plbox_content-item-f2 flex-col"><text class="plbox_content-item-f2-t1">{{item.$orig.nickname}}</text><view class="plbox_content-item-f2-t2 plbox_content-plcontent"><block wx:for="{{item.$orig.content}}" wx:for-item="con" wx:for-index="__i1__"><block><block wx:if="{{con.type=='text'}}"><text class="plbox_content-plcontent-text">{{con.content}}</text></block><block wx:if="{{con.type=='image'}}"><image class="plbox_content-plcontent-image" src="{{con.content}}"></image></block></block></block></view><block wx:if="{{item.g0>0}}"><block><view class="plbox_content-relist"><block wx:for="{{item.$orig.replylist}}" wx:for-item="hfitem" wx:for-index="index" wx:key="index"><block><view class="plbox_content-relist-item2"><view class="flex-row"><image class="plbox_content-relist-headimg" src="{{hfitem.headimg}}"></image><text class="plbox_content-relist-nickname">{{hfitem.nickname}}</text></view><view class="plbox_content-relist-f2 plbox_content-plcontent"><block wx:for="{{hfitem.content}}" wx:for-item="con" wx:for-index="__i2__"><block><block wx:if="{{con.type=='text'}}"><text class="plbox_content-plcontent-text">{{con.content}}</text></block><block wx:if="{{con.type=='image'}}"><image class="plbox_content-plcontent-image" src="{{con.content}}"></image></block></block></block></view></view></block></block></view></block></block><view class="plbox_content-item-f2-t3"><text class="plbox_content-item-f2-t3-x1">{{item.$orig.createtime}}</text><view class="plbox_content-item-f2-t3-x2"><text class="plbox_content-item-f2-phuifu" data-id="{{item.$orig.id}}" data-nickname="{{item.$orig.nickname}}" data-event-opts="{{[['tap',[['replyClick',['$event']]]]]}}" bindtap="__e">回复</text></view><view class="plbox_content-item-f2-pzan" data-id="{{item.$orig.id}}" data-index="{{idx}}" data-event-opts="{{[['tap',[['pzan',['$event']]]]]}}" bindtap="__e"><image class="plbox_content-item-f2-pzan-img" src="{{pre_url+'/static/img/zan-'+(item.$orig.iszan==1?'2':'1')+'.png'}}"></image><text class="plbox_content-item-f2-pzan-txt">{{item.$orig.zan}}</text></view></view></view></view></block></block><block wx:if="{{nomore}}"><view><text class="comment-nomore">没有更多数据了</text></view></block><block wx:if="{{nodata}}"><view><text class="comment-nodata">暂无评论!</text></view></block></scroll-view><block wx:if="{{loading}}"><loading vue-id="7def6912-4" bind:__l="__l"></loading></block><view style="height:160rpx;"></view><block wx:if="{{hfid!=0}}"><view data-event-opts="{{[['tap',[['replyshadowClick',['$event']]]]]}}" class="plbox-replyshadow" catchtap="__e"></view></block><view class="pinglun"><image class="pinglun-faceicon" src="{{pre_url+'/static/img/face-icon.png'}}" data-event-opts="{{[['tap',[['toggleFaceBox',['$event']]]]]}}" bindtap="__e"></image><input class="pinglun-input" confirmHold="true" confirmType="send" cursorSpacing="20" type="text" placeholder="{{messageholder}}" data-event-opts="{{[['confirm',[['sendMessage',['$event']]]],['focus',[['onInputFocus',['$event']]]],['input',[['messageChange',['$event']]]]]}}" value="{{message}}" bindconfirm="__e" bindfocus="__e" bindinput="__e"/><view data-event-opts="{{[['tap',[['sendcomment',['$event']]]]]}}" class="pinglun-buybtn" bindtap="__e"><text class="pinglun-buybtn-txt">发表</text></view></view><block wx:if="{{faceshow}}"><wxface bind:selectface="__e" vue-id="7def6912-5" data-event-opts="{{[['^selectface',[['selectface']]]]}}" bind:__l="__l"></wxface></block></view></block><block wx:if="{{showproduct}}"><view class="probox" style="{{(menuindex>-1?'bottom:110rpx':'bottom:0rpx')}}"><view class="probox_title"><text class="probox_title-t1">全部商品</text><text class="plbox_title-t2">{{"("+videoDataList[_videoDataIndex].pronum+")"}}</text></view><image class="plbox-close" src="{{pre_url+'/static/img/close.png'}}" data-event-opts="{{[['tap',[['proboxClick',['$event']]]]]}}" catchtap="__e"></image><scroll-view class="probox_content" scroll-y="true"><block wx:for="{{prolist}}" wx:for-item="item" wx:for-index="idx" wx:key="id"><block><view class="probox_content-item" data-url="{{'/shopPackage/shop/product?id='+item.id}}" data-event-opts="{{[['tap',[['gotourl',['$event']]]]]}}" bindtap="__e"><image class="probox_content-item-img" src="{{item.pic}}"></image><view class="probox_content-item-info"><text class="probox_content-item-name">{{item.name}}</text><text class="probox_content-item-sales">{{item.sales+"人购买"}}</text><text class="probox_content-item-price">{{"￥"+item.sell_price}}</text></view><view class="probox_content-item-btn"><text class="probox_content-item-btn-txt">立即抢购</text></view></view></block></block></scroll-view></view></block></view></block>