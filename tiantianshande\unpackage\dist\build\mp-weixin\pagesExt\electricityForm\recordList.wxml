<view class="container"><block wx:if="{{isload}}"><block><dd-tab vue-id="9c70b2ae-1" itemdata="{{['全部','待回复','已回复']}}" itemst="{{['1','2','3']}}" st="{{st}}" isfixed="{{true}}" data-event-opts="{{[['^changetab',[['changetab']]]]}}" bind:changetab="__e" bind:__l="__l"></dd-tab><view style="width:100%;height:100rpx;"></view><view class="order-content"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><view class="order-box" data-url="{{'orderdetail?id='+item.$orig.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e"><view class="head"><text class="flex1">{{"编号："+item.$orig.ordernum}}</text><block wx:if="{{item.$orig.already_replied==0}}"><text class="st0">待回复</text></block><block wx:if="{{item.$orig.already_replied==1}}"><text class="st3">已回复</text></block></view><view class="content" style="border-bottom:none;"><view class="detail"><text class="t1">{{item.$orig.title}}</text></view></view><view class="bottom"><text>{{item.$orig.createtime}}</text></view><view class="op"><block wx:if="{{item.g0}}"><block><view class="btn2" data-url="{{'/pagesExt/order/invoice?type=lucky_collage&orderid='+item.$orig.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">发票</view></block></block><view class="btn2" data-url="{{'recordDetail?id='+item.$orig.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e">详情</view></view></view></block></block></view><block wx:if="{{nomore}}"><nomore vue-id="9c70b2ae-2" bind:__l="__l"></nomore></block><block wx:if="{{nodata}}"><nodata vue-id="9c70b2ae-3" bind:__l="__l"></nodata></block></block></block><block wx:if="{{loading}}"><loading vue-id="9c70b2ae-4" bind:__l="__l"></loading></block><dp-tabbar vue-id="9c70b2ae-5" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="9c70b2ae-6" data-ref="popmsg" bind:__l="__l"></popmsg><uni-popup class="vue-ref" vue-id="9c70b2ae-7" type="center" data-ref="more_one" bind:__l="__l" vue-slots="{{['default']}}"><uni-popup-dialog vue-id="{{('9c70b2ae-8')+','+('9c70b2ae-7')}}" mode="input" message="成功消息" duration="{{2000}}" valueType="number" before-close="{{true}}" data-event-opts="{{[['^close',[['close']]],['^confirm',[['confirm']]]]}}" bind:close="__e" bind:confirm="__e" bind:__l="__l"></uni-popup-dialog></uni-popup></view>