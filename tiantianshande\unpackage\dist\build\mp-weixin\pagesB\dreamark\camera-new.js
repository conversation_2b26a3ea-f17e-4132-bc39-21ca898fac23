require('../common/vendor.js');(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pagesB/dreamark/camera-new"],{"0098":function(e,o,t){"use strict";(function(e){Object.defineProperty(o,"__esModule",{value:!0}),o.default=void 0;var t={data:function(){return{currentStep:"camera",currentStatus:"准备拍照",showCamera:!1,cameraReady:!1,cameraStatusText:"正在启动摄像头...",capturedImageUrl:"",predictedImageUrl:"",progressPercent:0,currentProcessStep:0,processingSteps:[{icon:"🔍",text:"面部识别"},{icon:"🧠",text:"AI分析"},{icon:"✨",text:"预测生成"}],showConfig:!1,showConfigTip:!1,genderIndex:-1,genderOptions:["请选择性别","男","女","其他"],userProfession:"",imageLoaded:!1,currentImageLoaded:!1,predictedImageLoaded:!1,processingTimer:null,progressTimer:null,pre_url:"",processingAudio:null,futureTalkEnabled:!1,futureTalkButtonText:"与未来对话"}},onLoad:function(){var e=this;console.log("=== 摄像头页面加载开始 ===");var o=getApp();this.pre_url=o.globalData.pre_url||"",setTimeout((function(){e.loadUserConfig(),e.initCamera(),e.checkDreamInspirationSettings()}),500)},onUnload:function(){this.clearTimers(),this.stopProcessingAudio()},methods:{loadUserConfig:function(){console.log("=== 开始加载用户配置 ===");try{var o=e.getStorageSync("user_gender"),t=e.getStorageSync("user_profession");if(o){var n=this.genderOptions.indexOf(o);n>0&&(this.genderIndex=n)}t&&(this.userProfession=t),console.log("加载已保存配置:",{gender:o,profession:t,genderIndex:this.genderIndex})}catch(r){console.error("加载配置失败:",r)}this.showConfigTip=!0,this.showConfig=!0,console.log("显示配置弹窗")},initCamera:function(){console.log("=== 开始初始化摄像头 ==="),this.showCamera=!0,this.cameraReady=!0,this.cameraStatusText="摄像头已就绪",console.log("摄像头状态设置完成")},onCameraReady:function(){this.cameraReady=!0,this.cameraStatusText="摄像头已就绪",this.currentStatus="准备拍照",console.log("摄像头初始化完成")},onCameraError:function(e){console.error("摄像头错误:",e),this.cameraStatusText="摄像头启动失败"},capturePhoto:function(){console.log("开始拍照"),this.uploadImage("camera")},chooseImage:function(){console.log("选择图片"),this.uploadImage("album")},uploadImage:function(){var o=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"album",t=this,n=getApp();e.showLoading({title:"正在上传图片...",mask:!0}),n.chooseImage((function(o){e.hideLoading(),o&&o.length>0?(t.capturedImageUrl=o[0],t.currentStep="preview",t.imageLoaded=!1,t.currentImageLoaded=!1,t.predictedImageLoaded=!1,console.log("图片上传成功:",o[0]),console.log("切换到预览模式，当前步骤:",t.currentStep),console.log("图片URL设置为:",t.capturedImageUrl),e.showToast({title:"图片上传成功",icon:"success"})):e.showToast({title:"图片上传失败",icon:"error"})}),1,"camera"===o?["camera"]:["album","camera"])},switchCamera:function(){console.log("切换摄像头")},retakePhoto:function(){this.currentStep="camera",this.capturedImageUrl="",this.predictedImageUrl="",this.imageLoaded=!1,this.currentImageLoaded=!1,this.predictedImageLoaded=!1},onImageLoad:function(){this.imageLoaded=!0,console.log("预览图片加载完成")},onImageError:function(o){console.error("预览图片加载失败:",o),e.showToast({title:"图片加载失败",icon:"none"})},onCurrentImageLoad:function(){this.currentImageLoaded=!0,console.log("当前图片加载完成")},onCurrentImageError:function(e){console.error("当前图片加载失败:",e),this.currentImageLoaded=!1},onPredictedImageLoad:function(){this.predictedImageLoaded=!0,console.log("预测图片加载完成")},onPredictedImageError:function(o){console.error("预测图片加载失败:",o),this.predictedImageLoaded=!1,e.showToast({title:"预测图片加载失败",icon:"none"})},startProcessing:function(){this.currentStep="processing",this.progressPercent=0,this.currentProcessStep=0,this.playProcessingAudio(),this.simulateAIProcessing()},playProcessingAudio:function(){var o=this;try{this.stopProcessingAudio(),this.processingAudio=e.createInnerAudioContext(),this.processingAudio.src=this.pre_url+"/static/MP3/耐心等待20秒.mp3",this.processingAudio.onPlay((function(){console.log("开始播放处理中音频")})),this.processingAudio.onEnded((function(){console.log("处理中音频播放完成"),o.processingAudio.destroy(),o.processingAudio=null})),this.processingAudio.onError((function(e){console.error("处理中音频播放错误:",e),o.processingAudio.destroy(),o.processingAudio=null})),this.processingAudio.play(),setTimeout((function(){o.stopProcessingAudio()}),12e3)}catch(t){console.error("播放处理中音频时发生错误:",t)}},stopProcessingAudio:function(){if(this.processingAudio)try{this.processingAudio.stop(),this.processingAudio.destroy(),this.processingAudio=null,console.log("处理中音频已停止")}catch(e){console.error("停止处理中音频时发生错误:",e)}},simulateAIProcessing:function(){var e=this,o=setInterval((function(){e.currentProcessStep<e.processingSteps.length-1&&e.currentProcessStep++}),1500);this.processingTimer=setInterval((function(){e.progressPercent+=1,e.progressPercent>=100&&(clearInterval(o),e.completeProcessing())}),50)},completeProcessing:function(){this.clearTimers(),this.stopProcessingAudio(),this.generateFutureImage()},generateFutureImage:function(){var o=this;this.predictedImageLoaded=!1;var t=e.getStorageSync("user_gender"),n=e.getStorageSync("user_profession");return console.log("检查用户配置:",{userGender:t,userProfession:n,capturedImageUrl:this.capturedImageUrl}),t&&n&&"未设置"!==t&&"未设置"!==n?this.capturedImageUrl?(console.log("配置检查通过，开始调用梦想启蒙API"),void this.callAIAPI()):(console.log("没有图片URL，无法调用API"),void e.showToast({title:"请先上传照片",icon:"none"})):(console.log("用户配置不完整，显示配置提醒"),void e.showModal({title:"配置提醒",content:"请先完成性别和职业配置，以便生成个性化的梦想图片",showCancel:!1,confirmText:"去配置",success:function(){o.showConfig=!0}}))},callAIAPI:function(){var o=this;console.log("=== 开始调用梦想启蒙API ===");var t=e.getStorageSync("user_gender")||"未设置",n=e.getStorageSync("user_profession")||"未设置",r="我是一个".concat(t,"，职业是").concat(n,"，希望在未来能够实现自己的梦想，变得更加优秀和成功。"),s={gender:t,dream_content:r,image_url:this.capturedImageUrl};console.log("API请求参数:",s),console.log("即将调用接口: ApiDreamInspiration/generateImage");var i=getApp();i.post("ApiDreamInspiration/generateImage",s,(function(t){if(console.log("API调用成功，返回结果:",t),1==t.code){var n=t.data.record_id;e.showLoading({title:"正在生成梦想图片...",mask:!0}),o.checkGenerationStatus(n)}else o.handleAPIError(t.msg||"生成请求失败")}),(function(e){console.error("API调用失败:",e),o.handleAPIError("网络请求失败，请检查网络连接")}))},checkGenerationStatus:function(o){var t=this,n=getApp(),r=0;(function s(){r++,console.log("轮询查询状态 - 第".concat(r,"次，记录ID: ").concat(o)),n.post("ApiDreamInspiration/checkGenerationStatus",{record_id:o},(function(n){if(console.log("状态查询结果:",n),1==n.code){var i=n.data;1==i.status?(e.hideLoading(),console.log("梦想图片生成成功，图片URL:",i.result_image),t.predictedImageUrl=i.result_image,t.currentStep="result",t.predictedImageLoaded=!1,t.$forceUpdate(),e.setStorageSync("current_dream_record_id",o),e.showToast({title:"梦想图片生成完成！",icon:"success"}),console.log("页面切换到结果展示，预测图片URL:",t.predictedImageUrl)):2==i.status?(e.hideLoading(),t.handleAPIError(i.error_msg||"图片生成失败")):0==i.status?r>=60?(e.hideLoading(),t.handleAPIError("生成超时，请稍后查看记录")):setTimeout(s,3e3):(e.hideLoading(),t.handleAPIError("未知的生成状态"))}else e.hideLoading(),t.handleAPIError(n.msg||"获取生成状态失败")}),(function(o){console.error("状态查询失败:",o),e.hideLoading(),t.handleAPIError("检查生成状态失败")}))})()},handleAPIError:function(o){var t=this;e.showModal({title:"预测失败",content:o+"，是否重试？",success:function(e){e.confirm?t.startProcessing():t.currentStep="preview"}})},startFutureChat:function(){if(console.log("开始与未来对话"),this.futureTalkEnabled){e.showLoading({title:"正在获取对话链接...",mask:!0});var o=getApp();o.post("ApiDreamInspiration/getFutureTalkUrl",{},(function(o){if(e.hideLoading(),console.log("获取与未来对话链接结果:",o),1==o.code){var t=o.data,n=t.url;if(n)if(console.log("准备跳转到链接:",n),n.startsWith("http://")||n.startsWith("https://")){var r="/pages/index/webView3?url="+encodeURIComponent(n);e.navigateTo({url:r,success:function(){console.log("跳转到WebView成功")},fail:function(o){console.error("跳转到WebView失败",o),e.showToast({title:"跳转失败，请稍后再试",icon:"none"})}})}else e.navigateTo({url:n,success:function(){console.log("跳转到内部页面成功")},fail:function(o){console.error("跳转到内部页面失败",o),e.showToast({title:"跳转失败，请稍后再试",icon:"none"})}});else e.showToast({title:"未配置对话链接",icon:"none"})}else e.showToast({title:o.msg||"获取对话链接失败",icon:"none"})}),(function(o){e.hideLoading(),console.error("获取与未来对话链接失败:",o),e.showToast({title:"网络请求失败，请检查网络连接",icon:"none"})}))}else e.showToast({title:"与未来对话功能未启用",icon:"none"})},getMyDreamRecords:function(){var e=getApp();e.post("ApiDreamInspiration/getMyRecords",{page:1,limit:10},(function(e){1==e.code?console.log("我的梦想记录:",e.data):console.error("获取记录失败:",e.msg)}),(function(e){console.error("获取记录失败:",e)}))},viewHistory:function(){this.getMyDreamRecords()},checkDreamInspirationSettings:function(){var o=this,t=getApp();t.post("ApiDreamInspiration/getSetting",{},(function(t){if(1==t.code){var n=t.data;if(!n.is_enabled)return void e.showModal({title:"功能未开启",content:"梦想启蒙功能暂未开启，请联系管理员",showCancel:!1,confirmText:"知道了",success:function(){e.navigateBack()}});void 0!==n.future_talk_enabled&&(o.futureTalkEnabled=1==n.future_talk_enabled),n.future_talk_button_text&&(o.futureTalkButtonText=n.future_talk_button_text),console.log("与未来对话配置:",{enabled:o.futureTalkEnabled,buttonText:o.futureTalkButtonText})}else console.error("获取设置失败:",t.msg)}),(function(e){console.error("检查设置失败:",e)}))},goBack:function(){e.navigateBack()},selectGender:function(e){0!==e&&(this.genderIndex=e)},onInputFocus:function(){console.log("职业输入框获得焦点")},onInputBlur:function(){console.log("职业输入框失去焦点")},onProfessionInput:function(e){this.userProfession=e.detail.value,console.log("职业输入内容:",this.userProfession)},onInputConfirm:function(e){this.userProfession=e.detail.value,console.log("职业输入确认:",this.userProfession)},saveConfig:function(){var o=this;this.genderIndex<=0?e.showToast({title:"请选择性别",icon:"none"}):this.userProfession&&""!==this.userProfession.trim()?this.doSaveConfig():e.showModal({title:"提示",content:"建议填写职业信息以获得更准确的预测结果，是否继续？",success:function(e){e.confirm&&o.doSaveConfig()}})},doSaveConfig:function(){try{var o=this.userProfession.trim();e.setStorageSync("user_gender",this.genderOptions[this.genderIndex]),e.setStorageSync("user_profession",o),console.log("保存配置:",{gender:this.genderOptions[this.genderIndex],profession:o}),e.showToast({title:"配置已保存",icon:"success"}),this.hideConfigModal()}catch(t){console.error("保存配置失败:",t),e.showToast({title:"保存失败，请重试",icon:"error"})}},skipConfig:function(){var o=this;e.showModal({title:"跳过配置",content:"跳过配置可能影响AI预测准确性，确定要跳过吗？",success:function(t){t.confirm&&(e.setStorageSync("user_gender","未设置"),e.setStorageSync("user_profession","未设置"),e.showToast({title:"已跳过配置",icon:"none"}),o.hideConfigModal())}})},hideConfigModal:function(){this.showConfigTip&&this.genderIndex<=0?e.showToast({title:"请先选择性别信息",icon:"none"}):(this.showConfig=!1,this.showConfigTip=!1)},forceShowConfig:function(){this.showConfig=!0,this.showConfigTip=!0},clearTimers:function(){this.processingTimer&&(clearInterval(this.processingTimer),this.processingTimer=null),this.progressTimer&&(clearInterval(this.progressTimer),this.progressTimer=null)}}};o.default=t}).call(this,t("df3c")["default"])},"3db1":function(e,o,t){"use strict";t.r(o);var n=t("fb46d"),r=t("c4e4");for(var s in r)["default"].indexOf(s)<0&&function(e){t.d(o,e,(function(){return r[e]}))}(s);t("417f");var i=t("828b"),c=Object(i["a"])(r["default"],n["b"],n["c"],!1,null,"24b32569",null,!1,n["a"],void 0);o["default"]=c.exports},"417f":function(e,o,t){"use strict";var n=t("4719"),r=t.n(n);r.a},4719:function(e,o,t){},"69e3":function(e,o,t){"use strict";(function(e,o){var n=t("47a9");t("06e9");n(t("3240"));var r=n(t("3db1"));e.__webpack_require_UNI_MP_PLUGIN__=t,o(r.default)}).call(this,t("3223")["default"],t("df3c")["createPage"])},c4e4:function(e,o,t){"use strict";t.r(o);var n=t("0098"),r=t.n(n);for(var s in n)["default"].indexOf(s)<0&&function(e){t.d(o,e,(function(){return n[e]}))}(s);o["default"]=r.a},fb46d:function(e,o,t){"use strict";t.d(o,"b",(function(){return n})),t.d(o,"c",(function(){return r})),t.d(o,"a",(function(){}));var n=function(){var e=this,o=e.$createElement,t=(e._self._c,"camera"===e.currentStep?e.t("color1"):null),n="camera"===e.currentStep?e.t("color1"):null,r="camera"===e.currentStep?e.t("color1"):null,s="camera"===e.currentStep?e.t("color1"):null,i="camera"===e.currentStep?e.t("color1"):null,c="preview"===e.currentStep?e.t("color1"):null,l="preview"===e.currentStep?e.t("color1rgb"):null,a="preview"===e.currentStep?e.t("color1"):null,u="processing"===e.currentStep?e.t("color1"):null,g="processing"===e.currentStep?e.t("color1rgb"):null,d="processing"===e.currentStep?e.t("color1"):null,p="processing"===e.currentStep?e.t("color1"):null,f="processing"===e.currentStep?e.t("color1"):null,h="processing"===e.currentStep?e.__map(e.processingSteps,(function(o,t){var n=e.__get_orig(o),r=e.t("color1"),s=t<=e.currentProcessStep?e.t("color1"):null,i=t<=e.currentProcessStep?e.t("color1"):null;return{$orig:n,m13:r,m14:s,m15:i}})):null,m="processing"===e.currentStep?e.t("color1"):null,I="processing"===e.currentStep?e.t("color1"):null,S="processing"===e.currentStep?e.t("color1rgb"):null,v="processing"===e.currentStep?e.t("color1"):null,w="result"===e.currentStep?e.t("color1"):null,P="result"===e.currentStep?e.t("color1rgb"):null,T="result"===e.currentStep?e.t("color1"):null,_="result"===e.currentStep?e.t("color1"):null,A="result"===e.currentStep?e.t("color1"):null,C="result"===e.currentStep?e.t("color1"):null,b="result"!==e.currentStep||e.predictedImageLoaded?null:e.t("color1"),L="result"===e.currentStep&&e.futureTalkEnabled?e.t("color1"):null,k="result"===e.currentStep&&e.futureTalkEnabled?e.t("color1"):null,x="result"===e.currentStep&&e.futureTalkEnabled?e.t("color1"):null,y="result"===e.currentStep&&e.futureTalkEnabled?e.t("color1"):null,E="result"===e.currentStep&&e.futureTalkEnabled?e.__map(5,(function(o,t){var n=e.__get_orig(o),r=e.t("color1");return{$orig:n,m31:r}})):null,U=e.showConfig?e.t("color1"):null,M=e.showConfig&&e.showConfigTip?e.t("color1"):null,D=e.showConfig?e.t("color1"):null,R=e.showConfig?e.__map(e.genderOptions,(function(o,t){var n=e.__get_orig(o),r=t>0&&e.genderIndex===t?e.t("color1"):null,s=t>0&&e.genderIndex===t?e.t("color1rgb"):null,i=t>0&&e.genderIndex===t?e.t("color1"):null;return{$orig:n,m35:r,m36:s,m37:i}})):null,O=e.showConfig?e.t("color1"):null,B=e.showConfig&&e.userProfession?e.t("color1"):null,$=e.showConfig&&e.userProfession?e.t("color1"):null,G=e.showConfig&&e.userProfession?e.t("color1"):null,F=e.showConfig?e.t("color1"):null,W=e.showConfig?e.t("color1rgb"):null;e.$mp.data=Object.assign({},{$root:{m0:t,m1:n,m2:r,m3:s,m4:i,m5:c,m6:l,m7:a,m8:u,m9:g,m10:d,m11:p,m12:f,l0:h,m16:m,m17:I,m18:S,m19:v,m20:w,m21:P,m22:T,m23:_,m24:A,m25:C,m26:b,m27:L,m28:k,m29:x,m30:y,l1:E,m32:U,m33:M,m34:D,l2:R,m38:O,m39:B,m40:$,m41:G,m42:F,m43:W}})},r=[]}},[["69e3","common/runtime","common/vendor"]]]);