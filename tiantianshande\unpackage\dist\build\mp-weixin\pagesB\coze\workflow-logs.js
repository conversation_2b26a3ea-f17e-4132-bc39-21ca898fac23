require('../common/vendor.js');(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pagesB/coze/workflow-logs"],{"017d9":function(t,o,e){"use strict";(function(t,o){var n=e("47a9");e("06e9");n(e("3240"));var r=n(e("206e"));t.__webpack_require_UNI_MP_PLUGIN__=e,o(r.default)}).call(this,e("3223")["default"],e("df3c")["createPage"])},"062d":function(t,o,e){},"206e":function(t,o,e){"use strict";e.r(o);var n=e("fdd9"),r=e("c668");for(var i in r)["default"].indexOf(i)<0&&function(t){e.d(o,t,(function(){return r[t]}))}(i);e("b35ed");var a=e("828b"),l=Object(a["a"])(r["default"],n["b"],n["c"],!1,null,null,null,!1,n["a"],void 0);o["default"]=l.exports},b35ed:function(t,o,e){"use strict";var n=e("062d"),r=e.n(n);r.a},c668:function(t,o,e){"use strict";e.r(o);var n=e("ccf0"),r=e.n(n);for(var i in n)["default"].indexOf(i)<0&&function(t){e.d(o,t,(function(){return n[t]}))}(i);o["default"]=r.a},ccf0:function(t,o,e){"use strict";(function(t){Object.defineProperty(o,"__esModule",{value:!0}),o.default=void 0;var e=getApp(),n={data:function(){return{isload:!1,loading:!1,logsList:[],workflowList:[],selectedWorkflow:null,currentLog:{},pagenum:1,nomore:!1,nodata:!1}},onLoad:function(t){this.getWorkflowList(),this.getLogsList()},onReachBottom:function(){this.nodata||this.nomore||(this.pagenum=this.pagenum+1,this.getLogsList(!0))},onPullDownRefresh:function(){this.getLogsList()},methods:{getWorkflowList:function(){var t=this;e.post("ApiCoze/getWorkflowList",{},(function(o){1===o.code&&(t.workflowList=o.data||[])}))},getLogsList:function(o){o||(this.pagenum=1,this.logsList=[]);var n=this;n.loading=!0,n.nodata=!1,n.nomore=!1;var r={page:n.pagenum,limit:20};n.selectedWorkflow&&(r.workflow_id=n.selectedWorkflow.workflow_id),e.post("ApiCoze/getWorkflowLogs",r,(function(o){if(n.loading=!1,t.stopPullDownRefresh(),1===o.code){var e=o.data.list||[];e.forEach((function(t){if(t.create_time_text=n.formatTime(t.create_time),t.parameters)try{var o=JSON.parse(t.parameters),e=Object.keys(o);e.length>0&&(t.parameters_preview=e.slice(0,2).map((function(t){return t+": "+o[t]})).join(", "),e.length>2&&(t.parameters_preview+="..."))}catch(r){t.parameters_preview="参数解析失败"}})),1===n.pagenum?(n.logsList=e,0===e.length&&(n.nodata=!0)):0===e.length?n.nomore=!0:n.logsList=n.logsList.concat(e)}else t.showToast({title:o.msg,icon:"none",duration:2e3}),1===n.pagenum&&(n.nodata=!0);n.loaded()}))},showWorkflowFilter:function(){this.$refs.workflowFilterPopup.open()},closeWorkflowFilter:function(){this.$refs.workflowFilterPopup.close()},selectWorkflowFilter:function(t){var o=t.currentTarget.dataset.workflow;this.selectedWorkflow=o?JSON.parse(o):null,this.getLogsList(),this.closeWorkflowFilter()},showLogDetail:function(t){var o=JSON.parse(t.currentTarget.dataset.log);this.currentLog=o,this.$refs.logDetailPopup.open()},closeLogDetail:function(){this.$refs.logDetailPopup.close()},formatJson:function(t){try{var o="string"===typeof t?JSON.parse(t):t;return JSON.stringify(o,null,2)}catch(e){return t}},formatTime:function(t){var o=new Date(1e3*t);return o.getFullYear()+"-"+(o.getMonth()+1).toString().padStart(2,"0")+"-"+o.getDate().toString().padStart(2,"0")+" "+o.getHours().toString().padStart(2,"0")+":"+o.getMinutes().toString().padStart(2,"0")}}};o.default=n}).call(this,e("df3c")["default"])},fdd9:function(t,o,e){"use strict";e.d(o,"b",(function(){return r})),e.d(o,"c",(function(){return i})),e.d(o,"a",(function(){return n}));var n={nomore:function(){return e.e("components/nomore/nomore").then(e.bind(null,"3892"))},nodata:function(){return e.e("components/nodata/nodata").then(e.bind(null,"101c"))},uniPopup:function(){return Promise.all([e.e("common/vendor"),e.e("components/uni-popup/uni-popup")]).then(e.bind(null,"ca44a"))},loading:function(){return e.e("components/loading/loading").then(e.bind(null,"ceaa"))}},r=function(){var t=this,o=t.$createElement,e=(t._self._c,t.isload?t.__map(t.logsList,(function(o,e){var n=t.__get_orig(o),r=JSON.stringify(o);return{$orig:n,g0:r}})):null),n=t.isload&&!t.selectedWorkflow?t.t("color1"):null,r=t.isload?t.__map(t.workflowList,(function(o,e){var n=t.__get_orig(o),r=JSON.stringify(o),i=t.selectedWorkflow&&t.selectedWorkflow.workflow_id===o.workflow_id?t.t("color1"):null;return{$orig:n,g1:r,m1:i}})):null,i=t.isload&&t.currentLog.parameters?t.formatJson(t.currentLog.parameters):null,a=t.isload&&t.currentLog.result?t.formatJson(t.currentLog.result):null,l=t.isload?t.t("color1"):null;t.$mp.data=Object.assign({},{$root:{l0:e,m0:n,l1:r,m2:i,m3:a,m4:l}})},i=[]}},[["017d9","common/runtime","common/vendor"]]]);