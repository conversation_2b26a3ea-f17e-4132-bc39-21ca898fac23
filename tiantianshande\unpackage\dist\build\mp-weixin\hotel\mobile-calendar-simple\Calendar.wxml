<transition vue-id="a0507b30-1" name="{{transition}}" class="data-v-37321f20" bind:__l="__l" vue-slots="{{['default']}}"><block wx:if="{{isShow}}"><view class="{{['calendar-tz','_div','data-v-37321f20',isFixed&&'fixed']}}"><slot name="header"></slot><view class="week-number _div data-v-37321f20"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><label style="{{'color:'+(item.g0)+';'}}" class="_span data-v-37321f20">{{item.$orig}}</label></block></view><block wx:if="{{title}}"><view class="tips _p data-v-37321f20">{{title}}</view></block><view class="content _div data-v-37321f20" id="scrollWrap"><block wx:for="{{$root.l2}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="con _div data-v-37321f20" id="{{item.$orig.year+''+item.$orig.month}}"><view class="_h3 data-v-37321f20">{{item.$orig.year+'年'+item.$orig.month+'月'}}</view><label class="month-bg _span data-v-37321f20" style="{{'color:'+(getBetweenColor)+';'}}">{{item.$orig.month}}</label><view class="each-month _ul data-v-37321f20"><block wx:for="{{item.l1}}" wx:for-item="day" wx:for-index="idx" wx:key="idx"><view data-event-opts="{{[['tap',[['chooseDate',['$0','$1','$2'],[[['calendar','',index],['dayList','',idx]],[['calendar','',index,'month']],[['calendar','',index,'year']]]]]]]}}" class="{{['each-day','_li','data-v-37321f20',day.m0]}}" style="{{'background:'+(day.m1)+';'}}" bindtap="__e"><block wx:if="{{mode!=4}}"><view class="{{['_div','data-v-37321f20',day.m2]}}" style="{{'background:'+(day.m3)+';'}}"><view class="day _p data-v-37321f20">{{day.$orig?day.$orig:''}}</view><block wx:if="{{day.m4}}"><view class="recent _p data-v-37321f20"><view class="_i data-v-37321f20">{{day.m5}}</view></view></block><block wx:else><block wx:if="{{showstock==1&&day.$orig>0}}"><view class="recent _p data-v-37321f20"><block wx:if="{{dayroomprice[item.$orig.year+'-'+(item.$orig.month<=9?'0':'')+item.$orig.month+'-'+(day.$orig>0&&day.$orig<=9?'0':'')+(day.$orig?day.$orig:'')]&&dayroomprice[item.$orig.year+'-'+(item.$orig.month<=9?'0':'')+item.$orig.month+'-'+(day.$orig>0&&day.$orig<=9?'0':'')+(day.$orig?day.$orig:'')]['status']==1}}"><block class="data-v-37321f20">{{dayroomprice[item.$orig.year+'-'+(item.$orig.month<=9?'0':'')+item.$orig.month+'-'+(day.$orig<=9?'0':'')+(day.$orig?day.$orig:'')]['stock']+text['间']+''}}</block></block></view></block></block></view></block><block wx:else><view class="{{['_div','data-v-37321f20',day.m6]}}"><view class="day _p data-v-37321f20">{{day.$orig?day.$orig:''}}</view><view class="recent _p data-v-37321f20">1<view class="_i data-v-37321f20">{{day.m7}}</view></view></view></block></view></block></view></view></block></view><view style="height:80px;" class="_div data-v-37321f20"></view><slot name="footer"></slot></view></block></transition>