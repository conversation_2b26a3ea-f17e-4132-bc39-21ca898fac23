<view class="like-button"><view class="animate-wrap"><block wx:for="{{viewList}}" wx:for-item="item" wx:for-index="index" wx:key="elId"><view data-ref="{{item.elId}}" class="a-img vue-ref-in-for" style="{{'right:'+(site.x||site[0]+'rpx')+';'+('bottom:'+(site.y||site[1]+'rpx')+';')}}"><image style="{{'width:'+(imgWidth+'rpx')+';'+('height:'+(imgHeight+'rpx')+';')}}" mode="widthFix" src="{{item.src}}" animation="{{item.animation}}"></image></view></block></view><view class="on-button"><block wx:if="{{!$slots.default}}"><image style="{{'width:'+(width+'rpx')+';'+('height:'+(height+'rpx')+';')}}" src="{{src}}" mode="widthFix" data-event-opts="{{[['tap',[['handleClick',['$event']]]]]}}" bindtap="__e"></image></block><view data-event-opts="{{[['tap',[['handleClick',['$event']]]]]}}" class="el_like_btn" bindtap="__e"><slot></slot></view></view></view>