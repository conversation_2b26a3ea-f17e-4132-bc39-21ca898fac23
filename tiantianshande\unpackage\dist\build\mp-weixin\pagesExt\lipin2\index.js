require('../common/vendor.js');(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pagesExt/lipin2/index"],{"269f":function(n,t,e){"use strict";e.d(t,"b",(function(){return a})),e.d(t,"c",(function(){return i})),e.d(t,"a",(function(){return o}));var o={parse:function(){return Promise.all([e.e("common/vendor"),e.e("components/parse/parse")]).then(e.bind(null,"1f1a"))},loading:function(){return e.e("components/loading/loading").then(e.bind(null,"ceaa"))},dpTabbar:function(){return e.e("components/dp-tabbar/dp-tabbar").then(e.bind(null,"b875"))},popmsg:function(){return e.e("components/popmsg/popmsg").then(e.bind(null,"2bf2"))}},a=function(){var n=this.$createElement;this._self._c},i=[]},"559e":function(n,t,e){"use strict";(function(n,t){var o=e("47a9");e("06e9");o(e("3240"));var a=o(e("ac08"));n.__webpack_require_UNI_MP_PLUGIN__=e,t(a.default)}).call(this,e("3223")["default"],e("df3c")["createPage"])},"66ab":function(n,t,e){"use strict";(function(n){Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=getApp(),a={data:function(){return{opt:{},loading:!1,isload:!1,menuindex:-1,platform:o.globalData.platform,pre_url:o.globalData.pre_url,userinfo:[],money:"",moneyduan:0,dhcode:"",lipinset:{}}},onLoad:function(n){this.opt=o.getopts(n),this.opt&&this.opt.dhcode&&(this.dhcode=this.opt.dhcode),this.getdata()},onPullDownRefresh:function(){this.getdata()},methods:{getdata:function(){var n=this;n.loading=!0,o.get("ApiLipin/index",{},(function(t){n.loading=!1,n.lipinset=t.lipinset,n.loaded()}))},subconfirm:function(n){var t=this,e=n.detail.value.dhcode;t.loading=!0,o.post("ApiLipin2/index",{dhcode:e},(function(n){t.loading=!1,0!=n.status?(1==n.status&&o.alert(n.msg,(function(){o.goto("/pages/my/usercenter")})),2==n.status&&(o.success(n.msg),setTimeout((function(){o.goto("prodh?dhcode="+e)}),1e3)),3==n.status&&o.alert(n.msg,(function(){o.goto("/pages/coupon/mycoupon")})),t.loaded()):o.error(n.msg)}))},saoyisao:function(t){var a=this;if("h5"!=o.globalData.platform)if("mp"==o.globalData.platform){var i=e("6fcc");i.ready((function(){i.scanQRCode({needResult:1,scanType:["qrCode","barCode"],success:function(n){var t=n.resultStr;if(-1===t.indexOf("?"))var e=t;else{var o=t.split("=");e=o.pop()}if(-1!==e.indexOf(",")){o=e.split(",");e=o.pop()}a.dhcode=e}})}))}else n.scanCode({success:function(n){console.log(n);var t=n.result;if(-1===t.indexOf("?"))var e=t;else{var o=t.split("=");e=o.pop()}if(-1!==e.indexOf(",")){o=e.split(",");e=o.pop()}a.dhcode=e}});else o.alert("请使用微信扫一扫功能扫码")}}};t.default=a}).call(this,e("df3c")["default"])},"84e1":function(n,t,e){"use strict";e.r(t);var o=e("66ab"),a=e.n(o);for(var i in o)["default"].indexOf(i)<0&&function(n){e.d(t,n,(function(){return o[n]}))}(i);t["default"]=a.a},ac08:function(n,t,e){"use strict";e.r(t);var o=e("269f"),a=e("84e1");for(var i in a)["default"].indexOf(i)<0&&function(n){e.d(t,n,(function(){return a[n]}))}(i);e("d2c1");var s=e("828b"),u=Object(s["a"])(a["default"],o["b"],o["c"],!1,null,null,null,!1,o["a"],void 0);t["default"]=u.exports},d1a1:function(n,t,e){},d2c1:function(n,t,e){"use strict";var o=e("d1a1"),a=e.n(o);a.a}},[["559e","common/runtime","common/vendor"]]]);