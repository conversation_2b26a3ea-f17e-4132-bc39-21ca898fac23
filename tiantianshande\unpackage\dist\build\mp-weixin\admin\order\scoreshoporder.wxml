<view class="container"><block wx:if="{{isload}}"><block><dd-tab vue-id="304e709e-1" itemdata="{{['全部','待付款','待发货','待收货','已完成','退款']}}" itemst="{{['all','0','1','2','3','10']}}" st="{{st}}" isfixed="{{true}}" data-event-opts="{{[['^changetab',[['changetab']]]]}}" bind:changetab="__e" bind:__l="__l"></dd-tab><view style="width:100%;height:100rpx;"></view><view class="topsearch flex-y-center"><view class="f1 flex-y-center"><image class="img" src="/static/img/search_ico.png"></image><input placeholder="输入关键字搜索" placeholder-style="font-size:24rpx;color:#C2C2C2" data-event-opts="{{[['confirm',[['searchConfirm',['$event']]]]]}}" value="{{keyword}}" bindconfirm="__e"/></view></view><view class="order-content"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><view class="order-box" data-url="{{'scoreshoporderdetail?id='+item.$orig.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="head"><view>{{"订单号："+item.$orig.ordernum}}</view><view class="flex1"></view><block wx:if="{{item.$orig.status==0}}"><text class="st0">待付款</text></block><block wx:if="{{item.$orig.status==1&&item.$orig.freight_type!=1}}"><text class="st1">待发货</text></block><block wx:if="{{item.$orig.status==1&&item.$orig.freight_type==1}}"><text class="st1">待提货</text></block><block wx:if="{{item.$orig.status==2}}"><text class="st2">待收货</text></block><block wx:if="{{item.$orig.status==3}}"><text class="st3">已完成</text></block><block wx:if="{{item.$orig.status==4}}"><text class="st4">已关闭</text></block></view><block wx:for="{{item.$orig.prolist}}" wx:for-item="item2" wx:for-index="idx" wx:key="idx"><block><view class="content" style="{{(idx+1==item.$orig.procount?'border-bottom:none':'')}}"><view data-url="{{'/pages/scoreshop/product?id='+item2.proid}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e"><image src="{{item2.pic}}"></image></view><view class="detail"><text class="t1">{{item2.name}}</text><text class="t2">{{"市场价￥"+item2.sell_price}}</text><view class="t3"><text class="x1 flex1"><block wx:if="{{item2.money_price>0}}"><text>{{"￥"+item2.money_price+"+"}}</text></block>{{item2.score_price+item.m0}}</text><text class="x2">{{"×"+item2.num}}</text></view></view></view></block></block><view class="bottom"><text>{{"共计"+item.$orig.procount+"件商品 实付:￥"+item.$orig.totalprice}}</text><block wx:if="{{item.$orig.refund_status==1}}"><text style="color:red;">{{'退款中￥'+item.$orig.refund_money}}</text></block><block wx:if="{{item.$orig.refund_status==2}}"><text style="color:red;">{{'已退款￥'+item.$orig.refund_money}}</text></block><block wx:if="{{item.$orig.refund_status==3}}"><text style="color:red;">退款申请已驳回</text></block></view><view class="bottom flex-y-center"><image style="width:40rpx;height:40rpx;border-radius:50%;margin-right:10rpx;" src="{{item.$orig.member.headimg}}"></image><text style="font-weight:bold;color:#333;margin-right:8rpx;">{{item.$orig.member.nickname}}</text>{{"(ID:"+item.$orig.mid+')'}}</view></view></block></block></view><block wx:if="{{nomore}}"><nomore vue-id="304e709e-2" bind:__l="__l"></nomore></block><block wx:if="{{nodata}}"><nodata vue-id="304e709e-3" bind:__l="__l"></nodata></block></block></block><block wx:if="{{loading}}"><loading vue-id="304e709e-4" bind:__l="__l"></loading></block><popmsg class="vue-ref" vue-id="304e709e-5" data-ref="popmsg" bind:__l="__l"></popmsg></view>