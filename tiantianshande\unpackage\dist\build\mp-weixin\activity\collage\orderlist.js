(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["activity/collage/orderlist"],{"0605":function(t,n,o){"use strict";(function(t,n){var a=o("47a9");o("06e9");a(o("3240"));var e=a(o("97fc"));t.__webpack_require_UNI_MP_PLUGIN__=o,n(e.default)}).call(this,o("3223")["default"],o("df3c")["createPage"])},4907:function(t,n,o){"use strict";o.d(n,"b",(function(){return e})),o.d(n,"c",(function(){return i})),o.d(n,"a",(function(){return a}));var a={ddTab:function(){return o.e("components/dd-tab/dd-tab").then(o.bind(null,"caa1"))},nomore:function(){return o.e("components/nomore/nomore").then(o.bind(null,"3892"))},nodata:function(){return o.e("components/nodata/nodata").then(o.bind(null,"101c"))},loading:function(){return o.e("components/loading/loading").then(o.bind(null,"ceaa"))},dpTabbar:function(){return o.e("components/dp-tabbar/dp-tabbar").then(o.bind(null,"b875"))},popmsg:function(){return o.e("components/popmsg/popmsg").then(o.bind(null,"2bf2"))}},e=function(){var t=this,n=t.$createElement,o=(t._self._c,t.isload?t.__map(t.datalist,(function(n,o){var a=t.__get_orig(n),e=[1,2,3].includes(n.status)&&n.invoice&&n.team&&2==n.team.status,i=0==n.status?t.t("color1"):null,r=2==n.status?t.t("color1"):null;return{$orig:a,g0:e,m0:i,m1:r}})):null);t.$mp.data=Object.assign({},{$root:{l0:o}})},i=[]},"97fc":function(t,n,o){"use strict";o.r(n);var a=o("4907"),e=o("a7ae");for(var i in e)["default"].indexOf(i)<0&&function(t){o.d(n,t,(function(){return e[t]}))}(i);o("fb9e");var r=o("828b"),u=Object(r["a"])(e["default"],a["b"],a["c"],!1,null,null,null,!1,a["a"],void 0);n["default"]=u.exports},a7ae:function(t,n,o){"use strict";o.r(n);var a=o("d658"),e=o.n(a);for(var i in a)["default"].indexOf(i)<0&&function(t){o.d(n,t,(function(){return a[t]}))}(i);n["default"]=e.a},d658:function(t,n,o){"use strict";(function(t){Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;var o=getApp(),a={data:function(){return{opt:{},loading:!1,isload:!1,menuindex:-1,st:"all",datalist:[],pagenum:1,nomore:!1,nodata:!1,keyword:""}},onLoad:function(t){this.opt=o.getopts(t),this.getdata()},onPullDownRefresh:function(){this.getdata()},onReachBottom:function(){this.nodata||this.nomore||(this.pagenum=this.pagenum+1,this.getdata(!0))},onNavigationBarSearchInputConfirmed:function(t){this.searchConfirm({detail:{value:t.text}})},methods:{changetab:function(n){this.st=n,t.pageScrollTo({scrollTop:0,duration:0}),this.getdata()},getdata:function(t){t||(this.pagenum=1,this.datalist=[]);var n=this,a=n.pagenum,e=n.st;n.nodata=!1,n.nomore=!1,n.loading=!0,o.post("ApiCollage/orderlist",{st:e,pagenum:a,keyword:n.keyword},(function(t){n.loading=!1;var o=t.datalist;if(1==a)n.datalist=o,0==o.length&&(n.nodata=!0),n.loaded();else if(0==o.length)n.nomore=!0;else{var e=n.datalist,i=e.concat(o);n.datalist=i}}))},toclose:function(t){var n=this,a=t.currentTarget.dataset.id;o.confirm("确定要关闭该订单吗?",(function(){o.showLoading("提交中"),o.post("ApiCollage/closeOrder",{orderid:a},(function(t){o.showLoading(!1),o.success(t.msg),setTimeout((function(){n.getdata()}),1e3)}))}))},todel:function(t){var n=this,a=t.currentTarget.dataset.id;o.confirm("确定要删除该订单吗?",(function(){o.showLoading("删除中"),o.post("ApiCollage/delOrder",{orderid:a},(function(t){o.showLoading(!1),o.success(t.msg),setTimeout((function(){n.getdata()}),1e3)}))}))},orderCollect:function(t){var n=this,a=t.currentTarget.dataset.id;o.confirm("确定要收货吗?",(function(){o.showLoading("提交中"),o.post("ApiCollage/orderCollect",{orderid:a},(function(t){o.showLoading(!1),o.success(t.msg),setTimeout((function(){n.getdata()}),1e3)}))}))},searchConfirm:function(t){this.keyword=t.detail.value,this.getdata(!1)}}};n.default=a}).call(this,o("df3c")["default"])},daa2:function(t,n,o){},fb9e:function(t,n,o){"use strict";var a=o("daa2"),e=o.n(a);e.a}},[["0605","common/runtime","common/vendor"]]]);