(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["activity/luntan/index"],{"1eef":function(t,n,e){},3715:function(t,n,e){"use strict";(function(t){Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;var e=getApp(),a={data:function(){return{opt:{},loading:!1,isload:!1,menuindex:-1,pre_url:e.globalData.pre_url,sysset:{},datalist:[],clist:[],title:"",pagenum:1,keyword:"",nomore:!1,nodata:!1,picindex:0,need_call:!1}},onLoad:function(t){this.opt=e.getopts(t),this.getdata()},onPullDownRefresh:function(){this.getdata()},onReachBottom:function(){this.nodata||this.nomore||(this.pagenum=this.pagenum+1,this.getdata(!0))},methods:{getdata:function(n){n||(this.pagenum=1,this.datalist=[]);var a=this,i=a.pagenum;a.keyword;a.nodata=!1,a.nomore=!1,a.loading=!0,e.post("ApiLuntan/index",{pagenum:i},(function(n){a.loading=!1,n.need_call&&(a.need_call=!0);var e=n.datalist;if(1==i)t.setNavigationBarTitle({title:n.title}),a.datalist=n.datalist,a.sysset=n.sysset,a.title=n.title,a.clist=n.clist,0==e.length&&(a.nodata=!0),a.loaded();else if(0==e.length)a.nomore=!0;else{var o=a.datalist,s=o.concat(e);a.datalist=s}}))},zan:function(t){var n=this,a=t.currentTarget.dataset.id,i=t.currentTarget.dataset.index,o=n.datalist;e.post("ApiLuntan/zan",{id:a},(function(t){0==t.type?(o[i].iszan=0,o[i].zan=o[i].zan-1):(o[i].iszan=1,o[i].zan=o[i].zan+1),n.datalist=o}))},savecontent:function(t){var n=this,e=t.currentTarget.dataset.index,a=n.datalist[e];n.fuzhi(a.content,(function(){n.savpic(a.pics,(function(){n.savevideo(a.video)}))}))},fuzhi:function(n,a){if(n){t.setClipboardData({data:n,success:function(){e.success("已复制到剪贴板"),setTimeout((function(){"function"==typeof a&&a()}),500)},fail:function(){e.error("请长按文本内容复制"),setTimeout((function(){"function"==typeof a&&a()}),500)}})}else"function"==typeof a&&a()},savpic:function(t,n){t?"mp"!=e.globalData.platform&&"h5"!=e.globalData.platform?(this.picindex=0,this.savpic2(t),"function"==typeof n&&n()):e.error("请长按图片保存"):"function"==typeof n&&n()},savpic2:function(n){var a=this,i=this.picindex;if(i>=n.length)return e.showLoading(!1),void e.success("已保存到相册");var o=n[i];e.showLoading("图片保存中"),t.downloadFile({url:o,success:function(i){200===i.statusCode&&t.saveImageToPhotosAlbum({filePath:i.tempFilePath,success:function(){a.picindex++,a.savpic2(n)},fail:function(){e.showLoading(!1),e.error("保存失败")}})},fail:function(){e.showLoading(!1),e.error("下载失败")}})},savevideo:function(n){n&&(e.showLoading("视频下载中"),t.downloadFile({url:n,success:function(n){200===n.statusCode&&t.saveVideoToPhotosAlbum({filePath:n.tempFilePath,success:function(){e.showLoading(!1),e.success("视频保存成功")},fail:function(){e.showLoading(!1),e.error("视频保存失败")}})},fail:function(){e.showLoading(!1),e.error("视频下载失败!")}}))},callphone:function(n){var e=n.currentTarget.dataset.phone;t.makePhoneCall({phoneNumber:e,fail:function(){}})}}};n.default=a}).call(this,e("df3c")["default"])},"3ee0":function(t,n,e){"use strict";e.r(n);var a=e("968a"),i=e("ace9");for(var o in i)["default"].indexOf(o)<0&&function(t){e.d(n,t,(function(){return i[t]}))}(o);e("9c2d");var s=e("828b"),c=Object(s["a"])(i["default"],a["b"],a["c"],!1,null,null,null,!1,a["a"],void 0);n["default"]=c.exports},"7c61":function(t,n,e){"use strict";(function(t,n){var a=e("47a9");e("06e9");a(e("3240"));var i=a(e("3ee0"));t.__webpack_require_UNI_MP_PLUGIN__=e,n(i.default)}).call(this,e("3223")["default"],e("df3c")["createPage"])},"968a":function(t,n,e){"use strict";e.d(n,"b",(function(){return i})),e.d(n,"c",(function(){return o})),e.d(n,"a",(function(){return a}));var a={nomore:function(){return e.e("components/nomore/nomore").then(e.bind(null,"3892"))},nodata:function(){return e.e("components/nodata/nodata").then(e.bind(null,"101c"))},loading:function(){return e.e("components/loading/loading").then(e.bind(null,"ceaa"))},dpTabbar:function(){return e.e("components/dp-tabbar/dp-tabbar").then(e.bind(null,"b875"))},popmsg:function(){return e.e("components/popmsg/popmsg").then(e.bind(null,"2bf2"))}},i=function(){var t=this.$createElement,n=(this._self._c,this.isload?this.clist.length:null);this.$mp.data=Object.assign({},{$root:{g0:n}})},o=[]},"9c2d":function(t,n,e){"use strict";var a=e("1eef"),i=e.n(a);i.a},ace9:function(t,n,e){"use strict";e.r(n);var a=e("3715"),i=e.n(a);for(var o in a)["default"].indexOf(o)<0&&function(t){e.d(n,t,(function(){return a[t]}))}(o);n["default"]=i.a}},[["7c61","common/runtime","common/vendor"]]]);