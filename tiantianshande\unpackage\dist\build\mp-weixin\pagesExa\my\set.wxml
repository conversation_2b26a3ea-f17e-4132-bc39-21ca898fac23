<view class="container"><block wx:if="{{isload}}"><block><view class="content"><view class="info-item" style="height:136rpx;line-height:136rpx;"><view class="t1" style="flex:1;">头像</view><image style="width:88rpx;height:88rpx;" src="{{userinfo.headimg}}" data-event-opts="{{[['tap',[['uploadHeadimg',['$event']]]]]}}" bindtap="__e"></image><image class="t3" src="/static/img/arrowright.png"></image></view><view class="info-item" data-url="setnickname" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="t1">昵称</view><view class="t2">{{userinfo.nickname}}</view><image class="t3" src="/static/img/arrowright.png"></image></view></view><view class="content"><view class="info-item" data-url="setidcard" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="t1">身份证认证</view><view class="t2">{{userinfo.realname}}</view><image class="t3" src="/static/img/arrowright.png"></image></view><view class="info-item" data-url="setrealname" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="t1">姓名</view><view class="t2">{{userinfo.realname}}</view><image class="t3" src="/static/img/arrowright.png"></image></view><view class="info-item" data-url="settel" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="t1">手机号</view><view class="t2">{{userinfo.tel}}</view><image class="t3" src="/static/img/arrowright.png"></image></view><view class="info-item" data-url="setsex" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><text class="t1">性别</text><block wx:if="{{userinfo.sex==1}}"><text class="t2">男</text></block><block wx:else><block wx:if="{{userinfo.sex==2}}"><text class="t2">女</text></block><block wx:else><text class="t2">未知</text></block></block><image class="t3" src="/static/img/arrowright.png"></image></view><view class="info-item" data-url="setbirthday" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><text class="t1">生日</text><text class="t2">{{userinfo.birthday}}</text><image class="t3" src="/static/img/arrowright.png"></image></view><view class="info-item" data-url="seteducation" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><text class="t1">学历</text><text class="t2">{{userinfo.education||'未设置'}}</text><image class="t3" src="/static/img/arrowright.png"></image></view><view class="info-item" data-url="setmaritalstatus" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><text class="t1">情感状态</text><text class="t2">{{userinfo.marital_status||'未设置'}}</text><image class="t3" src="/static/img/arrowright.png"></image></view><view class="info-item" data-url="setprofession" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><text class="t1">职业</text><text class="t2">{{userinfo.profession||'未设置'}}</text><image class="t3" src="/static/img/arrowright.png"></image></view><view class="info-item" data-url="editaddress" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="t1">地理位置</view><view class="t2">{{userinfo.full_address+''}}</view><image class="t3" src="/static/img/arrowright.png"></image></view></view><view class="content"><view class="info-item" data-url="setweixin" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="t1">微信号</view><view class="t2">{{userinfo.weixin}}</view><image class="t3" src="/static/img/arrowright.png"></image></view><view class="info-item" data-url="setaliaccount" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="t1">支付宝账号</view><view class="t2">{{userinfo.aliaccount}}</view><image class="t3" src="/static/img/arrowright.png"></image></view><view class="info-item" data-url="setbankinfo" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><text class="t1">银行卡</text><text class="t2">{{userinfo.bankname?'已设置':''}}</text><image class="t3" src="/static/img/arrowright.png"></image></view></view><view class="content"><view class="info-item" data-url="/pages/address/address" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="t1">收货地址</view><view class="t2"></view><image class="t3" src="/static/img/arrowright.png"></image></view></view><block wx:if="{{userinfo.haspwd==1}}"><view class="content"><view class="info-item" data-url="/pagesExa/my/setpwd" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="t1">修改密码</view><view class="t2"></view><image class="t3" src="/static/img/arrowright.png"></image></view></view></block><view class="content"><view class="info-item" data-url="/pagesB/login/login" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="t1">切换账号</view><view class="t2"></view><image class="t3" src="/static/img/arrowright.png"></image></view></view><block wx:if="{{$root.m0}}"><view class="content"><view data-event-opts="{{[['tap',[['logout',['$event']]]]]}}" class="info-item" bindtap="__e"><view class="t1">退出登录</view><view class="t2"></view><image class="t3" src="/static/img/arrowright.png"></image></view></view></block><view class="content"><view data-event-opts="{{[['tap',[['goBack',['$event']]]]]}}" class="back-button" bindtap="__e"><text class="t1">返回</text></view></view></block></block><block wx:if="{{loading}}"><loading vue-id="5f3bc560-1" bind:__l="__l"></loading></block><dp-tabbar vue-id="5f3bc560-2" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="5f3bc560-3" data-ref="popmsg" bind:__l="__l"></popmsg></view>