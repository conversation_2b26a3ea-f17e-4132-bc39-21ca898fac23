<view class="dp-product" style="{{'background-color:'+(params.bgcolor)+';'+('margin:'+(params.margin_y*2.2+'rpx '+params.margin_x*2.2+'rpx 0')+';')+('padding:'+(params.padding_y*2.2+'rpx '+params.padding_x*2.2+'rpx')+';')}}"><block wx:if="{{params.style=='1'||params.style=='2'||params.style=='3'}}"><dp-restaurant-product-item vue-id="0eedf300-1" showstyle="{{params.style}}" data="{{data}}" saleimg="{{params.saleimg}}" showname="{{params.showname}}" showtype="{{params.showtype}}" showprice="{{params.showprice}}" showsales="{{params.showsales}}" showcart="{{params.showcart}}" cartimg="{{params.cartimg}}" idfield="proid" menuindex="{{menuindex}}" bind:__l="__l"></dp-restaurant-product-item></block><block wx:if="{{params.style=='list'}}"><dp-restaurant-product-itemlist vue-id="0eedf300-2" data="{{data}}" saleimg="{{params.saleimg}}" showname="{{params.showname}}" showtype="{{params.showtype}}" showprice="{{params.showprice}}" showsales="{{params.showsales}}" cartimg="{{params.showcart}}" idfield="proid" menuindex="{{menuindex}}" bind:__l="__l"></dp-restaurant-product-itemlist></block><block wx:if="{{params.style=='line'}}"><dp-restaurant-product-itemline vue-id="0eedf300-3" data="{{data}}" saleimg="{{params.saleimg}}" showname="{{params.showname}}" showtype="{{params.showtype}}" showprice="{{params.showprice}}" showsales="{{params.showsales}}" cartimg="{{params.showcart}}" idfield="proid" menuindex="{{menuindex}}" bind:__l="__l"></dp-restaurant-product-itemline></block></view>