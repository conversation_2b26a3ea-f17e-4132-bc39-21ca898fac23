require('../common/vendor.js');(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["admin/workorder/myformdetail"],{"13fbc":function(t,n,i){},"1a9c":function(t,n,i){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;var o=getApp(),e={data:function(){return{opt:{},loading:!1,isload:!1,menuindex:-1,detail:{},formcontent:[],showstatus:0,ishowjindu:!1,jdlist:[]}},onLoad:function(t){this.opt=o.getopts(t),this.getdata(),this.getliucheng()},onPullDownRefresh:function(){this.getdata()},methods:{getdata:function(){var t=this;t.loading=!0,o.get("ApiAdminWorkorder/myformdetail",{id:t.opt.id},(function(n){t.loading=!1,t.form=n.form,t.formcontent=n.formcontent,t.detail=n.detail,t.loaded()}))},getliucheng:function(t){var n=this;o.post("ApiAdminWorkorder/getliucheng",{},(function(t){var i=t.datalist;n.lclist=i}))},setst:function(t){var n=t.currentTarget.dataset.id;this.id=n,this.showstatus=!0},close:function(t){this.showstatus=!1},del:function(t){var n=t.currentTarget.dataset.id;o.confirm("确定要删除吗?",(function(){o.post("ApiAdminWorkorder/formdel",{id:n},(function(t){o.success(t.msg),setTimeout((function(){o.goto("/admin/index/index")}),1e3)}))}))},jindu:function(t){var n=this;n.ishowjindu=!0;var i=t.currentTarget.dataset.id;o.post("ApiWorkorder/selectjindu",{id:i},(function(t){if(1==t.status){var i=t.data;n.jdlist=i}}))},closejd:function(t){this.ishowjindu=!1}}};n.default=e},"317f":function(t,n,i){"use strict";i.d(n,"b",(function(){return e})),i.d(n,"c",(function(){return u})),i.d(n,"a",(function(){return o}));var o={uniPopup:function(){return Promise.all([i.e("common/vendor"),i.e("components/uni-popup/uni-popup")]).then(i.bind(null,"ca44a"))},uniPopupDialog:function(){return i.e("components/uni-popup-dialog/uni-popup-dialog").then(i.bind(null,"267c"))},loading:function(){return i.e("components/loading/loading").then(i.bind(null,"ceaa"))},dpTabbar:function(){return i.e("components/dp-tabbar/dp-tabbar").then(i.bind(null,"b875"))},popmsg:function(){return i.e("components/popmsg/popmsg").then(i.bind(null,"2bf2"))}},e=function(){var t=this.$createElement,n=(this._self._c,this.isload?this.t("会员"):null),i=this.ishowjindu?this.jdlist.length:null;this.$mp.data=Object.assign({},{$root:{m0:n,g0:i}})},u=[]},"925d":function(t,n,i){"use strict";i.r(n);var o=i("1a9c"),e=i.n(o);for(var u in o)["default"].indexOf(u)<0&&function(t){i.d(n,t,(function(){return o[t]}))}(u);n["default"]=e.a},9445:function(t,n,i){"use strict";var o=i("13fbc"),e=i.n(o);e.a},9651:function(t,n,i){"use strict";(function(t,n){var o=i("47a9");i("06e9");o(i("3240"));var e=o(i("cebf"));t.__webpack_require_UNI_MP_PLUGIN__=i,n(e.default)}).call(this,i("3223")["default"],i("df3c")["createPage"])},cebf:function(t,n,i){"use strict";i.r(n);var o=i("317f"),e=i("925d");for(var u in e)["default"].indexOf(u)<0&&function(t){i.d(n,t,(function(){return e[t]}))}(u);i("9445");var a=i("828b"),r=Object(a["a"])(e["default"],o["b"],o["c"],!1,null,null,null,!1,o["a"],void 0);n["default"]=r.exports}},[["9651","common/runtime","common/vendor"]]]);