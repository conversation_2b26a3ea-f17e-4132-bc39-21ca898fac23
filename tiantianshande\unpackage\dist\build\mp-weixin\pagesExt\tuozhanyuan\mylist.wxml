<view class="container"><block wx:if="{{isload}}"><block><view class="search-container"><view class="topsearch flex-y-center"><view class="f1 flex-y-center"><image class="img" src="/static/img/search_ico.png"></image><input placeholder="搜索商家" placeholder-style="font-size:24rpx;color:#C2C2C2" confirm-type="search" data-event-opts="{{[['confirm',[['search',['$event']]]]]}}" value="{{keyword}}" bindconfirm="__e"/></view></view><view class="search-navbar"><view class="search-navbar-item" data-field="sales" data-order="{{order=='asc'?'desc':'asc'}}" data-event-opts="{{[['tap',[['sortClick',['$event']]]]]}}" catchtap="__e"><text style="{{(field=='sales'?'color:'+$root.m0:'')}}">销量排序</text><text class="iconfont iconshangla" style="{{(field=='sales'&&order=='asc'?'color:'+$root.m1:'')}}"></text><text class="iconfont icondaoxu" style="{{(field=='sales'&&order=='desc'?'color:'+$root.m2:'')}}"></text></view><view data-event-opts="{{[['tap',[['showDrawer',['showRight']]]]]}}" class="search-navbar-item flex-x-center flex-y-center" catchtap="__e">筛选<text class="{{['iconfont iconshaixuan '+(showfilter?'active':'')]}}"></text></view></view></view><uni-drawer class="vue-ref" vue-id="617611aa-1" mode="right" width="{{280}}" data-ref="showRight" data-event-opts="{{[['^change',[['change',['$event','showRight']]]]]}}" bind:change="__e" bind:__l="__l" vue-slots="{{['default']}}"><view class="filter-scroll-view"><scroll-view class="filter-scroll-view-box" scroll-y="true"><view class="search-filter"><view class="filter-title">筛选</view><view class="filter-content-title">商家分类</view><view class="search-filter-content"><view class="filter-item" style="{{(catchecid==oldcid?'color:'+$root.m3+';background:rgba('+$root.m4+',0.1)':'')}}" data-cid="{{oldcid}}" data-event-opts="{{[['tap',[['cateClick',['$event']]]]]}}" catchtap="__e">全部</view><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><view class="filter-item" style="{{(catchecid==item.$orig.id?'color:'+item.m5+';background:rgba('+item.m6+',0.1)':'')}}" data-cid="{{item.$orig.id}}" data-event-opts="{{[['tap',[['cateClick',['$event']]]]]}}" catchtap="__e">{{item.$orig.name}}</view></block></block></view><view class="search-filter-btn"><view data-event-opts="{{[['tap',[['filterReset',['$event']]]]]}}" class="btn" bindtap="__e">重置</view><view data-event-opts="{{[['tap',[['filterConfirm',['$event']]]]]}}" class="btn2" style="{{'background:'+($root.m7)+';'}}" bindtap="__e">确定</view></view></view></scroll-view></view></uni-drawer><view class="ind_business"><view class="ind_buslist" id="datalist"><block wx:for="{{datalist}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><view class="ind_busbox"><view class="flex1 flex-row"><view class="ind_buspic flex0"><image src="{{item.logo}}"></image></view><view class="flex1"><view class="bus_title">{{item.name}}</view><view class="bus_sales">{{"销量："+item.sales}}</view><view class="bus_score"><block wx:for="{{[0,1,2,3,4]}}" wx:for-item="item2" wx:for-index="index2"><image class="img" src="{{'/static/img/star'+(item.comment_score>item2?'2':'')+'.png'}}"></image></block><view class="txt">{{item.comment_score+"分"}}</view></view><block wx:if="{{item.address}}"><view class="bus_address" data-latitude="{{item.latitude}}" data-longitude="{{item.longitude}}" data-company="{{item.name}}" data-address="{{item.address}}" data-event-opts="{{[['tap',[['openLocation',['$event']]]]]}}" catchtap="__e"><image style="width:26rpx;height:26rpx;margin-right:10rpx;" src="/static/img/b_addr.png"></image><text class="x1">{{item.address}}</text><text class="x2">{{item.juli}}</text></view></block><block wx:if="{{item.tel}}"><view class="bus_address" data-phone="{{item.tel}}" data-event-opts="{{[['tap',[['phone',['$event']]]]]}}" catchtap="__e"><image style="width:26rpx;height:26rpx;margin-right:10rpx;" src="/static/img/b_tel.png"></image><text class="x1">{{"联系电话："+item.tel}}</text></view></block></view></view><block wx:if="{{showtype==0}}"><block><view class="flow-stats"><view class="flow-row"><view class="flow-item"><view class="amount">{{"￥"+item.zongliushui}}</view><view class="label">商家总流水</view></view><view class="flow-item"><view class="amount">{{"￥"+item.jinday}}</view><view class="label">今日流水</view></view></view><view class="flow-row"><view class="flow-item"><view class="amount">{{"￥"+item.jinmonth}}</view><view class="label">本月流水</view></view><view class="flow-item"><view class="amount">{{"￥"+item.zuoday}}</view><view class="label">昨日流水</view></view></view></view></block></block></view></block></block><block wx:if="{{nomore}}"><nomore vue-id="617611aa-2" bind:__l="__l"></nomore></block><block wx:if="{{nodata}}"><nodata vue-id="617611aa-3" bind:__l="__l"></nodata></block></view></view><block wx:if="{{buydialogShow}}"><buydialog vue-id="617611aa-4" proid="{{proid}}" menuindex="{{menuindex}}" data-event-opts="{{[['^addcart',[['addcart']]],['^buydialogChange',[['buydialogChange']]]]}}" bind:addcart="__e" bind:buydialogChange="__e" bind:__l="__l"></buydialog></block></block></block><block wx:if="{{loading}}"><loading vue-id="617611aa-5" bind:__l="__l"></loading></block><dp-tabbar vue-id="617611aa-6" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="617611aa-7" data-ref="popmsg" bind:__l="__l"></popmsg></view>