<view class="container"><block wx:if="{{isload}}"><block><view class="couponbg" style="{{'background:'+('linear-gradient(90deg,'+$root.m0+' 0%,rgba('+$root.m1+',0.8) 100%)')+';'}}"></view><view class="orderinfo"><block wx:if="{{record.id}}"><block><view class="topitem"><block wx:if="{{record.type==1}}"><view class="f1" style="{{'color:'+($root.m2)+';'}}"><text style="font-size:32rpx;">￥</text><text class="t1">{{record.money}}</text></view></block><block wx:else><block wx:if="{{record.type==10}}"><view class="f1" style="{{'color:'+($root.m3)+';'}}"><text class="t1">{{record.discount/10}}</text><text style="font-size:32rpx;">折</text></view></block><block wx:else><block wx:if="{{record.type==2}}"><view class="f1" style="{{'color:'+($root.m4)+';'}}">礼品券</view></block><block wx:else><block wx:if="{{record.type==3}}"><view class="f1" style="{{'color:'+($root.m5)+';'}}"><text class="t1">{{record.limit_count}}</text><text class="t2">次</text></view></block><block wx:else><block wx:if="{{record.type==4}}"><view class="f1" style="{{'color:'+($root.m6)+';'}}">抵运费</view></block><block wx:else><block wx:if="{{record.type==5}}"><view class="f1" style="{{'color:'+($root.m7)+';'}}">餐饮券</view></block></block></block></block></block></block><view class="f2"><view class="t1">{{record.couponname}}</view><block wx:if="{{record.type==1||record.type==4||record.type==5}}"><view class="t2"><block wx:if="{{record.minprice>0}}"><text>{{"满"+record.minprice+"元可用"}}</text></block><block wx:else><text>无门槛</text></block></view></block><block wx:if="{{record.type==2}}"><view class="t2">礼品券</view></block><block wx:if="{{record.type==3}}"><view class="t2">计次券</view></block></view></view><block wx:if="{{coupon.bid!=0}}"><view class="item"><text class="t1">适用商家</text><text class="t2">{{coupon.bname}}</text></view></block><view class="item"><text class="t1">类型</text><block wx:if="{{record.type==1}}"><text class="t2">代金券<block wx:if="{{coupon.isgive==1||coupon.isgive==2}}"><text>（可赠送）</text></block></text></block><block wx:if="{{record.type==10}}"><text class="t2">折扣券<block wx:if="{{coupon.isgive==1||coupon.isgive==2}}"><text>（可赠送）</text></block></text></block><block wx:if="{{record.type==2}}"><text class="t2">礼品券<block wx:if="{{coupon.isgive==1||coupon.isgive==2}}"><text>（可赠送）</text></block></text></block><block wx:if="{{record.type==3}}"><text class="t2">计次券<block wx:if="{{coupon.isgive==1||coupon.isgive==2}}"><text>（可赠送）</text></block></text></block><block wx:if="{{record.type==4}}"><text class="t2">运费抵扣券<block wx:if="{{coupon.isgive==1||coupon.isgive==2}}"><text>（可赠送）</text></block></text></block><block wx:if="{{record.type==5}}"><text class="t2">餐饮券<block wx:if="{{coupon.isgive==1||coupon.isgive==2}}"><text>（可赠送）</text></block></text></block></view><block wx:if="{{record.type==3}}"><block><view class="item"><text class="t1">共计次数</text><text class="t2">{{record.limit_count}}</text></view><view class="item"><text class="t1">已使用次数</text><view class="t2 flex"><view class="flex1">{{record.used_count}}</view><block wx:if="{{record.used_count>0}}"><view style="{{'color:'+($root.m8)+';'}}" data-url="{{'/pagesExt/coupon/record?crid='+record.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">查看使用记录</view></block></view></view><block wx:if="{{record.limit_perday>0}}"><view class="item"><text class="t1">每天限制次数</text><text class="t2">{{record.limit_perday}}</text></view></block></block></block><view class="item"><text class="t1">领取时间</text><text class="t2">{{record.createtime}}</text></view><block wx:if="{{record.status==1}}"><block><view class="item"><text class="t1">使用时间</text><text class="t2">{{record.usetime}}</text></view></block></block><view class="item flex-col"><text class="t1">有效期</text><text class="t2">{{record.starttime+" 至 "+record.endtime}}</text></view><view class="item flex-col"><text class="t1">使用说明</text><view class="t2">{{coupon.usetips}}</view></view><block wx:if="{{record.status==0&&record.hexiaoqr&&coupon.isgive!=2}}"><view class="item flex-col"><text class="t1">核销码</text><view class="flex-x-center"><image style="width:500rpx;height:500rpx;" src="{{record.hexiaoqr}}" data-url="{{record.hexiaoqr}}" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image></view><text class="flex-x-center">到店使用时请出示核销码进行核销</text></view></block></block></block><block wx:else><block><view class="topitem"><block wx:if="{{coupon.type==1}}"><view class="f1" style="{{'color:'+($root.m9)+';'}}"><text style="font-size:32rpx;">￥</text><text class="t1">{{coupon.money}}</text></view></block><block wx:if="{{coupon.type==10}}"><view class="f1" style="{{'color:'+($root.m10)+';'}}"><text class="t1">{{coupon.discount/10}}</text><text style="font-size:32rpx;">折</text></view></block><block wx:else><block wx:if="{{coupon.type==2}}"><view class="f1" style="{{'color:'+($root.m11)+';'}}">礼品券</view></block><block wx:else><block wx:if="{{coupon.type==3}}"><view class="f1" style="{{'color:'+($root.m12)+';'}}"><text class="t1">{{coupon.limit_count}}</text><text class="t2">次</text></view></block><block wx:else><block wx:if="{{coupon.type==4}}"><view class="f1" style="{{'color:'+($root.m13)+';'}}">抵运费</view></block><block wx:else><block wx:if="{{coupon.type==5}}"><view class="f1" style="{{'color:'+($root.m14)+';'}}">餐饮券</view></block></block></block></block></block><view class="f2"><view class="t1">{{coupon.name}}</view><block wx:if="{{coupon.type==1||coupon.type==4||coupon.type==5}}"><view class="t2"><block wx:if="{{coupon.minprice>0}}"><text>{{"满"+coupon.minprice+"元可用"}}</text></block><block wx:else><text>无门槛</text></block></view></block><block wx:if="{{coupon.type==2}}"><view class="t2">礼品券</view></block><block wx:if="{{coupon.type==3}}"><view class="t2">计次券</view></block></view></view><block wx:if="{{coupon.bid!=0}}"><view class="item"><text class="t1">适用商家</text><text class="t2">{{coupon.bname}}</text></view></block><view class="item"><text class="t1">类型</text><block wx:if="{{coupon.type==1}}"><text class="t2">代金券</text></block><block wx:if="{{coupon.type==10}}"><text class="t2">折扣券</text></block><block wx:if="{{coupon.type==2}}"><text class="t2">礼品券</text></block><block wx:if="{{coupon.type==3}}"><text class="t2">计次券</text></block><block wx:if="{{coupon.type==4}}"><text class="t2">运费抵扣券</text></block><block wx:if="{{coupon.type==5}}"><text class="t2">餐饮券</text></block></view><block wx:if="{{coupon.house_status}}"><view class="item"><text class="t1">领取限制</text><text class="t2">一户仅限一次</text></view></block><block wx:if="{{coupon.type==3}}"><block><view class="item"><text class="t1">共计次数</text><text class="t2">{{coupon.limit_count+"次"}}</text></view><block wx:if="{{coupon.limit_perday>0}}"><block><view class="item"><text class="t1">每天限制使用</text><text class="t2">{{coupon.limit_perday+"次"}}</text></view></block></block></block></block><block wx:if="{{coupon.price>0}}"><block><view class="item"><text class="t1">所需金额</text><text class="t2">{{"￥"+coupon.price}}</text></view></block></block><block wx:if="{{coupon.score>0}}"><block><view class="item"><text class="t1">{{"所需"+$root.m15}}</text><text class="t2">{{coupon.score+$root.m16}}</text></view></block></block><view class="item"><text class="t1">活动时间</text><text class="t2">{{coupon.starttime+" ~ "+coupon.endtime}}</text></view><view class="item"><text class="t1">有效期</text><block wx:if="{{coupon.yxqtype==1}}"><block><text class="t2">{{coupon.yxqtime}}</text></block></block><block wx:else><block wx:if="{{coupon.yxqtype==2}}"><block><text class="t2">{{"领取后"+coupon.yxqdate+"天内有效"}}</text></block></block><block wx:else><block wx:if="{{coupon.yxqtype==3}}"><block><text class="t2">{{"领取后"+coupon.yxqdate+"天内有效（次日0点生效）"}}</text></block></block></block></block></view><view class="item"><text class="t1">使用说明</text><view class="t2">{{coupon.usetips}}</view></view></block></block></view><block wx:if="{{!record.id}}"><view class="btn-add" style="{{'background:'+('linear-gradient(90deg,'+$root.m17+' 0%,rgba('+$root.m18+',0.8) 100%)')+';'}}" data-id="{{coupon.id}}" data-price="{{coupon.price}}" data-score="{{coupon.score}}" data-event-opts="{{[['tap',[['getcoupon',['$event']]]]]}}" bindtap="__e">{{coupon.price>0?'立即购买':coupon.score>0?'立即兑换':'立即领取'}}</view></block><block wx:if="{{mid==record.mid}}"><block><block wx:if="{{coupon.isgive!=2}}"><block><block wx:if="{{record.id&&(coupon.type==1||coupon.type==10)&&record.status==0}}"><block><block wx:if="{{$root.m19}}"><view class="btn-add" style="{{'background:'+('linear-gradient(90deg,'+$root.m20+' 0%,rgba('+$root.m21+',0.8) 100%)')+';'}}" data-url="{{'/shopPackage/shop/prolist?cpid='+record.couponid+(coupon.bid?'&bid='+coupon.bid:'')}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e">去使用</view></block><block wx:if="{{coupon.fwtype==4}}"><view class="btn-add" style="{{'background:'+('linear-gradient(90deg,'+$root.m22+' 0%,rgba('+$root.m23+',0.8) 100%)')+';'}}" data-url="{{'/yuyue/prolist?cpid='+record.couponid+(coupon.bid?'&bid='+coupon.bid:'')}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e">去使用</view></block></block></block><block wx:if="{{record.id&&coupon.type==3&&record.status==0&&record.yuyue_proid>0}}"><view class="btn-add" style="{{'background:'+('linear-gradient(90deg,'+$root.m24+' 0%,rgba('+$root.m25+',0.8) 100%)')+';'}}" data-url="{{'/yuyue/product?id='+record.yuyue_proid}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e">去预约</view></block></block></block><block wx:if="{{record.id&&record.status==0&&!record.from_mid&&(coupon.isgive==1||coupon.isgive==2)}}"><block><block wx:if="{{$root.m26=='app'}}"><view class="btn-add" style="{{'background:'+('linear-gradient(90deg,'+$root.m27+' 0%,rgba('+$root.m28+',0.8) 100%)')+';'}}" data-id="{{record.id}}" data-event-opts="{{[['tap',[['shareapp',['$event']]]]]}}" bindtap="__e">转赠好友</view></block><block wx:else><block wx:if="{{$root.m29=='mp'}}"><view class="btn-add" style="{{'background:'+('linear-gradient(90deg,'+$root.m30+' 0%,rgba('+$root.m31+',0.8) 100%)')+';'}}" data-id="{{record.id}}" data-event-opts="{{[['tap',[['sharemp',['$event']]]]]}}" bindtap="__e">转赠好友</view></block><block wx:else><block wx:if="{{$root.m32=='h5'}}"><view class="btn-add" style="{{'background:'+('linear-gradient(90deg,'+$root.m33+' 0%,rgba('+$root.m34+',0.8) 100%)')+';'}}" data-id="{{record.id}}" data-event-opts="{{[['tap',[['sharemp',['$event']]]]]}}" bindtap="__e">转赠好友</view></block><block wx:else><button class="btn-add" style="{{'background:'+('linear-gradient(90deg,'+$root.m35+' 0%,rgba('+$root.m36+',0.8) 100%)')+';'}}" open-type="share" data-id="{{record.id}}">转赠好友</button></block></block></block></block></block></block></block><block wx:else><block><block wx:if="{{(coupon.isgive==1||coupon.isgive==2)&&opt.pid==record.mid&&opt.pid>0}}"><view class="btn-add" style="{{'background:'+('linear-gradient(90deg,'+$root.m37+' 0%,rgba('+$root.m38+',0.8) 100%)')+';'}}" data-id="{{record.id}}" data-event-opts="{{[['tap',[['receiveCoupon',['$event']]]]]}}" bindtap="__e">立即领取</view></block></block></block><view class="text-center" style="margin-top:40rpx;line-height:60rpx;" data-url="/pages/index/index" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><text>返回首页</text></view></block></block><block wx:if="{{loading}}"><loading vue-id="14ac9edc-1" bind:__l="__l"></loading></block><dp-tabbar vue-id="14ac9edc-2" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="14ac9edc-3" data-ref="popmsg" bind:__l="__l"></popmsg></view>