<view class="container"><dd-tab vue-id="2b20015e-1" itemdata="{{['进行中('+jxz_num+')','未开始('+wks_num+')','已结束('+yjs_num+')']}}" itemst="{{['2','1','3']}}" st="{{st}}" isfixed="{{true}}" data-event-opts="{{[['^changetab',[['changetab']]]]}}" bind:changetab="__e" bind:__l="__l"></dd-tab><view style="width:100%;height:100rpx;"></view><block wx:if="{{isload}}"><block><view class="topsearch flex-y-center"><view class="f1 flex-y-center"><image class="img" src="{{pre_url+'/static/img/search_ico.png'}}"></image><input placeholder="输入关键字搜索" placeholder-style="font-size:24rpx;color:#C2C2C2" data-event-opts="{{[['confirm',[['searchConfirm',['$event']]]]]}}" value="{{keyword}}" bindconfirm="__e"/></view></view><view class="coupon-list"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="coupon"><view class="order-box"><view class="content" style="border-bottom:none;"><view class="pt_left"><view class="pt_left-content"><block wx:if="{{item.$orig.type==1}}"><view class="f1" style="{{'color:'+(item.m0)+';'}}"><text class="t0">￥</text><text class="t1">{{item.$orig.money}}</text></view></block><block wx:if="{{item.$orig.type==10}}"><view class="f1" style="{{'color:'+(item.m1)+';'}}"><text class="t1">{{item.$orig.discount/10}}</text><text class="t0">折</text></view></block><block wx:if="{{item.$orig.type==3}}"><view class="f1" style="{{'color:'+(item.m2)+';'}}"><text class="t1">{{item.$orig.limit_count}}</text><text class="t2">次</text></view></block><block wx:if="{{item.$orig.type==5}}"><view class="f1" style="{{'color:'+(item.m3)+';'}}"><text class="t0">￥</text><text class="t1">{{item.$orig.money}}</text></view></block><block wx:if="{{item.$orig.type==6}}"><view class="f1" style="{{'color:'+(item.m4)+';'}}"><text class="t0">￥</text><text class="t1">{{item.$orig.money}}</text></view></block><block wx:if="{{item.$orig.type!=1&&item.$orig.type!=10&&item.$orig.type!=3&&item.$orig.type!=5&&item.$orig.type!=6}}"><block><view class="f1" style="{{'color:'+(item.m5)+';'}}">{{item.$orig.type_txt}}</view></block></block><block wx:if="{{item.$orig.type==1||item.$orig.type==4||item.$orig.type==5||item.$orig.type==10||item.$orig.type==6}}"><view class="f2" style="{{'color:'+(item.m6)+';'}}"><block wx:if="{{item.$orig.minprice>0}}"><text>{{"满"+item.$orig.minprice+"元可用"}}</text></block><block wx:else><text>无门槛</text></block></view></block></view></view><view class="pt_right"><view class="f1"><view class="t1">{{item.$orig.name}}</view><text class="t2" style="{{'background:'+('rgba('+item.m7+',0.1)')+';'+('color:'+(item.m8)+';')}}">{{item.$orig.type_txt}}</text><block wx:if="{{!item.$orig.from_mid&&(item.$orig.isgive==1||item.$orig.isgive==2)}}"><text class="t2" style="{{'background:'+('rgba('+item.m9+',0.1)')+';'+('color:'+(item.m10)+';')}}">可赠送</text></block><view class="t3" style="{{(item.$orig.bid>0?'margin-top:0':'margin-top:10rpx')}}">{{"有效期至 "+item.$orig.endtime}}</view></view></view></view><view class="op"><block wx:if="{{item.$orig.status=='已结束'}}"><text class="flex1" style="color:red;margin-left:20rpx;">{{item.$orig.status}}</text></block><block wx:else><text class="flex1" style="color:green;margin-left:20rpx;">{{item.$orig.status}}</text></block><block wx:if="{{item.$orig.bid==0}}"><view class="btn2" data-url="{{'/adminExt/member/index?coupon=1'+'&name='+item.$orig.name+'&id='+item.$orig.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">推送</view></block><view class="btn2" data-url="{{'edit?id='+item.$orig.id+'&type=1'}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">编辑</view><view class="btn2" data-id="{{item.$orig.id}}" data-event-opts="{{[['tap',[['todel',['$event']]]]]}}" bindtap="__e">删除</view></view></view></view></block></view><view class="bottom-but-view notabbarbot"><button class="savebtn" style="{{('background:linear-gradient(90deg,'+$root.m11+' 0%,rgba('+$root.m12+',1) 100%)')}}" data-url="/adminExt/coupon/edit?type=2" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">{{"添加"+$root.m13}}</button></view><block wx:if="{{nomore}}"><nomore vue-id="2b20015e-2" bind:__l="__l"></nomore></block><block wx:if="{{loading}}"><loading vue-id="2b20015e-3" bind:__l="__l"></loading></block><block wx:if="{{nodata}}"><nodata vue-id="2b20015e-4" bind:__l="__l"></nodata></block><view style="width:100%;height:calc(160rpx + env(safe-area-inset-bottom));"></view></block></block></view>