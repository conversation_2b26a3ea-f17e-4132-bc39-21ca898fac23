<view class="danshujiang-container data-v-160f3988"><view class="reward-overview data-v-160f3988"><view class="overview-card data-v-160f3988"><view class="card-header data-v-160f3988"><text class="title data-v-160f3988">当前奖励进度</text><text class="{{['mode-tag','data-v-160f3988',rewardMode]}}">{{rewardModeName}}</text></view><view class="progress-section data-v-160f3988"><view class="current-order data-v-160f3988"><text class="number data-v-160f3988">{{currentOrderCount}}</text><text class="label data-v-160f3988">当前单数</text></view><view class="progress-bar-wrapper data-v-160f3988"><view class="progress-bar data-v-160f3988"><view class="progress data-v-160f3988" style="{{'width:'+(progressWidth+'%')+';'}}"><view class="progress-dot data-v-160f3988"></view></view><block wx:for="{{milestones}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="{{['milestone','data-v-160f3988',(currentOrderCount>=item.orderNum)?'active':'',(currentOrderCount===item.orderNum)?'current':'']}}" style="{{'left:'+(item.position+'%')+';'}}"><view class="milestone-dot data-v-160f3988"></view><view class="milestone-info data-v-160f3988"><text class="milestone-num data-v-160f3988">{{item.orderNum+"单"}}</text></view></view></block></view></view></view></view></view><view class="stats-overview data-v-160f3988"><view class="stats-card data-v-160f3988"><view class="stats-title data-v-160f3988">总统计数据</view><view class="stats-grid data-v-160f3988"><view class="stats-item data-v-160f3988"><text class="amount data-v-160f3988">{{"¥"+totalReward}}</text><text class="label data-v-160f3988">总奖励金额</text></view><view class="stats-item data-v-160f3988"><text class="amount data-v-160f3988">{{"¥"+rewardedAmount}}</text><text class="label data-v-160f3988">已发放奖励</text></view><view class="stats-item data-v-160f3988"><text class="amount data-v-160f3988">{{"¥"+pendingReward}}</text><text class="label data-v-160f3988">待发放奖励</text></view><view class="stats-item data-v-160f3988"><text class="amount data-v-160f3988">{{"¥"+(pendingClaim||'0.00')}}</text><text class="label data-v-160f3988">待领取奖励</text></view></view></view></view><view class="rules-section data-v-160f3988"><view class="section-title data-v-160f3988"><text class="title-text data-v-160f3988">奖励规则</text></view><view class="rules-content data-v-160f3988"><block wx:if="{{rewardMode==='normal'}}"><block class="data-v-160f3988"><block wx:for="{{allRules}}" wx:for-item="rule" wx:for-index="index" wx:key="index"><view class="rule-item data-v-160f3988"><view class="rule-icon data-v-160f3988">{{index+1}}</view><view class="rule-detail data-v-160f3988"><text class="order-num data-v-160f3988">{{"第"+rule.order_num+"单"}}</text><text class="reward-rate data-v-160f3988">{{rule.reward_rate+"%奖励"}}</text></view></view></block></block></block><block wx:if="{{rewardMode==='ladder'}}"><block class="data-v-160f3988"><view class="ladder-rules data-v-160f3988"><block wx:for="{{allRules}}" wx:for-item="rule" wx:for-index="index" wx:key="index"><view class="{{['ladder-item','data-v-160f3988',(currentOrderCount>=rule.order_num)?'active':'']}}"><view class="ladder-step data-v-160f3988"><text class="step-range data-v-160f3988">{{rule.order_num+"单"}}</text><text class="step-rate data-v-160f3988">{{rule.reward_rate+"%"}}</text></view></view></block></view></block></block><block wx:if="{{rewardMode==='cycle'}}"><block class="data-v-160f3988"><view class="cycle-rules data-v-160f3988"><view class="cycle-progress data-v-160f3988"><view class="cycle-indicator data-v-160f3988" style="{{'transform:'+('rotate('+$root.m0+'deg)')+';'}}"></view><block wx:for="{{$root.l0}}" wx:for-item="rule" wx:for-index="index" wx:key="index"><view class="{{['cycle-step','data-v-160f3988',(currentStage&&currentStage.order_num&&currentStage.order_num===rule.$orig.order_num)?'active':'']}}" style="{{'transform:'+('rotate('+index*(360/rule.g0)+'deg) translateY(-120rpx) rotate(-'+index*(360/rule.g1)+'deg)')+';'}}"><text class="step-num data-v-160f3988">{{rule.$orig.order_num+"单"}}</text><text class="step-rate data-v-160f3988">{{rule.$orig.reward_rate+"%"}}</text></view></block></view><view class="cycle-info data-v-160f3988"><text class="cycle-text data-v-160f3988">{{cycle_num+"单为一个循环"}}</text><text class="current-cycle data-v-160f3988">{{"第"+$root.g2+"轮 第"+((currentOrderCount-1)%cycle_num+1)+"单"}}</text></view></view></block></block><block wx:if="{{currentStage}}"><view class="current-stage data-v-160f3988"><view class="stage-info data-v-160f3988"><text class="label data-v-160f3988">当前阶段</text><text class="value data-v-160f3988">{{(currentStage.order_num||0)+"单 - "+(currentStage.reward_rate||0)+"%"}}</text></view><block wx:if="{{nextStage}}"><view class="stage-info data-v-160f3988"><text class="label data-v-160f3988">下一阶段</text><text class="value data-v-160f3988">{{(nextStage.order_num||0)+"单 - "+(nextStage.reward_rate||0)+"%"}}</text></view></block></view></block></view></view><view class="records-section data-v-160f3988"><view class="section-title data-v-160f3988"><text class="title-text data-v-160f3988">奖励记录</text></view><view class="records-list data-v-160f3988"><block wx:for="{{rewardRecords}}" wx:for-item="record" wx:for-index="index" wx:key="index"><view class="record-item data-v-160f3988"><view class="record-main data-v-160f3988"><view class="order-info data-v-160f3988"><text class="order-id data-v-160f3988">{{"订单 #"+record.order_id}}</text><text class="order-amount data-v-160f3988">{{"¥"+record.order_amount}}</text></view><view class="reward-info data-v-160f3988"><text class="reward-amount data-v-160f3988">{{"+¥"+record.reward_amount}}</text><text class="reward-rate data-v-160f3988">{{record.reward_rate+"%"}}</text></view></view><view class="{{['record-status','data-v-160f3988',record.status===1?'success':'pending']}}">{{''+(record.status===1?'已发放':'待发放')+''}}</view></view></block></view></view><view class="reward-details-section data-v-160f3988"><view class="section-title data-v-160f3988"><text class="title-text data-v-160f3988">奖励详情</text></view><view class="reward-details-list data-v-160f3988"><block wx:for="{{rewardDetails}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="detail-item data-v-160f3988"><view class="detail-header data-v-160f3988"><text class="order-title data-v-160f3988">{{"订单 #"+item.order_id}}</text><text class="order-num data-v-160f3988">{{"第"+item.order_num_actual+"单"}}</text></view><view class="detail-content data-v-160f3988"><view class="calculation data-v-160f3988"><text class="calc-text data-v-160f3988">{{item.calculation||'订单金额 '+item.order_amount+' × 奖励比例 '+item.reward_rate+'% = '+item.reward_amount}}</text></view><view class="detail-amount data-v-160f3988"><text class="amount-value data-v-160f3988">{{"¥"+item.reward_amount}}</text><view data-event-opts="{{[['tap',[['claimReward',['$0'],[[['rewardDetails','',index]]]]]]]}}" class="{{['claim-btn','data-v-160f3988',(!item.is_claimed&&!item.is_paid)?'can-claim':'',(item.is_claimed&&!item.is_paid)?'pending':'',(item.is_paid)?'claimed':'']}}" bindtap="__e"><block wx:if="{{!item.is_claimed&&!item.is_paid}}"><text class="data-v-160f3988">领取</text></block><block wx:else><block wx:if="{{item.is_claimed&&!item.is_paid}}"><text class="data-v-160f3988">待发放</text></block><block wx:else><text class="data-v-160f3988">已发放</text></block></block></view></view></view></view></block><block wx:if="{{$root.g3===0}}"><nodata vue-id="c7dfa5e4-1" class="data-v-160f3988" bind:__l="__l"></nodata></block></view></view><block wx:if="{{nodata}}"><nodata vue-id="c7dfa5e4-2" class="data-v-160f3988" bind:__l="__l"></nodata></block><block wx:if="{{nomore}}"><nomore vue-id="c7dfa5e4-3" class="data-v-160f3988" bind:__l="__l"></nomore></block><block wx:if="{{loading}}"><loading vue-id="c7dfa5e4-4" class="data-v-160f3988" bind:__l="__l"></loading></block></view>