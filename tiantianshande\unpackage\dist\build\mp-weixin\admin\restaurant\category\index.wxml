<view><view class="container" id="datalist"><view><view class="info-item"><view class="t1">分类名称</view><view class="t2">店内</view><view class="t2">外卖</view><view class="t2">预定</view><view class="t2">排序</view></view><block wx:for="{{datalist}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="info-item"><view class="t1">{{item.name}}<block wx:if="{{item.status==0}}"><text style="color:#DBAA83;">(隐藏)</text></block></view><view class="t2"><block wx:if="{{item.is_shop==1}}"><text>开</text></block><block wx:else><text>关</text></block></view><view class="t2"><block wx:if="{{item.is_takeaway==1}}"><text>开</text></block><block wx:else><text>关</text></block></view><view class="t2"><block wx:if="{{item.is_booking==1}}"><text>开</text></block><block wx:else><text>关</text></block></view><view class="t2">{{item.sort}}</view><image class="t3" data-url="{{'edit?id='+item.id}}" src="/static/img/arrowright.png" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"></image></view></block></view></view><block wx:if="{{nomore}}"><nomore vue-id="50db491e-1" bind:__l="__l"></nomore></block><block wx:if="{{nodata}}"><nodata vue-id="50db491e-2" bind:__l="__l"></nodata></block></view>