<view class="container"><view class="filter-container"><view data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" class="filter-item" bindtap="__e"><text class="filter-label">规则：</text><text class="filter-value">{{selectedRuleName||'全部'}}</text><text class="filter-arrow">▼</text></view></view><view class="records-list"><block wx:if="{{$root.g0===0}}"><view class="no-data"><text>暂无奖励记录</text></view></block><block wx:for="{{recordsList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="record-card"><view class="record-top"><view class="record-title">{{item.rule_name}}</view><view class="{{['record-status',(item.status===1)?'status-success':'',(item.status===0)?'status-pending':'',(item.status===2)?'status-rejected':'']}}">{{''+item.status_text+''}}</view></view><view class="record-info"><view class="record-item"><text class="record-label">统计周期：</text><text class="record-value">{{item.period_text}}</text></view><view class="record-item"><text class="record-label">排名：</text><text class="record-value">{{"第"+item.rank+"名"}}</text></view><view class="record-item"><text class="record-label">统计数量：</text><text class="record-value">{{item.count_num}}</text></view><view class="record-item"><text class="record-label">奖励比例：</text><text class="record-value">{{item.reward_rate+"%"}}</text></view><view class="record-item"><text class="record-label">奖励金额：</text><text class="record-value reward-color">{{"¥"+item.reward_amount}}</text></view><view class="record-item"><text class="record-label">创建时间：</text><text class="record-value">{{item.create_time_text}}</text></view><block wx:if="{{item.status===1}}"><view class="record-item"><text class="record-label">发放时间：</text><text class="record-value">{{item.issue_time_text}}</text></view></block></view></view></block></view><block wx:if="{{$root.g1>0}}"><view class="load-more"><block wx:if="{{loading}}"><text>加载中...</text></block><block wx:else><block wx:if="{{hasMore}}"><text data-event-opts="{{[['tap',[['loadMore',['$event']]]]]}}" bindtap="__e">点击加载更多</text></block><block wx:else><text>没有更多数据了</text></block></block></view></block><block wx:if="{{showRuleSelector}}"><view class="rule-selector"><view data-event-opts="{{[['tap',[['e1',['$event']]]]]}}" class="selector-mask" bindtap="__e"></view><view class="selector-content"><view class="selector-header"><text class="selector-title">选择规则</text><text data-event-opts="{{[['tap',[['e2',['$event']]]]]}}" class="selector-close" bindtap="__e">×</text></view><scroll-view class="selector-body" scroll-y="{{true}}"><view data-event-opts="{{[['tap',[['selectRule',[0,'全部']]]]]}}" class="{{['selector-item',(selectedRuleId===0)?'item-selected':'']}}" bindtap="__e"><text>全部</text><block wx:if="{{selectedRuleId===0}}"><text class="item-check">✓</text></block></view><block wx:for="{{rulesList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['selectRule',['$0','$1'],[[['rulesList','',index,'id']],[['rulesList','',index,'name']]]]]]]}}" class="{{['selector-item',(selectedRuleId===item.id)?'item-selected':'']}}" bindtap="__e"><text>{{item.name}}</text><block wx:if="{{selectedRuleId===item.id}}"><text class="item-check">✓</text></block></view></block></scroll-view></view></view></block></view>