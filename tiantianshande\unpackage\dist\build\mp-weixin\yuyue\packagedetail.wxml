<block wx:if="{{!loading&&packageDetail.id}}"><view class="container"><image class="package-banner" src="{{packageDetail.pic}}" mode="widthFix"></image><view class="section info-section"><view class="package-name">{{packageDetail.name}}</view><view class="package-tags"><block wx:if="{{packageDetail.valid_days>0}}"><text class="tag">{{"有效期 "+packageDetail.valid_days+" 天"}}</text></block><block wx:else><text class="tag">永久有效</text></block></view><view class="package-price" style="{{'color:'+($root.m0)+';'}}">￥<text class="price-value">{{packageDetail.sell_price}}</text></view></view><block wx:if="{{$root.g0}}"><view class="section items-section"><view class="section-title">包含服务项目</view><view class="service-list"><block wx:for="{{packageDetail.items}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="service-item"><image class="service-pic" src="{{item.product_pic||'/static/img/goods-default.png'}}" mode="aspectFill"></image><view class="service-info"><view class="service-name">{{item.product_name}}</view><view class="service-times">{{"x "+item.num+" 次"}}</view></view></view></block></view></view></block><block wx:if="{{packageDetail.business}}"><view class="section business-section"><view class="section-title">适用商家</view><view data-event-opts="{{[['tap',[['gotoBusiness',['$0'],['packageDetail.bid']]]]]}}" class="business-info" bindtap="__e"><image class="business-logo" src="{{packageDetail.business.logo}}" mode="aspectFill"></image><view class="business-details"><view class="business-name">{{packageDetail.business.name}}</view><view class="business-address">{{packageDetail.business.address}}</view></view><text class="arrow">></text></view></view></block><view class="section detail-section"><view class="section-title">套餐详情</view><rich-text class="rich-text-content" nodes="{{packageDetail.content||'暂无详情'}}"></rich-text></view><view class="bottom-bar"><view class="bottom-price"><text>合计：</text><text class="price-symbol" style="{{'color:'+($root.m1)+';'}}">￥</text><text class="price-value-bottom" style="{{'color:'+($root.m2)+';'}}">{{packageDetail.sell_price}}</text></view><button data-event-opts="{{[['tap',[['gotoBuy',['$event']]]]]}}" class="buy-button" style="{{'background:'+($root.m3)+';'}}" bindtap="__e">立即购买</button></view></view></block><block wx:else><block wx:if="{{loading}}"><view class="loading-container"><text>加载中...</text></view></block><block wx:else><view class="empty-container"><text>未找到套餐信息</text></view></block></block>