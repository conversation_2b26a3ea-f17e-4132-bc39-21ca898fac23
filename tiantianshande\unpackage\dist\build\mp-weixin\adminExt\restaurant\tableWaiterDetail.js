require('../common/vendor.js');(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["adminExt/restaurant/tableWaiterDetail"],{"0e99":function(t,n,a){"use strict";a.r(n);var e=a("8662"),o=a.n(e);for(var i in e)["default"].indexOf(i)<0&&function(t){a.d(n,t,(function(){return e[t]}))}(i);n["default"]=o.a},"646a":function(t,n,a){"use strict";var e=a("77c0"),o=a.n(e);o.a},"77c0":function(t,n,a){},8662:function(t,n,a){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;var e=getApp(),o={data:function(){return{opt:{},loading:!1,isload:!1,pre_url:e.globalData.pre_url,detail:{},order:{},orderGoods:[],business:{},nindex:0,orderGoodsSum:0,numArr:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20]}},onLoad:function(t){this.opt=e.getopts(t),this.getdata()},onShow:function(){this.getdata()},onPullDownRefresh:function(){this.getdata()},methods:{getdata:function(){var t=this;t.loading=!0,e.get("ApiAdminRestaurantTable/detail",{id:t.opt.id},(function(n){t.loading=!1,0!=n.status?(t.detail=n.info,t.order=n.order,t.orderGoods=n.order_goods,t.orderGoodsSum=n.order_goods_sum,t.loaded()):e.alert(n.msg,(function(){e.goback()}))}))},subform:function(t){var n=this,a=t.detail.value;return a.tableId=n.opt.id,a.renshu=n.numArr[n.nindex],a.renshu<=0?(e.error("请选择人数"),!1):0==a.tableId?(e.error("请选择餐桌"),!1):(n.loading=!0,void e.post("ApiAdminRestaurantShopOrder/add",{info:a},(function(t){n.loading=!1,0!=t.status?e.alert(t.msg,(function(){n.getdata()})):e.alert(t.msg)})))},clean:function(){var t=this,n=t.opt.id;t.loading=!0,e.post("ApiAdminRestaurantTable/clean",{tableId:n},(function(n){t.loading=!1,0!=n.status?e.alert(n.msg,(function(){t.getdata()})):e.alert(n.msg)}))},cleanOver:function(){var t=this,n=t.opt.id;t.loading=!0,e.post("ApiAdminRestaurantTable/cleanOver",{tableId:n},(function(n){t.loading=!1,0!=n.status?e.alert(n.msg,(function(){t.getdata()})):e.alert(n.msg)}))},close:function(){var t=this,n=t.opt.id;e.confirm("确定要关闭订单吗？关闭后会自动切换餐桌状态为空闲，请及时通知厨房取消相关菜品",(function(){t.loading=!0,e.post("ApiAdminRestaurantTable/closeOrder",{tableId:n},(function(n){t.loading=!1,0!=n.status?1==n.status&&(e.success(n.msg),t.getdata()):e.alert(n.msg)}))}))},numChange:function(t){this.nindex=t.detail.value},startpause:function(t){var n=this,a=t.currentTarget.dataset.status,o=n.opt.id;n.loading=!0,e.post("ApiAdminRestaurantTable/timingPause",{tableid:o,type:a},(function(t){n.loading=!1,0!=t.status?1==t.status&&(e.success(t.msg),n.getdata()):e.alert(t.msg)}))}}};n.default=o},be50:function(t,n,a){"use strict";(function(t,n){var e=a("47a9");a("06e9");e(a("3240"));var o=e(a("fe79"));t.__webpack_require_UNI_MP_PLUGIN__=a,n(o.default)}).call(this,a("3223")["default"],a("df3c")["createPage"])},fd02:function(t,n,a){"use strict";a.d(n,"b",(function(){return o})),a.d(n,"c",(function(){return i})),a.d(n,"a",(function(){return e}));var e={loading:function(){return a.e("components/loading/loading").then(a.bind(null,"ceaa"))},dpTabbar:function(){return a.e("components/dp-tabbar/dp-tabbar").then(a.bind(null,"b875"))}},o=function(){var t=this,n=t.$createElement,a=(t._self._c,t.isload&&2==t.detail.status?t.dateFormat(t.order.createtime):null),e=t.isload&&2==t.detail.status&&t.detail.timing_fee_type&&t.detail.timing_fee_type>0?t.__map(t.detail.timing_log,(function(n,a){var e=t.__get_orig(n),o=t.detail.timing_log.length;return{$orig:e,g0:o}})):null,o=t.isload&&2==t.detail.status?t.orderGoods.length:null,i=t.isload&&2==t.detail.status&&o>0?t.__map(t.orderGoods,(function(n,a){var e=t.__get_orig(n),o=n.ggtext&&n.ggtext.length;return{$orig:e,g2:o}})):null;t.$mp.data=Object.assign({},{$root:{m0:a,l0:e,g1:o,l1:i}})},i=[]},fe79:function(t,n,a){"use strict";a.r(n);var e=a("fd02"),o=a("0e99");for(var i in o)["default"].indexOf(i)<0&&function(t){a.d(n,t,(function(){return o[t]}))}(i);a("646a");var r=a("828b"),d=Object(r["a"])(o["default"],e["b"],e["c"],!1,null,null,null,!1,e["a"],void 0);n["default"]=d.exports}},[["be50","common/runtime","common/vendor"]]]);