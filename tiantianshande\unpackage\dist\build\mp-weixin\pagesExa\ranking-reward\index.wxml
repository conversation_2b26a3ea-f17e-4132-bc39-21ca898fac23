<view class="container"><view class="stats-container"><view class="stats-card"><view class="stats-title">累计获得奖励</view><view class="stats-value">{{statsData.total_reward||'0.00'}}</view></view><view class="stats-flex"><view class="stats-item"><view class="stats-subtitle">已发放奖励</view><view class="stats-subvalue">{{statsData.issued_reward||'0.00'}}</view></view><view class="stats-item"><view class="stats-subtitle">待发放奖励</view><view class="stats-subvalue">{{statsData.pending_reward||'0.00'}}</view></view></view></view><view class="rules-container"><view class="section-title">奖励规则</view><block wx:if="{{$root.g0===0}}"><view class="no-data"><text>暂无可用奖励规则</text></view></block><block wx:for="{{rulesList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['goToRulePreview',['$0'],[[['rulesList','',index,'id']]]]]]]}}" class="rule-card" bindtap="__e"><view class="rule-top"><view class="rule-name">{{item.name}}</view><view class="rule-tag">{{item.reward_mode_name}}</view></view><view class="rule-info"><view class="rule-item"><text class="rule-label">排名类型：</text><text class="rule-value">{{item.rank_type_name}}</text></view><view class="rule-item"><text class="rule-label">奖励比例：</text><text class="rule-value">{{item.total_reward_rate+"%"}}</text></view><view class="rule-item"><text class="rule-label">奖励排名：</text><text class="rule-value">{{"前"+item.reward_top_num+"名"}}</text></view></view><view class="rule-button">查看详情</view></view></block></view><view class="records-container"><view class="section-title-row"><view class="section-title">最近奖励记录</view><view data-event-opts="{{[['tap',[['goToRecords',['$event']]]]]}}" class="view-more" bindtap="__e">查看更多</view></view><block wx:if="{{$root.g1}}"><view class="no-data"><text>暂无奖励记录</text></view></block><block wx:for="{{statsData.recent_records}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="record-card"><view class="record-top"><view class="record-title">{{item.rule_name}}</view><view class="{{['record-status',(item.status===1)?'status-success':'',(item.status===0)?'status-pending':'',(item.status===2)?'status-rejected':'']}}">{{''+item.status_text+''}}</view></view><view class="record-info"><view class="record-period">{{item.period_text}}</view><view class="record-rank">{{"排名：第"+item.rank+"名"}}</view></view><view class="record-amount">{{"+"+item.reward_amount}}</view></view></block></view></view>