<view class="container"><block wx:if="{{isload}}"><block><view class="view-show"><view class="topbannerbg" style="{{(sysset.banner_show&&business.pic?'background:url('+business.pic+') center no-repeat;background-size:cover;':'')}}"></view><view class="topbannerbg2"></view><view class="topbanner"><view class="left"><image class="img" src="{{business.logo}}"></image></view><view class="right"><view class="f1">{{business.name}}</view><view class="f2">{{business.desc}}</view></view></view><view class="navtab"><view class="{{['item',st==0?'on':'']}}" data-st="0" data-event-opts="{{[['tap',[['changetab',['$event']]]]]}}" bindtap="__e">点外卖<view class="after" style="{{'background:'+($root.m0)+';'}}"></view></view><block wx:if="{{sysset.business_info_show}}"><view class="{{['item',st==1?'on':'']}}" data-st="1" data-event-opts="{{[['tap',[['changetab',['$event']]]]]}}" bindtap="__e">商家信息<view class="after" style="{{'background:'+($root.m1)+';'}}"></view></view></block><block wx:if="{{sysset.comment_show}}"><view class="{{['item',st==2?'on':'']}}" data-st="2" data-event-opts="{{[['tap',[['changetab',['$event']]]]]}}" bindtap="__e">评价<view class="after" style="{{'background:'+($root.m2)+';'}}"></view></view></block></view><block wx:if="{{st==0}}"><view class="content" style="{{'overflow:hidden;display:flex;margin-top:86rpx;'+('height:'+('calc(100% - '+(menuindex>-1?460:360)+'rpx)')+';')}}"><block wx:if="{{$root.g0}}"><scroll-view class="{{['nav_left',menuindex>-1?'tabbarbot':'']}}" scrollWithAnimation="{{animation}}" scroll-y="true"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><view class="{{['nav_left_items',index===currentActiveIndex?'active':'']}}" data-root-item-id="{{item.$orig.id}}" data-root-item-index="{{index}}" data-event-opts="{{[['tap',[['clickRootItem',['$event']]]]]}}" bindtap="__e"><view class="before" style="{{'background:'+(item.m3)+';'}}"></view>{{''+item.$orig.name}}<block wx:if="{{numCat[item.$orig.id]>0}}"><view class="cartnum" style="{{'background:'+(item.m4)+';'}}">{{numCat[item.$orig.id]}}</view></block></view></block></block></scroll-view></block><view class="nav_right"><view class="nav_right-content"><scroll-view class="{{['detail-list',menuindex>-1?'tabbarbot':'']}}" scrollIntoView="{{scrollToViewId}}" scrollWithAnimation="{{animation}}" scroll-y="true" data-event-opts="{{[['scroll',[['scroll',['$event']]]]]}}" bindscroll="__e"><block wx:for="{{$root.l1}}" wx:for-item="detail" wx:for-index="index" wx:key="index"><view class="classification-detail-item"><view class="head" data-id="{{detail.$orig.id}}" id="{{'detail-'+detail.$orig.id}}"><view class="txt">{{detail.$orig.name}}</view></view><view class="product-itemlist"><block wx:for="{{detail.$orig.prolist}}" wx:for-item="item" wx:for-index="index" wx:key="id"><view class="{{['item',item.stock<=0||item.stock_daily<=item.sales_daily?'soldout':'']}}"><view class="product-pic" data-url="{{'product?id='+item.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image class="image" src="{{item.pic}}" mode="widthFix"></image><view class="overlay"><view class="text">售罄</view></view></view><view class="product-info"><view class="p1"><text>{{item.name}}</text></view><view class="p2"><text class="t1" style="{{'color:'+(detail.m5)+';'}}"><text style="font-size:20rpx;padding-right:1px;">￥</text>{{item.sell_price}}</text><block wx:if="{{item.market_price*1>item.sell_price*1}}"><text class="t2">{{"￥"+item.market_price}}</text></block></view><view class="p3"><block wx:if="{{item.sales>0}}"><view class="p3-1"><text style="overflow:hidden;">{{"已售"+item.sales+"件"}}</text></view></block><block wx:if="{{item.limit_start>0}}"><view class="p3-1"><text style="overflow:hidden;">{{item.limit_start+"件起售"}}</text></view></block></view><block wx:if="{{item.stock>0&&item.stock_daily>item.sales_daily}}"><view class="addnum"><block wx:if="{{numtotal[item.id]>0}}"><view class="minus" data-num="-1" data-proid="{{item.id}}" data-stock="{{item.stock}}" data-event-opts="{{[['tap',[['addcart',['$event']]]]]}}" catchtap="__e">-</view></block><block wx:if="{{numtotal[item.id]>0}}"><text class="i">{{numtotal[item.id]}}</text></block><block wx:if="{{item.ggcount>1}}"><view class="plus" data-proid="{{item.id}}" data-stock="{{item.stock}}" data-event-opts="{{[['tap',[['buydialogChange',['$event']]]]]}}" catchtap="__e">+</view></block><block wx:else><view class="plus" data-num="1" data-proid="{{item.id}}" data-ggid="{{item.gglist[0].id}}" data-stock="{{item.stock}}" data-event-opts="{{[['tap',[['addcart',['$event']]]]]}}" catchtap="__e">+</view></block></view></block></view></view></block></view></view></block></scroll-view><block wx:if="{{nodata}}"><nodata vue-id="0694744c-1" bind:__l="__l"></nodata></block></view></view></view></block><block wx:if="{{st==1}}"><view class="content1" style="margin-top:86rpx;padding-top:20rpx;"><view class="item flex-col"><text class="t1">联系电话</text><text class="t2"><block wx:if="{{business.tel}}"><text>{{business.tel}}</text></block><block wx:else><text>暂无</text></block></text></view><view class="item flex-col"><text class="t1">商家地址</text><text class="t2"><block wx:if="{{business.address}}"><text>{{business.address}}</text></block><block wx:else><text>暂无</text></block></text></view><view class="item flex-col"><text class="t1">商家简介</text><text class="t2"><block wx:if="{{business.desc}}"><text>{{business.desc}}</text></block><block wx:else><text>暂无</text></block></text></view><view class="item flex-col"><text class="t1">营业时间</text><text class="t2">{{sysset.start_hours+" 至 "+sysset.end_hours}}</text></view><block wx:if="{{$root.g1}}"><view class="item flex-col"><text class="t1">证照公示</text><view class="flex t2" style="flex-wrap:wrap;padding-top:20rpx;" id="content_picpreview"><block wx:for="{{business.zhengming}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="layui-imgbox"><view class="layui-imgbox-img"><image src="{{item}}" data-url="{{item}}" mode="widthFix" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image></view></view></block></view></view></block></view></block><block wx:if="{{st==2}}"><view class="content2" style="margin-top:86rpx;padding-top:20rpx;"><view class="comment"><block wx:if="{{$root.g2>0}}"><block><block wx:for="{{commentlist}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="item"><view class="f1"><image class="t1" src="{{item.headimg}}"></image><view class="t2">{{item.nickname}}</view><view class="flex1"></view><view class="t3"><block wx:for="{{[0,1,2,3,4]}}" wx:for-item="item2" wx:for-index="index2" wx:key="index2"><image class="img" src="{{'/static/img/star'+(item.score>item2?'2':'')+'.png'}}"></image></block></view></view><view style="color:#777;font-size:22rpx;">{{item.createtime}}</view><view class="f2"><text class="t1">{{item.content}}</text><view class="t2"><block wx:if="{{item.content_pic!=''}}"><block><block wx:for="{{item.content_pic}}" wx:for-item="itemp" wx:for-index="index" wx:key="index"><block><view data-url="{{itemp}}" data-urls="{{item.content_pic}}" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"><image src="{{itemp}}" mode="widthFix"></image></view></block></block></block></block></view></view><block wx:if="{{item.reply_content}}"><view class="f3"><view class="arrow"></view><view class="t1">{{"商家回复："+item.reply_content}}</view></view></block></view></block></block></block><block wx:else><block><nodata data-custom-hidden="{{!(comment_nodata)}}" vue-id="0694744c-2" bind:__l="__l"></nodata></block></block></view></view></block></view><block wx:if="{{buydialogShow}}"><buydialog vue-id="0694744c-3" proid="{{proid}}" menuindex="{{menuindex}}" btntype="1" needaddcart="{{false}}" controller="ApiRestaurantTakeaway" data-event-opts="{{[['^buydialogChange',[['buydialogChange']]],['^addcart',[['afteraddcart']]]]}}" bind:buydialogChange="__e" bind:addcart="__e" bind:__l="__l"></buydialog></block><view style="height:auto;position:relative;"><view style="width:100%;height:100rpx;"></view><view class="{{['footer','flex',menuindex>-1?'tabbarbot':'']}}"><view data-event-opts="{{[['tap',[['handleClickMask',['$event']]]]]}}" class="cart_ico" style="{{'background:'+('linear-gradient(0deg,'+$root.m6+' 0%,rgba('+$root.m7+',0.8) 100%)')+';'}}" catchtap="__e"><image class="img" src="/static/img/cart.png"></image><block wx:if="{{cartList.total>0}}"><view class="cartnum" style="{{'background:'+($root.m8)+';'}}">{{cartList.total}}</view></block></view><view class="text1">合计</view><view class="text2 flex1" style="{{'color:'+($root.m9)+';'}}"><text style="font-size:20rpx;">￥</text>{{totalprice}}</view><block wx:if="{{cartList.leftprice>0}}"><view class="op" style="{{'background:'+('#888')+';'+('width:'+('220rpx')+';')}}">{{"差"+cartList.leftprice+"元起送"}}</view></block><block wx:else><view data-event-opts="{{[['tap',[['gopay',['$event']]]]]}}" class="op" style="{{'background:'+('linear-gradient(270deg,'+$root.m10+' 0%,rgba('+$root.m11+',0.8) 100%)')+';'}}" bindtap="__e">去结算</view></block></view></view><block wx:if="{{cartListShow}}"><view class="{{['popup__container',menuindex>-1?'tabbarbot':'']}}" style="margin-bottom:100rpx;"><view data-event-opts="{{[['tap',[['handleClickMask',['$event']]]]]}}" class="{{['popup__overlay',menuindex>-1?'tabbarbot':'']}}" style="margin-bottom:100rpx;" catchtap="__e"></view><view class="popup__modal" style="min-height:400rpx;padding:0;"><view class="popup__title" style="border-bottom:1px solid #EFEFEF;"><view class="popup__left flex-y-center">打包费<text>{{"￥"+totalpricePack}}</text></view><text class="popup__title-text" style="color:#323232;font-weight:bold;font-size:32rpx;">购物车</text><view data-event-opts="{{[['tap',[['clearShopCartFn',['$event']]]]]}}" class="popup__close flex-y-center" style="color:#999999;font-size:24rpx;" catchtap="__e"><image style="width:24rpx;height:24rpx;margin-right:6rpx;" src="/static/img/del.png"></image>清空</view></view><view class="popup__content" style="padding:0;"><scroll-view class="prolist" scroll-y="{{true}}"><block wx:for="{{$root.l2}}" wx:for-item="cart" wx:for-index="index" wx:key="index"><block><view class="proitem"><image class="pic flex0" src="{{cart.$orig.guige.pic?cart.$orig.guige.pic:cart.$orig.product.pic}}"></image><view class="con"><view class="f1">{{cart.$orig.product.name}}</view><block wx:if="{{cart.$orig.guige.name!='默认规格'}}"><view class="f2">{{cart.$orig.guige.name+cart.$orig.jltitle}}</view></block><view class="f3" style="color:#ff5555;margin-top:10rpx;font-size:28rpx;">{{"￥"+cart.g3}}</view></view><view class="addnum"><view class="minus"><image class="img" src="/static/img/cart-minus.png" data-num="-1" data-proid="{{cart.$orig.proid}}" data-ggid="{{cart.$orig.ggid}}" data-stock="{{cart.$orig.guige.stock}}" data-jltitle="{{cart.$orig.jltitle}}" data-jlprice="{{cart.$orig.jlprice}}" data-event-opts="{{[['tap',[['addcart',['$event']]]]]}}" bindtap="__e"></image></view><text class="i">{{cart.$orig.num}}</text><view class="plus"><image class="img" src="/static/img/cart-plus.png" data-num="1" data-proid="{{cart.$orig.proid}}" data-ggid="{{cart.$orig.ggid}}" data-stock="{{cart.$orig.guige.stock}}" data-jltitle="{{cart.$orig.jltitle}}" data-jlprice="{{cart.$orig.jlprice}}" data-event-opts="{{[['tap',[['addcart',['$event']]]]]}}" bindtap="__e"></image></view></view></view></block></block><block wx:if="{{!$root.g4}}"><block><text class="nopro">暂时没有商品喔~</text></block></block></scroll-view></view></view></view></block></block></block><block wx:if="{{loading}}"><loading vue-id="0694744c-4" bind:__l="__l"></loading></block><dp-tabbar vue-id="0694744c-5" opt="{{opt}}" data-event-opts="{{[['^getmenuindex',[['getmenuindex']]]]}}" bind:getmenuindex="__e" bind:__l="__l"></dp-tabbar></view>