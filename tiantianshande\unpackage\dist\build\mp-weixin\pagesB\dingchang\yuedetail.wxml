<view class="container"><block wx:if="{{isload}}"><block><scroll-view style="height:100%;overflow:scroll;" scrollIntoView="{{scrollToViewId}}" scrollTop="{{scrollTop}}" scroll-y="{{true}}" data-event-opts="{{[['scroll',[['scroll',['$event']]]]]}}" bindscroll="__e"><view id="scroll_view_tab0"><view class="swiper-container"><view class="swiper-item-view"><image class="img" src="{{data.logo}}" mode="scaleToFill"></image></view></view><view class="header"><block wx:if="{{data.totalprice>0}}"><block><view class="price_share"><view class="price"><view class="f1" style="{{'color:'+($root.m0)+';'}}"><text style="font-size:36rpx;">￥</text>{{data.totalprice+"/人"}}</view></view><view data-event-opts="{{[['tap',[['shareClick',['$event']]]]]}}" class="share" bindtap="__e"><image class="img" src="/static/img/share.png"></image><text class="txt">分享</text></view></view><view class="title">{{data.title}}</view><view class="time">{{"活动时间 : "+data.event_date}}</view><view data-event-opts="{{[['tap',[['openMap',['$event']]]]]}}" class="dz" bindtap="__e"><view class="f1 flex1"><view>{{"活动地点: "+data.venues_title+''}}</view></view><image class="f2" src="/static/img/map.png"></image></view></block></block></view><view class="choose"><view class="flex"><text class="tit">{{data.application+1+"人一起"}}</text><text class="tit" style="{{('color:'+$root.m1)}}">{{"仅剩"+data.num+"个名额"}}</text></view><scroll-view style="height:80px;" scroll-x="{{true}}"><view class="img-ll"><image class="img" src="{{data.headimg}}"></image><view class="txt">{{data.nickname}}</view></view><block wx:for="{{data.statistics}}" wx:for-item="m" wx:for-index="n" wx:key="n"><view class="img-ll"><image class="img" src="{{m.headimg}}"></image><view class="txt">{{m.nickname}}</view></view></block></scroll-view></view><view class="choose"><view class="flex"><text class="tit">活动描述</text></view><view class="content">{{data.introduce}}</view></view></view><view style="width:100%;height:140rpx;"></view></scroll-view><view class="{{['bottombar','flex-row',menuindex>-1?'tabbarbot':'notabbarbot']}}"><view class="f1"><view class="item" data-url="/pagesB/theater/orderlist" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image class="img" src="/static/img/shou.png"></image><view class="t1">首页</view></view><button class="item" open-type="contact"><image class="img" src="/static/img/kefu.png"></image><view class="t1">客服</view></button></view><view class="op"><view style="flex:1;"><view data-event-opts="{{[['tap',[['dateClick',['$event']]]]]}}" class="tocart flex-x-center flex-y-center" style="{{'background:'+($root.m2)+';'}}" bindtap="__e">立即支付</view></view></view></view><block wx:if="{{sharetypevisible}}"><view class="popup__container"><view data-event-opts="{{[['tap',[['handleClickMask',['$event']]]]]}}" class="popup__overlay" catchtap="__e"></view><view class="popup__modal" style="height:320rpx;min-height:320rpx;"><view class="popup__content"><view class="sharetypecontent"><block wx:if="{{$root.m3=='app'}}"><view data-event-opts="{{[['tap',[['shareapp',['$event']]]]]}}" class="f1" bindtap="__e"><image class="img" src="/static/img/weixin.png"></image><text class="t1">分享给好友</text></view></block><block wx:else><block wx:if="{{$root.m4=='mp'}}"><view data-event-opts="{{[['tap',[['sharemp',['$event']]]]]}}" class="f1" bindtap="__e"><image class="img" src="/static/img/weixin.png"></image><text class="t1">分享给好友</text></view></block><block wx:else><block wx:if="{{$root.m5=='h5'}}"><view data-event-opts="{{[['tap',[['sharemp',['$event']]]]]}}" class="f1" bindtap="__e"><image class="img" src="/static/img/weixin.png"></image><text class="t1">分享给好友</text></view></block><block wx:else><button class="f1" open-type="share"><image class="img" src="/static/img/weixin.png"></image><text class="t1">分享给好友</text></button></block></block></block><view data-event-opts="{{[['tap',[['showPoster',['$event']]]]]}}" class="f2" bindtap="__e"><image class="img" src="/static/img/sharepic.png"></image><text class="t1">生成分享图片</text></view><block wx:if="{{$root.m6}}"><view data-event-opts="{{[['tap',[['shareScheme',['$event']]]]]}}" class="f1" bindtap="__e"><image class="img" src="/static/img/weixin.png"></image><text class="t1">小程序链接</text></view></block></view></view></view></view></block></block></block><block wx:if="{{loading}}"><loading vue-id="7bc03887-1" bind:__l="__l"></loading></block></view>