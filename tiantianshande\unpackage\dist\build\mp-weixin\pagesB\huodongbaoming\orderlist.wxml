<view class="container"><block wx:if="{{isload}}"><block><dd-tab vue-id="3747c997-1" itemdata="{{['全部','待付款','待使用','已完成','已取消']}}" itemst="{{['all','0','1','3','4']}}" st="{{st}}" isfixed="{{true}}" data-event-opts="{{[['^changetab',[['changetab']]]]}}" bind:changetab="__e" bind:__l="__l"></dd-tab><view style="width:100%;height:100rpx;"></view><view class="topsearch flex-y-center"><view class="f1 flex-y-center"><image class="img" src="{{pre_url+'/static/img/search_ico.png'}}"></image><input placeholder="输入关键字搜索" placeholder-style="font-size:24rpx;color:#C2C2C2" data-event-opts="{{[['confirm',[['searchConfirm',['$event']]]]]}}" value="{{keyword}}" bindconfirm="__e"/></view></view><view class="order-content"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><view class="order-box" data-url="{{'orderdetail?id='+item.$orig.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e"><view class="head"><block wx:if="{{item.$orig.bid!=0}}"><view class="f1" data-url="{{'/pagesExt/business/index?id='+item.$orig.bid}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e"><image src="{{pre_url+'/static/img/ico-shop.png'}}"></image>{{''+item.$orig.binfo.name}}</view></block><block wx:else><view>{{"订单号："+item.$orig.ordernum}}</view></block><view class="flex1"></view><block wx:if="{{item.$orig.status==0}}"><text class="st0">待付款</text></block><block wx:if="{{item.$orig.status==1}}"><text class="st0">待使用</text></block><block wx:if="{{item.$orig.status==3}}"><text class="st4">已完成</text></block><block wx:if="{{item.$orig.status==4}}"><text class="st4">订单已关闭</text></block></view><view class="content" style="border-bottom:none;"><view data-url="{{'product?id='+item.$orig.proid}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e"><image src="{{item.$orig.propic}}"></image></view><view class="detail"><text class="t1">{{item.$orig.proname}}</text><text class="t1"><block wx:if="{{item.$orig.ggname}}"><text>{{item.$orig.ggname+";"}}</text></block><block wx:if="{{item.$orig.num}}"><text>{{"数量:"+item.$orig.num}}</text></block></text><view class="t3"><block wx:if="{{item.$orig.totalprice>0&&item.$orig.totalscore>0}}"><block><text class="x1 flex1">{{"实付金额：￥"+item.$orig.totalprice+" + "+item.$orig.totalscore+''}}</text></block></block><block wx:if="{{item.$orig.totalprice>0&&item.$orig.totalscore==0}}"><block><text class="x1 flex1">{{"实付金额：￥"+item.$orig.totalprice+''}}</text></block></block><block wx:if="{{item.$orig.totalprice==0&&item.$orig.totalscore>0}}"><block><text class="x1 flex1">{{"实付金额："+item.$orig.totalscore+" "+item.m0}}</text></block></block></view></view></view><view class="op"><view class="btn2" data-url="{{'orderdetail?id='+item.$orig.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e">详情</view><block wx:if="{{item.$orig.status==0}}"><block><view class="btn2" data-id="{{item.$orig.id}}" data-event-opts="{{[['tap',[['toclose',['$event']]]]]}}" catchtap="__e">关闭订单</view><view class="btn1" style="{{'background:'+(item.m1)+';'}}" data-url="{{'/pagesExt/pay/pay?id='+item.$orig.payorderid}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e">去付款</view></block></block><block wx:if="{{item.$orig.status==1&&item.$orig.protype==1}}"><block><view class="btn2" data-id="{{item.$orig.id}}" data-event-opts="{{[['tap',[['orderCollect',['$event']]]]]}}" catchtap="__e">确认完成</view></block></block><block wx:if="{{item.$orig.status==3||item.$orig.status==4}}"><block><view class="btn2" data-id="{{item.$orig.id}}" data-event-opts="{{[['tap',[['todel',['$event']]]]]}}" catchtap="__e">删除订单</view></block></block></view></view></block></block></view><block wx:if="{{nomore}}"><nomore vue-id="3747c997-2" bind:__l="__l"></nomore></block><block wx:if="{{nodata}}"><nodata vue-id="3747c997-3" bind:__l="__l"></nodata></block></block></block><block wx:if="{{loading}}"><loading vue-id="3747c997-4" bind:__l="__l"></loading></block><dp-tabbar vue-id="3747c997-5" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="3747c997-6" data-ref="popmsg" bind:__l="__l"></popmsg></view>