<view><view class="navigation"><view class="navcontent" style="{{'margin-top:'+(navigationMenu.top+'px')+';'+('width:'+(navigationMenu.right+'px')+';')}}"><view class="header-location-top" style="{{'height:'+(navigationMenu.height+'px')+';'}}"><view data-event-opts="{{[['tap',[['goBack',['$event']]]]]}}" class="header-back-but" bindtap="__e"><image src="{{pre_url+'/static/img/adminExt/goback.png'}}"></image></view><view class="header-page-title">代客下单</view></view></view></view><view class="content"><view class="itemfirst flex-y-center"><view class="itemfirst-options flex-y-center" data-url="../member/index?type=''" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="flex-y-center"><view class="avat-img-view"><image src="{{merberInfo.headimg?merberInfo.headimg:pre_url+'/static/img/touxiang.png'}}"></image></view><block wx:if="{{merberInfo.id}}"><view class="user-info"><view class="un-text">{{merberInfo.realname?merberInfo.realname:merberInfo.nickname}}</view><view class="tel-text"><text>id :</text>{{merberInfo.id+''}}</view></view></block><block wx:else><view class="user-info"><text class="un-text">请选择会员</text></view></block></view><view class="jiantou-img flex flex-y-center"><image src="{{pre_url+'/static/img/arrowright.png'}}"></image></view></view><view class="itemfirst-options flex-y-center" data-url="/adminExt/order/addmember?type=0" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view>添加会员</view><view class="jiantou-img flex flex-y-center"><image src="{{pre_url+'/static/img/arrowright.png'}}"></image></view></view></view><view class="item flex-col"><view class="flex-y-center input-view"><text class="input-title">联 系 人：</text><input placeholder="请输入联系人的姓名" placeholder-style="color:#626262;font-size:28rpx" data-event-opts="{{[['input',[['__set_model',['','linkman','$event',[]]],['linkmanInput',['$event']]]]]}}" value="{{linkman}}" bindinput="__e"/></view><view class="flex-y-center input-view"><text class="input-title">联系电话：</text><input type="number" placeholder="请输入联系人的手机号" placeholder-style="color:#626262;font-size:28rpx" data-event-opts="{{[['input',[['__set_model',['','tel','$event',[]]],['telInput',['$event']]]]]}}" value="{{tel}}" bindinput="__e"/></view><view class="flex-y-center input-view"><text class="input-title">所在地区：</text><view><uni-data-picker class="vue-ref" vue-id="6a96f2ef-1" localdata="{{items}}" border="{{false}}" placeholder="{{regiondata||'请选择省市区'}}" data-ref="unidatapicker" data-event-opts="{{[['^change',[['regionchange']]]]}}" bind:change="__e" bind:__l="__l"></uni-data-picker></view></view><view class="flex-y-center input-view address-view"><text class="input-title">详细地址：</text><view class="address-chose flex-y-center"><textarea style="width:400rpx;height:100rpx;" placeholder="请输入联系人的地址" placeholder-style="color:#626262;font-size:28rpx" data-event-opts="{{[['input',[['__set_model',['','address','$event',[]]],['addressInput',['$event']]]]]}}" value="{{address}}" bindinput="__e"></textarea><view data-event-opts="{{[['tap',[['selectAddres']]]]}}" class="but-class" style="{{'background:'+('linear-gradient(-90deg,'+$root.m0+' 0%,rgba('+$root.m1+',0.8) 100%)')+';'}}" bindtap="__e">选择</view></view></view></view><view class="item"><view class="title-view flex-y-center"><view>商品列表</view><view data-event-opts="{{[['tap',[['addshop',['$event']]]]]}}" class="but-class" style="{{'background:'+('linear-gradient(-90deg,'+$root.m2+' 0%,rgba('+$root.m3+',0.8) 100%)')+';'}}" bindtap="__e">添加商品</view></view><view class="product"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index2" wx:key="index2"><view><view class="item flex"><view class="img"><block wx:if="{{item.$orig.guige.pic}}"><image src="{{item.$orig.guige.pic}}"></image></block><block wx:else><image src="{{item.$orig.product.pic}}"></image></block></view><view class="info flex1"><view class="f1">{{item.$orig.product.name}}</view><view class="f2">{{"规格："+item.$orig.guige.name}}</view><view class="modify-price flex-y-center"><view class="f2">修改单价：</view><input class="inputPrice" type="digit" data-event-opts="{{[['input',[['inputPrice',['$event',index2]]]]]}}" value="{{item.$orig.guige.sell_price}}" bindinput="__e"/></view><view class="f3"><block><text style="font-weight:bold;">{{"￥"+item.$orig.guige.sell_price}}</text></block><text style="padding-left:20rpx;">{{'× '+item.$orig.num}}</text></view></view><view data-event-opts="{{[['tap',[['clearShopCartFn',['$0'],[[['prodata','',index2,'id']]]]]]]}}" class="del-view flex-y-center" style="color:#999999;font-size:24rpx;" catchtap="__e"><image style="width:24rpx;height:24rpx;margin-right:6rpx;" src="{{pre_url+'/static/img/del.png'}}"></image></view></view><block wx:if="{{item.$orig.product.product_type==1}}"><view class="glassinfo" style="{{('background:rgba('+item.m4+',0.8);color:#FFF')}}" data-index="{{index2}}" data-grid="{{item.$orig.product.has_glassrecord==1?item.$orig.product.glassrecord.id:0}}" data-event-opts="{{[['tap',[['showglass',['$event']]]]]}}" bindtap="__e"><view class="f1">视力档案</view><view class="f2"><text>{{item.$orig.product.has_glassrecord==1?item.$orig.product.glassrecord.name:''}}</text><image src="{{pre_url+'/static/img/arrowright.png'}}"></image></view></view></block></view></block></view><block wx:if="{{$root.g0}}"><view class="input-view"><view class="title-view flex-y-center"><text class="input-title">配送方式：</text></view><view class="freight"><view class="freight-ul"><view style="width:100%;overflow-y:hidden;overflow-x:scroll;white-space:nowrap;"><block wx:for="{{$root.l1}}" wx:for-item="item" wx:for-index="index" wx:key="*this"><block><view data-event-opts="{{[['tap',[['changeFreight',['$0',index],[[['freightList','idx2',idx2]]]]]]]}}" class="freight-li" style="{{(freightkey==index?'color:'+item.m5+';background:rgba('+item.m6+',0.2)':'')}}" bindtap="__e">{{item.$orig.name+''}}</view></block></block></view></view></view><block wx:if="{{buydata.desc&&buydata.pstype==0}}"><view class="freighttips">{{buydata.desc}}</view></block><block wx:if="{{buydata.isoutjuli==1}}"><view class="freighttips">超出配送范围</view></block><block wx:if="{{$root.g1}}"><view class="storeitem"><view class="panel"><view class="f1">可使用门店</view></view><block><block wx:for="{{buydata.storedata}}" wx:for-item="item" wx:for-index="idx" wx:key="idx"><block><block wx:if="{{idx<5}}"><view class="radio-item" data-bid="{{buydata.bid}}" data-index="{{idx}}"><view class="f1"><view>{{item.name}}</view><block wx:if="{{item.address}}"><view class="flex-y-center" style="text-align:left;font-size:24rpx;color:#aaaaae;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:1;overflow:hidden;">{{item.address}}</view></block></view><text style="color:#f50;">{{item.juli}}</text></view></block></block></block></block></view></block><block wx:if="{{buydata.pstype==5}}"><view class="storeitem"><view class="panel"><view class="f1">配送门店</view><block wx:if="{{$root.g2>0}}"><view class="f2"><text class="iconfont icondingwei"></text>{{buydata.storedata[buydata.storekey].name+''}}</view></block><block wx:else><view class="f2">暂无</view></block></view><block wx:for="{{$root.l2}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><block wx:if="{{storeshowall?index<5:true}}"><view data-event-opts="{{[['tap',[['choosestore',[index]]]]]}}" class="radio-item" catchtap="__e"><view class="f1"><view>{{item.$orig.name}}</view><block wx:if="{{item.$orig.address}}"><view style="text-align:left;font-size:24rpx;color:#aaaaae;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:1;overflow:hidden;">{{item.$orig.address}}</view></block></view><text style="color:#f50;">{{item.$orig.juli}}</text><view class="radio" style="{{(buydata.storekey==index?'background:'+item.m7+';border:0':'')}}"><image class="radio-img" src="{{pre_url+'/static/img/checkd.png'}}"></image></view></view></block></block></block><block wx:if="{{$root.g3}}"><view data-event-opts="{{[['tap',[['doStoreShowAll',['$event']]]]]}}" class="storeviewmore" bindtap="__e">- 查看更多 -</view></block></view></block></view></block><view class="flex-y-center input-view"><text class="input-title">支付方式：</text><view class="picker-paytype flex1"><picker class="picker-class" value="{{payTypeIndex}}" range="{{payTypeArr}}" data-event-opts="{{[['change',[['bindPickerChange',['$event']]]]]}}" bindchange="__e"><view class="uni-input">{{payTypeArr[payTypeIndex]}}</view></picker><view class="jiantou-img flex flex-y-center"><image src="{{pre_url+'/static/img/arrowright.png'}}"></image></view></view></view><view class="flex-y-center input-view"><text class="input-title">商品金额：</text><text class="f2">{{"¥"+priceCount}}</text></view><view class="flex-y-center input-view" style="justify-content:space-between;"><view class="flex-y-center"><text class="input-title">订单总价：</text><input type="digit" placeholder="请输入订单总价" disabled="{{totalpricefocus}}" focus="{{!totalpricefocus}}" placeholder-style="color:#626262;font-size:28rpx" data-event-opts="{{[['blur',[['totalpricenblur',['$event']]]],['input',[['__set_model',['','totalprice','$event',[]]]]]]}}" value="{{totalprice}}" bindblur="__e" bindinput="__e"/></view><view data-event-opts="{{[['touchend',[['focusInput',['$event']]]]]}}" class="but-class" style="{{'background:'+('linear-gradient(-90deg,'+$root.m8+' 0%,rgba('+$root.m9+',0.8) 100%)')+';'}}" bindtouchend="__e">修改</view></view><view class="flex-y-center input-view"><text class="input-title">订单备注：</text><input style="flex:1;" placeholder="请输入订单备注" placeholder-style="color:#626262;font-size:28rpx;" data-event-opts="{{[['input',[['__set_model',['','orderNotes','$event',[]]],['',['$event']]]]]}}" value="{{orderNotes}}" bindinput="__e"/></view></view><view style="width:100%;height:182rpx;"></view><view class="footer flex notabbarbot"><view class="text1 flex1">总计：<block><text style="font-weight:bold;font-size:36rpx;">{{"￥"+totalprice}}</text></block></view><button data-event-opts="{{[['tap',[['topay',['$event']]]]]}}" class="op" style="{{'background:'+('linear-gradient(-90deg,'+$root.m10+' 0%,rgba('+$root.m11+',0.8) 100%)')+';'}}" bindtap="__e">提交订单</button></view><block wx:if="{{dialogShow}}"><view class="popup__container"><view data-event-opts="{{[['tap',[['showdialog',['$event']]]]]}}" class="popup__overlay" catchtap="__e"></view><view class="popup__modal" style="min-height:450rpx;"><view class="popup__title"><text class="popup__title-text">提示</text><image class="popup__close" style="width:36rpx;height:36rpx;" src="{{pre_url+'/static/img/close.png'}}" data-event-opts="{{[['tap',[['showdialog',['$event']]]]]}}" catchtap="__e"></image></view><view class="popup__content invoiceBox"><form report-submit="true" data-event-opts="{{[['submit',[['sendCoupon',['$event']]]],['reset',[['formReset',['$event']]]]]}}" bindsubmit="__e" bindreset="__e"><view class="orderinfo">下单成功！快去分享吧</view><button class="ff_btn" style="{{'background:'+($root.m12)+';'}}" open-type="share" data-event-opts="{{[['tap',[['shareBut',['$event']]]]]}}" bindtap="__e">去分享</button></form></view></view></view></block><block wx:if="{{isshowglass}}"><view class="popup__container glass_popup"><view data-event-opts="{{[['tap',[['hideglass',['$event']]]]]}}" class="popup__overlay" catchtap="__e"></view><view class="popup__modal" style="height:1100rpx;"><view class="popup__title"><text class="popup__title-text">视力档案</text><image class="popup__close" style="width:36rpx;height:36rpx;" src="{{pre_url+'/static/img/close.png'}}" data-event-opts="{{[['tap',[['hideglass',['$event']]]]]}}" catchtap="__e"></image></view><view class="popup__content"><radio-group data-event-opts="{{[['change',[['chooseglass',['$event']]]]]}}" bindchange="__e"><block wx:for="{{$root.l3}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><label><view class="{{['glassitem',grid==item.$orig.id?'on':'']}}"><view class="fc"><view class="radio"><radio style="transform:scale(0.8);" color="{{item.m13}}" checked="{{grid==item.$orig.id?true:false}}" value="{{''+index}}"></radio></view><view class="gcontent"><view class="grow gtitle">{{item.$orig.name+" "+(item.$orig.nickname?item.$orig.nickname:'')+" "+(item.$orig.check_time?item.$orig.check_time:'')+" "+item.$orig.typetxt+''}}<block wx:if="{{item.$orig.double_ipd==0}}"><text>{{''+(item.$orig.ipd?' PD'+item.$orig.ipd:'')}}</text></block><block wx:else><text>{{'PD R'+item.$orig.ipd_right+" L"+item.$orig.ipd_left}}</text></block></view><view class="grow">{{'R '+item.$orig.degress_right+"/"+item.$orig.ats_right+"*"+item.$orig.ats_zright+''}}<block wx:if="{{item.$orig.type==3}}"><text class="pdl10">{{'ADD+'+(item.$orig.add_right?item.$orig.add_right:0)}}</text></block></view><view class="grow"><text>{{"L "+item.$orig.degress_left+"/"+item.$orig.ats_left+"*"+item.$orig.ats_zleft+''}}</text><block wx:if="{{item.$orig.type==3}}"><text class="pdl10">{{'ADD+'+(item.$orig.add_left?item.$orig.add_left:0)}}</text></block></view></view><view class="opt" data-url="{{'/pagesExt/glass/add?id='+item.$orig.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">编辑</view></view><block wx:if="{{item.$orig.remark}}"><view class="gremark">{{"备注："+item.$orig.remark}}</view></block></view></label></block></block></radio-group><view class="gr-add"><button class="gr-btn" style="{{'background:'+($root.m14)+';'}}" data-url="/pagesExt/glass/add" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">新增档案</button></view></view></view></view></block></view></view>