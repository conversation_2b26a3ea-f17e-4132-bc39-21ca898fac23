<view><block wx:if="{{isload}}"><block><form data-event-opts="{{[['submit',[['subform',['$event']]]]]}}" bindsubmit="__e"><view class="apply_box"><view class="apply_item"><view>商家名称<text style="color:red;">*</text></view><view class="flex-y-center"><input type="text" name="name" disabled="{{true}}" placeholder="请输入商家名称" value="{{info.name}}"/></view></view><view class="apply_item"><view>商家描述<text style="color:red;">*</text></view><view class="flex-y-center"><input type="text" name="desc" placeholder="请输入商家描述" value="{{info.desc}}"/></view></view><input type="text" hidden="true" name="latitude" value="{{latitude}}"/><input type="text" hidden="true" name="longitude" value="{{longitude}}"/><view class="apply_item"><view>客服电话<text style="color:red;">*</text></view><view class="flex-y-center"><input type="text" name="tel" placeholder="请填写客服电话" value="{{info.tel}}"/></view></view><view class="apply_item" style="line-height:50rpx;"><textarea name="content" placeholder="请输入商家简介" value="{{info.content}}"></textarea></view></view><view class="apply_box"><view class="apply_item" style="border-bottom:0;"><text>商家主图<text style="color:red;">*</text></text></view><view class="flex" style="flex-wrap:wrap;padding-bottom:20rpx;"><block wx:for="{{pic}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="layui-imgbox"><view class="layui-imgbox-close" data-index="{{index}}" data-field="pic" data-event-opts="{{[['tap',[['removeimg',['$event']]]]]}}" bindtap="__e"><image src="/static/img/ico-del.png"></image></view><view class="layui-imgbox-img"><image src="{{item}}" data-url="{{item}}" mode="widthFix" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image></view></view></block><block wx:if="{{$root.g0==0}}"><view class="uploadbtn" style="{{('background:url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 60rpx;background-size:80rpx 80rpx;background-color:#F3F3F3;')}}" data-field="pic" data-event-opts="{{[['tap',[['uploadimg',['$event']]]]]}}" bindtap="__e"></view></block></view><input type="text" hidden="true" name="logo" maxlength="-1" value="{{$root.g1}}"/></view><view class="apply_box"><view class="apply_item" style="border-bottom:0;"><text>商家照片(3-5张)<text style="color:red;">*</text></text></view><view class="flex" style="flex-wrap:wrap;padding-bottom:20rpx;"><block wx:for="{{pics}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="layui-imgbox"><view class="layui-imgbox-close" data-index="{{index}}" data-field="pics" data-event-opts="{{[['tap',[['removeimg',['$event']]]]]}}" bindtap="__e"><image src="/static/img/ico-del.png"></image></view><view class="layui-imgbox-img"><image src="{{item}}" data-url="{{item}}" mode="widthFix" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image></view></view></block><block wx:if="{{$root.g2<5}}"><view class="uploadbtn" style="{{('background:url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 60rpx;background-size:80rpx 80rpx;background-color:#F3F3F3;')}}" data-field="pics" data-event-opts="{{[['tap',[['uploadimg',['$event']]]]]}}" bindtap="__e"></view></block></view><input type="text" hidden="true" name="pics" maxlength="-1" value="{{$root.g3}}"/></view><view style="padding:30rpx 0;"><button class="set-btn" style="{{('background:linear-gradient(90deg,'+$root.m0+' 0%,rgba('+$root.m1+',0.8) 100%)')}}" form-type="submit">确 定</button></view></form></block></block><block wx:if="{{loading}}"><loading vue-id="73cc686c-1" bind:__l="__l"></loading></block><dp-tabbar vue-id="73cc686c-2" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="73cc686c-3" data-ref="popmsg" bind:__l="__l"></popmsg></view>