require('../common/vendor.js');(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["admin/workorder/formlog"],{"04d9":function(t,n,o){"use strict";(function(t){Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;var o=getApp(),a={data:function(){return{opt:{},loading:!1,isload:!1,menuindex:-1,st:"all",datalist:[],pagenum:1,nomore:!1,ishowjindu:!1,jdlist:[]}},onLoad:function(t){this.opt=o.getopts(t),this.st=this.opt.st||"all",this.formid=this.opt.formid||"",this.getdata()},onPullDownRefresh:function(){this.getdata()},onReachBottom:function(){this.nodata||this.nomore||(this.pagenum=this.pagenum+1,this.getdata(!0))},methods:{changetab:function(n){this.st=n,t.pageScrollTo({scrollTop:0,duration:0}),this.getdata()},getdata:function(t){t||(this.pagenum=1,this.datalist=[]);var n=this,a=n.pagenum,e=n.st;this.nodata=!1,this.nomore=!1,this.loading=!0,o.post("ApiAdminWorkorder/myformlog",{formid:n.formid,st:e,pagenum:a},(function(t){n.loading=!1;var o=t.data;if(1==a)n.datalist=o,0==o.length&&(n.nodata=!0),n.loaded();else if(0==o.length)n.nomore=!0;else{var e=n.datalist,i=e.concat(o);n.datalist=i}}))},jindu:function(t){var n=this;n.ishowjindu=!0;var a=t.currentTarget.dataset.id;o.post("ApiWorkorder/selectjindu",{id:a},(function(t){if(1==t.status){var o=t.data;n.jdlist=o}}))},close:function(t){this.ishowjindu=!1}}};n.default=a}).call(this,o("df3c")["default"])},"12d1":function(t,n,o){"use strict";o.d(n,"b",(function(){return e})),o.d(n,"c",(function(){return i})),o.d(n,"a",(function(){return a}));var a={ddTab:function(){return o.e("components/dd-tab/dd-tab").then(o.bind(null,"caa1"))},nodata:function(){return o.e("components/nodata/nodata").then(o.bind(null,"101c"))},nomore:function(){return o.e("components/nomore/nomore").then(o.bind(null,"3892"))},loading:function(){return o.e("components/loading/loading").then(o.bind(null,"ceaa"))},dpTabbar:function(){return o.e("components/dp-tabbar/dp-tabbar").then(o.bind(null,"b875"))},popmsg:function(){return o.e("components/popmsg/popmsg").then(o.bind(null,"2bf2"))}},e=function(){var t=this.$createElement,n=(this._self._c,this.ishowjindu?this.jdlist.length:null);this.$mp.data=Object.assign({},{$root:{g0:n}})},i=[]},"3ff3":function(t,n,o){"use strict";o.r(n);var a=o("04d9"),e=o.n(a);for(var i in a)["default"].indexOf(i)<0&&function(t){o.d(n,t,(function(){return a[t]}))}(i);n["default"]=e.a},"6c2f":function(t,n,o){},b0b3:function(t,n,o){"use strict";(function(t,n){var a=o("47a9");o("06e9");a(o("3240"));var e=a(o("e70e"));t.__webpack_require_UNI_MP_PLUGIN__=o,n(e.default)}).call(this,o("3223")["default"],o("df3c")["createPage"])},c6f5:function(t,n,o){"use strict";var a=o("6c2f"),e=o.n(a);e.a},e70e:function(t,n,o){"use strict";o.r(n);var a=o("12d1"),e=o("3ff3");for(var i in e)["default"].indexOf(i)<0&&function(t){o.d(n,t,(function(){return e[t]}))}(i);o("c6f5");var d=o("828b"),r=Object(d["a"])(e["default"],a["b"],a["c"],!1,null,null,null,!1,a["a"],void 0);n["default"]=r.exports}},[["b0b3","common/runtime","common/vendor"]]]);