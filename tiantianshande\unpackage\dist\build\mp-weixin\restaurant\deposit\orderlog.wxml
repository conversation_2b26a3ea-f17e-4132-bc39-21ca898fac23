<view class="container"><block wx:if="{{isload}}"><block><view class="order-content"><view class="order-box"><view class="head"><view class="f1" data-url="{{data.bid!=0?'/pagesExt/business/index?bid='+data.bid:'/pages/index/index'}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e"><image src="{{data.binfo.logo}}"></image><text>{{data.binfo.name}}</text><text class="flex1"></text><text class="iconfont iconjiantou" style="color:#999;font-weight:normal;"></text></view></view><view class="content" style="{{(idx+1==data.procount?'border-bottom:none':'')}}"><view class="pic"><image class="img" src="{{data.pic}}"></image></view><view class="detail"><text class="t1">{{data.name}}</text><text class="t2">{{"数量："+data.num}}</text><text class="t2">{{"存入时间："+$root.m0}}</text></view><block wx:if="{{data.status==0}}"><view class="takeout st0" data-orderid="{{data.id}}">审核中</view></block><block wx:if="{{data.status==1}}"><view class="takeout">寄存中</view></block><block wx:if="{{data.status==2}}"><view class="takeout st2" data-orderid="{{data.id}}">已取走</view></block><block wx:if="{{data.status==3}}"><view class="takeout st3" data-orderid="{{data.id}}">未通过</view></block><block wx:if="{{data.status==4}}"><view class="takeout st4" data-orderid="{{data.id}}">已过期</view></block></view><view>{{"备注："+data.message}}</view><view class="op"><block wx:if="{{data.status==1}}"><view class="btn1" style="{{'background:'+($root.m1)+';'}}" data-bid="{{data.bid}}" data-orderid="0" data-num="{{data.num}}" data-event-opts="{{[['tap',[['takeout',['$event']]]]]}}" catchtap="__e">取出</view></block></view></view><block wx:if="{{$root.g0>0}}"><view class="expressinfo"><view class="content"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="{{['item '+(index==0?'on':'')]}}"><view class="f1"><image src="{{'/static/img/dot'+(index==0?'2':'1')+'.png'}}"></image></view><view class="f2"><text class="t2">{{item.m2}}</text><text class="t1">{{item.$orig.remark+item.$orig.num+"件"}}</text></view></view></block></view></view></block></view><block wx:if="{{nodata}}"><nodata vue-id="3443122a-1" bind:__l="__l"></nodata></block><block wx:if="{{boxShow}}"><view><view data-event-opts="{{[['tap',[['handleClickMask',['$event']]]]]}}" class="popup__overlay" catchtap="__e"></view><view class="popup__modal"><view class="popup__title"><text class="popup__title-text">请输入取出数量</text><image class="popup__close" style="width:36rpx;height:36rpx;" src="/static/img/close.png" data-event-opts="{{[['tap',[['handleClickMask',['$event']]]]]}}" catchtap="__e"></image></view><view class="popup__content takeoutBox"><form report-submit="true" data-event-opts="{{[['submit',[['formSubmit',['$event']]]],['reset',[['formReset',['$event']]]]]}}" bindsubmit="__e" bindreset="__e"><view class="orderinfo"><view class="item"><text class="t1">取出数量</text><input class="t2" type="text" placeholder="请输入要取出的数量" placeholder-style="font-size:28rpx;color:#BBBBBB" name="numbers" value="{{num}}"/></view></view><button class="btn" style="{{'background:'+($root.m3)+';'}}" form-type="submit">确定</button></form></view></view></view></block></block></block><block wx:if="{{loading}}"><loading vue-id="3443122a-2" bind:__l="__l"></loading></block><dp-tabbar vue-id="3443122a-3" opt="{{opt}}" bind:__l="__l"></dp-tabbar></view>