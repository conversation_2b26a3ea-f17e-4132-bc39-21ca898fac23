<view><dd-search vue-id="48b367ca-1" isfixed="{{true}}" data-event-opts="{{[['^getdata',[['getdata']]]]}}" bind:getdata="__e" bind:__l="__l"></dd-search><view class="container" id="datalist"><block wx:for="{{datalist}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><view class="order-box"><view class="content" style="border-bottom:none;"><view data-url="{{'detail?id='+item.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image src="{{item.coverimg}}"></image></view><view class="detail"><text class="t1">{{item.name}}</text><text class="t2">{{"播放量 "+item.view_num}}<text style="color:#a88;padding-left:20rpx;">{{"点赞数 "+item.zan_num}}</text></text><block wx:if="{{item.status==2}}"><text class="t3">{{"驳回原因："+item.reason}}</text></block></view></view><view class="op"><block wx:if="{{item.status==0}}"><text class="flex1" style="color:orange;">待审核</text></block><block wx:if="{{item.status==2}}"><text class="flex1" style="color:red;">未通过</text></block><block wx:if="{{item.status==1}}"><text class="flex1" style="color:green;">已通过</text></block><view class="btn2" data-id="{{item.id}}" data-event-opts="{{[['tap',[['todel',['$event']]]]]}}" bindtap="__e">删除</view></view></view></block></block></view><block wx:if="{{nomore}}"><nomore vue-id="48b367ca-2" bind:__l="__l"></nomore></block><block wx:if="{{nodata}}"><nodata vue-id="48b367ca-3" bind:__l="__l"></nodata></block><view style="height:140rpx;"></view><view class="{{['btn-add',menuindex>-1?'tabbarbot':'notabbarbot3']}}" style="{{('background:linear-gradient(90deg,'+$root.m0+' 0%,rgba('+$root.m1+',0.8) 100%)')}}" data-url="uploadvideo" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image style="width:28rpx;height:28rpx;margin-right:6rpx;" src="/static/img/add.png"></image>发布短视频</view><popmsg class="vue-ref" vue-id="48b367ca-4" data-ref="popmsg" bind:__l="__l"></popmsg></view>