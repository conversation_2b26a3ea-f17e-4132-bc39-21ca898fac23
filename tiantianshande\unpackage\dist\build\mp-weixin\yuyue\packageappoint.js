(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["yuyue/packageappoint"],{"137c":function(e,o,t){"use strict";t.r(o);var d=t("8504"),a=t.n(d);for(var n in d)["default"].indexOf(n)<0&&function(e){t.d(o,e,(function(){return d[e]}))}(n);o["default"]=a.a},4502:function(e,o,t){"use strict";t.d(o,"b",(function(){return d})),t.d(o,"c",(function(){return a})),t.d(o,"a",(function(){}));var d=function(){var e=this,o=e.$createElement,t=(e._self._c,1==e.fwtype?e.t("color1"):null),d=1==e.fwtype?e.t("color1rgb"):null,a=1==e.fwtype?e.t("color1"):null,n=2==e.fwtype?e.t("color1"):null,r=2==e.fwtype?e.t("color1rgb"):null,s=2==e.fwtype?e.t("color1"):null,i=e.canSubmit?e.t("color1"):null,c=e.canSubmit?e.t("color1rgb"):null,l=e.canSubmit?e.t("color1rgb"):null;e.$mp.data=Object.assign({},{$root:{m0:t,m1:d,m2:a,m3:n,m4:r,m5:s,m6:i,m7:c,m8:l}})},a=[]},"67d2":function(e,o,t){},8504:function(e,o,t){"use strict";Object.defineProperty(o,"__esModule",{value:!0}),o.default=void 0;var d=getApp(),a={data:function(){return{packageOrderId:"",productId:"",packageOrderInfo:{},packageInfo:{},productFwtype:1,fwtype:1,addressInfo:null,selectedAddressId:0,linkman:"",tel:"",loading:!0,order_flow_mode:1}},computed:{canSubmit:function(){return 2==this.fwtype?this.addressInfo&&this.selectedAddressId>0:this.linkman&&this.tel}},onLoad:function(e){console.log("加载套餐使用页面，参数:",e),e&&e.package_order_id&&e.product_id?(this.packageOrderId=e.package_order_id,this.productId=e.product_id,this.getPackageOrderInfo()):(d.error("缺少必要参数"),this.loading=!1)},methods:{t:function(e){return d.globalData.config&&d.globalData.config.t?d.globalData.config.t(e):""},getPackageOrderInfo:function(){var e=this;d.showLoading("加载套餐信息"),d.post("ApiYuyuePackage/getPackageOrderDetail",{order_id:e.packageOrderId},(function(o){if(1==o.status&&o.data)if(console.log("套餐订单信息:",o.data),e.packageOrderInfo=o.data,o.data.items&&o.data.items.length>0){var t=o.data.items.find((function(o){return o.product_id==e.productId||o.service_id==e.productId}));t?(e.packageInfo=t,console.log("找到的服务项信息:",t),e.getProductInfo(e.productId)):(d.error("未找到指定的服务项"),e.loading=!1)}else d.error("套餐中没有服务项"),e.loading=!1;else d.error(o.msg||"获取套餐订单详情失败"),e.loading=!1}),(function(){d.showLoading(!1),d.error("请求失败"),e.loading=!1}))},getProductInfo:function(e){var o=this;console.log("正在获取商品信息，ID:",e),d.post("ApiYuyue/product",{id:e},(function(e){d.showLoading(!1),1==e.status&&e.data?(console.log("商品详情:",e.data),e.data.product&&void 0!==e.data.product.fwtype?(o.productFwtype=parseInt(e.data.product.fwtype||"0"),console.log("商品支持的服务方式:",o.productFwtype),2===o.productFwtype||3===o.productFwtype?(o.fwtype=2,o.loadAddressInfo()):o.fwtype=1):(console.log("商品未设置服务方式，默认设置为上门"),o.productFwtype=3,o.fwtype=2,o.loadAddressInfo()),(e.data.linkman||e.data.tel)&&(o.linkman=e.data.linkman||"",o.tel=e.data.tel||"",console.log("获取到默认联系方式:",o.linkman,o.tel))):(console.log("获取商品详情失败，默认设置为上门"),o.productFwtype=3,o.fwtype=2,o.loadAddressInfo())}),(function(e){d.showLoading(!1),console.log("请求商品详情出错，默认设置为上门:",e),o.productFwtype=3,o.fwtype=2,o.loadAddressInfo()}))},selectServiceType:function(e){console.log("选择服务方式:",e),this.fwtype=e,2==e&&this.loadAddressInfo()},loadAddressInfo:function(){var e=this;d.showLoading("获取地址信息"),d.post("ApiAddress/address",{type:0},(function(o){if(d.showLoading(!1),1==o.status&&o.data&&o.data.length>0){console.log("获取到地址列表:",o.data);var t=o.data.find((function(e){return 1==e.isdefault}));t?(console.log("找到默认地址:",t),e.addressInfo=t,e.selectedAddressId=t.id):(console.log("未找到默认地址，使用列表第一个地址"),e.addressInfo=o.data[0],e.selectedAddressId=o.data[0].id)}else console.log("地址列表为空或获取失败"),e.addressInfo=null,e.selectedAddressId=0}),(function(o){d.showLoading(!1),console.log("获取地址列表失败:",o),e.addressInfo=null,e.selectedAddressId=0}))},goToAddressPage:function(){var e="/pages/address/"+(this.addressInfo&&this.addressInfo.id?"address":"addressadd")+"?fromPage=packageappoint&type=1";console.log("跳转到地址页面:",e),d.goto(e)},inputLinkman:function(e){this.linkman=e.detail.value},inputTel:function(e){this.tel=e.detail.value},submitAppoint:function(){var e=this;if(!e.canSubmit)return 2==e.fwtype&&(!e.addressInfo||e.selectedAddressId<=0)?void d.error("请选择服务地址"):1!=e.fwtype||e.linkman&&e.tel?void 0:void d.error("请填写联系人和电话");d.confirm("确认使用套餐服务吗？",(function(){d.showLoading("提交中");var o={order_id:e.packageOrderId,product_id:e.productId,fwtype:e.fwtype,order_flow_mode:1,formdata:{}};2==e.fwtype&&e.addressInfo?o.address_id=e.selectedAddressId:1==e.fwtype&&(o.linkman=e.linkman,o.tel=e.tel),console.log("提交预约参数:",o),d.post("ApiYuyuePackage/usePackageService",o,(function(o){d.showLoading(!1),1==o.status?d.success(o.msg||"预约成功",(function(){o.data&&o.data.yuyue_order_id?d.goto("/yuyue/orderdetail?id="+o.data.yuyue_order_id):d.goto("/yuyue/packageorderdetail?order_id="+e.packageOrderId)})):d.error(o.msg||"预约失败")}),(function(){d.showLoading(!1),d.error("请求失败")}))}))}}};o.default=a},b668:function(e,o,t){"use strict";(function(e,o){var d=t("47a9");t("06e9");d(t("3240"));var a=d(t("da4d"));e.__webpack_require_UNI_MP_PLUGIN__=t,o(a.default)}).call(this,t("3223")["default"],t("df3c")["createPage"])},bd83:function(e,o,t){"use strict";var d=t("67d2"),a=t.n(d);a.a},da4d:function(e,o,t){"use strict";t.r(o);var d=t("4502"),a=t("137c");for(var n in a)["default"].indexOf(n)<0&&function(e){t.d(o,e,(function(){return a[e]}))}(n);t("bd83");var r=t("828b"),s=Object(r["a"])(a["default"],d["b"],d["c"],!1,null,null,null,!1,d["a"],void 0);o["default"]=s.exports}},[["b668","common/runtime","common/vendor"]]]);