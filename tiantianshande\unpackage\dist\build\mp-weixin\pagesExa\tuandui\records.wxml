<view><block wx:if="{{isload}}"><block><view class="container"><view class="header"><text class="title">奖励记录</text></view><view class="filter-box"><view class="filter-item"><picker value="{{selectedActivityIndex}}" range="{{activityOptions}}" range-key="title" data-event-opts="{{[['change',[['onActivityChange',['$event']]]]]}}" bindchange="__e"><view class="picker-text">{{''+(selectedActivityIndex>=0?activityOptions[selectedActivityIndex].title:'选择活动')+''}}<image class="arrow-icon" src="/static/img/arrow-down.png"></image></view></picker></view><view class="filter-item"><picker value="{{selectedStatusIndex}}" range="{{statusOptions}}" range-key="text" data-event-opts="{{[['change',[['onStatusChange',['$event']]]]]}}" bindchange="__e"><view class="picker-text">{{''+(selectedStatusIndex>=0?statusOptions[selectedStatusIndex].text:'选择状态')+''}}<image class="arrow-icon" src="/static/img/arrow-down.png"></image></view></picker></view></view><block wx:if="{{statsData}}"><view class="stats-box"><view class="stats-title">统计信息</view><view class="stats-content"><view class="stats-item"><view class="stats-label">总记录数</view><view class="stats-value">{{statsData.total_records}}</view></view><view class="stats-item"><view class="stats-label">总奖励金额</view><view class="stats-value reward-amount">{{"¥"+statsData.total_reward}}</view></view><view class="stats-item"><view class="stats-label">已发放</view><view class="stats-value success">{{"¥"+statsData.issued_amount}}</view></view><view class="stats-item"><view class="stats-label">待发放</view><view class="stats-value pending">{{"¥"+statsData.pending_amount}}</view></view></view></view></block><view class="records-box"><view class="records-title">奖励记录</view><view class="records-content"><block wx:for="{{recordsList}}" wx:for-item="item" wx:for-index="index" wx:key="id"><block><view class="record-item"><view class="record-header"><view class="activity-title">{{item.activity_title}}</view><view class="record-time">{{item.create_time}}</view></view><view class="record-info"><view class="performance-info"><view class="perf-text">{{"目标业绩："+item.achievement_target}}</view><view class="actual-perf">{{"实际业绩："+item.team_performance}}</view></view><view class="reward-info"><view class="reward-amount">{{"¥"+item.reward_amount}}</view><view class="{{['status-tag',item.status==1?'status-active':item.status==2?'status-claimed':item.status==3?'status-paid':'status-inactive']}}">{{''+item.status_text+''}}</view></view></view><view class="record-footer"><view class="type-info"><text class="type-tag">{{item.performance_type_text}}</text><text class="type-tag">{{item.reward_type_text}}</text></view><view class="level-info">{{"等级 "+item.achievement_level}}</view></view></view></block></block></view><view class="load-status"><block wx:if="{{nodata}}"><text class="no-data">暂无记录</text></block><block wx:else><block wx:if="{{nomore}}"><text class="no-more">没有更多记录了</text></block><block wx:else><block wx:if="{{loading}}"><loading vue-id="b5983ac0-1" bind:__l="__l"></loading></block></block></block></view></view></view></block></block><block wx:if="{{$root.g0}}"><loading vue-id="b5983ac0-2" bind:__l="__l"></loading></block><dp-tabbar vue-id="b5983ac0-3" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="b5983ac0-4" data-ref="popmsg" bind:__l="__l"></popmsg></view>