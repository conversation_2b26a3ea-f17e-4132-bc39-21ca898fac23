<view class="camera-container data-v-bcf83cac"><block wx:if="{{!capturedImage}}"><view class="camera-mode data-v-bcf83cac"><camera class="camera-preview data-v-bcf83cac" device-position="{{devicePosition}}" flash="{{flashMode}}" data-event-opts="{{[['error',[['onCameraError',['$event']]]],['initdone',[['onCameraInit',['$event']]]]]}}" binderror="__e" bindinitdone="__e"></camera><view class="status-bar data-v-bcf83cac"><view data-event-opts="{{[['touchstart',[['goBack',['$event']]]],['tap',[['goBack',['$event']]]]]}}" class="back-btn data-v-bcf83cac" bindtouchstart="__e" bindtap="__e"><text class="back-icon data-v-bcf83cac">←</text></view><text class="page-title data-v-bcf83cac">舌诊拍摄</text><view data-event-opts="{{[['touchstart',[['toggleFlash',['$event']]]],['tap',[['toggleFlash',['$event']]]]]}}" class="{{['flash-btn','data-v-bcf83cac',(flashOn)?'active':'']}}" bindtouchstart="__e" bindtap="__e"><text class="flash-icon data-v-bcf83cac">{{flashOn?'💡':'🔦'}}</text></view></view><view class="guide-overlay data-v-bcf83cac"><view class="guide-frame data-v-bcf83cac"><view class="frame-corner corner-tl data-v-bcf83cac"></view><view class="frame-corner corner-tr data-v-bcf83cac"></view><view class="frame-corner corner-bl data-v-bcf83cac"></view><view class="frame-corner corner-br data-v-bcf83cac"></view><view class="frame-center data-v-bcf83cac"><text class="guide-text data-v-bcf83cac">请将舌头置于框内</text><view class="pulse-dot data-v-bcf83cac"></view></view></view></view><view class="tips-section data-v-bcf83cac"><view class="tips-container data-v-bcf83cac"><block wx:for="{{shootingTips}}" wx:for-item="tip" wx:for-index="index" wx:key="index"><view class="tip-item data-v-bcf83cac"><view class="tip-icon data-v-bcf83cac">{{tip.icon}}</view><text class="tip-text data-v-bcf83cac">{{tip.text}}</text></view></block></view></view><view class="control-section data-v-bcf83cac"><view class="control-container data-v-bcf83cac"><view class="control-item data-v-bcf83cac"><view data-event-opts="{{[['touchstart',[['chooseImage',['$event']]]],['tap',[['chooseImage',['$event']]]]]}}" class="control-btn secondary data-v-bcf83cac" bindtouchstart="__e" bindtap="__e"><image class="control-icon-img data-v-bcf83cac" src="{{pre_url+'/static/img/xuanzetupian.png'}}" mode="aspectFit"></image></view><text class="control-label data-v-bcf83cac">选择图片</text></view><view class="control-item capture data-v-bcf83cac"><view data-event-opts="{{[['touchstart',[['takePhoto',['$event']]]],['tap',[['takePhoto',['$event']]]]]}}" class="{{['control-btn','primary','data-v-bcf83cac',(captureLoading)?'capturing':'']}}" bindtouchstart="__e" bindtap="__e"><block wx:if="{{!captureLoading}}"><view class="capture-ring data-v-bcf83cac"></view></block><view class="capture-dot data-v-bcf83cac"></view></view><text class="control-label data-v-bcf83cac">拍照</text></view><view class="control-item data-v-bcf83cac"><view data-event-opts="{{[['touchstart',[['flipCamera',['$event']]]],['tap',[['flipCamera',['$event']]]]]}}" class="{{['control-btn','secondary','data-v-bcf83cac',(isFlipping)?'active':'']}}" bindtouchstart="__e" bindtap="__e"><image class="control-icon-img data-v-bcf83cac" src="{{pre_url+'/static/img/fanzhuanxiangji.png'}}" mode="aspectFit"></image></view><text class="control-label data-v-bcf83cac">翻转相机</text></view></view></view></view></block><block wx:else><view class="preview-mode data-v-bcf83cac"><view class="preview-header data-v-bcf83cac"><text class="preview-title data-v-bcf83cac">预览图片</text><text class="preview-subtitle data-v-bcf83cac">请确认图片清晰度</text></view><view class="image-preview data-v-bcf83cac"><image class="preview-image data-v-bcf83cac" src="{{capturedImage}}" mode="aspectFit" data-event-opts="{{[['error',[['onImageError',['$event']]]]]}}" binderror="__e"></image><view class="analysis-indicators data-v-bcf83cac"><block wx:for="{{analysisPoints}}" wx:for-item="point" wx:for-index="index" wx:key="index"><view class="analysis-point data-v-bcf83cac" style="{{'left:'+(point.x+'%')+';'+('top:'+(point.y+'%')+';')}}"><view class="point-ring data-v-bcf83cac"></view><view class="point-dot data-v-bcf83cac"></view></view></block></view><view class="quality-check data-v-bcf83cac"><block wx:for="{{qualityChecks}}" wx:for-item="check" wx:for-index="index" wx:key="index"><view class="quality-item data-v-bcf83cac"><view class="{{['quality-icon','data-v-bcf83cac',check.status]}}"><text class="data-v-bcf83cac">{{check.status==='pass'?'✓':'!'}}</text></view><text class="quality-text data-v-bcf83cac">{{check.text}}</text></view></block></view></view><view class="preview-actions data-v-bcf83cac"><view data-event-opts="{{[['touchstart',[['retakePhoto',['$event']]]],['tap',[['retakePhoto',['$event']]]]]}}" class="action-btn secondary data-v-bcf83cac" bindtouchstart="__e" bindtap="__e"><view class="btn-content data-v-bcf83cac"><text class="btn-icon data-v-bcf83cac">🔄</text><text class="btn-text data-v-bcf83cac">重新拍摄</text></view></view><view data-event-opts="{{[['touchstart',[['confirmPhoto',['$event']]]],['tap',[['confirmPhoto',['$event']]]]]}}" class="action-btn primary data-v-bcf83cac" bindtouchstart="__e" bindtap="__e"><view class="btn-content data-v-bcf83cac"><text class="btn-icon data-v-bcf83cac">✨</text><text class="btn-text data-v-bcf83cac">开始分析</text></view><view class="btn-glow data-v-bcf83cac"></view></view></view></view></block><block wx:if="{{isLoading}}"><view class="loading-overlay data-v-bcf83cac"><view class="loading-content data-v-bcf83cac"><view class="loading-spinner data-v-bcf83cac"><view class="spinner-ring data-v-bcf83cac"></view><view class="spinner-dot data-v-bcf83cac"></view></view><text class="loading-text data-v-bcf83cac">{{loadingText}}</text></view></view></block></view>