<view><block wx:if="{{isload}}"><block><view class="banner" style="{{'background:'+('url('+pre_url+'/static/img/topbg.png)')+';'+('background-size:'+('100%')+';')}}"><image src="{{set.logo}}" background-size="cover" data-url="{{uinfo.bid==0?'/pages/index/index':'/pagesExt/business/index?id='+uinfo.bid}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"></image><view class="info"><text class="nickname">{{set.name}}</text><text>{{uinfo.un}}</text></view><view data-event-opts="{{[['tap',[['saoyisao',['$event']]]]]}}" class="set" bindtap="__e"><image src="/static/img/ico-scan.png"></image></view></view><view class="contentdata"><block wx:if="{{auth_data.order}}"><block><block wx:if="{{showshoporder}}"><view class="order"><view class="head"><text class="f1">商城订单</text><view class="f2" data-url="../order/shoporder" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><text>查看全部订单</text><image src="/static/img/arrowright.png"></image></view></view><view class="content"><view class="item" data-url="../order/shoporder?st=0" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image src="{{pre_url+'/static/img/admin/order1.png'}}"></image><text class="t3">{{"待付款("+count0+")"}}</text></view><view class="item" data-url="../order/shoporder?st=1" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image src="{{pre_url+'/static/img/admin/order2.png'}}"></image><text class="t3">{{"待发货("+count1+")"}}</text></view><view class="item" data-url="../order/shoporder?st=2" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image src="{{pre_url+'/static/img/admin/order3.png'}}"></image><text class="t3">{{"待收货("+count2+")"}}</text></view><view class="item" data-url="../order/shopRefundOrder" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image src="{{pre_url+'/static/img/admin/order4.png'}}"></image><text class="t3">{{"退款/售后("+count4+")"}}</text></view></view></view></block><block wx:if="{{deposit_switch==1}}"><view><block wx:if="{{depositPaid>0}}"><view class="deposit-status"><view style="background-color:#d4edda;border:1px solid #c3e6cb;color:#155724;padding:20rpx;border-radius:10rpx;margin-top:20rpx;"><view style="font-size:30rpx;font-weight:bold;">已缴纳</view><view style="margin-top:10rpx;">{{'您已缴纳保证金：'+depositPaid+' 元。'}}</view></view></view></block><block wx:else><view class="deposit-reminder"><view style="background-color:#fff3cd;border:1px solid #ffeeba;color:#856404;padding:20rpx;border-radius:10rpx;margin-top:20rpx;"><view style="font-size:30rpx;font-weight:bold;">注意</view><view style="margin-top:10rpx;">您尚未缴纳保证金，暂时没有添加和管理商品的功能，请尽快缴纳以完成入驻。</view></view><button data-event-opts="{{[['tap',[['payDeposit',['$event']]]]]}}" class="deposit-button" style="{{('width: 100%; background: linear-gradient(90deg,'+$root.m0+' 0%, rgba('+$root.m1+',0.8) 100%); color: #fff; padding: 20rpx; border: none; border-radius: 10rpx; font-size: 32rpx; margin-top: 20rpx;')}}" bindtap="__e">缴纳保证金</button></view></block></view></block><view class="list"><block wx:if="{{showcollageorder}}"><view class="item" data-url="../order/collageorder" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="f2">拼团订单</view><text class="f3">{{collageCount}}</text><image class="f4" src="/static/img/arrowright.png"></image></view></block><block wx:if="{{showkanjiaorder}}"><view class="item" data-url="../order/kanjiaorder" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="f2">砍价订单</view><text class="f3">{{kanjiaCount}}</text><image class="f4" src="/static/img/arrowright.png"></image></view></block><block wx:if="{{showseckillorder}}"><view class="item" data-url="../order/seckillorder" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="f2">秒杀订单</view><text class="f3">{{seckillCount}}</text><image class="f4" src="/static/img/arrowright.png"></image></view></block><block wx:if="{{showtuangouorder}}"><view class="item" data-url="../order/tuangouorder" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="f2">团购订单</view><text class="f3">{{tuangouCount}}</text><image class="f4" src="/static/img/arrowright.png"></image></view></block><block wx:if="{{showscoreshoporder}}"><view class="item" data-url="../order/scoreshoporder" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="f2">{{$root.m2+"商城订单"}}</view><text class="f3">{{scoreshopCount}}</text><image class="f4" src="/static/img/arrowright.png"></image></view></block><block wx:if="{{showluckycollageorder}}"><view class="item" data-url="../order/luckycollageorder" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="f2">幸运拼团订单</view><text class="f3">{{luckycollageCount}}</text><image class="f4" src="/static/img/arrowright.png"></image></view></block><block wx:if="{{showyuyueorder}}"><view class="item" data-url="../order/yuyueorder" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="f2">预约订单</view><text class="f3">{{yuyueorderCount}}</text><image class="f4" src="/static/img/arrowright.png"></image></view></block><block wx:if="{{showyuekeorder}}"><view class="item" data-url="../order/yuekeorder" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="f2">约课记录</view><text class="f3"></text><image class="f4" src="/static/img/arrowright.png"></image></view></block><block wx:if="{{showCycleorder}}"><view class="item" data-url="../order/cycleorder" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="f2">周期购订单</view><text class="f3">{{cycleCount}}</text><image class="f4" src="/static/img/arrowright.png"></image></view></block><block wx:if="{{showmaidanlog}}"><view class="item" data-url="../order/maidanlog" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="f2">买单记录</view><text class="f3">{{maidanCount}}</text><image class="f4" src="/static/img/arrowright.png"></image></view></block><block wx:if="{{showformlog}}"><view class="item" data-url="../form/formlog" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="f2">表单提交记录</view><text class="f3">{{formlogCount}}</text><image class="f4" src="/static/img/arrowright.png"></image></view></block><block wx:if="{{searchmember}}"><view class="item" data-url="/activity/searchmember/searchmember" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="f2">一键查看</view><text class="f3"></text><image class="f4" src="/static/img/arrowright.png"></image></view></block></view></block></block><block wx:if="{{showworkorder}}"><view class="list"><view class="item" data-url="../workorder/record" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="f2">工单记录</view><text class="f3">{{workordercount}}</text><image class="f4" src="/static/img/arrowright.png"></image></view></view></block><block wx:if="{{auth_data.product}}"><block><block wx:if="{{deposit_switch==0||depositPaid>0}}"><view class="list"><view class="item" data-url="../product/index" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="f2">商品列表</view><text class="f3">{{productCount}}</text><image class="f4" src="/static/img/arrowright.png"></image></view><view class="item" data-url="../product/edit" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="f2">添加商品</view><text class="f3"></text><image class="f4" src="/static/img/arrowright.png"></image></view></view></block></block></block><block wx:if="{{auth_data.product}}"><block><view class="list"><view class="item" data-url="../freight/index" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="f2">运费设置</view><text class="f3"></text><image class="f4" src="/static/img/arrowright.png"></image></view></view></block></block><block wx:if="{{auth_data.product}}"><block><block wx:if="{{(deposit_switch==0||depositPaid>0)&&(showpurchasemanage||showreturnmanage)||showreturnmanage}}"><view class="list"><view class="list-header"><text class="list-title">进货退货管理</text></view><block wx:if="{{showpurchasemanage}}"><view class="item" data-url="../purchase/index" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="f2">进货管理</view><text class="f3"></text><image class="f4" src="/static/img/arrowright.png"></image></view></block><block wx:if="{{showreturnmanage}}"><view class="item" data-url="../return/index" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="f2">退货管理</view><text class="f3"></text><image class="f4" src="/static/img/arrowright.png"></image></view></block></view></block></block></block><block wx:if="{{auth_data.restaurant_product||auth_data.restaurant_table||auth_data.restaurant_tableWaiter}}"><block><view class="list"><block wx:if="{{auth_data.restaurant_product}}"><view class="item" data-url="../restaurant/product/index" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="f2">菜品列表</view><text class="f3"></text><image class="f4" src="/static/img/arrowright.png"></image></view></block><block wx:if="{{auth_data.restaurant_product}}"><view class="item" data-url="../restaurant/product/edit" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="f2">添加菜品</view><text class="f3"></text><image class="f4" src="/static/img/arrowright.png"></image></view></block><block wx:if="{{auth_data.restaurant_product}}"><view class="item" data-url="../restaurant/category/index" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="f2">菜品分类</view><text class="f3"></text><image class="f4" src="/static/img/arrowright.png"></image></view></block><block wx:if="{{auth_data.restaurant_product}}"><view class="item" data-url="../restaurant/category/edit" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="f2">添加分类</view><text class="f3"></text><image class="f4" src="/static/img/arrowright.png"></image></view></block><block wx:if="{{auth_data.restaurant_table}}"><view class="item" data-url="../restaurant/tableCategory" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="f2">餐桌分类</view><text class="f3"></text><image class="f4" src="/static/img/arrowright.png"></image></view></block><block wx:if="{{auth_data.restaurant_table}}"><view class="item" data-url="../restaurant/table" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="f2">餐桌编辑</view><text class="f3"></text><image class="f4" src="/static/img/arrowright.png"></image></view></block><block wx:if="{{auth_data.restaurant_tableWaiter}}"><view class="item" data-url="../restaurant/tableWaiter" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="f2">餐桌管理</view><text class="f3"></text><image class="f4" src="/static/img/arrowright.png"></image></view></block></view></block></block><block wx:if="{{auth_data.restaurant_takeaway||auth_data.restaurant_shop||auth_data.restaurant_booking||auth_data.restaurant_deposit||auth_data.restaurant_queue}}"><block><view class="list"><block wx:if="{{auth_data.restaurant_takeaway}}"><view class="item" data-url="../restaurant/takeawayorder" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="f2">外卖订单</view><text class="f3"></text><image class="f4" src="/static/img/arrowright.png"></image></view></block><block wx:if="{{auth_data.restaurant_shop}}"><view class="item" data-url="../restaurant/shoporder" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="f2">点餐订单</view><text class="f3"></text><image class="f4" src="/static/img/arrowright.png"></image></view></block><block wx:if="{{auth_data.restaurant_booking}}"><view class="item" data-url="../restaurant/bookingorder" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="f2">预定订单</view><text class="f3"></text><image class="f4" src="/static/img/arrowright.png"></image></view></block><block wx:if="{{auth_data.restaurant_booking}}"><view class="item" data-url="../restaurant/booking" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="f2">添加预定</view><text class="f3"></text><image class="f4" src="/static/img/arrowright.png"></image></view></block><block wx:if="{{auth_data.restaurant_deposit}}"><view class="item" data-url="../restaurant/depositorder" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="f2">寄存订单</view><text class="f3"></text><image class="f4" src="/static/img/arrowright.png"></image></view></block><block wx:if="{{auth_data.restaurant_queue}}"><view class="item" data-url="../restaurant/queue" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="f2">排队叫号</view><text class="f3"></text><image class="f4" src="/static/img/arrowright.png"></image></view></block><block wx:if="{{auth_data.restaurant_queue}}"><view class="item" data-url="../restaurant/queueCategory" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="f2">排队管理</view><text class="f3"></text><image class="f4" src="/static/img/arrowright.png"></image></view></block></view></block></block><view class="list"><view class="item" data-url="../hexiao/record" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="f2">我的核销</view><text class="f3">{{hexiaoCount}}</text><image class="f4" src="/static/img/arrowright.png"></image></view></view><block wx:if="{{uinfo.tixingshow==1}}"><view class="list"><view><block wx:if="{{uinfo.tmpl_orderconfirm_show==1}}"><view class="item"><view class="f2">订单提交通知</view><view class="f3"><switch value="1" checked="{{uinfo.tmpl_orderconfirm==1?true:false}}" data-type="tmpl_orderconfirm" data-event-opts="{{[['change',[['switchchange',['$event']]]]]}}" bindchange="__e"></switch></view></view></block><view style="color:#999;font-size:24rpx;margin-bottom:10rpx;" data-tmplid="{{wxtmplset.tmpl_orderconfirm}}" data-event-opts="{{[['tap',[['addsubnum',['$event']]]]]}}" bindtap="__e">剩余可接收次数：<text style="color:#FC5648;font-weight:bold;font-size:30rpx;">{{uinfo.tmpl_orderconfirmNum}}</text>，每点击此处一次可增加一次机会</view></view><view><block wx:if="{{uinfo.tmpl_orderpay_show==1}}"><view class="item"><view class="f2">订单支付通知</view><view class="f3"><switch value="1" checked="{{uinfo.tmpl_orderpay==1?true:false}}" data-type="tmpl_orderpay" data-event-opts="{{[['change',[['switchchange',['$event']]]]]}}" bindchange="__e"></switch></view></view></block><view style="color:#999;font-size:24rpx;margin-bottom:10rpx;" data-tmplid="{{wxtmplset.tmpl_orderconfirm}}" data-event-opts="{{[['tap',[['addsubnum',['$event']]]]]}}" bindtap="__e">剩余可接收次数：<text style="color:#FC5648;font-weight:bold;font-size:30rpx;">{{uinfo.tmpl_orderconfirmNum}}</text>，每点击此处一次可增加一次机会</view></view><view><block wx:if="{{uinfo.tmpl_ordershouhuo_show==1}}"><view class="item"><view class="f2">订单收货通知</view><view class="f3"><switch value="1" checked="{{uinfo.tmpl_ordershouhuo==1?true:false}}" data-type="tmpl_ordershouhuo" data-event-opts="{{[['change',[['switchchange',['$event']]]]]}}" bindchange="__e"></switch></view></view></block><view style="color:#999;font-size:24rpx;margin-bottom:10rpx;" data-tmplid="{{wxtmplset.tmpl_ordershouhuo}}" data-event-opts="{{[['tap',[['addsubnum',['$event']]]]]}}" bindtap="__e">剩余可接收次数：<text style="color:#FC5648;font-weight:bold;font-size:30rpx;">{{uinfo.tmpl_ordershouhuoNum}}</text>，每点击此处一次可增加一次机会</view></view><view><block wx:if="{{uinfo.tmpl_ordertui_show==1}}"><view class="item"><view class="f2">退款申请通知</view><view class="f3"><switch value="1" checked="{{uinfo.tmpl_ordertui==1?true:false}}" data-type="tmpl_ordertui" data-event-opts="{{[['change',[['switchchange',['$event']]]]]}}" bindchange="__e"></switch></view></view></block><view style="color:#999;font-size:24rpx;margin-bottom:10rpx;" data-tmplid="{{wxtmplset.tmpl_ordertui}}" data-event-opts="{{[['tap',[['addsubnum',['$event']]]]]}}" bindtap="__e">剩余可接收次数：<text style="color:#FC5648;font-weight:bold;font-size:30rpx;">{{uinfo.tmpl_ordertuiNum}}</text>，每点击此处一次可增加一次机会</view></view><view><block wx:if="{{uinfo.tmpl_withdraw_show==1}}"><view class="item"><view class="f2">提现申请通知</view><view class="f3"><switch value="1" checked="{{uinfo.tmpl_withdraw==1?true:false}}" data-type="tmpl_withdraw" data-event-opts="{{[['change',[['switchchange',['$event']]]]]}}" bindchange="__e"></switch></view></view></block><view style="color:#999;font-size:24rpx;margin-bottom:10rpx;" data-tmplid="{{wxtmplset.tmpl_withdraw}}" data-event-opts="{{[['tap',[['addsubnum',['$event']]]]]}}" bindtap="__e">剩余可接收次数：<text style="color:#FC5648;font-weight:bold;font-size:30rpx;">{{uinfo.tmpl_withdrawNum}}</text>，每点击此处一次可增加一次机会</view></view><block wx:if="{{uinfo.tmpl_formsub_show==1}}"><view class="item"><view class="f2">表单提交通知</view><view class="f3"><switch value="1" checked="{{uinfo.tmpl_formsub==1?true:false}}" data-type="tmpl_formsub" data-event-opts="{{[['change',[['switchchange',['$event']]]]]}}" bindchange="__e"></switch></view></view></block><view><block wx:if="{{uinfo.tmpl_kehuzixun_show==1}}"><view class="item"><view class="f2">用户咨询通知</view><view class="f3"><switch value="1" checked="{{uinfo.tmpl_kehuzixun==1?true:false}}" data-type="tmpl_kehuzixun" data-event-opts="{{[['change',[['switchchange',['$event']]]]]}}" bindchange="__e"></switch></view></view></block><view style="color:#999;font-size:24rpx;margin-bottom:30rpx;" data-tmplid="{{wxtmplset.tmpl_kehuzixun}}" data-event-opts="{{[['tap',[['addsubnum',['$event']]]]]}}" bindtap="__e">剩余可接收次数：<text style="color:#FC5648;font-weight:bold;font-size:30rpx;">{{uinfo.tmpl_kehuzixunNum}}</text>，每点击此处一次可增加一次机会</view></view></view></block><view class="list"><block wx:if="{{showbusinessqr}}"><view class="item" data-url="businessqr" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="f2">推广码</view><text class="f3"></text><image class="f4" src="/static/img/arrowright.png"></image></view></block><block wx:if="{{showbusinessqr}}"><view class="item" data-url="businessqr" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="f2">推广码</view><text class="f3"></text><image class="f4" src="/static/img/arrowright.png"></image></view></block><block wx:if="{{uinfo.bid>0}}"><view class="item" data-url="../businessposter/index" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="f2">商家收款码</view><text class="f3"></text><image class="f4" src="/static/img/arrowright.png"></image></view></block><block wx:if="{{showworkadd}}"><view class="item" data-url="../workorder/add" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="f2">工单提交</view><text class="f3"></text><image class="f4" src="/static/img/arrowright.png"></image></view></block><block wx:if="{{uinfo.bid>0}}"><view class="item" data-url="setinfo" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="f2">店铺设置</view><text class="f3"></text><image class="f4" src="/static/img/arrowright.png"></image></view></block><view class="item" data-url="setpwd" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="f2">修改密码</view><text class="f3"></text><image class="f4" src="/static/img/arrowright.png"></image></view><view class="item" data-url="../index/login" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="f2">切换账号</view><text class="f3"></text><image class="f4" src="/static/img/arrowright.png"></image></view><view class="item" data-url="/pages/my/usercenter" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="f2">返回个人中心</view><text class="f3"></text><image class="f4" src="/static/img/arrowright.png"></image></view></view></view><view class="tabbar"><view class="tabbar-bot"></view><view class="tabbar-bar" style="background-color:#ffffff;"><block wx:if="{{auth_data.member}}"><view class="tabbar-item" data-url="../member/index" data-opentype="reLaunch" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="tabbar-image-box"><image class="tabbar-icon" src="{{pre_url+'/static/img/admin/member.png'}}"></image></view><view class="tabbar-text">{{$root.m3}}</view></view></block><block wx:if="{{auth_data.zixun}}"><view class="tabbar-item" data-url="../kefu/index" data-opentype="reLaunch" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="tabbar-image-box"><image class="tabbar-icon" src="{{pre_url+'/static/img/admin/zixun.png'}}"></image></view><view class="tabbar-text">咨询</view></view></block><block wx:if="{{auth_data.finance}}"><view class="tabbar-item" data-url="../finance/index" data-opentype="reLaunch" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="tabbar-image-box"><image class="tabbar-icon" src="{{pre_url+'/static/img/admin/finance.png'}}"></image></view><view class="tabbar-text">财务</view></view></block><view class="tabbar-item" data-url="../index/index" data-opentype="reLaunch" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="tabbar-image-box"><image class="tabbar-icon" src="{{pre_url+'/static/img/admin/my2.png'}}"></image></view><view class="tabbar-text active">我的</view></view></view></view></block></block><popmsg class="vue-ref" vue-id="6ab71922-1" data-ref="popmsg" bind:__l="__l"></popmsg></view>