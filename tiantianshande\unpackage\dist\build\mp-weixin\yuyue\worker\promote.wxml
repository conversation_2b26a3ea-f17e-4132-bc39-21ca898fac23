<block wx:if="{{isload}}"><view><view class="container"><view class="header" style="{{'background:'+('linear-gradient(135deg,'+$root.m0+' 0%,rgba('+$root.m1+',0.8) 100%)')+';'}}"><text class="title">推广产品</text><text class="subtitle">为顾客提供优质服务，提升您的收入</text></view><view class="content"><block wx:if="{{!loading}}"><block><block wx:if="{{$root.g0}}"><view class="product-list"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="id"><view class="product-item"><image class="product-image" src="{{item.$orig.pic}}" mode="aspectFill"></image><view class="product-info"><view class="product-name">{{item.$orig.name}}</view><view class="product-price" style="{{'color:'+(item.m2)+';'}}"><text class="price-symbol">¥</text><text class="price-integer">{{item.g1[0]}}</text><text class="price-decimal">{{"."+item.g2}}</text></view><view class="product-sales">{{"已售 "+(item.$orig.sales||0)}}</view></view><view class="poster-button-wrapper"><view data-event-opts="{{[['tap',[['generatePoster',['$0'],[[['dataList','id',item.$orig.id,'id']]]]]]]}}" class="poster-button" style="{{'background:'+('linear-gradient(90deg,'+item.m3+' 0%,rgba('+item.m4+',0.8) 100%)')+';'}}" bindtap="__e"><text class="button-text">生成海报</text></view></view></view></block><uni-load-more vue-id="4b497c87-1" status="{{loadingType}}" bind:__l="__l"></uni-load-more></view></block><block wx:else><view class="no-data"><image class="no-data-image" src="/static/img/no-data.png" mode="aspectFit"></image><text class="no-data-text">暂无可推广的产品</text></view></block></block></block><block wx:else><loading vue-id="4b497c87-2" bind:__l="__l"></loading></block></view><block wx:if="{{showPosterPopup}}"><view data-event-opts="{{[['touchmove',[['stopPrevent',['$event']]]]]}}" class="poster-popup" catchtouchmove="__e"><view data-event-opts="{{[['tap',[['closePosterPopup',['$event']]]]]}}" class="popup-mask" bindtap="__e"></view><view class="poster-content"><view class="poster-header"><text class="poster-title">产品推广海报</text><view data-event-opts="{{[['tap',[['closePosterPopup',['$event']]]]]}}" class="close-icon" bindtap="__e">×</view></view><image class="poster-image-preview" src="{{posterImageUrl}}" mode="widthFix" data-event-opts="{{[['tap',[['previewPoster',['$event']]]]]}}" bindtap="__e"></image><view class="poster-tip"><view class="tip-icon" style="{{'color:'+($root.m5)+';'}}">i</view><text class="tip-text">长按图片保存或分享给好友</text></view><view class="action-buttons"><view data-event-opts="{{[['tap',[['savePoster',['$event']]]]]}}" class="save-button" style="{{'background:'+('linear-gradient(90deg,'+$root.m6+' 0%,rgba('+$root.m7+',0.8) 100%)')+';'}}" bindtap="__e"><text>保存到相册</text></view><view data-event-opts="{{[['tap',[['closePosterPopup',['$event']]]]]}}" class="close-button" bindtap="__e"><text>关闭</text></view></view></view></view></block><dp-tabbar vue-id="4b497c87-3" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="4b497c87-4" data-ref="popmsg" bind:__l="__l"></popmsg></view></view></block><block wx:else><loading vue-id="4b497c87-5" bind:__l="__l"></loading></block>