<view class="job-filter"><scroll-view class="filter-content" scroll-y="{{true}}"><view class="filter-section"><view class="section-title"><view class="title-line" style="{{'background-color:'+($root.m0)+';'}}"></view><text>期望城市</text></view><view class="location-row"><text class="location-hint">选择你想要工作的区域</text><view class="location-selectors"><view class="selector-item"><picker value="{{provinceIndex}}" range="{{provinceList}}" range-key="name" data-event-opts="{{[['change',[['onProvinceChange',['$event']]]]]}}" bindchange="__e"><view class="picker-content"><text>{{currentProvinceName||'江苏省'}}</text><text class="arrow-down">▼</text></view></picker></view><view class="selector-item"><picker value="{{cityIndex}}" range="{{cityList}}" range-key="name" disabled="{{!currentProvince}}" data-event-opts="{{[['change',[['onCityChange',['$event']]]]]}}" bindchange="__e"><view class="picker-content"><text>{{currentCityName||'苏州市'}}</text><text class="arrow-down">▼</text></view></picker></view><view class="selector-item"><picker value="{{districtIndex}}" range="{{districtList}}" range-key="name" disabled="{{!currentCity}}" data-event-opts="{{[['change',[['onDistrictChange',['$event']]]]]}}" bindchange="__e"><view class="picker-content"><text>{{currentDistrictName||'常熟市'}}</text><text class="arrow-down">▼</text></view></picker></view></view><block wx:if="{{$root.g0>0}}"><view class="city-list"><block wx:for="{{selectedCities}}" wx:for-item="city" wx:for-index="index" wx:key="index"><view class="city-tag">{{''+city+''}}<text data-event-opts="{{[['tap',[['removeCity',[index]]]]]}}" class="delete-icon" bindtap="__e">×</text></view></block></view></block><view data-event-opts="{{[['tap',[['addSelectedCity',['$event']]]]]}}" class="add-city-btn" style="{{'background-color:'+($root.m1)+';'+('color:'+('#ffffff')+';')}}" bindtap="__e"><text class="add-icon" style="color:#ffffff;">+</text><text>确定</text></view></view></view><view class="filter-section"><view class="section-title"><view class="title-line" style="{{'background-color:'+($root.m2)+';'}}"></view><text>工作形式</text></view><view class="option-list"><block wx:for="{{$root.l0}}" wx:for-item="mode" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['selectWorkMode',['$0'],[[['workModeOptions','',index,'value']]]]]]]}}" class="{{['option-item',(selectedWorkMode===mode.$orig.value)?'active':'']}}" style="{{'background:'+(selectedWorkMode===mode.$orig.value?mode.m3:'#f8f8f8')+';'+('color:'+(selectedWorkMode===mode.$orig.value?'#fff':'#666')+';')}}" bindtap="__e">{{''+mode.$orig.label+''}}</view></block></view></view><view class="filter-section"><view class="section-title"><view class="title-line" style="{{'background-color:'+($root.m4)+';'}}"></view><text>结算方式</text></view><view class="option-list"><block wx:for="{{$root.l1}}" wx:for-item="payment" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['selectPayment',['$0'],[[['paymentOptions','',index,'value']]]]]]]}}" class="{{['option-item',(selectedPayment===payment.$orig.value)?'active':'']}}" style="{{'background:'+(selectedPayment===payment.$orig.value?payment.m5:'#f8f8f8')+';'+('color:'+(selectedPayment===payment.$orig.value?'#fff':'#666')+';')}}" bindtap="__e">{{''+payment.$orig.label+''}}</view></block></view></view><view class="filter-section"><view class="section-title"><view class="title-line" style="{{'background-color:'+($root.m6)+';'}}"></view><text>工作时间</text></view><view class="worktime-options"><block wx:for="{{$root.l2}}" wx:for-item="time" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['selectWorkTime',['$0'],[[['workTimeOptions','',index,'value']]]]]]]}}" class="{{['worktime-item',(selectedWorkTime===time.$orig.value)?'active':'']}}" style="{{'background:'+(selectedWorkTime===time.$orig.value?time.m7:'#f8f8f8')+';'+('color:'+(selectedWorkTime===time.$orig.value?'#fff':'#666')+';')}}" bindtap="__e">{{''+time.$orig.label+''}}</view></block></view></view><view class="filter-section"><view class="section-title"><view class="title-line" style="{{'background-color:'+($root.m8)+';'}}"></view><text>薪资范围</text></view><view class="salary-selector"><view class="range-text">{{$root.m9+" - "+$root.m10}}</view><view class="selector-group"><view class="selector-container"><picker value="{{salaryStartIndex}}" range="{{$root.g1}}" data-event-opts="{{[['change',[['e0',['$event']]]]]}}" bindchange="__e"><view class="selector-content"><text>{{$root.m11}}</text><text class="arrow-down">▼</text></view></picker></view><view class="selector-separator">至</view><view class="selector-container"><picker value="{{salaryEndIndex}}" range="{{$root.g2}}" data-event-opts="{{[['change',[['e1',['$event']]]]]}}" bindchange="__e"><view class="selector-content"><text>{{$root.m12}}</text><text class="arrow-down">▼</text></view></picker></view></view></view><view class="salary-options"><block wx:for="{{$root.l3}}" wx:for-item="salary" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['selectSalary',['$0'],[[['salaryOptions','',index,'value']]]]]]]}}" class="{{['salary-item',(selectedSalary===salary.$orig.value)?'active':'']}}" style="{{'background:'+(selectedSalary===salary.$orig.value?salary.m13:'#f8f8f8')+';'+('color:'+(selectedSalary===salary.$orig.value?'#fff':'#666')+';')}}" bindtap="__e">{{''+salary.$orig.label+''}}</view></block></view></view><view class="filter-section"><view class="section-title"><view class="title-line" style="{{'background-color:'+($root.m14)+';'}}"></view><text>年龄</text></view><view class="location-row"><text class="location-hint">请输入您身份证上面的年龄</text><view class="age-input-container"><view class="age-input-wrapper single-age"><input class="age-input" type="number" placeholder="请输入年龄" maxlength="2" data-event-opts="{{[['input',[['__set_model',['','ageValue','$event',[]]]]]]}}" value="{{ageValue}}" bindinput="__e"/><text class="age-label">岁</text></view></view></view></view></scroll-view><view class="footer safe-area-bottom"><view class="btn-group"><button data-event-opts="{{[['tap',[['submitFilter',['$event']]]]]}}" class="next-btn full-width" style="{{'background:'+('linear-gradient(135deg, '+$root.m15+', '+$root.m16+'dd)')+';'}}" bindtap="__e">开始匹配</button></view></view></view>