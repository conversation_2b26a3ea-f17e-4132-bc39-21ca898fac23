<view class="container" style="{{'background-color:'+('pageinfo.bgcolor')+';'}}"><view class="header">{{''+tit+''}}<view class="pickerview"><uni-datetime-picker vue-id="37c3840c-1" type="date" value="{{date_time}}" data-event-opts="{{[['^change',[['clickTime']]],['^input',[['__set_model',['','date_time','$event',[]]]]]]}}" bind:change="__e" bind:input="__e" bind:__l="__l" vue-slots="{{['default']}}"><view style="display:flex;align-items:center;"><text style="color:#999999;font-size:20px;">📅</text>{{''+(date_time||'请选择你要运动的时间')+''}}</view></uni-datetime-picker></view></view><view style="margin:50rpx 20rpx 0;background:#f5f5f5;border-radius:70rpx 70rpx 0 0;"><view class="tabs"><uni-data-picker class="tabs_item" vue-id="37c3840c-2" popup-title="请选择" localdata="{{list1}}" value="{{list1_id}}" data-event-opts="{{[['^change',[['onchange1']]],['^input',[['__set_model',['','list1_id','$event',[]]]]]]}}" bind:change="__e" bind:input="__e" bind:__l="__l" vue-slots="{{['default']}}">{{list1_name+"∨"}}</uni-data-picker><uni-data-picker class="tabs_item" vue-id="37c3840c-3" popup-title="请选择" localdata="{{list2}}" value="{{list2_id}}" data-event-opts="{{[['^change',[['onchange2']]],['^input',[['__set_model',['','list2_id','$event',[]]]]]]}}" bind:change="__e" bind:input="__e" bind:__l="__l" vue-slots="{{['default']}}">{{list2_name+"∨"}}</uni-data-picker><uni-data-picker class="tabs_item" vue-id="37c3840c-4" popup-title="请选择" localdata="{{list3}}" value="{{list3_id}}" data-event-opts="{{[['^change',[['onchange3']]],['^input',[['__set_model',['','list3_id','$event',[]]]]]]}}" bind:change="__e" bind:input="__e" bind:__l="__l" vue-slots="{{['default']}}">{{list3_name+"∨"}}</uni-data-picker></view><scroll-view class="scroll-Y" scroll-y="true" data-event-opts="{{[['scrolltoupper',[['upper',['$event']]]]]}}" bindscrolltoupper="__e"><block wx:for="{{list}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['goItem',[index]]]]]}}" class="item" bindtap="__e"><image class="itemImg _img" src="{{item.logo}}"></image><view class="itemMain"><view class="name">{{item.title}}</view><view class="info"><view class="tag">{{item.score}}</view>{{'5599人预定.'+item.visit+'浏览'}}</view><view class="address"><view class="detail">{{tit+"/"+item.district}}</view><view class="distance">{{item.distance}}</view></view><view class="other"><block wx:for="{{item.services}}" wx:for-item="ite" wx:for-index="ind" wx:key="ind"><view class="otherTag">{{''+ite+''}}</view></block></view></view></view></block></scroll-view></view></view>