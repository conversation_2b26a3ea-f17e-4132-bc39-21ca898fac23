<view><block wx:if="{{isload}}"><block><view class="orderinfo"><view class="item"><text class="t1">ID</text><text class="t2">{{member.id}}</text></view><view class="item"><text class="t1">头像</text><view class="t2"><image style="width:80rpx;height:80rpx;" src="{{member.headimg}}"></image></view></view><view class="item"><text class="t1">昵称</text><text class="t2">{{member.nickname}}</text></view><view class="item"><text class="t1">地区</text><text class="t2">{{(member.province?member.province:'')+(member.city?member.city:'')}}</text></view><view class="item"><text class="t1">加入时间</text><text class="t2">{{member.createtime}}</text></view><view class="item"><text class="t1">姓名</text><text class="t2">{{member.realname?member.realname:''}}</text></view><view class="item"><text class="t1">电话</text><text class="t2">{{member.tel?member.tel:''}}</text></view><view class="item"><text class="t1">{{$root.m0}}</text><text class="t2">{{member.money}}</text></view><view class="item"><text class="t1">{{$root.m1}}</text><text class="t2">{{member.score}}</text></view><view class="item"><text class="t1">等级</text><text class="t2">{{member.levelname}}</text></view><block wx:if="{{member.remark}}"><view class="item"><text class="t1">备注</text><text class="t2">{{member.remark}}</text></view></block><block wx:if="{{member.mendian_member_levelup_fenhong}}"><view class="item"><text class="t1">门店</text><text class="t2">{{member.mdname?member.mdname:'无'}}</text></view></block><block wx:if="{{ordershow}}"><view class="item" style="justify-content:space-between;"><text class="t1" style="color:#007aff;">商城订单</text><view class="flex" data-url="{{'/adminExt/order/shoporder?mid='+member.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">{{member.ordercount+''}}<text class="iconfont iconjiantou" style="color:#999;font-weight:normal;margin-top:2rpx;"></text></view></view></block></view><view style="width:100%;height:120rpx;"></view><view class="bottom"><view class="btn" data-id="{{member.id}}" data-event-opts="{{[['tap',[['recharge',['$event']]]]]}}" bindtap="__e">充值</view><view class="btn" data-id="{{member.id}}" data-event-opts="{{[['tap',[['consume',['$event']]]]]}}" bindtap="__e">消费</view><view class="btn" data-id="{{member.id}}" data-event-opts="{{[['tap',[['addscore',['$event']]]]]}}" bindtap="__e">{{"加"+$root.m2}}</view><view class="btn" data-id="{{member.id}}" data-event-opts="{{[['tap',[['changelv',['$event']]]]]}}" bindtap="__e">修改等级</view><view class="btn" data-id="{{member.id}}" data-event-opts="{{[['tap',[['remark',['$event']]]]]}}" bindtap="__e">备注</view><view class="btn" data-url="{{'/adminExt/member/history?id='+member.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">足迹</view><block wx:if="{{member.showrichinfo}}"><view class="btn" data-url="{{'richinfo?id='+member.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">介绍</view></block></view><uni-popup class="vue-ref" vue-id="22c392ef-1" id="rechargeDialog" type="dialog" data-ref="rechargeDialog" bind:__l="__l" vue-slots="{{['default']}}"><uni-popup-dialog vue-id="{{('22c392ef-2')+','+('22c392ef-1')}}" mode="input" title="充值" value="" placeholder="请输入充值金额" data-event-opts="{{[['^confirm',[['rechargeConfirm']]]]}}" bind:confirm="__e" bind:__l="__l"></uni-popup-dialog></uni-popup><uni-popup class="vue-ref" vue-id="22c392ef-3" id="consumeDialog" type="dialog" data-ref="consumeDialog" bind:__l="__l" vue-slots="{{['default']}}"><uni-popup-dialog vue-id="{{('22c392ef-4')+','+('22c392ef-3')}}" mode="input" title="消费" value="" placeholder="请输入消费金额" data-event-opts="{{[['^confirm',[['consumeConfirm']]]]}}" bind:confirm="__e" bind:__l="__l"></uni-popup-dialog></uni-popup><uni-popup class="vue-ref" vue-id="22c392ef-5" id="addscoreDialog" type="dialog" data-ref="addscoreDialog" bind:__l="__l" vue-slots="{{['default']}}"><uni-popup-dialog vue-id="{{('22c392ef-6')+','+('22c392ef-5')}}" mode="input" title="{{'加'+$root.m3}}" value="" placeholder="{{'请输入增加'+$root.m4+'数'}}" data-event-opts="{{[['^confirm',[['addscoreConfirm']]]]}}" bind:confirm="__e" bind:__l="__l"></uni-popup-dialog></uni-popup><uni-popup class="vue-ref" vue-id="22c392ef-7" id="remarkDialog" type="dialog" data-ref="remarkDialog" bind:__l="__l" vue-slots="{{['default']}}"><uni-popup-dialog vue-id="{{('22c392ef-8')+','+('22c392ef-7')}}" mode="input" title="设置备注" value="" placeholder="请输入备注" data-event-opts="{{[['^confirm',[['remarkConfirm']]]]}}" bind:confirm="__e" bind:__l="__l"></uni-popup-dialog></uni-popup><uni-popup class="vue-ref" vue-id="22c392ef-9" id="dialogChangelv" type="dialog" data-ref="dialogChangelv" bind:__l="__l" vue-slots="{{['default']}}"><view class="uni-popup-dialog"><view class="uni-dialog-title"><text class="uni-dialog-title-text">请选择等级</text></view><view class="uni-dialog-content"><picker style="width:100%;font-size:28rpx;border:1px #eee solid;padding:10rpx;height:70rpx;border-radius:4px;flex:1;" value="{{index2}}" range="{{levelList2}}" data-event-opts="{{[['change',[['levelChange',['$event']]]]]}}" bindchange="__e"><view class="picker">{{levelList2[index2]}}</view></picker></view><view class="uni-dialog-button-group"><view data-event-opts="{{[['tap',[['dialogChangelvClose',['$event']]]]]}}" class="uni-dialog-button" bindtap="__e"><text class="uni-dialog-button-text">取消</text></view><view data-event-opts="{{[['tap',[['confirmChangelv',['$event']]]]]}}" class="uni-dialog-button uni-border-left" bindtap="__e"><text class="uni-dialog-button-text uni-button-color">确定</text></view></view></view></uni-popup></block></block><popmsg class="vue-ref" vue-id="22c392ef-10" data-ref="popmsg" bind:__l="__l"></popmsg><block wx:if="{{loading}}"><loading vue-id="22c392ef-11" bind:__l="__l"></loading></block></view>