<view class="dp-product" style="{{'background-color:'+(params.bgcolor)+';'+('margin:'+(params.margin_y*2.2+'rpx '+params.margin_x*2.2+'rpx 0')+';')+('padding:'+(params.padding_y*2.2+'rpx '+params.padding_x*2.2+'rpx')+';')+('width:'+('calc(100% - '+params.margin_x*2.2*2+'rpx)')+';')}}"><block wx:if="{{params.style=='1'||params.style=='2'||params.style=='3'}}"><dp-product-item vue-id="4401b2fc-1" showstyle="{{params.style}}" data="{{displayData}}" saleimg="{{params.saleimg}}" showname="{{params.showname}}" showprice="{{params.showprice}}" showsales="{{params.showsales}}" showcart="{{params.showcart}}" cartimg="{{params.cartimg}}" idfield="proid" menuindex="{{menuindex}}" probgcolor="{{params.probgcolor}}" params="{{$root.a0}}" bind:__l="__l"></dp-product-item></block><block wx:if="{{params.style=='list'}}"><dp-product-itemlist vue-id="4401b2fc-2" data="{{displayData}}" saleimg="{{params.saleimg}}" showname="{{params.showname}}" showprice="{{params.showprice}}" showsales="{{params.showsales}}" showcart="{{params.showcart}}" cartimg="{{params.cartimg}}" idfield="proid" menuindex="{{menuindex}}" probgcolor="{{params.probgcolor}}" bind:__l="__l"></dp-product-itemlist></block><block wx:if="{{params.style=='line'}}"><dp-product-itemline vue-id="4401b2fc-3" data="{{displayData}}" saleimg="{{params.saleimg}}" showname="{{params.showname}}" showprice="{{params.showprice}}" showsales="{{params.showsales}}" showcart="{{params.showcart}}" cartimg="{{params.cartimg}}" idfield="proid" menuindex="{{menuindex}}" probgcolor="{{params.probgcolor}}" params="{{$root.a1}}" bind:__l="__l"></dp-product-itemline></block><block wx:if="{{params.style=='waterfall'}}"><dp-product-waterfall vue-id="4401b2fc-4" list="{{displayData}}" saleimg="{{params.saleimg}}" showname="{{params.showname}}" showprice="{{params.showprice}}" showsales="{{params.showsales}}" showcart="{{params.showcart}}" showstock="{{params.showstock}}" showbname="{{params.showbname}}" showcoupon="{{params.showcoupon}}" cartimg="{{params.cartimg}}" idfield="proid" menuindex="{{menuindex}}" probgcolor="{{params.probgcolor}}" data-event-opts="{{[['^imageLoad',[['onWaterfallLoad']]]]}}" bind:imageLoad="__e" bind:__l="__l"></dp-product-waterfall></block><block wx:if="{{params.style=='mixed-row'}}"><dp-product-mixed-row vue-id="4401b2fc-5" list="{{displayData}}" saleimg="{{params.saleimg}}" showname="{{params.showname}}" showprice="{{params.showprice}}" showsales="{{params.showsales}}" showcart="{{params.showcart}}" showstock="{{params.showstock}}" showbname="{{params.showbname}}" showcoupon="{{params.showcoupon}}" cartimg="{{params.cartimg}}" idfield="proid" menuindex="{{menuindex}}" probgcolor="{{params.probgcolor}}" params="{{$root.a2}}" data-event-opts="{{[['^imageLoad',[['onMixedLoad']]]]}}" bind:imageLoad="__e" bind:__l="__l"></dp-product-mixed-row></block><block wx:if="{{params.style=='mixed-waterfall'}}"><dp-product-mixed-waterfall vue-id="4401b2fc-6" list="{{displayData}}" saleimg="{{params.saleimg}}" showname="{{params.showname}}" showprice="{{params.showprice}}" showsales="{{params.showsales}}" showcart="{{params.showcart}}" showstock="{{params.showstock}}" showbname="{{params.showbname}}" showcoupon="{{params.showcoupon}}" cartimg="{{params.cartimg}}" idfield="proid" menuindex="{{menuindex}}" probgcolor="{{params.probgcolor}}" data-event-opts="{{[['^imageLoad',[['onWaterfallLoad']]]]}}" bind:imageLoad="__e" bind:__l="__l"></dp-product-mixed-waterfall></block><block wx:if="{{$root.g0}}"><view class="load-more-area vue-ref" id="loadMoreArea" data-ref="loadMoreArea"><block wx:if="{{loading}}"><view class="loading"><text>加载中...</text></view></block><block wx:else><block wx:if="{{hasMoreData}}"><view class="can-load-more"><text>上拉加载更多</text></view></block><block wx:else><view class="no-more"><text>没有更多数据了</text></view></block></block></view></block></view>