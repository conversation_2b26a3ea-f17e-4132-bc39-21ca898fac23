<view class="container"><block><view class="view-show" style="padding:30rpx;padding-bottom:200rpx;"><view style="margin-bottom:30rpx;"><view style="display:flex;align-items:center;margin-bottom:40rpx;"><view style="height:30rpx;width:8rpx;background-color:#05aaf6;margin-right:10rpx;"></view><label style="font-weight:bold;font-size:36rpx;" class="_span">高考分数线：</label></view><view style="display:flex;margin-bottom:20rpx;"><view class="tag-item" style="margin-right:10rpx;"><picker value="{{index}}" range="{{array}}" data-event-opts="{{[['change',[['areaChange',['$event','1']]]]]}}" bindchange="__e"><view class="uni-input">{{college.areaName+''}}</view></picker></view><view class="tag-item" style="margin-right:10rpx;"><picker value="{{index}}" range="{{arrayYear}}" data-event-opts="{{[['change',[['yearChange',['$event','1']]]]]}}" bindchange="__e"><view class="uni-input">{{college.year}}</view></picker></view><view class="tag-item" style="margin-right:10rpx;"><picker value="{{index}}" range="{{arrayGroup}}" data-event-opts="{{[['change',[['groupChange',['$event','1']]]]]}}" bindchange="__e"><view class="uni-input">{{college.group}}</view></picker></view></view><view class="table-box"><view class="table-header"><view>录取批次</view><view>最低分/位次</view><view>省控线</view><view>招生类型</view><view>选科要求</view></view><block wx:for="{{data['高考分数线']}}" wx:for-item="item" wx:for-index="__i0__"><view class="table-content"><view>{{item.batch}}</view><view>{{item.min_score}}</view><view>{{item.control_line}}</view><view>{{item.admission_type}}</view><view>{{item.requirement}}</view></view></block></view></view><view style="margin-bottom:20rpx;"><view style="display:flex;align-items:center;margin-bottom:40rpx;"><view style="height:30rpx;width:8rpx;background-color:#05aaf6;margin-right:10rpx;"></view><label style="font-weight:bold;font-size:36rpx;" class="_span">专业分数线：</label></view><view style="display:flex;margin-bottom:20rpx;"><view class="tag-item" style="margin-right:10rpx;"><picker value="{{index}}" range="{{array}}" data-event-opts="{{[['change',[['areaChange',['$event','2']]]]]}}" bindchange="__e"><view class="uni-input">{{speciality.areaName+''}}</view></picker></view><view class="tag-item" style="margin-right:10rpx;"><picker value="{{index}}" range="{{arrayYear}}" data-event-opts="{{[['change',[['yearChange',['$event','2']]]]]}}" bindchange="__e"><view class="uni-input">{{speciality.year}}</view></picker></view><view class="tag-item" style="margin-right:10rpx;"><picker value="{{index}}" range="{{arrayGroup}}" data-event-opts="{{[['change',[['groupChange',['$event','2']]]]]}}" bindchange="__e"><view class="uni-input">{{speciality.group}}</view></picker></view><view class="tag-item" style="margin-right:10rpx;"><picker value="{{index}}" range="{{arrayBatch}}" data-event-opts="{{[['change',[['batchChange',['$event','2']]]]]}}" bindchange="__e"><view class="uni-input">{{speciality.batch}}</view></picker></view></view><view class="table-box"><view class="table-header"><view>专业名称</view><view>选科要求</view><view>最低分/位次</view><view>录取批次</view></view><block wx:for="{{data['专业分数线']}}" wx:for-item="item" wx:for-index="__i1__"><view class="table-content"><view>{{item.zhuanye_name}}</view><view>{{item.requirement}}</view><view>{{item.min_score}}</view><view>{{item.batch}}</view></view></block></view></view><view style="margin-bottom:20rpx;"><view style="display:flex;align-items:center;margin-bottom:40rpx;"><view style="height:30rpx;width:8rpx;background-color:#05aaf6;margin-right:10rpx;"></view><label style="font-weight:bold;font-size:36rpx;" class="_span">分类单招分数线：</label></view><view style="display:flex;margin-bottom:20rpx;"><view class="tag-item" style="margin-right:10rpx;"><picker value="{{index}}" range="{{array}}" data-event-opts="{{[['change',[['areaChange',['$event','3']]]]]}}" bindchange="__e"><view class="uni-input">{{classify.areaName+''}}</view></picker></view><view class="tag-item" style="margin-right:10rpx;"><picker value="{{index}}" range="{{arrayYear}}" data-event-opts="{{[['change',[['yearChange',['$event','3']]]]]}}" bindchange="__e"><view class="uni-input">{{classify.year}}</view></picker></view><view class="tag-item" style="margin-right:10rpx;"><picker value="{{index}}" range="{{arrayType}}" data-event-opts="{{[['change',[['typeChange',['$event','3']]]]]}}" bindchange="__e"><view class="uni-input">{{classify.type}}</view></picker></view></view><view class="table-box"><view class="table-header"><view>录取批次</view><view>最低分/位次</view><view>省控线</view><view>招生类型</view><view>选科要求</view></view><block wx:for="{{data['分类单招分数线']}}" wx:for-item="item" wx:for-index="__i2__"><view class="table-content"><view>{{item.batch}}</view><view>{{item.min_score}}</view><view>{{item.control_line}}</view><view>{{item.admission_type}}</view><view>{{item.requirement}}</view></view></block></view></view><view style="margin-bottom:20rpx;"><view style="display:flex;align-items:center;margin-bottom:40rpx;"><view style="height:30rpx;width:8rpx;background-color:#05aaf6;margin-right:10rpx;"></view><label style="font-weight:bold;font-size:36rpx;" class="_span">专升本分数线：</label></view><view style="display:flex;margin-bottom:20rpx;"><view class="tag-item" style="margin-right:10rpx;"><picker value="{{index}}" range="{{array}}" data-event-opts="{{[['change',[['areaChange',['$event','4']]]]]}}" bindchange="__e"><view class="uni-input">{{upgradation.areaName+''}}</view></picker></view><view class="tag-item" style="margin-right:10rpx;"><picker value="{{index}}" range="{{arrayYear}}" data-event-opts="{{[['change',[['yearChange',['$event','4']]]]]}}" bindchange="__e"><view class="uni-input">{{upgradation.year}}</view></picker></view><view class="tag-item" style="margin-right:10rpx;"><picker value="{{index}}" range="{{arraySubject}}" data-event-opts="{{[['change',[['subjectChange',['$event','4']]]]]}}" bindchange="__e"><view class="uni-input">{{upgradation.subject}}</view></picker></view></view><view class="table-box"><view class="table-header"><view>录取批次</view><view>最低分/位次</view><view>省控线</view></view><block wx:for="{{data['专升本分数线']}}" wx:for-item="item" wx:for-index="__i3__"><view class="table-content"><view>{{item.batch}}</view><view>{{item.min_score}}</view><view>{{item.control_line}}</view></view></block></view></view></view></block></view>