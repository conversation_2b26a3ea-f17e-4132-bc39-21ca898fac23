<view class="container"><block wx:if="{{isload}}"><block><view class="view-show"><view class="search-container" data-url="{{'/shopPackage/shop/search?bid='+bid}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e"><view class="search-box"><image class="img" src="/static/img/search_ico.png"></image><view class="search-text">搜索感兴趣的商品</view></view></view><view class="content" style="height:calc(100% - 94rpx);overflow:hidden;display:flex;"><scroll-view class="{{['nav_left',menuindex>-1?'tabbarbot':'notabbarbot']}}" scrollWithAnimation="{{animation}}" scroll-y="true"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><view class="{{['nav_left_items',index===currentActiveIndex?'active':'']}}" data-root-item-id="{{item.$orig.id}}" data-root-item-index="{{index}}" data-event-opts="{{[['tap',[['clickRootItem',['$event']]]]]}}" bindtap="__e"><view class="before" style="{{'background:'+(item.m0)+';'}}"></view>{{item.$orig.name}}</view></block></block></scroll-view><view class="nav_right"><view class="nav_right-content"><scroll-view class="{{['detail-list',menuindex>-1?'tabbarbot':'notabbarbot']}}" scrollWithAnimation="{{animation}}" scroll-y="true"><block wx:if="{{data[currentActiveIndex].banner}}"><view><image style="width:100%;height:auto;margin-bottom:40rpx;" src="{{data[currentActiveIndex].banner}}" mode="widthFix" data-id="{{data[currentActiveIndex].id}}" data-event-opts="{{[['tap',[['gotoCatproductPage',['$event']]]]]}}" bindtap="__e"></image></view></block><block wx:else><view class="head"><view class="txt">{{data[currentActiveIndex].name}}</view><view class="show-all" data-id="{{data[currentActiveIndex].id}}" data-event-opts="{{[['tap',[['gotoCatproductPage',['$event']]]]]}}" bindtap="__e">查看全部<text class="iconfont iconjiantou"></text></view></view></block><view class="detail"><block wx:for="{{data[currentActiveIndex].child}}" wx:for-item="item" wx:for-index="itemIndex" wx:key="itemIndex"><view class="detail-item" style="{{((itemIndex+1)%3===0?'margin-right: 0':'')}}" data-id="{{item.id}}" form-type="submit" data-event-opts="{{[['tap',[['gotoCatproductPage',['$event']]]]]}}" catchtap="__e"><image class="img" src="{{item.pic}}"></image><view class="txt">{{item.name}}</view></view></block></view><block wx:if="{{$root.g0==0}}"><nodata vue-id="c8a8d1fa-1" bind:__l="__l"></nodata></block></scroll-view></view></view></view></view></block></block><block wx:if="{{loading}}"><loading vue-id="c8a8d1fa-2" loadstyle="left:62.5%" bind:__l="__l"></loading></block><dp-tabbar vue-id="c8a8d1fa-3" opt="{{opt}}" data-event-opts="{{[['^getmenuindex',[['getmenuindex']]]]}}" bind:getmenuindex="__e" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="c8a8d1fa-4" data-ref="popmsg" bind:__l="__l"></popmsg></view>