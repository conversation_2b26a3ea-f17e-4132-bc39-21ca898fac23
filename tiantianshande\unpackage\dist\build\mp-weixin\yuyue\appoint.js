(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["yuyue/appoint"],{3274:function(e,t,o){},"401d":function(e,t,o){"use strict";o.r(t);var i=o("6de2"),a=o.n(i);for(var s in i)["default"].indexOf(s)<0&&function(e){o.d(t,e,(function(){return i[e]}))}(s);t["default"]=a.a},6575:function(e,t,o){"use strict";(function(e,t){var i=o("47a9");o("06e9");i(o("3240"));var a=i(o("d5e5"));e.__webpack_require_UNI_MP_PLUGIN__=o,t(a.default)}).call(this,o("3223")["default"],o("df3c")["createPage"])},"6de2":function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i=getApp(),a={data:function(){return{orderId:"",orderInfo:{product:{pic:"",name:"",sell_price:0},order_no:"",pay_time:""},selectedDate:"",selectedTime:"",timeList:[],dateList:[],startDate:"",endDate:"",loading:!0,activePeriod:"all",timePeriods:[{label:"全部",value:"all"},{label:"上午",value:"上午"},{label:"下午",value:"下午"},{label:"傍晚",value:"傍晚"}],productId:"",dateType:1,dayDate:"",dateFromUrl:!1}},computed:{canSubmit:function(){return this.selectedDate&&this.selectedTime},filteredTimeList:function(){var e=this;return"all"===this.activePeriod?this.timeList:this.timeList.filter((function(t){return t.period===e.activePeriod}))},formattedSelectedDate:function(){if(!this.selectedDate)return"";if(this.selectedDate.includes("-")){var e=this.selectedDate.split("-");return"".concat(e[0],"年").concat(e[1],"月").concat(e[2],"日")}return this.selectedDate}},onLoad:function(e){console.log("加载预约页面，参数:",e),this.initDateRange(),e&&e.id?(this.orderId=e.id,e.date?(console.log("已传入日期参数:",e.date),this.selectedDate=e.date,this.dateFromUrl=!0):(console.log("未传入日期参数，不会向接口发送日期"),this.dateFromUrl=!1),this.getOrderInfo()):i.error("未找到订单信息")},methods:{getOrderInfo:function(){var e=this;i.showLoading("加载订单信息"),console.log("当前选择的日期(获取订单前):",e.selectedDate),i.get("ApiYuyue/orderDetail",{id:e.orderId},(function(t){if(i.showLoading(!1),console.log("获取订单信息原始返回:",t),1==t.status&&t.data?(console.log("标准接口返回结构 status+data"),e.orderInfo=t.data):t.detail?(console.log("直接detail结构返回"),e.orderInfo=t.detail):(console.log("使用整个返回对象"),e.orderInfo=t),console.log("处理后的订单信息:",e.orderInfo),e.orderInfo.product||(e.orderInfo.product={pic:e.orderInfo.propic||"",name:e.orderInfo.proname||"商品信息",sell_price:e.orderInfo.product_price||e.orderInfo.totalprice||0},console.log("构建的产品信息:",e.orderInfo.product)),e.orderInfo.order_no||(e.orderInfo.order_no=e.orderInfo.ordernum||""),!e.orderInfo.pay_time)if(e.orderInfo.paytime)if("number"===typeof e.orderInfo.paytime){var o=new Date(1e3*e.orderInfo.paytime);e.orderInfo.pay_time=o.getFullYear()+"-"+("0"+(o.getMonth()+1)).slice(-2)+"-"+("0"+o.getDate()).slice(-2)+" "+("0"+o.getHours()).slice(-2)+":"+("0"+o.getMinutes()).slice(-2)+":"+("0"+o.getSeconds()).slice(-2)}else e.orderInfo.pay_time=e.orderInfo.paytime;else e.orderInfo.pay_time="";e.productId=e.orderInfo.product_id||e.orderInfo.proid||"",console.log("提取的商品ID:",e.productId),console.log("当前选择的日期(订单信息处理后):",e.selectedDate),e.loading=!1,e.getDateList()}))},initDateRange:function(){var e=new Date;this.startDate=e.toISOString().split("T")[0];var t=new Date;t.setDate(e.getDate()+30),this.endDate=t.toISOString().split("T")[0],this.dayDate=e.getFullYear()+"-"+(e.getMonth()+1<10?"0"+(e.getMonth()+1):e.getMonth()+1)+"-"+(e.getDate()<10?"0"+e.getDate():e.getDate()),this.selectedDate||(this.selectedDate=this.dayDate,console.log("初始化选中当天日期:",this.selectedDate))},getDateList:function(){var e=this,t=e.productId||e.orderId;if(console.log("获取日期列表前，当前选择的日期:",e.selectedDate),console.log("当前系统日期:",e.dayDate),console.log("日期是否来自URL:",e.dateFromUrl),!t)return console.log("未找到商品ID，使用本地生成的日期列表"),void e.generateDefaultDateList();i.showLoading("获取可预约日期");var o={id:t};e.dateFromUrl&&e.selectedDate?(o.date=e.selectedDate,console.log("将URL指定的日期传递给接口:",e.selectedDate)):console.log("日期未从URL获取，不传递给接口"),console.log("获取日期列表参数:",o),i.get("ApiYuyue/getDateList",o,(function(t){i.showLoading(!1),console.log("获取日期列表返回:",t),1==t.status&&t.datelist&&t.datelist.length>0?(e.dateType=t.rqtype||1,t.daydate&&(e.dayDate=t.daydate,console.log("接口返回的当前日期:",e.dayDate)),e.processDateList(t.datelist)):(console.log("接口未返回有效的日期列表，使用默认生成"),e.generateDefaultDateList())}),(function(t){i.showLoading(!1),console.log("获取日期列表失败，使用默认列表:",t),e.generateDefaultDateList()}))},processDateList:function(e){var t=this;console.log("处理接口返回的日期列表:",e),this.dateList=[];var o=new Date,a=o.getFullYear()+"-"+(o.getMonth()+1<10?"0"+(o.getMonth()+1):o.getMonth()+1)+"-"+(o.getDate()<10?"0"+o.getDate():o.getDate());if(e.forEach((function(e){var o="",i="";if(e.date){var s=e.date.split("月");s.length>1&&(o=s[0].replace("月",""),i=s[1])}var l="";if(e.full_date)l=e.full_date;else if(e.year&&e.date){var r=e.year.replace("年",""),n=e.date.split("月")[0],c=e.date.split("月")[1];l="".concat(r,"-").concat(n,"-").concat(c)}var d=i,u=e.weeks||"";t.dateList.push({value:l,year:e.year?e.year.replace("年",""):"",month:o,day:d,weekday:u,isToday:l===a})})),console.log("处理后的日期列表:",this.dateList),this.dateList.length>0){for(var s=!1,l=!1,r=-1,n=0;n<this.dateList.length;n++)if(this.dateList[n].isToday){l=!0,r=n;break}if(this.selectedDate){for(var c=0;c<this.dateList.length;c++)if(this.dateList[c].value===this.selectedDate){console.log("找到预选日期:",this.selectedDate),s=!0;break}s||(l?(console.log("选择今天的日期:",this.dateList[r].value),this.selectedDate=this.dateList[r].value):(console.log("预选日期不在可用日期列表中，使用第一个可用日期"),this.selectedDate=this.dateList[0].value),i.toast("选择的日期不可用，已为您选择最近可用日期"))}else l?(this.selectedDate=this.dateList[r].value,console.log("选择今天的日期:",this.selectedDate)):(this.selectedDate=this.dateList[0].value,console.log("没有找到今天的日期，选择第一个日期:",this.selectedDate));this.$nextTick((function(){t.getTimeList()}))}},generateDefaultDateList:function(){var e=this;this.dateList=[];for(var t=["周日","周一","周二","周三","周四","周五","周六"],o=new Date,i=o.toISOString().split("T")[0],a=0;a<14;a++){var s=new Date;s.setDate(o.getDate()+a);var l=s.getFullYear(),r=s.getMonth()+1,n=s.getDate(),c=t[s.getDay()],d=l+"-"+(r<10?"0"+r:r)+"-"+(n<10?"0"+n:n),u=n.toString();0===a?u="今天":1===a&&(u="明天"),this.dateList.push({value:d,year:l,month:r,day:u,weekday:c,isToday:0===a})}console.log("生成的默认日期列表:",this.dateList),this.selectedDate=i,console.log("默认选中当天日期:",this.selectedDate),this.$nextTick((function(){e.getTimeList()}))},selectDate:function(e){this.selectedDate=e.value,this.selectedTime="",this.dateFromUrl=!1,console.log("用户选择了新日期, dateFromUrl设为false"),this.getTimeList()},switchPeriod:function(e){this.activePeriod=e},getTimeList:function(){var e=this;i.showLoading("加载时间段");var t=e.selectedDate;t&&t.includes("年")&&(t=t.replace("年","-").replace("月","-").replace("日","")),console.log("当前选择的日期:",t);var o={order_id:e.orderId};o.date=t,console.log("获取时间段参数:",o),i.get("ApiYuyue/getTimeList",o,(function(t){if(i.showLoading(!1),console.log("获取时间段返回:",t),"string"===typeof t&&-1!==t.indexOf("<!DOCTYPE html>")||0===t.status&&t.msg)return console.log("接口报错，使用默认时间段"),void e.createDefaultTimeSlots();var o=[];1==t.status&&t.data||t.data?o=t.data:Array.isArray(t)&&(o=t),e.processTimeData(o)}),(function(t){i.showLoading(!1),console.log("时间段获取失败，使用默认时间段:",t),e.createDefaultTimeSlots()}))},processTimeData:function(e){var t=this;if(console.log("提取的时间段数据:",e),e&&e.length>0){var o=new Date,a=o.getHours(),s=this.selectedDate===o.toISOString().split("T")[0];console.log("处理时间段数据 - 当前小时:",a,"是否今天:",s),this.timeList=e.map((function(e){var t=void 0!==e.disabled?!e.disabled:void 0===e.available||e.available;if(s&&t){var o=-1;if(e.time){var i=e.time.split(":");if(i.length>0)o=parseInt(i[0]);else if(e.time.includes("-")){var l=e.time.split("-")[0].split(":");l.length>0&&(o=parseInt(l[0]))}}o>=0&&o<=a&&(t=!1,e.reason="已过期",console.log("时间段已过期:",e.time))}return{time:e.time,available:t,period:e.period||"",reason:e.reason||"不可预约",booked_count:e.booked_count||0,max_bookings:e.max_bookings||999999,full_time:e.full_time||""}})),console.log("处理后的时间段数据:",this.timeList);var l=!1;if(this.selectedTime){var r=this.timeList.some((function(e){return e.time===t.selectedTime&&e.available}));if(r)l=!0;else{console.log("已选择的时间段不可用:",this.selectedTime);for(var n=0;n<this.timeList.length;n++)if(this.timeList[n].available){this.selectedTime=this.timeList[n].time,l=!0,console.log("重新选择第一个可用时间段:",this.selectedTime);break}}}else for(var c=0;c<this.timeList.length;c++)if(this.timeList[c].available){this.selectedTime=this.timeList[c].time,l=!0,console.log("自动选择第一个可用时间段:",this.selectedTime);break}l||(i.toast("当前日期没有可用的预约时间段，请选择其他日期"),this.selectedTime="")}else this.createDefaultTimeSlots()},createDefaultTimeSlots:function(){this.timeList=[];var e=new Date,t=e.getHours(),o="";console.log("创建默认时间段，当前小时:",t),console.log("当前选择的日期:",this.selectedDate,"今天日期:",e.toISOString().split("T")[0]);var i=this.selectedDate===e.toISOString().split("T")[0];console.log("选择的日期是否为今天:",i);for(var a=8;a<20;a++){var s=a<10?"0"+a+":00":a+":00",l=a+1<10?"0"+(a+1)+":00":a+1+":00",r=s+"-"+l,n="";n=a<12?"上午":a<17?"下午":"傍晚";var c=!1,d="";i&&a<=t&&(c=!0,d="已过期",console.log("时间段已过期:",r)),c||o||(o=r,console.log("找到第一个可用时间段:",r)),this.timeList.push({time:r,available:!c,period:n,reason:d,booked_count:0,max_bookings:999999,full_time:this.selectedDate+" "+r})}!this.selectedTime&&o?(this.selectedTime=o,console.log("自动选择默认时间段:",this.selectedTime)):console.log("保留已选择的时间段:",this.selectedTime),console.log("创建的默认时间段:",this.timeList)},selectTime:function(e){console.log("选择时间段:",e),e.available?this.selectedTime=e.time:e.reason?i.toast(e.reason):i.toast("该时间段不可预约")},submitAppoint:function(){var e=this;if(e.selectedDate&&e.selectedTime){var t=e.selectedDate;t&&t.includes("年")&&(t=t.replace("年","-").replace("月","-").replace("日",""));var o={id:e.orderId,yydate:t,yytime:e.selectedTime};console.log("提交预约参数:",o),i.showLoading("提交预约"),i.post("ApiYuyue/appointTime",o,(function(o){i.showLoading(!1),console.log("预约接口返回:",o);var a=!1,s="",l=!1;1==o.status?(a=!0,s=o.msg||"预约成功",l=o.needSelectWorker||!1):200!=o.code&&1!=o.code||(a=!0,s=o.message||o.msg||"预约成功",l=o.needSelectWorker||!1),a?i.alert(s,(function(){if(l)try{console.log("转到选择服务人员页面");var o="/yuyue/selectworker?id="+e.orderId+"&yydate="+t;i.goto(o),console.log("导航到选择服务人员页面:",o)}catch(a){console.error("导航失败:",a),i.error("页面跳转失败")}else i.goto("/yuyue/orderlist")})):i.error(o.msg||o.message||"预约失败")}))}else i.error("请选择预约日期和时间")}}};t.default=a},c0f3:function(e,t,o){"use strict";var i=o("3274"),a=o.n(i);a.a},d5e5:function(e,t,o){"use strict";o.r(t);var i=o("ec0e"),a=o("401d");for(var s in a)["default"].indexOf(s)<0&&function(e){o.d(t,e,(function(){return a[e]}))}(s);o("c0f3");var l=o("828b"),r=Object(l["a"])(a["default"],i["b"],i["c"],!1,null,null,null,!1,i["a"],void 0);t["default"]=r.exports},ec0e:function(e,t,o){"use strict";o.d(t,"b",(function(){return i})),o.d(t,"c",(function(){return a})),o.d(t,"a",(function(){}));var i=function(){var e=this,t=e.$createElement,o=(e._self._c,e.orderInfo&&e.orderInfo.product?e.t("color1"):null),i=e.__map(e.dateList,(function(t,o){var i=e.__get_orig(t),a=e.selectedDate===t.value?e.t("color1"):null,s=e.selectedDate===t.value?e.t("color1rgb"):null;return{$orig:i,m1:a,m2:s}})),a=e.selectedDate?e.__map(e.timePeriods,(function(t,o){var i=e.__get_orig(t),a=e.activePeriod===t.value?e.t("color1"):null,s=e.activePeriod===t.value?e.t("color1rgb"):null;return{$orig:i,m3:a,m4:s}})):null,s=e.selectedDate?e.__map(e.filteredTimeList,(function(t,o){var i=e.__get_orig(t),a=e.selectedTime===t.time?e.t("color1"):null,s=e.selectedTime===t.time?e.t("color1rgb"):null;return{$orig:i,m5:a,m6:s}})):null,l=e.canSubmit?e.t("color1"):null,r=e.canSubmit?e.t("color1rgb"):null,n=e.canSubmit?e.t("color1rgb"):null;e.$mp.data=Object.assign({},{$root:{m0:o,l0:i,l1:a,l2:s,m7:l,m8:r,m9:n}})},a=[]}},[["6575","common/runtime","common/vendor"]]]);