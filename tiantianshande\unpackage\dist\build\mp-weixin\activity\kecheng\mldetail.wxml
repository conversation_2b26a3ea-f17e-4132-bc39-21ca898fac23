<view class="wrap"><block wx:if="{{isload}}"><block><block wx:if="{{detail.kctype==1}}"><block><view class="title">{{detail.name}}</view><dp vue-id="6e055264-1" pagecontent="{{pagecontent}}" bind:__l="__l"></dp><view style="margin-bottom:40rpx;"></view></block></block><block wx:if="{{detail.kctype==2}}"><view class="audo-video"><view class="audoimg"><image src="{{detail.pic}}"></image></view><view class="play"><view class="play-left"><image hidden="{{!(playshow)}}" src="/static/img/video_icon.png" data-event-opts="{{[['tap',[['play',['$event']]]]]}}" bindtap="__e"></image><image hidden="{{!(!playshow)}}" src="/static/img/play.png" data-event-opts="{{[['tap',[['pauseaudio',['$event']]]]]}}" bindtap="__e"></image><text>{{nowtime}}</text></view><view class="play-right"><slider class="slider" block-size="16" min="{{0}}" max="{{time}}" value="{{currentTime}}" activeColor="#595959" data-event-opts="{{[['change',[['sliderChange',['$event']]]],['changing',[['sliderChanging',['$event']]]]]}}" bindchange="__e" bindchanging="__e"></slider></view><view class="play-end"><text>{{duration}}</text></view><view data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" class="audio-speed-btn" bindtap="__e"><text class="speed-text">{{currentSpeed+"x"}}</text></view></view></view></block><block wx:if="{{detail.kctype==3}}"><view class="{{['videobox',(isVideoSticky)?'video-sticky':'']}}"><video class="video" id="video" autoplay="{{true}}" src="{{detail.video_url}}" initial-time="{{detail.startTime}}" data-event-opts="{{[['pause',[['pause',['$event']]]],['timeupdate',[['timeupdate',['$event']]]],['ended',[['ended',['$event']]]]]}}" bindpause="__e" bindtimeupdate="__e" bindended="__e"></video><view data-event-opts="{{[['tap',[['e1',['$event']]]]]}}" class="speed-control-btn" bindtap="__e"><text class="speed-text">{{currentSpeed+"x"}}</text></view></view></block><block wx:if="{{detail.kctype==3&&isVideoSticky}}"><view class="video-placeholder" style="{{'height:'+(videoHeight+'px')+';'}}"></view></block><view style="height:30rpx;width:100%;background-color:#f5f5f5;"></view><block wx:if="{{kechengset&&kechengset.enable_notes==1&&(detail.ispay==1||kechengset.notes_need_buy==0)}}"><view class="note-section"><view data-event-opts="{{[['tap',[['showNoteDialog',['$event']]]]]}}" class="note-button" style="{{'background:'+($root.m0)+';'}}" bindtap="__e"><text>记笔记</text></view></view></block><view class="content_box"><view class="title flex"><view class="t1">课程目录</view><block wx:if="{{detail.isdt==1&&detail.count>=detail.kccount&&iskaoshi!=1}}"><view class="t2" data-url="{{'tiku?id='+detail.kcid}}" data-opentype="redirect" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e">去答题</view></block><block wx:if="{{iskaoshi==1}}"><view class="t2" data-url="{{'recordlog?kcid='+detail.kcid}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e">答题记录</view></block></view><block wx:if="{{currentChapter}}"><view class="current-learning-card"><view class="card-header"><text class="card-title">正在学习</text><text class="progress-text">{{"学习进度: "+(currentChapter.jindu||0)+(currentChapter.kctype==1&&(currentChapter.jindu||0)!='100'?'':'%')}}</text></view><view data-event-opts="{{[['tap',[['scrollToCurrentChapter',['$event']]]]]}}" class="card-content" bindtap="__e"><view class="chapter-info"><block wx:if="{{currentChapter.kctype==1}}"><image class="chapter-icon" src="/static/img/tw_icon.png"></image></block><block wx:if="{{currentChapter.kctype==2}}"><image class="chapter-icon" src="/static/img/mp3_icon.png"></image></block><block wx:if="{{currentChapter.kctype==3}}"><image class="chapter-icon" src="/static/img/video_icon.png"></image></block><view class="chapter-text"><view class="chapter-name">{{currentChapter.name}}</view><view class="chapter-type"><block wx:if="{{currentChapter.kctype==1}}"><text class="type-tag">图文课程</text></block><block wx:if="{{currentChapter.kctype==2}}"><text class="type-tag">音频课程</text></block><block wx:if="{{currentChapter.kctype==3}}"><text class="type-tag">视频课程</text></block><block wx:if="{{currentChapter.video_duration>0}}"><text class="duration-tag">{{"时长: "+currentChapter.duration}}</text></block></view></view></view><view class="continue-btn">继续学习</view></view></view></block><block wx:for="{{datalist}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="mulubox flex" id="{{'chapter-'+item.id}}"><view class="left_box"><block wx:if="{{item.kctype==1}}"><image src="/static/img/tw_icon.png"></image></block><block wx:if="{{item.kctype==2}}"><image src="/static/img/mp3_icon.png"></image></block><block wx:if="{{item.kctype==3}}"><image src="/static/img/video_icon.png"></image></block></view><view class="right_box flex"><view class="{{['title_box '+(item.id==detail.id?'current-chapter':'')+(item.jindu=='100'?' completed-chapter':'')]}}" data-key="{{item.key}}" data-mianfei="{{item.ismianfei}}" data-url="{{'mldetail?id='+item.id+'&kcid='+item.kcid}}" data-opentype="{{item.kctype==1?'redirect':'redirect'}}" data-event-opts="{{[['tap',[['todetail',['$event']]]]]}}" bindtap="__e"><view class="t1"><block wx:if="{{item.id==detail.id}}"><text class="current-indicator">正在学习</text></block>{{''+item.name+''}}</view><view><block wx:if="{{item.kctype==1}}"><text class="t2">图文课程</text></block><block wx:if="{{item.kctype==2}}"><text class="t2">音频课程</text></block><block wx:if="{{item.kctype==3}}"><text class="t2">视频课程</text></block><block wx:if="{{item.video_duration>0}}"><text class="t2">{{'时长: '+item.duration}}</text></block></view></view><block wx:if="{{item.jindu}}"><view class="jindu">{{item.jindu+(item.kctype==1&&item.jindu!='100'?'':'%')}}</view></block><block wx:if="{{item.ismianfei&&!item.jindu}}"><view class="skbtn">试看</view></block></view></view></block></view></block></block><block wx:if="{{nomore}}"><nomore vue-id="6e055264-2" text="没有更多课程了" bind:__l="__l"></nomore></block><block wx:if="{{nodata}}"><nodata vue-id="6e055264-3" text="没有查找到相关课程" bind:__l="__l"></nodata></block><block wx:if="{{loading}}"><loading vue-id="6e055264-4" bind:__l="__l"></loading></block><dp-tabbar vue-id="6e055264-5" opt="{{opt}}" data-event-opts="{{[['^getmenuindex',[['getmenuindex']]]]}}" bind:getmenuindex="__e" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="6e055264-6" data-ref="popmsg" bind:__l="__l"></popmsg><block wx:if="{{noteDialogVisible}}"><view class="popup__container"><view data-event-opts="{{[['tap',[['hideNoteDialog',['$event']]]]]}}" class="popup__overlay" catchtap="__e"></view><view class="popup__modal note-modal"><view class="popup__title"><text class="popup__title-text">我的课程笔记</text><image class="popup__close" style="width:36rpx;height:36rpx;" src="{{pre_url+'/static/img/close.png'}}" data-event-opts="{{[['tap',[['hideNoteDialog',['$event']]]]]}}" catchtap="__e"></image></view><view class="popup__content"><view class="note-tabs"><view class="{{['tab-item '+(noteTabIndex==1?'active':'')]}}" data-index="{{1}}" data-event-opts="{{[['tap',[['switchNoteTab',['$event']]]]]}}" bindtap="__e"><text>添加笔记</text></view><view class="{{['tab-item '+(noteTabIndex==2?'active':'')]}}" data-index="{{2}}" data-event-opts="{{[['tap',[['switchNoteTab',['$event']]]]]}}" bindtap="__e"><text>我的笔记</text></view></view><block wx:if="{{noteTabIndex==1}}"><view class="add-note-content"><view class="form-group"><view class="form-label">笔记内容</view><textarea class="note-textarea" placeholder="请输入您的学习笔记..." maxlength="1000" show-count="{{true}}" data-event-opts="{{[['input',[['__set_model',['$0','content','$event',[]],['noteForm']]]]]}}" value="{{noteForm.content}}" bindinput="__e"></textarea></view><view class="form-group"><view class="form-label">时间点 (秒)</view><input class="note-input" type="number" placeholder="当前学习时间点" data-event-opts="{{[['input',[['__set_model',['$0','time_point','$event',[]],['noteForm']]]]]}}" value="{{noteForm.time_point}}" bindinput="__e"/></view><block wx:if="{{kechengset&&kechengset.notes_need_progress==1}}"><view class="form-group"><view class="form-label">学习进度</view><view class="progress-input"><slider value="{{noteForm.study_progress}}" min="{{0}}" max="{{100}}" step="{{1}}" show-value="{{true}}" activeColor="#FF5347" data-event-opts="{{[['change',[['onProgressChange',['$event']]]]]}}" bindchange="__e"></slider><text class="progress-text">{{noteForm.study_progress+"%"}}</text></view></view></block><view class="form-actions"><button data-event-opts="{{[['tap',[['resetNoteForm',['$event']]]]]}}" class="btn-cancel" bindtap="__e">重置</button><button data-event-opts="{{[['tap',[['submitNote',['$event']]]]]}}" class="btn-submit" style="{{'background:'+($root.m1)+';'}}" bindtap="__e">{{''+(editingNoteId?'更新笔记':'保存笔记')+''}}</button></view></view></block><block wx:if="{{noteTabIndex==2}}"><view class="my-notes-content"><view class="notes-filter"><view class="filter-item"><text class="filter-label">筛选：</text><picker value="{{filterIndex}}" range="{{filterOptions}}" data-event-opts="{{[['change',[['onFilterChange',['$event']]]]]}}" bindchange="__e"><view class="picker-text">{{filterOptions[filterIndex]}}</view></picker></view></view><block wx:if="{{$root.g0>0}}"><view class="notes-list"><block wx:for="{{$root.l0}}" wx:for-item="note" wx:for-index="index" wx:key="id"><view class="note-item"><view class="note-header"><view class="note-time">{{note.$orig.createtime_format}}</view><view class="note-actions"><text class="action-btn edit-btn" data-note="{{note.g1}}" data-event-opts="{{[['tap',[['editNote',['$event']]]]]}}" bindtap="__e">编辑</text><text class="action-btn delete-btn" data-id="{{note.$orig.id}}" data-event-opts="{{[['tap',[['deleteNote',['$event']]]]]}}" bindtap="__e">删除</text></view></view><view class="note-content">{{note.$orig.content}}</view><view class="note-info"><block wx:if="{{kechengset&&kechengset.notes_need_progress==1}}"><text class="note-progress">{{"学习进度: "+note.$orig.study_progress+"%"}}</text></block><block wx:if="{{note.$orig.note_time}}"><text class="note-time-point">{{"时间点: "+note.$orig.note_time+"秒"}}</text></block><block wx:if="{{note.$orig.chapter_name}}"><text class="note-chapter">{{"章节: "+note.$orig.chapter_name}}</text></block></view></view></block></view></block><block wx:else><view class="no-notes"><image class="empty-icon" src="{{pre_url+'/static/img/empty.png'}}"></image><text class="empty-text">暂无笔记记录</text><text class="empty-tip">开始学习并记录您的想法吧！</text></view></block><block wx:if="{{$root.g2}}"><view data-event-opts="{{[['tap',[['loadMoreNotes',['$event']]]]]}}" class="load-more-notes" bindtap="__e"><text>{{loadingNotes?'加载中...':'加载更多'}}</text></view></block></view></block></view></view></view></block><block wx:if="{{showSpeedMenu}}"><view data-event-opts="{{[['tap',[['e2',['$event']]]]]}}" class="speed-menu-mask" bindtap="__e"><view data-event-opts="{{[['tap',[['',['$event']]]]]}}" class="speed-menu" catchtap="__e"><view class="speed-menu-header"><text class="speed-title">播放倍速</text><view data-event-opts="{{[['tap',[['e3',['$event']]]]]}}" class="close-btn" bindtap="__e">×</view></view><view class="speed-options"><block wx:for="{{$root.l1}}" wx:for-item="speed" wx:for-index="__i0__" wx:key="$orig"><view data-event-opts="{{[['tap',[['selectSpeed',['$0'],[[['speedOptions','',__i0__]]]]]]]}}" class="{{['speed-option',(speed.$orig===currentSpeed)?'active':'']}}" bindtap="__e"><text class="speed-label">{{speed.$orig+"x"}}</text><view class="speed-desc">{{speed.m2}}</view></view></block></view></view></view></block></view>