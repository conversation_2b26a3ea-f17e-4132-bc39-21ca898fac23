<view class="container"><block wx:if="{{isload}}"><block><dd-tab vue-id="5d4cccf6-1" itemdata="{{['全部','待付款','派单中','待确认','已完成','已取消']}}" itemst="{{['all','0','1','2','3','4']}}" st="{{st}}" isfixed="{{true}}" data-event-opts="{{[['^changetab',[['changetab']]]]}}" bind:changetab="__e" bind:__l="__l"></dd-tab><view style="width:100%;height:100rpx;"></view><view class="topsearch flex-y-center"><view class="f1 flex-y-center"><image class="img" src="{{pre_url+'/static/img/search_ico.png'}}"></image><input placeholder="输入关键字搜索" placeholder-style="font-size:24rpx;color:#C2C2C2" data-event-opts="{{[['confirm',[['searchConfirm',['$event']]]]]}}" value="{{keyword}}" bindconfirm="__e"/></view></view><view class="order-content"><block wx:for="{{datalist}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><view class="order-box" data-url="{{'yuyueorderdetail?id='+item.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e"><view class="head"><block wx:if="{{item.bid!=0}}"><view class="f1" data-url="{{'/pagesExt/business/index?id='+item.bid}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e"><image src="{{pre_url+'/static/img/ico-shop.png'}}"></image>{{''+item.binfo.name}}</view></block><block wx:else><view>{{"订单号："+item.ordernum}}</view></block><view class="flex1"></view><block wx:if="{{item.status==0}}"><text class="st0">待付款</text></block><block wx:if="{{item.status==1&&item.refund_status==0&&item.worker_orderid}}"><block><block wx:if="{{item.worker.status==0}}"><text class="st1">待接单</text></block><block wx:if="{{item.worker.status==1}}"><text class="st1">已接单</text></block><block wx:if="{{item.worker.status==2}}"><text class="st2">服务中</text></block></block></block><block wx:else><block wx:if="{{item.status==1&&item.refund_status==0}}"><block><text class="st1">派单中</text></block></block></block><block wx:if="{{item.status==1&&item.refund_status==1}}"><text class="st1">退款审核中</text></block><block wx:if="{{item.status==2}}"><text class="st2">服务中</text></block><block wx:if="{{item.status==3&&item.isconmement==0}}"><text class="st3">待评价</text></block><block wx:if="{{item.status==3}}"><text class="st4">已完成</text></block><block wx:if="{{item.status==4}}"><text class="st4">订单已关闭</text></block></view><view class="content" style="border-bottom:none;"><block wx:if="{{item.paidan_type==3}}"><view><image src="{{item.propic}}"></image></view></block><block wx:else><view data-url="{{'product?id='+item.proid}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e"><image src="{{item.propic}}"></image></view></block><view class="detail"><text class="t1">{{item.proname}}</text><text class="t1">{{"预约日期："+item.yy_time}}</text><block wx:if="{{yuyue_sign}}"><text class="t2">{{"服务地址："+item.area}}</text></block><block wx:if="{{item.balance_price>0}}"><view class="t3"><text class="x1 flex1">{{"实付金额：￥"+item.totalprice}}</text><block wx:if="{{item.balance_price>0}}"><text class="x1 flex1">{{"尾款：￥"+item.balance_price}}</text></block></view></block><block wx:else><view class="t3"><text class="x1 flex1">{{"实付金额：￥"+item.totalprice}}</text><block wx:if="{{item.showpaidanfee}}"><text class="x1 flex1">{{"含跑腿费：￥"+item.paidan_money}}</text></block></view></block></view></view><block wx:if="{{item.send_time>0}}"><view class="bottom"><text>{{"派单时间："+item.senddate}}</text><block wx:if="{{item.refund_status==1}}"><text style="color:red;">{{'退款中￥'+item.refund_money}}</text></block><block wx:if="{{item.refund_status==2}}"><text style="color:red;">{{'已退款￥'+item.refund_money}}</text></block><block wx:if="{{item.refund_status==3}}"><text style="color:red;">退款申请已驳回</text></block></view></block><view class="op"><block wx:if="{{yuyue_sign&&item.status==2&&item.addmoney>0&&item.addmoneyStatus==0}}"><view class="btn2" data-addprice="{{item.addmoney}}" data-addmoneyPayorderid="{{item.addmoneyPayorderid}}" data-orderid="{{item.id}}" data-event-opts="{{[['tap',[['update',['$event']]]]]}}" catchtap="__e">修改差价</view></block><view class="btn2" data-url="{{'yuyueorderdetail?id='+item.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e">详情</view></view><view class="bottom flex-y-center"><image style="width:40rpx;height:40rpx;border-radius:50%;margin-right:10rpx;" src="{{item.member.headimg}}"></image><text style="font-weight:bold;color:#333;margin-right:8rpx;">{{item.member.nickname}}</text>{{"(ID:"+item.mid+')'}}</view></view></block></block></view><block wx:if="{{showmodal}}"><view class="modal"><view class="addmoney"><view class="title">{{(addprice>0?'修改':'创建')+"补余款"}}</view><view class="item"><label class="label">金额：</label><input type="text" name="blance_price" placeholder="请输入补余款金额" placeholder-style="font-size:24rpx" data-event-opts="{{[['input',[['bindMoney',['$event']]]]]}}" value="{{addprice}}" bindinput="__e"/>元</view><view class="btn"><button data-event-opts="{{[['tap',[['cancel',['$event']]]]]}}" class="btn-cancel" bindtap="__e">取消</button><button data-event-opts="{{[['tap',[['addconfirm',['$event']]]]]}}" class="confirm" style="{{('background:linear-gradient(90deg,'+$root.m0+' 0%,rgba('+$root.m1+',0.8) 100%)')}}" catchtap="__e">确定</button></view></view></view></block><block wx:if="{{nomore}}"><nomore vue-id="5d4cccf6-2" bind:__l="__l"></nomore></block><block wx:if="{{nodata}}"><nodata vue-id="5d4cccf6-3" bind:__l="__l"></nodata></block></block></block><block wx:if="{{loading}}"><loading vue-id="5d4cccf6-4" bind:__l="__l"></loading></block><dp-tabbar vue-id="5d4cccf6-5" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="5d4cccf6-6" data-ref="popmsg" bind:__l="__l"></popmsg></view>