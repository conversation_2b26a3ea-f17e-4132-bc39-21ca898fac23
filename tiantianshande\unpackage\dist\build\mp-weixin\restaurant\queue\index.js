(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["restaurant/queue/index"],{"218c":function(e,t,n){"use strict";var a=n("8971"),o=n.n(a);o.a},"415ee":function(e,t,n){"use strict";n.r(t);var a=n("920d"),o=n("6740");for(var u in o)["default"].indexOf(u)<0&&function(e){n.d(t,e,(function(){return o[e]}))}(u);n("218c");var s=n("828b"),i=Object(s["a"])(o["default"],a["b"],a["c"],!1,null,null,null,!1,a["a"],void 0);t["default"]=i.exports},6740:function(e,t,n){"use strict";n.r(t);var a=n("801a"),o=n.n(a);for(var u in a)["default"].indexOf(u)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(u);t["default"]=o.a},"801a":function(e,t,n){"use strict";(function(e){Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n,a=getApp(),o=[],u={data:function(){return{opt:{},loading:!1,isload:!1,menuindex:-1,business:{},clist:[],myqueue:"",myjustqueue:"",notice:"",bid:"",socketOpen:!1,token:""}},onLoad:function(e){this.opt=a.getopts(e),this.bid=this.opt.bid||0,this.getdata()},onUnload:function(){clearInterval(n),this.socketOpen&&e.closeSocket()},onPullDownRefresh:function(){this.getdata()},methods:{getdata:function(){var t=this;t.loading=!0,a.get("ApiRestaurantQueue/index",{bid:t.opt.bid},(function(u){if(t.loading=!1,t.business=u.business,t.clist=u.clist,t.notice=u.notice,t.myqueue=u.myqueue,t.myjustqueue=u.myjustqueue,t.token=u.token,t.loaded(),!t.socketOpen){var s=a.globalData.pre_url,i=s.replace("https://","wss://")+"/wss";e.closeSocket(),e.connectSocket({url:i}),e.onSocketOpen((function(e){console.log(e),t.socketOpen=!0;for(var n=0;n<o.length;n++)t.sendSocketMessage(o[n]);o=[]})),t.sendSocketMessage({type:"restaurant_queue",token:t.token,data:{aid:a.globalData.aid,bid:t.bid}}),n=setInterval((function(){t.sendSocketMessage({type:"connect",token:t.token})}),25e3),e.onSocketMessage((function(e){console.log(e);try{var n=JSON.parse(e.data);t.receiveMessage(n)}catch(a){}}))}}))},sendSocketMessage:function(t){this.socketOpen?(console.log(t),e.sendSocketMessage({data:JSON.stringify(t)})):(console.log("111"),o.push(t))},receiveMessage:function(e){console.log(e),"restaurant_queue_add"!=e.type&&"restaurant_queue_callno"!=e.type&&"restaurant_queue_cancel"!=e.type||this.getdata()},cancel:function(e){var t=this,n=e.currentTarget.dataset.id;e.currentTarget.dataset.queu_no;a.confirm("确定要取消排队吗?",(function(){a.showLoading("取消中"),a.get("ApiRestaurantQueue/cancel",{id:n},(function(e){a.showLoading(!1),a.alert(e.msg,(function(){t.getdata()})),1==e.status&&t.sendSocketMessage({type:"restaurant_queue_cancel",token:t.token,data:{aid:a.globalData.aid,bid:t.bid,queue_id:n}})}))}))}}};t.default=u}).call(this,n("df3c")["default"])},8971:function(e,t,n){},"920d":function(e,t,n){"use strict";n.d(t,"b",(function(){return o})),n.d(t,"c",(function(){return u})),n.d(t,"a",(function(){return a}));var a={loading:function(){return n.e("components/loading/loading").then(n.bind(null,"ceaa"))},dpTabbar:function(){return n.e("components/dp-tabbar/dp-tabbar").then(n.bind(null,"b875"))}},o=function(){var e=this,t=e.$createElement,n=(e._self._c,e.isload&&e.myqueue?e.dateFormat(e.myqueue.create_time,"H:i:s"):null),a=e.isload&&e.myjustqueue?e.dateFormat(e.myjustqueue.create_time,"H:i:s"):null,o=e.isload&&e.myjustqueue?e.dateFormat(e.myjustqueue.call_time,"H:i:s"):null;e.$mp.data=Object.assign({},{$root:{m0:n,m1:a,m2:o}})},u=[]},e9af:function(e,t,n){"use strict";(function(e,t){var a=n("47a9");n("06e9");a(n("3240"));var o=a(n("415ee"));e.__webpack_require_UNI_MP_PLUGIN__=n,t(o.default)}).call(this,n("3223")["default"],n("df3c")["createPage"])}},[["e9af","common/runtime","common/vendor"]]]);