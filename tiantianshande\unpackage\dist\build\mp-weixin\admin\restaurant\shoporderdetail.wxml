<view class="container"><block wx:if="{{isload}}"><block><view class="ordertop" style="{{('background:url('+pre_url+'/static/img/ordertop.png);background-size:100%')}}"><block wx:if="{{detail.status==0}}"><view class="f1"><view class="t1">等待买家付款</view></view></block><block wx:if="{{detail.status==1&&detail.paytypeid!=4}}"><view class="f1"><view class="t1">已支付</view></view></block><block wx:if="{{detail.status==1&&detail.paytypeid==4}}"><view class="f1"><view class="t1">线下支付，请收款</view></view></block><block wx:if="{{detail.status==2}}"><view class="f1"><view class="t1">订单已发货</view></view></block><block wx:if="{{detail.status==3}}"><view class="f1"><view class="t1">订单已完成</view></view></block><block wx:if="{{detail.status==4}}"><view class="f1"><view class="t1">订单已取消</view></view></block></view><view class="address"><view class="info"><text class="t1">{{detail.linkman+" "+detail.tel}}</text><text class="t2">{{"桌号："+detail.tableName}}</text></view></view><view class="product"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="idx" wx:key="idx"><view class="content"><view data-url="{{'/restaurant/shop/product?id='+item.$orig.proid}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image src="{{item.$orig.pic}}"></image></view><view class="detail"><text class="t1">{{item.$orig.name}}</text><text class="t2">{{item.$orig.ggname+(item.$orig.jltitle?item.$orig.jltitle:'')}}</text><view class="t3"><block wx:if="{{item.$orig.jlprice}}"><text class="x1 flex1">{{"￥"+item.g0}}</text></block><block wx:else><text class="x1 flex1">{{"￥"+item.$orig.sell_price}}</text></block><text class="x2">{{"×"+item.$orig.num}}</text></view></view></view></block></view><block wx:if="{{(detail.status==3||detail.status==2)&&(detail.freight_type==3||detail.freight_type==4)}}"><view class="orderinfo"><view class="item flex-col"><text class="t1" style="color:#111;">发货信息</text><text class="t2" style="text-align:left;margin-top:10rpx;padding:0 10rpx;" user-select="true" selectable="true">{{detail.freight_content}}</text></view></view></block><view class="orderinfo"><view class="item"><text class="t1">下单人</text><text class="flex1"></text><image style="width:80rpx;height:80rpx;margin-right:8rpx;" src="{{detail.headimg}}"></image><text style="height:80rpx;line-height:80rpx;">{{detail.nickname}}</text></view><view class="item"><text class="t1">{{$root.m0+"ID"}}</text><text class="t2">{{detail.mid}}</text></view></view><block wx:if="{{detail.remark}}"><view class="orderinfo"><view class="item"><text class="t1">备注</text><text class="t2">{{detail.remark}}</text></view></view></block><view class="orderinfo"><view class="item"><text class="t1">订单编号</text><text class="t2" user-select="true" selectable="true">{{detail.ordernum}}</text></view><view class="item"><text class="t1">下单时间</text><text class="t2">{{detail.createtime}}</text></view><block wx:if="{{detail.status>0&&detail.paytypeid!='4'&&detail.paytime}}"><view class="item"><text class="t1">支付时间</text><text class="t2">{{detail.paytime}}</text></view></block><block wx:if="{{detail.status>0&&detail.paytime}}"><view class="item"><text class="t1">支付方式</text><text class="t2">{{detail.paytype}}</text></view></block><block wx:if="{{detail.status>1&&detail.send_time}}"><view class="item"><text class="t1">发货时间</text><text class="t2">{{detail.send_time}}</text></view></block><block wx:if="{{detail.status==3&&detail.collect_time}}"><view class="item"><text class="t1">收货时间</text><text class="t2">{{detail.collect_time}}</text></view></block></view><view class="orderinfo"><view class="item"><text class="t1">菜品金额</text><text class="t2 red">{{"¥"+detail.product_price}}</text></view><block wx:if="{{detail.leveldk_money>0}}"><view class="item"><text class="t1">{{$root.m1+"折扣"}}</text><text class="t2 red">{{"-¥"+detail.leveldk_money}}</text></view></block><block wx:if="{{detail.jianmoney>0}}"><view class="item"><text class="t1">满减活动</text><text class="t2 red">{{"-¥"+detail.manjian_money}}</text></view></block><block wx:if="{{detail.tea_fee>0}}"><view class="item"><text class="t1">{{shopset.tea_fee_text}}</text><text class="t2 red">{{"+¥"+detail.tea_fee}}</text></view></block><block wx:if="{{detail.couponmoney>0}}"><view class="item"><text class="t1">{{$root.m2+"抵扣"}}</text><text class="t2 red">{{"-¥"+detail.coupon_money}}</text></view></block><block wx:if="{{detail.scoredk>0}}"><view class="item"><text class="t1">{{$root.m3+"抵扣"}}</text><text class="t2 red">{{"-¥"+detail.scoredk_money}}</text></view></block><block wx:if="{{detail.discount_money>0}}"><view class="item"><text class="t1">优惠</text><text class="t2 red">{{"-¥"+detail.discount_money}}</text></view></block><view class="item"><text class="t1">实付款</text><text class="t2 red">{{"¥"+detail.totalprice}}</text></view><view class="item"><text class="t1">订单状态</text><block wx:if="{{detail.status==0}}"><text class="t2">未付款</text></block><block wx:if="{{detail.status==1&&detail.paytypeid!=4}}"><text class="t2">已付款</text></block><block wx:if="{{detail.status==1&&detail.paytypeid==4}}"><text class="t2">线下支付</text></block><block wx:if="{{detail.status==2}}"><text class="t2">已发货</text></block><block wx:if="{{detail.status==3}}"><text class="t2">已完成</text></block><block wx:if="{{detail.status==4}}"><text class="t2">已关闭</text></block></view><block wx:if="{{detail.refund_status>0}}"><view class="item"><text class="t1">退款状态</text><block wx:if="{{detail.refund_status==1}}"><text class="t2 red">{{"审核中,¥"+detail.refund_money}}</text></block><block wx:if="{{detail.refund_status==2}}"><text class="t2 red">{{"已退款,¥"+detail.refund_money}}</text></block><block wx:if="{{detail.refund_status==3}}"><text class="t2 red">{{"已驳回,¥"+detail.refund_money}}</text></block></view></block><block wx:if="{{detail.refund_status>0}}"><view class="item"><text class="t1">退款原因</text><text class="t2 red">{{detail.refund_reason}}</text></view></block><block wx:if="{{detail.refund_checkremark}}"><view class="item"><text class="t1">审核备注</text><text class="t2 red">{{detail.refund_checkremark}}</text></view></block><view class="item"><text class="t1">备注</text><text class="t2 red">{{detail.message?detail.message:'无'}}</text></view><block wx:if="{{detail.field1}}"><view class="item"><text class="t1">{{detail.field1data[0]}}</text><text class="t2 red">{{detail.field1data[1]}}</text></view></block><block wx:if="{{detail.field2}}"><view class="item"><text class="t1">{{detail.field2data[0]}}</text><text class="t2 red">{{detail.field2data[1]}}</text></view></block><block wx:if="{{detail.field3}}"><view class="item"><text class="t1">{{detail.field3data[0]}}</text><text class="t2 red">{{detail.field3data[1]}}</text></view></block><block wx:if="{{detail.field4}}"><view class="item"><text class="t1">{{detail.field4data[0]}}</text><text class="t2 red">{{detail.field4data[1]}}</text></view></block><block wx:if="{{detail.field5}}"><view class="item"><text class="t1">{{detail.field5data[0]}}</text><text class="t2 red">{{detail.field5data[1]}}</text></view></block><block wx:if="{{(detail.status==1||detail.status==2)&&detail.freight_type==1}}"><view class="item flex-col"><text class="t1">核销码</text><view class="flex-x-center"><image style="width:400rpx;height:400rpx;" src="{{detail.hexiao_qr}}" data-url="{{detail.hexiao_qr}}" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image></view></view></block></view><view style="width:100%;height:120rpx;"></view><view class="bottom"><block wx:if="{{detail.refund_status==1}}"><view class="btn2" data-id="{{detail.id}}" data-event-opts="{{[['tap',[['refundnopass',['$event']]]]]}}" bindtap="__e">退款驳回</view></block><block wx:if="{{detail.refund_status==1}}"><view class="btn2" data-id="{{detail.id}}" data-event-opts="{{[['tap',[['refundpass',['$event']]]]]}}" bindtap="__e">退款通过</view></block><block wx:if="{{detail.status==0}}"><view class="btn2" data-id="{{detail.id}}" data-event-opts="{{[['tap',[['closeOrder',['$event']]]]]}}" bindtap="__e">关闭订单</view></block><block wx:if="{{detail.status==0&&detail.bid==0}}"><view class="btn2" data-id="{{detail.id}}" data-event-opts="{{[['tap',[['ispay',['$event']]]]]}}" bindtap="__e">改为已支付</view></block><block wx:if="{{detail.status==4}}"><view class="btn2" data-id="{{detail.id}}" data-event-opts="{{[['tap',[['delOrder',['$event']]]]]}}" bindtap="__e">删除</view></block><view class="btn2" data-id="{{detail.id}}" data-event-opts="{{[['tap',[['setremark',['$event']]]]]}}" bindtap="__e">设置备注</view></view><uni-popup class="vue-ref" vue-id="6833f541-1" id="dialogSetremark" type="dialog" data-ref="dialogSetremark" bind:__l="__l" vue-slots="{{['default']}}"><uni-popup-dialog vue-id="{{('6833f541-2')+','+('6833f541-1')}}" mode="input" title="设置备注" value="{{detail.remark}}" placeholder="请输入备注" data-event-opts="{{[['^confirm',[['setremarkconfirm']]]]}}" bind:confirm="__e" bind:__l="__l"></uni-popup-dialog></uni-popup></block></block><block wx:if="{{loading}}"><loading vue-id="6833f541-3" bind:__l="__l"></loading></block><dp-tabbar vue-id="6833f541-4" opt="{{opt}}" bind:__l="__l"></dp-tabbar></view>