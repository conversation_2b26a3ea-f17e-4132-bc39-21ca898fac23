require('../common/vendor.js');(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pagesB/coze/history"],{"068a":function(n,e,t){"use strict";var o=t("470c"),i=t.n(o);i.a},"2d0e8":function(n,e,t){"use strict";t.r(e);var o=t("da6f"),i=t.n(o);for(var a in o)["default"].indexOf(a)<0&&function(n){t.d(e,n,(function(){return o[n]}))}(a);e["default"]=i.a},"470c":function(n,e,t){},b6a9:function(n,e,t){"use strict";t.d(e,"b",(function(){return i})),t.d(e,"c",(function(){return a})),t.d(e,"a",(function(){return o}));var o={nomore:function(){return t.e("components/nomore/nomore").then(t.bind(null,"3892"))},nodata:function(){return t.e("components/nodata/nodata").then(t.bind(null,"101c"))},uniPopup:function(){return Promise.all([t.e("common/vendor"),t.e("components/uni-popup/uni-popup")]).then(t.bind(null,"ca44a"))},uniPopupDialog:function(){return t.e("components/uni-popup-dialog/uni-popup-dialog").then(t.bind(null,"267c"))},loading:function(){return t.e("components/loading/loading").then(t.bind(null,"ceaa"))}},i=function(){var n=this,e=n.$createElement,t=(n._self._c,n.isload?n.__map(n.conversationList,(function(e,t){var o=n.__get_orig(e),i=JSON.stringify(e);return{$orig:o,g0:i}})):null),o=n.isload?n.t("color1"):null;n.$mp.data=Object.assign({},{$root:{l0:t,m0:o}})},a=[]},c59b:function(n,e,t){"use strict";(function(n,e){var o=t("47a9");t("06e9");o(t("3240"));var i=o(t("d2070"));n.__webpack_require_UNI_MP_PLUGIN__=t,e(i.default)}).call(this,t("3223")["default"],t("df3c")["createPage"])},d2070:function(n,e,t){"use strict";t.r(e);var o=t("b6a9"),i=t("2d0e8");for(var a in i)["default"].indexOf(a)<0&&function(n){t.d(e,n,(function(){return i[n]}))}(a);t("068a");var r=t("828b"),s=Object(r["a"])(i["default"],o["b"],o["c"],!1,null,null,null,!1,o["a"],void 0);e["default"]=s.exports},da6f:function(n,e,t){"use strict";(function(n){Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var t=getApp(),o={data:function(){return{isload:!1,loading:!1,userInfo:{},conversationList:[],currentMessages:[],currentConversation:{},deleteConversationId:"",pagenum:1,nomore:!1,nodata:!1}},onLoad:function(n){this.userInfo=t.getUserInfo(),this.getConversationList()},onReachBottom:function(){this.nodata||this.nomore||(this.pagenum=this.pagenum+1,this.getConversationList(!0))},onPullDownRefresh:function(){this.getConversationList()},methods:{getConversationList:function(e){e||(this.pagenum=1,this.conversationList=[]);var o=this;o.loading=!0,o.nodata=!1,o.nomore=!1,t.post("ApiCoze/getconversations",{pagenum:o.pagenum,pagesize:20},(function(e){if(o.loading=!1,n.stopPullDownRefresh(),1===e.code){var t=e.data||[];t.forEach((function(n){n.update_time_text=o.formatTime(n.update_time),n.create_time_text=o.formatTime(n.create_time)})),1===o.pagenum?(o.conversationList=t,0===t.length&&(o.nodata=!0)):0===t.length?o.nomore=!0:o.conversationList=o.conversationList.concat(t)}else n.showToast({title:e.msg,icon:"none",duration:2e3}),1===o.pagenum&&(o.nodata=!0);o.loaded()}))},openConversation:function(n){var e=JSON.parse(n.currentTarget.dataset.conversation);this.currentConversation=e,this.getConversationMessages(e.conversation_id)},getConversationMessages:function(e){var o=this;o.loading=!0,t.post("ApiCoze/getmessages",{conversation_id:e,pagenum:1,pagesize:100},(function(e){o.loading=!1,1===e.code?(o.currentMessages=e.data||[],o.$refs.conversationDetailPopup.open()):n.showToast({title:e.msg,icon:"none",duration:2e3})}))},closeConversationDetail:function(){this.$refs.conversationDetailPopup.close()},continueConversation:function(){this.closeConversationDetail(),n.navigateTo({url:"/pagesB/coze/chat?conversation_id="+this.currentConversation.conversation_id})},deleteConversation:function(n){this.deleteConversationId=n.currentTarget.dataset.conversationId,this.$refs.confirmDeletePopup.open()},confirmDelete:function(){var e=this;e.loading=!0,t.post("ApiCoze/deleteconversation",{conversation_id:e.deleteConversationId},(function(t){e.loading=!1,1===t.code?(n.showToast({title:"删除成功",icon:"success",duration:2e3}),e.getConversationList()):n.showToast({title:t.msg,icon:"none",duration:2e3})})),this.closeConfirmDelete()},closeConfirmDelete:function(){this.$refs.confirmDeletePopup.close()},clearAllHistory:function(){this.$refs.confirmClearPopup.open()},confirmClearAll:function(){var e=this;e.loading=!0;var o=e.conversationList.map((function(n){return new Promise((function(e){t.post("ApiCoze/deleteconversation",{conversation_id:n.conversation_id},(function(n){e(n)}))}))}));Promise.all(o).then((function(){e.loading=!1,n.showToast({title:"清空成功",icon:"success",duration:2e3}),e.getConversationList()})),this.closeClearConfirm()},closeClearConfirm:function(){this.$refs.confirmClearPopup.close()},formatTime:function(n){var e=new Date(1e3*n),t=new Date,o=t-e;return o<6e4?"刚刚":o<36e5?Math.floor(o/6e4)+"分钟前":o<864e5?Math.floor(o/36e5)+"小时前":o<6048e5?Math.floor(o/864e5)+"天前":e.getFullYear()+"-"+(e.getMonth()+1).toString().padStart(2,"0")+"-"+e.getDate().toString().padStart(2,"0")}}};e.default=o}).call(this,t("df3c")["default"])}},[["c59b","common/runtime","common/vendor"]]]);