require('../common/vendor.js');(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["admin/restaurant/queue"],{"5212a":function(e,t,n){"use strict";(function(e,t){var a=n("47a9");n("06e9");a(n("3240"));var o=a(n("9b16"));e.__webpack_require_UNI_MP_PLUGIN__=n,t(o.default)}).call(this,n("3223")["default"],n("df3c")["createPage"])},5337:function(e,t,n){"use strict";(function(e){Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n,a=getApp(),o=[],u={data:function(){return{opt:{},loading:!1,isload:!1,menuindex:-1,business:{},clist:[],myqueue:"",myjustqueue:"",lastQueue:{},bid:"",socketOpen:!1,token:""}},onLoad:function(e){this.opt=a.getopts(e),this.bid=this.opt.bid||0,this.getdata()},onShow:function(){},onPullDownRefresh:function(){this.getdata()},onUnload:function(){clearInterval(n),this.socketOpen&&e.closeSocket()},methods:{getdata:function(){var t=this;a.get("ApiAdminRestaurantQueue/index",{},(function(u){if(0==u.status&&a.alert(u.msg,(function(){a.goback()})),t.business=u.business,t.clist=u.clist,t.lastQueue=u.lastQueue,t.myqueue=u.myqueue,t.myjustqueue=u.myjustqueue,t.token=u.token,t.loaded(),!t.socketOpen){var s=a.globalData.pre_url,i=s.replace("https://","wss://")+"/wss";e.closeSocket(),e.connectSocket({url:i}),e.onSocketOpen((function(e){console.log(e),t.socketOpen=!0;for(var n=0;n<o.length;n++)t.sendSocketMessage(o[n]);o=[]})),t.sendSocketMessage({type:"restaurant_queue",token:t.token,data:{aid:a.globalData.aid,bid:t.bid}}),n=setInterval((function(){t.sendSocketMessage({type:"connect",token:t.token})}),25e3),e.onSocketMessage((function(e){try{var n=JSON.parse(e.data);t.receiveMessage(n)}catch(a){}}))}}))},sendSocketMessage:function(t){this.socketOpen?(console.log(t),e.sendSocketMessage({data:JSON.stringify(t)})):(console.log("111"),o.push(t))},receiveMessage:function(e){console.log(e),"restaurant_queue_add"!=e.type&&"restaurant_queue_cancel"!=e.type&&"restaurant_queue_callno"!=e.type||this.getdata()},jiaohao:function(e){var t=this,n=e.currentTarget.dataset.cid;e.currentTarget.dataset.queue_no;a.showLoading(),a.get("ApiAdminRestaurantQueue/jiaohao",{cid:n},(function(e){a.showLoading(!1),0==e.status&&a.alert(e.msg,(function(){t.getdata()})),1==e.status&&(a.success(e.msg,(function(){})),t.sendSocketMessage({type:"restaurant_queue_callno",token:t.token,data:{call_id:e.queue.id,aid:a.globalData.aid,bid:t.bid}}))}))},repeat:function(e){var t=this,n=e.currentTarget.dataset.id;e.currentTarget.dataset.queue_no;a.showLoading(),a.get("ApiAdminRestaurantQueue/jiaohao",{id:n},(function(e){a.showLoading(!1),0==e.status&&a.alert(e.msg,(function(){t.getdata()})),1==e.status&&(a.success(e.msg,(function(){})),t.sendSocketMessage({type:"restaurant_queue_callno",token:t.token,data:{call_id:e.queue.id,aid:a.globalData.aid,bid:t.bid}}))}))},guohao:function(e){var t=this,n=e.currentTarget.dataset.id;a.confirm("确定要过号吗?",(function(){a.showLoading(),a.get("ApiAdminRestaurantQueue/guohao",{id:n},(function(e){a.showLoading(!1),a.alert(e.msg,(function(){})),t.sendSocketMessage({type:"restaurant_queue_cancel",token:t.token,data:{aid:a.globalData.aid,bid:t.bid,queue_id:n}})}))}))}}};t.default=u}).call(this,n("df3c")["default"])},"7b26":function(e,t,n){},8878:function(e,t,n){"use strict";var a=n("7b26"),o=n.n(a);o.a},"9b16":function(e,t,n){"use strict";n.r(t);var a=n("ca49"),o=n("9fef");for(var u in o)["default"].indexOf(u)<0&&function(e){n.d(t,e,(function(){return o[e]}))}(u);n("8878");var s=n("828b"),i=Object(s["a"])(o["default"],a["b"],a["c"],!1,null,null,null,!1,a["a"],void 0);t["default"]=i.exports},"9fef":function(e,t,n){"use strict";n.r(t);var a=n("5337"),o=n.n(a);for(var u in a)["default"].indexOf(u)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(u);t["default"]=o.a},ca49:function(e,t,n){"use strict";n.d(t,"b",(function(){return o})),n.d(t,"c",(function(){return u})),n.d(t,"a",(function(){return a}));var a={loading:function(){return n.e("components/loading/loading").then(n.bind(null,"ceaa"))}},o=function(){var e=this.$createElement;this._self._c},u=[]}},[["5212a","common/runtime","common/vendor"]]]);