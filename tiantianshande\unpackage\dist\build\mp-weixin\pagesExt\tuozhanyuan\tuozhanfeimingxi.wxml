<view class="container"><block wx:if="{{isload}}"><block><dd-tab vue-id="c24b10e4-1" itemdata="{{[$root.m0+'明细','额度明细']}}" itemst="{{['2','3']}}" st="{{st}}" isfixed="{{false}}" data-event-opts="{{[['^changetab',[['changetab']]]]}}" bind:changetab="__e" bind:__l="__l"></dd-tab><view class="content"><block wx:if="{{st==2}}"><block><block wx:for="{{datalist}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="item"><view class="f1"><text class="t1">{{item.remark}}</text><text class="t2">{{item.createtime}}</text><text class="t3">{{"变更后余额: "+item.after+"元"}}</text></view><view class="f2"><block wx:if="{{item.money>0}}"><text class="t1">{{"+"+item.money}}</text></block><block wx:else><text class="t2">{{item.money}}</text></block></view></view></block></block></block><block wx:if="{{st==3}}"><block><block wx:for="{{datalist}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="item"><view class="f1"><text class="t1">{{item.remark}}</text><text class="t2">{{item.createtime}}</text><text class="t3">{{"变更后余额: "+item.after+"元"}}</text></view><view class="f2"><block wx:if="{{item.money>0}}"><text class="t1">{{"+"+item.money}}</text></block><block wx:else><text class="t2">{{item.money}}</text></block></view></view></block></block></block></view></block></block><block wx:if="{{nodata}}"><nodata vue-id="c24b10e4-2" bind:__l="__l"></nodata></block><block wx:if="{{nomore}}"><nomore vue-id="c24b10e4-3" bind:__l="__l"></nomore></block><block wx:if="{{loading}}"><loading vue-id="c24b10e4-4" bind:__l="__l"></loading></block><dp-tabbar vue-id="c24b10e4-5" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="c24b10e4-6" data-ref="popmsg" bind:__l="__l"></popmsg></view>