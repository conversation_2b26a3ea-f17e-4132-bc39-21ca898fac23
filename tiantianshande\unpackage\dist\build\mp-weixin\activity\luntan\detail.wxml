<view class="container"><block wx:if="{{isload}}"><block><view class="datalist"><view class="item"><view class="top"><image class="f1" src="{{detail.headimg}}"></image><view class="f2"><view class="{{['covermy',menuindex>-1?'tabbarbot':'notabbarbot']}}" data-url="fatie" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image src="{{pre_url+'/static/img/lt_fatie.png'}}"></image></view><view class="t1">{{detail.nickname}}</view><view class="t2">{{detail.showtime}}</view></view></view><view class="con previewImgContent"><view class="f1"><text style="white-space:pre-wrap;">{{detail.content}}</text></view><block wx:if="{{detail.pics}}"><view class="f2"><block wx:for="{{detail.pics}}" wx:for-item="pic" wx:for-index="idx" wx:key="idx"><image style="height:auto;" src="{{pic}}" mode="widthFix" data-url="{{pic}}" data-urls="{{detail.pics}}" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image></block></view></block><block wx:if="{{detail.video}}"><video class="video" id="video" src="{{detail.video}}"></video></block></view></view><view class="flex"><block wx:if="{{detail.mid==mid}}"><view style="color:#507DAF;font-size:28rpx;height:60rpx;line-height:60rpx;margin-right:20rpx;" data-id="{{detail.id}}" data-event-opts="{{[['tap',[['deltie',['$event']]]]]}}" bindtap="__e">删除</view></block><view style="color:#aaa;font-size:28rpx;height:60rpx;line-height:60rpx;margin-right:20rpx;">{{"阅读 "+detail.readcount}}</view><block wx:if="{{need_call&&detail.mobile}}"><view class="f1" style="margin-left:60rpx;height:30px;line-height:30px;overflow:hidden;" data-phone="{{detail.mobile}}" data-event-opts="{{[['tap',[['callphone',['$event']]]]]}}" catchtap="__e"><image style="width:30rpx;height:30rpx;float:left;margin-top:12rpx;" src="/static/img/mobile.png"></image><text style="margin-left:10rpx;">拨打电话</text></view></block></view></view><view class="plbox"><view class="plbox_title"><text class="t1">评论</text><text>{{plcount}}</text></view><view class="plbox_content" id="datalist"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="idx" wx:key="idx"><block><view class="item1 flex"><view class="f1 flex0"><image src="{{item.$orig.headimg}}"></image></view><view class="f2 flex-col"><text class="t1">{{item.$orig.nickname}}</text><text class="t11">{{item.$orig.createtime}}</text><view class="t2 plcontent"><parse vue-id="{{'f4365e18-1-'+idx}}" content="{{item.$orig.content}}" bind:__l="__l"></parse></view><block wx:if="{{item.g0>0}}"><block><view class="relist"><block wx:for="{{item.$orig.replylist}}" wx:for-item="hfitem" wx:for-index="index" wx:key="index"><block><view class="item2"><view class="f1">{{hfitem.nickname}}<text class="t1">{{hfitem.createtime}}</text><block wx:if="{{hfitem.mid==mid}}"><text class="phuifu" style="font-size:20rpx;margin-left:20rpx;font-weight:normal;" data-id="{{hfitem.id}}" data-event-opts="{{[['tap',[['delplreply',['$event']]]]]}}" bindtap="__e">删除</text></block></view><view class="f2 plcontent"><parse vue-id="{{'f4365e18-2-'+idx+'-'+index}}" content="{{hfitem.content}}" bind:__l="__l"></parse></view></view></block></block></view></block></block><view class="t3 flex"><view class="flex1"><text class="phuifu" style="cursor:pointer;" data-url="{{'pinglun?type=1&id='+detail.id+'&hfid='+item.$orig.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">回复</text><block wx:if="{{item.$orig.mid==mid}}"><text class="phuifu" style="cursor:pointer;margin-left:20rpx;" data-id="{{item.$orig.id}}" data-event-opts="{{[['tap',[['delpinglun',['$event']]]]]}}" bindtap="__e">删除</text></block></view><view class="flex-y-center pzan" data-id="{{item.$orig.id}}" data-index="{{idx}}" data-event-opts="{{[['tap',[['pzan',['$event']]]]]}}" bindtap="__e"><image src="{{'/static/img/lt_like'+(item.$orig.iszan==1?'2':'')+'.png'}}"></image>{{item.$orig.zan}}</view></view></view></view></block></block></view></view><view style="height:100rpx;"></view><view class="pinglun"><view class="pinput" data-url="{{'pinglun?type=0&id='+detail.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">发表评论</view><view class="zan flex-y-center" data-id="{{detail.id}}" data-event-opts="{{[['tap',[['zan',['$event']]]]]}}" bindtap="__e"><image src="{{'/static/img/lt_like'+(iszan?'2':'')+'.png'}}"></image><text style="padding-left:2px;">{{detail.zan}}</text></view></view></block></block><block wx:if="{{loading}}"><loading vue-id="f4365e18-3" bind:__l="__l"></loading></block><dp-tabbar vue-id="f4365e18-4" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="f4365e18-5" data-ref="popmsg" bind:__l="__l"></popmsg></view>