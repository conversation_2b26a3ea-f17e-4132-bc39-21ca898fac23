<view><block wx:if="{{isload}}"><block><block wx:if="{{info.id&&info.status==2}}"><view style="color:red;padding:10rpx 30rpx;margin-top:20rpx;">{{"审核不通过："+info.reason+"，请修改后再提交"}}</view></block><block wx:if="{{info.id&&info.status==0}}"><view style="color:red;padding:10rpx 30rpx;margin-top:20rpx;">您已提交申请，请等待审核</view></block><form data-event-opts="{{[['submit',[['subform',['$event']]]]]}}" bindsubmit="__e"><view class="apply_box"><view class="apply_item"><view>联系人姓名<text style="color:red;">*</text></view><view class="flex-y-center"><input type="text" name="linkman" placeholder="请填写姓名" value="{{info.contact_name}}"/></view></view><view class="apply_item"><view>联系人电话<text style="color:red;">*</text></view><view class="flex-y-center"><input type="text" name="linktel" placeholder="请填写手机号码" value="{{info.phone}}"/></view></view><block wx:if="{{$root.g0>0}}"><view class="apply_item"><view>选择商机来源<text style="color:red;">*</text></view><view><picker value="{{sourceIndex}}" range="{{sourceArr}}" data-event-opts="{{[['change',[['sourceChange',['$event']]]]]}}" bindchange="__e"><view class="picker">{{sourceArr[sourceIndex]}}</view></picker></view></view></block></view><view class="apply_box"><view class="apply_item"><view>商家名称<text style="color:red;">*</text></view><view class="flex-y-center"><input type="text" name="name" placeholder="请输入商家名称" value="{{info.company_name}}"/></view></view><view class="apply_item"><view>商家备注<text style="color:red;"></text></view><view class="flex-y-center"><input type="text" name="desc" placeholder="请输入商家描述" value="{{info.remark}}"/></view></view><view class="apply_item"><view>主营类目<text style="color:red;">*</text></view><view><picker value="{{cindex}}" range="{{cateArr}}" data-event-opts="{{[['change',[['cateChange',['$event']]]]]}}" bindchange="__e"><view class="picker">{{cateArr[cindex]}}</view></picker></view></view><view class="apply_item"><view>商家单价<text style="color:red;"></text></view><view class="flex-y-center"><input type="text" name="price" placeholder="类目不确定可以填" value="{{info.price}}"/></view></view><view class="apply_item"><view>意向度<text style="color:red;">*</text></view><picker value="{{yixiangduIndex}}" range="{{yixiangduArr}}" data-event-opts="{{[['change',[['yixiangduChange',['$event']]]]]}}" bindchange="__e"><view class="picker">{{yixiangduArr[yixiangduIndex]}}</view></picker></view><view class="apply_item"><view>店铺坐标<text style="color:red;"></text></view><view class="flex-y-center"><input type="text" disabled="{{true}}" placeholder="请选择坐标" name="zuobiao" data-event-opts="{{[['tap',[['locationSelect',['$event']]]]]}}" value="{{latitude?latitude+','+longitude:''}}" bindtap="__e"/></view></view><view class="apply_item"><view>店铺地址<text style="color:red;">*</text></view><view class="flex-y-center"><input type="text" name="address" placeholder="请输入商家详细地址" data-event-opts="{{[['input',[['__set_model',['','address','$event',[]]]]]]}}" value="{{address}}" bindinput="__e"/></view></view><input type="text" hidden="true" name="latitude" value="{{latitude}}"/><input type="text" hidden="true" name="longitude" value="{{longitude}}"/><view class="apply_item" style="line-height:50rpx;"><textarea name="sample_feedback" placeholder="请输入商家样品反馈" value="{{info.sample_feedback}}"></textarea></view></view><view class="apply_box"><view class="apply_item" style="border-bottom:0;"><text>商家照片(3-4张)<text style="color:red;"></text></text></view><view class="flex" style="flex-wrap:wrap;padding-bottom:20rpx;"><block wx:for="{{pics}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="layui-imgbox"><view class="layui-imgbox-close" data-index="{{index}}" data-field="pics" data-event-opts="{{[['tap',[['removeimg',['$event']]]]]}}" bindtap="__e"><image src="/static/img/ico-del.png"></image></view><view class="layui-imgbox-img"><image src="{{item}}" data-url="{{item}}" mode="widthFix" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image></view></view></block><block wx:if="{{$root.g1<5}}"><view class="uploadbtn" style="{{('background:url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 60rpx;background-size:80rpx 80rpx;background-color:#F3F3F3;')}}" data-field="pics" data-event-opts="{{[['tap',[['uploadimg',['$event']]]]]}}" bindtap="__e"></view></block></view><input type="text" hidden="true" name="pics" maxlength="-1" value="{{$root.g2}}"/></view><view style="padding:30rpx 0;"><button class="set-btn" style="{{('background:linear-gradient(90deg,'+$root.m0+' 0%,rgba('+$root.m1+',0.8) 100%)')}}" form-type="submit">提交申请</button></view></form></block></block><block wx:if="{{loading}}"><loading vue-id="7f2567d0-1" bind:__l="__l"></loading></block><dp-tabbar vue-id="7f2567d0-2" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="7f2567d0-3" data-ref="popmsg" bind:__l="__l"></popmsg></view>