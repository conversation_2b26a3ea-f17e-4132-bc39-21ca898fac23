<view class="container"><block wx:if="{{isload}}"><block><view class="content"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="item"><view class="f1"><text class="t1">{{item.$orig.remark}}</text><text class="t2">{{item.m0}}</text><text class="t3">{{"变更后余额: "+item.$orig.after}}</text></view><view class="f2"><block wx:if="{{item.$orig.value>0}}"><text class="t1">{{"+"+item.$orig.value}}</text></block><block wx:else><text class="t2">{{item.$orig.value}}</text></block></view></view></block></view><block wx:if="{{nomore}}"><nomore vue-id="695a0b25-1" bind:__l="__l"></nomore></block><block wx:if="{{nodata}}"><nodata vue-id="695a0b25-2" bind:__l="__l"></nodata></block></block></block><block wx:if="{{loading}}"><loading vue-id="695a0b25-3" bind:__l="__l"></loading></block><dp-tabbar vue-id="695a0b25-4" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="695a0b25-5" data-ref="popmsg" bind:__l="__l"></popmsg></view>