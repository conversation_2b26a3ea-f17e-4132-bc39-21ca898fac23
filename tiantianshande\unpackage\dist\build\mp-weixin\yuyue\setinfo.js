(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["yuyue/setinfo"],{"24c0":function(n,t,e){"use strict";e.d(t,"b",(function(){return i})),e.d(t,"c",(function(){return o})),e.d(t,"a",(function(){return a}));var a={loading:function(){return e.e("components/loading/loading").then(e.bind(null,"ceaa"))},dpTabbar:function(){return e.e("components/dp-tabbar/dp-tabbar").then(e.bind(null,"b875"))},popmsg:function(){return e.e("components/popmsg/popmsg").then(e.bind(null,"2bf2"))}},i=function(){var n=this.$createElement;this._self._c},o=[]},"34f5":function(n,t,e){"use strict";(function(n,t){var a=e("47a9");e("06e9");a(e("3240"));var i=a(e("7021"));n.__webpack_require_UNI_MP_PLUGIN__=e,t(i.default)}).call(this,e("3223")["default"],e("df3c")["createPage"])},5593:function(n,t,e){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var a=getApp(),i={data:function(){return{opt:{},loading:!1,isload:!1,menuindex:-1,smsdjs:"",banklist:["工商银行","农业银行","中国银行","建设银行","招商银行","邮储银行","交通银行","浦发银行","民生银行","兴业银行","平安银行","中信银行","华夏银行","广发银行","光大银行","北京银行","宁波银行"],bankname:"",userinfo:{},textset:{}}},onLoad:function(n){this.opt=a.getopts(n),this.isload=!0,this.getdata()},onPullDownRefresh:function(){this.getdata()},methods:{getdata:function(){var n=this;n.loading=!0,a.get("ApiYuyueWorker/set",{},(function(t){n.loading=!1,n.userinfo=t.userinfo,n.bankname=t.userinfo.bankname,n.loaded()}))},formSubmit:function(n){var t=n.detail.value,e=this.bankname,i=t.bankcarduser,o=t.bankcardnum,u=t.weixin,r=t.aliaccount,s=t.code||"";""!=e?(a.showLoading("提交中"),a.post("ApiYuyueWorker/set",{bankname:e,bankcarduser:i,bankcardnum:o,weixin:u,aliaccount:r,code:s},(function(n){a.showLoading(!1),1==n.status?(a.success(n.msg),setTimeout((function(){a.goback(!0)}),1e3)):a.error(n.msg)}))):a.alert("请选择开户行")},bindBanknameChange:function(n){this.bankname=this.banklist[n.detail.value]},smscode:function(){var n=this;if(1!=n.hqing){n.hqing=1;var t=n.userinfo.tel;if(""==t)return a.alert("请输入手机号码"),n.hqing=0,!1;if(!/^1[3456789]\d{9}$/.test(t))return a.alert("手机号码有误，请重填"),n.hqing=0,!1;a.post("ApiIndex/sendsms",{tel:t},(function(n){1!=n.status&&a.alert(n.msg)}));var e=120,i=setInterval((function(){e--,e<0?(n.smsdjs="重新获取",n.hqing=0,clearInterval(i)):e>=0&&(n.smsdjs=e+"秒")}),1e3)}}}};t.default=i},7021:function(n,t,e){"use strict";e.r(t);var a=e("24c0"),i=e("7230");for(var o in i)["default"].indexOf(o)<0&&function(n){e.d(t,n,(function(){return i[n]}))}(o);e("9808");var u=e("828b"),r=Object(u["a"])(i["default"],a["b"],a["c"],!1,null,null,null,!1,a["a"],void 0);t["default"]=r.exports},7230:function(n,t,e){"use strict";e.r(t);var a=e("5593"),i=e.n(a);for(var o in a)["default"].indexOf(o)<0&&function(n){e.d(t,n,(function(){return a[n]}))}(o);t["default"]=i.a},9808:function(n,t,e){"use strict";var a=e("a5a8"),i=e.n(a);i.a},a5a8:function(n,t,e){}},[["34f5","common/runtime","common/vendor"]]]);