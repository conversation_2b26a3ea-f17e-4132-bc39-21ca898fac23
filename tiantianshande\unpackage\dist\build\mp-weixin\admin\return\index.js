require('../common/vendor.js');(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["admin/return/index"],{"250e":function(t,n,a){"use strict";a.d(n,"b",(function(){return o})),a.d(n,"c",(function(){return s})),a.d(n,"a",(function(){return e}));var e={ddTab:function(){return a.e("components/dd-tab/dd-tab").then(a.bind(null,"caa1"))},loading:function(){return a.e("components/loading/loading").then(a.bind(null,"ceaa"))},nomore:function(){return a.e("components/nomore/nomore").then(a.bind(null,"3892"))},nodata:function(){return a.e("components/nodata/nodata").then(a.bind(null,"101c"))},popmsg:function(){return a.e("components/popmsg/popmsg").then(a.bind(null,"2bf2"))}},o=function(){var t=this,n=t.$createElement,a=(t._self._c,t.__map(t.datalist,(function(n,a){var e=t.__get_orig(n),o=t.getStatusColor(n.status),s=t.getStatusText(n.status);return{$orig:e,m0:o,m1:s}})));t.$mp.data=Object.assign({},{$root:{l0:a}})},s=[]},"8d84":function(t,n,a){"use strict";(function(t){Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;var a=getApp(),e={data:function(){return{st:"all",datalist:[],pagenum:1,nodata:!1,nomore:!1,loading:!1,count0:0,count1:0,count2:0,count3:0,countall:0,search:{orderNo:""},isRefreshing:!1,hasMore:!0}},onLoad:function(t){this.opt=a.getopts(t),this.getdata()},onPullDownRefresh:function(){this.isRefreshing?t.stopPullDownRefresh():(this.isRefreshing=!0,this.pagenum=1,this.hasMore=!0,this.getdata(!1,!0))},onReachBottom:function(){this.loading||!this.hasMore||this.nodata||(this.pagenum=this.pagenum+1,this.getdata(!0))},methods:{changetab:function(t){this.st=t,this.pagenum=1,this.hasMore=!0,this.getdata()},getdata:function(n,e){if(!this.loading||e){n||(this.pagenum=1,this.datalist=[],this.hasMore=!0);var o=this,s=o.pagenum,r=o.st;o.nodata=!1,o.nomore=!1,o.loading=!0,a.post("ApiAdminPurchase/getReturnOrders",{pagenum:s,status:"all"===r?"":r,order_no:o.search.orderNo||""},(function(n){if(o.loading=!1,e&&(o.isRefreshing=!1,t.stopPullDownRefresh()),0===n.code){var r=n.data.list||[];if(1==s){if(console.log("退货订单统计数据:",n.data),o.count0=parseInt(n.data.count0||0),o.count1=parseInt(n.data.count1||0),o.count2=parseInt(n.data.count2||0),o.count3=parseInt(n.data.count3||0),o.countall=parseInt(n.data.total||0),0===o.countall&&r.length>0){console.log("API未返回退货统计数据，手动计算"),o.countall=r.length;var u=0,i=0,c=0,d=0;r.forEach((function(t){0==t.status?u++:1==t.status?i++:2==t.status?c++:3==t.status&&d++})),o.count0=u,o.count1=i,o.count2=c,o.count3=d}o.datalist=r,0==r.length&&(o.nodata=!0,o.hasMore=!1)}else if(0==r.length)o.nomore=!0,o.hasMore=!1;else{var l=o.datalist,f=l.concat(r);o.datalist=f,o.countall>0&&o.datalist.length>=o.countall&&(o.hasMore=!1,o.nomore=!0)}}else a.error(n.msg),o.nodata=!0,o.hasMore=!1}))}},searchConfirm:function(t){this.search.orderNo=t.detail.value,this.pagenum=1,this.hasMore=!0,this.getdata(!1)},getStatusText:function(t){return{0:"待审核",1:"已通过",2:"已驳回",3:"已完成"}[t]||"未知状态"},getStatusColor:function(t){return{0:"#FF9800",1:"#4CAF50",2:"#F44336",3:"#2196F3"}[t]||"#999999"},viewDetail:function(t){var n=t.currentTarget.dataset.id;a.goto("detail?id="+n)},createReturnOrder:function(){a.goto("selectorder")}}};n.default=e}).call(this,a("df3c")["default"])},"9a85":function(t,n,a){"use strict";a.r(n);var e=a("250e"),o=a("9dce");for(var s in o)["default"].indexOf(s)<0&&function(t){a.d(n,t,(function(){return o[t]}))}(s);a("de1f");var r=a("828b"),u=Object(r["a"])(o["default"],e["b"],e["c"],!1,null,null,null,!1,e["a"],void 0);n["default"]=u.exports},"9dce":function(t,n,a){"use strict";a.r(n);var e=a("8d84"),o=a.n(e);for(var s in e)["default"].indexOf(s)<0&&function(t){a.d(n,t,(function(){return e[t]}))}(s);n["default"]=o.a},c4b4:function(t,n,a){},d943:function(t,n,a){"use strict";(function(t,n){var e=a("47a9");a("06e9");e(a("3240"));var o=e(a("9a85"));t.__webpack_require_UNI_MP_PLUGIN__=a,n(o.default)}).call(this,a("3223")["default"],a("df3c")["createPage"])},de1f:function(t,n,a){"use strict";var e=a("c4b4"),o=a.n(e);o.a}},[["d943","common/runtime","common/vendor"]]]);