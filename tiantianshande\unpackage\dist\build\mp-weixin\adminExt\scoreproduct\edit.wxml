<view><block wx:if="{{isload}}"><block><form data-event-opts="{{[['submit',[['subform',['$event']]]]]}}" bindsubmit="__e"><view class="form-box"><view class="form-item"><view class="f1">商品名称<text style="color:red;">*</text></view><view class="f2"><input type="text" name="name" placeholder="请填写商品名称" placeholder-style="color:#888" value="{{info.name}}"/></view></view><view class="form-item"><view class="f1">商品分类<text style="color:red;">*</text></view><view data-event-opts="{{[['tap',[['changeClistDialog',['$event']]]]]}}" class="f2" bindtap="__e"><block wx:if="{{$root.g0>0}}"><text>{{cnames}}</text></block><block wx:else><text style="color:#888;">请选择</text></block><image style="width:30rpx;height:30rpx;" src="{{pre_url+'/static/img/arrowright.png'}}"></image></view></view></view><view class="form-box"><view class="form-item flex-col" style="border-bottom:0;"><view class="f1">商品主图<text style="color:red;">*</text></view><view class="f2"><block wx:for="{{pic}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="layui-imgbox"><view class="layui-imgbox-close" data-index="{{index}}" data-field="pic" data-event-opts="{{[['tap',[['removeimg',['$event']]]]]}}" bindtap="__e"><image style="display:block;" src="{{pre_url+'/static/img/ico-del.png'}}"></image></view><view class="layui-imgbox-img"><image src="{{item}}" data-url="{{item}}" mode="widthFix" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image></view></view></block><block wx:if="{{$root.g1==0}}"><view class="uploadbtn" style="{{('background:url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 60rpx;background-size:80rpx 80rpx;background-color:#F3F3F3;')}}" data-field="pic" data-pernum="1" data-event-opts="{{[['tap',[['uploadimg',['$event']]]]]}}" bindtap="__e"></view></block></view><input type="text" hidden="true" name="pic" maxlength="-1" value="{{$root.g2}}"/></view><view class="form-item flex-col"><view class="f1">商品图片</view><view class="f2" style="flex-wrap:wrap;"><block wx:for="{{pics}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="layui-imgbox"><view class="layui-imgbox-close" data-index="{{index}}" data-field="pics" data-event-opts="{{[['tap',[['removeimg',['$event']]]]]}}" bindtap="__e"><image style="display:block;" src="{{pre_url+'/static/img/ico-del.png'}}"></image></view><view class="layui-imgbox-img"><image src="{{item}}" data-url="{{item}}" mode="widthFix" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image></view></view></block><block wx:if="{{$root.g3<5}}"><view class="uploadbtn" style="{{('background:url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 60rpx;background-size:80rpx 80rpx;background-color:#F3F3F3;')}}" data-field="pics" data-pernum="9" data-event-opts="{{[['tap',[['uploadimg',['$event']]]]]}}" bindtap="__e"></view></block></view><input type="text" hidden="true" name="pics" maxlength="-1" value="{{$root.g4}}"/></view></view><view hidden="{{!(scoreshop_guige)}}" class="form-box"><view class="form-item"><text>多规格</text><view><radio-group class="radio-group" name="guigeset" data-event-opts="{{[['change',[['bindguigesetChange',['$event']]]]]}}" bindchange="__e"><label><radio value="0" checked="{{!info||info.guigeset==0?true:false}}"></radio>关闭</label><label><radio value="1" checked="{{info.guigeset==1?true:false}}"></radio>开启</label></radio-group></view></view><view hidden="{{!(info.guigeset==1)}}" class="form-item flex-col"><view class="f1">设置规格</view><view class="flex-col"><view class="ggtitle"><view class="t1">规格分组</view><view class="t2">规格名称</view></view><block wx:for="{{guigedata}}" wx:for-item="gg" wx:for-index="index" wx:key="index"><view class="ggcontent"><view class="t1" data-index="{{index}}" data-title="{{gg.title}}" data-event-opts="{{[['tap',[['delgggroupname',['$event']]]]]}}" bindtap="__e">{{gg.title}}<image class="edit" src="{{pre_url+'/static/img/edit2.png'}}"></image></view><view class="t2"><block wx:for="{{gg.items}}" wx:for-item="ggitem" wx:for-index="index2" wx:key="index2"><view class="ggname" data-index="{{index}}" data-index2="{{index2}}" data-title="{{ggitem.title}}" data-k="{{ggitem.k}}" data-event-opts="{{[['tap',[['delggname',['$event']]]]]}}" bindtap="__e">{{ggitem.title}}<image class="close" src="{{pre_url+'/static/img/ico-del.png'}}"></image></view></block><view class="ggnameadd" data-index="{{index}}" data-event-opts="{{[['tap',[['addggname',['$event']]]]]}}" bindtap="__e">+</view></view></view></block><view class="ggcontent"><view data-event-opts="{{[['tap',[['addgggroupname',['$event']]]]]}}" class="ggadd" bindtap="__e">添加分组</view></view></view></view></view><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="form-box"><view hidden="{{!(info.guigeset==1)}}" class="form-item" style="height:80rpx;line-height:80rpx;"><view class="f1">规格</view><view class="f2" style="font-weight:bold;">{{item.$orig.name}}</view></view><view class="form-item" style="height:80rpx;line-height:80rpx;"><view class="f1">市场价（元）</view><view class="f2"><input type="text" data-index="{{index}}" data-field="market_price" name="{{'market_price['+index+']'}}" placeholder="请填写市场价" placeholder-style="color:#888" data-event-opts="{{[['input',[['gglistInput',['$event']]]]]}}" value="{{item.$orig.market_price}}" bindinput="__e"/></view></view><view class="form-item" style="height:80rpx;line-height:80rpx;"><view class="f1">成本价（元）</view><view class="f2"><input type="text" data-index="{{index}}" data-field="cost_price" name="{{'cost_price['+index+']'}}" placeholder="请填写成本价" placeholder-style="color:#888" data-event-opts="{{[['input',[['gglistInput',['$event']]]]]}}" value="{{item.$orig.cost_price}}" bindinput="__e"/></view></view><view class="form-item" style="height:80rpx;line-height:80rpx;"><view class="f1">所需金额（元）</view><view class="f2"><input type="text" data-index="{{index}}" data-field="money_price" name="{{'money_price['+index+']'}}" placeholder="请填写所需金额" placeholder-style="color:#888" data-event-opts="{{[['input',[['gglistInput',['$event']]]]]}}" value="{{item.$orig.money_price}}" bindinput="__e"/></view></view><view class="form-item" style="height:80rpx;line-height:80rpx;"><view class="f1">{{"所需"+item.m0+"（个）"}}</view><view class="f2"><input type="text" data-index="{{index}}" data-field="score_price" name="{{'score_price['+index+']'}}" placeholder="{{'请填写所需'+item.m1}}" placeholder-style="color:#888" data-event-opts="{{[['input',[['gglistInput',['$event']]]]]}}" value="{{item.$orig.score_price}}" bindinput="__e"/></view></view><view class="form-item" style="height:80rpx;line-height:80rpx;"><view class="f1">重量（克）</view><view class="f2"><input type="text" data-index="{{index}}" data-field="weight" name="{{'weight['+index+']'}}" placeholder="请填写重量" placeholder-style="color:#888" data-event-opts="{{[['input',[['gglistInput',['$event']]]]]}}" value="{{item.$orig.weight}}" bindinput="__e"/></view></view><view class="form-item" style="height:80rpx;line-height:80rpx;"><view class="f1">库存</view><view class="f2"><input type="text" data-index="{{index}}" data-field="stock" name="{{'stock['+index+']'}}" placeholder="请填写库存" placeholder-style="color:#888" data-event-opts="{{[['input',[['gglistInput',['$event']]]]]}}" value="{{item.$orig.stock}}" bindinput="__e"/></view></view><view hidden="{{!(info.guigeset==1)}}" class="form-item"><view class="f1">规格图片</view><view class="f2" style="flex-wrap:wrap;margin-top:20rpx;margin-bottom:20rpx;"><block wx:if="{{item.$orig.pic!=''}}"><view class="layui-imgbox"><view class="layui-imgbox-close" data-index="{{index}}" data-event-opts="{{[['tap',[['removeimg2',['$event']]]]]}}" bindtap="__e"><image style="display:block;" src="{{pre_url+'/static/img/ico-del.png'}}"></image></view><view class="layui-imgbox-img"><image src="{{item.$orig.pic}}" data-url="{{item.$orig.pic}}" mode="widthFix" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image></view></view></block><block wx:else><view class="uploadbtn" style="{{('background:url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 60rpx;background-size:80rpx 80rpx;background-color:#F3F3F3;')}}" data-index="{{index}}" data-event-opts="{{[['tap',[['uploadimg2',['$event']]]]]}}" bindtap="__e"></view></block></view></view></view></block><view class="form-box"><view class="form-item"><view class="f1">配送模板</view><view class="f2"><picker value="{{freightindex}}" range="{{freighttypeArr}}" data-event-opts="{{[['change',[['freighttypeChange',['$event']]]]]}}" bindchange="__e"><view class="picker">{{freighttypeArr[freightindex]}}</view></picker><image style="width:30rpx;height:30rpx;" src="{{pre_url+'/static/img/arrowright.png'}}"></image></view></view><block wx:if="{{freightindex==1}}"><view class="form-item flex-col"><view class="f1">选择模板</view><view class="f2 flex-col"><block wx:for="{{$root.l1}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="freightitem" style="width:100%;" data-index="{{index}}" data-id="{{item.$orig.id}}" data-event-opts="{{[['tap',[['changeFrieght',['$event']]]]]}}" bindtap="__e"><view class="f1">{{item.$orig.name}}</view><view class="radio" style="{{(item.m2?'background:'+item.m3+';border:0':'')}}"><image class="radio-img" src="{{pre_url+'/static/img/checkd.png'}}"></image></view></view></block></view></view></block><block wx:if="{{freightindex==2}}"><view class="form-item flex-col"><view class="f1">发货信息</view><view class="f2 flex-col"><textarea style="height:160rpx;min-height:160rpx;font-size:28rpx;" name="freightdata" placeholder="请输入发货信息" placeholder-style="color:#888;font-size:28rpx" maxlength="-1" value="{{info.freightdata}}"></textarea></view></view></block><block wx:if="{{freightindex==3}}"><view class="form-item flex-col" style="color:#999;">请在电脑端后台上传卡密信息</view></block></view><view class="form-box"><view class="form-item"><view class="f1">销量</view><view class="f2"><input type="text" name="sales" placeholder="请填写销量" placeholder-style="color:#888" value="{{info.sales}}"/></view><input type="text" hidden="true" name="oldsales" value="{{info.id?info.sales:'0'}}"/></view><view class="form-item"><view class="f1">每人限购</view><view class="f2"><input type="text" name="buymax" placeholder="0表示不限购" placeholder-style="color:#888" value="{{info.buymax}}"/></view></view><view class="form-item"><view class="f1">序号</view><view class="f2"><input type="text" name="sort" placeholder="用于排序,越大越靠前" placeholder-style="color:#888" value="{{info.sort}}"/></view></view></view><view class="form-box"><view class="form-item"><view>状态<text style="color:red;">*</text></view><view><radio-group class="radio-group" name="status" data-event-opts="{{[['change',[['bindStatusChange',['$event']]]]]}}" bindchange="__e"><label><radio value="1" checked="{{info.status==1?true:false}}"></radio>上架</label><label><radio value="0" checked="{{!info||info.status==0?true:false}}"></radio>下架</label></radio-group></view></view></view><view class="form-box"><view class="form-item flex-col"><text>商品详情</text><view class="detailop"><view data-event-opts="{{[['tap',[['detailAddtxt',['$event']]]]]}}" class="btn" bindtap="__e">+文本</view><view data-event-opts="{{[['tap',[['detailAddpic',['$event']]]]]}}" class="btn" bindtap="__e">+图片</view><view data-event-opts="{{[['tap',[['detailAddvideo',['$event']]]]]}}" class="btn" bindtap="__e">+视频</view></view><view><block wx:for="{{pagecontent}}" wx:for-item="setData" wx:for-index="index" wx:key="index"><block><view class="detaildp"><view class="op"><view class="flex1"></view><view class="btn" data-index="{{index}}" data-event-opts="{{[['tap',[['detailMoveup',['$event']]]]]}}" bindtap="__e">上移</view><view class="btn" data-index="{{index}}" data-event-opts="{{[['tap',[['detailMovedown',['$event']]]]]}}" bindtap="__e">下移</view><view class="btn" data-index="{{index}}" data-event-opts="{{[['tap',[['detailMovedel',['$event']]]]]}}" bindtap="__e">删除</view></view><view class="detailbox"><block wx:if="{{setData.temp=='notice'}}"><block><dp-notice vue-id="{{'7c9017d9-1-'+index}}" params="{{setData.params}}" data="{{setData.data}}" bind:__l="__l"></dp-notice></block></block><block wx:if="{{setData.temp=='banner'}}"><block><dp-banner vue-id="{{'7c9017d9-2-'+index}}" params="{{setData.params}}" data="{{setData.data}}" bind:__l="__l"></dp-banner></block></block><block wx:if="{{setData.temp=='search'}}"><block><dp-search vue-id="{{'7c9017d9-3-'+index}}" params="{{setData.params}}" data="{{setData.data}}" bind:__l="__l"></dp-search></block></block><block wx:if="{{setData.temp=='text'}}"><block><dp-text vue-id="{{'7c9017d9-4-'+index}}" params="{{setData.params}}" data="{{setData.data}}" bind:__l="__l"></dp-text></block></block><block wx:if="{{setData.temp=='title'}}"><block><dp-title vue-id="{{'7c9017d9-5-'+index}}" params="{{setData.params}}" data="{{setData.data}}" bind:__l="__l"></dp-title></block></block><block wx:if="{{setData.temp=='dhlist'}}"><block><dp-dhlist vue-id="{{'7c9017d9-6-'+index}}" params="{{setData.params}}" data="{{setData.data}}" bind:__l="__l"></dp-dhlist></block></block><block wx:if="{{setData.temp=='line'}}"><block><dp-line vue-id="{{'7c9017d9-7-'+index}}" params="{{setData.params}}" data="{{setData.data}}" bind:__l="__l"></dp-line></block></block><block wx:if="{{setData.temp=='blank'}}"><block><dp-blank vue-id="{{'7c9017d9-8-'+index}}" params="{{setData.params}}" data="{{setData.data}}" bind:__l="__l"></dp-blank></block></block><block wx:if="{{setData.temp=='menu'}}"><block><dp-menu vue-id="{{'7c9017d9-9-'+index}}" params="{{setData.params}}" data="{{setData.data}}" bind:__l="__l"></dp-menu></block></block><block wx:if="{{setData.temp=='map'}}"><block><dp-map vue-id="{{'7c9017d9-10-'+index}}" params="{{setData.params}}" data="{{setData.data}}" bind:__l="__l"></dp-map></block></block><block wx:if="{{setData.temp=='cube'}}"><block><dp-cube vue-id="{{'7c9017d9-11-'+index}}" params="{{setData.params}}" data="{{setData.data}}" bind:__l="__l"></dp-cube></block></block><block wx:if="{{setData.temp=='picture'}}"><block><dp-picture vue-id="{{'7c9017d9-12-'+index}}" params="{{setData.params}}" data="{{setData.data}}" bind:__l="__l"></dp-picture></block></block><block wx:if="{{setData.temp=='pictures'}}"><block><dp-pictures vue-id="{{'7c9017d9-13-'+index}}" params="{{setData.params}}" data="{{setData.data}}" bind:__l="__l"></dp-pictures></block></block><block wx:if="{{setData.temp=='video'}}"><block><dp-video vue-id="{{'7c9017d9-14-'+index}}" params="{{setData.params}}" data="{{setData.data}}" bind:__l="__l"></dp-video></block></block><block wx:if="{{setData.temp=='shop'}}"><block><dp-shop vue-id="{{'7c9017d9-15-'+index}}" params="{{setData.params}}" data="{{setData.data}}" shopinfo="{{setData.shopinfo}}" bind:__l="__l"></dp-shop></block></block><block wx:if="{{setData.temp=='product'}}"><block><dp-product vue-id="{{'7c9017d9-16-'+index}}" params="{{setData.params}}" data="{{setData.data}}" menuindex="{{menuindex}}" bind:__l="__l"></dp-product></block></block><block wx:if="{{setData.temp=='collage'}}"><block><dp-collage vue-id="{{'7c9017d9-17-'+index}}" params="{{setData.params}}" data="{{setData.data}}" bind:__l="__l"></dp-collage></block></block><block wx:if="{{setData.temp=='kanjia'}}"><block><dp-kanjia vue-id="{{'7c9017d9-18-'+index}}" params="{{setData.params}}" data="{{setData.data}}" bind:__l="__l"></dp-kanjia></block></block><block wx:if="{{setData.temp=='seckill'}}"><block><dp-seckill vue-id="{{'7c9017d9-19-'+index}}" params="{{setData.params}}" data="{{setData.data}}" bind:__l="__l"></dp-seckill></block></block><block wx:if="{{setData.temp=='scoreshop'}}"><block><dp-scoreshop vue-id="{{'7c9017d9-20-'+index}}" params="{{setData.params}}" data="{{setData.data}}" bind:__l="__l"></dp-scoreshop></block></block><block wx:if="{{setData.temp=='coupon'}}"><block><dp-coupon vue-id="{{'7c9017d9-21-'+index}}" params="{{setData.params}}" data="{{setData.data}}" bind:__l="__l"></dp-coupon></block></block><block wx:if="{{setData.temp=='article'}}"><block><dp-article vue-id="{{'7c9017d9-22-'+index}}" params="{{setData.params}}" data="{{setData.data}}" bind:__l="__l"></dp-article></block></block><block wx:if="{{setData.temp=='business'}}"><block><dp-business vue-id="{{'7c9017d9-23-'+index}}" params="{{setData.params}}" data="{{setData.data}}" bind:__l="__l"></dp-business></block></block><block wx:if="{{setData.temp=='liveroom'}}"><block><dp-liveroom vue-id="{{'7c9017d9-24-'+index}}" params="{{setData.params}}" data="{{setData.data}}" bind:__l="__l"></dp-liveroom></block></block><block wx:if="{{setData.temp=='button'}}"><block><dp-button vue-id="{{'7c9017d9-25-'+index}}" params="{{setData.params}}" data="{{setData.data}}" bind:__l="__l"></dp-button></block></block><block wx:if="{{setData.temp=='hotspot'}}"><block><dp-hotspot vue-id="{{'7c9017d9-26-'+index}}" params="{{setData.params}}" data="{{setData.data}}" bind:__l="__l"></dp-hotspot></block></block><block wx:if="{{setData.temp=='cover'}}"><block><dp-cover vue-id="{{'7c9017d9-27-'+index}}" params="{{setData.params}}" data="{{setData.data}}" bind:__l="__l"></dp-cover></block></block><block wx:if="{{setData.temp=='richtext'}}"><block><dp-richtext vue-id="{{'7c9017d9-28-'+index}}" params="{{setData.params}}" data="{{setData.data}}" content="{{setData.content}}" bind:__l="__l"></dp-richtext></block></block><block wx:if="{{setData.temp=='form'}}"><block><dp-form vue-id="{{'7c9017d9-29-'+index}}" params="{{setData.params}}" data="{{setData.data}}" content="{{setData.content}}" bind:__l="__l"></dp-form></block></block><block wx:if="{{setData.temp=='userinfo'}}"><block><dp-userinfo vue-id="{{'7c9017d9-30-'+index}}" params="{{setData.params}}" data="{{setData.data}}" content="{{setData.content}}" bind:__l="__l"></dp-userinfo></block></block></view></view></block></block></view></view></view><button class="savebtn" style="{{('background:linear-gradient(90deg,'+$root.m4+' 0%,rgba('+$root.m5+',0.8) 100%)')}}" form-type="submit">提交</button><view style="height:50rpx;"></view></form><block wx:if="{{clistshow}}"><view class="popup__container"><view data-event-opts="{{[['tap',[['changeClistDialog',['$event']]]]]}}" class="popup__overlay" catchtap="__e"></view><view class="popup__modal"><view class="popup__title"><text class="popup__title-text">请选择商品分类</text><image class="popup__close" style="width:36rpx;height:36rpx;" src="{{pre_url+'/static/img/close.png'}}" data-event-opts="{{[['tap',[['changeClistDialog',['$event']]]]]}}" catchtap="__e"></image></view><view class="popup__content"><block wx:for="{{$root.l4}}" wx:for-item="item" wx:for-index="index" wx:key="id"><block><view class="clist-item" data-id="{{item.$orig.id}}" data-event-opts="{{[['tap',[['cidsChange',['$event']]]]]}}" bindtap="__e"><view class="flex1">{{item.$orig.name}}</view><view class="radio" style="{{(item.m6?'background:'+item.m7+';border:0':'')}}"><image class="radio-img" src="{{pre_url+'/static/img/checkd.png'}}"></image></view></view><block wx:for="{{item.l3}}" wx:for-item="item2" wx:for-index="index2" wx:key="id"><block><view class="clist-item" style="padding-left:80rpx;" data-id="{{item2.$orig.id}}" data-event-opts="{{[['tap',[['cidsChange',['$event']]]]]}}" bindtap="__e"><block wx:if="{{item.g5-1==index2}}"><view class="flex1">{{"└ "+item2.$orig.name}}</view></block><block wx:else><view class="flex1">{{"├ "+item2.$orig.name}}</view></block><view class="radio" style="{{(item2.m8?'background:'+item2.m9+';border:0':'')}}"><image class="radio-img" src="{{pre_url+'/static/img/checkd.png'}}"></image></view></view><block wx:for="{{item2.l2}}" wx:for-item="item3" wx:for-index="index3" wx:key="id"><block><view class="clist-item" style="padding-left:160rpx;" data-id="{{item3.$orig.id}}" data-event-opts="{{[['tap',[['cidsChange',['$event']]]]]}}" bindtap="__e"><block wx:if="{{item2.g6-1==index3}}"><view class="flex1">{{"└ "+item3.$orig.name}}</view></block><block wx:else><view class="flex1">{{"├ "+item3.$orig.name}}</view></block><view class="radio" style="{{(item3.m10?'background:'+item3.m11+';border:0':'')}}"><image class="radio-img" src="{{pre_url+'/static/img/checkd.png'}}"></image></view></view></block></block></block></block></block></block></view></view></view></block><uni-popup class="vue-ref" vue-id="7c9017d9-31" id="dialogInput" type="dialog" data-ref="dialogInput" bind:__l="__l" vue-slots="{{['default']}}"><uni-popup-dialog vue-id="{{('7c9017d9-32')+','+('7c9017d9-31')}}" mode="input" title="输入规格名称" value="{{ggname}}" placeholder="请输入规格名称" data-event-opts="{{[['^confirm',[['setggname']]]]}}" bind:confirm="__e" bind:__l="__l"></uni-popup-dialog></uni-popup><uni-popup class="vue-ref" vue-id="7c9017d9-33" id="dialogInput2" type="dialog" data-ref="dialogInput2" bind:__l="__l" vue-slots="{{['default']}}"><uni-popup-dialog vue-id="{{('7c9017d9-34')+','+('7c9017d9-33')}}" mode="input" title="输入规格分组" value="{{ggname}}" placeholder="请输入规格分组" data-event-opts="{{[['^confirm',[['setgggroupname']]]]}}" bind:confirm="__e" bind:__l="__l"></uni-popup-dialog></uni-popup><uni-popup class="vue-ref" vue-id="7c9017d9-35" id="dialogDetailtxt" type="dialog" data-ref="dialogDetailtxt" bind:__l="__l" vue-slots="{{['default']}}"><view class="uni-popup-dialog"><view class="uni-dialog-title"><text class="uni-dialog-title-text">请输入文本内容</text></view><view class="uni-dialog-content"><textarea value="" placeholder="请输入文本内容" data-event-opts="{{[['input',[['catcheDetailtxt',['$event']]]]]}}" bindinput="__e"></textarea></view><view class="uni-dialog-button-group"><view data-event-opts="{{[['tap',[['dialogDetailtxtClose',['$event']]]]]}}" class="uni-dialog-button" bindtap="__e"><text class="uni-dialog-button-text">取消</text></view><view data-event-opts="{{[['tap',[['dialogDetailtxtConfirm',['$event']]]]]}}" class="uni-dialog-button uni-border-left" bindtap="__e"><text class="uni-dialog-button-text uni-button-color">确定</text></view></view><view data-event-opts="{{[['tap',[['dialogDetailtxtClose',['$event']]]]]}}" class="uni-popup-dialog__close" bindtap="__e"><label class="uni-popup-dialog__close-icon _span"></label></view></view></uni-popup></block></block><view style="display:none;">{{test}}</view><block wx:if="{{loading}}"><loading vue-id="7c9017d9-36" bind:__l="__l"></loading></block><popmsg class="vue-ref" vue-id="7c9017d9-37" data-ref="popmsg" bind:__l="__l"></popmsg><wxxieyi vue-id="7c9017d9-38" bind:__l="__l"></wxxieyi></view>