<view class="uni-countdown"><block wx:if="{{showDay}}"><text class="uni-countdown__number" style="{{'border-color:'+(borderColor)+';'+('color:'+(color)+';')+('background-color:'+(backgroundColor)+';')}}">{{d}}</text></block><block wx:if="{{showDay}}"><text class="uni-countdown__splitor" style="{{'color:'+(splitorColor)+';'}}">天</text></block><text class="uni-countdown__number" style="{{'border-color:'+(borderColor)+';'+('color:'+(color)+';')+('background-color:'+(backgroundColor)+';')}}">{{h}}</text><text class="uni-countdown__splitor" style="{{'color:'+(splitorColor)+';'}}">{{showColon?':':'时'}}</text><text class="uni-countdown__number" style="{{'border-color:'+(borderColor)+';'+('color:'+(color)+';')+('background-color:'+(backgroundColor)+';')}}">{{i}}</text><text class="uni-countdown__splitor" style="{{'color:'+(splitorColor)+';'}}">{{showColon?':':'分'}}</text><text class="uni-countdown__number" style="{{'border-color:'+(borderColor)+';'+('color:'+(color)+';')+('background-color:'+(backgroundColor)+';')}}">{{s}}</text><block wx:if="{{!showColon}}"><text class="uni-countdown__splitor" style="{{'color:'+(splitorColor)+';'}}">秒</text></block></view>