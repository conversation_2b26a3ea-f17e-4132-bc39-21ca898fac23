<view class="container"><block wx:if="{{isload}}"><block><dd-tab vue-id="6ac41a04-1" itemdata="{{['未使用','已使用','已过期']}}" itemst="{{['0','1','2']}}" st="{{st}}" isfixed="{{false}}" data-event-opts="{{[['^changetab',[['changetab']]]]}}" bind:changetab="__e" bind:__l="__l"></dd-tab><view class="coupon-list"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="coupon" style="{{(item.$orig.isgive==1||item.$orig.isgive==2?'padding-left:40rpx':'')}}" data-url="{{'coupondetail?rid='+item.$orig.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e"><view class="pt_left"><view class="pt_left-content"><block wx:if="{{item.$orig.type==0}}"><view class="f1" style="{{'color:'+(item.m0)+';'}}"><text class="t0">￥</text><text class="t1">{{item.$orig.money}}</text></view></block><block wx:if="{{item.$orig.type==1}}"><view class="f1" style="{{'color:'+(item.m1)+';'}}">商品券</view></block><block wx:if="{{item.$orig.type==2}}"><view class="f1" style="{{'color:'+(item.m2)+';'}}"><text class="t1">{{item.$orig.score}}</text><text class="t2">积分</text></view></block><block wx:if="{{item.$orig.type==3}}"><view class="f1" style="{{'color:'+(item.m3)+';'}}">礼品卡</view></block><view class="f2" style="{{'color:'+(item.m4)+';'}}"><text>{{"兑换码："+item.$orig.code}}</text></view></view></view><view class="pt_right"><view class="f1"><view class="t1">{{item.$orig.name}}</view><block wx:if="{{item.$orig.isgive==1||item.$orig.isgive==2}}"><text class="t2" style="{{'background:'+('rgba('+item.m5+',0.1)')+';'+('color:'+(item.m6)+';')}}">可赠送</text></block><view class="t3" style="margin-top:10rpx;">{{"有效期至 "+item.$orig.endtime}}</view></view><view class="f1_top"><block><button class="btn" style="{{'background:'+('linear-gradient(270deg,'+item.m7+' 0%,rgba('+item.m8+',0.8) 100%)')+';'}}" data-url="{{'coupondetail?rid='+item.$orig.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e">详情</button></block></view><block wx:if="{{st==1}}"><image class="sygq" src="{{pre_url+'/static/img/ysy.png'}}"></image></block><block wx:if="{{st==2}}"><image class="sygq" src="{{pre_url+'/static/img/ygq.png'}}"></image></block></view></view></block></view><block wx:if="{{nomore}}"><nomore vue-id="6ac41a04-2" bind:__l="__l"></nomore></block><block wx:if="{{nodata}}"><nodata vue-id="6ac41a04-3" bind:__l="__l"></nodata></block><block wx:if="{{checkednum>0}}"><view class="{{['giveopbox',menuindex>-1?'tabbarbot':'notabbarbot3']}}"><block wx:if="{{$root.m9=='app'}}"><view data-event-opts="{{[['tap',[['shareapp',['$event']]]]]}}" class="btn-give" style="{{'background:'+('linear-gradient(90deg,'+$root.m10+' 0%,rgba('+$root.m11+',0.8) 100%)')+';'}}" bindtap="__e">{{"转赠好友("+checkednum+"张)"}}</view></block><block wx:else><block wx:if="{{$root.m12=='mp'}}"><view data-event-opts="{{[['tap',[['sharemp',['$event']]]]]}}" class="btn-give" style="{{'background:'+('linear-gradient(90deg,'+$root.m13+' 0%,rgba('+$root.m14+',0.8) 100%)')+';'}}" bindtap="__e">{{"转赠好友("+checkednum+"张)"}}</view></block><block wx:else><block wx:if="{{$root.m15=='h5'}}"><view data-event-opts="{{[['tap',[['sharemp',['$event']]]]]}}" class="btn-give" style="{{'background:'+('linear-gradient(90deg,'+$root.m16+' 0%,rgba('+$root.m17+',0.8) 100%)')+';'}}" bindtap="__e">{{"转赠好友("+checkednum+"张)"}}</view></block><block wx:else><button class="btn-give" style="{{'background:'+('linear-gradient(90deg,'+$root.m18+' 0%,rgba('+$root.m19+',0.8) 100%)')+';'}}" open-type="share">{{"转赠好友("+checkednum+"张)"}}</button></block></block></block></view></block><view style="display:none;">{{test}}</view></block></block><block wx:if="{{loading}}"><loading vue-id="6ac41a04-4" bind:__l="__l"></loading></block><dp-tabbar vue-id="6ac41a04-5" opt="{{opt}}" data-event-opts="{{[['^getmenuindex',[['getmenuindex']]]]}}" bind:getmenuindex="__e" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="6ac41a04-6" data-ref="popmsg" bind:__l="__l"></popmsg></view>