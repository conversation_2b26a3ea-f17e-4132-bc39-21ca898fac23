<template>
<view class="container">
	<block v-if="isload">
		<!-- 顶部导航 -->
		<view class="header">
			<view class="header-title">扣子工作流</view>
			<view class="header-actions">
				<view class="action-btn" @tap="goto" data-url="/pagesB/coze/workflow-logs">
					<text class="iconfont iconlishi"></text>
					<text>执行记录</text>
				</view>
			</view>
		</view>

		<!-- 工作流列表 -->
		<view class="workflow-list">
			<block v-for="(workflow, index) in workflowList" :key="index">
				<view class="workflow-item" @tap="selectWorkflow" :data-workflow="JSON.stringify(workflow)">
					<view class="workflow-header">
						<view class="workflow-name">{{workflow.name}}</view>
						<view class="workflow-status" :style="{color: t('color1')}">
							<text class="iconfont iconzhengchang"></text>
						</view>
					</view>
					<view class="workflow-desc" v-if="workflow.description">{{workflow.description}}</view>
					<view class="workflow-id">ID: {{workflow.workflow_id}}</view>
				</view>
			</block>
			<nodata v-if="nodata"></nodata>
		</view>

		<!-- 工作流参数配置弹窗 -->
		<uni-popup ref="paramPopup" type="bottom">
			<view class="param-popup">
				<view class="popup-header">
					<view class="popup-title">{{selectedWorkflow.name}}</view>
					<view class="popup-close" @tap="closeParamPopup">
						<text class="iconfont iconguanbi"></text>
					</view>
				</view>
				
				<scroll-view class="param-content" scroll-y="true">
					<view class="param-form">
						<!-- 自定义参数模式 -->
						<block v-if="paramMode === 'custom'">
							<block v-for="(param, index) in paramList" :key="index">
								<view class="form-item">
									<view class="form-label">
										{{param.param_name}}
										<text v-if="param.is_required" class="required">*</text>
									</view>
									
									<!-- 文本输入 -->
									<view v-if="param.param_type === 'text'" class="form-input">
										<input 
											v-model="paramValues[param.param_key]" 
											:placeholder="param.placeholder || '请输入' + param.param_name"
											placeholder-style="color:#999"
										/>
									</view>
									
									<!-- 数字输入 -->
									<view v-else-if="param.param_type === 'number'" class="form-input">
										<input 
											v-model="paramValues[param.param_key]" 
											type="number"
											:placeholder="param.placeholder || '请输入' + param.param_name"
											placeholder-style="color:#999"
										/>
									</view>
									
									<!-- 选择器 -->
									<view v-else-if="param.param_type === 'select'" class="form-select" @tap="showSelectOptions" :data-param="JSON.stringify(param)">
										<text :class="paramValues[param.param_key] ? '' : 'placeholder'">
											{{paramValues[param.param_key] || '请选择' + param.param_name}}
										</text>
										<text class="iconfont iconxiala"></text>
									</view>
									
									<!-- 文本域 -->
									<view v-else-if="param.param_type === 'textarea'" class="form-textarea">
										<textarea 
											v-model="paramValues[param.param_key]" 
											:placeholder="param.placeholder || '请输入' + param.param_name"
											placeholder-style="color:#999"
											auto-height
										></textarea>
									</view>
									
									<!-- 文件上传 -->
									<view v-else-if="param.param_type === 'file'" class="form-file">
										<view class="file-upload-btn" @tap="chooseFile" :data-param-key="param.param_key">
											<text class="iconfont iconshangchuan"></text>
											<text>选择文件</text>
										</view>
										<view v-if="paramValues[param.param_key]" class="file-name">
											{{paramValues[param.param_key].name}}
										</view>
									</view>
									
									<view v-if="param.param_desc" class="form-desc">{{param.param_desc}}</view>
								</view>
							</block>
						</block>
						
						<!-- JSON模式 -->
						<block v-else>
							<view class="form-item">
								<view class="form-label">参数配置</view>
								<view class="form-textarea">
									<textarea 
										v-model="jsonParams" 
										placeholder="请输入JSON格式的参数"
										placeholder-style="color:#999"
										auto-height
									></textarea>
								</view>
								<view class="form-desc">请输入有效的JSON格式参数</view>
							</view>
						</block>
						
						<!-- 异步执行选项 -->
						<view class="form-item">
							<view class="form-checkbox" @tap="toggleAsync">
								<text :class="'iconfont ' + (isAsync ? 'iconduihao' : 'iconweixuanzhong')" :style="{color: isAsync ? t('color1') : '#999'}"></text>
								<text>异步执行</text>
							</view>
							<view class="form-desc">异步执行不会立即返回结果，需要通过执行记录查看结果</view>
						</view>
					</view>
				</scroll-view>
				
				<view class="popup-footer">
					<view class="btn-cancel" @tap="closeParamPopup">取消</view>
					<view class="btn-confirm" :style="{background: t('color1')}" @tap="runWorkflow">运行工作流</view>
				</view>
			</view>
		</uni-popup>

		<!-- 选择器弹窗 -->
		<uni-popup ref="selectPopup" type="bottom">
			<view class="select-popup">
				<view class="popup-header">
					<view class="popup-title">{{currentSelectParam.param_name}}</view>
					<view class="popup-close" @tap="closeSelectPopup">
						<text class="iconfont iconguanbi"></text>
					</view>
				</view>
				<scroll-view class="select-options" scroll-y="true">
					<block v-for="(option, index) in currentSelectParam.param_options" :key="index">
						<view class="select-option" @tap="selectOption" :data-value="option.value" :data-param-key="currentSelectParam.param_key">
							<text>{{option.label}}</text>
							<text v-if="paramValues[currentSelectParam.param_key] === option.value" class="iconfont iconduihao" :style="{color: t('color1')}"></text>
						</view>
					</block>
				</scroll-view>
			</view>
		</uni-popup>

		<!-- 结果显示弹窗 -->
		<uni-popup ref="resultPopup" type="center">
			<view class="result-popup">
				<view class="popup-header">
					<view class="popup-title">执行结果</view>
					<view class="popup-close" @tap="closeResultPopup">
						<text class="iconfont iconguanbi"></text>
					</view>
				</view>
				<scroll-view class="result-content" scroll-y="true">
					<view class="result-text">{{workflowResult}}</view>
				</scroll-view>
				<view class="popup-footer">
					<view class="btn-confirm" :style="{background: t('color1')}" @tap="closeResultPopup">确定</view>
				</view>
			</view>
		</uni-popup>
	</block>
	<loading v-if="loading"></loading>
</view>
</template>

<script>
var app = getApp();

export default {
	data() {
		return {
			isload: false,
			loading: false,
			workflowList: [],
			selectedWorkflow: {},
			paramMode: 'custom', // custom 或 json
			paramList: [],
			paramValues: {},
			jsonParams: '',
			isAsync: false,
			currentSelectParam: {},
			workflowResult: '',
			nodata: false
		};
	},

	onLoad: function(opt) {
		this.getWorkflowList();
	},

	methods: {
		// 获取工作流列表
		getWorkflowList: function() {
			var that = this;
			that.loading = true;
			app.post('ApiCoze/getWorkflowList', {}, function(res) {
				that.loading = false;
				if (res.code === 1) {
					that.workflowList = res.data || [];
					if (that.workflowList.length === 0) {
						that.nodata = true;
					}
				} else {
					uni.showToast({
						title: res.msg,
						icon: 'none',
						duration: 2000
					});
					that.nodata = true;
				}
				that.loaded();
			});
		},

		// 选择工作流
		selectWorkflow: function(e) {
			var workflow = JSON.parse(e.currentTarget.dataset.workflow);
			this.selectedWorkflow = workflow;
			this.getWorkflowParams(workflow.workflow_id);
		},

		// 获取工作流参数
		getWorkflowParams: function(workflowId) {
			var that = this;
			that.loading = true;
			app.post('ApiCoze/getWorkflowParams', {
				workflow_id: workflowId
			}, function(res) {
				that.loading = false;
				if (res.code === 1) {
					that.paramMode = res.data.mode;
					if (res.data.mode === 'custom') {
						that.paramList = res.data.params || [];
						that.paramValues = {};
						// 设置默认值
						that.paramList.forEach(param => {
							if (param.default_value) {
								that.paramValues[param.param_key] = param.default_value;
							}
						});
					} else {
						that.jsonParams = JSON.stringify(res.data.params || {}, null, 2);
					}
					that.$refs.paramPopup.open();
				} else {
					uni.showToast({
						title: res.msg,
						icon: 'none',
						duration: 2000
					});
				}
			});
		},

		// 关闭参数弹窗
		closeParamPopup: function() {
			this.$refs.paramPopup.close();
		},

		// 显示选择器选项
		showSelectOptions: function(e) {
			var param = JSON.parse(e.currentTarget.dataset.param);
			this.currentSelectParam = param;
			this.$refs.selectPopup.open();
		},

		// 关闭选择器弹窗
		closeSelectPopup: function() {
			this.$refs.selectPopup.close();
		},

		// 选择选项
		selectOption: function(e) {
			var value = e.currentTarget.dataset.value;
			var paramKey = e.currentTarget.dataset.paramKey;
			this.paramValues[paramKey] = value;
			this.closeSelectPopup();
		},

		// 切换异步执行
		toggleAsync: function() {
			this.isAsync = !this.isAsync;
		},

		// 选择文件
		chooseFile: function(e) {
			var paramKey = e.currentTarget.dataset.paramKey;
			var that = this;
			
			uni.chooseFile({
				count: 1,
				success: function(res) {
					var file = res.tempFiles[0];
					that.paramValues[paramKey] = {
						name: file.name,
						path: file.path,
						size: file.size
					};
				}
			});
		},

		// 运行工作流
		runWorkflow: function() {
			if (!this.selectedWorkflow.workflow_id) return;

			var params = {};
			if (this.paramMode === 'custom') {
				// 验证必填参数
				for (let param of this.paramList) {
					if (param.is_required && !this.paramValues[param.param_key]) {
						uni.showToast({
							title: param.param_name + '不能为空',
							icon: 'none',
							duration: 2000
						});
						return;
					}
				}
				params = this.paramValues;
			} else {
				try {
					params = JSON.parse(this.jsonParams);
				} catch (e) {
					uni.showToast({
						title: '参数格式错误，请输入有效的JSON',
						icon: 'none',
						duration: 2000
					});
					return;
				}
			}

			var that = this;
			that.loading = true;
			app.post('ApiCoze/runWorkflowWithParams', {
				workflow_id: that.selectedWorkflow.workflow_id,
				params: params,
				is_async: that.isAsync
			}, function(res) {
				that.loading = false;
				if (res.code === 1) {
					if (that.isAsync) {
						uni.showToast({
							title: '工作流已开始异步执行，请在执行记录中查看结果',
							icon: 'success',
							duration: 3000
						});
						that.closeParamPopup();
					} else {
						that.workflowResult = JSON.stringify(res.data, null, 2);
						that.closeParamPopup();
						that.$refs.resultPopup.open();
					}
				} else {
					uni.showToast({
						title: res.msg,
						icon: 'none',
						duration: 2000
					});
				}
			});
		},

		// 关闭结果弹窗
		closeResultPopup: function() {
			this.$refs.resultPopup.close();
		}
	}
};
</script>

<style>
.container {
	background: #f5f5f5;
	min-height: 100vh;
}

.header {
	background: #fff;
	padding: 20rpx 30rpx;
	display: flex;
	justify-content: space-between;
	align-items: center;
	border-bottom: 1rpx solid #eee;
	position: fixed;
	top: var(--window-top);
	left: 0;
	right: 0;
	z-index: 100;
}

.header-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
}

.header-actions {
	display: flex;
	align-items: center;
}

.action-btn {
	display: flex;
	align-items: center;
	padding: 10rpx 20rpx;
	background: #f0f0f0;
	border-radius: 20rpx;
	font-size: 24rpx;
	color: #666;
}

.action-btn .iconfont {
	margin-right: 10rpx;
	font-size: 28rpx;
}

.workflow-list {
	padding: 30rpx;
	margin-top: 120rpx;
}

.workflow-item {
	background: #fff;
	border-radius: 16rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);
}

.workflow-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 20rpx;
}

.workflow-name {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
}

.workflow-status {
	font-size: 24rpx;
}

.workflow-desc {
	font-size: 26rpx;
	color: #666;
	margin-bottom: 15rpx;
	line-height: 1.5;
}

.workflow-id {
	font-size: 22rpx;
	color: #999;
}

.param-popup {
	background: #fff;
	border-radius: 20rpx 20rpx 0 0;
	max-height: 80vh;
	display: flex;
	flex-direction: column;
}

.popup-header {
	padding: 30rpx;
	border-bottom: 1rpx solid #eee;
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.popup-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
}

.popup-close {
	font-size: 32rpx;
	color: #999;
}

.param-content {
	flex: 1;
	padding: 0 30rpx;
}

.param-form {
	padding: 20rpx 0;
}

.form-item {
	margin-bottom: 40rpx;
}

.form-label {
	font-size: 28rpx;
	color: #333;
	margin-bottom: 20rpx;
}

.required {
	color: #ff4757;
	margin-left: 5rpx;
}

.form-input {
	background: #f5f5f5;
	border-radius: 12rpx;
	padding: 0 20rpx;
}

.form-input input {
	height: 80rpx;
	font-size: 28rpx;
	color: #333;
}

.form-select {
	background: #f5f5f5;
	border-radius: 12rpx;
	padding: 0 20rpx;
	height: 80rpx;
	display: flex;
	align-items: center;
	justify-content: space-between;
	font-size: 28rpx;
	color: #333;
}

.form-select .placeholder {
	color: #999;
}

.form-textarea {
	background: #f5f5f5;
	border-radius: 12rpx;
	padding: 20rpx;
}

.form-textarea textarea {
	font-size: 28rpx;
	color: #333;
	min-height: 120rpx;
}

.form-file {
	display: flex;
	align-items: center;
}

.file-upload-btn {
	background: #f5f5f5;
	border-radius: 12rpx;
	padding: 20rpx 30rpx;
	display: flex;
	align-items: center;
	font-size: 28rpx;
	color: #666;
}

.file-upload-btn .iconfont {
	margin-right: 10rpx;
}

.file-name {
	margin-left: 20rpx;
	font-size: 24rpx;
	color: #333;
}

.form-checkbox {
	display: flex;
	align-items: center;
	font-size: 28rpx;
	color: #333;
}

.form-checkbox .iconfont {
	margin-right: 15rpx;
	font-size: 32rpx;
}

.form-desc {
	font-size: 24rpx;
	color: #999;
	margin-top: 10rpx;
	line-height: 1.4;
}

.popup-footer {
	padding: 30rpx;
	border-top: 1rpx solid #eee;
	display: flex;
	justify-content: space-between;
}

.btn-cancel {
	flex: 1;
	height: 80rpx;
	line-height: 80rpx;
	text-align: center;
	background: #f5f5f5;
	border-radius: 12rpx;
	font-size: 28rpx;
	color: #666;
	margin-right: 20rpx;
}

.btn-confirm {
	flex: 1;
	height: 80rpx;
	line-height: 80rpx;
	text-align: center;
	border-radius: 12rpx;
	font-size: 28rpx;
	color: #fff;
}

.select-popup {
	background: #fff;
	border-radius: 20rpx 20rpx 0 0;
	max-height: 60vh;
	display: flex;
	flex-direction: column;
}

.select-options {
	flex: 1;
	padding: 0 30rpx;
}

.select-option {
	padding: 30rpx 0;
	border-bottom: 1rpx solid #f5f5f5;
	display: flex;
	justify-content: space-between;
	align-items: center;
	font-size: 28rpx;
	color: #333;
}

.result-popup {
	background: #fff;
	border-radius: 20rpx;
	width: 80vw;
	max-height: 70vh;
	display: flex;
	flex-direction: column;
}

.result-content {
	flex: 1;
	padding: 30rpx;
	max-height: 50vh;
}

.result-text {
	font-size: 24rpx;
	color: #333;
	line-height: 1.5;
	white-space: pre-wrap;
	word-break: break-all;
}
</style>
