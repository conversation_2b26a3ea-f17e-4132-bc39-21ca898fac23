<view style="width:100%;"><view class="dp-tuangou-itemlist"><block wx:for="{{data}}" wx:for-item="item" wx:for-index="index" wx:key="id"><view class="item" data-url="{{'/activity/tuangou/product?id='+item[idfield]}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="product-pic"><image class="image" src="{{item.pic}}" mode="widthFix"></image><block wx:if="{{saleimg!=''}}"><image class="saleimg" src="{{saleimg}}" mode="widthFix"></image></block></view><view class="product-info"><block wx:if="{{showname==1}}"><view class="p1">{{item.name}}</view></block><block wx:if="{{showprice!='0'}}"><view class="p2"><text class="t1" style="{{'color:'+($root.m0)+';'}}"><text style="font-size:24rpx;padding-right:1px;">低至￥</text>{{item.min_price}}</text><block wx:if="{{showprice=='1'&&item.market_price*1>item.sell_price*1}}"><text class="t2">{{"￥"+item.market_price}}</text></block></view></block><view class="p3"><block wx:if="{{showsales=='1'&&item.sales>0}}"><view class="p3-1"><text style="overflow:hidden;">{{"已售"+item.sales+"件"}}</text></view></block></view><block wx:if="{{showcart==1}}"><view class="p4" style="{{'background:'+('rgba('+$root.m1+',0.1)')+';'+('color:'+($root.m2)+';')}}" data-proid="{{item[idfield]}}" data-event-opts="{{[['tap',[['buydialogChange',['$event']]]]]}}" catchtap="__e"><text class="iconfont icon_gouwuche"></text></view></block><block wx:if="{{showcart==2}}"><view class="p4" style="{{'background:'+('rgba('+$root.m3+',0.1)')+';'+('color:'+($root.m4)+';')}}" data-proid="{{item[idfield]}}" data-event-opts="{{[['tap',[['buydialogChange',['$event']]]]]}}" catchtap="__e"><image class="img" src="{{cartimg}}"></image></view></block></view></view></block></view><block wx:if="{{buydialogShow}}"><buydialog-tuangou vue-id="0be10a40-1" proid="{{proid}}" controller="ApiTuangou" menuindex="{{menuindex}}" data-event-opts="{{[['^buydialogChange',[['buydialogChange']]]]}}" bind:buydialogChange="__e" bind:__l="__l"></buydialog-tuangou></block></view>