<view class="container"><block wx:if="{{isload}}"><block><view class="order-content"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><view class="order-box"><view class="head"></view><view class="content"><view><image src="{{item.$orig.backimg}}"></image></view><view class="detail"><text class="t1">{{"场次名称:"+item.$orig.name}}</text><view class="t3"><block wx:if="{{currentTime<item.$orig.actual_starttime}}"><text class="x1 flex1">活动未开始<block wx:if="{{item.$orig.qiangtime>0}}"><text style="color:#1E9FFF;">{{'(您可提前'+item.$orig.qiangtime+'分钟抢购)'}}</text></block></text><text class="x2" style="background-color:#ccc;color:#fff;padding:10rpx 20rpx;border-radius:8rpx;">未开始</text></block><block wx:else><block wx:if="{{currentTime>item.$orig.endtime}}"><text class="x1 flex1">活动已结束</text><text class="x2" style="background-color:#ccc;color:#fff;padding:10rpx 20rpx;border-radius:8rpx;">已结束</text></block><block wx:else><text class="x1 flex1">{{''+(currentTime<item.$orig.starttime?'提前抢购中':'正常抢购中')+''}}</text><block wx:if="{{item.$orig.is_appointment==1}}"><block wx:if="{{item.$orig.already_reserved}}"><text class="x2" style="background-color:#1E9FFF;color:#fff;padding:10rpx 20rpx;border-radius:8rpx;" data-url="{{'classify2?ccid='+item.$orig.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">去抢购</text></block><block wx:else><text class="x2" style="{{(item.$orig.miaoshacount==0?'background-color: #ccc; cursor: not-allowed;':'background-color: #1E9FFF; color: #fff; padding: 10rpx 20rpx; border-radius: 8rpx; cursor: pointer;')}}" disabled="{{item.$orig.miaoshacount==0}}" data-event-opts="{{[['tap',[['makeReservation',['$0'],[[['datalist','',index,'id']]]]]]]}}" bindtap="__e">立即预约</text></block></block><block wx:else><text class="x2" style="background-color:#1E9FFF;color:#fff;padding:10rpx 20rpx;border-radius:8rpx;" data-url="{{'classify2?ccid='+item.$orig.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">去抢购</text></block></block></block></view><block wx:if="{{item.$orig.qingsuan_status==1&&item.$orig.isfukuan==1}}"><view class="t3"><text class="x1 flex1">订单处理中</text><text class="x2" data-url="{{'fukuan?id='+item.$orig.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">去查看</text></view></block><block wx:if="{{item.$orig.qingsuan_status==1&&item.$orig.isfukuan==0}}"><view class="t3"><text class="x1 flex1">订单处理中</text><text class="x2">未生成付款</text></view></block><block wx:if="{{item.$orig.qingsuan_status==2}}"><view class="t3"><text class="x1 flex1">已清算完成</text></view></block><block wx:if="{{item.$orig.starttime}}"><view class="t3"><text class="x1 flex1">{{"开始时间: "+item.m0}}</text></view></block><block wx:if="{{item.$orig.endtime}}"><view class="t3"><text class="x1 flex1">{{"结束时间: "+item.m1}}</text></view></block><block wx:if="{{item.$orig.actual_starttime&&currentTime<item.$orig.actual_starttime}}"><view class="t3"><text class="x1 flex1">{{'距开始：'+item.m2+''}}</text></view></block></view></view></view></block></block></view><block wx:if="{{nomore}}"><nomore vue-id="b28fd7a2-1" bind:__l="__l"></nomore></block><block wx:if="{{nodata}}"><nodata vue-id="b28fd7a2-2" bind:__l="__l"></nodata></block></block></block><block wx:if="{{loading}}"><loading vue-id="b28fd7a2-3" bind:__l="__l"></loading></block><dp-tabbar vue-id="b28fd7a2-4" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="b28fd7a2-5" data-ref="popmsg" bind:__l="__l"></popmsg></view>