<view><block wx:if="{{isload}}"><block><view class="orderinfo"><view class="item"><text class="t1">ID</text><text class="t2">{{member.id}}</text></view><view class="item"><text class="t1">昵称</text><text class="t2">{{member.company_name}}</text></view><view class="item"><text class="t1">地区</text><text class="t2">{{member.address}}</text></view><view class="item"><text class="t1">加入时间</text><text class="t2">{{member.created_at}}</text></view><view class="item"><text class="t1">姓名</text><text class="t2">{{member.contact_name}}</text></view><view class="item"><text class="t1">电话</text><text class="t2">{{member.phone}}</text></view><block wx:if="{{member.remark}}"><view class="item"><text class="t1">备注</text><text class="t2">{{member.remark}}</text></view></block><block wx:if="{{ordershow}}"><view class="item" style="justify-content:space-between;"><text class="t1" style="color:#007aff;">商城订单</text><view class="flex" data-url="{{'/admin/order/shoporder?mid='+member.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">{{member.ordercount+''}}<text class="iconfont iconjiantou" style="color:#999;font-weight:normal;margin-top:2rpx;"></text></view></view></block></view><view style="width:100%;height:120rpx;"></view><view class="bottom"><view class="btn" data-id="{{member.id}}" data-event-opts="{{[['tap',[['remark',['$event']]]]]}}" bindtap="__e">备注</view><view data-event-opts="{{[['tap',[['showFollowUpDialog',['$event']]]]]}}" class="btn" bindtap="__e">跟进</view><view class="btn" data-url="{{'shagjigenjinjilu?id='+member.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">跟进记录</view><view class="btn" data-url="{{'shangjixiugai?id='+member.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">商机修改</view></view><uni-popup class="vue-ref" vue-id="13a30f9c-1" id="remarkDialog" type="dialog" data-ref="remarkDialog" bind:__l="__l" vue-slots="{{['default']}}"><uni-popup-dialog vue-id="{{('13a30f9c-2')+','+('13a30f9c-1')}}" mode="input" title="设置备注" value="" placeholder="请输入备注" data-event-opts="{{[['^confirm',[['remarkConfirm']]]]}}" bind:confirm="__e" bind:__l="__l"></uni-popup-dialog></uni-popup><uni-popup class="vue-ref" vue-id="13a30f9c-3" id="followUpDialog" type="dialog" data-ref="followUpDialog" bind:__l="__l" vue-slots="{{['default']}}"><uni-popup-dialog vue-id="{{('13a30f9c-4')+','+('13a30f9c-3')}}" mode="input" title="添加跟进记录" value="" placeholder="请输入跟进内容" data-event-opts="{{[['^confirm',[['followUpConfirm']]]]}}" bind:confirm="__e" bind:__l="__l"></uni-popup-dialog></uni-popup></block></block><popmsg class="vue-ref" vue-id="13a30f9c-5" data-ref="popmsg" bind:__l="__l"></popmsg><block wx:if="{{loading}}"><loading vue-id="13a30f9c-6" bind:__l="__l"></loading></block></view>