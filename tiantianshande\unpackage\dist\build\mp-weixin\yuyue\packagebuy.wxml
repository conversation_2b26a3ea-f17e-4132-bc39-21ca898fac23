<block wx:if="{{!loading&&packageInfo.id}}"><view class="container"><view class="package-card"><image class="package-pic" src="{{packageInfo.pic}}" mode="aspectFill"></image><view class="package-details"><view class="package-name">{{packageInfo.name}}</view><view class="package-price-buy" style="{{'color:'+($root.m0)+';'}}">{{"￥"+packageInfo.sell_price}}</view></view></view><view class="form-section"><view class="form-item"><text class="label">联系人</text><input class="input" placeholder="请填写联系人姓名" data-event-opts="{{[['input',[['__set_model',['','linkman','$event',[]]]]]]}}" value="{{linkman}}" bindinput="__e"/></view><view class="form-item"><text class="label">联系电话</text><input class="input" type="number" placeholder="请填写联系电话" data-event-opts="{{[['input',[['__set_model',['','tel','$event',[]]]]]]}}" value="{{tel}}" bindinput="__e"/></view></view><view class="form-section"><block wx:if="{{$root.g0>0}}"><view data-event-opts="{{[['tap',[['chooseCoupon',['$event']]]]]}}" class="form-item arrow" bindtap="__e"><text class="label">优惠券</text><view class="{{['value','coupon-value',(!selectedCoupon)?'placeholder':'']}}">{{''+(selectedCoupon?'已选 '+selectedCoupon.name:couponCount+' 张可用')+''}}</view></view></block><block wx:if="{{orderData.userinfo&&orderData.userinfo.leveldk_money>0}}"><view class="form-item"><text class="label">会员折扣</text><view class="value discount-value" style="{{'color:'+($root.m1)+';'}}">{{"-￥"+orderData.userinfo.leveldk_money}}</view></view></block><block wx:if="{{coupon_money>0}}"><view class="form-item"><text class="label">优惠券抵扣</text><view class="value discount-value" style="{{'color:'+($root.m2)+';'}}">{{"-￥"+coupon_money}}</view></view></block><view class="form-item"><text class="label">商品金额</text><view class="value">{{"￥"+(packageInfo.product_price||packageInfo.sell_price)}}</view></view></view><view class="form-section"><view class="form-item remark-item"><text class="label">订单备注</text><textarea class="textarea" placeholder="选填，请输入备注信息" data-event-opts="{{[['input',[['__set_model',['','remark','$event',[]]]]]]}}" value="{{remark}}" bindinput="__e"></textarea></view></view><view class="bottom-bar-buy"><view class="total-price-area"><text>合计：</text><text class="price-symbol-buy" style="{{'color:'+($root.m3)+';'}}">￥</text><text class="total-price-value" style="{{'color:'+($root.m4)+';'}}">{{actual_price}}</text></view><button data-event-opts="{{[['tap',[['submitOrder',['$event']]]]]}}" class="submit-button" style="{{'background:'+($root.m5)+';'}}" bindtap="__e">提交订单</button></view><uni-popup class="vue-ref" vue-id="7201e0d6-1" type="bottom" data-ref="couponPopup" bind:__l="__l" vue-slots="{{['default']}}"><view class="coupon-popup-content"><view class="popup-header"><text>选择优惠券</text><text data-event-opts="{{[['tap',[['closeCouponPopup',['$event']]]]]}}" class="close-icon" bindtap="__e">×</text></view><scroll-view class="coupon-scroll" scroll-y="{{true}}"><block wx:for="{{$root.l0}}" wx:for-item="coupon" wx:for-index="__i0__" wx:key="couponrid"><view data-event-opts="{{[['tap',[['selectCoupon',['$0'],[[['couponList','couponrid',coupon.$orig.couponrid]]]]]]]}}" class="{{['coupon-item-popup',(selectedCoupon&&selectedCoupon.couponrid===coupon.$orig.couponrid)?'selected':'']}}" bindtap="__e"><view class="coupon-info"><view class="coupon-name">{{coupon.$orig.name}}</view><view class="coupon-desc">{{coupon.$orig.desc}}</view><view class="coupon-expire">{{"有效期至 "+coupon.$orig.expiredate}}</view></view><view class="coupon-radio" style="{{(selectedCoupon&&selectedCoupon.couponrid===coupon.$orig.couponrid?'border-color:'+coupon.m6+';background:'+coupon.m7:'')}}"><block wx:if="{{selectedCoupon&&selectedCoupon.couponrid===coupon.$orig.couponrid}}"><text class="check-icon">✓</text></block></view></view></block><view data-event-opts="{{[['tap',[['selectCoupon',[null]]]]]}}" class="{{['coupon-item-popup','no-use',(!selectedCoupon)?'selected':'']}}" bindtap="__e">不使用优惠券<view class="coupon-radio" style="{{(!selectedCoupon?'border-color:'+$root.m8+';background:'+$root.m9:'')}}"><block wx:if="{{!selectedCoupon}}"><text class="check-icon">✓</text></block></view></view></scroll-view></view></uni-popup></view></block><block wx:else><block wx:if="{{loading}}"><view class="loading-container"><text>加载中...</text></view></block><block wx:else><view class="empty-container"><text>加载订单信息失败</text></view></block></block>