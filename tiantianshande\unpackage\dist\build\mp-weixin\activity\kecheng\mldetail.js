(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["activity/kecheng/mldetail"],{"244b":function(e,t,o){"use strict";o.r(t);var i=o("be10"),n=o.n(i);for(var l in i)["default"].indexOf(l)<0&&function(e){o.d(t,e,(function(){return i[e]}))}(l);t["default"]=n.a},"5d74":function(e,t,o){"use strict";o.d(t,"b",(function(){return n})),o.d(t,"c",(function(){return l})),o.d(t,"a",(function(){return i}));var i={dp:function(){return o.e("components/dp/dp").then(o.bind(null,"1506"))},nomore:function(){return o.e("components/nomore/nomore").then(o.bind(null,"3892"))},nodata:function(){return o.e("components/nodata/nodata").then(o.bind(null,"101c"))},loading:function(){return o.e("components/loading/loading").then(o.bind(null,"ceaa"))},dpTabbar:function(){return o.e("components/dp-tabbar/dp-tabbar").then(o.bind(null,"b875"))},popmsg:function(){return o.e("components/popmsg/popmsg").then(o.bind(null,"2bf2"))}},n=function(){var e=this,t=e.$createElement,o=(e._self._c,e.isload&&e.kechengset&&1==e.kechengset.enable_notes&&(1==e.detail.ispay||0==e.kechengset.notes_need_buy)?e.t("color1"):null),i=e.noteDialogVisible&&1==e.noteTabIndex?e.t("color1"):null,n=e.noteDialogVisible&&2==e.noteTabIndex?e.myNotesList.length:null,l=e.noteDialogVisible&&2==e.noteTabIndex&&n>0?e.__map(e.myNotesList,(function(t,o){var i=e.__get_orig(t),n=JSON.stringify(t);return{$orig:i,g1:n}})):null,a=e.noteDialogVisible&&2==e.noteTabIndex?!e.noMoreNotes&&e.myNotesList.length>0:null,d=e.showSpeedMenu?e.__map(e.speedOptions,(function(t,o){var i=e.__get_orig(t),n=e.getSpeedDesc(t);return{$orig:i,m2:n}})):null;e._isMounted||(e.e0=function(t){e.showSpeedMenu=!0},e.e1=function(t){e.showSpeedMenu=!0},e.e2=function(t){e.showSpeedMenu=!1},e.e3=function(t){e.showSpeedMenu=!1}),e.$mp.data=Object.assign({},{$root:{m0:o,m1:i,g0:n,l0:l,g2:a,l1:d}})},l=[]},"954a":function(e,t,o){"use strict";(function(e,t){var i=o("47a9");o("06e9");i(o("3240"));var n=i(o("f8e0"));e.__webpack_require_UNI_MP_PLUGIN__=o,t(n.default)}).call(this,o("3223")["default"],o("df3c")["createPage"])},be10:function(e,t,o){"use strict";(function(e,o){Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i=getApp(),n=null,l={data:function(){return{loading:!1,isload:!1,isplay:0,detail:[],datalist:[],pagecontent:"",playshow:!0,stipshow:!1,lock:!1,status:1,currentTime:0,duration:"",videoContext:"",iskaoshi:"",pagenum:1,studlog:[],innerAudioContext:"",startTime:"",seek:!1,time:"",playJd:0,nowtime:"",isauto:!1,isVideoSticky:!1,videoHeight:0,scrollTop:0,currentChapter:null,showSpeedMenu:!1,currentSpeed:1,speedOptions:[.5,.75,1,1.25,1.5,1.75,2],noteDialogVisible:!1,noteTabIndex:1,noteForm:{content:"",time_point:"",study_progress:0},filterIndex:0,filterOptions:["全部","学习进度","时间点","章节"],myNotesList:[],loadingNotes:!1,noMoreNotes:!1,editingNoteId:null,notesPageNum:1,kechengset:{enable_notes:1,notes_need_progress:1},pre_url:""}},computed:{getCurrentChapter:function(){var e=this;return console.log("2025-01-03 22:55:53,565-INFO-[mldetail][getCurrentChapter_001] 计算当前章节信息"),this.datalist&&this.detail.id?this.datalist.find((function(t){return t.id===e.detail.id})):null}},onLoad:function(t){console.log("2025-01-14 22:55:53,565-INFO-[mldetail][onLoad_001] 页面加载开始，参数:",t),this.opt=i.getopts(t),this.currentSpeed=1,console.log("2025-01-14 22:55:53,565-INFO-[mldetail][onLoad_002] 重置倍速显示为默认值"),this.getdata(),this.getdatalist(),this.innerAudioContext=e.createInnerAudioContext(),console.log("2025-01-14 22:55:53,565-INFO-[mldetail][onLoad_003] 音频上下文创建完成")},onShow:function(){console.log("2025-01-03 22:55:53,565-INFO-[mldetail][onShow_001] 页面显示");clearInterval(n),this.innerAudioContext.stop()},onUnload:function(){console.log("2025-01-14 22:55:53,565-INFO-[mldetail][onUnload_001] 页面卸载，清理资源"),clearInterval(n);this.innerAudioContext.stop(),console.log("2025-01-14 22:55:53,566-INFO-[mldetail][onUnload_002] 小程序环境，跳过 offPageScroll 调用")},onHide:function(){console.log("2025-01-03 22:55:53,565-INFO-[mldetail][onHide_001] 页面隐藏"),this.playshow=!1},onReady:function(){console.log("2025-01-03 22:55:53,565-INFO-[mldetail][onReady_001] 页面准备完成"),this.initVideoStickyListener(),this.initVideoContext(),console.log("2025-01-03 22:55:53,565-INFO-[mldetail][onReady_002] 视频上下文初始化完成")},onPageScroll:function(e){console.log("2025-01-03 22:55:53,565-INFO-[mldetail][onPageScroll_001] 页面滚动，scrollTop:",e.scrollTop),this.scrollTop=e.scrollTop,this.handleVideoSticky(e.scrollTop)},onReachBottom:function(){console.log("2025-01-03 22:55:53,565-INFO-[mldetail][onReachBottom_001] 触底加载更多"),this.nodata||this.nomore||(this.pagenum=this.pagenum+1,this.getdatalist(!0))},methods:{scrollToCurrentChapter:function(){console.log("2025-01-03 22:55:53,565-INFO-[mldetail][scrollToCurrentChapter_001] 滚动到当前章节"),this.detail.id&&e.pageScrollTo({selector:"#chapter-"+this.detail.id,duration:300})},updateCurrentChapter:function(){var e=this;console.log("2025-01-03 22:55:53,565-INFO-[mldetail][updateCurrentChapter_001] 更新当前章节信息"),this.datalist&&this.detail.id&&(this.currentChapter=this.datalist.find((function(t){return t.id===e.detail.id})),console.log("2025-01-03 22:55:53,565-INFO-[mldetail][updateCurrentChapter_002] 当前章节信息:",this.currentChapter))},initVideoStickyListener:function(){var t=this;if(console.log("2025-01-03 22:55:53,565-INFO-[mldetail][initVideoStickyListener_001] 初始化视频吸顶监听"),3===this.detail.kctype){var o=e.createSelectorQuery().in(this);o.select(".videobox").boundingClientRect((function(e){e&&(t.videoHeight=e.height,console.log("2025-01-03 22:55:53,565-INFO-[mldetail][initVideoStickyListener_002] 视频高度获取成功:",t.videoHeight))})).exec()}},handleVideoSticky:function(t){var o=this;if(3===this.detail.kctype){console.log("2025-01-03 22:55:53,565-INFO-[mldetail][handleVideoSticky_001] 处理视频吸顶，scrollTop:",t);var i=e.createSelectorQuery().in(this);i.select(".videobox").boundingClientRect((function(e){e&&(console.log("2025-01-03 22:55:53,565-INFO-[mldetail][handleVideoSticky_002] 视频容器位置:",e.top),e.top<=0&&!o.isVideoSticky?(o.isVideoSticky=!0,console.log("2025-01-03 22:55:53,565-INFO-[mldetail][handleVideoSticky_003] 启用视频吸顶")):t<=0&&o.isVideoSticky&&(o.isVideoSticky=!1,console.log("2025-01-03 22:55:53,565-INFO-[mldetail][handleVideoSticky_004] 取消视频吸顶")))})).exec()}},getdata:function(){console.log("2025-01-14 22:55:53,565-INFO-[mldetail][getdata_001] 开始获取课程详情数据，参数:",this.opt);var t=this,o=this.opt.id||0;t.id=o;var l=this.opt.kcid||0;t.loading=!0,console.log("2025-01-14 22:55:53,565-INFO-[mldetail][getdata_002] 调用ApiKecheng/mldetail接口，id:",o,"kcid:",l),i.get("ApiKecheng/mldetail",{id:o,kcid:l},(function(o){console.log("2025-01-14 22:55:53,565-INFO-[mldetail][getdata_003] ApiKecheng/mldetail接口返回结果:",o),t.loading=!1;var l=o.detail;if(t.detail=l,t.iskaoshi=o.iskaoshi,t.isauto=o.isauto,t.currentTime=l.startTime,e.setNavigationBarTitle({title:l.name}),l.jumpurl)i.goto(l.jumpurl);else{t.studylog=o.studylog,console.log("2025-01-14 22:55:53,565-INFO-[mldetail][getdata_004] 学习记录信息:",o.studylog),o.kechengset&&(t.kechengset=o.kechengset,console.log("2025-01-14 22:55:53,565-INFO-[mldetail][getdata_008] 课程设置信息:",o.kechengset));var a=JSON.parse(l.detail);t.pagecontent=a,t.loaded({title:l.name,pic:l.pic}),console.log("2025-01-14 22:55:53,565-INFO-[mldetail][getdata_005] 准备调用addstudy方法"),t.addstudy(),l.kctype>1&&(n=setInterval((function(){t.addstudy()}),1e4)),t.play(),t.$nextTick((function(){t.initVideoStickyListener(),t.updateCurrentChapter(),setTimeout((function(){t.restoreSpeedSetting()}),2e3)}))}}))},todetail:function(e){console.log("2025-01-14 22:55:53,565-INFO-[mldetail][todetail_001] 点击章节跳转，事件数据:",e.currentTarget.dataset);var t=this,o=e.currentTarget.dataset.url,n=e.currentTarget.dataset.mianfei,l=e.currentTarget.dataset.opentype,a=e.currentTarget.dataset.key;console.log("2025-01-14 22:55:53,565-INFO-[mldetail][todetail_002] 跳转参数 - URL:",o,"免费试看:",n,"打开方式:",l,"key:",a),console.log("2025-01-14 22:55:53,565-INFO-[mldetail][todetail_003] 当前课程状态 - 是否购买:",t.detail.ispay,"课程价格:",t.detail.price),1==n||1==t.detail.ispay||0==t.detail.price?(console.log("2025-01-14 22:55:53,565-INFO-[mldetail][todetail_004] 有权限访问章节，准备跳转:",o),i.goto(o,l)):(console.log("2025-01-14 22:55:53,565-INFO-[mldetail][todetail_005] 无权限访问章节，需要购买课程"),i.alert("请先购买课程",(function(){i.goto("product?id="+t.opt.kcid)})))},getdatalist:function(t){console.log("2025-01-03 22:55:53,565-INFO-[mldetail][getdatalist_001] 获取课程目录列表，loadmore:",t),t||(this.pagenum=1,this.datalist=[]);var o=this,n=o.pagenum,l=o.opt.kcid?o.opt.kcid:"",a=o.order,d=o.field;o.loading=!0,o.nodata=!1,o.nomore=!1,i.post("ApiKecheng/getmululist",{pagenum:n,field:d,order:a,id:l},(function(t){console.log("2025-01-03 22:55:53,565-INFO-[mldetail][getdatalist_002] 课程目录数据获取成功:",t),o.loading=!1,e.stopPullDownRefresh();var i=t.data;if(1==n)o.datalist=i,0==i.length&&(o.nodata=!0);else if(0==i.length)o.nomore=!0;else{var l=o.datalist,a=l.concat(i);o.datalist=a}o.$nextTick((function(){o.updateCurrentChapter()}))}))},scrolltolower:function(){console.log("2025-01-03 22:55:53,565-INFO-[mldetail][scrolltolower_001] 滚动到底部"),this.nomore||(this.pagenum=this.pagenum+1,this.getdatalist(!0))},payvideo:function(){console.log("2025-01-03 22:55:53,565-INFO-[mldetail][payvideo_001] 播放视频"),this.isplay=1,e.createVideoContext("video").play()},parsevideo:function(){console.log("2025-01-03 22:55:53,565-INFO-[mldetail][parsevideo_001] 暂停视频"),this.isplay=0,e.createVideoContext("video").stop()},pause:function(){console.log("2025-01-03 22:55:53,565-INFO-[mldetail][pause_001] 视频暂停，记录学习进度");this.opt.id&&this.opt.id;this.addstudy()},addstudy:function(){console.log("2025-01-14 22:55:53,565-INFO-[mldetail][addstudy_001] 开始记录学习进度，当前进度:",this.currentTime,"学习进度:",this.playJd),console.log("2025-01-14 22:55:53,565-INFO-[mldetail][addstudy_002] 学习记录信息:",this.studylog);var e=this,t=e.studylog.id;e.detail.id;console.log("2025-01-14 22:55:53,565-INFO-[mldetail][addstudy_003] 准备调用ApiKecheng/addstudy接口，参数:",{logid:t,currentTime:e.currentTime,playJd:e.playJd}),i.post("ApiKecheng/addstudy",{logid:t,currentTime:e.currentTime,playJd:e.playJd},(function(t){console.log("2025-01-14 22:55:53,565-INFO-[mldetail][addstudy_004] ApiKecheng/addstudy接口返回结果:",t),1==t.status?(console.log("2025-01-14 22:55:53,565-INFO-[mldetail][addstudy_005] 学习记录保存成功，进度:",t.jindu),e.datalist[e.detail.key].jindu=t.jindu,e.detail.startTime=e.currentTime):console.log("2025-01-14 22:55:53,565-ERROR-[mldetail][addstudy_006] 学习记录保存失败:",t.msg)}))},timeupdate:function(e){var t=e.detail.duration,i=e.detail.currentTime;console.log("2025-01-03 22:55:53,565-INFO-[mldetail][timeupdate_001] 视频时间更新，当前时间:",i,"总时长:",t);var n=this.currentTime;if(1==this.detail.isjinzhi&&i>n&&i-n>1&&"100"!=this.datalist[this.detail.key].jindu){var l=o.createVideoContext("video");l.seek(this.currentTime),o.showToast({title:"未完整看完该视频，不能快进",icon:"none",duration:2e3})}this.currentTime=i;var a=Math.floor(i/60),d=i%60;this.nowtime=(a>=10?a:"0"+a)+":"+(d>=10?d:"0"+d),this.playJd<100&&(this.playJd=100*(this.currentTime/(t-1)).toFixed(2),this.playJd>100&&(this.playJd=100)),this.datalist[this.detail.key].jindu=this.playJd.toFixed(1)},ended:function(){console.log("2025-01-03 22:55:53,565-INFO-[mldetail][ended_001] 视频播放结束");1!=this.iskaoshi&&100==this.playJd&&this.isauto&&1==this.detail.isdt&&i.goto("tiku?id="+this.detail.kcid)},play:function(){var t=this;console.log("2025-01-14 22:55:53,565-INFO-[mldetail][play_001] 开始播放音频");var o=this;this.playshow=!0,this.innerAudioContext.autoplay=!0,this.innerAudioContext.src=o.detail.voice_url,this.innerAudioContext.play(),this.innerAudioContext.onCanplay((function(){t.innerAudioContext.duration,setTimeout((function(){o.time=t.innerAudioContext.duration.toFixed(0);var e=Math.floor(o.time/60),i=o.time%60;t.duration=(e>10?e:"0"+e)+":"+(i>10?i:"0"+i),console.log("2025-01-14 22:55:53,565-INFO-[mldetail][play_002] 音频时长获取成功:",t.duration),setTimeout((function(){o.restoreAndApplySpeedSetting()}),1e3)}),1e3)})),o.startTime=o.detail.startTime,o.detail.startTime>=o.detail.video_duration&&(o.startTime=0),this.innerAudioContext.seek(o.startTime),this.innerAudioContext.onPlay((function(){console.log("2025-01-14 22:55:53,565-INFO-[mldetail][play_003] 音频开始播放"),o.playshow=!1,setTimeout((function(){var t=e.getStorageSync("playbackSpeed")||1;1!==t&&o.applyAudioSpeedSetting(t)}),500)})),this.innerAudioContext.onPause((function(){console.log("2025-01-14 22:55:53,565-INFO-[mldetail][play_004] 音频暂停播放"),o.playshow=!0})),this.innerAudioContext.onEnded((function(){console.log("2025-01-14 22:55:53,565-INFO-[mldetail][play_005] 音频播放结束"),o.playJd=100,clearInterval(n),o.addstudy(),o.playshow=!0})),this.innerAudioContext.onTimeUpdate((function(){var e=t.innerAudioContext.currentTime.toFixed(0),i=Math.floor(e/60),n=e%60;o.nowtime=(i>=10?i:"0"+i)+":"+(n>=10?n:"0"+n),o.playJd<100&&o.innerAudioContext.duration>0&&(o.playJd=100*(e/o.innerAudioContext.duration).toFixed(2),o.playJd>100&&(o.playJd=100)),o.currentTime=t.innerAudioContext.currentTime}))},pauseaudio:function(){console.log("2025-01-03 22:55:53,565-INFO-[mldetail][pauseaudio_001] 暂停音频播放");this.innerAudioContext.pause(),this.addstudy()},sliderChange:function(e){console.log("2025-01-03 22:55:53,565-INFO-[mldetail][sliderChange_001] 拖动进度条到:",e.detail.value);1==this.detail.isjinzhi&&e.detail.value>this.detail.startTime&&"100"!=this.datalist[this.detail.key].jindu?i.error("未完整听完该音频，不能快进"):(this.currentTime=e.detail.value,this.innerAudioContext.seek(e.detail.value))},sliderChanging:function(e){console.log("2025-01-03 22:55:53,565-INFO-[mldetail][sliderChanging_001] 正在拖动进度条:",e.detail.value),this.currentTime=e.detail.value},showSpeedMenu:function(e){console.log("2025-01-03 22:55:53,565-INFO-[mldetail][showSpeedMenu_001] 显示倍速选择菜单"),this.currentSpeed=e,this.showSpeedMenu=!0},selectSpeed:function(t){console.log("2025-01-14 22:55:53,565-INFO-[mldetail][selectSpeed_001] 选择倍速:",t),this.currentSpeed=t,this.showSpeedMenu=!1,this.applySpeedSetting(t),e.setStorageSync("playbackSpeed",t),console.log("2025-01-14 22:55:53,565-INFO-[mldetail][selectSpeed_002] 倍速设置已保存到本地存储:",t),e.showToast({title:"倍速已设置为".concat(t,"x"),icon:"none",duration:1500})},getSpeedDesc:function(e){console.log("2025-01-03 22:55:53,565-INFO-[mldetail][getSpeedDesc_001] 获取倍速描述:",e);return{.5:"慢速",.75:"较慢",1:"正常",1.25:"较快",1.5:"快速",1.75:"很快",2:"超快"}[e]||"正常"},applySpeedSetting:function(t){console.log("2025-01-14 22:55:53,565-INFO-[mldetail][applySpeedSetting_001] 应用倍速设置:",t);var o=this;3==this.detail.kctype?(console.log("2025-01-14 22:55:53,565-INFO-[mldetail][applySpeedSetting_002] 设置视频倍速:",t),setTimeout((function(){try{o.videoContext=e.createVideoContext("video",o),o.videoContext&&"function"===typeof o.videoContext.playbackRate?(o.videoContext.playbackRate(t),console.log("2025-01-14 22:55:53,565-INFO-[mldetail][applySpeedSetting_003] 视频倍速设置成功:",t)):(console.log("2025-01-14 22:55:53,565-WARN-[mldetail][applySpeedSetting_004] 视频上下文不支持倍速功能"),1!==t&&e.showToast({title:"当前设备不支持视频倍速",icon:"none",duration:2e3}))}catch(i){console.log("2025-01-14 22:55:53,565-ERROR-[mldetail][applySpeedSetting_005] 视频倍速设置失败:",i),1!==t&&e.showToast({title:"倍速设置失败，请重试",icon:"none",duration:2e3})}}),500)):2==this.detail.kctype&&(console.log("2025-01-14 22:55:53,565-INFO-[mldetail][applySpeedSetting_006] 设置音频倍速:",t),setTimeout((function(){try{o.innerAudioContext&&"undefined"!==typeof o.innerAudioContext.playbackRate?(o.innerAudioContext.playbackRate=t,console.log("2025-01-14 22:55:53,565-INFO-[mldetail][applySpeedSetting_007] 音频倍速设置成功:",t),setTimeout((function(){Math.abs(o.innerAudioContext.playbackRate-t)>.01&&(console.log("2025-01-14 22:55:53,565-WARN-[mldetail][applySpeedSetting_008] 音频倍速设置未生效"),1!==t&&e.showToast({title:"当前设备音频倍速功能不稳定",icon:"none",duration:2e3}))}),100)):(console.log("2025-01-14 22:55:53,565-WARN-[mldetail][applySpeedSetting_009] 音频上下文不支持倍速功能"),1!==t&&e.showToast({title:"当前设备不支持音频倍速",icon:"none",duration:2e3}))}catch(i){console.log("2025-01-14 22:55:53,565-ERROR-[mldetail][applySpeedSetting_010] 音频倍速设置失败:",i),1!==t&&e.showToast({title:"倍速设置失败，请重试",icon:"none",duration:2e3})}}),300))},initVideoContext:function(){console.log("2025-01-14 22:55:53,565-INFO-[mldetail][initVideoContext_001] 初始化视频上下文");var t=this;3==this.detail.kctype&&setTimeout((function(){t.videoContext=e.createVideoContext("video",t),console.log("2025-01-14 22:55:53,565-INFO-[mldetail][initVideoContext_002] 视频上下文创建完成"),setTimeout((function(){t.restoreAndApplySpeedSetting()}),1e3)}),1e3)},showNoteDialog:function(){console.log("2025-01-03 22:55:53,565-INFO-[mldetail][showNoteDialog_001] 显示笔记弹窗"),this.noteDialogVisible=!0,this.noteTabIndex=1,this.resetNoteForm(),this.getMyNotes()},hideNoteDialog:function(){console.log("2025-01-03 22:55:53,565-INFO-[mldetail][hideNoteDialog_001] 隐藏笔记弹窗"),this.noteDialogVisible=!1,this.editingNoteId=null,this.resetNoteForm()},resetNoteForm:function(){console.log("2025-01-03 22:55:53,565-INFO-[mldetail][resetNoteForm_001] 重置笔记表单"),this.noteForm={content:"",time_point:this.currentTime||"",study_progress:(this.kechengset&&this.kechengset.notes_need_progress,0)},this.editingNoteId=null},switchNoteTab:function(e){console.log("2025-01-03 22:55:53,565-INFO-[mldetail][switchNoteTab_001] 切换笔记标签页");var t=e.currentTarget.dataset.index;this.noteTabIndex=t,2==t&&this.getMyNotes()},onProgressChange:function(e){console.log("2025-01-03 22:55:53,565-INFO-[mldetail][onProgressChange_001] 学习进度变化"),this.noteForm.study_progress=e.detail.value},submitNote:function(){console.log("2025-01-03 22:55:53,565-INFO-[mldetail][submitNote_001] 开始提交笔记");var e=this;if(e.noteForm.content.trim()){var t=e.editingNoteId?"ApiKechengNotes/updateNote":"ApiKechengNotes/addNote",o={kcid:e.opt.kcid,chapter_id:e.detail.id||0,content:e.noteForm.content.trim(),note_time:e.noteForm.time_point||e.currentTime||0};e.kechengset&&1==e.kechengset.notes_need_progress&&(o.study_progress=e.noteForm.study_progress),e.editingNoteId&&(o.id=e.editingNoteId),console.log("2025-01-03 22:55:53,565-INFO-[mldetail][submitNote_002] 提交参数:",o),i.showLoading("保存中..."),i.post(t,o,(function(t){i.showLoading(!1),console.log("2025-01-03 22:55:53,565-INFO-[mldetail][submitNote_003] 笔记保存结果:",t),1==t.status?(i.success(t.msg),e.resetNoteForm(),e.noteTabIndex=2,e.getMyNotes()):i.error(t.msg)}))}else i.error("请输入笔记内容")},getMyNotes:function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];console.log("2025-01-03 22:55:53,565-INFO-[mldetail][getMyNotes_001] 获取我的笔记列表，loadMore:",e);var t=this;e||(t.notesPageNum=1,t.myNotesList=[]),t.loadingNotes=!0,t.noMoreNotes=!1;var o={kcid:t.opt.kcid,chapter_id:t.detail.id||0,pagenum:t.notesPageNum,pernum:10};i.post("ApiKechengNotes/getMyNotes",o,(function(e){if(t.loadingNotes=!1,console.log("2025-01-03 22:55:53,565-INFO-[mldetail][getMyNotes_002] 笔记列表获取结果:",e),1==e.status){var o=e.data||[];1==t.notesPageNum?t.myNotesList=o:t.myNotesList=t.myNotesList.concat(o),o.length<10&&(t.noMoreNotes=!0)}else i.error(e.msg)}))},editNote:function(e){console.log("2025-01-03 22:55:53,565-INFO-[mldetail][editNote_001] 编辑笔记");var t=JSON.parse(e.currentTarget.dataset.note);this.editingNoteId=t.id,this.noteForm={content:t.content,study_progress:t.study_progress||0,time_point:t.note_time||""},this.noteTabIndex=1},deleteNote:function(e){console.log("2025-01-03 22:55:53,565-INFO-[mldetail][deleteNote_001] 删除笔记");var t=this,o=e.currentTarget.dataset.id;i.confirm("确定要删除这条笔记吗？",(function(){i.showLoading("删除中..."),i.post("ApiKechengNotes/deleteNote",{id:o},(function(e){i.showLoading(!1),console.log("2025-01-03 22:55:53,565-INFO-[mldetail][deleteNote_002] 删除结果:",e),1==e.status?(i.success(e.msg),t.getMyNotes()):i.error(e.msg)}))}))},onFilterChange:function(e){console.log("2025-01-03 22:55:53,565-INFO-[mldetail][onFilterChange_001] 筛选变化:",e.detail.value),this.filterIndex=e.detail.value,this.getMyNotes()},loadMoreNotes:function(){console.log("2025-01-03 22:55:53,565-INFO-[mldetail][loadMoreNotes_001] 加载更多笔记"),this.loadingNotes||this.noMoreNotes||(this.notesPageNum++,this.getMyNotes(!0))},applyAudioSpeedSetting:function(e){console.log("2025-01-14 22:55:53,565-INFO-[mldetail][applyAudioSpeedSetting_001] 设置音频倍速:",e);var t=this;try{if(t.innerAudioContext&&"undefined"!==typeof t.innerAudioContext.playbackRate){var o=0,i=3;(function n(){try{t.innerAudioContext.playbackRate=e,console.log("2025-01-14 22:55:53,565-INFO-[mldetail][applyAudioSpeedSetting_002] 音频倍速设置尝试:",o+1,"倍速:",e),setTimeout((function(){t.innerAudioContext&&Math.abs(t.innerAudioContext.playbackRate-e)<.01?console.log("2025-01-14 22:55:53,565-INFO-[mldetail][applyAudioSpeedSetting_003] 音频倍速设置成功验证:",t.innerAudioContext.playbackRate):o<i-1?(o++,setTimeout(n,200)):console.log("2025-01-14 22:55:53,565-WARN-[mldetail][applyAudioSpeedSetting_004] 音频倍速设置多次尝试后仍未生效")}),100)}catch(l){console.log("2025-01-14 22:55:53,565-ERROR-[mldetail][applyAudioSpeedSetting_005] 音频倍速设置失败:",l)}})()}else console.log("2025-01-14 22:55:53,565-WARN-[mldetail][applyAudioSpeedSetting_006] 音频上下文不支持倍速或未初始化")}catch(n){console.log("2025-01-14 22:55:53,565-ERROR-[mldetail][applyAudioSpeedSetting_007] 音频倍速设置异常:",n)}},initSpeedSetting:function(){console.log("2025-01-14 22:55:53,565-INFO-[mldetail][initSpeedSetting_001] 初始化倍速设置"),this.currentSpeed=1,console.log("2025-01-14 22:55:53,565-INFO-[mldetail][initSpeedSetting_002] 设置默认倍速显示为1")},restoreSpeedSetting:function(){console.log("2025-01-14 22:55:53,565-INFO-[mldetail][restoreSpeedSetting_001] 恢复倍速设置");var t=e.getStorageSync("playbackSpeed");t&&1!==t&&(this.currentSpeed=t,console.log("2025-01-14 22:55:53,565-INFO-[mldetail][restoreSpeedSetting_002] 从本地存储恢复倍速设置:",t))},restoreAndApplySpeedSetting:function(){console.log("2025-01-14 22:55:53,565-INFO-[mldetail][restoreAndApplySpeedSetting_001] 恢复并应用倍速设置");var t=e.getStorageSync("playbackSpeed");t&&1!==t?(this.currentSpeed=t,console.log("2025-01-14 22:55:53,565-INFO-[mldetail][restoreAndApplySpeedSetting_002] 恢复倍速设置:",t),this.applySpeedSetting(t)):console.log("2025-01-14 22:55:53,565-INFO-[mldetail][restoreAndApplySpeedSetting_003] 使用默认倍速1")}}};t.default=l}).call(this,o("df3c")["default"],o("3223")["default"])},c607:function(e,t,o){"use strict";var i=o("ee6c"),n=o.n(i);n.a},ee6c:function(e,t,o){},f8e0:function(e,t,o){"use strict";o.r(t);var i=o("5d74"),n=o("244b");for(var l in n)["default"].indexOf(l)<0&&function(e){o.d(t,e,(function(){return n[e]}))}(l);o("c607");var a=o("828b"),d=Object(a["a"])(n["default"],i["b"],i["c"],!1,null,null,null,!1,i["a"],void 0);t["default"]=d.exports}},[["954a","common/runtime","common/vendor"]]]);