require('../common/vendor.js');(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pagesExb/business/index"],{8244:function(t,n,e){"use strict";e.d(n,"b",(function(){return a})),e.d(n,"c",(function(){return u})),e.d(n,"a",(function(){}));var a=function(){var t=this.$createElement;this._self._c},u=[]},af5c:function(t,n,e){"use strict";e.r(n);var a=e("dbc3"),u=e.n(a);for(var o in a)["default"].indexOf(o)<0&&function(t){e.d(n,t,(function(){return a[t]}))}(o);n["default"]=u.a},b2b5:function(t,n,e){"use strict";e.r(n);var a=e("8244"),u=e("af5c");for(var o in u)["default"].indexOf(o)<0&&function(t){e.d(n,t,(function(){return u[t]}))}(o);var r=e("828b"),i=Object(r["a"])(u["default"],a["b"],a["c"],!1,null,null,null,!1,a["a"],void 0);n["default"]=i.exports},dbc3:function(t,n,e){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;var a=getApp(),u={data:function(){return{opt:{},loading:!1,isload:!1,menuindex:-1,platform:a.globalData.platform,pre_url:a.globalData.pre_url}},onLoad:function(t){this.opt=a.getopts(t),a.goto("/pagesExt/business/index?id="+this.opt.id,"redirect")}};n.default=u},dbd0:function(t,n,e){"use strict";(function(t,n){var a=e("47a9");e("06e9");a(e("3240"));var u=a(e("b2b5"));t.__webpack_require_UNI_MP_PLUGIN__=e,n(u.default)}).call(this,e("3223")["default"],e("df3c")["createPage"])}},[["dbd0","common/runtime","common/vendor"]]]);