<view class="resume-page data-v-145b735b"><view class="header data-v-145b735b" style="{{('background:linear-gradient(135deg,'+$root.m0+' 0%,rgba('+$root.m1+',0.8) 100%)')}}"><text class="title data-v-145b735b">完善简历</text><text class="subtitle data-v-145b735b">完善您的简历信息，获得更多工作机会</text><view class="step-nav data-v-145b735b"><block wx:for="{{$root.l0}}" wx:for-item="step" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['jumpToStep',[index]]]]]}}" class="{{['step-item','data-v-145b735b',(currentStep===index)?'active':'',(index<currentStep)?'completed':'']}}" bindtap="__e"><view class="step-number data-v-145b735b" style="{{(currentStep===index||index<currentStep?'background-color:'+step.m2+';color:#fff':'')}}">{{''+(index+1)+''}}</view><text class="step-text data-v-145b735b">{{step.$orig.title}}</text></view></block></view></view><swiper class="content data-v-145b735b" style="{{'height:'+(contentHeight+'px')+';'}}" current="{{currentStep}}" disable-touch="{{true}}" data-event-opts="{{[['change',[['onSwiperChange',['$event']]]]]}}" bindchange="__e"><swiper-item class="data-v-145b735b"><scroll-view class="step-content data-v-145b735b" scroll-y="{{true}}"><view class="section basic-info data-v-145b735b"><view class="section-header data-v-145b735b"><text class="section-title data-v-145b735b"><view class="title-bar data-v-145b735b" style="{{('background:'+$root.m3)}}"></view>基本信息</text></view><view class="avatar-wrapper data-v-145b735b"><view class="flex data-v-145b735b" style="flex-wrap:wrap;padding-bottom:20rpx;"><block wx:if="{{formData.avatar}}"><view class="layui-imgbox data-v-145b735b"><view data-event-opts="{{[['tap',[['removeAvatar',['$event']]]]]}}" class="layui-imgbox-close data-v-145b735b" bindtap="__e"><image src="/static/img/ico-del.png" class="data-v-145b735b"></image></view><view class="layui-imgbox-img data-v-145b735b"><image src="{{formData.avatar}}" data-url="{{formData.avatar}}" mode="aspectFill" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e" class="data-v-145b735b"></image></view></view></block><block wx:else><view data-event-opts="{{[['tap',[['chooseAvatar',['$event']]]]]}}" class="uploadbtn data-v-145b735b" style="{{('background:url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 60rpx;background-size:80rpx 80rpx;background-color:#F3F3F3;')}}" bindtap="__e"></view></block></view><text class="upload-text data-v-145b735b">点击上传头像</text></view><view class="form-group data-v-145b735b"><view class="input-wrapper data-v-145b735b"><text class="input-label required data-v-145b735b">姓名</text><input class="input-item data-v-145b735b" style="{{(isFocused?'border-color:'+$root.m4+';box-shadow:0 0 0 3rpx rgba('+$root.m5+',0.1)':'')}}" placeholder="请输入您的真实姓名" maxlength="20" data-event-opts="{{[['input',[['__set_model',['$0','name','$event',[]],['formData']]]]]}}" value="{{formData.name}}" bindinput="__e"/></view><view class="input-wrapper data-v-145b735b"><text class="input-label required data-v-145b735b">手机号码</text><input class="input-item data-v-145b735b" style="{{(isFocused?'border-color:'+$root.m6+';box-shadow:0 0 0 3rpx rgba('+$root.m7+',0.1)':'')}}" type="number" placeholder="请输入手机号码" maxlength="11" data-event-opts="{{[['input',[['__set_model',['$0','phone','$event',[]],['formData']]]]]}}" value="{{formData.phone}}" bindinput="__e"/></view><view class="input-row data-v-145b735b"><view class="input-wrapper half data-v-145b735b"><text class="input-label required data-v-145b735b">性别</text><picker class="input-item data-v-145b735b" style="{{(isFocused?'border-color:'+$root.m8+';box-shadow:0 0 0 3rpx rgba('+$root.m9+',0.1)':'')}}" mode="selector" range="{{genderOptions}}" data-event-opts="{{[['change',[['onGenderChange',['$event']]]]]}}" bindchange="__e"><view class="picker-item data-v-145b735b">{{''+(formData.gender||'请选择性别')+''}}</view></picker></view><view class="input-wrapper half data-v-145b735b"><text class="input-label required data-v-145b735b">出生日期</text><picker class="input-item data-v-145b735b" style="{{(isFocused?'border-color:'+$root.m10+';box-shadow:0 0 0 3rpx rgba('+$root.m11+',0.1)':'')}}" mode="date" start="{{startDate}}" end="{{endDate}}" data-event-opts="{{[['change',[['onBirthChange',['$event']]]]]}}" bindchange="__e"><view class="picker-item data-v-145b735b">{{''+(formData.birthday||'请选择出生日期')+''}}</view></picker></view></view><view class="input-wrapper data-v-145b735b"><text class="input-label data-v-145b735b">邮箱</text><input class="input-item data-v-145b735b" style="{{(isFocused?'border-color:'+$root.m12+';box-shadow:0 0 0 3rpx rgba('+$root.m13+',0.1)':'')}}" type="text" placeholder="请输入邮箱地址" data-event-opts="{{[['input',[['__set_model',['$0','email','$event',[]],['formData']]]]]}}" value="{{formData.email}}" bindinput="__e"/></view><view class="input-wrapper data-v-145b735b"><text class="input-label data-v-145b735b">居住地址</text><input class="input-item data-v-145b735b" style="{{(isFocused?'border-color:'+$root.m14+';box-shadow:0 0 0 3rpx rgba('+$root.m15+',0.1)':'')}}" type="text" placeholder="请输入居住地址" data-event-opts="{{[['input',[['__set_model',['$0','address','$event',[]],['formData']]]]]}}" value="{{formData.address}}" bindinput="__e"/></view><view class="input-row data-v-145b735b"><view class="input-wrapper half data-v-145b735b"><text class="input-label required data-v-145b735b">民族</text><picker class="input-item data-v-145b735b" style="{{(isFocused?'border-color:'+$root.m16+';box-shadow:0 0 0 3rpx rgba('+$root.m17+',0.1)':'')}}" mode="selector" range="{{nationOptions}}" data-event-opts="{{[['change',[['onNationChange',['$event']]]]]}}" bindchange="__e"><view class="picker-item data-v-145b735b">{{''+(formData.nation||'请选择民族')+''}}</view></picker></view><view class="input-wrapper half data-v-145b735b"><text class="input-label required data-v-145b735b">年龄</text><input class="input-item data-v-145b735b" style="{{(isFocused?'border-color:'+$root.m18+';box-shadow:0 0 0 3rpx rgba('+$root.m19+',0.1)':'')}}" type="number" placeholder="请输入年龄" maxlength="3" data-event-opts="{{[['input',[['__set_model',['$0','age','$event',[]],['formData']]]]]}}" value="{{formData.age}}" bindinput="__e"/></view></view><view class="input-wrapper data-v-145b735b"><text class="input-label required data-v-145b735b">文化程度</text><picker class="input-item data-v-145b735b" style="{{(isFocused?'border-color:'+$root.m20+';box-shadow:0 0 0 3rpx rgba('+$root.m21+',0.1)':'')}}" mode="selector" range="{{cultureOptions}}" data-event-opts="{{[['change',[['onCultureChange',['$event']]]]]}}" bindchange="__e"><view class="picker-item data-v-145b735b">{{''+(formData.culture||'请选择文化程度')+''}}</view></picker></view></view></view></scroll-view></swiper-item><swiper-item class="data-v-145b735b"><scroll-view class="step-content data-v-145b735b" scroll-y="{{true}}"><view class="section education data-v-145b735b"><view class="section-header data-v-145b735b"><text class="section-title data-v-145b735b"><view class="title-bar data-v-145b735b" style="{{('background:linear-gradient(to bottom,'+$root.m22+' 0%,rgba('+$root.m23+',0.8) 100%)')}}"></view>教育经历</text><view data-event-opts="{{[['tap',[['addEducation',['$event']]]]]}}" class="add-btn data-v-145b735b" style="{{('color:'+$root.m24+';background:rgba('+$root.m25+',0.1)')}}" bindtap="__e">添加教育经历</view></view><block wx:if="{{$root.g0===0}}"><view class="empty-tip data-v-145b735b"><image class="empty-icon data-v-145b735b" src="/static/images/empty.png" mode="aspectFit"></image><text class="data-v-145b735b">暂无教育经历，点击上方添加</text></view></block><block wx:else><block wx:for="{{$root.l1}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="edu-item data-v-145b735b"><view class="item-header data-v-145b735b"><text class="item-index data-v-145b735b" style="{{('background:linear-gradient(135deg,'+item.m26+' 0%,rgba('+item.m27+',0.8) 100%);box-shadow:0 4rpx 12rpx rgba('+item.m28+',0.2);'+(isFocused?'border-color:'+item.m29+';box-shadow:0 0 0 3rpx rgba('+item.m30+',0.1)':''))}}">{{''+(index+1)+''}}</text><view data-event-opts="{{[['tap',[['deleteEducation',[index]]]]]}}" class="delete-btn data-v-145b735b" bindtap="__e"><text class="delete-icon data-v-145b735b">×</text><text class="data-v-145b735b">删除</text></view></view><view class="form-group data-v-145b735b"><view class="input-wrapper data-v-145b735b"><text class="input-label data-v-145b735b">学校名称</text><input class="input-item data-v-145b735b" style="{{(isFocused?'border-color:'+item.m31+';box-shadow:0 0 0 3rpx rgba('+item.m32+',0.1)':'')}}" placeholder="请输入学校名称" data-event-opts="{{[['input',[['__set_model',['$0','school','$event',[]],[[['formData.education','',index]]]]]]]}}" value="{{item.$orig.school}}" bindinput="__e"/></view><view class="input-wrapper data-v-145b735b"><text class="input-label data-v-145b735b">专业</text><input class="input-item data-v-145b735b" style="{{(isFocused?'border-color:'+item.m33+';box-shadow:0 0 0 3rpx rgba('+item.m34+',0.1)':'')}}" placeholder="请输入专业名称" data-event-opts="{{[['input',[['__set_model',['$0','major','$event',[]],[[['formData.education','',index]]]]]]]}}" value="{{item.$orig.major}}" bindinput="__e"/></view><view class="input-wrapper data-v-145b735b"><text class="input-label data-v-145b735b">学历</text><picker class="input-item data-v-145b735b" style="{{(isFocused?'border-color:'+item.m35+';box-shadow:0 0 0 3rpx rgba('+item.m36+',0.1)':'')}}" mode="selector" range="{{educationLevels}}" data-event-opts="{{[['change',[['e0',['$event']]]]]}}" data-event-params="{{({index})}}" bindchange="__e"><view class="picker-item data-v-145b735b">{{item.$orig.level||'请选择学历'}}</view></picker></view><view class="date-range data-v-145b735b"><view class="date-item data-v-145b735b"><text class="input-label data-v-145b735b">入学时间</text><picker style="{{(isFocused?'border-color:'+item.m37+';box-shadow:0 0 0 3rpx rgba('+item.m38+',0.1)':'')}}" mode="date" start="{{startDate}}" end="{{endDate}}" data-event-opts="{{[['change',[['e1',['$event']]]]]}}" data-event-params="{{({index})}}" bindchange="__e" class="data-v-145b735b"><view class="picker-item data-v-145b735b">{{item.$orig.startDate||'请选择入学时间'}}</view></picker></view><view class="date-separator data-v-145b735b"></view><view class="date-item data-v-145b735b"><text class="input-label data-v-145b735b">毕业时间</text><picker style="{{(isFocused?'border-color:'+item.m39+';box-shadow:0 0 0 3rpx rgba('+item.m40+',0.1)':'')}}" mode="date" start="{{startDate}}" end="{{endDate}}" data-event-opts="{{[['change',[['e2',['$event']]]]]}}" data-event-params="{{({index})}}" bindchange="__e" class="data-v-145b735b"><view class="picker-item data-v-145b735b">{{item.$orig.endDate||'请选择毕业时间'}}</view></picker></view></view></view></view></block></block></view></scroll-view></swiper-item><swiper-item class="data-v-145b735b"><scroll-view class="step-content data-v-145b735b" scroll-y="{{true}}"><view class="section work-exp data-v-145b735b"><view class="section-header data-v-145b735b"><text class="section-title data-v-145b735b"><view class="title-bar data-v-145b735b" style="{{('background:linear-gradient(to bottom,'+$root.m41+' 0%,rgba('+$root.m42+',0.8) 100%)')}}"></view>工作经历</text><view data-event-opts="{{[['tap',[['addWork',['$event']]]]]}}" class="add-btn data-v-145b735b" style="{{('color:'+$root.m43+';background:rgba('+$root.m44+',0.1);'+(isFocused?'border-color:'+$root.m45+';box-shadow:0 0 0 3rpx rgba('+$root.m46+',0.1)':''))}}" bindtap="__e">添加工作经历</view></view><block wx:if="{{$root.g1===0}}"><view class="empty-tip data-v-145b735b"><image class="empty-icon data-v-145b735b" src="/static/images/empty.png" mode="aspectFit"></image><text class="data-v-145b735b">暂无工作经历，点击上方添加</text></view></block><block wx:else><block wx:for="{{$root.l2}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="work-item data-v-145b735b"><view class="item-header data-v-145b735b"><text class="item-index data-v-145b735b" style="{{('background:linear-gradient(135deg,'+item.m47+' 0%,rgba('+item.m48+',0.8) 100%);box-shadow:0 4rpx 12rpx rgba('+item.m49+',0.2);'+(isFocused?'border-color:'+item.m50+';box-shadow:0 0 0 3rpx rgba('+item.m51+',0.1)':''))}}">{{''+(index+1)+''}}</text><view data-event-opts="{{[['tap',[['deleteWork',[index]]]]]}}" class="delete-btn data-v-145b735b" bindtap="__e"><text class="delete-icon data-v-145b735b">×</text><text class="data-v-145b735b">删除</text></view></view><view class="form-group data-v-145b735b"><view class="input-wrapper data-v-145b735b"><text class="input-label data-v-145b735b">公司名称</text><input class="input-item data-v-145b735b" style="{{(isFocused?'border-color:'+item.m52+';box-shadow:0 0 0 3rpx rgba('+item.m53+',0.1)':'')}}" placeholder="请输入公司名称" data-event-opts="{{[['input',[['__set_model',['$0','company','$event',[]],[[['formData.workExperience','',index]]]]]]]}}" value="{{item.$orig.company}}" bindinput="__e"/></view><view class="input-wrapper data-v-145b735b"><text class="input-label data-v-145b735b">职位</text><input class="input-item data-v-145b735b" style="{{(isFocused?'border-color:'+item.m54+';box-shadow:0 0 0 3rpx rgba('+item.m55+',0.1)':'')}}" placeholder="请输入职位名称" data-event-opts="{{[['input',[['__set_model',['$0','position','$event',[]],[[['formData.workExperience','',index]]]]]]]}}" value="{{item.$orig.position}}" bindinput="__e"/></view><view class="date-range data-v-145b735b"><view class="date-item data-v-145b735b"><text class="input-label data-v-145b735b">入职时间</text><picker style="{{(isFocused?'border-color:'+item.m56+';box-shadow:0 0 0 3rpx rgba('+item.m57+',0.1)':'')}}" mode="date" start="{{startDate}}" end="{{endDate}}" data-event-opts="{{[['change',[['e3',['$event']]]]]}}" data-event-params="{{({index})}}" bindchange="__e" class="data-v-145b735b"><view class="picker-item data-v-145b735b">{{item.$orig.startDate||'请选择入职时间'}}</view></picker></view><view class="date-separator data-v-145b735b"></view><view class="date-item data-v-145b735b"><text class="input-label data-v-145b735b">离职时间</text><picker style="{{(isFocused?'border-color:'+item.m58+';box-shadow:0 0 0 3rpx rgba('+item.m59+',0.1)':'')}}" mode="date" start="{{startDate}}" end="{{endDate}}" data-event-opts="{{[['change',[['e4',['$event']]]]]}}" data-event-params="{{({index})}}" bindchange="__e" class="data-v-145b735b"><view class="picker-item data-v-145b735b">{{item.$orig.endDate||'请选择离职时间'}}</view></picker></view></view><view class="input-wrapper data-v-145b735b"><text class="input-label data-v-145b735b">工作描述</text><textarea class="textarea-item data-v-145b735b" style="{{(isFocused?'border-color:'+item.m60+';box-shadow:0 0 0 3rpx rgba('+item.m61+',0.1)':'')}}" placeholder="请描述您的工作职责和成果" maxlength="{{500}}" show-count="{{true}}" data-event-opts="{{[['input',[['__set_model',['$0','description','$event',[]],[[['formData.workExperience','',index]]]]]]]}}" value="{{item.$orig.description}}" bindinput="__e"></textarea></view></view></view></block></block></view></scroll-view></swiper-item><swiper-item class="data-v-145b735b"><scroll-view class="step-content data-v-145b735b" scroll-y="{{true}}"><view class="section job-intention data-v-145b735b"><view class="section-header data-v-145b735b"><text class="section-title data-v-145b735b"><view class="title-bar data-v-145b735b" style="{{('background:linear-gradient(to bottom,'+$root.m62+' 0%,rgba('+$root.m63+',0.8) 100%)')}}"></view>求职意向</text></view><view class="form-group data-v-145b735b"><view class="input-wrapper data-v-145b735b"><text class="input-label data-v-145b735b">期望职位</text><input class="input-item data-v-145b735b" style="{{(isFocused?'border-color:'+$root.m64+';box-shadow:0 0 0 3rpx rgba('+$root.m65+',0.1)':'')}}" placeholder="请输入期望职位" data-event-opts="{{[['input',[['__set_model',['$0','expectedPosition','$event',[]],['formData']]]]]}}" value="{{formData.expectedPosition}}" bindinput="__e"/></view><view class="input-wrapper data-v-145b735b"><text class="input-label data-v-145b735b">期望薪资</text><view class="salary-input data-v-145b735b"><input class="input-item data-v-145b735b" style="{{(isFocused?'border-color:'+$root.m66+';box-shadow:0 0 0 3rpx rgba('+$root.m67+',0.1)':'')}}" type="digit" placeholder="请输入期望薪资" data-event-opts="{{[['input',[['__set_model',['$0','expectedSalary','$event',[]],['formData']]]]]}}" value="{{formData.expectedSalary}}" bindinput="__e"/><text class="salary-unit data-v-145b735b">元/月</text></view></view><view class="input-wrapper data-v-145b735b"><text class="input-label data-v-145b735b">期望城市</text><picker class="input-item data-v-145b735b" style="{{(isFocused?'border-color:'+$root.m68+';box-shadow:0 0 0 3rpx rgba('+$root.m69+',0.1)':'')}}" mode="selector" range="{{cityList}}" data-event-opts="{{[['change',[['onCityChange',['$event']]]]]}}" bindchange="__e"><view class="picker-item data-v-145b735b">{{''+(formData.expectedCity||'请选择期望城市')+''}}</view></picker></view><view class="input-wrapper data-v-145b735b"><text class="input-label data-v-145b735b">到岗时间</text><picker class="input-item data-v-145b735b" style="{{(isFocused?'border-color:'+$root.m70+';box-shadow:0 0 0 3rpx rgba('+$root.m71+',0.1)':'')}}" mode="selector" range="{{arrivalTimeOptions}}" data-event-opts="{{[['change',[['onArrivalTimeChange',['$event']]]]]}}" bindchange="__e"><view class="picker-item data-v-145b735b">{{''+(formData.arrivalTime||'请选择到岗时间')+''}}</view></picker></view><view class="input-wrapper data-v-145b735b"><text class="input-label data-v-145b735b">工作类型</text><view class="job-type-group data-v-145b735b"><block wx:for="{{$root.l3}}" wx:for-item="type" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['selectJobType',['$0'],[[['jobTypes','',index]]]]]]]}}" class="{{['job-type-item','data-v-145b735b',(formData.jobType===type.$orig)?'active':'']}}" style="{{(formData.jobType===type.$orig?'background:rgba('+type.m72+',0.1);color:'+type.m73:'')}}" bindtap="__e">{{''+type.$orig+''}}</view></block></view></view><view class="input-wrapper data-v-145b735b"><text class="input-label data-v-145b735b">补充说明</text><textarea class="textarea-item data-v-145b735b" style="{{(isFocused?'border-color:'+$root.m74+';box-shadow:0 0 0 3rpx rgba('+$root.m75+',0.1)':'')}}" placeholder="请补充说明您的其他期望（如：行业偏好、公司规模等）" maxlength="{{200}}" show-count="{{true}}" data-event-opts="{{[['input',[['__set_model',['$0','additionalInfo','$event',[]],['formData']]]]]}}" value="{{formData.additionalInfo}}" bindinput="__e"></textarea></view></view></view></scroll-view></swiper-item></swiper><view class="footer safe-area-bottom data-v-145b735b"><view class="btn-group data-v-145b735b"><block wx:if="{{currentStep>0}}"><button data-event-opts="{{[['tap',[['prevStep',['$event']]]]]}}" class="nav-btn prev-btn data-v-145b735b" bindtap="__e">上一步</button></block><block wx:if="{{currentStep<$root.g2-1}}"><button class="nav-btn next-btn data-v-145b735b" style="{{('background:linear-gradient(135deg,'+$root.m76+' 0%,rgba('+$root.m77+',0.8) 100%)')}}" disabled="{{!canGoNext}}" data-event-opts="{{[['tap',[['nextStep',['$event']]]]]}}" bindtap="__e">下一步</button></block><block wx:if="{{currentStep===$root.g3-1}}"><button class="submit-btn data-v-145b735b" style="{{('background:linear-gradient(135deg,'+$root.m78+' 0%,rgba('+$root.m79+',0.8) 100%)')}}" disabled="{{isSubmitting}}" data-event-opts="{{[['tap',[['submitResume',['$event']]]]]}}" bindtap="__e"><text class="btn-text data-v-145b735b">{{isSubmitting?'保存中...':'保存简历'}}</text></button></block></view></view></view>