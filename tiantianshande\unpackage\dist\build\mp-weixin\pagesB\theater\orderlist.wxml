<view class="container"><block wx:if="{{isload}}"><block><dd-tab vue-id="f0c1548a-1" itemdata="{{['全部','待付款','已支付','已完成','已退款']}}" itemst="{{['-1','0','1','2','3']}}" st="{{st}}" isfixed="{{true}}" data-event-opts="{{[['^changetab',[['changetab']]]]}}" bind:changetab="__e" bind:__l="__l"></dd-tab><view style="width:100%;height:100rpx;"></view><view class="topsearch flex-y-center"><view class="f1 flex-y-center"><image class="img" src="/static/img/search_ico.png"></image><input placeholder="输入关键字搜索" placeholder-style="font-size:24rpx;color:#C2C2C2" data-event-opts="{{[['confirm',[['searchConfirm',['$event']]]]]}}" value="{{keyword}}" bindconfirm="__e"/></view></view><view class="order-content"><block><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="ll"><view data-event-opts="{{[['tap',[['goDetail',['$0'],[[['list','',index,'id']]]]]]]}}" bindtap="__e"><view class="ll-tit"><text style="font-size:34rpx;font-weight:bold;">{{item.$orig.episode?item.$orig.episode.title:''}}</text><block wx:if="{{item.$orig.status==2}}"><text>已完成</text></block><block wx:else><block wx:if="{{item.$orig.status==3}}"><text>已退款</text></block><block wx:else><block wx:if="{{item.$orig.status==1}}"><text>已支付</text></block><block wx:else><text>未支付</text></block></block></block></view><view class="ll-txt"><text>{{"总价:"+item.$orig.totalprice+"元"}}</text></view><view class="ll-txt"><text>{{(item.$orig.order_detail.list?item.g0:'1')+"个座位"}}</text></view><view class="ll-txt"><text>{{"开始时间:"+item.$orig.order_detail.title}}</text></view></view><view class="line"></view><view class="ll-btn"><view data-event-opts="{{[['tap',[['goDetail',['$0'],[[['list','',index,'id']]]]]]]}}" class="btn-1" bindtap="__e">详情</view><block wx:if="{{item.$orig.status==0}}"><view data-event-opts="{{[['tap',[['goPay',['$0'],[[['list','',index,'payorder_id']]]]]]]}}" class="btn-2" style="{{('background:'+primary_color)}}" bindtap="__e">去支付</view></block><block wx:if="{{item.$orig.status==1}}"><view class="btn-2" style="{{('background:'+primary_color)}}" data-hexiao_qr="{{item.$orig.hexiao_qr}}" data-event-opts="{{[['tap',[['showhxqr',['$event']]]]]}}" catchtap="__e">核销码</view></block></view></view></block></block></view><block wx:if="{{nomore}}"><nomore vue-id="f0c1548a-2" bind:__l="__l"></nomore></block><block wx:if="{{nodata}}"><nodata vue-id="f0c1548a-3" bind:__l="__l"></nodata></block><uni-popup class="vue-ref" vue-id="f0c1548a-4" id="dialogHxqr" type="dialog" data-ref="dialogHxqr" bind:__l="__l" vue-slots="{{['default']}}"><view class="hxqrbox"><image class="img" src="{{hexiao_qr}}" data-url="{{hexiao_qr}}" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image><view class="txt">请出示核销码给核销员进行核销</view><view data-event-opts="{{[['tap',[['closeHxqr',['$event']]]]]}}" class="close" bindtap="__e"><image style="width:100%;height:100%;" src="/static/img/close2.png"></image></view></view></uni-popup><uni-popup class="vue-ref" vue-id="f0c1548a-5" id="dialogSelectExpress" type="dialog" data-ref="dialogSelectExpress" bind:__l="__l" vue-slots="{{['default']}}"><block wx:if="{{express_content}}"><view style="background:#fff;padding:20rpx 30rpx;border-radius:10rpx;width:600rpx;"><block wx:for="{{express_content}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="sendexpress" style="border-bottom:1px solid #f5f5f5;padding:20rpx 0;"><view class="sendexpress-item" style="display:flex;" data-url="{{'/pagesExt/order/logistics?express_com='+item.express_com+'&express_no='+item.express_no}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="flex1" style="color:#121212;">{{item.express_com+" - "+item.express_no}}</view><image style="width:30rpx;height:30rpx;" src="/static/img/arrowright.png"></image></view><block wx:if="{{item.express_oglist}}"><view style="margin-top:20rpx;"><block wx:for="{{item.express_oglist}}" wx:for-item="item2" wx:for-index="index2" wx:key="index2"><view class="oginfo-item" style="display:flex;align-items:center;margin-bottom:10rpx;"><image style="width:50rpx;height:50rpx;margin-right:10rpx;flex-shrink:0;" src="{{item2.pic}}"></image><view class="flex1" style="color:#555;">{{item2.name+"("+item2.ggname+")"}}</view></view></block></view></block></view></block></view></block></uni-popup></block></block><block wx:if="{{loading}}"><loading vue-id="f0c1548a-6" bind:__l="__l"></loading></block><dp-tabbar vue-id="f0c1548a-7" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="f0c1548a-8" data-ref="popmsg" bind:__l="__l"></popmsg></view>