<view class="container"><block wx:if="{{isload}}"><block><view class="orderinfos"><block wx:if="{{coupon.id}}"><block><view class="item flex-col"><view class="t2"><view class="guize_txt" style="margin-bottom:1rem;"><parse vue-id="a53f0c0e-1" content="{{content}}" bind:__l="__l"></parse></view></view></view></block></block></view><block wx:if="{{mid==record.mid}}"><block><block wx:if="{{coupon.isgive==1||coupon.isgive==0||coupon.isgive==2&&record.from_mid}}"><block><block wx:if="{{record.id&&record.status==2}}"><block><block wx:if="{{coupon.payment==2}}"><view data-event-opts="{{[['tap',[['gotouse',['$event']]]]]}}" class="btn-add" style="{{'background:'+('linear-gradient(90deg,'+$root.m0+' 0%,rgba('+$root.m1+',0.8) 100%)')+';'}}" catchtap="__e">去使用</view></block><block wx:else><view class="btn-add" style="{{'background:'+('linear-gradient(90deg,'+$root.m2+' 0%,rgba('+$root.m3+',0.8) 100%)')+';'}}" data-id="{{record.id}}" data-event-opts="{{[['tap',[['receiveCoupon',['$event']]]]]}}" bindtap="__e">去使用</view></block></block></block><block wx:else><block><view class="btn-add" style="{{'background:'+('linear-gradient(90deg,'+$root.m4+' 0%,rgba('+$root.m5+',0.8) 100%)')+';'}}" data-id="{{coupon.id}}">已使用</view></block></block></block></block><block wx:if="{{record.id&&record.status==2&&(coupon.isgive==1||coupon.isgive==2)}}"><block><block wx:if="{{$root.m6=='app'}}"><view class="btn-add" style="{{'background:'+('linear-gradient(90deg,'+$root.m7+' 0%,rgba('+$root.m8+',0.8) 100%)')+';'}}" data-id="{{record.id}}" data-event-opts="{{[['tap',[['shareapp',['$event']]]]]}}" bindtap="__e">转赠好友</view></block><block wx:else><block wx:if="{{$root.m9=='mp'}}"><view class="btn-add" style="{{'background:'+('linear-gradient(90deg,'+$root.m10+' 0%,rgba('+$root.m11+',0.8) 100%)')+';'}}" data-id="{{record.id}}" data-event-opts="{{[['tap',[['sharemp',['$event']]]]]}}" bindtap="__e">转赠好友</view></block><block wx:else><block wx:if="{{$root.m12=='h5'}}"><view class="btn-add" style="{{'background:'+('linear-gradient(90deg,'+$root.m13+' 0%,rgba('+$root.m14+',0.8) 100%)')+';'}}" data-id="{{record.id}}" data-event-opts="{{[['tap',[['sharemp',['$event']]]]]}}" bindtap="__e">转赠好友</view></block><block wx:else><button class="btn-add" style="{{'background:'+('linear-gradient(90deg,'+$root.m15+' 0%,rgba('+$root.m16+',0.8) 100%)')+';'}}" open-type="share" data-id="{{record.id}}">转赠好友</button></block></block></block></block></block></block></block><block wx:else><block><block wx:if="{{record.status==1}}"><view class="btn-add" style="background:#9d9d9d;">已使用</view></block><block wx:else><block wx:if="{{record.status==2&&isrec==1}}"><view class="btn-add" style="background:#9d9d9d;">已抢光</view></block><block wx:else><view class="btn-add" style="{{'background:'+('linear-gradient(90deg,'+$root.m17+' 0%,rgba('+$root.m18+',0.8) 100%)')+';'}}" data-id="{{record.id}}" data-send="{{send}}" data-isrec="{{isrec}}" data-event-opts="{{[['tap',[['getcoupon',['$event']]]]]}}" bindtap="__e">立即领取</view></block></block></block></block><view class="text-center" style="margin-top:40rpx;line-height:60rpx;" data-url="/pagesExt/coupons/couponlist" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><text>返回</text></view></block></block><block wx:if="{{shareshow}}"><view data-event-opts="{{[['tap',[['remove',[0]]]]]}}" bindtap="__e"><view class="cpt-mask"><image style="width:100%;" src="/static/img/sharebg.png"></image></view></view></block><block wx:if="{{loading}}"><loading vue-id="a53f0c0e-2" bind:__l="__l"></loading></block><dp-tabbar vue-id="a53f0c0e-3" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="a53f0c0e-4" data-ref="popmsg" bind:__l="__l"></popmsg></view>