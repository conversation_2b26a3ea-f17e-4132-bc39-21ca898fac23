<view class="container"><block wx:if="{{isload}}"><block><view class="search-container" data-url="{{'/shopPackage/shop/search?bid='+bid}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e"><view class="search-box"><image class="img" src="/static/img/search_ico.png"></image><view class="search-text">搜索感兴趣的商品</view></view></view><view class="order-tab"><view class="order-tab2"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><view class="{{['item '+(curTopIndex==index?'on':'')]}}" data-index="{{index}}" data-id="{{item.$orig.id}}" data-event-opts="{{[['tap',[['switchTopTab',['$event']]]]]}}" bindtap="__e">{{item.$orig.name}}<view class="after" style="{{'background:'+(item.m0)+';'}}"></view></view></block></block></view></view><view class="content-container"><view class="nav_left"><view class="{{['nav_left_items '+(curIndex==-1?'active':'')]}}" style="{{'color:'+(curIndex==-1?$root.m1:'#333')+';'}}" data-index="-1" data-id="{{clist[curTopIndex].id}}" data-event-opts="{{[['tap',[['switchRightTab',['$event']]]]]}}" bindtap="__e"><view class="before" style="{{'background:'+($root.m2)+';'}}"></view>全部</view><block wx:for="{{$root.l1}}" wx:for-item="item" wx:for-index="index2" wx:key="index2"><block><view class="{{['nav_left_items '+(curIndex==index2?'active':'')]}}" style="{{'color:'+(curIndex==index2?item.m3:'#333')+';'}}" data-index="{{index2}}" data-id="{{item.$orig.id}}" data-event-opts="{{[['tap',[['switchRightTab',['$event']]]]]}}" bindtap="__e"><view class="before" style="{{'background:'+(item.m4)+';'}}"></view>{{item.$orig.name}}</view></block></block></view><view class="nav_right"><view class="nav_right-content"><view class="nav-pai"><view class="nav-paili" style="{{'color:'+(!field||field=='sort'?$root.m5:'#323232')+';'}}" data-field="sort" data-order="desc" data-event-opts="{{[['tap',[['changeOrder',['$event']]]]]}}" bindtap="__e">综合</view><view class="nav-paili" style="{{(field=='sales'?'color:'+$root.m6:'')}}" data-field="sales" data-order="desc" data-event-opts="{{[['tap',[['changeOrder',['$event']]]]]}}" bindtap="__e">销量</view><view class="nav-paili" data-field="sell_price" data-order="{{order=='asc'?'desc':'asc'}}" data-event-opts="{{[['tap',[['changeOrder',['$event']]]]]}}" bindtap="__e"><text style="{{(field=='sell_price'?'color:'+$root.m7:'')}}">价格</text><text class="iconfont iconshangla" style="{{(field=='sell_price'&&order=='asc'?'color:'+$root.m8:'')}}"></text><text class="iconfont icondaoxu" style="{{(field=='sell_price'&&order=='desc'?'color:'+$root.m9:'')}}"></text></view></view><block wx:if="{{$root.g0}}"><view class="classify-ul"><view class="flex" style="width:100%;overflow-y:hidden;overflow-x:scroll;"><view class="classify-li" style="{{(curIndex2==-1?'color:'+$root.m10+';background:rgba('+$root.m11+',0.2)':'')}}" data-id="{{clist[curTopIndex].child[curIndex].id}}" data-index="-1" data-event-opts="{{[['tap',[['changeCTab',['$event']]]]]}}" bindtap="__e">全部</view><block wx:for="{{$root.l2}}" wx:for-item="item" wx:for-index="idx2" wx:key="idx2"><block><view class="classify-li" style="{{(curIndex2==idx2?'color:'+item.m12+';background:rgba('+item.m13+',0.2)':'')}}" data-id="{{item.$orig.id}}" data-index="{{idx2}}" data-event-opts="{{[['tap',[['changeCTab',['$event']]]]]}}" bindtap="__e">{{item.$orig.name}}</view></block></block></view></view></block><scroll-view class="classify-box" scroll-y="true" data-event-opts="{{[['scrolltolower',[['scrolltolower',['$event']]]]]}}" bindscrolltolower="__e"><view class="product-itemlist"><block wx:for="{{$root.l3}}" wx:for-item="item" wx:for-index="index" wx:key="id"><view class="item" data-url="{{'/shopPackage/shop/product?id='+item.$orig.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="product-pic"><image class="image" src="{{item.$orig.pic}}" mode="widthFix"></image></view><view class="product-info"><view class="p1"><text>{{item.$orig.name}}</text></view><block wx:if="{{item.$orig.price_type!=1||item.$orig.sell_price>0}}"><view class="p2"><text class="t1" style="{{'color:'+(item.m14)+';'}}"><text style="font-size:20rpx;padding-right:1px;">￥</text>{{item.$orig.sell_price}}</text><block wx:if="{{item.$orig.market_price*1>item.$orig.sell_price*1}}"><text class="t2">{{"￥"+item.$orig.market_price}}</text></block></view></block><block wx:if="{{item.$orig.xunjia_text&&item.$orig.price_type==1&&item.$orig.sell_price<=0}}"><view class="p2" style="height:50rpx;line-height:44rpx;"><text class="t1" style="{{'color:'+(item.m15)+';'+('font-size:'+('30rpx')+';')}}">询价</text><block wx:if="{{item.$orig.xunjia_text&&item.$orig.price_type==1}}"><block><view class="lianxi" style="{{'background:'+(item.m16)+';'}}" data-lx_name="{{item.$orig.lx_name}}" data-lx_bid="{{item.$orig.lx_bid}}" data-lx_bname="{{item.$orig.lx_bname}}" data-lx_tel="{{item.$orig.lx_tel}}" data-btntype="2" data-event-opts="{{[['tap',[['showLinkChange',['$event']]]]]}}" catchtap="__e">{{item.$orig.xunjia_text?item.$orig.xunjia_text:'联系TA'}}</view></block></block></view></block><block wx:if="{{item.$orig.merchant_name}}"><view class="p1" style="color:#666;font-size:24rpx;white-space:nowrap;text-overflow:ellipsis;margin-top:6rpx;height:30rpx;line-height:30rpx;font-weight:normal;"><text>{{item.$orig.merchant_name}}</text></view></block><block wx:if="{{item.$orig.main_business}}"><view class="p1" style="color:#666;font-size:24rpx;margin-top:4rpx;font-weight:normal;"><text>{{item.$orig.main_business}}</text></view></block><view class="p3"><block wx:if="{{item.$orig.sales>0}}"><view class="p3-1"><text style="overflow:hidden;">{{"已售"+item.$orig.sales+"件"}}</text></view></block></view><block wx:if="{{item.$orig.sales<=0&&item.$orig.merchant_name}}"><view style="height:44rpx;"></view></block><block wx:if="{{!item.$orig.price_type}}"><view class="p4" style="{{'background:'+('rgba('+item.m17+',0.1)')+';'+('color:'+(item.m18)+';')}}" data-proid="{{item.$orig.id}}" data-event-opts="{{[['tap',[['buydialogChange',['$event']]]]]}}" catchtap="__e"><text class="iconfont icon_gouwuche"></text></view></block></view></view></block></view><block wx:if="{{nomore}}"><nomore vue-id="f503726a-1" text="没有更多商品了" bind:__l="__l"></nomore></block><block wx:if="{{nodata}}"><nodata vue-id="f503726a-2" text="没有查找到相关商品" bind:__l="__l"></nodata></block></scroll-view></view></view></view></block></block><block wx:if="{{buydialogShow}}"><buydialog vue-id="f503726a-3" proid="{{proid}}" menuindex="{{menuindex}}" data-event-opts="{{[['^buydialogChange',[['buydialogChange']]]]}}" bind:buydialogChange="__e" bind:__l="__l"></buydialog></block><block wx:if="{{showLinkStatus}}"><view class="posterDialog linkDialog"><view class="main"><view data-event-opts="{{[['tap',[['showLinkChange',['$event']]]]]}}" class="close" bindtap="__e"><image class="img" src="/static/img/close.png"></image></view><view class="content"><view class="title">{{lx_name}}</view><block wx:if="{{lx_bid>0}}"><view class="row"><view class="f1">店铺名称</view><view class="f2" data-url="{{'/pagesExt/business/index?id='+lx_bid}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">{{lx_bname}}<image class="image" src="/static/img/arrowright.png"></image></view></view></block><block wx:if="{{lx_tel}}"><view class="row"><view class="f1">联系电话</view><view class="f2" style="{{'color:'+($root.m19)+';'}}" data-url="{{'tel::'+lx_tel}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">{{lx_tel}}<image class="copyicon" src="/static/img/copy.png" data-text="{{lx_tel}}" data-event-opts="{{[['tap',[['copy',['$event']]]]]}}" catchtap="__e"></image></view></view></block></view></view></view></block><block wx:if="{{loading}}"><loading vue-id="f503726a-4" bind:__l="__l"></loading></block><dp-tabbar vue-id="f503726a-5" opt="{{opt}}" data-event-opts="{{[['^getmenuindex',[['getmenuindex']]]]}}" bind:getmenuindex="__e" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="f503726a-6" data-ref="popmsg" bind:__l="__l"></popmsg></view>