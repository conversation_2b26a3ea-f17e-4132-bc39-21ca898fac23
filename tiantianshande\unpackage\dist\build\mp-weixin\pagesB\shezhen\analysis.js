require('../common/vendor.js');(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pagesB/shezhen/analysis"],{"12d3":function(s,e,a){"use strict";a.d(e,"b",(function(){return n})),a.d(e,"c",(function(){return i})),a.d(e,"a",(function(){}));var n=function(){var s=this,e=s.$createElement,a=(s._self._c,s.isAnalyzing?s.__map(16,(function(e,a){var n=s.__get_orig(e),i=s.scannedCells.includes(e);return{$orig:n,g0:i}})):null);s.$mp.data=Object.assign({},{$root:{l0:a}})},i=[]},"2aab":function(s,e,a){"use strict";a.r(e);var n=a("12d3"),i=a("f4e7");for(var o in i)["default"].indexOf(o)<0&&function(s){a.d(e,s,(function(){return i[s]}))}(o);a("85f0");var t=a("828b"),l=Object(t["a"])(i["default"],n["b"],n["c"],!1,null,"42035be2",null,!1,n["a"],void 0);e["default"]=l.exports},"519b":function(s,e,a){"use strict";(function(s){var n=a("47a9");Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var i=n(a("3b2d")),o=n(a("7ca3"));function t(s,e){var a=Object.keys(s);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(s);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(s,e).enumerable}))),a.push.apply(a,n)}return a}function l(s){for(var e=1;e<arguments.length;e++){var a=null!=arguments[e]?arguments[e]:{};e%2?t(Object(a),!0).forEach((function(e){(0,o.default)(s,e,a[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(s,Object.getOwnPropertyDescriptors(a)):t(Object(a)).forEach((function(e){Object.defineProperty(s,e,Object.getOwnPropertyDescriptor(a,e))}))}return s}var r={name:"ShezhenAnalysis",data:function(){return{imageUrl:"",isAnalyzing:!0,analysisComplete:!1,currentStep:0,analysisProgress:0,currentTaskIndex:0,scanPosition:{x:0,y:0},scannedCells:[],estimatedTime:15,analysisSteps:[{name:"图像识别",key:"recognition"},{name:"特征分析",key:"analysis"},{name:"生成报告",key:"report"}],analysisTasks:[{title:"正在识别舌象特征",description:"使用深度学习算法识别舌质、舌苔等基本特征...",duration:3e3},{title:"分析舌质颜色",description:"检测舌质的颜色分布，判断气血状况...",duration:2500},{title:"检测舌苔厚薄",description:"分析舌苔的厚薄程度和分布情况...",duration:2e3},{title:"识别舌体形态",description:"检测舌体的大小、形状和边缘特征...",duration:2500},{title:"综合中医理论分析",description:"结合传统中医舌诊理论进行综合分析...",duration:3e3},{title:"生成个性化报告",description:"基于分析结果生成详细的健康报告...",duration:2e3}],analysisRegions:[{name:"舌尖",x:35,y:20,width:30,height:25,progress:0,active:!1},{name:"舌中",x:30,y:40,width:40,height:20,progress:0,active:!1},{name:"舌根",x:25,y:65,width:50,height:20,progress:0,active:!1},{name:"舌边",x:15,y:30,width:15,height:40,progress:0,active:!1},{name:"舌边",x:70,y:30,width:15,height:40,progress:0,active:!1}],analysisMarkers:[{x:45,y:25,type:"normal",label:"舌尖正常"},{x:50,y:45,type:"attention",label:"舌苔偏厚"},{x:40,y:65,type:"normal",label:"舌根正常"},{x:25,y:40,type:"warning",label:"舌边有齿痕"},{x:75,y:40,type:"normal",label:"舌边正常"}],analysisLogs:[],recordId:"",configData:{},analysisApiCompleted:!1,orderNo:"",apiType:"",orderId:"",isInitialized:!1,isApiCalling:!1,isNavigatingToResult:!1}},computed:{currentTask:function(){return this.analysisTasks[this.currentTaskIndex]||this.analysisTasks[0]},formattedAnalysisProgress:function(){return Math.round(this.analysisProgress)}},onLoad:function(s){if(console.log("2025-01-26 12:00:00,001-INFO-[analysis][onLoad_001] 舌诊分析页面加载完成"),this.isInitialized)console.log("2025-01-27 12:00:00,001-WARN-[analysis][onLoad_001_duplicate] 检测到重复初始化，忽略");else{if(this.isInitialized=!0,s.image_url&&(this.imageUrl=decodeURIComponent(s.image_url),console.log("2025-01-26 12:00:00,002-INFO-[analysis][onLoad_002] 获取图片路径:",this.imageUrl)),s.config)try{this.configData=JSON.parse(decodeURIComponent(s.config)),console.log("2025-01-26 12:00:00,003-INFO-[analysis][onLoad_003] 获取配置信息:",this.configData)}catch(e){console.error("2025-01-26 12:00:00,004-ERROR-[analysis][onLoad_004] 配置信息解析失败:",e)}s.recordId&&(this.recordId=s.recordId,console.log("2025-01-26 12:00:00,005-INFO-[analysis][onLoad_005] 获取分析记录ID:",this.recordId)),s.order_id&&(this.orderId=s.order_id,console.log("2025-01-27 12:00:00,006-INFO-[analysis][onLoad_006] 获取订单ID:",this.orderId)),this.startRealAnalysis()}},methods:{startRealAnalysis:function(){console.log("2025-01-26 11:30:00,006-INFO-[analysis][startRealAnalysis_001] 开始真实的分析流程"),this.isAnalyzing=!0,this.analysisComplete=!1,this.currentStep=0,this.analysisProgress=0,this.currentTaskIndex=0,this.initAnalysisLogs(),this.startScanAnimation(),this.executeAnalysisTasks(),this.callAnalysisApi()},initAnalysisLogs:function(){var s=this;this.analysisLogs=this.analysisTasks.map((function(e,a){return{title:e.title,time:s.getCurrentTime(),status:0===a?"processing":"waiting"}}))},getCurrentTime:function(){var s=new Date;return"".concat(String(s.getHours()).padStart(2,"0"),":").concat(String(s.getMinutes()).padStart(2,"0"),":").concat(String(s.getSeconds()).padStart(2,"0"))},startScanAnimation:function(){var s=this,e=setInterval((function(){if(s.isAnalyzing){if(s.scanPosition.x=100*Math.random(),s.scanPosition.y=100*Math.random(),s.scannedCells.length<16){var a=Math.floor(16*Math.random())+1;s.scannedCells.includes(a)||s.scannedCells.push(a)}}else clearInterval(e)}),200)},executeAnalysisTasks:function(){this.executeTask(0)},executeTask:function(s){var e=this;if(s>=this.analysisTasks.length)this.completeAnalysis();else{console.log("2025-01-26 11:30:00,".concat(String(s+4).padStart(3,"0"),"-INFO-[analysis][executeTask_").concat(String(s+1).padStart(3,"0"),"] 执行任务: ").concat(this.analysisTasks[s].title)),this.currentTaskIndex=s,this.currentStep=s<2?0:s<4?1:2,this.analysisLogs[s].status="processing",this.analysisLogs[s].time=this.getCurrentTime(),this.$nextTick((function(){e.scrollToLatestLog()})),s<this.analysisRegions.length&&(this.analysisRegions[s].active=!0);var a=this.analysisTasks[s],n=setInterval((function(){var a;s<e.analysisRegions.length&&(e.analysisRegions[s].progress+=2,e.analysisRegions[s].progress>100&&(e.analysisRegions[s].progress=100));var n=s/e.analysisTasks.length*100,i=((null===(a=e.analysisRegions[s])||void 0===a?void 0:a.progress)||0)/e.analysisTasks.length;e.analysisProgress=Math.round(Math.min(n+i,100))}),50);setTimeout((function(){clearInterval(n),e.analysisLogs[s].status="completed",e.analysisLogs[s].time=e.getCurrentTime(),s<e.analysisRegions.length&&(e.analysisRegions[s].progress=100,e.analysisRegions[s].active=!1),setTimeout((function(){e.executeTask(s+1)}),500)}),a.duration)}},completeAnalysis:function(){console.log("2025-01-26 11:30:00,024-INFO-[analysis][completeAnalysis_001] 动画分析完成"),this.isAnalyzing=!1,this.analysisComplete=!0,this.analysisProgress=100,this.currentStep=3,this.scannedCells=[],this.checkCompletionAndJump()},navigateToResult:function(){var e=this;if(console.log("2025-01-26 11:30:00,030-INFO-[analysis][navigateToResult_001] 跳转到完成页面"),this.isNavigatingToResult)console.log("2025-01-27 11:30:00,030-WARN-[analysis][navigateToResult_001_duplicate] 检测到重复跳转，忽略");else{if(this.isNavigatingToResult=!0,!this.recordId)return console.error("2025-01-26 11:30:00,031-ERROR-[analysis][navigateToResult_002] 记录ID为空，无法跳转"),this.isNavigatingToResult=!1,void s.showToast({title:"分析结果异常，请重试",icon:"none"});var a={record_id:this.recordId,order_no:this.orderNo||"",api_type:this.apiType||"",order_id:this.orderId||""};console.log("2025-01-26 11:30:00,032-INFO-[analysis][navigateToResult_003] 跳转参数:",a),s.navigateTo({url:"/pagesB/shezhen/complete?record_id=".concat(a.record_id,"&order_no=").concat(a.order_no,"&api_type=").concat(a.api_type,"&order_id=").concat(a.order_id),success:function(){console.log("2025-01-26 11:30:00,033-INFO-[analysis][navigateToResult_004] 成功跳转到完成页面")},fail:function(a){console.error("2025-01-26 11:30:00,034-ERROR-[analysis][navigateToResult_005] 跳转完成页面失败:",a),e.isNavigatingToResult=!1,s.showToast({title:"页面跳转失败",icon:"none"})}})}},onImageError:function(e){console.error("2025-01-26 11:30:00,014-ERROR-[analysis][onImageError_001] 图片加载错误:",e),s.showModal({title:"图片加载失败",content:"无法加载分析图片，请返回重新拍摄",showCancel:!1,confirmText:"返回",success:function(){s.navigateBack()}})},scrollToLatestLog:function(){var e=this;try{console.log("2025-01-27 16:00:00,004-INFO-[analysis][scrollToLatestLog_001] 尝试滚动到最新日志"),setTimeout((function(){var a=s.createSelectorQuery().in(e);a.select(".logs-container").boundingClientRect((function(a){if(a&&a.height){console.log("2025-01-27 16:00:00,005-INFO-[analysis][scrollToLatestLog_002] 获取日志容器信息成功，高度:",a.height);var n=s.createSelectorQuery().in(e);n.select(".log-item:last-child").boundingClientRect((function(s){s?(console.log("2025-01-27 16:00:00,006-INFO-[analysis][scrollToLatestLog_003] 找到最后一个日志项，执行滚动"),n.select(".log-item:last-child").scrollIntoView({duration:300,success:function(){console.log("2025-01-27 16:00:00,007-INFO-[analysis][scrollToLatestLog_004] 滚动到最新日志成功")},fail:function(s){console.log("2025-01-27 16:00:00,008-WARN-[analysis][scrollToLatestLog_005] 滚动到最新日志失败:",s)}})):console.log("2025-01-27 16:00:00,009-WARN-[analysis][scrollToLatestLog_006] 未找到最后一个日志项")})),n.exec()}else console.log("2025-01-27 16:00:00,010-WARN-[analysis][scrollToLatestLog_007] 获取日志容器信息失败")})),a.exec()}),100)}catch(a){console.error("2025-01-27 16:00:00,011-ERROR-[analysis][scrollToLatestLog_008] 自动滚动失败:",a)}},callAnalysisApi:function(){var e=this;if(console.log("2025-01-26 11:30:00,007-INFO-[analysis][callAnalysisApi_001] 调用舌诊分析接口"),this.analysisApiCompleted||this.isApiCalling)console.log("2025-01-27 11:30:00,007-WARN-[analysis][callAnalysisApi_001_duplicate] 检测到重复API调用，忽略");else{this.isApiCalling=!0;var a=getApp(),n=0;this.configData&&this.configData.can_use_free&&(n=1);var o={image_url:this.imageUrl,use_free:n};this.orderId&&(o.order_id=this.orderId,console.log("2025-01-27 11:30:00,008-INFO-[analysis][callAnalysisApi_002] 使用已创建的订单ID:",this.orderId)),console.log("2025-01-27 15:00:00,002-INFO-[analysis][callAnalysisApi_002_2] 请求参数详情 - ",l(l({},o),{},{configData:this.configData?{is_free:this.configData.is_free,can_use_free:this.configData.can_use_free,price:this.configData.price}:null})),a.post("ApiSheZhen/analyze",o,(function(a){console.log("2025-01-26 11:30:00,009-INFO-[analysis][callAnalysisApi_003] 分析接口响应:",a),console.log("2025-01-26 11:30:00,009-INFO-[analysis][callAnalysisApi_003_detail] 响应详情 - status:",null===a||void 0===a?void 0:a.status,"code:",null===a||void 0===a?void 0:a.code,"msg:",null===a||void 0===a?void 0:a.msg,"data:",null===a||void 0===a?void 0:a.data);var n=a&&(1===a.status||"1"===a.status||200===a.status||"200"===a.status||1===a.code||"1"===a.code||200===a.code||"200"===a.code||!0===a.success||"true"===a.success||a.msg&&(a.msg.includes("成功")||a.msg.includes("完成")));if(n&&a.data){if(console.log("2025-01-26 11:30:00,010-INFO-[analysis][callAnalysisApi_004] 分析成功判断通过"),e.isApiCalling=!1,e.recordId=a.data.record_id||a.data.recordId||a.data.id,e.orderNo=a.data.order_no||a.data.orderNo||"",e.apiType=a.data.api_type||a.data.apiType||"",console.log("2025-01-26 11:30:00,011-INFO-[analysis][callAnalysisApi_005] 分析成功，记录ID:",e.recordId,"订单号:",e.orderNo,"API类型:",e.apiType),!e.recordId)return console.error("2025-01-26 11:30:00,012-ERROR-[analysis][callAnalysisApi_006] 分析成功但缺少记录ID"),void e.handleAnalysisError("分析成功但缺少必要数据，请重试",a);s.showToast({title:"分析完成",icon:"success",duration:1500}),e.analysisApiCompleted=!0,e.checkCompletionAndJump()}else console.error("2025-01-26 11:30:00,013-ERROR-[analysis][callAnalysisApi_007] 分析失败或响应格式错误"),console.error("2025-01-26 11:30:00,013-ERROR-[analysis][callAnalysisApi_007_detail] 失败原因分析 - response:",JSON.stringify(a)),e.isApiCalling=!1,a&&a.data&&"object"===(0,i.default)(a.data)&&(e.configData=l(l({},e.configData),a.data),console.log("2025-01-27 16:00:00,013-INFO-[analysis][callAnalysisApi_007_2] 保存错误响应中的配置数据:",e.configData)),e.handleAnalysisError((null===a||void 0===a?void 0:a.msg)||(null===a||void 0===a?void 0:a.message)||"分析失败，请重试",a)}),(function(s){e.isApiCalling=!1,console.error("2025-01-26 11:30:00,014-ERROR-[analysis][callAnalysisApi_008] 分析接口调用失败:",s),e.handleAnalysisError("网络连接失败，请检查网络后重试",s)}))}},handleAnalysisError:function(e,a){var n=this;if(console.log("2025-01-26 11:30:00,015-INFO-[analysis][handleAnalysisError_001] 处理分析错误:",e),this.isAnalyzing=!1,s.hideLoading(),e&&(e.includes("余额不足")||e.includes("充值")||e.includes("积分不足")||a&&0===a.code&&a.msg&&a.msg.includes("余额不足")))return console.log("2025-01-27 13:00:00,002-INFO-[analysis][handleAnalysisError_003] 检测到余额不足错误，显示支付确认弹窗"),void s.showModal({title:"余额不足",content:"您的账户余额不足，请充值后继续使用舌诊分析功能",confirmText:"去充值",cancelText:"返回",success:function(e){if(e.confirm){console.log("2025-01-27 13:00:00,003-INFO-[analysis][handleAnalysisError_004] 用户选择去充值");var a="/pagesExb/money/recharge";n.configData&&n.configData.recharge_url?(a=n.configData.recharge_url,console.log("2025-01-27 16:00:00,002-INFO-[analysis][handleAnalysisError_004_2] 使用配置中的充值路径:",a)):console.log("2025-01-27 16:00:00,003-INFO-[analysis][handleAnalysisError_004_3] 配置中无充值路径，使用默认路径:",a),s.navigateTo({url:a,success:function(){console.log("2025-01-27 13:00:00,004-INFO-[analysis][handleAnalysisError_005] 成功跳转到充值页面")},fail:function(e){console.error("2025-01-27 13:00:00,005-ERROR-[analysis][handleAnalysisError_006] 跳转充值页面失败:",e),s.showToast({title:"跳转失败，请手动前往充值页面",icon:"none",duration:2e3}),setTimeout((function(){s.switchTab({url:"/pages/index/index",fail:function(){s.navigateBack()}})}),2e3)}})}else console.log("2025-01-27 13:00:00,006-INFO-[analysis][handleAnalysisError_007] 用户选择返回"),s.navigateBack({delta:1})}});s.showModal({title:"分析失败",content:e,showCancel:!0,cancelText:"重试",confirmText:"返回",success:function(e){e.confirm?(console.log("2025-01-26 11:30:00,016-INFO-[analysis][handleAnalysisError_002] 用户选择返回拍摄页面"),s.navigateBack()):(console.log("2025-01-26 11:30:00,017-INFO-[analysis][handleAnalysisError_003] 用户选择重试分析"),n.startRealAnalysis())}})},checkCompletionAndJump:function(){var e=this;if(console.log("2025-01-26 11:30:00,018-INFO-[analysis][checkCompletionAndJump_001] 检查分析完成并跳转"),console.log("2025-01-26 11:30:00,018-INFO-[analysis][checkCompletionAndJump_001_detail] API完成状态:",this.analysisApiCompleted,"动画完成状态:",this.analysisComplete,"记录ID:",this.recordId),this.analysisApiCompleted){if(console.log("2025-01-26 11:30:00,019-INFO-[analysis][checkCompletionAndJump_002] API已完成"),!this.recordId)return console.error("2025-01-26 11:30:00,020-ERROR-[analysis][checkCompletionAndJump_003] 缺少记录ID，无法跳转"),void this.handleAnalysisError("分析数据异常，缺少记录ID",null);this.analysisComplete?(console.log("2025-01-26 11:30:00,021-INFO-[analysis][checkCompletionAndJump_004] 动画也完成，立即跳转"),s.hideLoading(),setTimeout((function(){e.navigateToResult()}),500)):(console.log("2025-01-26 11:30:00,022-INFO-[analysis][checkCompletionAndJump_005] 等待动画完成"),s.showLoading({title:"即将完成...",mask:!0}))}else console.log("2025-01-26 11:30:00,023-INFO-[analysis][checkCompletionAndJump_006] API未完成，继续等待")}}};e.default=r}).call(this,a("df3c")["default"])},"854f":function(s,e,a){},"85f0":function(s,e,a){"use strict";var n=a("854f"),i=a.n(n);i.a},df4e:function(s,e,a){"use strict";(function(s,e){var n=a("47a9");a("06e9");n(a("3240"));var i=n(a("2aab"));s.__webpack_require_UNI_MP_PLUGIN__=a,e(i.default)}).call(this,a("3223")["default"],a("df3c")["createPage"])},f4e7:function(s,e,a){"use strict";a.r(e);var n=a("519b"),i=a.n(n);for(var o in n)["default"].indexOf(o)<0&&function(s){a.d(e,s,(function(){return n[s]}))}(o);e["default"]=i.a}},[["df4e","common/runtime","common/vendor"]]]);