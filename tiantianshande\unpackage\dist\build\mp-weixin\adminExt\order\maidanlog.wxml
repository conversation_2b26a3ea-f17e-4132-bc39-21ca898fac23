<view><block wx:if="{{isload}}"><block><view class="topsearch flex-y-center"><view class="f1 flex-y-center"><image class="img" src="{{pre_url+'/static/img/search_ico.png'}}"></image><input placeholder="输入用户昵称或订单号搜索" placeholder-style="font-size:24rpx;color:#C2C2C2" data-event-opts="{{[['confirm',[['searchConfirm',['$event']]]]]}}" value="{{keyword}}" bindconfirm="__e"/></view></view><block wx:for="{{$root.l1}}" wx:for-item="dateitem" wx:for-index="index" wx:key="index"><block wx:if="{{dateitem.g0}}"><block><view class="content"><view class="label"><text class="t1">{{dateitem.$orig.date}}</text></view><block wx:for="{{dateitem.l0}}" wx:for-item="item" wx:for-index="index1" wx:key="index1"><view class="item" data-url="{{'maidandetail?id='+item.$orig.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="left"><view class="f2"><view class="t2">{{"支付会员："+item.$orig.nickname}}</view><text class="t2">{{"支付时间："+item.$orig.paytime}}</text><text class="t2">{{"支付方式："+item.$orig.paytype}}</text><text class="t2">{{"订单编号："+item.$orig.ordernum}}</text><block wx:if="{{item.$orig.paynum}}"><text class="t2">{{"支付单号："+item.$orig.paynum}}</text></block></view></view><view class="right"><view><view class="money" style="{{'color:'+(item.m0)+';'}}">{{"￥"+item.$orig.money}}</view><block wx:if="{{canrefund&&item.$orig.refund_money>0}}"><view class="refund-money">{{"已退:￥"+item.$orig.refund_money}}</view></block></view><image class="more" src="{{pre_url+'/static/img/arrowright.png'}}"></image></view></view></block></view></block></block></block><block wx:if="{{nodata}}"><nodata vue-id="b1f3b5e4-1" bind:__l="__l"></nodata></block><block wx:if="{{nomore}}"><nomore vue-id="b1f3b5e4-2" bind:__l="__l"></nomore></block></block></block><block wx:if="{{loading}}"><loading vue-id="b1f3b5e4-3" bind:__l="__l"></loading></block><popmsg class="vue-ref" vue-id="b1f3b5e4-4" data-ref="popmsg" bind:__l="__l"></popmsg></view>