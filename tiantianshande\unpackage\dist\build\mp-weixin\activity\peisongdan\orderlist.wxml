<view class="container"><block wx:if="{{isload}}"><block><view><view class="search-container"><view class="search-box"><image class="img" src="/static/img/search_ico.png"></image><input class="search-text" placeholder="搜索商家" placeholder-style="color:#aaa;font-size:24rpx" data-event-opts="{{[['confirm',[['searchConfirm',['$event']]]]]}}" bindconfirm="__e"/></view></view><block wx:if="{{st!=='4'}}"><view><view class="order-box"><view class="oop"><view data-event-opts="{{[['tap',[['yesterdays']]]]}}" class="{{['btn',(is_day===0)?'active':'']}}" bindtap="__e">昨日下单</view><view data-event-opts="{{[['tap',[['todays']]]]}}" class="{{['btn',(is_day===1)?'active':'']}}" bindtap="__e">今日下单</view><view data-event-opts="{{[['tap',[['allOrders']]]]}}" class="{{['btn',(is_day===-1)?'active':'']}}" bindtap="__e">全部订单</view></view></view></view></block><checkbox-group data-event-opts="{{[['change',[['handleChange',['$event']]]]]}}" bindchange="__e"><view class="select-all"><checkbox value="all" checked="{{isAllSelected}}">全选</checkbox></view><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="id"><block><view class="order-box"><view class="head"><checkbox value="{{item.$orig.id+''}}" checked="{{item.g0}}"></checkbox><block wx:if="{{item.$orig.status==4}}"><view class="f1"><image class="img" src="/static/peisong/ps_time.png"></image>已送达</view></block><block wx:else><block wx:if="{{item.$orig.leftminute>0}}"><view class="f1"><image class="img" src="/static/peisong/ps_time.png"></image><text class="t1">{{item.$orig.leftminute+"分钟内"}}</text>送达</view></block><block wx:else><view class="f1"><image class="img" src="/static/peisong/ps_time.png"></image>已超时<text class="t1" style="margin-left:10rpx;">{{-item.$orig.leftminute+"分钟"}}</text></view></block></block><view class="flex1"></view><view class="f2"><text class="t1">{{item.$orig.ticheng}}</text>元</view></view><view class="content" data-url="{{'orderdetail?id='+item.$orig.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="f2"><view class="t3">{{item.$orig.orderinfo.address}}</view><view class="t2">{{item.$orig.orderinfo.area}}</view></view><view class="f3" data-index="{{index}}" data-event-opts="{{[['tap',[['daohang',['$event']]]]]}}" catchtap="__e"><image class="img" src="{{pre_url+'/static/img/peisong/ps_daohang.png'}}"></image></view></view><block wx:if="{{st!=='4'}}"><view data-url="{{'orderdetail?id='+item.$orig.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><block wx:for="{{item.$orig.prolist}}" wx:for-item="items" wx:for-index="index" wx:key="id"><view class="oop"><view class="t1">{{items.name+''}}<label style="color:red;" class="_span">{{"X"+items.num}}</label></view></view></block></view></block><view class="op"><block wx:if="{{item.$orig.status==1}}"><view class="t1">已接单，正在赶往商家</view></block><block wx:if="{{item.$orig.status==2}}"><view class="t1">已到店，等待取货</view></block><block wx:if="{{item.$orig.status==3}}"><view class="t1">已取货，正在配送中</view></block><block wx:if="{{item.$orig.status==4}}"><view class="t1">已送达</view></block><view class="flex1"></view><block wx:if="{{item.$orig.status==1}}"><view class="btn1" data-id="{{item.$orig.id}}" data-st="2" data-event-opts="{{[['tap',[['setst',['$event']]]]]}}" catchtap="__e">我已到店</view></block><block wx:if="{{item.$orig.status==2}}"><view class="btn1" data-id="{{item.$orig.id}}" data-st="3" data-event-opts="{{[['tap',[['setst',['$event']]]]]}}" catchtap="__e">我已取货</view></block><block wx:if="{{item.$orig.status==3}}"><view class="btn1" data-id="{{item.$orig.id}}" data-st="4" data-event-opts="{{[['tap',[['setst',['$event']]]]]}}" catchtap="__e">我已送达</view></block></view></view></block></block></checkbox-group><block wx:if="{{nomore}}"><nomore vue-id="76913cc2-1" bind:__l="__l"></nomore></block><block wx:if="{{nodata}}"><nodata vue-id="76913cc2-2" bind:__l="__l"></nodata></block></view><block wx:if="{{$root.g1>0}}"><view class="bottom-btns"><view class="btn-item"><view data-event-opts="{{[['tap',[['getline',['$event']]]]]}}" class="tb" bindtap="__e">规划路线</view></view><view class="btn-item"><view data-event-opts="{{[['tap',[['copyAddresses',['$event']]]]]}}" class="tb copy-btn" bindtap="__e">复制地址</view></view></view></block><view class="tabbar"><view class="tabbar-bot"></view><view class="tabbar-bar" style="background-color:#ffffff;"><view class="tabbar-item" data-url="dating" data-opentype="reLaunch" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="tabbar-image-box"><image class="tabbar-icon" src="{{pre_url+'/static/img/peisong/home.png'}}"></image></view><view class="tabbar-text">大厅</view></view><view class="tabbar-item" data-url="orderlist" data-opentype="reLaunch" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="tabbar-image-box"><image class="tabbar-icon" src="{{pre_url+'/static/img/peisong/order'+(st!=4?'2':'')+'.png'}}"></image></view><view class="{{['tabbar-text',st!=4?'active':'']}}">订单</view></view><view class="tabbar-item" data-url="orderlist?st=4" data-opentype="reLaunch" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="tabbar-image-box"><image class="tabbar-icon" src="{{pre_url+'/static/img/peisong/orderwc'+(st==4?'2':'')+'.png'}}"></image></view><view class="{{['tabbar-text',st==4?'active':'']}}">已完成</view></view><view class="tabbar-item" data-url="my" data-opentype="reLaunch" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="tabbar-image-box"><image class="tabbar-icon" src="{{pre_url+'/static/img/peisong/my.png'}}"></image></view><view class="tabbar-text">我的</view></view></view></view></block></block><block wx:if="{{loading}}"><loading vue-id="76913cc2-3" bind:__l="__l"></loading></block><dp-tabbar vue-id="76913cc2-4" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="76913cc2-5" data-ref="popmsg" bind:__l="__l"></popmsg><view style="display:none;">{{timestamp}}</view></view>