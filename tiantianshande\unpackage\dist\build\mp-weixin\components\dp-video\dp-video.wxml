<view class="dp-video" style="{{'background-color:'+(params.bgcolor)+';'+('background-image:'+(params.bg_image?'url('+params.bg_image+')':'none')+';')+('background-size:'+('cover')+';')+('background-position:'+('center')+';')+('margin:'+(params.margin_y*2.2+'rpx '+params.margin_x*2.2+'rpx 0')+';')+('padding:'+(params.padding_y*2.2+'rpx '+params.padding_x*2.2+'rpx')+';')+('border-radius:'+(params.bg_border_radius?params.bg_border_radius+'rpx':'0')+';')}}"><block wx:if="{{showPoster}}"><view data-event-opts="{{[['tap',[['playVideo',['$event']]]]]}}" class="poster-container" style="{{'background-image:'+('url('+params.pic+')')+';'+('height:'+(videoHeight+'px')+';')+('border-radius:'+(params.border_radius?params.border_radius+'rpx':'0')+';')}}" bindtap="__e"><view class="play-icon"></view></view></block><video class="dp-video-video" style="{{'height:'+(videoHeight+'px')+';'+('border-radius:'+(params.border_radius?params.border_radius+'rpx':'0')+';')+('display:'+(showPoster?'none':'block')+';')}}" src="{{params.src}}" poster="{{params.pic}}" controls="{{params.controls!==false}}" autoplay="{{params.autoplay&&!showPoster}}" loop="{{params.loop}}" object-fit="{{params.fillMode||videoObjectFit}}" id="myVideo" data-event-opts="{{[['play',[['onPlay',['$event']]]],['pause',[['onPause',['$event']]]],['ended',[['onEnded',['$event']]]],['loadedmetadata',[['onLoadedMetadata',['$event']]]]]}}" muted="{{params.muted}}" bindplay="__e" bindpause="__e" bindended="__e" bindloadedmetadata="__e"></video></view>