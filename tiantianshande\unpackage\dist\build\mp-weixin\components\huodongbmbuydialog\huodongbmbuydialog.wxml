<view><block wx:if="{{isload}}"><view><view data-event-opts="{{[['tap',[['buydialogChange',['$event']]]]]}}" class="buydialog-mask" bindtap="__e"></view><view class="{{['buydialog',menuindex>-1?'tabbarbot':'']}}"><view data-event-opts="{{[['tap',[['buydialogChange',['$event']]]]]}}" class="close" bindtap="__e"><image class="image" src="{{pre_url+'/static/img/close.png'}}"></image></view><view class="title"><image class="img" src="{{nowguige.pic||product.pic}}" data-url="{{nowguige.pic||product.pic}}" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image><view class="price" style="{{'color:'+($root.m0)+';'}}"><block wx:if="{{nowguige.score_price>0&&nowguige.sell_price>0}}"><block>{{''+nowguige.score_price+$root.m1+" + ￥"+nowguige.sell_price+''}}<block wx:if="{{nowguige.market_price>nowguige.sell_price}}"><text class="t2">{{"￥"+nowguige.market_price}}</text></block></block></block><block wx:else><block wx:if="{{nowguige.score_price>0}}"><block>{{''+nowguige.score_price+$root.m2+''}}</block></block><block wx:else><block wx:if="{{nowguige.sell_price>0}}"><block>{{'￥'+nowguige.sell_price+''}}<block wx:if="{{nowguige.market_price>nowguige.sell_price}}"><text class="t2">{{"￥"+nowguige.market_price}}</text></block></block></block></block></block></view><view class="choosename">{{"已选规格: "+nowguige.name}}</view></view><view style="max-height:50vh;overflow:scroll;"><block wx:for="{{guigedata}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="guigelist flex-col"><view class="name">{{item.title}}</view><view class="item flex flex-y-center"><block wx:for="{{item.items}}" wx:for-item="item2" wx:for-index="index2" wx:key="index2"><block><view class="{{['item2 '+(ggselected[item.k]==item2.k?'on':'')]}}" data-itemk="{{item.k}}" data-idx="{{item2.k}}" data-event-opts="{{[['tap',[['ggchange',['$event']]]]]}}" bindtap="__e">{{item2.title}}</view></block></block></view></view></block></view><view class="buynum flex flex-y-center"><view class="flex1">数量：</view><view class="addnum"><view class="minus"><image class="img" src="{{pre_url+'/static/img/cart-minus.png'}}" data-event-opts="{{[['tap',[['gwcminus',['$event']]]]]}}" bindtap="__e"></image></view><input class="input" type="number" data-event-opts="{{[['input',[['gwcinput',['$event']]]]]}}" value="{{gwcnum}}" bindinput="__e"/><view class="plus"><image class="img" src="{{pre_url+'/static/img/cart-plus.png'}}" data-event-opts="{{[['tap',[['gwcplus',['$event']]]]]}}" bindtap="__e"></image></view></view></view><view class="op"><button data-event-opts="{{[['tap',[['addtobuy',['$event']]]]]}}" class="tobuy" style="{{'background-color:'+($root.m3)+';'}}" bindtap="__e">确 定</button></view></view></view></block><block wx:if="{{loading}}"><loading vue-id="d826bc3c-1" bind:__l="__l"></loading></block></view>