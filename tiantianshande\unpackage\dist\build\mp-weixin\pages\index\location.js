(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages/index/location"],{"18c7":function(t,e,o){"use strict";o.r(e);var i=o("f6ab"),n=o("8328");for(var a in n)["default"].indexOf(a)<0&&function(t){o.d(e,t,(function(){return n[t]}))}(a);o("b4f5");var r=o("828b"),s=Object(r["a"])(n["default"],i["b"],i["c"],!1,null,null,null,!1,i["a"],void 0);e["default"]=s.exports},"71d3":function(t,e,o){"use strict";(function(t,e){var i=o("47a9");o("06e9");i(o("3240"));var n=i(o("18c7"));t.__webpack_require_UNI_MP_PLUGIN__=o,e(n.default)}).call(this,o("3223")["default"],o("df3c")["createPage"])},8328:function(t,e,o){"use strict";o.r(e);var i=o("9338"),n=o.n(i);for(var a in i)["default"].indexOf(a)<0&&function(t){o.d(e,t,(function(){return i[t]}))}(a);e["default"]=n.a},9338:function(t,e,o){"use strict";(function(t){var i=o("47a9");Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n=i(o("7ca3")),a=i(o("af34"));function r(t,e){var o=Object.keys(t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);e&&(i=i.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),o.push.apply(o,i)}return o}function s(t){for(var e=1;e<arguments.length;e++){var o=null!=arguments[e]?arguments[e]:{};e%2?r(Object(o),!0).forEach((function(e){(0,n.default)(t,e,o[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(o)):r(Object(o)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(o,e))}))}return t}var c=getApp(),u={data:function(){return{pre_url:c.globalData.pre_url,keyword:"",currentAddress:"",locationInfo:null,searchResults:[],loading:!1,page:1,latitude:"",longitude:"",hasMore:!0,isLocating:!1,searchHistory:[]}},onLoad:function(t){if(t.data){var e=JSON.parse(decodeURIComponent(t.data));this.latitude=e.latitude,this.longitude=e.longitude,this.currentAddress=e.current_address,this.locationInfo=e.location_info}this.latitude&&this.longitude||this.getCurrentLocation(),this.loadSearchHistory()},methods:{t:function(t){try{var e=c.globalData.colors||{};return e[t]||""}catch(o){return console.error("获取颜色错误:",o),""}},getCurrentLocation:function(){var e=this;this.isLocating=!0,this.loading=!0,c.getLocation((function(t){e.latitude=t.latitude,e.longitude=t.longitude,c.get("apiIndex/getLocation",{latitude:t.latitude,longitude:t.longitude},(function(t){1===t.status&&(e.currentAddress=t.data.district+t.data.street,e.locationInfo=t.data),e.loading=!1,e.isLocating=!1}))}),(function(o){console.log("获取位置失败：",o),e.loading=!1,e.isLocating=!1,t.showToast({title:"获取位置失败",icon:"none"})}))},handleSearch:function(){var t=this;this.searchTimer&&clearTimeout(this.searchTimer),this.searchTimer=setTimeout((function(){t.page=1,t.searchResults=[],t.hasMore=!0,t.searchLocation()}),500)},searchLocation:function(){var t=this;this.keyword&&!this.loading&&this.hasMore&&(this.loading=!0,c.get("apiIndex/searchLocation",{keyword:this.keyword,latitude:this.latitude,longitude:this.longitude,page:this.page},(function(e){1===e.status&&(1===t.page?t.searchResults=e.data:t.searchResults=[].concat((0,a.default)(t.searchResults),(0,a.default)(e.data)),t.hasMore=10===e.data.length),t.loading=!1})))},loadMore:function(){this.hasMore&&!this.loading&&(this.page++,this.searchLocation())},clearSearch:function(){this.keyword="",this.searchResults=[]},loadSearchHistory:function(){try{var e=t.getStorageSync("location_search_history");e&&(this.searchHistory=JSON.parse(e))}catch(o){console.error("加载搜索历史失败",o)}},addToHistory:function(e){this.searchHistory=this.searchHistory.filter((function(t){return t.name!==e.name})),this.searchHistory.unshift({name:e.name,latitude:e.latitude,longitude:e.longitude,address:e.address}),this.searchHistory.length>10&&(this.searchHistory=this.searchHistory.slice(0,10)),t.setStorageSync("location_search_history",JSON.stringify(this.searchHistory))},clearHistory:function(){var e=this;t.showModal({title:"提示",content:"确定要清除所有搜索历史吗？",success:function(o){o.confirm&&(e.searchHistory=[],t.removeStorageSync("location_search_history"))}})},useHistoryItem:function(t){this.selectLocation(t)},useCurrentLocation:function(){this.locationInfo?(t.$emit("location_selected",s(s({},this.locationInfo),{},{latitude:this.latitude,longitude:this.longitude,display_name:this.currentAddress})),t.navigateBack()):t.showToast({title:"位置信息不完整",icon:"none"})},selectLocation:function(e){this.addToHistory(e),t.$emit("location_selected",s(s({},e),{},{latitude:e.latitude,longitude:e.longitude,display_name:e.name})),t.navigateBack()}}};e.default=u}).call(this,o("df3c")["default"])},b4f5:function(t,e,o){"use strict";var i=o("eedb"),n=o.n(i);n.a},eedb:function(t,e,o){},f6ab:function(t,e,o){"use strict";o.d(e,"b",(function(){return i})),o.d(e,"c",(function(){return n})),o.d(e,"a",(function(){}));var i=function(){var t=this,e=t.$createElement,o=(t._self._c,t.t("color1")),i=t.t("color1rgb"),n=t.t("color1"),a=t.t("color1rgb"),r=!t.keyword&&!t.searchResults.length,s=t.searchResults.length;t.$mp.data=Object.assign({},{$root:{m0:o,m1:i,m2:n,m3:a,g0:r,g1:s}})},n=[]}},[["71d3","common/runtime","common/vendor"]]]);