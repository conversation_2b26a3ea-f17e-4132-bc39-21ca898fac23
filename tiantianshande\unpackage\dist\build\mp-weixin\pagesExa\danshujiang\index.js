require('../common/vendor.js');(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pagesExa/danshujiang/index"],{"453b":function(e,t,r){},5069:function(e,t,r){"use strict";r.r(t);var a=r("5ef2"),n=r.n(a);for(var i in a)["default"].indexOf(i)<0&&function(e){r.d(t,e,(function(){return a[e]}))}(i);t["default"]=n.a},"5ef2":function(e,t,r){"use strict";(function(e){var a=r("47a9");Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=a(r("7eb4")),i=a(r("af34")),d=a(r("ee10")),o=getApp(),l={data:function(){return{activityId:"",currentOrderCount:0,rewardMode:"normal",rewardModeName:"",progressWidth:0,totalReward:"0.00",pendingReward:"0.00",rewardedAmount:"0.00",milestones:[],allRules:[],currentStage:null,nextStage:null,rewardRecords:[],page:1,limit:10,loading:!1,nodata:!1,nomore:!1,cycleRotation:120,cycle_num:0,rewardDetails:[],pendingClaim:"0.00"}},onLoad:function(e){this.activityId=e.id||"",this.getdata()},onPullDownRefresh:function(){this.getdata()},onReachBottom:function(){this.nomore||this.nodata||(this.page=this.page+1,this.getRewardRecords())},methods:{getdata:function(e){e||(this.page=1,this.rewardRecords=[]),this.loading=!0,this.getUserRewardInfo(),this.getRewardRecords(),this.getRewardStats()},getUserRewardInfo:function(){var e=this;return(0,d.default)(n.default.mark((function t(){var r;return n.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:try{r=e,o.showLoading("加载中"),o.post("ApiDanshujiang/getUserRewardInfo",{aid:o.aid,activity_id:e.activityId},(function(e){if(o.showLoading(!1),1===e.status){var t=e.data;r.currentOrderCount=t.current_order_count,r.rewardMode=1===t.reward_mode?"normal":2===t.reward_mode?"ladder":"cycle",r.rewardModeName=t.reward_mode_name,r.allRules=t.all_rules||[],r.currentStage=t.current_stage,r.nextStage=t.next_stage,r.cycle_num=t.cycle_num,r.rewardDetails=t.reward_details||[],r.updateMilestones(),r.calculateProgress()}else o.error(e.msg)}))}catch(a){o.showLoading(!1),o.error("获取奖励信息失败")}case 1:case"end":return t.stop()}}),t)})))()},getRewardRecords:function(){var t=this;return(0,d.default)(n.default.mark((function r(){var a;return n.default.wrap((function(r){while(1)switch(r.prev=r.next){case 0:try{a=t,a.loading=!0,o.showLoading("加载中"),o.post("ApiDanshujiang/getRewardRecords",{aid:o.aid,activity_id:t.activityId,page:t.page,limit:t.limit},(function(t){if(a.loading=!1,o.showLoading(!1),1===t.status){var r=t.data,n=r.list,d=r.total;1===a.page?(a.rewardRecords=n,a.nodata=0===n.length):a.rewardRecords=[].concat((0,i.default)(a.rewardRecords),(0,i.default)(n)),a.nomore=a.rewardRecords.length>=d,e.stopPullDownRefresh()}else o.error(t.msg)}))}catch(n){t.loading=!1,o.showLoading(!1),o.error("获取奖励记录失败"),e.stopPullDownRefresh()}case 1:case"end":return r.stop()}}),r)})))()},getRewardStats:function(){var e=this;return(0,d.default)(n.default.mark((function t(){var r;return n.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:try{r=e,o.showLoading("加载中"),o.post("ApiDanshujiang/getRewardStats",{aid:o.aid,activity_id:e.activityId},(function(e){if(o.showLoading(!1),1===e.status){var t=e.data;r.totalReward=t.total_reward.toFixed(2),r.pendingReward=t.pending_reward.toFixed(2),r.rewardedAmount=t.issued_reward.toFixed(2),r.pendingClaim=t.pending_claim?t.pending_claim.toFixed(2):"0.00"}else o.error(e.msg)}))}catch(a){o.showLoading(!1),o.error("获取奖励统计失败")}case 1:case"end":return t.stop()}}),t)})))()},updateMilestones:function(){var e=this;if(this.allRules&&this.allRules.length)if("cycle"===this.rewardMode){var t=Math.ceil(this.currentOrderCount/this.cycle_num);this.milestones=this.allRules.map((function(r,a){return{orderNum:r.order_num+(t-1)*e.cycle_num,rate:r.reward_rate,position:a/(e.allRules.length-1)*100,reward:e.calculateRewardAtMilestone(r.order_num,r.reward_rate)}}))}else{var r=2*Math.max.apply(Math,(0,i.default)(this.allRules.map((function(e){return e.order_num}))));this.milestones=this.allRules.map((function(t,a){return{orderNum:t.order_num,rate:t.reward_rate,position:t.order_num/r*100,reward:e.calculateRewardAtMilestone(t.order_num,t.reward_rate)}}))}},calculateRewardAtMilestone:function(e,t){return(t/100*e*100).toFixed(2)},calculateProgress:function(){if(this.allRules&&this.allRules.length)if("cycle"===this.rewardMode){var e=(this.currentOrderCount-1)%this.cycle_num+1;this.progressWidth=Math.max(e/this.cycle_num*100,5)}else{var t=2*Math.max.apply(Math,(0,i.default)(this.allRules.map((function(e){return e.order_num}))));this.progressWidth=Math.min(this.currentOrderCount/t*100,100)}},calculateCycleRotation:function(){var e=this;if(!this.allRules||!this.allRules.length||!this.currentStage)return 0;var t=this.allRules.findIndex((function(t){return t.order_num===(e.currentStage.order_num||0)}));if(-1===t)return 0;var r=360/this.allRules.length;return t*r},claimReward:function(e){var t=this;return(0,d.default)(n.default.mark((function r(){var a;return n.default.wrap((function(r){while(1)switch(r.prev=r.next){case 0:if(!e.is_claimed&&!e.is_paid){r.next=2;break}return r.abrupt("return");case 2:try{a=t,o.showLoading("领取中"),o.post("ApiDanshujiang/claimReward",{aid:o.aid,activity_id:e.activity_id||t.activityId,order_id:e.order_id},(function(t){o.showLoading(!1),1===t.status?(e.is_claimed=!0,t.data.reward_details&&(a.rewardDetails=t.data.reward_details),a.getUserRewardInfo(),a.getRewardStats(),o.success(t.msg||"领取成功，等待发放")):o.error(t.msg)}))}catch(n){o.showLoading(!1),o.error("领取奖励失败")}case 3:case"end":return r.stop()}}),r)})))()}}};t.default=l}).call(this,r("df3c")["default"])},"8c93":function(e,t,r){"use strict";var a=r("453b"),n=r.n(a);n.a},cd30:function(e,t,r){"use strict";(function(e,t){var a=r("47a9");r("06e9");a(r("3240"));var n=a(r("fdd8"));e.__webpack_require_UNI_MP_PLUGIN__=r,t(n.default)}).call(this,r("3223")["default"],r("df3c")["createPage"])},e64e:function(e,t,r){"use strict";r.d(t,"b",(function(){return n})),r.d(t,"c",(function(){return i})),r.d(t,"a",(function(){return a}));var a={nodata:function(){return r.e("components/nodata/nodata").then(r.bind(null,"101c"))},nomore:function(){return r.e("components/nomore/nomore").then(r.bind(null,"3892"))},loading:function(){return r.e("components/loading/loading").then(r.bind(null,"ceaa"))}},n=function(){var e=this,t=e.$createElement,r=(e._self._c,"cycle"===e.rewardMode?e.calculateCycleRotation():null),a="cycle"===e.rewardMode?e.__map(e.allRules,(function(t,r){var a=e.__get_orig(t),n=e.allRules.length,i=e.allRules.length;return{$orig:a,g0:n,g1:i}})):null,n="cycle"===e.rewardMode?Math.ceil(e.currentOrderCount/e.cycle_num):null,i=e.rewardDetails.length;e.$mp.data=Object.assign({},{$root:{m0:r,l0:a,g2:n,g3:i}})},i=[]},fdd8:function(e,t,r){"use strict";r.r(t);var a=r("e64e"),n=r("5069");for(var i in n)["default"].indexOf(i)<0&&function(e){r.d(t,e,(function(){return n[e]}))}(i);r("8c93");var d=r("828b"),o=Object(d["a"])(n["default"],a["b"],a["c"],!1,null,"160f3988",null,!1,a["a"],void 0);t["default"]=o.exports}},[["cd30","common/runtime","common/vendor"]]]);