<view><block wx:if="{{isload}}"><block><view class="banner" style="{{'background:'+('url('+pre_url+'/static/img/topbg.png)')+';'+('background-size:'+('100%')+';')}}"><image src="{{set.logo}}" background-size="cover" data-url="{{uinfo.bid==0?'/pages/index/index':'/pagesExt/business/index?id='+uinfo.bid}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"></image><view class="info"><text class="nickname">{{set.name}}</text><text>{{uinfo.un}}</text><block wx:if="{{showrecharge}}"><view class="recharge" data-url="recharge" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">充值</view></block></view><view data-event-opts="{{[['tap',[['saoyisao',['$event']]]]]}}" class="set" bindtap="__e"><image src="/static/img/ico-scan.png"></image></view></view><view class="contentdata"><block wx:if="{{auth_data.product}}"><block><view class="list"><view class="item" data-url="../index/mytuanlist" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="f2">我的团</view><text class="f3"></text><image class="f4" src="/static/img/arrowright.png"></image></view><view class="item" data-url="/activity/commission/downorder" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="f2">我的订单</view><text class="f3"></text><image class="f4" src="/static/img/arrowright.png"></image></view><view class="item" data-url="/activity/commission/poster" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="f2">推广码</view><text class="f3"></text><image class="f4" src="/static/img/arrowright.png"></image></view><view class="item" data-url="../index/mtuanlist" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="f2">推荐团购</view><text class="f3"></text><image class="f4" src="/static/img/arrowright.png"></image></view></view></block></block><block wx:if="{{auth_data.product}}"><block><view class="list"></view></block></block><view class="list"><block wx:if="{{showbusinessqr}}"><view class="item" data-url="businessqr" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="f2">推广码</view><text class="f3"></text><image class="f4" src="/static/img/arrowright.png"></image></view></block><block wx:if="{{showbusinessqr}}"><view class="item" data-url="businessqr" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="f2">推广码</view><text class="f3"></text><image class="f4" src="/static/img/arrowright.png"></image></view></block><block wx:if="{{showworkadd}}"><view class="item" data-url="../workorder/add" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="f2">工单提交</view><text class="f3"></text><image class="f4" src="/static/img/arrowright.png"></image></view></block><block wx:if="{{uinfo.bid>0}}"><view class="item" data-url="setinfo" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="f2">店铺设置</view><text class="f3"></text><image class="f4" src="/static/img/arrowright.png"></image></view></block><view class="item" data-url="setpwd" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="f2">修改密码</view><text class="f3"></text><image class="f4" src="/static/img/arrowright.png"></image></view><view class="item" data-url="../index/login" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="f2">切换账号</view><text class="f3"></text><image class="f4" src="/static/img/arrowright.png"></image></view><view class="item" data-url="/pages/my/usercenter" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="f2">返回个人中心</view><text class="f3"></text><image class="f4" src="/static/img/arrowright.png"></image></view></view></view><view class="tabbar"><view class="tabbar-bot"></view><view class="tabbar-bar" style="background-color:#ffffff;"><block wx:if="{{auth_data.member}}"><view class="tabbar-item" data-url="../member/index" data-opentype="reLaunch" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="tabbar-image-box"><image class="tabbar-icon" src="{{pre_url+'/static/img/admin/member.png'}}"></image></view><view class="tabbar-text">{{$root.m0}}</view></view></block><block wx:if="{{auth_data.finance}}"><view class="tabbar-item" data-url="../finance/index" data-opentype="reLaunch" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="tabbar-image-box"><image class="tabbar-icon" src="{{pre_url+'/static/img/admin/finance.png'}}"></image></view><view class="tabbar-text">财务</view></view></block><view class="tabbar-item" data-url="../index/index" data-opentype="reLaunch" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="tabbar-image-box"><image class="tabbar-icon" src="{{pre_url+'/static/img/admin/my2.png'}}"></image></view><view class="tabbar-text active">我的</view></view></view></view></block></block><popmsg class="vue-ref" vue-id="17171748-1" data-ref="popmsg" bind:__l="__l"></popmsg></view>