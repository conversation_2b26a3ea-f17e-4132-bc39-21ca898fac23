<view class="container"><block wx:if="{{isload}}"><block><dd-tab vue-id="78261338-1" itemdata="{{['已结束','抢购中','即将开始']}}" itemst="{{['0','1','2']}}" st="{{st}}" isfixed="{{true}}" data-event-opts="{{[['^changetab',[['changetab']]]]}}" bind:changetab="__e" bind:__l="__l"></dd-tab><view style="width:100%;height:100rpx;"></view><view class="content"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="item"><image class="f1" style="height:150px;" src="{{item.$orig.pic}}" data-url="{{'product?id='+item.$orig.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"></image><view class="f2"><text class="t1">{{item.$orig.name}}</text><view class="t2" style="{{'color:'+(item.m0)+';'}}"><text>{{"售卖人:"+item.$orig.outname}}</text></view><view class="t2" style="{{'color:'+(item.m1)+';'}}"><text>{{"开始时间:"+item.$orig.miaosha_date}}</text></view><view class="t2" style="{{'color:'+(item.m2)+';'}}"><block wx:if="{{st==2&&item.$orig.qiang==1}}"><text>{{"可提前开始时间:"+item.$orig.miaosha_date2}}</text></block></view><view class="t3"><text class="x1" style="{{'color:'+(item.m3)+';'}}"><text style="font-size:24rpx;">￥</text>{{item.$orig.sell_price}}</text><block wx:if="{{st==1}}"><button class="x3" style="{{'background:'+('linear-gradient(90deg,'+item.m4+' 0%,rgba('+item.m5+',0.8) 100%)')+';'}}" data-url="{{'product?id='+item.$orig.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">立即抢购</button></block><block wx:else><block wx:if="{{item.$orig.starttime2<=nowtime&&item.$orig.qiang==1}}"><button class="x3" style="{{'background:'+('linear-gradient(90deg,'+item.m6+' 0%,rgba('+item.m7+',0.8) 100%)')+';'}}" data-url="{{'product?id='+item.$orig.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">可提前抢购</button></block><block wx:else><block wx:if="{{st==2}}"><button class="x3" style="{{'background:'+(item.m8)+';'}}" data-url="{{'product?id='+item.$orig.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">抢先看看</button></block><block wx:else><button class="x3 xx1" style="{{'background:'+(item.m9)+';'}}">已结束</button></block></block></block></view></view></view></block><block wx:if="{{nodata}}"><view class="item" style="display:block;"><nodata vue-id="78261338-2" bind:__l="__l"></nodata></view></block><block wx:if="{{nomore}}"><nomore vue-id="78261338-3" bind:__l="__l"></nomore></block></view></block></block><block wx:if="{{loading}}"><loading vue-id="78261338-4" bind:__l="__l"></loading></block><dp-tabbar vue-id="78261338-5" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="78261338-6" data-ref="popmsg" bind:__l="__l"></popmsg></view>