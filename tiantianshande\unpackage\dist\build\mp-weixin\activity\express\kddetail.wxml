<view class="container"><block wx:if="{{isload}}"><block><view class="expressinfo"><view class="head"><view class="f1"><block wx:if="{{data.kuaidinum}}"><view>{{"运单号："+data.kuaidinum}}</view></block><block wx:else><view>{{"订单号："+data.ordernum}}</view></block><view></view></view><view class="f2 flex"><view class="t2_box"><text>{{data.sendManPrintCity}}</text><text class="t2">{{data.sendManName}}</text></view><view class="t2_box"><image class="jiantou" src="/static/img/jiantou.png"></image><view class="t2">{{data.sta}}</view></view><view class="t2_box"><text>{{data.recManPrintCity}}</text><text class="t2">{{data.sendManName}}</text></view></view></view><view class="content"><block wx:if="{{datalist}}"><block><block wx:for="{{datalist}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="{{['item '+(index==0?'on':'')]}}"><view class="f1"><image src="{{'/static/img/dot'+(index==0?'2':'1')+'.png'}}"></image></view><view class="f2"><text class="t2">{{item.time}}</text><text class="t1">{{item.context}}</text></view></view></block><block wx:if="{{nodata}}"><nodata vue-id="1349424a-1" text="暂未查找到物流信息" bind:__l="__l"></nodata></block></block></block><block wx:else><view class="item"><view class="f1"><image src="{{'/static/img/dot'+(index==0?'2':'1')+'.png'}}"></image></view><view class="f2"><text class="t2">{{data.sta}}</text><text class="t1"></text></view></view></block></view></view></block></block><uni-popup class="vue-ref" vue-id="1349424a-2" id="dialogRemark" type="dialog" data-ref="dialogExpress" bind:__l="__l" vue-slots="{{['default']}}"><view style="background:#fff;padding:20rpx 30rpx;border-radius:10rpx;width:600rpx;"><form data-event-opts="{{[['submit',[['formSubmit',['$event']]]]]}}" bindsubmit="__e"><view class="sendexpress-item" style="padding:20rpx 0;"><view><input class="input" type="text" placeholder="请输入原因" placeholder-style="font-size:28rpx;color:#BBBBBB" name="remark"/></view><button class="submit" style="{{('background:linear-gradient(90deg,'+$root.m0+' 0%,rgba('+$root.m1+',0.8) 100%)')}}" form-type="submit">确定取消</button></view></form></view></uni-popup><block wx:if="{{loading}}"><loading vue-id="1349424a-3" bind:__l="__l"></loading></block><dp-tabbar vue-id="1349424a-4" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="1349424a-5" data-ref="popmsg" bind:__l="__l"></popmsg></view>