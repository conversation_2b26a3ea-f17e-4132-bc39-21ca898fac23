(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages/index/getpwd"],{"0c24":function(t,n,e){"use strict";e.r(n);var o=e("2626"),i=e("9eb2");for(var a in i)["default"].indexOf(a)<0&&function(t){e.d(n,t,(function(){return i[t]}))}(a);e("4890");var s=e("828b"),r=Object(s["a"])(i["default"],o["b"],o["c"],!1,null,null,null,!1,o["a"],void 0);n["default"]=r.exports},2626:function(t,n,e){"use strict";e.d(n,"b",(function(){return i})),e.d(n,"c",(function(){return a})),e.d(n,"a",(function(){return o}));var o={loading:function(){return e.e("components/loading/loading").then(e.bind(null,"ceaa"))},dpTabbar:function(){return e.e("components/dp-tabbar/dp-tabbar").then(e.bind(null,"b875"))},popmsg:function(){return e.e("components/popmsg/popmsg").then(e.bind(null,"2bf2"))}},i=function(){var t=this.$createElement,n=(this._self._c,this.isload?this.t("color1"):null),e=this.isload?this.t("color1"):null,o=this.isload?this.t("color1rgb"):null;this.$mp.data=Object.assign({},{$root:{m0:n,m1:e,m2:o}})},a=[]},4890:function(t,n,e){"use strict";var o=e("5118"),i=e.n(o);i.a},5118:function(t,n,e){},"9eb2":function(t,n,e){"use strict";e.r(n);var o=e("e4a1"),i=e.n(o);for(var a in o)["default"].indexOf(a)<0&&function(t){e.d(n,t,(function(){return o[t]}))}(a);n["default"]=i.a},e360:function(t,n,e){"use strict";(function(t,n){var o=e("47a9");e("06e9");o(e("3240"));var i=o(e("0c24"));t.__webpack_require_UNI_MP_PLUGIN__=e,n(i.default)}).call(this,e("3223")["default"],e("df3c")["createPage"])},e4a1:function(t,n,e){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;var o=getApp(),i={data:function(){return{opt:{},loading:!1,isload:!1,menuindex:-1,logintype_1:!0,logintype_2:!1,logintype_3:!1,xystatus:0,xycontent:"",needsms:!1,showxieyi:!1,isagree:!1,smsdjs:"",tel:"",hqing:0}},onLoad:function(t){this.opt=o.getopts(t),this.getdata()},onPullDownRefresh:function(){this.getdata()},methods:{getdata:function(){this.loaded()},formSubmit:function(t){var n=t.detail.value;""!=n.tel?""!=n.pwd?n.pwd.length<6?o.alert("新密码不小于6位"):""!=n.repwd?n.pwd==n.repwd?""!=n.smscode?(o.showLoading("提交中"),o.post("ApiIndex/getpwd",{tel:n.tel,pwd:n.pwd,smscode:n.smscode},(function(t){o.showLoading(!1),1==t.status?(o.success(t.msg),setTimeout((function(){o.goto("/pagesB/login/login")}),1e3)):o.error(t.msg)}))):o.alert("请输入短信验证码"):o.alert("两次密码不一致"):o.alert("请再次输入新密码"):o.alert("请输入密码"):o.alert("请输入手机号")},telinput:function(t){this.tel=t.detail.value},smscode:function(){var t=this;if(1!=t.hqing){t.hqing=1;var n=t.tel;if(""==n)return o.alert("请输入手机号码"),t.hqing=0,!1;if(!/^1[3456789]\d{9}$/.test(n))return o.alert("手机号码有误，请重填"),t.hqing=0,!1;o.post("ApiIndex/sendsms",{tel:n},(function(t){1!=t.status&&o.alert(t.msg)}));var e=120,i=setInterval((function(){e--,e<0?(t.smsdjs="重新获取",t.hqing=0,clearInterval(i)):e>=0&&(t.smsdjs=e+"秒")}),1e3)}}}};n.default=i}},[["e360","common/runtime","common/vendor"]]]);