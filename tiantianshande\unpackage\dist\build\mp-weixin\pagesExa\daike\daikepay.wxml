<view class="container"><block wx:if="{{isload}}"><block><view class="top"><view class="f1">需支付金额</view><block wx:if="{{payorder.score==0}}"><view class="f2"><text class="t1">￥</text><text class="t2">{{payorder.money}}</text></view></block><block wx:else><block wx:if="{{payorder.money>0&&payorder.score>0}}"><view class="f2"><text class="t1">￥</text><text class="t2">{{payorder.money}}</text><text style="font-size:28rpx;">{{'+ '+payorder.score+$root.m0}}</text></view></block><block wx:else><view class="f2"><text class="t3">{{payorder.score+$root.m1}}</text></view></block></block><block wx:if="{{detailurl!=''}}"><view class="f3" data-url="{{detailurl}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">订单详情<text class="iconfont iconjiantou"></text></view></block></view><view class="paytype" style="margin-bottom:0px;position:relative;overflow:auto;"><block wx:if="{{is_shop_pay==true}}"><view style="z-index:3;width:100%;height:200px;background-color:#ccc;position:absolute;opacity:0.5;"></view></block><view class="f1">选择支付方式：</view><block wx:if="{{payorder.money==0&&payorder.score>0}}"><block><view class="f2"><block wx:if="{{moneypay==1}}"><view class="item" data-typeid="1" data-event-opts="{{[['tap',[['changeradio',['$event']]]]]}}" catchtap="__e"><view class="t1 flex"><image class="img" src="/static/img/pay-money.png"></image><view class="flex-col"><text>{{$root.m2+"支付"}}</text><text style="font-size:22rpx;font-weight:normal;">{{"剩余"+$root.m3}}<text style="color:#FC5729;">{{userinfo.score}}</text></text></view></view><view class="radio" style="{{(typeid=='1'?'background:'+$root.m4+';border:0':'')}}"><image class="radio-img" src="/static/img/checkd.png"></image></view></view></block></view></block></block><block wx:else><block><view class="f2"><block wx:if="{{sandepay==1}}"><view class="item" data-typeid="29" data-event-opts="{{[['tap',[['changeradio',['$event']]]]]}}" catchtap="__e"><view class="t1"><image class="img" src="/static/img/pay-money.png"></image>杉德支付</view><view class="radio" style="{{(typeid=='29'?'background:'+$root.m5+';border:0':'')}}"><image class="radio-img" src="/static/img/checkd.png"></image></view></view></block><block wx:if="{{wxpay==1&&(wxpay_type==0||wxpay_type==1||wxpay_type==2||wxpay_type==3||wxpay_type==4)}}"><view class="item" data-typeid="2" data-event-opts="{{[['tap',[['changeradio',['$event']]]]]}}" catchtap="__e"><view class="t1"><image class="img" src="/static/img/withdraw-weixin.png"></image>微信支付</view><view class="radio" style="{{(typeid=='2'?'background:'+$root.m6+';border:0':'')}}"><image class="radio-img" src="/static/img/checkd.png"></image></view></view></block><block wx:if="{{wxpay==1&&wxpay_type==5}}"><view class="item" data-typeid="2" data-event-opts="{{[['tap',[['changeradio',['$event']]]]]}}" catchtap="__e"><view class="t1"><image class="img" src="/static/img/withdraw-weixin.png"></image>嘉联微信支付</view><view class="radio" style="{{(typeid=='2'?'background:'+$root.m7+';border:0':'')}}"><image class="radio-img" src="/static/img/checkd.png"></image></view></view></block><block wx:if="{{wxpay==1&&wxpay_type==22}}"><view class="item" data-typeid="22" data-event-opts="{{[['tap',[['changeradio',['$event']]]]]}}" catchtap="__e"><view class="t1"><image class="img" src="/static/img/withdraw-weixin.png"></image>微信支付</view><view class="radio" style="{{(typeid=='22'?'background:'+$root.m8+';border:0':'')}}"><image class="radio-img" src="/static/img/checkd.png"></image></view></view></block><block wx:if="{{alipay==2}}"><view class="item" data-typeid="23" data-event-opts="{{[['tap',[['changeradio',['$event']]]]]}}" catchtap="__e"><view class="t1"><image class="img" src="/static/img/withdraw-alipay.png"></image>支付宝支付</view><view class="radio" style="{{(typeid=='23'?'background:'+$root.m9+';border:0':'')}}"><image class="radio-img" src="/static/img/checkd.png"></image></view></view></block><block wx:if="{{alipay==1}}"><view class="item" data-typeid="3" data-event-opts="{{[['tap',[['changeradio',['$event']]]]]}}" catchtap="__e"><view class="t1"><image class="img" src="/static/img/withdraw-alipay.png"></image>支付宝支付</view><view class="radio" style="{{(typeid=='3'?'background:'+$root.m10+';border:0':'')}}"><image class="radio-img" src="/static/img/checkd.png"></image></view></view></block><block wx:if="{{more_alipay==1}}"><block><block wx:if="{{alipay2==1}}"><view class="item" data-typeid="31" data-event-opts="{{[['tap',[['changeradio',['$event']]]]]}}" catchtap="__e"><view class="t1"><image class="img" src="/static/img/withdraw-alipay.png"></image>支付宝支付2</view><view class="radio" style="{{(typeid=='31'?'background:'+$root.m11+';border:0':'')}}"><image class="radio-img" src="/static/img/checkd.png"></image></view></view></block><block wx:if="{{alipay3==1}}"><view class="item" data-typeid="32" data-event-opts="{{[['tap',[['changeradio',['$event']]]]]}}" catchtap="__e"><view class="t1"><image class="img" src="/static/img/withdraw-alipay.png"></image>支付宝支付3</view><view class="radio" style="{{(typeid=='32'?'background:'+$root.m12+';border:0':'')}}"><image class="radio-img" src="/static/img/checkd.png"></image></view></view></block></block></block><block wx:if="{{paypal==1}}"><view class="item" data-typeid="51" data-event-opts="{{[['tap',[['changeradio',['$event']]]]]}}" catchtap="__e"><view class="t1"><image class="img" src="/static/img/paypal.png"></image>PayPal支付</view><view class="radio" style="{{(typeid=='51'?'background:'+$root.m13+';border:0':'')}}"><image class="radio-img" src="/static/img/checkd.png"></image></view></view></block><block wx:if="{{baidupay==1}}"><view class="item" data-typeid="11" data-event-opts="{{[['tap',[['changeradio',['$event']]]]]}}" catchtap="__e"><view class="t1"><image class="img" src="/static/img/pay-money.png"></image>在线支付</view><view class="radio" style="{{(typeid=='11'?'background:'+$root.m14+';border:0':'')}}"><image class="radio-img" src="/static/img/checkd.png"></image></view></view></block><block wx:if="{{toutiaopay==1}}"><view class="item" data-typeid="12" data-event-opts="{{[['tap',[['changeradio',['$event']]]]]}}" catchtap="__e"><view class="t1"><image class="img" src="/static/img/pay-money.png"></image>在线支付</view><view class="radio" style="{{(typeid=='12'?'background:'+$root.m15+';border:0':'')}}"><image class="radio-img" src="/static/img/checkd.png"></image></view></view></block><block wx:if="{{moneypay==1}}"><view class="item" data-typeid="1" data-event-opts="{{[['tap',[['changeradio',['$event']]]]]}}" catchtap="__e"><view class="t1 flex"><image class="img" src="/static/img/pay-money.png"></image><view class="flex-col"><text>{{$root.m16+"支付"}}</text><text style="font-size:22rpx;font-weight:normal;">可用余额<text style="color:#FC5729;">{{"￥"+userinfo.money}}</text></text></view></view><view class="radio" style="{{(typeid=='1'?'background:'+$root.m17+';border:0':'')}}"><image class="radio-img" src="/static/img/checkd.png"></image></view></view></block><block wx:if="{{heipay==1}}"><view class="item" data-typeid="88" data-event-opts="{{[['tap',[['changeradio',['$event']]]]]}}" catchtap="__e"><view class="t1 flex"><image class="img" src="/static/img/pay-money.png"></image><view class="flex-col"><text>{{$root.m18+"支付"}}</text><text style="font-size:22rpx;font-weight:normal;">可用购物积分<text style="color:#FC5729;">{{"￥"+userinfo.heiscore}}</text></text></view></view><view class="radio" style="{{(typeid=='88'?'background:'+$root.m19+';border:0':'')}}"><image class="radio-img" src="/static/img/checkd.png"></image></view></view></block><block wx:if="{{yuanbaopay==1}}"><view class="item" style="height:130rpx;" data-typeid="yuanbao" data-event-opts="{{[['tap',[['changeradio',['$event']]]]]}}" catchtap="__e"><view class="t1 flex"><image class="img" src="/static/img/pay-money.png"></image><view class="flex-col"><text>{{$root.m20+"支付"}}</text><text style="font-size:22rpx;font-weight:normal;">{{'可用'+$root.m21+''}}<text style="color:#FC5729;">{{userinfo.yuanbao}}</text></text><text style="font-size:22rpx;font-weight:normal;">需支付<text style="color:#FC5729;">{{yuanbao_msg}}</text></text></view></view><view class="radio" style="{{(typeid=='yuanbao'?'background:'+$root.m22+';border:0':'')}}"><image class="radio-img" src="/static/img/checkd.png"></image></view></view></block></view></block></block></view><block wx:if="{{$root.g0}}"><view class="paytype" style="margin-bottom:0px;"><view class="f1">使用商店余额支付：<switch checked="{{is_shop_pay==true}}" data-event-opts="{{[['change',[['switch_shop_pay',['$event']]]]]}}" bindchange="__e"></switch></view></view></block><block wx:if="{{$root.g1}}"><view class="paytype"><view class="f1">商店支付：</view><block><view class="f2"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index"><view class="item" data-typeid="1"><view class="t1 flex"><image class="img" src="/static/img/pay-money.png"></image><view class="flex-col"><text>{{item.$orig.name}}</text><text style="font-size:22rpx;font-weight:normal;">{{"剩余"+item.m23}}<text style="color:#FC5729;">{{item.$orig.balance+"元"}}</text></text><text style="font-size:22rpx;font-weight:normal;">{{"剩余"+item.m24}}<text style="color:#FC5729;">{{item.$orig.pay_total+"元"}}</text></text></view></view><view class="radio" style="{{(true?'background:'+item.m25+';border:0':'')}}"><image class="radio-img" src="/static/img/checkd.png"></image></view></view></block></view></block></view></block><block><block wx:if="{{typeid==29}}"><view><rich-text nodes="{{san_pay.form}}"></rich-text></view></block></block><block wx:if="{{typeid!='0'&&typeid!=29}}"><button data-event-opts="{{[['tap',[['topay',['$event']]]]]}}" class="btn" style="{{'background:'+($root.m26)+';'}}" bindtap="__e">立即支付</button></block><block wx:if="{{cancod==1}}"><button data-event-opts="{{[['tap',[['topay2',['$event']]]]]}}" class="btn" style="background:rgba(126,113,246,0.5);" bindtap="__e">{{codtxt}}</button></block><block wx:if="{{pay_transfer==1}}"><button class="btn" style="{{'background:'+($root.m27)+';'}}" data-text="{{$root.m28}}" data-event-opts="{{[['tap',[['topayTransfer',['$event']]]]]}}" bindtap="__e">{{$root.m29}}</button></block><block wx:if="{{pay_month==1}}"><button data-event-opts="{{[['tap',[['topayMonth',['$event']]]]]}}" class="btn" style="{{'background:'+($root.m30)+';'}}" bindtap="__e">{{pay_month_txt}}</button></block><block wx:if="{{daifu}}"><block><block wx:if="{{$root.m31}}"><button data-event-opts="{{[['tap',[['todaifu',['$event']]]]]}}" class="btn daifu-btn" bindtap="__e">{{''+daifu_txt+''}}</button></block><block wx:else><button class="btn daifu-btn" open-type="share">{{''+daifu_txt+''}}</button></block></block></block><uni-popup class="vue-ref" vue-id="2d4d2654-1" id="dialogInput" type="dialog" data-ref="dialogInput" bind:__l="__l" vue-slots="{{['default']}}"><uni-popup-dialog vue-id="{{('2d4d2654-2')+','+('2d4d2654-1')}}" mode="input" title="支付密码" value="" placeholder="请输入支付密码" data-event-opts="{{[['^confirm',[['getpwd']]]]}}" bind:confirm="__e" bind:__l="__l"></uni-popup-dialog></uni-popup><block wx:if="{{give_coupon_show}}"><block><view class="give-coupon flex-x-center flex-y-center"><view class="coupon-block"><image style="width:630rpx;height:330rpx;" src="{{pre_url+'/static/img/coupon-top.png'}}"></image><view class="coupon-del flex-x-center flex-y-center" data-url="{{give_coupon_close_url}}" data-event-opts="{{[['tap',[['give_coupon_close',['$event']]]]]}}" bindtap="__e"><image src="/static/img/coupon-del.png"></image></view><view class="flex-x-center"><view class="coupon-info"><view class="flex-x-center coupon-get">{{"获得"+give_coupon_num+"张"+$root.m32}}</view><view style="background:#f5f5f5;padding:10rpx 0;"><block wx:for="{{give_coupon_list}}" wx:for-item="item" wx:for-index="index" wx:key="id"><block><block wx:if="{{index<3}}"><block><view class="coupon-coupon"><view class="{{[item.type==1?'pt_img1':'pt_img2']}}"></view><view class="{{['pt_left',item.type==1?'':'bg2']}}"><block wx:if="{{item.type==1}}"><view class="f1"><text class="t0">￥</text><text class="t1">{{item.money}}</text></view></block><block wx:if="{{item.type==2}}"><view class="f1">礼品券</view></block><block wx:if="{{item.type==3}}"><view class="f1"><text class="t1">{{item.limit_count}}</text><text class="t2">次</text></view></block><block wx:if="{{item.type==4}}"><view class="f1">抵运费</view></block><block wx:if="{{item.type==1||item.type==4}}"><view class="f2"><block wx:if="{{item.minprice>0}}"><text>{{"满"+item.minprice+"元可用"}}</text></block><block wx:else><text>无门槛</text></block></view></block></view><view class="pt_right"><view class="f1"><view class="t1">{{item.name}}</view><block wx:if="{{item.type==1}}"><view class="t2">代金券</view></block><block wx:if="{{item.type==2}}"><view class="t2">礼品券</view></block><block wx:if="{{item.type==3}}"><view class="t2">计次券</view></block><block wx:if="{{item.type==4}}"><view class="t2">运费抵扣券</view></block></view></view><block wx:if="{{item.givenum>1}}"><view class="coupon_num">{{"×"+item.givenum}}</view></block></view></block></block></block></block></view><view class="flex-x-center coupon-btn" data-url="/pages/coupon/mycoupon" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">前往查看</view></view></view></view></view></block></block><uni-popup class="vue-ref" vue-id="2d4d2654-3" id="dialogOpenWeapp" type="dialog" maskClick="{{false}}" data-ref="dialogOpenWeapp" bind:__l="__l" vue-slots="{{['default']}}"><view style="background:#fff;padding:50rpx;position:relative;border-radius:20rpx;"><view style="height:80px;line-height:80px;width:200px;margin:0 auto;font-size:18px;text-align:center;font-weight:bold;color:#333;">恭喜您支付成功</view><view style="height:50px;line-height:50px;width:200px;margin:0 auto;border-radius:5px;color:#66f;font-size:14px;text-align:center;" data-url="{{detailurl}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">查看订单详情</view></view></uni-popup><uni-popup class="vue-ref" vue-id="2d4d2654-4" id="dialogPayconfirm" type="dialog" maskClick="{{false}}" data-ref="dialogPayconfirm" bind:__l="__l" vue-slots="{{['default']}}"><uni-popup-dialog vue-id="{{('2d4d2654-5')+','+('2d4d2654-4')}}" type="info" title="支付确认" content="是否已完成支付" data-event-opts="{{[['^confirm',[['PayconfirmFun']]]]}}" bind:confirm="__e" bind:__l="__l"></uni-popup-dialog></uni-popup><block wx:if="{{yuanbaopay==1&&open_pay}}"><view style="width:100%;height:100%;position:fixed;z-index:10;background-color:#000;opacity:0.45;top:0;"></view></block><block wx:if="{{yuanbaopay==1&&open_pay}}"><view style="width:90%;position:fixed;z-index:11;left:5%;top:25%;background-color:#fff;"><view class="paytype"><view class="f2"><block wx:if="{{wxpay==1&&(wxpay_type==0||wxpay_type==1||wxpay_type==2||wxpay_type==3)}}"><view class="item" data-typeid="2" data-event-opts="{{[['tap',[['changeradio',['$event']]]]]}}" catchtap="__e"><view class="t1"><image class="img" src="/static/img/withdraw-weixin.png"></image>微信支付</view><view class="radio" style="{{(typeid=='2'?'background:'+$root.m33+';border:0':'')}}"><image class="radio-img" src="/static/img/checkd.png"></image></view></view></block><block wx:if="{{wxpay==1&&wxpay_type==22}}"><view class="item" data-typeid="22" data-event-opts="{{[['tap',[['changeradio',['$event']]]]]}}" catchtap="__e"><view class="t1"><image class="img" src="/static/img/withdraw-weixin.png"></image>微信支付</view><view class="radio" style="{{(typeid=='22'?'background:'+$root.m34+';border:0':'')}}"><image class="radio-img" src="/static/img/checkd.png"></image></view></view></block><block wx:if="{{alipay==2}}"><view class="item" data-typeid="23" data-event-opts="{{[['tap',[['changeradio',['$event']]]]]}}" catchtap="__e"><view class="t1"><image class="img" src="/static/img/withdraw-alipay.png"></image>支付宝支付</view><view class="radio" style="{{(typeid=='23'?'background:'+$root.m35+';border:0':'')}}"><image class="radio-img" src="/static/img/checkd.png"></image></view></view></block><block wx:if="{{alipay==1}}"><view class="item" data-typeid="3" data-event-opts="{{[['tap',[['changeradio',['$event']]]]]}}" catchtap="__e"><view class="t1"><image class="img" src="/static/img/withdraw-alipay.png"></image>支付宝支付</view><view class="radio" style="{{(typeid=='3'?'background:'+$root.m36+';border:0':'')}}"><image class="radio-img" src="/static/img/checkd.png"></image></view></view></block><block wx:if="{{more_alipay==1}}"><block><block wx:if="{{alipay2==1}}"><view class="item" data-typeid="31" data-event-opts="{{[['tap',[['changeradio',['$event']]]]]}}" catchtap="__e"><view class="t1"><image class="img" src="/static/img/withdraw-alipay.png"></image>支付宝支付2</view><view class="radio" style="{{(typeid=='31'?'background:'+$root.m37+';border:0':'')}}"><image class="radio-img" src="/static/img/checkd.png"></image></view></view></block><block wx:if="{{alipay3==1}}"><view class="item" data-typeid="32" data-event-opts="{{[['tap',[['changeradio',['$event']]]]]}}" catchtap="__e"><view class="t1"><image class="img" src="/static/img/withdraw-alipay.png"></image>支付宝支付3</view><view class="radio" style="{{(typeid=='32'?'background:'+$root.m38+';border:0':'')}}"><image class="radio-img" src="/static/img/checkd.png"></image></view></view></block></block></block><block wx:if="{{baidupay==1}}"><view class="item" data-typeid="11" data-event-opts="{{[['tap',[['changeradio',['$event']]]]]}}" catchtap="__e"><view class="t1"><image class="img" src="/static/img/pay-money.png"></image>在线支付</view><view class="radio" style="{{(typeid=='11'?'background:'+$root.m39+';border:0':'')}}"><image class="radio-img" src="/static/img/checkd.png"></image></view></view></block><block wx:if="{{toutiaopay==1}}"><view class="item" data-typeid="12" data-event-opts="{{[['tap',[['changeradio',['$event']]]]]}}" catchtap="__e"><view class="t1"><image class="img" src="/static/img/pay-money.png"></image>在线支付</view><view class="radio" style="{{(typeid=='12'?'background:'+$root.m40+';border:0':'')}}"><image class="radio-img" src="/static/img/checkd.png"></image></view></view></block><block wx:if="{{moneypay==1}}"><view class="item" data-typeid="1" data-event-opts="{{[['tap',[['changeradio',['$event']]]]]}}" catchtap="__e"><view class="t1 flex"><image class="img" src="/static/img/pay-money.png"></image><view class="flex-col"><text>{{$root.m41+"支付555"}}</text><text style="font-size:22rpx;font-weight:normal;">可用余额<text style="color:#FC5729;">{{"￥"+userinfo.money}}</text></text></view></view><view class="radio" style="{{(typeid=='1'?'background:'+$root.m42+';border:0':'')}}"><image class="radio-img" src="/static/img/checkd.png"></image></view></view></block><block wx:if="{{heipay==1}}"><view class="item" data-typeid="1" data-event-opts="{{[['tap',[['changeradio',['$event']]]]]}}" catchtap="__e"><view class="t1 flex"><image class="img" src="/static/img/pay-money.png"></image><view class="flex-col"><text>{{$root.m43+"支付"}}</text><text style="font-size:22rpx;font-weight:normal;">可用抵扣<text style="color:#FC5729;">{{"￥"+userinfo.heiscore}}</text></text></view></view><view class="radio" style="{{(typeid=='1'?'background:'+$root.m44+';border:0':'')}}"><image class="radio-img" src="/static/img/checkd.png"></image></view></view></block></view></view><view style="overflow:hidden;width:100%;"><view style="width:300rpx;float:left;"><view><button data-event-opts="{{[['tap',[['close_pay',['$event']]]]]}}" class="btn" style="margin-bottom:20rpx;background-color:#999;" bindtap="__e">取消</button></view></view><view style="width:300rpx;float:right;"><view><block wx:if="{{typeid!='0'}}"><button data-event-opts="{{[['tap',[['topay',['$event']]]]]}}" class="btn" style="{{'margin-bottom:20rpx;'+('background:'+($root.m45)+';')}}" bindtap="__e">确定</button></block></view></view></view></view></block></block></block><block wx:if="{{invite_status&&invite_free}}"><view data-event-opts="{{[['tap',[['closeInvite',['$event']]]]]}}" style="width:100%;height:100%;background-color:#000;position:fixed;opacity:0.5;z-index:99;top:0;" bindtap="__e"></view></block><block wx:if="{{invite_status&&invite_free}}"><view style="width:700rpx;margin:0 auto;position:fixed;top:10%;left:25rpx;z-index:100;"><view data-event-opts="{{[['tap',[['gotoInvite',['$event']]]]]}}" style="background-color:#fff;border-radius:20rpx;overflow:hidden;width:100%;min-height:700rpx;" bindtap="__e"><image style="width:100%;height:auto;" src="{{invite_free.pic}}" mode="widthFix"></image></view><block wx:if="{{invite_status&&invite_free}}"><view data-event-opts="{{[['tap',[['closeInvite',['$event']]]]]}}" style="width:80rpx;height:80rpx;line-height:80rpx;text-align:center;font-size:30rpx;background-color:#fff;margin:0 auto;border-radius:50%;margin-top:20rpx;" bindtap="__e">X</view></block></view></block><block wx:if="{{loading}}"><loading vue-id="2d4d2654-6" bind:__l="__l"></loading></block><dp-tabbar vue-id="2d4d2654-7" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="2d4d2654-8" data-ref="popmsg" bind:__l="__l"></popmsg></view>