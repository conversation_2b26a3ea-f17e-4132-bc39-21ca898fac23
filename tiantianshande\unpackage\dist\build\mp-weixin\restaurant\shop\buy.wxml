<view class="container"><block wx:if="{{isload}}"><block><view class="address-add flex-y-center"><view class="f1">桌台信息</view><block wx:if="{{tableinfo.id}}"><view class="f2 flex1"><view style="font-weight:bold;color:#111111;font-size:30rpx;">{{tableinfo.name}}<text style="font-size:24rpx;font-weight:normal;color:#666;margin-left:10rpx;">{{tableinfo.seat+"人桌"}}</text></view></view></block><block wx:else><view class="f2 flex1">请扫描桌台二维码</view></block><image class="f3" src="/static/img/arrowright.png"></image></view><block wx:for="{{$root.l1}}" wx:for-item="buydata" wx:for-index="index" wx:key="index"><view class="buydata"><view class="btitle"><image class="img" src="/static/img/ico-shop.png"></image>{{buydata.$orig.business.name}}</view><view class="bcontent"><view class="product"><block wx:for="{{buydata.l0}}" wx:for-item="item" wx:for-index="index2" wx:key="index2"><view class="item flex"><view class="img" data-url="{{'product?id='+item.$orig.product.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image src="{{item.$orig.product.pic}}"></image></view><view class="info flex1"><view class="f1">{{item.$orig.product.name}}</view><view class="f2">{{"规格："+item.$orig.guige.name+item.$orig.jldata.jltitle}}</view><view class="f3"><text style="font-weight:bold;">{{"￥"+item.g0}}</text><text style="padding-left:20rpx;">{{'× '+item.$orig.num}}</text></view></view></view></block></view><block wx:if="{{ordertype=='create_order'}}"><view class="price"><text class="f1">就餐人数</text><text class="f2" data-bid="{{buydata.$orig.bid}}" data-event-opts="{{[['tap',[['showRenshuSelect',['$event']]]]]}}" bindtap="__e">{{buydata.$orig.renshu>0?buydata.$orig.renshu+'人':'请选择人数'}}<text class="iconfont iconjiantou" style="color:#999;font-weight:normal;"></text></text></view></block><view class="price"><text class="f1">菜品金额</text><text class="f2">{{"¥"+buydata.$orig.product_price}}</text></view><block wx:if="{{buydata.$orig.leveldk_money>0}}"><view class="price"><text class="f1">{{buydata.m0+"折扣("+userinfo.discount+"折)"}}</text><text class="f2">{{"-¥"+buydata.$orig.leveldk_money}}</text></view></block><block wx:if="{{buydata.$orig.manjian_money>0}}"><view class="price"><text class="f1">满减活动</text><text class="f2">{{"-¥"+buydata.$orig.manjian_money}}</text></view></block><block wx:if="{{ordertype=='create_order'}}"><view class="price"><text class="f1">{{buydata.$orig.tea_fee_text}}</text><text class="f2">{{"+¥"+buydata.$orig.tea_fee*(buydata.$orig.renshu>0?buydata.$orig.renshu:1)}}</text></view></block><block wx:if="{{ordertype=='create_order'}}"><view class="price"><view class="f1">{{buydata.m1}}</view><block wx:if="{{buydata.$orig.couponCount>0}}"><view class="f2" data-bid="{{buydata.$orig.bid}}" data-event-opts="{{[['tap',[['showCouponList',['$event']]]]]}}" bindtap="__e"><text style="{{'color:#fff;padding:4rpx 16rpx;font-weight:normal;border-radius:8rpx;font-size:24rpx;'+('background:'+(buydata.m2)+';')}}">{{buydata.$orig.couponrid!=0?buydata.$orig.couponList[buydata.$orig.couponkey].couponname:buydata.$orig.couponCount+'张可用'}}</text><text class="iconfont iconjiantou" style="color:#999;font-weight:normal;"></text></view></block><block wx:else><text class="f2" style="color:#999;">{{"无可用"+buydata.m3}}</text></block></view></block><block wx:if="{{ordertype=='create_order'&&buydata.$orig.cuxiaoCount>0}}"><view class="price"><view class="f1">促销活动</view><view class="f2" data-bid="{{buydata.$orig.bid}}" data-event-opts="{{[['tap',[['showCuxiaoList',['$event']]]]]}}" bindtap="__e"><text style="{{'color:#fff;padding:4rpx 16rpx;font-weight:normal;border-radius:8rpx;font-size:24rpx;'+('background:'+(buydata.m4)+';')}}">{{buydata.$orig.cuxiaoname?buydata.$orig.cuxiaoname:buydata.$orig.cuxiaoCount+'个可用'}}</text><text class="iconfont iconjiantou" style="color:#999;font-weight:normal;"></text></view></view></block><view class="remark"><text class="f1">备注</text><input class="flex1" type="text" placeholder="请输入您的口味或要求" data-field="message" data-bid="{{buydata.$orig.bid}}" placeholder-style="color:#cdcdcd;font-size:28rpx" data-event-opts="{{[['input',[['inputfield',['$event']]]]]}}" bindinput="__e"/></view></view></view></block><block wx:if="{{ordertype=='create_order'&&userinfo.score2money>0&&(userinfo.scoremaxtype==0||userinfo.scoremaxtype==1&&userinfo.scoredkmaxmoney>0)}}"><view class="scoredk"><checkbox-group data-event-opts="{{[['change',[['scoredk',['$event']]]]]}}" class="flex" style="width:100%;" bindchange="__e"><view class="f1"><view>{{userinfo.score*1+" "+$root.m5+'可抵扣'}}<text style="color:#e94745;">{{userinfo.scoredk_money*1}}</text>元</view><block wx:if="{{userinfo.scoremaxtype==0&&userinfo.scoredkmaxpercent>0&&userinfo.scoredkmaxpercent<100}}"><view style="font-size:22rpx;color:#999;">{{"最多可抵扣订单金额的"+userinfo.scoredkmaxpercent+"%"}}</view></block><block wx:else><block wx:if="{{userinfo.scoremaxtype==1}}"><view style="font-size:22rpx;color:#999;">{{"最多可抵扣"+userinfo.scoredkmaxmoney+"元"}}</view></block></block></view><view class="f2">{{"使用"+$root.m6+'抵扣'}}<checkbox style="margin-left:6px;transform:scale(.8);" value="1"></checkbox></view></checkbox-group></view></block><view style="width:100%;height:110rpx;"></view><view class="footer flex"><view class="text1 flex1">总计：<text style="font-weight:bold;font-size:36rpx;">{{"￥"+alltotalprice}}</text></view><view data-event-opts="{{[['tap',[['topay',['$event']]]]]}}" class="op" style="{{'background:'+('linear-gradient(-90deg,'+$root.m7+' 0%,rgba('+$root.m8+',0.8) 100%)')+';'}}" bindtap="__e">提交订单</view></view><block wx:if="{{couponvisible}}"><view class="popup__container"><view data-event-opts="{{[['tap',[['handleClickMask',['$event']]]]]}}" class="popup__overlay" catchtap="__e"></view><view class="popup__modal"><view class="popup__title"><text class="popup__title-text">{{"请选择"+$root.m9}}</text><image class="popup__close" style="width:36rpx;height:36rpx;" src="/static/img/close.png" data-event-opts="{{[['tap',[['handleClickMask',['$event']]]]]}}" catchtap="__e"></image></view><view class="popup__content"><couponlist vue-id="33642c45-1" couponlist="{{allbuydata[bid].couponList}}" choosecoupon="{{true}}" selectedrid="{{allbuydata[bid].couponrid}}" bid="{{bid}}" data-event-opts="{{[['^chooseCoupon',[['chooseCoupon']]]]}}" bind:chooseCoupon="__e" bind:__l="__l"></couponlist></view></view></view></block><block wx:if="{{cuxiaovisible}}"><view class="popup__container"><view data-event-opts="{{[['tap',[['handleClickMask',['$event']]]]]}}" class="popup__overlay" catchtap="__e"></view><view class="popup__modal"><view class="popup__title"><text class="popup__title-text">优惠促销</text><image class="popup__close" style="width:36rpx;height:36rpx;" src="/static/img/close.png" data-event-opts="{{[['tap',[['handleClickMask',['$event']]]]]}}" catchtap="__e"></image></view><view class="popup__content"><view class="cuxiao-desc"><view class="cuxiao-item" data-id="0" data-event-opts="{{[['tap',[['changecx',['$event']]]]]}}" bindtap="__e"><view class="type-name"><text style="color:#333;">不使用促销</text></view><view class="radio" style="{{(cxid==0?'background:'+$root.m10+';border:0':'')}}"><image class="radio-img" src="/static/img/checkd.png"></image></view></view><block wx:for="{{$root.l2}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="cuxiao-item" data-id="{{item.$orig.id}}" data-event-opts="{{[['tap',[['changecx',['$event']]]]]}}" bindtap="__e"><view class="type-name"><text style="border-radius:4px;border:1px solid #f05423;color:#ff550f;font-size:20rpx;padding:2px 5px;">{{item.$orig.tip}}</text><text style="color:#333;padding-left:20rpx;">{{item.$orig.name}}</text></view><view class="radio" style="{{(cxid==item.$orig.id?'background:'+item.m11+';border:0':'')}}"><image class="radio-img" src="/static/img/checkd.png"></image></view></view></block></view><block wx:if="{{cuxiaoinfo.product}}"><view style="padding:0 40rpx;" id="cxproinfo"><view class="product"><view class="item flex" style="background:#f5f5f5;"><view class="img" data-url="{{'product?id='+cuxiaoinfo.product.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image src="{{cuxiaoinfo.product.pic}}"></image></view><view class="info flex1"><view class="f1">{{cuxiaoinfo.product.name}}</view><view class="f2">{{"规格："+cuxiaoinfo.guige.name}}</view><view class="f3"><text style="font-weight:bold;">{{"￥"+cuxiaoinfo.guige.sell_price}}</text><text style="padding-left:20rpx;">× 1</text></view></view></view></view></view></block><view style="width:100%;height:120rpx;"></view><view style="width:100%;position:absolute;bottom:0;padding:20rpx 5%;background:#fff;"><view data-event-opts="{{[['tap',[['chooseCuxiao',['$event']]]]]}}" style="{{'width:100%;height:80rpx;line-height:80rpx;border-radius:40rpx;text-align:center;color:#fff;'+('background:'+($root.m12)+';')}}" bindtap="__e">确 定</view></view></view></view></view></block><block wx:if="{{renshuvisible}}"><view class="popup__container"><view data-event-opts="{{[['tap',[['handleClickMask',['$event']]]]]}}" class="popup__overlay" catchtap="__e"></view><view class="popup__modal"><view class="popup__title"><text class="popup__title-text">请选择就餐人数</text><image class="popup__close" style="width:36rpx;height:36rpx;" src="/static/img/close.png" data-event-opts="{{[['tap',[['handleClickMask',['$event']]]]]}}" catchtap="__e"></image></view><view class="popup__content"><view class="cuxiao-desc"><block wx:for="{{$root.l3}}" wx:for-item="item" wx:for-index="__i0__" wx:key="*this"><view class="cuxiao-item" data-id="{{item.$orig}}" data-event-opts="{{[['tap',[['changerenshu',['$event']]]]]}}" bindtap="__e"><view class="type-name"><text style="color:#333;">{{item.$orig+"人"}}</text></view><view class="radio" style="{{(renshu==item.$orig?'background:'+item.m13+';border:0':'')}}"><image class="radio-img" src="/static/img/checkd.png"></image></view></view></block></view><view style="width:100%;height:120rpx;"></view><view style="width:100%;position:absolute;bottom:0;padding:20rpx 5%;background:#fff;"><view data-event-opts="{{[['tap',[['chooseRenshu',['$event']]]]]}}" style="{{'width:100%;height:80rpx;line-height:80rpx;border-radius:40rpx;text-align:center;color:#fff;'+('background:'+($root.m14)+';')}}" bindtap="__e">确 定</view></view></view></view></view></block></block></block><block wx:if="{{loading}}"><loading vue-id="33642c45-2" bind:__l="__l"></loading></block><dp-tabbar vue-id="33642c45-3" opt="{{opt}}" bind:__l="__l"></dp-tabbar></view>