<view><block wx:if="{{isload}}"><block><view class="wrap" style="{{'background:'+($root.m0)+';'}}"><view class="top flex"><view class="f1"><text class="t1">{{tkdata.ishg==1?'合格':'不合格'}}</text><text class="t2">{{(tkdata.ishg==1?'恭喜您，已通过考试':'请认真学习后再来试一下吧~')+''}}</text></view><view class="score"><text class="t1">{{tkdata.score}}</text><text class="t2">分</text></view></view><view class="content"><view class="c_1 flex"><view class="f2"><text class="t1">{{tkdata.rightnum}}</text><text class="t2">答对题目</text></view><view class="f2"><text class="t1">{{tkdata.errornum}}</text><text class="t2">答错题目</text></view></view><view class="c_2"><view class="list1"><text class="t3">答题时间</text><text class="t4">{{tkdata.time}}</text></view><view class="list1"><text class="t3">提交时间</text><text class="t4">{{tkdata.endtime}}</text></view><view class="list1"><text class="t3">答题用时</text><text class="t4">{{tkdata.longtime}}</text></view></view><block wx:if="{{tkdata.ishg==1}}"><view class="aginbtn" style="{{'background:'+($root.m1)+';'}}" data-url="{{'recordlog?kcid='+tkdata.kcid}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">答题记录</view></block><block wx:if="{{tkdata.ishg!=1}}"><view class="aginbtn" style="{{'background:'+($root.m2)+';'}}" data-url="{{'tiku?id='+tkdata.kcid}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">再答一次</view></block><view class="bottom flex"><block wx:if="{{tkdata.ishg!=1}}"><view class="btn2" style="{{('width:100%')}}" data-url="{{'product?id='+tkdata.kcid}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">继续学习</view></block><block wx:if="{{tkdata.ishg==1&&tkdata.errornum>0}}"><view class="btn3" style="{{('margin-left:0;width:100%')}}" data-url="{{'error?rid='+tkdata.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">错题回顾</view></block><block wx:if="{{tkdata.ishg!=1&&tkdata.errornum>0}}"><view class="btn3" style="{{('width:100%')}}" data-url="{{'error?rid='+tkdata.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">错题回顾</view></block></view></view><view style="height:130rpx;"></view></view></block></block><block wx:if="{{loading}}"><loading vue-id="1d27b3d6-1" bind:__l="__l"></loading></block><dp-tabbar vue-id="1d27b3d6-2" opt="{{opt}}" data-event-opts="{{[['^getmenuindex',[['getmenuindex']]]]}}" bind:getmenuindex="__e" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="1d27b3d6-3" data-ref="popmsg" bind:__l="__l"></popmsg></view>