<view class="container"><block wx:if="{{isload}}"><block><view class="view-show"><view class="search-container" data-url="/activity/kecheng/list" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e"><view class="search-box"><image class="img" src="/static/img/search_ico.png"></image><view class="search-text">搜索感兴趣的课程</view></view></view><scroll-view class="top-category" scroll-x="true" scrollWithAnimation="{{animation}}"><block wx:for="{{data}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="{{['top-category-item',index===currentActiveIndex?'top-active':'']}}" data-root-item-id="{{item.id}}" data-root-item-index="{{index}}" data-event-opts="{{[['tap',[['clickRootItem',['$event']]]]]}}" bindtap="__e">{{''+item.name+''}}</view></block></scroll-view><view class="content" style="height:calc(100% - 94rpx - 80rpx);overflow:hidden;display:flex;"><scroll-view class="{{['nav_left',menuindex>-1?'tabbarbot':'notabbarbot']}}" scrollWithAnimation="{{animation}}" scroll-y="true"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><view class="{{['nav_left_items',index===currentLevel2Index?'active':'']}}" data-item-id="{{item.$orig.id}}" data-item-index="{{index}}" data-event-opts="{{[['tap',[['clickLevel2Item',['$event']]]]]}}" bindtap="__e"><view class="before" style="{{'background:'+(item.m0)+';'}}"></view>{{item.$orig.name}}</view></block></block></scroll-view><view class="nav_right"><view class="nav_right-content"><scroll-view class="{{['detail-list',menuindex>-1?'tabbarbot':'notabbarbot']}}" scrollWithAnimation="{{animation}}" scroll-y="true" data-event-opts="{{[['scroll',[['scroll',['$event']]]]]}}" bindscroll="__e"><block wx:for="{{clist}}" wx:for-item="detail" wx:for-index="index" wx:key="index"><view class="classification-detail-item"><view class="head" data-id="{{detail.id}}" id="{{'detail-'+detail.id}}"><view class="txt">{{detail.name}}</view><view class="show-all" data-key="{{index}}" data-id="{{detail.id}}" data-event-opts="{{[['tap',[['hideshow',['$event']]]]]}}" bindtap="__e">{{detail.ishide!=1?'收起':'展开'}}</view></view><block wx:if="{{detail.ishide!=1}}"><view class="detail"><block wx:for="{{detail.child}}" wx:for-item="item" wx:for-index="itemIndex" wx:key="itemIndex"><view class="detail-item" style="{{((itemIndex+1)%3===0?'margin-right: 0':'')}}" data-url="{{'/activity/kecheng/list?cid='+item.id}}" data-id="{{item.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e"><image class="img" src="{{item.pic}}"></image><view class="txt">{{item.name}}</view></view></block></view></block></view></block><block wx:if="{{nodata}}"><nodata vue-id="6ad3629a-1" bind:__l="__l"></nodata></block></scroll-view></view></view></view></view></block></block><view style="display:none;">{{test}}</view><block wx:if="{{loading}}"><loading vue-id="6ad3629a-2" loadstyle="left:62.5%" bind:__l="__l"></loading></block><dp-tabbar vue-id="6ad3629a-3" opt="{{opt}}" data-event-opts="{{[['^getmenuindex',[['getmenuindex']]]]}}" bind:getmenuindex="__e" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="6ad3629a-4" data-ref="popmsg" bind:__l="__l"></popmsg></view>