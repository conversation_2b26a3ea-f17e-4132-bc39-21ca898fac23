<view class="container"><block wx:if="{{isload}}"><block><image class="background-image" src="{{settings.background_image||defaultBg}}"></image><view class="background-overlay"></view><view class="header"><text class="title">上香供奉许愿</text><text class="subtitle">虔诚祈福，心愿成真</text></view><view class="floating-buttons"><view data-event-opts="{{[['tap',[['goToMyWishes',['$event']]]]]}}" class="floating-btn" bindtap="__e"><image class="btn-icon" src="{{pre_url+'/static/img/my-wishes.png'}}"></image><text class="btn-text">许愿记录</text></view></view><block wx:if="{{settings.description}}"><view class="description"><text>{{settings.description}}</text></view></block><view class="bottom-function-container"><view class="function-grid"><view class="function-item" data-type="gonghua" data-event-opts="{{[['tap',[['selectFunction',['$event']]]]]}}" bindtap="__e"><view class="function-icon-wrapper"><image class="function-icon" src="{{pre_url+'/static/img/gonghua-icon.png'}}" mode="aspectFit"></image></view><text class="function-name">供花</text><text class="function-price">{{"￥"+settings.gonghua_price}}</text></view><view class="function-item" data-type="gongguo" data-event-opts="{{[['tap',[['selectFunction',['$event']]]]]}}" bindtap="__e"><view class="function-icon-wrapper"><image class="function-icon" src="{{pre_url+'/static/img/gongguo-icon.png'}}" mode="aspectFit"></image></view><text class="function-name">供果</text><text class="function-price">{{"￥"+settings.gongguo_price}}</text></view><view class="function-item" data-type="shangxiang" data-event-opts="{{[['tap',[['selectFunction',['$event']]]]]}}" bindtap="__e"><view class="function-icon-wrapper"><image class="function-icon" src="{{pre_url+'/static/img/shangxiang-icon.png'}}" mode="aspectFit"></image></view><text class="function-name">上香</text><text class="function-price">{{"￥"+settings.shangxiang_price}}</text></view><view class="function-item" data-type="xuyuan" data-event-opts="{{[['tap',[['selectFunction',['$event']]]]]}}" bindtap="__e"><view class="function-icon-wrapper"><image class="function-icon" src="{{pre_url+'/static/img/xuyuan-icon.png'}}" mode="aspectFit"></image></view><text class="function-name">许愿</text><text class="function-price">{{"￥"+settings.xuyuan_price}}</text></view></view></view><block wx:if="{{showModal}}"><view data-event-opts="{{[['tap',[['closeModal',['$event']]]]]}}" class="modal-overlay" bindtap="__e"><view data-event-opts="{{[['tap',[['',['$event']]]]]}}" class="modal-content" catchtap="__e"><view class="modal-header"><text class="modal-title">{{$root.m0}}</text><view data-event-opts="{{[['tap',[['closeModal',['$event']]]]]}}" class="close-btn" bindtap="__e">×</view></view><view class="amount-section"><text class="section-label">选择金额</text><view class="amount-options"><view class="{{['amount-item',(selectedAmount==$root.m1)?'active':'']}}" data-amount="{{$root.m2}}" data-event-opts="{{[['tap',[['selectAmount',['$event']]]]]}}" bindtap="__e"><text>{{"￥"+$root.m3}}</text></view><view class="{{['amount-item',(selectedAmount==$root.m4*2)?'active':'']}}" data-amount="{{$root.m5*2}}" data-event-opts="{{[['tap',[['selectAmount',['$event']]]]]}}" bindtap="__e"><text>{{"￥"+$root.m6*2}}</text></view><view class="{{['amount-item',(selectedAmount==$root.m7*5)?'active':'']}}" data-amount="{{$root.m8*5}}" data-event-opts="{{[['tap',[['selectAmount',['$event']]]]]}}" bindtap="__e"><text>{{"￥"+$root.m9*5}}</text></view></view><block wx:if="{{settings.allow_custom_amount}}"><view class="custom-amount"><text class="custom-label">自定义金额</text><input class="custom-input" type="digit" placeholder="请输入金额" data-event-opts="{{[['input',[['__set_model',['','customAmount','$event',[]]],['onCustomAmountInput',['$event']]]]]}}" value="{{customAmount}}" bindinput="__e"/></view></block></view><block wx:if="{{selectedType=='xuyuan'}}"><view class="wish-section"><text class="section-label">许愿内容</text><textarea class="wish-input" placeholder="请输入您的心愿..." maxlength="200" data-event-opts="{{[['input',[['__set_model',['','wishContent','$event',[]]]]]]}}" value="{{wishContent}}" bindinput="__e"></textarea><text class="char-count">{{$root.g0+"/200"}}</text></view></block><view class="modal-footer"><button data-event-opts="{{[['tap',[['confirmSubmit',['$event']]]]]}}" class="confirm-btn" bindtap="__e">{{"确认"+$root.m10}}</button></view></view></view></block></block></block><block wx:if="{{nodata}}"><nodata vue-id="573faa6c-1" bind:__l="__l"></nodata></block><block wx:if="{{loading}}"><loading vue-id="573faa6c-2" bind:__l="__l"></loading></block><popmsg class="vue-ref" vue-id="573faa6c-3" data-ref="popmsg" bind:__l="__l"></popmsg></view>