<view class="container"><block wx:if="{{isload}}"><block><view class="topfix"><view class="toplabel"><text class="t1">{{"分红订单（"+count+"）"}}</text><text class="t2">{{"预计：+"+commissionyj+"元"}}</text></view><dd-tab vue-id="39b6ed34-1" itemdata="{{['待结算','已结算']}}" itemst="{{['1','2']}}" st="{{st}}" isfixed="{{false}}" data-event-opts="{{[['^changetab',[['changetab']]]]}}" bind:changetab="__e" bind:__l="__l"></dd-tab></view><view style="margin-top:190rpx;"></view><block wx:if="{{$root.g0}}"><block><view class="content"><block wx:if="{{st==1}}"><block><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="item"><view class="f1"><text>{{item.$orig.ordernum}}</text></view><view class="f2"><view class="t1"><text class="x1">{{item.$orig.name+" ×"+item.$orig.num}}</text><text class="x2" style="{{'font-size:28rpx;'+('color:'+(item.m0)+';')}}">{{"￥"+item.$orig.real_totalprice}}</text><text class="x2">{{item.m1}}</text><view class="x3"><image src="{{item.$orig.headimg}}"></image>{{item.$orig.nickname}}</view></view><view class="t2"><text class="x1">{{"+"+item.$orig.commission}}</text><block wx:if="{{item.$orig.status==1||item.$orig.status==2}}"><text class="dior-sp6 yfk">已付款</text></block><block wx:if="{{item.$orig.status==0}}"><text class="dior-sp6 dfk">待付款</text></block><block wx:if="{{item.$orig.status==3}}"><text class="dior-sp6 ywc">已完成</text></block><block wx:if="{{item.$orig.status==4}}"><text class="dior-sp6 ygb">已关闭</text></block><block wx:if="{{item.$orig.refund_money>0}}"><text class="dior-sp6 ygb">退款/售后</text></block></view></view></view></block></block></block><block wx:if="{{st==2}}"><block><block wx:for="{{$root.l2}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="item"><view class="f1"><text>{{item.$orig.remark+" "+item.m2}}</text></view><view class="f2"><view class="t1"><block wx:for="{{item.l1}}" wx:for-item="item2" wx:for-index="index" wx:key="index"><view class="item2"><text class="x1">{{item2.$orig.name+" ×"+item2.$orig.num}}</text><text class="x2" style="{{'font-size:28rpx;'+('color:'+(item.m3)+';')}}">{{"￥"+item2.$orig.real_totalprice}}</text><text class="x2">{{item2.m4}}</text><view class="x3"><image src="{{item2.$orig.headimg}}"></image>{{item2.$orig.nickname}}</view></view></block></view><view class="t2"><text class="x1">{{"+"+item.$orig.commission}}</text></view></view></view></block></block></block></view></block></block><view style="width:100%;height:20rpx;"></view></block></block><block wx:if="{{nodata}}"><nodata vue-id="39b6ed34-2" bind:__l="__l"></nodata></block><block wx:if="{{nomore}}"><nomore vue-id="39b6ed34-3" bind:__l="__l"></nomore></block><block wx:if="{{loading}}"><loading vue-id="39b6ed34-4" bind:__l="__l"></loading></block><dp-tabbar vue-id="39b6ed34-5" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="39b6ed34-6" data-ref="popmsg" bind:__l="__l"></popmsg></view>