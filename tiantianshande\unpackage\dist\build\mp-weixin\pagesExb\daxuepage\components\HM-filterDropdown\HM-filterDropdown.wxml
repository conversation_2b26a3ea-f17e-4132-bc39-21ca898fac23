<view data-event-opts="{{[['touchmove',[['discard',['$event']]]],['tap',[['discard',['$event']]]]]}}" class="{{['HMfilterDropdown',(maskVisibility)?'setDropdownBottom':'']}}" style="{{'top:'+(menuTop+'rpx')+';'}}" catchtouchmove="__e" catchtap="__e"><view class="nav"><block wx:for="{{menu}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><view data-event-opts="{{[['tap',[['togglePage',[index]]]]]}}" class="{{['first-menu',(showPage==index)?'on':'']}}" bindtap="__e"><text class="name">{{item.name}}</text><text class="iconfont triangle" style="{{('transform:rotate('+triangleDeg[index]+'deg);')}}"></text></view></block></block><slot name="body"></slot></view><view data-event-opts="{{[['tap',[['hideMenu',[false]]]]]}}" class="{{['mask',(isShowMask)?'show':'',(maskVisibility!=true)?'hide':'']}}" bindtap="__e"></view><block wx:for="{{$root.l6}}" wx:for-item="page" wx:for-index="page_index" wx:key="page_index"><block><view class="{{['sub-menu-class',(showPage==page_index)?'show':'',(pageState[page_index]!=true)?'hide':'']}}"><block wx:if="{{page.g0}}"><block><scroll-view class="{{['sub-menu-list',(page.g1>1)?'first':'',(page.g2<=1)?'alone':'']}}" scroll-y="{{true}}" scroll-into-view="{{'first_id'+firstScrollInto}}"><block wx:for="{{page.$orig.submenu}}" wx:for-item="sub" wx:for-index="index" wx:key="value"><block><view class="{{['sub-menu',(activeMenuArr[page_index][0]==index)?'on':'']}}" id="{{'first_id'+index}}" data-event-opts="{{[['tap',[['selectHierarchyMenu',[page_index,index,null,null]]]]]}}" bindtap="__e"><view class="menu-name"><text>{{sub.name}}</text><text class="iconfont selected"></text></view></view></block></block></scroll-view><block wx:if="{{page.$orig.type=='hierarchy'}}"><block><block wx:for="{{page.l2}}" wx:for-item="sub" wx:for-index="index" wx:key="value"><block><block wx:if="{{sub.g3}}"><scroll-view class="sub-menu-list not-first" scroll-y="{{true}}" scroll-into-view="{{'second_id'+secondScrollInto}}"><block wx:for="{{sub.l1}}" wx:for-item="sub_second" wx:for-index="second_index" wx:key="value"><block><view class="{{['sub-menu',(activeMenuArr[page_index][1]==second_index)?'on':'']}}" id="{{'second_id'+second_index}}"><view data-event-opts="{{[['tap',[['selectHierarchyMenu',[page_index,'$0',second_index,null],['activeMenuArr.'+page_index+'.__$n0']]]]]}}" class="menu-name" bindtap="__e"><text>{{sub_second.$orig.name}}</text><text class="iconfont selected"></text></view><block wx:if="{{sub_second.g4}}"><view class="more-sub-menu"><block wx:for="{{sub_second.l0}}" wx:for-item="sub2" wx:for-index="sub2_index" wx:key="value"><block><block wx:if="{{sub_second.$orig.showAllSub||sub2_index<8}}"><text data-event-opts="{{[['tap',[['selectHierarchyMenu',[page_index,'$0',second_index,sub2_index],['activeMenuArr.'+page_index+'.__$n0']]]]]}}" class="{{[(activeMenuArr[page_index][1]==second_index&&activeMenuArr[page_index][2]==sub2_index)?'on':'']}}" catchtap="__e">{{sub2.$orig.name}}</text></block><block wx:if="{{sub2.g5}}"><text data-event-opts="{{[['tap',[['showMoreSub',[second_index]]]]]}}" catchtap="__e">更多<text class="iconfont triangle"></text></text></block></block></block></view></block></view></block></block></scroll-view></block></block></block></block></block><block wx:else><block wx:if="{{page.$orig.type=='hierarchy-column'}}"><block><block wx:for="{{page.l3}}" wx:for-item="sub" wx:for-index="index" wx:key="index"><block><block wx:if="{{sub.g6}}"><scroll-view class="sub-menu-list not-first" scroll-y="{{true}}" scroll-into-view="{{'second_id'+secondScrollInto}}"><block wx:for="{{sub.$orig.submenu}}" wx:for-item="sub_second" wx:for-index="second_index" wx:key="second_index"><block><view class="{{['sub-menu',(activeMenuArr[page_index][1]==second_index)?'on':'']}}" id="{{'second_id'+second_index}}"><view data-event-opts="{{[['tap',[['selectHierarchyMenu',[page_index,'$0',second_index,null],['activeMenuArr.'+page_index+'.__$n0']]]]]}}" class="menu-name" bindtap="__e"><text>{{sub_second.name}}</text></view></view></block></block></scroll-view></block></block></block><block wx:for="{{page.l5}}" wx:for-item="sub" wx:for-index="index"><block><block wx:for="{{sub.l4}}" wx:for-item="sub_second" wx:for-index="second_index"><block><block wx:if="{{sub_second.g7}}"><scroll-view class="sub-menu-list not-first third" scroll-y="{{true}}" scroll-into-view="{{'third_id'+thirdScrollInto}}"><block wx:for="{{sub_second.$orig.submenu}}" wx:for-item="sub2" wx:for-index="sub2_index" wx:key="sub2_index"><block><view class="{{['sub-menu',(activeMenuArr[page_index][2]==sub2_index)?'on':'']}}" id="{{'third_id'+sub2_index}}"><view data-event-opts="{{[['tap',[['selectHierarchyMenu',[page_index,'$0',second_index,sub2_index],['activeMenuArr.'+page_index+'.__$n0']]]]]}}" class="menu-name" bindtap="__e"><text>{{sub2.name}}</text></view></view></block></block></scroll-view></block></block></block></block></block></block></block></block></block></block><block wx:if="{{page.$orig.type=='filter'}}"><block><view class="filter"><scroll-view class="menu-box" scroll-y="{{true}}"><block wx:for="{{page.$orig.submenu}}" wx:for-item="box" wx:for-index="box_index" wx:key="box_index"><view class="box"><view class="title">{{box.name}}</view><view class="labels"><block wx:for="{{box.submenu}}" wx:for-item="label" wx:for-index="label_index" wx:key="label_index"><view data-event-opts="{{[['tap',[['selectFilterLabel',[page_index,box_index,label_index]]]]]}}" class="{{[(label.selected)?'on':'']}}" bindtap="__e">{{label.name}}</view></block></view></view></block></scroll-view><view class="btn-box"><view data-event-opts="{{[['tap',[['resetFilterData',[page_index]]]]]}}" class="reset" bindtap="__e">重置</view><view data-event-opts="{{[['tap',[['setFilterData',[page_index]]]]]}}" class="submit" bindtap="__e">确定</view></view></view></block></block><block wx:if="{{page.$orig.type=='radio'}}"><block><view class="filter"><scroll-view class="menu-box" scroll-y="{{true}}"><block wx:for="{{page.$orig.submenu}}" wx:for-item="box" wx:for-index="box_index" wx:key="box_index"><view class="box"><view class="title">{{box.name}}</view><view class="labels"><block wx:for="{{box.submenu}}" wx:for-item="label" wx:for-index="label_index" wx:key="label_index"><view data-event-opts="{{[['tap',[['selectRadioLabel',[page_index,box_index,label_index]]]]]}}" class="{{[(label.selected)?'on':'']}}" bindtap="__e">{{label.name}}</view></block></view></view></block></scroll-view><view class="btn-box"><view data-event-opts="{{[['tap',[['resetFilterData',[page_index]]]]]}}" class="reset" bindtap="__e">重置</view><view data-event-opts="{{[['tap',[['setFilterData',[page_index]]]]]}}" class="submit" bindtap="__e">确定</view></view></view></block></block></view></block></block></view>