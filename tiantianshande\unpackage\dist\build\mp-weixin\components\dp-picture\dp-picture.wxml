<view class="dp-picture" style="{{'background-color:'+(params.bgcolor)+';'+('margin:'+(params.margin_y*2.2+'rpx '+params.margin_x*2.2+'rpx 0')+';')+('padding:'+(params.padding_y*2.2+'rpx '+params.padding_x*2.2+'rpx')+';')}}"><block wx:for="{{data}}" wx:for-item="item" wx:for-index="index" wx:key="id"><view data-url="{{item.hrefurl}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><block wx:if="{{item.hrefurl=='contact::'}}"><button class="picture-button" open-type="contact"><image class="picture-img" style="{{'border-radius:'+(params.borderradius*2.2+'rpx')+';'}}" src="{{item.imgurl}}" mode="widthFix"></image></button></block><block wx:else><block wx:if="{{item.hrefurl=='share::'}}"><button class="picture-button" open-type="share"><image class="picture-img" style="{{'border-radius:'+(params.borderradius*2.2+'rpx')+';'}}" src="{{item.imgurl}}" mode="widthFix"></image></button></block><block wx:else><image class="picture-img" style="{{'border-radius:'+(params.borderradius*2.2+'rpx')+';'}}" src="{{item.imgurl}}" mode="widthFix"></image></block></block></view></block></view>