<?php /*a:4:{s:80:"D:\qianhouduankaifabao\shangchengquan\shangcheng\app\home\coze\workflow_log.html";i:1754026608;s:73:"D:\qianhouduankaifabao\shangchengquan\shangcheng\app\home\public\css.html";i:1745486434;s:72:"D:\qianhouduankaifabao\shangchengquan\shangcheng\app\home\public\js.html";i:1745486434;s:79:"D:\qianhouduankaifabao\shangchengquan\shangcheng\app\home\public\copyright.html";i:1745486434;}*/ ?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>工作流执行日志</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" type="text/css" href="/static/admin/layui/css/layui.css?v=20200516" media="all">
<link rel="stylesheet" type="text/css" href="/static/admin/layui/css/modules/formSelects-v4.css?v=20200516" media="all">
<link rel="stylesheet" type="text/css" href="/static/admin/css/admin.css?v=20210826" media="all">
<link rel="stylesheet" type="text/css" href="/static/admin/css/font-awesome.min.css?v=20200516" media="all">
<link rel="stylesheet" type="text/css" href="/static/admin/webuploader/webuploader.css?v=<?php echo time(); ?>" media="all">
<link rel="stylesheet" type="text/css" href="/static/imgsrc/designer.css?v=20220803" media="all">
<link rel="stylesheet" type="text/css" href="/static/fonts/iconfont.css?v=20201218" media="all">
</head>
<body>
    <div class="layui-fluid">
        <div class="layui-row layui-col-space15">
            <div class="layui-col-md12">
                <div class="layui-card">
                    <div class="layui-card-header">
                        <h3>工作流执行日志</h3>
                    </div>
                    <div class="layui-card-body">
						<div class="layui-col-md4" style="padding-bottom:10px">
							<button class="layui-btn layui-btn-primary layuiadmin-btn-list" onclick="datadel(0)">删除</button>
							<button class="layui-btn layui-btn-warm layuiadmin-btn-list" onclick="batchQuery()">批量查询状态</button>
							<button class="layui-btn layui-btn-normal layuiadmin-btn-list" onclick="refreshTable()">刷新</button>
						</div>
						<div class="layui-form layui-col-md8" style="text-align:right;padding-bottom:10px">
							<div class="layui-inline">
								<label class="layui-form-label" style="width:60px">工作流</label>
								<div class="layui-input-block" style="width:180px;margin-left:90px;text-align:left">
									<select name="workflow_id" lay-search="">
										<option value="">选择工作流</option>
										<?php if(is_array($workflows) || $workflows instanceof \think\Collection || $workflows instanceof \think\Paginator): $i = 0; $__LIST__ = $workflows;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$workflow): $mod = ($i % 2 );++$i;?>
										<option value="<?php echo $workflow['workflow_id']; ?>"><?php echo $workflow['name']; ?> (<?php echo $workflow['workflow_id']; ?>)</option>
										<?php endforeach; endif; else: echo "" ;endif; ?>
									</select>
								</div>
							</div>
							<div class="layui-inline">
								<label class="layui-form-label" style="width:30px">状态</label>
								<div class="layui-input-block" style="width:120px;margin-left:60px;text-align:left">
									<select name="status">
										<option value="">全部状态</option>
										<option value="completed">已完成</option>
										<option value="running">运行中</option>
										<option value="failed">失败</option>
										<option value="unknown">未知</option>
									</select>
								</div>
							</div>
							<div class="layui-inline">
								<button class="layui-btn layuiadmin-btn-replys" lay-submit lay-filter="LAY-app-forumreply-search">
									<i class="layui-icon layui-icon-search layuiadmin-button-btn"></i>
								</button>
							</div>
						</div>
						<div class="layui-col-md12">
							<table id="tabledata" lay-filter="tabledata"></table>
						</div>
                    </div>
                </div>
            </div>
        </div>
    </div>



    <!-- 状态显示模板 -->
    <script type="text/html" id="status-tpl">
        <span class="layui-badge {{d.status_class}}">{{d.status_text}}</span>
    </script>

    <!-- 执行类型模板 -->
    <script type="text/html" id="async-tpl">
        <span class="layui-badge {{d.is_async ? 'layui-bg-blue' : 'layui-bg-green'}}">{{d.is_async_text}}</span>
    </script>

    <!-- 参数显示模板 -->
    <script type="text/html" id="params-tpl">
        {{# if(d.parameters_text && d.parameters_text !== '无参数') { }}
            <a href="javascript:;" lay-event="showParams" class="layui-text">查看参数</a>
        {{# } else { }}
            <span class="layui-text layui-text-muted">无参数</span>
        {{# } }}
    </script>

    <!-- 输出结果模板 -->
    <script type="text/html" id="output-tpl">
        {{# if(d.output_text && d.output_text !== '无输出' && d.output_text !== '无结果') { }}
            <a href="javascript:;" lay-event="showOutput" class="layui-text">查看输出</a>
        {{# } else { }}
            <span class="layui-text layui-text-muted">{{d.output_text}}</span>
        {{# } }}
    </script>

    <!-- 调试链接模板 -->
    <script type="text/html" id="debug-tpl">
        {{# if(d.debug_url) { }}
            <a href="{{d.debug_url}}" target="_blank" class="layui-btn layui-btn-xs layui-btn-normal">
                <i class="layui-icon layui-icon-link"></i>调试
            </a>
        {{# } else { }}
            <span class="layui-text layui-text-muted">无</span>
        {{# } }}
    </script>

	<script type="text/javascript" src="/static/admin/layui/layui.all.js?v=20210222"></script>
<script type="text/javascript" src="/static/admin/layui/lay/modules/formSelects-v4.js"></script>
<script type="text/javascript" src="/static/admin/js/jquery-ui.min.js?v=20200228"></script>
<script type="text/javascript" src="/static/admin/ueditor/ueditor.js?v=20220707"></script>
<script type="text/javascript" src="/static/admin/ueditor/135editor.js?v=20200228"></script>
<script type="text/javascript" src="/static/admin/webuploader/webuploader.js?v=20200620"></script>
<script type="text/javascript" src="/static/admin/js/qrcode.min.js?v=20200228"></script>
<script type="text/javascript" src="/static/admin/js/dianda.js?v=2022"></script>
	<script>
	layui.use(['table', 'form', 'layer'], function(){
		var table = layui.table;
		var form = layui.form;
		var layer = layui.layer;
		var $ = layui.jquery;
		var datawhere = {};

		//数据表
		var tableIns = table.render({
			elem: '#tabledata'
			,url: "<?php echo app('request')->url(); ?>" //数据接口
			,page: true //开启分页
			,cols: [[ //表头
				{type:"checkbox"},
				{field: 'id', title: 'ID',  sort: true,width:80},
				{field: 'workflow_id', title: '工作流ID', width: 180},
				{field: 'execute_id', title: '执行ID', width: 180},
				{field: 'status', title: '状态', width: 100, templet: function(d){
					var statusClass = '';
					var statusText = '';
					switch(d.status) {
						case 'completed':
							statusClass = 'layui-bg-green';
							statusText = '已完成';
							break;
						case 'running':
							statusClass = 'layui-bg-blue';
							statusText = '运行中';
							break;
						case 'failed':
							statusClass = 'layui-bg-red';
							statusText = '失败';
							break;
						default:
							statusClass = 'layui-bg-gray';
							statusText = '未知';
					}
					return '<span class="layui-badge ' + statusClass + '">' + statusText + '</span>';
				}},
				{field: 'is_async', title: '类型', width: 80, templet: function(d){
					return '<span class="layui-badge ' + (d.is_async ? 'layui-bg-blue' : 'layui-bg-green') + '">' + (d.is_async ? '异步' : '同步') + '</span>';
				}},
				{field: 'parameters', title: '参数', width: 120, templet: function(d){
					if(d.parameters && d.parameters !== '无参数') {
						return '<button class="table-btn" onclick="showParams('+d.id+')">查看参数</button>';
					} else {
						return '<span class="layui-text layui-text-muted">无参数</span>';
					}
				}},
				{field: 'output', title: '输出', width: 120, templet: function(d){
					if(d.output && d.output !== '无输出' && d.output !== '无结果') {
						return '<button class="table-btn" onclick="showOutput('+d.id+')">查看输出</button>';
					} else {
						return '<span class="layui-text layui-text-muted">无输出</span>';
					}
				}},
				{field: 'debug_url', title: '调试', width: 100, templet: function(d){
					if(d.debug_url) {
						return '<a href="'+d.debug_url+'" target="_blank" class="table-btn">调试</a>';
					} else {
						return '<span class="layui-text layui-text-muted">无</span>';
					}
				}},
				{field: 'create_time', title: '创建时间', width: 160},
				{field: 'operation', title: '操作',templet:function(d){
					var html = '';
					html += '<button class="table-btn" onclick="showDetail('+d.id+')">详情</button>';
					if(d.status == 'running' || d.status == 'Running') {
						html += '<button class="table-btn" onclick="queryWorkflowStatus('+d.id+')">查询状态</button>';
					}
					html += '<button class="table-btn" onclick="datadel(\''+d.id+'\')">删除</button>';
					return html;
				},width:200}
			]]
		});

		//排序
		table.on('sort(tabledata)', function(obj){
			datawhere.field = obj.field;
			datawhere.order = obj.type;
			tableIns.reload({
				initSort: obj,
				where: datawhere
			});
		});

		//检索
		form.on('submit(LAY-app-forumreply-search)', function(obj){
			var field = obj.field
			var olddatawhere = datawhere
			datawhere = field
			datawhere.field = olddatawhere.field
			datawhere.order = olddatawhere.order
			tableIns.reload({
				where: datawhere,
				page: {curr: 1}
			});
		})

		//删除
		window.datadel = function(id){
		var ids = [];
		if(id==0){
			var checkStatus = table.checkStatus('tabledata')
			var checkData = checkStatus.data; //得到选中的数据
			if(checkData.length === 0){
				 return layer.msg('请选择数据');
			}
			var ids = [];
			for(var i=0;i<checkData.length;i++){
				ids.push(checkData[i]['id']);
			}
		}else{
			ids.push(id)
		}
		layer.confirm('确定要删除吗?',{icon: 7, title:'操作确认'}, function(index){
			//do something
			layer.close(index);
			var index = layer.load();
			$.post("<?php echo url('delWorkflowLog'); ?>",{ids:ids},function(data){
				layer.close(index);
				dialog(data.msg,data.status);
				tableIns.reload()
			})
		});
		}

		// 批量查询状态
		window.batchQuery = function(){
		var checkStatus = table.checkStatus('tabledata')
		var checkData = checkStatus.data; //得到选中的数据
		if(checkData.length === 0){
			 return layer.msg('请选择数据');
		}
		layer.confirm('确定要查询选中的 ' + checkData.length + ' 条记录的状态吗？', function(index){
			layer.close(index);
			var loadIndex = layer.load(2, {content: '正在批量查询状态...'});
			var ids = [];
			for(var i=0;i<checkData.length;i++){
				ids.push(checkData[i]['id']);
			}
			$.post('<?php echo url("batchQueryWorkflowStatus"); ?>', {ids: ids}, function(res){
				layer.close(loadIndex);
				if(res.code == 1){
					layer.msg('批量查询完成', {icon: 1});
					tableIns.reload();
				} else {
					layer.msg(res.msg, {icon: 2});
				}
			});
		});
		}

		// 刷新表格
		window.refreshTable = function(){
			tableIns.reload();
			layer.msg('刷新成功', {icon: 1});
		}

		// 显示详情
		window.showDetail = function(id) {
		$.post('<?php echo url("getWorkflowLogDetail"); ?>', {id: id}, function(res){
			if(res.code == 1){
				var data = res.data;
            var content = '<div style="padding: 20px;">' +
                '<div class="layui-form-item">' +
                '<label class="layui-form-label">工作流ID:</label>' +
                '<div class="layui-input-block">' + data.workflow_id + '</div>' +
                '</div>' +
                '<div class="layui-form-item">' +
                '<label class="layui-form-label">执行ID:</label>' +
                '<div class="layui-input-block">' + (data.execute_id || '无') + '</div>' +
                '</div>' +
                '<div class="layui-form-item">' +
                '<label class="layui-form-label">执行状态:</label>' +
                '<div class="layui-input-block"><span class="layui-badge ' + data.status_class + '">' + data.status_text + '</span></div>' +
                '</div>' +
                '<div class="layui-form-item">' +
                '<label class="layui-form-label">执行类型:</label>' +
                '<div class="layui-input-block">' + data.is_async_text + '</div>' +
                '</div>' +
                '<div class="layui-form-item">' +
                '<label class="layui-form-label">创建时间:</label>' +
                '<div class="layui-input-block">' + data.create_time_format + '</div>' +
                '</div>' +
                '<div class="layui-form-item">' +
                '<label class="layui-form-label">更新时间:</label>' +
                '<div class="layui-input-block">' + data.update_time_format + '</div>' +
                '</div>';
            
            if(data.debug_url) {
                content += '<div class="layui-form-item">' +
                    '<label class="layui-form-label">调试链接:</label>' +
                    '<div class="layui-input-block"><a href="' + data.debug_url + '" target="_blank">打开调试页面</a></div>' +
                    '</div>';
            }
            
            content += '</div>';
            
            layer.open({
                type: 1,
                title: '执行详情',
                content: content,
                area: ['600px', '500px'],
                btn: ['关闭'],
                yes: function(index) {
                    layer.close(index);
                }
			});
		} else {
			layer.msg(res.msg, {icon: 2});
		}
	});
}

// 显示参数
window.showParams = function(id) {
	$.post('<?php echo url("getWorkflowLogDetail"); ?>', {id: id}, function(res){
		if(res.code == 1 && res.data.parameters){
			layer.open({
				type: 1,
				title: '执行参数',
				content: '<div style="padding: 20px;"><pre>' + res.data.parameters + '</pre></div>',
				area: ['600px', '400px'],
				btn: ['关闭'],
				yes: function(index) {
					layer.close(index);
				}
			});
		} else {
			layer.msg('无参数数据', {icon: 2});
		}
	});
}

// 显示输出
window.showOutput = function(id) {
	$.post('<?php echo url("getWorkflowLogDetail"); ?>', {id: id}, function(res){
		if(res.code == 1 && res.data.output){
			layer.open({
				type: 1,
				title: '执行输出',
				content: '<div style="padding: 20px;"><pre>' + res.data.output + '</pre></div>',
				area: ['600px', '400px'],
				btn: ['关闭'],
				yes: function(index) {
					layer.close(index);
				}
			});
		} else {
			layer.msg('无输出数据', {icon: 2});
		}
	});
}

		// 查询工作流状态
		window.queryWorkflowStatus = function(id) {
			$.post('<?php echo url("getWorkflowLogDetail"); ?>', {id: id}, function(res){
				if(res.code == 1){
					var data = res.data;
					if(!data.workflow_id || !data.execute_id) {
						layer.msg('缺少必要的查询参数', {icon: 2});
						return;
					}

					var loadIndex = layer.load(2, {content: '正在查询状态...'});

					$.post('<?php echo url("queryWorkflowStatus"); ?>', {
						id: data.id,
						workflow_id: data.workflow_id,
						execute_id: data.execute_id
					}, function(res){
						layer.close(loadIndex);
						if(res.code == 1){
							layer.msg('状态查询成功', {icon: 1});
							tableIns.reload(); // 刷新表格显示最新状态

							// 显示查询结果
							if(res.data && res.data.status) {
								var statusText = res.data.status === 'Success' ? '已完成' :
											   res.data.status === 'Failed' ? '失败' :
											   res.data.status === 'Running' ? '运行中' : res.data.status;
								layer.msg('当前状态: ' + statusText, {icon: 1, time: 3000});
							}
						} else {
							layer.msg(res.msg, {icon: 2});
						}
					}).fail(function(){
						layer.close(loadIndex);
						layer.msg('查询请求失败', {icon: 2});
					});
				} else {
					layer.msg(res.msg, {icon: 2});
				}
			});
		}
	});
	</script>
	
</body>
</html>
