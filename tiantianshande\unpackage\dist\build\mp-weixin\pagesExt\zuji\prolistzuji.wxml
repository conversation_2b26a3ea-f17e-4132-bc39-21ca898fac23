<view class="page-container"><view class="header"><image class="store-image" src="{{business.logo}}"></image><view class="store-info"><text class="store-name">{{business.name}}</text><text class="store-address">{{business.address}}</text></view></view><view class="model-selection"><text class="section-title">请选择机型</text><view class="tab"><block wx:for="{{catagoryList}}" wx:for-item="item" wx:for-index="__i0__" wx:key="id"><view data-event-opts="{{[['tap',[['selectTab',['$0'],[[['catagoryList','id',item.id,'id']]]]]]]}}" class="{{['tab-item',activeTab===item.id?'active':'']}}" bindtap="__e">{{''+item.name+''}}<block wx:if="{{activeTab===item.id}}"><view class="active-after"></view></block></view></block></view></view><view class="product-info"><view class="info-item"><text class="label">品牌</text><picker mode="selector" range="{{brands}}" range-key="name" data-event-opts="{{[['change',[['onBrandChange',['$event']]]]]}}" bindchange="__e"><view class="picker">{{selectedBrand}}<text class="arrow">></text></view></picker></view><block wx:for="{{guigeList}}" wx:for-item="item" wx:for-index="index" wx:key="k"><view class="info-item"><text class="label">{{item.title}}</text><picker mode="selector" range="{{item['items']}}" range-key="title" data-event-opts="{{[['change',[['handleCShangeGuigeData',[index,'$event']]]]]}}" bindchange="__e"><view class="picker">{{''+(item['items'][guigeChoosenData[index]]&&item['items'][guigeChoosenData[index]].title)}}<text class="arrow">></text></view></picker></view></block></view><view class="credit-selection"><text class="section-title">芝麻信用分范围</text><text class="note">*请选择客户支付宝芝麻信用分</text><radio-group data-event-opts="{{[['change',[['onCreditChange',['$event']]]]]}}" class="credit-options" bindchange="__e"><label class="radio-label"><radio value="400以下">400以下</radio></label><label class="radio-label"><radio value="400-500">400-500</radio></label><label class="radio-label"><radio value="500以上">500以上</radio></label><label class="radio-label"><radio value="老客户专属">老客户专属</radio><text class="exclusive">包过！<text data-event-opts="{{[['tap',[['showPopup',['$event']]]]]}}" class="info-icon" bindtap="__e">i</text></text></label></radio-group></view><block wx:if="{{selectedCreditRange}}"><view class="initial-payment-selection"><text class="section-title">首期金额</text><view class="payment-options"><block wx:if="{{selectedCreditRange=='500以上'||selectedCreditRange=='老客户专属'}}"><view data-event-opts="{{[['tap',[['selectPayment',['30%']]]]]}}" class="{{['payment-option',selectedPayment==='30%'?'selected':'']}}" bindtap="__e">30%</view></block><block wx:if="{{selectedCreditRange!=='400以下'}}"><view data-event-opts="{{[['tap',[['selectPayment',['40%']]]]]}}" class="{{['payment-option',selectedPayment==='40%'?'selected':'']}}" bindtap="__e">40%</view></block><view data-event-opts="{{[['tap',[['selectPayment',['50%']]]]]}}" class="{{['payment-option',selectedPayment==='50%'?'selected':'']}}" bindtap="__e">50%</view></view></view></block><block wx:if="{{selectedCreditRange}}"><view class="lease-term-selection"><text class="section-title">租赁期数</text><view class="lease-options"><view data-event-opts="{{[['tap',[['selectLease',['6期']]]]]}}" class="{{['lease-option',selectedLease==='6期'?'selected':'']}}" bindtap="__e">6期</view><view data-event-opts="{{[['tap',[['selectLease',['8期']]]]]}}" class="{{['lease-option',selectedLease==='8期'?'selected':'']}}" bindtap="__e">8期</view></view></view></block><block wx:if="{{selectedCreditRange}}"><view class="contract-price"><text class="contract-label">设备总签约价</text><text class="contract-price">{{"¥ "+totalPrice}}</text></view></block><block wx:if="{{selectedCreditRange}}"><view class="billing-details"><text class="section-title">账单明细</text><view class="billing-item"><text class="billing-label">首期</text><text class="billing-value">¥ 2310</text></view><view class="billing-item"><text class="billing-label">第2期租金</text><text class="billing-value">¥ 1495</text></view></view></block><block wx:if="{{selectedCreditRange}}"><view class="next-step"><button type="primary" data-event-opts="{{[['tap',[['nextStep',['$event']]]]]}}" bindtap="__e">下一步</button></view></block><block wx:if="{{showPopup}}"><view data-event-opts="{{[['tap',[['closePopup',['$event']]]]]}}" class="popup-overlay" bindtap="__e"><view class="popup-content"><text class="popup-text">该选项仅供参考，具体情况请咨询店铺工作人员。</text></view></view></block></view>