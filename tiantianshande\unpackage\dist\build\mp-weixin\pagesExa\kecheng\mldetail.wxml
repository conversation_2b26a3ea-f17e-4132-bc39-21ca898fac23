<view class="wrap"><block wx:if="{{isload}}"><block><block wx:if="{{detail.kctype==1}}"><block><view class="title">{{detail.name}}</view><dp vue-id="64ab04bf-1" pagecontent="{{pagecontent}}" bind:__l="__l"></dp><view style="margin-bottom:40rpx;"></view></block></block><block wx:if="{{detail.kctype==2}}"><view class="audo-video"><view class="audoimg"><image src="{{detail.pic}}"></image></view><view class="play"><view class="play-left"><image hidden="{{!(playshow)}}" src="/static/img/video_icon.png" data-event-opts="{{[['tap',[['play',['$event']]]]]}}" bindtap="__e"></image><image hidden="{{!(!playshow)}}" src="/static/img/play.png" data-event-opts="{{[['tap',[['pauseaudio',['$event']]]]]}}" bindtap="__e"></image><text>{{nowtime}}</text></view><view class="play-right"><slider class="slider" block-size="16" min="{{0}}" max="{{time}}" value="{{currentTime}}" activeColor="#595959" data-event-opts="{{[['change',[['sliderChange',['$event']]]],['changing',[['sliderChanging',['$event']]]]]}}" bindchange="__e" bindchanging="__e"></slider></view><view class="play-end"><text>{{duration}}</text></view></view></view></block><block wx:if="{{detail.kctype==3}}"><view class="videobox"><video class="video" id="video" autoplay="{{true}}" src="{{detail.video_url}}" initial-time="{{detail.startTime}}" data-event-opts="{{[['pause',[['pause',['$event']]]],['timeupdate',[['timeupdate',['$event']]]],['ended',[['ended',['$event']]]]]}}" bindpause="__e" bindtimeupdate="__e" bindended="__e"></video></view></block><view style="height:30rpx;width:100%;background-color:#f5f5f5;"></view><view class="content_box"><view class="title flex"><view class="t1">课程目录</view><block wx:if="{{detail.isdt==1&&detail.count>=detail.kccount&&iskaoshi!=1}}"><view class="t2" data-url="{{'tiku?id='+detail.kcid}}" data-opentype="redirect" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e">去答题</view></block><block wx:if="{{iskaoshi==1}}"><view class="t2" data-url="{{'recordlog?kcid='+detail.kcid}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e">答题记录</view></block></view><block wx:for="{{datalist}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="mulubox flex"><view class="left_box"><block wx:if="{{item.kctype==1}}"><image src="/static/img/tw_icon.png"></image></block><block wx:if="{{item.kctype==2}}"><image src="/static/img/mp3_icon.png"></image></block><block wx:if="{{item.kctype==3}}"><image src="/static/img/video_icon.png"></image></block></view><view class="right_box flex"><view class="{{['title_box '+(item.id==detail.id?'on':'')]}}" data-key="{{item.key}}" data-mianfei="{{item.ismianfei}}" data-url="{{'mldetail?id='+item.id+'&kcid='+item.kcid}}" data-opentype="{{item.kctype==1?'redirect':'redirect'}}" data-event-opts="{{[['tap',[['todetail',['$event']]]]]}}" bindtap="__e"><view class="t1">{{''+item.name}}</view><view><block wx:if="{{item.kctype==1}}"><text class="t2">图文课程</text></block><block wx:if="{{item.kctype==2}}"><text class="t2">音频课程</text></block><block wx:if="{{item.kctype==3}}"><text class="t2">视频课程</text></block><block wx:if="{{item.video_duration>0}}"><text class="t2">{{'时长: '+item.duration}}</text></block></view></view><block wx:if="{{item.jindu}}"><view class="jindu">{{item.jindu+(item.kctype==1&&item.jindu!='100'?'':'%')}}</view></block><block wx:if="{{item.ismianfei&&!item.jindu}}"><view class="skbtn">试看</view></block></view></view></block></view></block></block><block wx:if="{{nomore}}"><nomore vue-id="64ab04bf-2" text="没有更多课程了" bind:__l="__l"></nomore></block><block wx:if="{{nodata}}"><nodata vue-id="64ab04bf-3" text="没有查找到相关课程" bind:__l="__l"></nodata></block><block wx:if="{{loading}}"><loading vue-id="64ab04bf-4" bind:__l="__l"></loading></block><dp-tabbar vue-id="64ab04bf-5" opt="{{opt}}" data-event-opts="{{[['^getmenuindex',[['getmenuindex']]]]}}" bind:getmenuindex="__e" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="64ab04bf-6" data-ref="popmsg" bind:__l="__l"></popmsg></view>