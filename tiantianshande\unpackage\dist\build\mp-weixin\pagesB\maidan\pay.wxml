<view><block wx:if="{{isload}}"><block><view class="container"><view class="header"><view class="flex-y-center"><image class="header_icon" src="{{logo}}"></image><view class="flex1"><view class="header_name">{{name}}</view><block wx:if="{{$root.g0>0}}"><view class="header_shop"><text>选择门店:</text><text data-event-opts="{{[['tap',[['selectmd',['$event']]]]]}}" bindtap="__e">{{mdlist[mdkey].name}}</text></view></block></view></view></view><view class="page"><view data-event-opts="{{[['tap',[['handleShowKey',['$event']]]]]}}" class="page_module flex-y-center" bindtap="__e"><text class="page_tag">￥</text><view class="page_price flex-y-center"><block wx:if="{{keyHidden&&!money}}"><text class="page_notice">请输入金额</text></block><block wx:if="{{money}}"><text>{{money}}</text></block><block wx:if="{{!keyHidden}}"><view class="page_cursor"></view></block></view></view><view><view class="info-box"><block wx:if="{{have_login==1&&userinfo.discount>0&&userinfo.discount<10}}"><view class="dkdiv-item flex"><text class="f1">{{$root.m0+"折扣("+userinfo.discount*100/100+"折)"}}</text><text class="f2" style="color:#e94745;">{{"-￥"+disprice}}</text></view></block><block wx:if="{{have_login==1}}"><view class="dkdiv-item flex-y-center"><text class="f1">{{$root.m1}}</text><block wx:if="{{$root.g1>0}}"><text data-event-opts="{{[['tap',[['showCouponList',['$event']]]]]}}" class="f2" style="color:#e94745;" bindtap="__e">{{couponrid!=0?couponList[couponkey].couponname:'请选择'+$root.m2}}</text></block><block wx:else><text class="f2" style="color:#999;">{{"无可用"+$root.m3}}</text></block><image class="f3" src="{{pre_url+'/static/img/arrowright.png'}}"></image></view></block><block wx:if="{{have_login==1&&userinfo.scoredkmaxpercent>0}}"><view class="dkdiv-item flex"><checkbox-group data-event-opts="{{[['change',[['scoredk',['$event']]]]]}}" class="flex" style="width:100%;" bindchange="__e"><view class="f1"><view>{{userinfo.score*1+" "+$root.m4+'可抵扣'}}<text style="color:#e94745;">{{userinfo.dkmoney*1}}</text>元</view><block wx:if="{{userinfo.scoredkmaxpercent>0&&userinfo.scoredkmaxpercent<100}}"><view style="font-size:22rpx;color:#999;">{{'最多可抵扣订单金额的'+userinfo.scoredkmaxpercent+"%"}}</view></block></view><view class="f2">{{"使用"+$root.m5+'抵扣'}}<checkbox style="margin-left:6px;transform:scale(.8);" value="1"></checkbox></view></checkbox-group></view></block><block wx:if="{{have_login==1&&moneydec&&money_dec_rate>0}}"><view class="dkdiv-item flex"><checkbox-group class="flex" style="width:100%;" data-bid="{{bid}}" data-rate="{{money_dec_rate}}" data-event-opts="{{[['change',[['moneydk',['$event']]]]]}}" bindchange="__e"><view class="f1"><view>{{''+$root.m6+"抵扣（余额："}}<text style="color:#e94745;">{{userinfo.money?userinfo.money:0}}</text>元）</view><view style="font-size:24rpx;color:#999;">{{'1、选择此项提交订单时将直接扣除'+$root.m7+''}}</view><view style="font-size:24rpx;color:#999;">{{'2、最多可抵扣订单金额的'+money_dec_rate+'%'}}</view></view><view class="f2" style="font-weight:normal;">{{'使用'+$root.m8+'抵扣'}}<checkbox style="margin-left:6px;transform:scale(.8);" value="1"></checkbox></view></checkbox-group></view></block><block wx:if="{{have_login==1&&userinfo.dedamount>0&&userinfo.dedamount_maxdkpercent>0}}"><view class="dkdiv-item flex"><checkbox-group data-event-opts="{{[['change',[['dedamountdk',['$event']]]]]}}" class="flex" style="width:100%;" bindchange="__e"><view class="f1"><view>{{userinfo.dedamount*1+''}}<text style="font-weight:bold;">抵扣金</text>可抵扣<text style="color:#e94745;">{{userinfo.dedamount*1}}</text>元</view><block wx:if="{{userinfo.dedamount_maxdkpercent>0&&userinfo.dedamount_maxdkpercent<100}}"><view style="font-size:22rpx;color:#999;">{{'最多可抵扣订单金额的'+userinfo.dedamount_maxdkpercent+'%'}}</view></block><view style="font-size:24rpx;color:#999;">选择此项提交订单时将直接扣除抵扣金</view></view><view class="f2">使用<text style="font-weight:bold;">抵扣金</text>抵扣<checkbox style="margin-left:6px;transform:scale(.8);" value="1" checked="{{usededamount}}"></checkbox></view></checkbox-group></view></block><block wx:if="{{menudata}}"><view class="dp-menu" style="{{'font-size:'+('28rpx')+';'+('background-color:'+(dhinfo.backgroundColor)+';')+('padding:'+('20rpx 0')+';')}}"><view style="padding-top:16rpx;"><view class="swiper-item"><block wx:for="{{menudata}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="menu-nav5" data-url="{{item.link}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image style="{{'width:'+('80rpx')+';'+('height:'+('80rpx')+';')}}" src="{{item.pic}}"></image><view class="menu-text" style="{{'color:'+(item.color)+';'+('height:'+('28rpx')+';')+('line-height:'+('28rpx')+';')}}">{{item.name||'按钮文字'}}</view></view></block></view></view></view></block><view class="dkdiv-item flex flex-bt"><text class="t1">实付金额:</text><text class="t2">{{"￥"+paymoney}}</text></view><view class="remark-item"><block wx:if="{{remark}}"><text class="remark-txt">{{remark}}</text></block><text data-event-opts="{{[['tap',[['toggleRemarkModal',['$event']]]]]}}" class="remark-btn" style="{{'color:'+($root.m9)+';'}}" bindtap="__e">{{remark==''?'添加备注':'修改'}}</text></view><view class="dkdiv-item flex flex-bt-tip" style="{{'color:'+($root.m10)+';'}}" data-url="{{'/pages/index/login?frompage='+$root.m11}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">{{login_tip}}</view><block wx:if="{{keyHidden}}"><view class="op"><view data-event-opts="{{[['tap',[['topay',['$event']]]]]}}" class="btn" style="{{'background:'+($root.m12)+';'}}" bindtap="__e">去支付</view></view></block></view></view><block wx:if="{{$root.g2>0}}"><view class="ad-box"><block wx:for="{{adlist}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><block wx:if="{{item.pic}}"><view class="ad-item" data-url="{{item.url}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image src="{{item.pic}}" mode="widthFix"></image></view></block></block></block></view></block></view></view><block wx:if="{{have_login&&couponvisible}}"><view class="popup__container"><view data-event-opts="{{[['tap',[['handleClickMask',['$event']]]]]}}" class="popup__overlay" catchtap="__e"></view><view class="popup__modal"><view class="popup__title"><text class="popup__title-text">{{"请选择"+$root.m13}}</text><image class="popup__close" style="width:36rpx;height:36rpx;" src="{{pre_url+'/static/img/close.png'}}" data-event-opts="{{[['tap',[['handleClickMask',['$event']]]]]}}" catchtap="__e"></image></view><view class="popup__content"><couponlist vue-id="2546d0be-1" couponlist="{{couponList}}" choosecoupon="{{true}}" selectedrid="{{couponrid}}" data-event-opts="{{[['^chooseCoupon',[['chooseCoupon']]]]}}" bind:chooseCoupon="__e" bind:__l="__l"></couponlist></view></view></view></block><block wx:if="{{!keyHidden}}"><view class="keyboard_page"><view data-event-opts="{{[['tap',[['handleHiddenKey',['$event']]]]]}}" class="keyboard_none" bindtap="__e"></view><view class="{{['keyboard_key','hind_box',menuindex>-1?'tabbarbot':'notabbarbot']}}"><image class="key-down" src="{{pre_url+'/static/img/pack_up.png'}}" mode data-event-opts="{{[['tap',[['handleHiddenKey',['$event']]]]]}}" bindtap="__e"></image><view class="key-box"><view class="number-box clearfix"><block wx:for="{{KeyboardKeys}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="{{[index===9?'key key-zero':'key']}}" hover-class="number-box-hover" data-event-opts="{{[['tap',[['handleKey',['$0'],[[['KeyboardKeys','',index]]]]]]]}}" bindtap="__e">{{item}}</view></block></view><view class="btn-box"><view class="key" hover-class="number-box-hover" data-key="X" data-event-opts="{{[['tap',[['handleKey',['X']]]]]}}" bindtap="__e">×</view><view class="{{[money?'key pay_btn':'key pay_btn pay-btn-display']}}" hover-class="pay-btn-hover" data-event-opts="{{[['tap',[['topay',['$event']]]]]}}" bindtap="__e">付款</view></view></view></view></view></block><block wx:if="{{selectmdDialogShow}}"><view class="popup__container"><view data-event-opts="{{[['tap',[['hideSelectmdDialog',['$event']]]]]}}" class="popup__overlay" catchtap="__e"></view><view class="popup__modal"><view class="popup__title"><text class="popup__title-text">请选择门店</text><image class="popup__close" style="width:36rpx;height:36rpx;" src="{{pre_url+'/static/img/close.png'}}" data-event-opts="{{[['tap',[['hideSelectmdDialog',['$event']]]]]}}" catchtap="__e"></image></view><view class="popup__content"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="pstime-item" data-index="{{index}}" data-event-opts="{{[['tap',[['selectmdRadioChange',['$event']]]]]}}" bindtap="__e"><view class="flex1">{{item.$orig.name}}</view><view style="color:#999;font-size:24rpx;margin-right:10rpx;">{{item.$orig.juli?' 距离:'+item.$orig.juli+'千米':''}}</view><view class="radio" style="{{(index==mdkey?'background:'+item.m14+';border:0':'')}}"><image class="radio-img" src="{{pre_url+'/static/img/checkd.png'}}"></image></view></view></block></view></view></view></block><block wx:if="{{isshowremark}}"><view class="popup__container popup__remark"><view data-event-opts="{{[['tap',[['toggleRemarkModal',['$event']]]]]}}" class="popup__overlay" catchtap="__e"></view><view class="popup__modal"><view class="popup__title"><text class="popup__title-text">备注信息</text><image class="popup__close" style="width:36rpx;height:36rpx;" src="{{pre_url+'/static/img/close.png'}}" data-event-opts="{{[['tap',[['toggleRemarkModal',['$event']]]]]}}" catchtap="__e"></image></view><view class="popup__content"><input class="remark" type="text" placeholder-style="font-size:26rpx;color:#999" placeholder="请填写备注信息" data-event-opts="{{[['input',[['__set_model',['','remark','$event',[]]]]]]}}" value="{{remark}}" bindinput="__e"/></view><view class="popup__bottom"><button data-event-opts="{{[['tap',[['remarkConfirm',['$event']]]]]}}" class="remark-confirm" style="{{'background:'+($root.m15)+';'}}" bindtap="__e">确定</button></view></view></view></block></block></block><block wx:if="{{loading}}"><loading vue-id="2546d0be-2" bind:__l="__l"></loading></block><dp-tabbar vue-id="2546d0be-3" opt="{{opt}}" data-event-opts="{{[['^getmenuindex',[['getmenuindex']]]]}}" bind:getmenuindex="__e" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="2546d0be-4" data-ref="popmsg" bind:__l="__l"></popmsg><wxxieyi vue-id="2546d0be-5" bind:__l="__l"></wxxieyi></view>