<view class="container"><block wx:if="{{isload}}"><block><view class="content"><view class="info-item"><text class="t1">订单名称:</text><text class="t2">{{orderinfo.title}}</text></view><view class="info-item"><text class="t1">订单编号:</text><text class="t2">{{orderinfo.ordernum}}</text></view><view class="info-item"><text class="t1">支付金额:</text><text class="t2" style="color:#e94745;">{{"￥"+orderinfo.totalprice}}</text></view></view><block wx:if="{{wxpayst==1}}"><button class="fpay-btn" data-typeid="1" data-event-opts="{{[['tap',[['topay',['$event']]]]]}}" bindtap="__e">微信支付</button></block><block wx:if="{{alipay==1}}"><button class="fpay-btn" style="background:#108EE9;margin-top:20rpx;" data-typeid="10" data-event-opts="{{[['tap',[['topay',['$event']]]]]}}" bindtap="__e">支付宝支付</button></block><block wx:if="{{moneypay==1}}"><block><block wx:if="{{userinfo.haspwd}}"><button data-event-opts="{{[['tap',[['modalinput',['$event']]]]]}}" class="fpay-btn2" bindtap="__e">{{$root.m0+"支付（当前余额¥"+userinfo.money+"）"}}</button></block><block wx:else><button class="fpay-btn2" data-typeid="2" data-event-opts="{{[['tap',[['topay',['$event']]]]]}}" bindtap="__e">{{$root.m1+"支付（当前余额¥"+userinfo.money+"）"}}</button></block></block></block><block wx:if="{{userinfo.haspwd}}"><view class="{{['weui-demo-dialog '+(!hiddenmodalput?'weui-demo-dialog_show':'')]}}"><view data-event-opts="{{[['tap',[['cancel',['$event']]]]]}}" class="weui-mask" bindtap="__e"></view><view class="weui-dialog__wrp"><view class="weui-dialog"><view class="weui-dialog__hd"><view class="weui-dialog__title">支付密码</view></view><view class="weui-dialog__bd"><view class="flex-y-center flex-x-center" style="margin:20rpx 130rpx;"><text style="font-size:40rpx;color:#000;"></text><input type="digit" placeholder="请输入支付密码" data-event-opts="{{[['input',[['getpwd',['$event']]]]]}}" bindinput="__e"/></view></view><view class="weui-dialog__ft"><view data-event-opts="{{[['tap',[['cancel',['$event']]]]]}}" class="weui-dialog__btn weui-dialog__btn_default" bindtap="__e">取消</view><view class="weui-dialog__btn" data-typeid="2" data-event-opts="{{[['tap',[['topay',['$event']]]]]}}" bindtap="__e">确定</view></view></view></view></view></block></block></block><block wx:if="{{loading}}"><loading vue-id="59a71251-1" bind:__l="__l"></loading></block><dp-tabbar vue-id="59a71251-2" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="59a71251-3" data-ref="popmsg" bind:__l="__l"></popmsg></view>