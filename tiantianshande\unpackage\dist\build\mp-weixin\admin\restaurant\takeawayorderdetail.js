require('../common/vendor.js');(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["admin/restaurant/takeawayorderdetail"],{"00d3":function(e,t,n){"use strict";n.r(t);var i=n("0adc"),a=n.n(i);for(var o in i)["default"].indexOf(o)<0&&function(e){n.d(t,e,(function(){return i[e]}))}(o);t["default"]=a.a},"0adc":function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i=getApp(),a=null,o={data:function(){return{opt:{},loading:!1,isload:!1,menuindex:-1,pre_url:i.globalData.pre_url,expressdata:[],express_index:0,express_no:"",prodata:"",djs:"",detail:"",prolist:"",shopset:"",storeinfo:"",lefttime:"",codtxt:"",peisonguser:[],peisonguser2:[],index2:0}},onLoad:function(e){this.opt=i.getopts(e),this.getdata()},onPullDownRefresh:function(){this.getdata()},onUnload:function(){clearInterval(a)},methods:{getdata:function(){var e=this;e.loading=!0,i.get("ApiAdminRestaurantTakeawayOrder/detail",{id:e.opt.id},(function(t){e.loading=!1,e.expressdata=t.expressdata,e.detail=t.detail,e.prolist=t.prolist,e.shopset=t.shopset,e.storeinfo=t.storeinfo,e.lefttime=t.lefttime,e.codtxt=t.codtxt,t.lefttime>0&&(a=setInterval((function(){e.lefttime=e.lefttime-1,e.getdjs()}),1e3)),e.loaded()}))},getdjs:function(){var e=this.lefttime;if(e<=0)this.djs="00时00分00秒";else{var t=Math.floor(e/3600),n=Math.floor((e-3600*t)/60),i=e-3600*t-60*n,a=(t<10?"0":"")+t+"时"+(n<10?"0":"")+n+"分"+(i<10?"0":"")+i+"秒";this.djs=a}},setremark:function(){this.$refs.dialogSetremark.open()},setremarkconfirm:function(e,t){this.$refs.dialogSetremark.close();var n=this;i.post("ApiAdminOrder/setremark",{type:"restaurant_takeaway",orderid:n.detail.id,content:t},(function(e){i.success(e.msg),setTimeout((function(){n.getdata()}),1e3)}))},fahuo:function(){this.$refs.dialogExpress.open()},dialogExpressClose:function(){this.$refs.dialogExpress.close()},expresschange:function(e){this.express_index=e.detail.value},setexpressno:function(e){this.express_no=e.detail.value},confirmfahuo:function(){this.$refs.dialogExpress.close();var e=this,t=this.expressdata[this.express_index];i.post("ApiAdminOrder/sendExpress",{type:"restaurant_takeaway",orderid:e.detail.id,express_no:e.express_no,express_com:t},(function(t){i.success(t.msg),setTimeout((function(){e.getdata()}),1e3)}))},ispay:function(e){var t=this,n=e.currentTarget.dataset.id;i.confirm("确定要改为已支付吗?",(function(){i.showLoading("提交中"),i.post("ApiAdminOrder/ispay",{type:"restaurant_takeaway",orderid:n},(function(e){i.showLoading(!1),i.success(e.msg),setTimeout((function(){t.getdata()}),1e3)}))}))},jiedan:function(e){var t=this,n=e.currentTarget.dataset.id;i.confirm("确定要接单吗?",(function(){i.showLoading("提交中"),i.post("ApiAdminOrder/jiedan",{type:"restaurant_takeaway",orderid:n},(function(e){i.showLoading(!1),i.success(e.msg),setTimeout((function(){t.getdata()}),1e3)}))}))},refund:function(e){var t=this,n=e.currentTarget.dataset.id;i.confirm("确定要拒单并退款吗?",(function(){i.showLoading("提交中"),i.post("ApiAdminOrder/judan",{type:"restaurant_takeaway",orderid:n},(function(e){i.showLoading(!1),i.success(e.msg),setTimeout((function(){t.getdata()}),1e3)}))}))},delOrder:function(e){var t=e.currentTarget.dataset.id;i.showLoading("删除中"),i.confirm("确定要删除该订单吗?",(function(){i.post("ApiAdminOrder/delOrder",{type:"restaurant_takeaway",orderid:t},(function(e){i.showLoading(!1),i.success(e.msg),setTimeout((function(){i.goto("takeawayorder")}),1e3)}))}))},closeOrder:function(e){var t=this,n=e.currentTarget.dataset.id;i.confirm("确定要关闭该订单吗?",(function(){i.showLoading("提交中"),i.post("ApiAdminOrder/closeOrder",{type:"restaurant_takeaway",orderid:n},(function(e){i.showLoading(!1),i.success(e.msg),setTimeout((function(){t.getdata()}),1e3)}))}))},refundnopass:function(e){var t=this,n=e.currentTarget.dataset.id;i.confirm("确定要驳回退款申请吗?",(function(){i.showLoading("提交中"),i.post("ApiAdminOrder/refundnopass",{type:"restaurant_takeaway",orderid:n},(function(e){i.showLoading(!1),i.success(e.msg),setTimeout((function(){t.getdata()}),1e3)}))}))},refundpass:function(e){var t=this,n=e.currentTarget.dataset.id;i.confirm("确定要审核通过并退款吗?",(function(){i.showLoading("提交中"),i.post("ApiAdminOrder/refundpass",{type:"restaurant_takeaway",orderid:n},(function(e){i.showLoading(!1),i.success(e.msg),setTimeout((function(){t.getdata()}),1e3)}))}))},print:function(e){var t=e.currentTarget.dataset.id;i.showLoading("打印中"),i.post("ApiAdminOrder/print",{type:"restaurant_takeaway",orderid:t},(function(e){i.showLoading(!1),i.success(e.msg)}))},peisong:function(){var e=this;e.loading=!0,i.post("ApiAdminOrder/getpeisonguser",{type:"restaurant_takeaway_order",orderid:e.detail.id},(function(t){e.loading=!1;var n=t.peisonguser,a=t.paidantype,o=t.psfee,s=t.ticheng,r=[];for(var d in n)r.push(n[d].title);if(e.peisonguser=t.peisonguser,e.peisonguser2=r,1==a)e.$refs.dialogPeisong.open();else{if(0==e.detail.bid)var u="选择配送员配送，订单将发布到抢单大厅由配送员抢单，配送员提成￥"+s+"，确定要配送员配送吗？";else u="选择配送员配送，订单将发布到抢单大厅由配送员抢单，需扣除配送费￥"+o+"，确定要配送员配送吗？";if(2==a)var c="-1";else c="0";i.confirm(u,(function(){i.post("ApiAdminOrder/peisong",{type:"restaurant_takeaway_order",orderid:e.detail.id,psid:c},(function(t){i.success(t.msg),setTimeout((function(){e.getdata()}),1e3)}))}))}}))},dialogPeisongClose:function(){this.$refs.dialogPeisong.close()},peisongChange:function(e){this.index2=e.detail.value},confirmPeisong:function(){var e=this,t=this.peisonguser[this.index2].id;i.post("ApiAdminOrder/peisong",{type:"restaurant_takeaway_order",orderid:e.detail.id,psid:t},(function(t){i.success(t.msg),e.$refs.dialogPeisong.close(),setTimeout((function(){e.getdata()}),1e3)}))},peisongWx:function(){var e=this,t=e.detail.freight_price;if(0==e.detail.bid)var n="选择即时配送，订单将派单到第三方配送平台，并扣除相应费用，确定要派单吗？";else n="选择即时配送，订单将派单到第三方配送平台，需扣除配送费￥"+t+"，确定要派单吗？";i.confirm(n,(function(){e.loading=!0,i.post("ApiAdminOrder/peisongWx",{type:"restaurant_takeaway_order",orderid:e.detail.id},(function(t){e.loading=!1,i.success(t.msg),setTimeout((function(){e.getdata()}),1e3)}))}))}}};t.default=o},1002:function(e,t,n){"use strict";n.r(t);var i=n("82f9"),a=n("00d3");for(var o in a)["default"].indexOf(o)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(o);n("e4d3");var s=n("828b"),r=Object(s["a"])(a["default"],i["b"],i["c"],!1,null,null,null,!1,i["a"],void 0);t["default"]=r.exports},2926:function(e,t,n){"use strict";(function(e,t){var i=n("47a9");n("06e9");i(n("3240"));var a=i(n("1002"));e.__webpack_require_UNI_MP_PLUGIN__=n,t(a.default)}).call(this,n("3223")["default"],n("df3c")["createPage"])},"82f9":function(e,t,n){"use strict";n.d(t,"b",(function(){return a})),n.d(t,"c",(function(){return o})),n.d(t,"a",(function(){return i}));var i={uniPopup:function(){return Promise.all([n.e("common/vendor"),n.e("components/uni-popup/uni-popup")]).then(n.bind(null,"ca44a"))},uniPopupDialog:function(){return n.e("components/uni-popup-dialog/uni-popup-dialog").then(n.bind(null,"267c"))},loading:function(){return n.e("components/loading/loading").then(n.bind(null,"ceaa"))},dpTabbar:function(){return n.e("components/dp-tabbar/dp-tabbar").then(n.bind(null,"b875"))}},a=function(){var e=this,t=e.$createElement,n=(e._self._c,e.isload?e.__map(e.prolist,(function(t,n){var i=e.__get_orig(t),a=t.jlprice?parseFloat(parseFloat(t.sell_price)+parseFloat(t.jlprice)).toFixed(2):null;return{$orig:i,g0:a}})):null),i=e.isload?e.t("会员"):null,a=e.isload&&e.detail.leveldk_money>0?e.t("会员"):null,o=e.isload&&e.detail.couponmoney>0?e.t("优惠券"):null,s=e.isload&&e.detail.scoredk>0?e.t("积分"):null;e.$mp.data=Object.assign({},{$root:{l0:n,m0:i,m1:a,m2:o,m3:s}})},o=[]},bfafa:function(e,t,n){},e4d3:function(e,t,n){"use strict";var i=n("bfafa"),a=n.n(i);a.a}},[["2926","common/runtime","common/vendor"]]]);