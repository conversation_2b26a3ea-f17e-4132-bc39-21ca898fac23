<view class="container"><block wx:if="{{isload}}"><block><view class="ordertop" style="{{('background:url('+pre_url+'/static/img/ordertop.png);background-size:100%')}}"><block wx:if="{{detail.status==0}}"><view class="f1"><view class="t1">等待买家付款</view><block wx:if="{{djs}}"><view class="t2">{{"剩余时间："+djs}}</view></block></view></block><block wx:if="{{detail.status==1}}"><view class="f1"><view class="t2">已付款，等待商家确认</view></view></block><block wx:if="{{detail.status==2}}"><view class="f1"><view class="t2">商家已确认，等待入住</view></view></block><block wx:if="{{detail.status==3}}"><view class="f1"><view class="t1">已到店</view></view></block><block wx:if="{{detail.status==4}}"><view class="f1"><view class="t1">已离店</view></view></block><block wx:if="{{detail.status==5}}"><view class="f1"><view class="t1">订单已完成</view></view></block><block wx:if="{{detail.status==-1}}"><view class="f1"><view class="t1">订单已关闭</view></view></block></view><view class="product"><view class="content"><view class="hotelpic"><image src="{{hotel.pic}}"></image></view><view class="detail"><text class="t1">{{hotel.name}}</text><text class="t2">{{hotel.address}}</text></view></view><view class="content" style="width:80%;margin:0 auto;display:flex;justify-content:space-between;"><view class="item1" data-latitude="{{hotel.latitude}}" data-longitude="{{hotel.longitude}}" data-company="{{hotel.name}}" data-address="{{hotel.address}}" data-event-opts="{{[['tap',[['openLocation',['$event']]]]]}}" bindtap="__e"><image src="{{pre_url+'/static/img/hotel/add2.png'}}"></image><text>地图导航</text></view><view class="item1" data-phone="{{hotel.tel}}" data-event-opts="{{[['tap',[['phone',['$event']]]]]}}" catchtap="__e"><image src="{{pre_url+'/static/img/hotel/tel.png'}}"></image><text>{{"联系"+text['酒店']}}</text></view></view></view><view class="orderinfo1"><view class="item flex-bt"><view class="f1"><label class="t1">在线支付</label><text class="price flex-bt">{{"￥"+detail.totalprice+''}}</text></view><view data-event-opts="{{[['tap',[['mignxiChange',['$event']]]]]}}" class="cost-details flex flex-y-center" style="{{'color:'+($root.m0)+';'}}" bindtap="__e">费用明细<text class="iconfont iconjiantou" style="color:#999;font-weight:normal;"></text></view></view></view><view class="orderinfo1"><view class="time-view flex flex-y-center flex-bt"><view class="time-options flex flex-y-center flex-bt"><view class="month-tetx">{{detail.in_date}}</view></view><view class="content-text"><view class="content-decorate left-c-d"></view>{{''+detail.daycount+'晚'}}<view class="content-decorate right-c-d"></view></view><view class="time-options flex flex-y-center flex-bt"><view class="month-tetx">{{detail.leave_date}}</view></view></view><view class="name-info flex flex-y-center flex-bt"><view class="flex flex-col"><view class="name-text">{{room.name}}</view><view class="name-tisp">{{room.tag}}</view></view><view data-event-opts="{{[['tap',[['showDetail',['$event']]]]]}}" class="hotel-details-view flex flex-y-center" style="{{'color:'+($root.m1)+';'}}" bindtap="__e">查看房型<image src="{{pre_url+'/static/img/arrowdown.png'}}"></image></view></view><view class="item"><view class="f1"><label class="t1">入住姓名</label><text class="t2 flex-bt">{{detail.linkman+''}}</text></view></view><view class="item"><view class="f1"><label class="t1">联系手机</label><text class="t2 flex-bt">{{detail.tel+''}}</text></view></view><block wx:if="{{detail.message}}"><view class="item"><view class="f1"><label class="t1">预定备注</label><text class="t2 flex-bt">{{detail.message+''}}</text></view></view></block></view><block wx:if="{{detail.isbefore==1&&(detail.real_usemoney>0||detail.real_roomprice>0)}}"><view class="orderinfo"><view class="title">实际支付</view><view class="item flex-bt"><text class="t1">房费</text><block wx:if="{{detail.real_usemoney>0&&detail.real_roomprice>0}}"><text class="t2 red">{{detail.real_usemoney+moneyunit+" + ￥"+detail.real_roomprice}}</text></block><block wx:else><block wx:if="{{detail.real_usemoney>0&&detail.real_roomprice==0}}"><text class="t2 red">{{detail.real_usemoney+moneyunit}}</text></block><block wx:else><text class="t2 red">{{"￥"+detail.real_roomprice}}</text></block></block></view><block wx:if="{{detail.real_fuwu_money>0}}"><view class="item flex-bt"><text class="t1">{{text['服务费']}}</text><view class="ordernum-info flex-bt"><text class="t2 red">{{"￥"+detail.real_fuwu_money}}</text></view></view></block></view></block><block wx:if="{{detail.yajin_money>0&&detail.status>2}}"><view class="orderinfo"><view class="title">押金信息</view><view class="item flex-bt"><text class="t1">押金状态</text><view class="ordernum-info flex-bt"><block wx:if="{{detail.yajin_refund_status==0}}"><text class="t2">待申请</text></block><block wx:if="{{detail.yajin_refund_status==1}}"><text class="t2">审核中</text></block><block wx:if="{{detail.yajin_refund_status==2}}"><text class="t2">已退款</text></block><block wx:if="{{detail.yajin_refund_status==-1}}"><text class="t2">已驳回</text></block></view></view><block wx:if="{{detail.yajin_refund_status==-1}}"><view class="item flex-bt"><text class="t1">驳回原因</text><view class="ordernum-info flex-bt"><text class="t2">{{detail.yajin_refund_reason?detail.yajin_refund_reason:'无'}}</text></view></view></block></view></block><view class="orderinfo"><view class="title">订单信息</view><view class="item flex-bt"><text class="t1">订单编号</text><view class="ordernum-info flex-bt"><text class="t2" user-select="true" selectable="true">{{detail.ordernum}}</text><view class="btn-class" style="margin-left:20rpx;" data-text="{{detail.ordernum}}" data-event-opts="{{[['tap',[['copy',['$event']]]]]}}" bindtap="__e">复制</view></view></view><view class="item"><text class="t1">下单时间</text><text class="t2">{{detail.createtime}}</text></view><block wx:if="{{detail.status>0&&detail.paytypeid!='4'&&detail.paytime}}"><view class="item"><text class="t1">支付时间</text><text class="t2">{{detail.paytime}}</text></view></block><block wx:if="{{detail.status>0&&detail.paytime}}"><view class="item"><text class="t1">支付方式</text><text class="t2">{{detail.paytype}}</text></view></block><block wx:if="{{detail.daodian_time}}"><view class="item"><text class="t1">到店时间</text><text class="t2">{{detail.daodian_time}}</text></view></block><block wx:if="{{detail.collect_time}}"><view class="item"><text class="t1">离店日期</text><text class="t2">{{detail.real_leavedate}}</text></view></block><view class="item"><text class="t1">订单状态</text><block wx:if="{{detail.status==0}}"><text class="t2">未付款</text></block><block wx:if="{{detail.status==1}}"><text class="t2">待确认</text></block><block wx:if="{{detail.status==2}}"><text class="t2">待入住</text></block><block wx:if="{{detail.status==3}}"><text class="t2">已到店</text></block><block wx:if="{{detail.status==4}}"><text class="t2">已离店</text></block><block wx:if="{{detail.status==5}}"><text class="t2">已完成</text></block><block wx:if="{{detail.status==-1}}"><text class="t2">已关闭</text></block></view><block wx:if="{{detail.refund_status>0}}"><view class="item"><text class="t1">退款状态</text><block wx:if="{{detail.refund_status==1}}"><text class="t2 red">{{"审核中,¥"+detail.refund_money}}</text></block><block wx:if="{{detail.refund_status==2}}"><text class="t2 red">{{"已退款,¥"+detail.refund_money}}</text></block><block wx:if="{{detail.refund_status==3}}"><text class="t2 red">{{"已驳回,¥"+detail.refund_money}}</text></block></view></block><block wx:if="{{detail.refund_status>0}}"><view class="item"><text class="t1">退款原因</text><text class="t2 red">{{detail.refund_reason}}</text></view></block><block wx:if="{{detail.refund_checkremark}}"><view class="item"><text class="t1">审核备注</text><text class="t2 red">{{detail.refund_checkremark}}</text></view></block></view><block wx:if="{{$root.g0>0}}"><view class="orderinfo"><block wx:for="{{detail.formdata}}" wx:for-item="item" wx:for-index="__i0__" wx:key="*this"><view class="item"><text class="t1">{{item[0]}}</text><block wx:if="{{item[2]=='upload'}}"><view class="t2"><image style="width:400rpx;height:auto;" src="{{item[1]}}" mode="widthFix" data-url="{{item[1]}}" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image></view></block><block wx:else><text class="t2" user-select="true" selectable="true">{{item[1]}}</text></block></view></block></view></block><view style="width:100%;height:calc(160rpx + env(safe-area-inset-bottom));"></view><view class="bottom notabbarbot"><block wx:if="{{detail.status==0}}"><block><view class="btn2" data-id="{{detail.id}}" data-event-opts="{{[['tap',[['toclose',['$event']]]]]}}" bindtap="__e">关闭订单</view><view class="btn1" style="{{'background:'+($root.m2)+';'}}" data-url="{{'/pagesExt/pay/pay?id='+detail.payorderid}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">去付款</view></block></block><block wx:if="{{detail.status==1&&detail.isrefund==1}}"><block><block wx:if="{{detail.refund_status==0||detail.refund_status==3}}"><view class="btn2" data-url="{{'refund?id='+detail.id+'&price='+detail.totalprice}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">申请退款</view></block></block></block><block wx:if="{{detail.status==2&&detail.isrefund==1}}"><block><block wx:if="{{detail.paytypeid!='4'}}"><block><block wx:if="{{detail.refund_status==0||detail.refund_status==3}}"><view class="btn2" data-url="{{'refund?id='+detail.id+'&price='+detail.totalprice}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">申请退款</view></block></block></block></block></block><block wx:if="{{detail.status==2}}"><block><view data-event-opts="{{[['tap',[['showhxqr',['$event']]]]]}}" class="btn2" bindtap="__e">核销码</view></block></block><block wx:if="{{detail.status==-1}}"><block><view class="btn2" data-id="{{detail.id}}" data-event-opts="{{[['tap',[['todel',['$event']]]]]}}" bindtap="__e">删除订单</view></block></block></view><uni-popup class="vue-ref" vue-id="150c6601-1" id="dialogHxqr" type="dialog" data-ref="dialogHxqr" bind:__l="__l" vue-slots="{{['default']}}"><view class="hxqrbox"><image class="img" src="{{detail.hexiao_qr}}" data-url="{{detail.hexiao_qr}}" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image><view class="txt">请出示核销码给核销员进行核销</view><view data-event-opts="{{[['tap',[['closeHxqr',['$event']]]]]}}" class="close" bindtap="__e"><image style="width:100%;height:100%;" src="{{pre_url+'/static/img/close2.png'}}"></image></view></view></uni-popup><uni-popup class="vue-ref" vue-id="150c6601-2" id="popup" type="bottom" data-ref="popup" bind:__l="__l" vue-slots="{{['default']}}"><view class="hotelpopup__content"><view data-event-opts="{{[['tap',[['popupClose',['$event']]]]]}}" class="popup-close" bindtap="__e"><image src="{{pre_url+'/static/img/hotel/popupClose2.png'}}"></image></view><scroll-view style="height:auto;max-height:50vh;" scroll-y="{{true}}"><view class="hotel-equity-view flex flex-col"><view class="equity-title-view flex" style="padding-bottom:40rpx;"><view class="equity-title">费用明细</view></view><view class="cost-details flex flex-col"><view class="price-view flex flex-bt flex-y-center" style="margin-bottom:30rpx;"><view class="price-text-title">房费</view></view><view class="price-view flex flex-bt flex-y-center"><view class="price-text">优惠券抵扣</view><view class="price-num">{{"￥"+detail.coupon_money}}</view></view><view class="price-view flex flex-bt flex-y-center"><view class="price-text">{{$root.m3+"抵扣"}}</view><view class="price-num">{{(detail.use_money?detail.use_money:0)+moneyunit}}</view></view><view class="price-view flex flex-bt flex-y-center"><view class="price-text">现金支付</view><view class="price-num">{{"￥"+detail.leftmoney}}</view></view></view><view class="cost-details flex flex-col"><view class="price-view flex flex-bt flex-y-center" style="margin-bottom:30rpx;"><view class="price-text-title">其他</view><view class="price-num-title"></view></view><block wx:if="{{detail.yajin_money>0}}"><view class="price-view flex flex-bt flex-y-center"><view class="price-text">押金(可退)</view><view class="price-num">{{"￥"+detail.yajin_money}}</view></view></block><block wx:if="{{detail.fuwu_money>0}}"><view class="price-view flex flex-bt flex-y-center"><view class="price-text">{{text['服务费']}}</view><view class="price-num">{{"￥"+detail.fuwu_money}}</view></view></block></view><block wx:if="{{detail.couponmoney>0||detail.scoredk_money>0}}"><view class="cost-details flex flex-col"><view class="price-view flex flex-bt flex-y-center" style="margin-bottom:30rpx;"><view class="price-text-title">优惠</view><view class="price-num-title"></view></view><block wx:if="{{detail.couponmoney>0}}"><view class="price-view flex flex-bt flex-y-center"><view class="price-text">优惠券抵扣</view><view class="price-num">{{"-￥"+detail.couponmoney}}</view></view></block><block wx:if="{{detail.scoredk_money>0}}"><view class="price-view flex flex-bt flex-y-center"><view class="price-text">{{$root.m4+"抵扣"}}</view><view class="price-num">{{"-￥"+detail.scoredk_money}}</view></view></block></view></block></view></scroll-view></view></uni-popup><uni-popup class="vue-ref" vue-id="150c6601-3" id="popupDetail" type="bottom" data-ref="popupDetail" bind:__l="__l" vue-slots="{{['default']}}"><view class="hotelpopup__content"><view data-event-opts="{{[['tap',[['popupdetailClose',['$event']]]]]}}" class="popup-close" bindtap="__e"><image src="{{pre_url+'/static/img/hotel/popupClose.png'}}"></image></view><scroll-view style="height:auto;max-height:70vh;" scroll-y="{{true}}"><view class="popup-banner-view" style="height:450rpx;"><swiper class="dp-banner-swiper" autoplay="{{true}}" indicator-dots="{{false}}" current="{{0}}" circular="{{true}}" interval="{{3000}}" data-event-opts="{{[['change',[['swiperChange',['$event']]]]]}}" bindchange="__e"><block wx:for="{{room.pics}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><swiper-item><view data-event-opts="{{[['tap',[['viewPicture',['$0'],[[['room.pics','',index]]]]]]]}}" bindtap="__e"><image class="dp-banner-swiper-img" src="{{item}}" mode="widthFix"></image></view></swiper-item></block></block></swiper><block wx:if="{{$root.g1}}"><view class="popup-numstatistics flex flex-xy-center">{{''+bannerindex+" / "+$root.g2+''}}</view></block></view><view class="hotel-details-view flex flex-col"><view class="hotel-title">{{room.name}}</view><view class="introduce-view flex"><block wx:if="{{room.bedxing!='不显示'}}"><view class="options-intro flex flex-y-center"><image src="{{pre_url+'/static/img/hotel/dachuang.png'}}"></image><view class="options-title">{{room.bedxing}}</view></view></block><view class="options-intro flex flex-y-center"><image src="{{pre_url+'/static/img/hotel/pingfang.png'}}"></image><view class="options-title">{{room.square+"m²"}}</view></view><view class="options-intro flex flex-y-center"><image src="{{pre_url+'/static/img/hotel/dachuang.png'}}"></image><view class="options-title">{{room.bedwidth+"米"}}</view></view><block wx:if="{{room.ischuanghu!='0'}}"><view class="options-intro flex flex-y-center"><image src="{{pre_url+'/static/img/hotel/youchuang.png'}}"></image><view class="options-title">{{room.ischuanghu}}</view></view></block><block wx:if="{{room.breakfast!='不显示'}}"><view class="options-intro flex flex-y-center"><image src="{{pre_url+'/static/img/hotel/zaocan.png'}}"></image><view class="options-title">{{room.breakfast+"早餐"}}</view></view></block></view><view class="other-view flex flex-y-center"><view class="other-title">特色</view><view class="other-text" style="white-space:pre-line;">{{room.tese}}</view></view></view><block wx:if="{{qystatus==1}}"><view class="hotel-equity-view flex flex-col"><view class="equity-title-view flex"><view class="equity-title">{{qyname}}</view></view><view class="equity-options flex flex-col"><parse vue-id="{{('150c6601-4')+','+('150c6601-3')}}" content="{{hotel.hotelquanyi}}" data-event-opts="{{[['^navigate',[['navigate']]]]}}" bind:navigate="__e" bind:__l="__l"></parse></view></view></block><block wx:if="{{fwstatus==1}}"><view class="hotel-equity-view flex flex-col"><view class="equity-title-view flex"><view class="equity-title">{{fwname}}</view></view><view class="equity-options flex flex-col"><parse vue-id="{{('150c6601-5')+','+('150c6601-3')}}" content="{{hotel.hotelfuwu}}" data-event-opts="{{[['^navigate',[['navigate']]]]}}" bind:navigate="__e" bind:__l="__l"></parse></view></view></block></scroll-view></view></uni-popup></block></block><block wx:if="{{loading}}"><loading vue-id="150c6601-6" bind:__l="__l"></loading></block><dp-tabbar vue-id="150c6601-7" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="150c6601-8" data-ref="popmsg" bind:__l="__l"></popmsg><wxxieyi vue-id="150c6601-9" bind:__l="__l"></wxxieyi></view>