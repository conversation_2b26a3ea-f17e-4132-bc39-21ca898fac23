<view class="container"><block wx:if="{{isload}}"><block><view class="mymoney" style="{{'background:'+($root.m0)+';'}}"><view class="f1">积分转佣金</view><view class="f2"><text style="font-size:26rpx;">积分：</text>{{userinfo.score||0}}</view><view class="f3" data-url="/pages/money/moneylog?st=6" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><text>转换记录</text><text class="iconfont iconjiantou" style="font-size:20rpx;"></text></view><view class="f1" style="font-size:20rpx;margin-top:20px;">转换比例</view><view class="f2"><text style="font-size:26rpx;"></text>{{(config.rate||100)+" ：1"}}</view></view><view class="content2"><view class="balance-info"><view class="balance-item"><text class="balance-label">当前积分</text><text class="balance-value">{{userinfo.score||0}}</text></view><view class="balance-item"><text class="balance-label">当前佣金</text><text class="balance-value">{{userinfo.commission||0}}</text></view></view><view class="rules-section"><view class="rules-title">转换规则</view><view class="rules-content"><view class="rule-item">{{"• 兑换比例："+(config.rate||100)+"积分 = 1佣金"}}</view><block wx:if="{{config.beishu>0}}"><view class="rule-item">{{"• 转换倍数：必须是"+config.beishu+"的倍数"}}</view></block><view class="rule-item">{{"• 转换范围："+(config.min||0)+" - "+(config.max>0?config.max:'不限制')+"积分"}}</view><block wx:if="{{config.fee_rate>0}}"><view class="rule-item">{{"• 手续费："+$root.g0+"%"}}</view></block></view></view><block wx:if="{{caninput==1}}"><block><view class="item3"><view class="f2"><input style="font-size:60rpx;" type="digit" name="score" placeholder="请输入转换积分数量" placeholder-style="color:#999;font-size:40rpx" data-event-opts="{{[['input',[['scoreinput',['$event']]]]]}}" value="{{score}}" bindinput="__e"/></view></view></block></block><block wx:if="{{score>0}}"><view class="preview-section"><view class="preview-item"><text class="preview-label">预计获得佣金：</text><text class="preview-value">{{$root.g1}}</text></view><block wx:if="{{calculatedFee>0}}"><view class="preview-item"><text class="preview-label">手续费：</text><text class="preview-value fee">{{$root.g2}}</text></view></block></view></block><block wx:if="{{caninput==1&&config.need_paypwd}}"><block><view class="item3"><view class="f2"><input style="font-size:60rpx;" type="number" name="paypwd" placeholder="请输入支付密码" placeholder-style="color:#999;font-size:40rpx" maxlength="6" data-event-opts="{{[['input',[['paypwdinput',['$event']]]]]}}" value="{{paypwd}}" bindinput="__e"/></view></view></block></block><block wx:if="{{shuoming}}"><view style="margin-top:40rpx;padding:0 30rpx;line-height:42rpx;"><parse vue-id="75b6d147-1" content="{{shuoming}}" data-event-opts="{{[['^navigate',[['navigate']]]]}}" bind:navigate="__e" bind:__l="__l"></parse></view></block></view><view class="op"><view data-event-opts="{{[['tap',[['convert',['$event']]]]]}}" class="btn" style="{{'background:'+($root.m1)+';'}}" bindtap="__e">确认转换</view></view><block wx:if="{{config.need_paypwd&&!userinfo.has_paypwd}}"><view style="width:100%;margin-top:40rpx;text-align:center;color:#999;display:flex;align-items:center;justify-content:center;" data-url="/pages/my/paypwd" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">设置支付密码<image style="width:30rpx;height:30rpx;" src="/static/img/arrowright.png"></image></view></block></block></block><block wx:if="{{loading}}"><loading vue-id="75b6d147-2" bind:__l="__l"></loading></block><dp-tabbar vue-id="75b6d147-3" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="75b6d147-4" data-ref="popmsg" bind:__l="__l"></popmsg></view>