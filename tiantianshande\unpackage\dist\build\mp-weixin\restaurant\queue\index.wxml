<view class="container"><block wx:if="{{isload}}"><block><view class="topbannerbg" style="{{(business.pic?'background:url('+business.pic+') 100%':'')}}"></view><view class="topbannerbg2"></view><view class="topbanner"><view class="left"><image class="img" src="{{business.logo}}"></image></view><view class="right"><view class="f1">{{business.name}}</view><view class="f2">{{business.desc}}</view></view></view><view class="notice"><view class="content"><image class="f1" src="/static/img/queue-notice.png"></image><view class="f2">{{notice}}</view></view></view><view class="clist"><view class="title">当前排队</view><view class="content"><block wx:for="{{clist}}" wx:for-item="item" wx:for-index="__i0__" wx:key="*this"><view class="item"><view class="f1"><view class="t1">{{item.name}}</view><view class="t2">{{item.seat_min+"-"+item.seat_max+"人"}}</view></view><view class="f2">等待<text style="color:#FC5729;padding:0 6rpx;font-size:28rpx;">{{item.waitnum}}</text>桌</view><view class="f3"><text style="color:#FC5729;padding:0 6rpx;font-size:28rpx;">{{item.need_minute}}</text>分钟</view></view></block></view></view><block wx:if="{{myqueue}}"><view class="myqueue"><view class="title"><text class="t1">我的排队</text><text class="t2" data-id="{{myqueue.id}}" data-queue_no="{{myqueue.queue_no}}" data-event-opts="{{[['tap',[['cancel',['$event']]]]]}}" bindtap="__e">取消排队</text></view><view class="content"><view class="f1">{{myqueue.queue_no}}</view><view class="f2">{{myqueue.cname}}</view><view class="f2">{{"取号时间："+$root.m0}}</view></view><view class="bottom"><view class="f1">前面还有<text style="color:#FC5729;padding:0 6rpx;">{{myqueue.beforenum}}</text>人等待中</view><view class="f2">预计等待<text style="color:#FC5729;padding:0 6rpx;">{{myqueue.need_minute}}</text>分钟</view></view></view></block><block wx:if="{{myjustqueue}}"><view class="myqueue"><view class="title"><text class="t1">我的排队</text><text class="t2">已叫号 请前往用餐</text></view><view class="content"><view class="f1">{{myjustqueue.queue_no}}</view><view class="f2">{{myjustqueue.cname}}</view><view class="f2">{{"取号时间："+$root.m1}}</view><view class="f2">{{"叫号时间："+$root.m2}}</view></view></view></block><block wx:if="{{!myqueue}}"><view class="btn" data-url="{{'quhao?bid='+bid}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">取号排队</view></block><block wx:if="{{!myqueue}}"><view class="log" data-url="{{'record?bid='+bid}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">我的排队记录</view></block></block></block><block wx:if="{{loading}}"><loading vue-id="6a872276-1" bind:__l="__l"></loading></block><dp-tabbar vue-id="6a872276-2" opt="{{opt}}" data-event-opts="{{[['^getmenuindex',[['getmenuindex']]]]}}" bind:getmenuindex="__e" bind:__l="__l"></dp-tabbar></view>