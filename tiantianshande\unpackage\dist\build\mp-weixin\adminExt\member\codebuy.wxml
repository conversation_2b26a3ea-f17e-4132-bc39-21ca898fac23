<view><block wx:if="{{isload}}"><block><view class="orderinfo"><view class="item"><text class="t1">ID</text><text class="t2">{{member.id}}</text></view><view class="item"><text class="t1">头像</text><view class="t2"><image style="width:80rpx;height:80rpx;" src="{{member.headimg}}"></image></view></view><view class="item"><text class="t1">昵称</text><text class="t2">{{member.nickname}}</text></view><view class="item"><text class="t1">加入时间</text><text class="t2">{{member.createtime}}</text></view><view class="item"><text class="t1">{{$root.m0}}</text><text class="t2">{{member.money}}</text></view><view class="item"><text class="t1">{{$root.m1}}</text><text class="t2">{{member.score}}</text></view><view class="item"><text class="t1">等级</text><text class="t2">{{member.levelname}}</text></view></view><view style="width:100%;height:60rpx;"></view><button class="btn" data-id="{{member.id}}" data-event-opts="{{[['tap',[['addscore',['$event']]]]]}}" bindtap="__e">{{$root.m2+"消费"}}</button><uni-popup class="vue-ref" vue-id="e765c2ba-1" id="addscoreDialog" type="dialog" data-ref="addscoreDialog" bind:__l="__l" vue-slots="{{['default']}}"><uni-popup-dialog vue-id="{{('e765c2ba-2')+','+('e765c2ba-1')}}" mode="input" title="{{$root.m3+'消费'}}" value="" placeholder="{{'请输入消费'+$root.m4+'数'}}" data-event-opts="{{[['^confirm',[['addscoreConfirm']]]]}}" bind:confirm="__e" bind:__l="__l"></uni-popup-dialog></uni-popup></block></block><popmsg class="vue-ref" vue-id="e765c2ba-3" data-ref="popmsg" bind:__l="__l"></popmsg><block wx:if="{{loading}}"><loading vue-id="e765c2ba-4" bind:__l="__l"></loading></block></view>