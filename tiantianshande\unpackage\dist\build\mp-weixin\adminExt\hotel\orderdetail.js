require('../common/vendor.js');(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["adminExt/hotel/orderdetail"],{1984:function(t,e,n){"use strict";(function(t,e){var o=n("47a9");n("06e9");o(n("3240"));var i=o(n("7199"));t.__webpack_require_UNI_MP_PLUGIN__=n,e(i.default)}).call(this,n("3223")["default"],n("df3c")["createPage"])},4312:function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var o=getApp(),i=null,a={data:function(){var t=this.getDate({format:!0});return{opt:{},loading:!1,isload:!1,menuindex:-1,pre_url:o.globalData.pre_url,djs:"",detail:"",hotel:"",lefttime:"",codtxt:"",date:t,text:[]}},computed:{startDate:function(){return this.getDate("start")},endDate:function(){return this.getDate("end")}},onLoad:function(t){this.opt=o.getopts(t),this.getdata()},onPullDownRefresh:function(){this.getdata()},onUnload:function(){clearInterval(i)},methods:{getdata:function(){var t=this;t.loading=!0,o.get("ApiAdminHotelOrder/detail",{id:t.opt.id},(function(e){t.loading=!1,t.detail=e.detail,t.hotel=e.hotel,t.lefttime=e.lefttime,t.codtxt=e.codtxt,t.text=e.text,e.lefttime>0&&(i=setInterval((function(){t.lefttime=t.lefttime-1,t.getdjs()}),1e3)),t.loaded()}))},getdjs:function(){var t=this.lefttime;if(t<=0)this.djs="00时00分00秒";else{var e=Math.floor(t/3600),n=Math.floor((t-3600*e)/60),o=t-3600*e-60*n,i=(e<10?"0":"")+e+"时"+(n<10?"0":"")+n+"分"+(o<10?"0":"")+o+"秒";this.djs=i}},setremark:function(){this.$refs.dialogSetremark.open()},setremarkconfirm:function(t,e){this.$refs.dialogSetremark.close();var n=this;o.post("ApiAdminHotelOrder/setremark",{type:"hotel",orderid:n.detail.id,content:e},(function(t){o.success(t.msg),setTimeout((function(){n.getdata()}),1e3)}))},qrdaodian:function(t){var e=this,n=t.currentTarget.dataset.id;o.confirm("确定用户已经到店吗?",(function(){o.showLoading("提交中"),o.post("ApiAdminHotelOrder/qrdaodian",{orderid:n},(function(t){o.showLoading(!1),o.success(t.msg),setTimeout((function(){e.getdata()}),1e3)}))}))},qrlidian:function(){this.$refs.dialogLeave.open()},dialogLeaveClose:function(){this.$refs.dialogLeave.close()},confirmleave:function(){this.$refs.dialogLeave.close();var t=this;o.post("ApiAdminHotelOrder/confirmleave",{orderid:t.detail.id,real_leavedate:t.date},(function(e){o.success(e.msg),setTimeout((function(){t.getdata()}),1e3)}))},ispay:function(t){var e=this,n=t.currentTarget.dataset.id;o.confirm("确定要改为已支付吗?",(function(){o.showLoading("提交中"),o.post("ApiAdminOrder/ispay",{type:"hotel",orderid:n},(function(t){o.showLoading(!1),o.success(t.msg),setTimeout((function(){e.getdata()}),1e3)}))}))},confirmorder:function(t){var e=this,n=t.currentTarget.dataset.id;o.confirm("确定确认该订单吗?",(function(){o.showLoading("提交中"),o.post("ApiAdminHotelOrder/confirmorder",{orderid:n},(function(t){o.showLoading(!1),o.success(t.msg),setTimeout((function(){e.getdata()}),1e3)}))}))},refund:function(t){var e=this,n=t.currentTarget.dataset.id;o.confirm("确定要拒单并退款吗?",(function(){o.showLoading("提交中"),o.post("ApiAdminHotelOrder/judan",{type:"hotel",orderid:n},(function(t){o.showLoading(!1),o.success(t.msg),setTimeout((function(){e.getdata()}),1e3)}))}))},refundYajin:function(t){var e=this,n=t.currentTarget.dataset.id;o.confirm("确定要退还押金吗?",(function(){o.showLoading("提交中"),o.post("ApiAdminHotelOrder/refundYajin",{type:"hotel",orderid:n},(function(t){o.showLoading(!1),o.success(t.msg),setTimeout((function(){e.getdata()}),1e3)}))}))},delOrder:function(t){var e=t.currentTarget.dataset.id;o.showLoading("删除中"),o.confirm("确定要删除该订单吗?",(function(){o.post("ApiAdminHotelOrder/delOrder",{type:"hotel",orderid:e},(function(t){o.showLoading(!1),o.success(t.msg),setTimeout((function(){o.goto("takeawayorder")}),1e3)}))}))},closeOrder:function(t){var e=this,n=t.currentTarget.dataset.id;o.confirm("确定要关闭该订单吗?",(function(){o.showLoading("提交中"),o.post("ApiAdminHotelOrder/closeOrder",{type:"hotel",orderid:n},(function(t){o.showLoading(!1),o.success(t.msg),setTimeout((function(){e.getdata()}),1e3)}))}))},refundnopass:function(t){var e=this,n=t.currentTarget.dataset.id;o.confirm("确定要驳回退款申请吗?",(function(){o.showLoading("提交中"),o.post("ApiAdminOrder/refundnopass",{type:"hotel",orderid:n},(function(t){o.showLoading(!1),o.success(t.msg),setTimeout((function(){e.getdata()}),1e3)}))}))},refundpass:function(t){var e=this,n=t.currentTarget.dataset.id;o.confirm("确定要审核通过并退款吗?",(function(){o.showLoading("提交中"),o.post("ApiAdminHotelOrder/refundpass",{type:"hotel",orderid:n},(function(t){o.showLoading(!1),o.success(t.msg),setTimeout((function(){e.getdata()}),1e3)}))}))},bindDateChange:function(t){this.date=t.detail.value},getDate:function(t){var e=new Date,n=e.getFullYear(),o=e.getMonth()+1,i=e.getDate();return"start"===t?n-=60:"end"===t&&(n+=2),o=o>9?o:"0"+o,i=i>9?i:"0"+i,"".concat(n,"-").concat(o,"-").concat(i)}}};e.default=a},"4ad2":function(t,e,n){"use strict";n.d(e,"b",(function(){return i})),n.d(e,"c",(function(){return a})),n.d(e,"a",(function(){return o}));var o={uniPopup:function(){return Promise.all([n.e("common/vendor"),n.e("components/uni-popup/uni-popup")]).then(n.bind(null,"ca44a"))},uniPopupDialog:function(){return n.e("components/uni-popup-dialog/uni-popup-dialog").then(n.bind(null,"267c"))},loading:function(){return n.e("components/loading/loading").then(n.bind(null,"ceaa"))},dpTabbar:function(){return n.e("components/dp-tabbar/dp-tabbar").then(n.bind(null,"b875"))}},i=function(){var t=this,e=t.$createElement,n=(t._self._c,t.isload?t.t("会员"):null),o=t.isload&&1==t.detail.isbefore&&t.detail.real_usemoney>0&&t.detail.real_roomprice>0?t.t("余额单位"):null,i=t.isload&&1==t.detail.isbefore&&t.detail.real_usemoney>0&&0==t.detail.real_roomprice?t.t("余额单位"):null,a=t.isload&&t.detail.use_money>0&&t.detail.leftmoney>0?t.t("余额单位"):null,r=t.isload&&!(t.detail.use_money>0&&t.detail.leftmoney>0)&&t.detail.use_money>0&&0==t.detail.leftmoney?t.t("余额单位"):null,d=t.isload&&t.detail.leveldk_money>0?t.t("会员"):null,s=t.isload&&t.detail.couponmoney>0?t.t("优惠券"):null,u=t.isload&&t.detail.scoredk_money>0?t.t("积分"):null,c=t.isload&&t.detail.use_money>0?t.t("余额"):null,l=t.isload&&t.detail.use_money>0?t.t("余额单位"):null;t.$mp.data=Object.assign({},{$root:{m0:n,m1:o,m2:i,m3:a,m4:r,m5:d,m6:s,m7:u,m8:c,m9:l}})},a=[]},7199:function(t,e,n){"use strict";n.r(e);var o=n("4ad2"),i=n("9533");for(var a in i)["default"].indexOf(a)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(a);n("be7c");var r=n("828b"),d=Object(r["a"])(i["default"],o["b"],o["c"],!1,null,null,null,!1,o["a"],void 0);e["default"]=d.exports},9533:function(t,e,n){"use strict";n.r(e);var o=n("4312"),i=n.n(o);for(var a in o)["default"].indexOf(a)<0&&function(t){n.d(e,t,(function(){return o[t]}))}(a);e["default"]=i.a},"973c":function(t,e,n){},be7c:function(t,e,n){"use strict";var o=n("973c"),i=n.n(o);i.a}},[["1984","common/runtime","common/vendor"]]]);