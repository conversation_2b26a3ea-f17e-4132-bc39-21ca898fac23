<view class="container"><block wx:if="{{isload}}"><block><view class="header-container"><view class="search-box"><input class="search-input" type="text" placeholder="搜索配置名称" data-event-opts="{{[['confirm',[['onSearch',['$event']]]],['input',[['__set_model',['','searchKeyword','$event',[]]]]]]}}" value="{{searchKeyword}}" bindconfirm="__e" bindinput="__e"/><view data-event-opts="{{[['tap',[['onSearch',['$event']]]]]}}" class="search-btn" bindtap="__e"><text>搜索</text></view></view><view class="filter-tabs"><block wx:for="{{statusTabs}}" wx:for-item="tab" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['changeStatus',['$0'],[[['statusTabs','',index,'value']]]]]]]}}" class="{{['tab-item',(currentStatus==tab.value)?'active':'']}}" bindtap="__e"><text>{{tab.label}}</text></view></block></view></view><view class="content-container"><scroll-view class="scroll-view" scroll-y="true" refresher-enabled="true" refresher-triggered="{{refreshing}}" data-event-opts="{{[['scrolltolower',[['loadmore',['$event']]]],['refresherrefresh',[['onRefresh',['$event']]]]]}}" bindscrolltolower="__e" bindrefresherrefresh="__e"><view class="config-list"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['toConfigDetail',['$0'],[[['configList','',index,'id']]]]]]]}}" class="config-item" bindtap="__e"><view class="config-header"><view class="config-info"><text class="config-name">{{item.$orig.name}}</text><text class="config-time">{{"创建时间："+item.$orig.createtime_text}}</text></view><view class="{{['status-badge',item.$orig.status==1?'active':'inactive']}}"><text>{{item.m0}}</text></view></view><view class="config-content"><block wx:if="{{item.$orig.description}}"><view class="config-desc"><text>{{item.$orig.description}}</text></view></block><view class="config-details"><view class="detail-row"><view class="detail-item"><text class="label">总点位：</text><text class="value" style="color:#FF5722;">{{item.$orig.total_positions||0}}</text></view><view class="detail-item"><text class="label">财富点位：</text><text class="value" style="color:#FF5722;">{{item.$orig.wealth_positions||0}}</text></view></view><view class="detail-row"><view class="detail-item"><text class="label">单数奖励：</text><text class="value">{{item.$orig.reward_amount||0}}</text></view><view class="detail-item"><text class="label">奖励类型：</text><text class="value">{{item.m1}}</text></view></view><block wx:if="{{item.$orig.auto_claim!==undefined}}"><view class="detail-row"><view class="detail-item"><text class="label">领取方式：</text><text class="value">{{item.$orig.auto_claim?'自动领取':'手动领取'}}</text></view><block wx:if="{{item.$orig.deduct_contribution_rate>0}}"><view class="detail-item"><text class="label">扣除比例：</text><text class="value">{{item.$orig.deduct_contribution_rate+"%"}}</text></view></block></view></block></view></view><view class="config-actions"><view data-event-opts="{{[['tap',[['toActivityProducts',['$0'],[[['configList','',index,'id']]]]]]]}}" class="action-btn" catchtap="__e"><text>查看商品</text></view><view data-event-opts="{{[['tap',[['toMyPosition',['$0'],[[['configList','',index,'id']]]]]]]}}" class="action-btn" catchtap="__e"><text>我的点位</text></view><view data-event-opts="{{[['tap',[['toPositionTree',['$0'],[[['configList','',index,'id']]]]]]]}}" class="action-btn primary" style="background:linear-gradient(90deg, #FF5722 0%, rgba(255,87,34,0.8) 100%);" catchtap="__e"><text>排单树</text></view></view></view></block></view><block wx:if="{{$root.g0}}"><view class="empty-state"><image class="empty-icon" src="/static/img/empty-config.png"></image><view class="empty-text">暂无排单配置</view></view></block><block wx:if="{{$root.g1>0}}"><view class="load-more"><block wx:if="{{loading}}"><text>加载中...</text></block><block wx:else><block wx:if="{{!hasMore}}"><text>没有更多了</text></block><block wx:else><text>上拉加载更多</text></block></block></view></block></scroll-view></view></block></block><block wx:if="{{loading}}"><loading vue-id="92732d00-1" loadstyle="left:62.5%" bind:__l="__l"></loading></block><dp-tabbar vue-id="92732d00-2" opt="{{opt}}" data-event-opts="{{[['^getmenuindex',[['getmenuindex']]]]}}" bind:getmenuindex="__e" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="92732d00-3" data-ref="popmsg" bind:__l="__l"></popmsg></view>