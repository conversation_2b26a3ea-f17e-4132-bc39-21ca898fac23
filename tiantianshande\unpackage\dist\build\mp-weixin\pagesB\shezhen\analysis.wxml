<view class="analysis-container data-v-42035be2"><view class="bg-decoration data-v-42035be2"><view class="gradient-bg data-v-42035be2"></view><view class="floating-elements data-v-42035be2"><block wx:for="{{6}}" wx:for-item="n" wx:for-index="__i0__" wx:key="*this"><view class="{{['element','data-v-42035be2','element-'+n]}}"></view></block></view></view><view class="status-header data-v-42035be2"><view class="status-steps data-v-42035be2"><block wx:for="{{analysisSteps}}" wx:for-item="step" wx:for-index="index" wx:key="index"><view class="{{['status-step','data-v-42035be2',(index<=currentStep)?'active':'',(index<currentStep)?'completed':'',(index===currentStep)?'current':'']}}"><view class="step-circle data-v-42035be2"><block wx:if="{{index<currentStep}}"><text class="step-icon data-v-42035be2">✓</text></block><block wx:else><text class="step-number data-v-42035be2">{{index+1}}</text></block><block wx:if="{{index===currentStep}}"><view class="step-pulse data-v-42035be2"></view></block></view><text class="step-label data-v-42035be2">{{step.name}}</text></view></block></view></view><scroll-view class="main-scroll-container data-v-42035be2" scroll-y="true" enhanced="true" bounces="false"><view class="image-section data-v-42035be2"><view class="image-container data-v-42035be2"><image class="analysis-image data-v-42035be2" src="{{imageUrl}}" mode="aspectFit" data-event-opts="{{[['error',[['onImageError',['$event']]]]]}}" binderror="__e"></image><block wx:if="{{isAnalyzing}}"><view class="scan-overlay data-v-42035be2"><view class="scan-grid data-v-42035be2"><block wx:for="{{$root.l0}}" wx:for-item="n" wx:for-index="__i1__" wx:key="$orig"><view class="{{['grid-cell','data-v-42035be2',(n.g0)?'scanned':'']}}"></view></block></view><view class="scan-line horizontal data-v-42035be2" style="{{'top:'+(scanPosition.y+'%')+';'}}"></view><view class="scan-line vertical data-v-42035be2" style="{{'left:'+(scanPosition.x+'%')+';'}}"></view><block wx:for="{{analysisRegions}}" wx:for-item="region" wx:for-index="index" wx:key="index"><view class="{{['analysis-region','data-v-42035be2',(region.active)?'active':'']}}" style="{{'left:'+(region.x+'%')+';'+('top:'+(region.y+'%')+';')+('width:'+(region.width+'%')+';')+('height:'+(region.height+'%')+';')}}"><view class="region-label data-v-42035be2">{{region.name}}</view><view class="region-progress data-v-42035be2"><view class="progress-fill data-v-42035be2" style="{{'width:'+(region.progress+'%')+';'}}"></view></view></view></block></view></block><block wx:if="{{!isAnalyzing&&analysisComplete}}"><view class="analysis-markers data-v-42035be2"><block wx:for="{{analysisMarkers}}" wx:for-item="marker" wx:for-index="index" wx:key="index"><view class="{{['analysis-marker','data-v-42035be2',marker.type]}}" style="{{'left:'+(marker.x+'%')+';'+('top:'+(marker.y+'%')+';')}}"><view class="marker-dot data-v-42035be2"></view><view class="marker-label data-v-42035be2">{{marker.label}}</view></view></block></view></block></view></view><view class="progress-section data-v-42035be2"><view class="progress-card data-v-42035be2"><view class="progress-header data-v-42035be2"><text class="progress-title data-v-42035be2">{{currentTask.title}}</text><text class="progress-percentage data-v-42035be2">{{formattedAnalysisProgress+"%"}}</text></view><view class="progress-bar data-v-42035be2"><view class="progress-track data-v-42035be2"><view class="progress-fill data-v-42035be2" style="{{'width:'+(formattedAnalysisProgress+'%')+';'}}"></view><view class="progress-glow data-v-42035be2"></view></view></view><text class="progress-desc data-v-42035be2">{{currentTask.description}}</text></view></view><view class="logs-section data-v-42035be2"><view class="logs-header data-v-42035be2"><text class="logs-title data-v-42035be2">🔍 分析日志</text><view class="logs-indicator data-v-42035be2"><view class="indicator-dot data-v-42035be2"></view><text class="indicator-text data-v-42035be2">实时监控</text></view></view><view class="logs-container data-v-42035be2"><block wx:for="{{analysisLogs}}" wx:for-item="log" wx:for-index="index" wx:key="index"><view class="{{['log-item','data-v-42035be2',log.status]}}"><view class="log-icon data-v-42035be2"><block wx:if="{{log.status==='completed'}}"><text class="data-v-42035be2">✓</text></block><block wx:else><block wx:if="{{log.status==='processing'}}"><text class="data-v-42035be2">⚡</text></block><block wx:else><text class="data-v-42035be2">⏳</text></block></block></view><view class="log-content data-v-42035be2"><text class="log-title data-v-42035be2">{{log.title}}</text><text class="log-time data-v-42035be2">{{log.time}}</text></view><view class="log-status data-v-42035be2"><view class="{{['status-indicator','data-v-42035be2',log.status]}}"></view></view></view></block></view></view><view class="tips-section data-v-42035be2"><view class="tips-card data-v-42035be2"><view class="tips-icon data-v-42035be2">⏱️</view><text class="tips-text data-v-42035be2">{{"分析预计需要 "+estimatedTime+" 秒，请耐心等待..."}}</text></view></view><view class="safe-area-bottom data-v-42035be2"></view></scroll-view><block wx:if="{{isAnalyzing}}"><view class="ai-animation data-v-42035be2"><view class="ai-brain data-v-42035be2"><view class="brain-core data-v-42035be2"><text class="brain-icon data-v-42035be2">🧠</text><view class="neural-network data-v-42035be2"><block wx:for="{{6}}" wx:for-item="n" wx:for-index="__i2__" wx:key="*this"><view class="{{['neural-node','data-v-42035be2','node-'+n]}}"></view></block></view></view><text class="ai-text data-v-42035be2">AI 正在分析中...</text></view></view></block></view>