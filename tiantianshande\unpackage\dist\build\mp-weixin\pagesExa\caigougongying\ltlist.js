require('../common/vendor.js');(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pagesExa/caigougongying/ltlist"],{"1b87":function(t,e,n){"use strict";var o=n("76f1"),i=n.n(o);i.a},2342:function(t,e,n){"use strict";n.r(e);var o=n("c547"),i=n.n(o);for(var s in o)["default"].indexOf(s)<0&&function(t){n.d(e,t,(function(){return o[t]}))}(s);e["default"]=i.a},"76f1":function(t,e,n){},b101:function(t,e,n){"use strict";n.r(e);var o=n("fdce"),i=n("2342");for(var s in i)["default"].indexOf(s)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(s);n("1b87");var a=n("828b"),r=Object(a["a"])(i["default"],o["b"],o["c"],!1,null,null,null,!1,o["a"],void 0);e["default"]=r.exports},c547:function(t,e,n){"use strict";(function(t){Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n=getApp(),o={data:function(){return{loading:!0,type:1,keywords:"",currentCid:0,page:1,pagesize:10,list:[],categories:[],hasMore:!0,isload:!1}},onLoad:function(e){console.log("页面加载参数:",e),e.type&&(this.type=parseInt(e.type)),e.cid&&(this.currentCid=parseInt(e.cid)),t.setNavigationBarTitle({title:1===this.type?"采购列表":"供应列表"}),this.init()},methods:{init:function(){this.getCategories(),this.getList()},getCategories:function(){console.log("开始获取分类");var t=this;n.get("ApiCaigou/category",{},(function(e){console.log("分类数据:",e),1===e.status?t.categories=e.data||[]:n.error(e.msg||"获取分类失败")}),(function(t){console.error("获取分类错误:",t),n.error("获取分类失败")}))},getList:function(e){console.log("开始获取列表, 参数:",{type:this.type,page:this.page,pagesize:this.pagesize,cid:this.currentCid,keywords:this.keywords});var o=this;e||(o.page=1,o.list=[],o.loading=!0),n.get("ApiCaigou/lists",{type:o.type,page:o.page,pagesize:o.pagesize,cid:o.currentCid||"",keywords:o.keywords},(function(i){if(console.log("列表数据:",i),o.loading=!1,o.isload=!0,1===i.status){var s=i.data;o.list=e?o.list.concat(s.list||[]):s.list||[],o.hasMore=(s.list||[]).length>=o.pagesize}else n.error(i.msg||"获取列表失败");t.stopPullDownRefresh()}),(function(e){console.error("获取列表错误:",e),o.loading=!1,o.isload=!0,n.error("获取列表失败"),t.stopPullDownRefresh()}))},selectCategory:function(t){console.log("选择分类:",t),this.currentCid=t,this.getList()},search:function(){console.log("搜索关键词:",this.keywords),this.getList()},goto:function(e){var n=e.currentTarget.dataset.url;console.log("页面跳转:",n),n&&t.navigateTo({url:n})}},onPullDownRefresh:function(){console.log("触发下拉刷新"),this.getList()},onReachBottom:function(){console.log("触发触底加载"),this.hasMore&&(this.page++,this.getList(!0))}};e.default=o}).call(this,n("df3c")["default"])},dfb0:function(t,e,n){"use strict";(function(t,e){var o=n("47a9");n("06e9");o(n("3240"));var i=o(n("b101"));t.__webpack_require_UNI_MP_PLUGIN__=n,e(i.default)}).call(this,n("3223")["default"],n("df3c")["createPage"])},fdce:function(t,e,n){"use strict";n.d(e,"b",(function(){return i})),n.d(e,"c",(function(){return s})),n.d(e,"a",(function(){return o}));var o={loading:function(){return n.e("components/loading/loading").then(n.bind(null,"ceaa"))},popmsg:function(){return n.e("components/popmsg/popmsg").then(n.bind(null,"2bf2"))}},i=function(){var t=this.$createElement,e=(this._self._c,this.isload?this.categories.length:null),n=this.isload?this.list.length:null;this.$mp.data=Object.assign({},{$root:{g0:e,g1:n}})},s=[]}},[["dfb0","common/runtime","common/vendor"]]]);