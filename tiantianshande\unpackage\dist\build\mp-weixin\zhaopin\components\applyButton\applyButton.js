(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["zhaopin/components/applyButton/applyButton"],{"07fe":function(t,o,e){"use strict";e.r(o);var n=e("9592"),l=e("883c");for(var a in l)["default"].indexOf(a)<0&&function(t){e.d(o,t,(function(){return l[t]}))}(a);e("2ceee");var p=e("828b"),r=Object(p["a"])(l["default"],n["b"],n["c"],!1,null,null,null,!1,n["a"],void 0);o["default"]=r.exports},"131e":function(t,o,e){},2492:function(t,o,e){"use strict";(function(t){Object.defineProperty(o,"__esModule",{value:!0}),o.default=void 0;getApp();var n={components:{openButton:function(){e.e("zhaopin/components/openButton/openButton").then(function(){return resolve(e("ed46"))}.bind(null,e)).catch(e.oe)}},data:function(){return{partJobFavoriteId:"",partJobVoClone:{jobFeeVO:{buttons:{btn2:"",btn1:""}}},collectImgClone:""}},mounted:function(){console.log("applyButton mounted"),console.log("初始props数据:",{partJobVo:this.partJobVo,hasToken:this.hasToken,template:this.partJobVo.template,buttonStatus:this.partJobVo.buttonStatus})},watch:{partJobVo:{handler:function(t,o){console.log("applyButton - partJobVo 更新:",t),this.partJobVoClone=t},immediate:!0,deep:!0},collectImg:{handler:function(t,o){this.collectImgClone=t},immediate:!0,deep:!0}},props:{hasToken:{type:Boolean,default:!1},isShow:{type:Boolean,default:!1},partJobVo:{type:Object,default:function(){return{}}},collectImg:{type:String,default:""},buttonTitle:{type:String,default:"立即报名"},baseInfo:{type:Object,default:function(){return{}}},agreementVo:{type:Object,default:function(){return{}}}},methods:{t:function(t){if("color1"==t)return getApp().globalData.initdata.color1;if("color2"==t)return getApp().globalData.initdata.color2;if("color1rgb"==t){var o=getApp().globalData.initdata.color1rgb;return o["red"]+","+o["green"]+","+o["blue"]}if("color2rgb"==t){var e=getApp().globalData.initdata.color2rgb;return e["red"]+","+e["green"]+","+e["blue"]}return getApp().globalData.initdata.textset[t]||t},onLoginSuccess:function(){this.$emit("loginSuccess")},onPromptlyClicked:function(){this.$emit("loginSuccess"),this.onPromptly()},onPromptly:function(){console.log("applyButton - onPromptly 被触发"),this.partJobVoClone.is_apply?t.showToast({title:"您已报名过该职位",icon:"none"}):(console.log("按钮状态详情:",{buttonStatus:this.partJobVoClone.buttonStatus,templateId:this.partJobVoClone.template.templateId,hasApply:this.partJobVoClone.hasApply,hasToken:this.hasToken}),console.log("完整的partJobVoClone:",this.partJobVoClone),this.$emit("promptly"))},payHandle:function(){this.$emit("pay")},collect:function(){console.log("收藏按钮被点击"),console.log("当前收藏状态:",this.collectImgClone),this.$emit("collect")},jumpToReport:function(){},jumpToAgreement:function(){},cancelHandle:function(){},handleShare:function(){t.navigateTo({url:"/activity/commission/poster"})}}};o.default=n}).call(this,e("df3c")["default"])},"2ceee":function(t,o,e){"use strict";var n=e("131e"),l=e.n(n);l.a},"883c":function(t,o,e){"use strict";e.r(o);var n=e("2492"),l=e.n(n);for(var a in n)["default"].indexOf(a)<0&&function(t){e.d(o,t,(function(){return n[t]}))}(a);o["default"]=l.a},9592:function(t,o,e){"use strict";e.d(o,"b",(function(){return n})),e.d(o,"c",(function(){return l})),e.d(o,"a",(function(){}));var n=function(){var t=this,o=t.$createElement,e=(t._self._c,16!==t.partJobVoClone.template.templateId&&13!==t.partJobVoClone.template.templateId&&t.partJobVoClone.hasApply&&9!==t.partJobVoClone.buttonStatus?t.t("color1"):null),n=16!==t.partJobVoClone.template.templateId&&13!==t.partJobVoClone.template.templateId&&6===t.partJobVoClone.buttonStatus?t.t("color1"):null,l=13===t.partJobVoClone.template.templateId&&t.partJobVoClone.hasApply&&9!==t.partJobVoClone.buttonStatus?t.t("color1"):null,a=13===t.partJobVoClone.template.templateId&&6===t.partJobVoClone.buttonStatus&&1===t.partJobVoClone.jobFeeVO.rushStatus?t.t("color1"):null,p=13===t.partJobVoClone.template.templateId&&6===t.partJobVoClone.buttonStatus&&1!==t.partJobVoClone.jobFeeVO.rushStatus?t.t("color1"):null,r=16===t.partJobVoClone.template.templateId&&t.partJobVoClone.hasApply&&9!==t.partJobVoClone.buttonStatus?t.t("color1"):null,u=16===t.partJobVoClone.template.templateId&&6===t.partJobVoClone.buttonStatus?t.t("color1"):null;t.$mp.data=Object.assign({},{$root:{m0:e,m1:n,m2:l,m3:a,m4:p,m5:r,m6:u}})},l=[]}}]);
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'zhaopin/components/applyButton/applyButton-create-component',
    {
        'zhaopin/components/applyButton/applyButton-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('df3c')['createComponent'](__webpack_require__("07fe"))
        })
    },
    [['zhaopin/components/applyButton/applyButton-create-component']]
]);
