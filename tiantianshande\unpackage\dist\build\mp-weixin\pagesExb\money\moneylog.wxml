<view class="container"><block wx:if="{{isload}}"><block><dd-tab vue-id="3cad70ed-1" itemdata="{{[$root.m0+'明细','充值记录','提现记录','转账记录',$root.m1+'明细','冻结明细','消费值/绿积分',$root.m2+'明细','月结明细','贡献值明细','创业值明细',$root.m3+'明细','积分明细','收益池明细']}}" itemst="{{['0','1','2','4','8','12','6','11','16','10','7','9','5','15']}}" st="{{st}}" isfixed="{{true}}" data-event-opts="{{[['^changetab',[['changetab']]]]}}" bind:changetab="__e" bind:__l="__l"></dd-tab><view class="content"><block wx:if="{{nodata}}"><view class="empty-box"></view></block><block wx:else><block><block wx:if="{{st==0}}"><block><block wx:for="{{datalist}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="item"><view class="f1"><text class="t1">{{item.remark}}</text><text class="t2">{{item.createtime}}</text><text class="t3">{{"变更后余额: "+item.after}}</text></view><view class="f2"><block wx:if="{{item.money>0}}"><text class="t1">{{"+"+item.money}}</text></block><block wx:else><text class="t2">{{item.money}}</text></block></view></view></block></block></block><block wx:if="{{st==1}}"><block><block wx:for="{{datalist}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="item"><view class="f1"><text class="t1">{{"充值金额："+item.money+"元"}}</text><text class="t2">{{item.createtime}}</text></view><view class="f3"><block wx:if="{{item.status==0}}"><text class="t1">充值失败</text></block><block wx:if="{{item.status==1}}"><text class="t2">充值成功</text></block></view></view></block></block></block><block wx:if="{{st==2}}"><block><block wx:for="{{datalist}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="item"><view class="f1"><text class="t1">{{"提现金额："+item.money+"元"}}</text><text class="t2">{{item.createtime}}</text></view><view class="f3"><block wx:if="{{item.status==0}}"><text class="t1">审核中</text></block><block wx:if="{{item.status==1}}"><text class="t1">已审核</text></block><block wx:if="{{item.status==2}}"><text class="t2">已驳回</text></block><block wx:if="{{item.status==3}}"><text class="t1">已打款</text></block></view></view></block></block></block><block wx:if="{{st==4}}"><block><block wx:for="{{datalist}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="item"><view class="f1"><text class="t1">{{item.remark}}</text><block wx:if="{{item.phone}}"><text class="t1">{{"用户："+item.phone}}</text></block><text class="t2">{{item.createtime}}</text><text class="t3">{{"变更后余额: "+item.after_money}}</text><block wx:if="{{item.charge_money}}"><text class="t3">{{"手续费: "+item.charge_money}}</text></block></view><view class="f2"><block wx:if="{{item.money>0}}"><text class="t1">{{"+"+item.money}}</text></block><block wx:else><text class="t2">{{item.money}}</text></block></view></view></block></block></block><block wx:if="{{st==8}}"><block><block wx:for="{{datalist}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="item"><view class="f1"><text class="t1">{{item.remark}}</text><text class="t2">{{item.createtime}}</text><text class="t3">{{"变更后余额: "+item.after}}</text></view><view class="f2"><block wx:if="{{item.money>0}}"><text class="t1">{{"+"+item.money}}</text></block><block wx:else><text class="t2">{{item.money}}</text></block></view></view></block></block></block><block wx:if="{{st==12}}"><block><block wx:for="{{datalist}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="item"><view class="f1"><text class="t1">{{item.remark}}</text><text class="t2">{{item.createtime}}</text><text class="t3">{{"变更后余额: "+item.after}}</text></view><view class="f2"><block wx:if="{{item.score>0}}"><text class="t1">{{"+"+item.score}}</text></block><block wx:else><text class="t2">{{item.score}}</text></block></view></view></block></block></block><block wx:if="{{st==6}}"><block><block wx:for="{{datalist}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="item"><view class="f1"><text class="t1">{{item.remark}}</text><text class="t2">{{item.createtime}}</text><text class="t3">变更后<block wx:if="{{item.money>0}}"><text>消费值</text></block><block wx:else><text>绿积分</text></block>{{": "+item.after_money+''}}</text></view><view class="f2"><block wx:if="{{item.money>0}}"><text class="t1">{{"+"+item.money}}</text></block><block wx:else><text class="t2">{{item.money}}</text></block></view></view></block></block></block><block wx:if="{{st==11}}"><block><block wx:for="{{datalist}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="item"><view class="f1"><text class="t1">{{item.remark}}</text><text class="t2">{{item.createtime}}</text><text class="t3">{{"变更后余额: "+item.after}}</text></view><view class="f2"><block wx:if="{{item.money>0}}"><text class="t1">{{"+"+item.money}}</text></block><block wx:else><text class="t2">{{item.money}}</text></block></view></view></block></block></block><block wx:if="{{st==10}}"><block><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="item"><view class="f1"><text class="t1">{{item.$orig.remark}}</text><text class="t2">{{item.$orig.createtime}}</text><text class="t3">{{"变更后"+item.m4+": "+item.$orig.after}}</text></view><view class="f2"><block wx:if="{{item.$orig.money>0}}"><text class="t1">{{"+"+item.$orig.money}}</text></block><block wx:else><text class="t2">{{item.$orig.money}}</text></block></view></view></block></block></block><block wx:if="{{st==16}}"><block><block wx:for="{{datalist}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="item"><view class="f1"><text class="t1">{{item.remark}}</text><text class="t2">{{item.createtime}}</text><text class="t3">{{"变更后月结金额: "+item.after}}</text></view><view class="f2"><block wx:if="{{item.arrears>0}}"><text class="t1">{{"+"+item.arrears}}</text></block><block wx:else><text class="t2">{{item.arrears}}</text></block></view></view></block></block></block><block wx:if="{{st==7}}"><block><block wx:for="{{datalist}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="item"><view class="f1"><text class="t1">{{item.remark}}</text><text class="t2">{{item.createtime}}</text><text class="t3">{{"变更后创业值: "+item.after}}</text></view><view class="f2"><block wx:if="{{item.busTotal>0}}"><text class="t1">{{"+"+item.busTotal}}</text></block><block wx:else><text class="t2">{{item.busTotal}}</text></block></view></view></block></block></block><block wx:if="{{st==9}}"><block><block wx:for="{{$root.l1}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="item"><view class="f1"><text class="t1">{{item.$orig.remark}}</text><text class="t2">{{item.$orig.createtime}}</text><text class="t3">{{"变更后"+item.m5+": "+item.$orig.after}}</text></view><view class="f2"><block wx:if="{{item.$orig.money>0}}"><text class="t1">{{"+"+item.$orig.money}}</text></block><block wx:else><text class="t2">{{item.$orig.money}}</text></block></view></view></block></block></block><block wx:if="{{st==5}}"><block><block wx:for="{{$root.l2}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="item"><view class="f1"><text class="t1">{{item.$orig.remark}}</text><text class="t2">{{item.$orig.createtime}}</text><text class="t3">{{"变更后"+item.m6+": "+item.$orig.after}}</text></view><view class="f2"><block wx:if="{{item.$orig.money>0}}"><text class="t1">{{"+"+item.$orig.money}}</text></block><block wx:else><text class="t2">{{item.$orig.money}}</text></block></view></view></block></block></block><block wx:if="{{st==15}}"><block><block wx:for="{{datalist}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="item"><view class="f1"><text class="t1">{{item.remark}}</text><text class="t2">{{item.createtime}}</text><text class="t3">{{"变更后收益池: "+item.after}}</text></view><view class="f2"><block wx:if="{{item.money>0}}"><text class="t1">{{"+"+item.money}}</text></block><block wx:else><text class="t2">{{item.money}}</text></block></view></view></block></block></block></block></block></view><block wx:if="{{nomore}}"><nomore vue-id="3cad70ed-2" bind:__l="__l"></nomore></block><block wx:if="{{nodata}}"><nodata vue-id="3cad70ed-3" bind:__l="__l"></nodata></block></block></block><block wx:if="{{loading}}"><loading vue-id="3cad70ed-4" bind:__l="__l"></loading></block><dp-tabbar vue-id="3cad70ed-5" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="3cad70ed-6" data-ref="popmsg" bind:__l="__l"></popmsg></view>