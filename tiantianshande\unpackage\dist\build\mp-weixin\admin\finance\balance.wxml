<view class="container"><view class="summary-card"><view class="summary-title">进货款信息</view><view class="summary-content"><view class="summary-item"><text class="summary-label">当前余额</text><text class="summary-value">{{"￥"+(summary.balance||'0.00')}}</text></view><view class="summary-item"><text class="summary-label">本月充值</text><text class="summary-value income">{{"￥"+(summary.month_recharge||'0.00')}}</text></view><view class="summary-item"><text class="summary-label">本月消费</text><text class="summary-value expense">{{"￥"+(summary.month_consume||'0.00')}}</text></view></view></view><view class="filter-section"><view class="date-range"><view data-event-opts="{{[['tap',[['showDatePicker',['start']]]]]}}" class="date-picker" bindtap="__e"><text>{{"开始日期："+(filter.start_time||'请选择')}}</text></view><text class="date-separator">至</text><view data-event-opts="{{[['tap',[['showDatePicker',['end']]]]]}}" class="date-picker" bindtap="__e"><text>{{"结束日期："+(filter.end_time||'请选择')}}</text></view></view><view class="type-filter"><view data-event-opts="{{[['tap',[['changeType',['']]]]]}}" class="{{['type-option',(filter.type==='')?'active':'']}}" bindtap="__e">全部</view><view data-event-opts="{{[['tap',[['changeType',['income']]]]]}}" class="{{['type-option',(filter.type==='income')?'active':'']}}" bindtap="__e">收入</view><view data-event-opts="{{[['tap',[['changeType',['expense']]]]]}}" class="{{['type-option',(filter.type==='expense')?'active':'']}}" bindtap="__e">支出</view></view><view class="filter-actions"><view data-event-opts="{{[['tap',[['resetFilter',['$event']]]]]}}" class="filter-btn reset" bindtap="__e">重置</view><view data-event-opts="{{[['tap',[['applyFilter',['$event']]]]]}}" class="filter-btn search" bindtap="__e">查询</view></view></view><block wx:if="{{statistics.total_income||statistics.total_expense}}"><view class="statistics"><view class="statistics-title">查询结果统计</view><view class="statistics-content"><view class="stat-item"><text class="stat-label">收入总额</text><text class="stat-value income">{{"￥"+(statistics.total_income||'0.00')}}</text></view><view class="stat-item"><text class="stat-label">支出总额</text><text class="stat-value expense">{{"￥"+(statistics.total_expense||'0.00')}}</text></view></view></view></block><block wx:if="{{$root.g0>0}}"><view class="transaction-list"><block wx:for="{{transactionList}}" wx:for-item="item" wx:for-index="index" wx:key="id"><view class="transaction-item"><view class="transaction-header"><text class="transaction-time">{{item.createtime_text}}</text><text class="{{['transaction-type',item.type]}}">{{item.type==='income'?'收入':'支出'}}</text></view><view class="transaction-body"><view class="{{['transaction-amount',item.type]}}">{{''+(item.type==='income'?'+':'-')+"￥"+item.money_abs+''}}</view><view class="transaction-after"><text class="after-label">余额：</text><text class="after-value">{{"￥"+item.after}}</text></view></view><view class="transaction-remark"><text class="remark-label">备注：</text><text class="remark-content">{{item.remark||'无'}}</text></view></view></block></view></block><block wx:if="{{nodata}}"><nodata vue-id="82420946-1" bind:__l="__l"></nodata></block><block wx:if="{{nomore}}"><nomore vue-id="82420946-2" bind:__l="__l"></nomore></block><block wx:if="{{loading}}"><loading vue-id="82420946-3" bind:__l="__l"></loading></block><popmsg class="vue-ref" vue-id="82420946-4" data-ref="popmsg" bind:__l="__l"></popmsg></view>