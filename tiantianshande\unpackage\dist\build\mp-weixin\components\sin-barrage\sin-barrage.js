(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["components/sin-barrage/sin-barrage"],{"2e1a":function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n={name:"sinBarrage",props:{list:{type:Array||Object,default:function(){return{}}},rows:{type:Number,default:3},color:{type:String,default:"#FFFFFF"},background:{type:String,default:"#000000"},opacity:{type:Number,default:.7},left:{type:Number,default:35},bottom:{type:Number,default:120},msec:{type:Number,default:2e3}},data:function(){return{barrageList:[],timer:null,show:!1}},created:function(){var t=this;if(0!=this.list.length){if(this.timer&&clearInterval(this.timer),1==this.list.length)return this.barrageList.push(this.list[0]),void setTimeout((function(){return t.barrageList=[]}),this.msec);this.timer=setInterval((function(){if(t.barrageList.length<t.rows)t.barrageList.push(t.list[0]),t.list.splice(0,1);else{var e=t.barrageList[0];t.barrageList.splice(e,1),t.list.push(e);var r=t.list[0];t.list.splice(r,1),t.barrageList.push(r)}}),this.msec)}}};e.default=n},"5cc8":function(t,e,r){"use strict";var n=r("d0f1"),i=r.n(n);i.a},"71b3":function(t,e,r){"use strict";r.r(e);var n=r("2e1a"),i=r.n(n);for(var a in n)["default"].indexOf(a)<0&&function(t){r.d(e,t,(function(){return n[t]}))}(a);e["default"]=i.a},d0f1:function(t,e,r){},f893:function(t,e,r){"use strict";r.r(e);var n=r("fbd9"),i=r("71b3");for(var a in i)["default"].indexOf(a)<0&&function(t){r.d(e,t,(function(){return i[t]}))}(a);r("5cc8");var u=r("828b"),s=Object(u["a"])(i["default"],n["b"],n["c"],!1,null,"2ad16050",null,!1,n["a"],void 0);e["default"]=s.exports},fbd9:function(t,e,r){"use strict";r.d(e,"b",(function(){return i})),r.d(e,"c",(function(){return a})),r.d(e,"a",(function(){return n}));var n={uniTransition:function(){return r.e("components/uni-transition/uni-transition").then(r.bind(null,"2251"))}},i=function(){var t=this.$createElement;this._self._c},a=[]}}]);
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'components/sin-barrage/sin-barrage-create-component',
    {
        'components/sin-barrage/sin-barrage-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('df3c')['createComponent'](__webpack_require__("f893"))
        })
    },
    [['components/sin-barrage/sin-barrage-create-component']]
]);
