<view class="container"><block wx:if="{{isload}}"><block><view class="stats-section"><view class="stats-item"><text class="stats-number" style="color:#FFD700;">{{statistics.total_count||0}}</text><text class="stats-label">总次数</text></view><view class="stats-item"><text class="stats-number" style="color:#FFD700;">{{statistics.total_amount||0}}</text><text class="stats-label">总金额</text></view><view class="stats-item"><text class="stats-number" style="color:#FFD700;">{{statistics.wish_count||0}}</text><text class="stats-label">许愿次数</text></view></view><view class="filter-tabs"><block wx:for="{{filterTabs}}" wx:for-item="tab" wx:for-index="index" wx:key="index"><view class="{{['filter-tab',(currentTab===index)?'active':'']}}" data-index="{{index}}" data-event-opts="{{[['tap',[['switchTab',['$event']]]]]}}" bindtap="__e"><text>{{tab.name}}</text></view></block></view><view class="my-wishes-list"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="wish-record"><view class="record-header"><text class="type-badge" style="{{'background-color:'+(item.m0)+';'}}">{{item.m1}}</text><text class="amount" style="color:#FFD700;">{{"￥"+item.$orig.amount}}</text><text class="time">{{item.m2}}</text></view><block wx:if="{{item.$orig.wish_content}}"><view class="record-content"><text>{{item.$orig.wish_content}}</text></view></block></view></block></view><block wx:if="{{$root.g0}}"><view class="empty-state"><image class="empty-icon" src="{{pre_url+'/static/img/empty-my-wishes.png'}}"></image><text class="empty-text">暂无记录</text></view></block><block wx:if="{{showEditModal}}"><view data-event-opts="{{[['tap',[['closeEditModal',['$event']]]]]}}" class="modal-overlay" bindtap="__e"><view data-event-opts="{{[['tap',[['',['$event']]]]]}}" class="modal-content" catchtap="__e"><view class="modal-header"><text class="modal-title">编辑许愿内容</text><text data-event-opts="{{[['tap',[['closeEditModal',['$event']]]]]}}" class="close-btn" bindtap="__e">×</text></view><view class="modal-body"><textarea class="edit-textarea" placeholder="请输入许愿内容" maxlength="200" data-event-opts="{{[['input',[['__set_model',['','editContent','$event',[]]]]]]}}" value="{{editContent}}" bindinput="__e"></textarea><text class="char-count">{{$root.g1+"/200"}}</text></view><view class="modal-footer"><button data-event-opts="{{[['tap',[['closeEditModal',['$event']]]]]}}" class="cancel-btn" bindtap="__e">取消</button><button data-event-opts="{{[['tap',[['saveEdit',['$event']]]]]}}" class="confirm-btn" style="background:linear-gradient(90deg, #FFD700 0%, rgba(255, 215, 0, 0.8) 100%);" bindtap="__e">保存</button></view></view></view></block></block></block><block wx:if="{{nodata}}"><nodata vue-id="6994b51d-1" bind:__l="__l"></nodata></block><block wx:if="{{loading}}"><loading vue-id="6994b51d-2" bind:__l="__l"></loading></block><popmsg class="vue-ref" vue-id="6994b51d-3" data-ref="popmsg" bind:__l="__l"></popmsg></view>