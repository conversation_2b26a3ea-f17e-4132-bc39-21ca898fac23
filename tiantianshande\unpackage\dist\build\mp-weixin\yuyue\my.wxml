<block wx:if="{{isload}}"><view><view class="banner" style="{{'background:'+('linear-gradient(135deg,'+$root.m0+' 0%,rgba('+$root.m1+',0.8) 100%)')+';'}}"><image src="{{worker.headimg}}" background-size="cover"></image><view class="info"><text class="nickname">{{worker.realname}}</text><text>{{worker.tel}}</text></view><block wx:if="{{sets}}"><view class="sets" data-url="sets" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image src="/static/img/set.png"></image></view></block></view><view class="contentdata"><view class="custom_field"><view class="item" data-url="jdorderlist?st=3" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><text class="t1">累计服务</text><text class="t2" style="{{'color:'+($root.m2)+';'}}">{{worker.totalnum+"次"}}</text></view><view class="item"><text class="t1">总收入</text><text class="t2" style="{{'color:'+($root.m3)+';'}}">{{worker.totalmoney+"元"}}</text></view><view class="item"><text class="t1">好评率</text><text class="t2" style="{{'color:'+($root.m4)+';'}}">{{worker.comment_haopercent+"%"}}</text></view></view><view class="listcontent"><block wx:if="{{enable_calendar}}"><view class="list"><view class="item" data-url="calendar" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="f1"><image src="{{pre_url+'/static/img/peisong/ico-calendar.png'}}"></image></view><view class="f2">工作日历</view><text class="f3"></text><image class="f4" src="/static/img/arrowright.png"></image></view></view></block><block wx:if="{{enable_promote}}"><view class="list"><view class="item" data-url="worker/promote" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="f1"><image src="{{pre_url+'/static/img/peisong/ico-tuiguang.png'}}"></image></view><view class="f2">推广产品</view><text class="f3"></text><image class="f4" src="/static/img/arrowright.png"></image></view></view></block><view class="list"><view class="item" data-url="withdraw" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="f1"><image src="{{pre_url+'/static/img/peisong/ico-qianbao.png'}}"></image></view><view class="f2">我的钱包</view><text class="f3">{{"余额："+worker.money}}</text><image class="f4" src="/static/img/arrowright.png"></image></view></view><view class="list"><view class="item" data-url="moneylog" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="f1"><image src="{{pre_url+'/static/img/peisong/ico-zhangdan.png'}}"></image></view><view class="f2">账单明细</view><text class="f3"></text><image class="f4" src="/static/img/arrowright.png"></image></view></view><view class="list"><view class="item" data-url="comments" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="f1"><image src="{{pre_url+'/static/img/peisong/ico-comment.png'}}"></image></view><view class="f2">客户评价</view><text class="f3"></text><image class="f4" src="/static/img/arrowright.png"></image></view></view><view class="list"><view class="item"><view class="f1"><image src="{{pre_url+'/static/img/peisong/ico-jiedan.png'}}"></image></view><view class="f2">接单状态</view><view class="f3"><switch value="1" checked="{{worker.status==1?true:false}}" data-event-opts="{{[['change',[['switchchange',['$event']]]]]}}" bindchange="__e"></switch></view></view></view><view class="list"><view class="item" data-url="setinfo" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="f1"><image src="{{pre_url+'/static/img/peisong/ico-shenfen.png'}}"></image></view><view class="f2">提现设置</view><text class="f3"></text><image class="f4" src="/static/img/arrowright.png"></image></view></view><block wx:if="{{searchmember}}"><view class="list"><view class="item" data-url="/activity/searchmember/searchmember" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="f1"><image src="{{pre_url+'/static/img/peisong/ico-shenfen.png'}}"></image></view><view class="f2">一键查看</view><text class="f3"></text><image class="f4" src="/static/img/arrowright.png"></image></view></view></block><view class="list"><view class="item" data-url="setpwd" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="f1"><image src="{{pre_url+'/static/img/peisong/ico-shenfen.png'}}"></image></view><view class="f2">修改密码</view><text class="f3"></text><image class="f4" src="/static/img/arrowright.png"></image></view></view><view class="list"><view class="item" data-url="login" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="f1"><image src="{{pre_url+'/static/img/peisong/ico-logout.png'}}"></image></view><view class="f2">退出登录</view><text class="f3"></text><image class="f4" src="/static/img/arrowright.png"></image></view></view></view></view><view class="tabbar"><view class="tabbar-bot"></view><view class="tabbar-bar" style="background-color:#ffffff;"><view class="tabbar-item" data-url="dating" data-opentype="reLaunch" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="tabbar-image-box"><image class="tabbar-icon" src="{{pre_url+'/static/img/peisong/home.png'}}"></image></view><view class="tabbar-text">大厅</view></view><view class="tabbar-item" data-url="jdorderlist" data-opentype="reLaunch" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="tabbar-image-box"><image class="tabbar-icon" src="{{pre_url+'/static/img/peisong/order.png'}}"></image></view><view class="tabbar-text">订单</view></view><view class="tabbar-item" data-url="jdorderlist?st=3" data-opentype="reLaunch" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="tabbar-image-box"><image class="tabbar-icon" src="{{pre_url+'/static/img/peisong/orderwc.png'}}"></image></view><view class="tabbar-text">已完成</view></view><block wx:if="{{showform}}"><view class="tabbar-item" data-url="formlog" data-opentype="reLaunch" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="tabbar-image-box"><image class="tabbar-icon" src="{{pre_url+'/static/img/peisong/dangan.png'}}"></image></view><view class="tabbar-text">档案</view></view></block><view class="tabbar-item" data-url="my" data-opentype="reLaunch" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="tabbar-image-box"><image class="tabbar-icon" src="{{pre_url+'/static/img/peisong/my2.png'}}"></image></view><view class="tabbar-text" style="{{'color:'+($root.m5)+';'}}">我的</view></view></view></view><popmsg class="vue-ref" vue-id="efd899ac-1" data-ref="popmsg" bind:__l="__l"></popmsg></view></block><block wx:else><loading vue-id="efd899ac-2" bind:__l="__l"></loading></block>