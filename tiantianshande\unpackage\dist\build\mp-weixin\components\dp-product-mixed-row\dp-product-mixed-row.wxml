<view class="dp-product-mixed-row"><block wx:if="{{params&&(params.main_title||params.sub_title)}}"><view class="mixed-header"><block wx:if="{{params.bgimg}}"><view class="bgstyle"><image class="bgimg" src="{{params.bgimg}}" mode="aspectFill"></image></view></block><view class="mixed-header-content"><view class="left"><block wx:if="{{params.main_title}}"><text class="main-title" style="{{'color:'+(params.main_title_color||'#333')+';'}}">{{params.main_title}}</text></block><block wx:if="{{params.sub_title}}"><text class="sub-title" style="{{'color:'+(params.sub_title_color||'#999')+';'}}">{{params.sub_title}}</text></block></view><block wx:if="{{params.more_text}}"><view class="right" data-url="{{params.hrefurl||''}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><text class="more-text" style="{{'color:'+(params.more_text_color||'#999')+';'}}">{{params.more_text}}</text><text class="iconfont icon_arrowright_fill" style="{{'color:'+(params.more_text_color||'#999')+';'}}"></text></view></block></view></view></block><view class="mixed-content"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="m0"><view class="{{['mixed-item',(item.$orig.content_type==='article')?'article-container':'',(item.$orig.content_type==='video')?'video-container':'',(!item.$orig.content_type)?'product-container':'']}}"><block wx:if="{{!item.$orig.content_type}}"><view class="product-item" data-url="{{'/shopPackage/shop/product?id='+item.$orig[idfield]}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image class="product-image" src="{{item.$orig.pic}}" mode="aspectFill"></image><block wx:if="{{saleimg!==''}}"><image class="saleimg" src="{{saleimg}}"></image></block><view class="product-info"><block wx:if="{{showname==1}}"><text class="product-name">{{item.$orig.name}}</text></block><block wx:if="{{showprice!='0'&&(item.$orig.price_type!=1||item.$orig.sell_price>0)}}"><view class="price-box"><text class="sell-price">{{"¥"+item.$orig.sell_price}}</text><block wx:if="{{showprice=='1'&&item.$orig.market_price*1>item.$orig.sell_price*1}}"><text class="market-price">{{"¥"+item.$orig.market_price}}</text></block></view></block><block wx:if="{{item.$orig.xunjia_text&&item.$orig.price_type==1&&item.$orig.sell_price<=0}}"><view class="price-box"><text class="inquiry-price">询价</text><view class="contact-btn" data-lx_name="{{item.$orig.lx_name}}" data-lx_bid="{{item.$orig.lx_bid}}" data-lx_bname="{{item.$orig.lx_bname}}" data-lx_tel="{{item.$orig.lx_tel}}" data-btntype="2" data-event-opts="{{[['tap',[['showLinkChange',['$event']]]]]}}" catchtap="__e">{{''+(item.$orig.xunjia_text||'联系TA')+''}}</view></view></block><block wx:if="{{showsales=='1'&&item.$orig.sales>0||showstock=='1'}}"><view class="sales-info"><block wx:if="{{showsales=='1'&&item.$orig.sales>0}}"><text>{{"已售"+item.$orig.sales}}</text></block><block wx:if="{{showsales=='1'&&item.$orig.sales>0&&showstock=='1'}}"><text class="separator">|</text></block><block wx:if="{{showstock=='1'}}"><text>{{"仅剩"+item.$orig.stock}}</text></block></view></block><block wx:if="{{showcart=='1'&&!item.$orig.price_type}}"><view class="cart-btn" data-proid="{{item.$orig[idfield]}}" data-event-opts="{{[['tap',[['buydialogChange',['$event']]]]]}}" catchtap="__e"><text class="iconfont icon_gouwuche"></text></view></block><block wx:if="{{showcart=='2'&&!item.$orig.price_type}}"><view class="cart-btn" data-proid="{{item.$orig[idfield]}}" data-event-opts="{{[['tap',[['buydialogChange',['$event']]]]]}}" catchtap="__e"><image class="cart-icon" src="{{cartimg}}"></image></view></block></view></view></block><block wx:if="{{item.$orig.content_type==='article'}}"><view class="article-item" data-url="{{'/pages/article/detail?id='+item.$orig.article_id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="article-image-container"><image class="article-image" src="{{item.$orig.cover_img||item.$orig.coverimg}}" mode="aspectFill"></image><view class="article-tag">文章</view></view><view class="article-info"><text class="article-title">{{item.$orig.title}}</text><view class="article-meta"><text class="article-date">{{item.m1}}</text><text class="article-views">{{(item.$orig.view_num||0)+"阅读"}}</text></view></view></view></block><block wx:if="{{item.$orig.content_type==='video'}}"><view class="video-item" data-url="{{'/activity/shortvideo/detail?id='+item.$orig.videoId}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="video-image-container"><image class="video-image" src="{{item.$orig.coverimg}}" mode="aspectFill"></image><view class="video-play-icon"><text class="iconfont icon_bofang"></text></view><view class="video-tag">视频</view></view><view class="video-info"><text class="video-title">{{item.$orig.title||'精彩短视频'}}</text><view class="video-meta"><text class="video-view-count"><text class="iconfont icon_bofang1"></text>{{''+(item.$orig.view_num||0)+''}}</text><text class="video-like-count"><text class="iconfont icon_dianzan"></text>{{''+(item.$orig.zan_num||0)+''}}</text></view></view></view></block></view></block></view><block wx:if="{{buydialogShow}}"><buydialog vue-id="1ccfcd62-1" proid="{{proid}}" menuindex="{{menuindex}}" data-event-opts="{{[['^addcart',[['addcart']]],['^buydialogChange',[['buydialogChange']]]]}}" bind:addcart="__e" bind:buydialogChange="__e" bind:__l="__l"></buydialog></block><block wx:if="{{showLinkStatus}}"><view class="link-dialog"><view class="link-dialog-main"><view data-event-opts="{{[['tap',[['showLinkChange',['$event']]]]]}}" class="link-dialog-close" bindtap="__e"><image class="link-dialog-close-icon" src="/static/img/close.png"></image></view><view class="link-dialog-content"><view class="link-dialog-title">{{lx_name}}</view><block wx:if="{{lx_bid>0}}"><view class="link-dialog-row"><view class="link-dialog-label">店铺名称</view><view class="link-dialog-value" data-url="{{'/pagesExt/business/index?id='+lx_bid}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">{{''+lx_bname+''}}<image class="link-dialog-arrow" src="/static/img/arrowright.png"></image></view></view></block><block wx:if="{{lx_tel}}"><view class="link-dialog-row"><view class="link-dialog-label">联系电话</view><view class="link-dialog-value" data-url="{{'tel::'+lx_tel}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">{{''+lx_tel+''}}<image class="link-dialog-copy" src="/static/img/copy.png" data-text="{{lx_tel}}" data-event-opts="{{[['tap',[['copy',['$event']]]]]}}" catchtap="__e"></image></view></view></block></view></view></view></block></view>