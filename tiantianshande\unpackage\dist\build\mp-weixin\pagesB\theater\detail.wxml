<view class="container"><block wx:if="{{isload}}"><block><scroll-view style="height:100%;overflow:scroll;" scrollIntoView="{{scrollToViewId}}" scrollTop="{{scrollTop}}" scroll-y="{{true}}" data-event-opts="{{[['scroll',[['scroll',['$event']]]]]}}" bindscroll="__e"><view id="scroll_view_tab0"><view class="swiper-container"><view class="swiper-item-view"><image class="img" src="{{data.logo}}" mode="scaleToFill"></image></view></view><view class="header"><block wx:if="{{data.min_price>0}}"><block><view class="price_share"><view class="price"><view class="f1" style="{{'color:'+($root.m0)+';'}}"><text style="font-size:36rpx;">￥</text>{{data.min_price}}<block wx:if="{{data.max_price!=data.min_price}}"><text>{{"-"+data.max_price}}</text></block></view></view><view data-event-opts="{{[['tap',[['shareClick',['$event']]]]]}}" class="share" bindtap="__e"><image class="img" src="{{pre_url+'/static/img/share.png'}}"></image><text class="txt">分享</text></view></view><view class="title">{{data.title}}</view></block></block></view><view data-event-opts="{{[['tap',[['openMap',['$event']]]]]}}" class="choose" bindtap="__e"><view class="f1 flex1"><view>{{''+data.venues_info.title+''}}</view><view style="color:#aaa;font-size:22rpx;">{{''+data.venues_info.city+data.venues_info.district}}</view></view><image class="f2" src="{{pre_url+'/static/img/map.png'}}"></image></view><block wx:if="{{nearestShow}}"><view class="recommend-show"><view class="recommend-title"><view class="icon"></view><view class="text">推荐场次</view></view><view class="recommend-content"><view class="show-time">{{nearestShow.title}}</view><view class="show-desc">最近可预订场次</view></view></view></block></view><view id="scroll_view_tab2"><view class="detail_title"><view class="t1"></view><view class="t2"></view><view class="t0">详情</view><view class="t2"></view><view class="t1"></view></view><view class="detail"><dp vue-id="02b334a4-1" pagecontent="{{pagecontent}}" bind:__l="__l"></dp></view></view><view style="width:100%;height:140rpx;"></view></scroll-view><view class="{{['bottombar','flex-row',menuindex>-1?'tabbarbot':'notabbarbot']}}"><view class="f1"><view class="item" data-url="/pagesB/theater/orderlist" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image class="img" src="{{pre_url+'/static/img/shou.png'}}"></image><view class="t1">首页</view></view><button class="item" open-type="contact"><image class="img" src="{{pre_url+'/static/img/kefu.png'}}"></image><view class="t1">客服</view></button><view data-event-opts="{{[['tap',[['addfavorite',['$event']]]]]}}" class="item" bindtap="__e"><image class="img" src="{{pre_url+'/static/img/shoucang.png'}}"></image><view class="t1">{{isfavorite?'已收藏':'收藏'}}</view></view><view data-event-opts="{{[['tap',[['manualDateClick',['$event']]]]]}}" class="item" bindtap="__e"><image class="img" src="{{pre_url+'/static/img/date.png'}}"></image><view class="t1">选择场次</view></view></view><view class="op"><view style="flex:1;"><view data-event-opts="{{[['tap',[['dateClick',['$event']]]]]}}" class="tocart flex-x-center flex-y-center" style="{{'background:'+($root.m1)+';'}}" bindtap="__e">选座购买</view></view></view></view><block wx:if="{{sharetypevisible}}"><view class="popup__container"><view data-event-opts="{{[['tap',[['handleClickMask',['$event']]]]]}}" class="popup__overlay" catchtap="__e"></view><view class="popup__modal" style="height:320rpx;min-height:320rpx;"><view class="popup__content"><view class="sharetypecontent"><block wx:if="{{$root.m2=='app'}}"><view data-event-opts="{{[['tap',[['shareapp',['$event']]]]]}}" class="f1" bindtap="__e"><image class="img" src="{{pre_url+'/static/img/weixin.png'}}"></image><text class="t1">分享给好友</text></view></block><block wx:else><block wx:if="{{$root.m3=='mp'}}"><view data-event-opts="{{[['tap',[['sharemp',['$event']]]]]}}" class="f1" bindtap="__e"><image class="img" src="{{pre_url+'/static/img/weixin.png'}}"></image><text class="t1">分享给好友</text></view></block><block wx:else><block wx:if="{{$root.m4=='h5'}}"><view data-event-opts="{{[['tap',[['sharemp',['$event']]]]]}}" class="f1" bindtap="__e"><image class="img" src="{{pre_url+'/static/img/weixin.png'}}"></image><text class="t1">分享给好友</text></view></block><block wx:else><button class="f1" open-type="share"><image class="img" src="{{pre_url+'/static/img/weixin.png'}}"></image><text class="t1">分享给好友</text></button></block></block></block><view data-event-opts="{{[['tap',[['showPoster',['$event']]]]]}}" class="f2" bindtap="__e"><image class="img" src="{{pre_url+'/static/img/sharepic.png'}}"></image><text class="t1">生成分享图片</text></view><block wx:if="{{$root.m5}}"><view data-event-opts="{{[['tap',[['shareScheme',['$event']]]]]}}" class="f1" bindtap="__e"><image class="img" src="{{pre_url+'/static/img/weixin.png'}}"></image><text class="t1">小程序链接</text></view></block></view></view></view></view></block></block></block><block wx:if="{{loading}}"><loading vue-id="02b334a4-2" bind:__l="__l"></loading></block><uni-calendar class="vue-ref" vue-id="02b334a4-3" insert="{{false}}" selected="{{selected}}" data-ref="calendar" data-event-opts="{{[['^confirm',[['confirm']]]]}}" bind:confirm="__e" bind:__l="__l"></uni-calendar><uni-popup class="vue-ref" vue-id="02b334a4-4" id="dialogShowCategory" type="bottom" mask-click="{{true}}" data-ref="dialogShowCategory" bind:__l="__l" vue-slots="{{['default']}}"><view class="view-main"><view class="view-title">选择场次<image class="icon" src="{{pre_url+'/static/img/close.png'}}" data-event-opts="{{[['tap',[['hideDialog']]]]}}" bindtap="__e"></image></view><view class="tit-t">选择日期</view><view class="view-title"><view class="tit-date" style="{{'background:'+($root.m6)+';'}}">{{date}}</view><view><image class="icon2" src="{{pre_url+'/static/img/date.png'}}" data-event-opts="{{[['tap',[['dateClick']]]]}}" bindtap="__e"></image></view></view><view class="tit-t">场次时间</view><scroll-view style="height:600rpx;" scroll-y="{{true}}"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['changeCC',[index]]]]]}}" bindtap="__e"><block wx:if="{{cci==index}}"><view class="tit-time" style="{{('color:'+item.m7+';background:'+item.m8+'33')}}">{{item.$orig}}</view></block><block wx:else><view class="tit-time">{{item.$orig}}</view></block></view></block></scroll-view><view data-event-opts="{{[['tap',[['goSeat']]]]}}" class="view-btn" style="{{'background:'+($root.m9)+';'}}" bindtap="__e">去选座</view></view></uni-popup></view>