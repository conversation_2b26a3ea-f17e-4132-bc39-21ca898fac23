<view class="container"><dd-tab vue-id="131ffa44-1" itemdata="{{['全部('+(countall||0)+')','已启用('+(count1||0)+')','已停用('+(count0||0)+')']}}" itemst="{{['all','1','0']}}" st="{{st}}" isfixed="{{true}}" data-event-opts="{{[['^changetab',[['changetab']]]]}}" bind:changetab="__e" bind:__l="__l"></dd-tab><view style="width:100%;height:100rpx;"></view><view class="search-header"><view class="topsearch flex-y-center"><view class="f1 flex-y-center"><image class="img" src="/static/img/search_ico.png"></image><input placeholder="输入模板名称搜索" placeholder-style="font-size:24rpx;color:#C2C2C2" data-event-opts="{{[['confirm',[['searchConfirm',['$event']]]]]}}" value="{{keyword}}" bindconfirm="__e"/></view></view><view class="add-btn" style="{{'background:'+($root.m0)+';'}}" data-url="edit" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><text class="icon">+</text><text>新增模板</text></view></view><view class="freight-content"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><view class="freight-box"><view class="header"><view class="name">{{item.$orig.name}}</view><view class="{{['tag',item.$orig.status?'active':'']}}">{{''+(item.$orig.status?'已启用':'已停用')+''}}</view></view><view class="content"><view class="info-row"><text class="label">计费方式：</text><text class="value">{{item.$orig.type===1?'按重量':'按件数'}}</text></view><view class="info-row"><text class="label">配送区域：</text><text class="value">{{item.$orig.areas||'全国'}}</text></view><block wx:if="{{item.$orig.type===1}}"><view class="info-row"><text class="label">首重：</text><text class="value">{{item.m1+"kg，¥"+item.m2}}</text></view></block><block wx:if="{{item.$orig.type===1}}"><view class="info-row"><text class="label">续重：</text><text class="value">{{item.m3+"kg，¥"+item.m4}}</text></view></block><block wx:if="{{item.$orig.type===2}}"><view class="info-row"><text class="label">首件：</text><text class="value">{{item.m5+"件，¥"+item.m6}}</text></view></block><block wx:if="{{item.$orig.type===2}}"><view class="info-row"><text class="label">续件：</text><text class="value">{{item.m7+"件，¥"+item.m8}}</text></view></block></view><view class="op"><block wx:if="{{!item.$orig.status}}"><text class="flex1" style="color:red;">已停用</text></block><block wx:if="{{item.$orig.status}}"><text class="flex1" style="color:green;">已启用</text></block><block wx:if="{{!item.$orig.status}}"><view class="btn1" style="{{'background:'+(item.m9)+';'}}" data-status="1" data-id="{{item.$orig.id}}" data-event-opts="{{[['tap',[['setStatus',['$event']]]]]}}" bindtap="__e">启用</view></block><block wx:else><view class="btn1" style="{{'background:'+(item.m10)+';'}}" data-status="0" data-id="{{item.$orig.id}}" data-event-opts="{{[['tap',[['setStatus',['$event']]]]]}}" bindtap="__e">停用</view></block><view class="btn2" data-url="{{'edit?id='+item.$orig.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">编辑</view><view class="btn2" data-id="{{item.$orig.id}}" data-event-opts="{{[['tap',[['deleteTemplate',['$event']]]]]}}" bindtap="__e">删除</view></view></view></block></block></view><block wx:if="{{loading}}"><loading vue-id="131ffa44-2" bind:__l="__l"></loading></block><block wx:if="{{nomore}}"><nomore vue-id="131ffa44-3" bind:__l="__l"></nomore></block><block wx:if="{{nodata}}"><nodata vue-id="131ffa44-4" bind:__l="__l"></nodata></block><popmsg class="vue-ref" vue-id="131ffa44-5" data-ref="popmsg" bind:__l="__l"></popmsg></view>