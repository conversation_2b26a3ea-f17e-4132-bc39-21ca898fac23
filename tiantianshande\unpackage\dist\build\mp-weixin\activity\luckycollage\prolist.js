(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["activity/luckycollage/prolist"],{"18aa":function(t,n,a){"use strict";(function(t,n){var e=a("47a9");a("06e9");e(a("3240"));var o=e(a("4641"));t.__webpack_require_UNI_MP_PLUGIN__=a,n(o.default)}).call(this,a("3223")["default"],a("df3c")["createPage"])},4641:function(t,n,a){"use strict";a.r(n);var e=a("fdf91"),o=a("ac98");for(var i in o)["default"].indexOf(i)<0&&function(t){a.d(n,t,(function(){return o[t]}))}(i);a("9b21");var u=a("828b"),c=Object(u["a"])(o["default"],e["b"],e["c"],!1,null,null,null,!1,e["a"],void 0);n["default"]=c.exports},8513:function(t,n,a){"use strict";(function(t){Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;var a=getApp(),e={data:function(){return{opt:{},loading:!1,isload:!1,menuindex:-1,keyword:"",bid:"",pics:[],pagenum:1,st:"",datalist:[],nomore:!1,nodata:!1,linktype:"product"}},onLoad:function(t){this.opt=a.getopts(t),this.bid=this.opt.bid||"",this.cid=this.opt.cid||"",this.getdata()},onPullDownRefresh:function(){this.getdata()},onReachBottom:function(){this.nodata||this.nomore||(this.pagenum=this.pagenum+1,this.getdata(!0))},methods:{getdata:function(t){var n=this;a.get("ApiIndex/getCustom",{},(function(t){t.data.includes("plug_luckycollage")&&(n.linktype="product2")})),t||(this.pagenum=1,this.datalist=[]);var e=n.pagenum,o=n.st,i=n.keyword;n.loading=!0,n.nodata=!1,n.nomore=!1,a.post("ApiLuckyCollage/prolist",{cid:n.cid,bid:n.bid,keyword:i,st:o,pagenum:e},(function(t){n.loading=!1;var a=t.datalist;if(1==e)n.pics=t.pics,n.clist=t.clist,n.datalist=a,0==a.length&&(n.nodata=!0);else if(0==a.length)n.nomore=!0;else{var o=n.datalist,i=o.concat(a);n.datalist=i}n.loaded()}))},changetab:function(n){var a=n.currentTarget.dataset.st;this.pagenum=1,this.st=a,this.datalist=[],t.pageScrollTo({scrollTop:0,duration:0}),this.getdata()},searchChange:function(t){this.keyword=t.detail.value},searchConfirm:function(t){var n=t.detail.value;this.keyword=n,this.getdata()}}};n.default=e}).call(this,a("df3c")["default"])},"9b21":function(t,n,a){"use strict";var e=a("fa4c"),o=a.n(e);o.a},ac98:function(t,n,a){"use strict";a.r(n);var e=a("8513"),o=a.n(e);for(var i in e)["default"].indexOf(i)<0&&function(t){a.d(n,t,(function(){return e[t]}))}(i);n["default"]=o.a},fa4c:function(t,n,a){},fdf91:function(t,n,a){"use strict";a.d(n,"b",(function(){return o})),a.d(n,"c",(function(){return i})),a.d(n,"a",(function(){return e}));var e={nomore:function(){return a.e("components/nomore/nomore").then(a.bind(null,"3892"))},nodata:function(){return a.e("components/nodata/nodata").then(a.bind(null,"101c"))},loading:function(){return a.e("components/loading/loading").then(a.bind(null,"ceaa"))},dpTabbar:function(){return a.e("components/dp-tabbar/dp-tabbar").then(a.bind(null,"b875"))},popmsg:function(){return a.e("components/popmsg/popmsg").then(a.bind(null,"2bf2"))}},o=function(){var t=this,n=t.$createElement,a=(t._self._c,t.isload?t.__map(t.datalist,(function(n,a){var e=t.__get_orig(n),o=t.t("color1");return{$orig:e,m0:o}})):null);t.$mp.data=Object.assign({},{$root:{l0:a}})},i=[]}},[["18aa","common/runtime","common/vendor"]]]);