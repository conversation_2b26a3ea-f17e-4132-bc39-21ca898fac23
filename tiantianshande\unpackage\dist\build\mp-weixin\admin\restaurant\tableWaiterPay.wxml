<view class="container"><block wx:if="{{isload}}"><block><view class="head-bg"><view class="text-center _h1">{{detail.name}}</view></view><form data-event-opts="{{[['submit',[['subform',['$event']]]]]}}" bindsubmit="__e"><block wx:if="{{detail.status==2}}"><view class="card-view"><view class="card-wrap"><view class="card-title">餐桌信息</view><view class="info-item"><view class="t1">桌台</view><view class="t2">{{detail.name}}</view></view><view class="info-item"><view class="t1">人数/座位数</view><view class="t2">{{order.renshu+"/"+detail.seat}}</view></view></view><block wx:if="{{$root.g0>0}}"><view class="card-wrap card-goods"><view class="card-title">{{"已点菜品("+orderGoodsSum+")"}}</view><block wx:for="{{orderGoods}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="info-item"><view class="t1">{{item.name+"["+item.ggname+"]"}}</view><view class="t2">{{"x"+item.num}}</view><view class="t3">{{"￥"+item.real_totalprice}}</view></view></block><view class="info-item"><view class="t1">合计</view><view class="t2">{{"x"+orderGoodsSum}}</view><view class="t3">{{"￥"+order.totalprice}}</view></view><view class="info-item"><view class="t1">优惠</view><view class="t2" style="text-align:right;"><input type="number" data-name="discount" name="discount" placeholder="输入优惠金额" data-event-opts="{{[['input',[['input',['$event']]]]]}}" bindinput="__e"/></view></view><view class="info-item"><view class="t1">实付</view><view class="t2">{{"￥"+real_totalprice}}</view></view></view></block></view></block><view class="btn-view button-sp-area"><block wx:if="{{detail.status==2}}"><button type="primary" form-type="submit">确认用户已支付</button></block></view></form><view class="btn-view button-sp-area mb"><button class="btn-default" type="default" data-url="tableWaiter" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">返回餐桌列表</button></view></block></block><block wx:if="{{loading}}"><loading vue-id="7eb35e1a-1" bind:__l="__l"></loading></block><dp-tabbar vue-id="7eb35e1a-2" opt="{{opt}}" bind:__l="__l"></dp-tabbar></view>