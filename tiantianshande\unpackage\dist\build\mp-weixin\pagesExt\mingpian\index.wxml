<block wx:if="{{isload}}"><view class="data-v-5e86e283"><view class="banner data-v-5e86e283"></view><view class="page data-v-5e86e283"><view class="data data-v-5e86e283" style="{{'background:'+(info.bgpic?'url('+info.bgpic+')':'#fff')+';'+('background-size:'+('100%')+';')}}"><view class="data_info data-v-5e86e283"><image class="data_head _img data-v-5e86e283" src="{{info.headimg}}" alt></image><view class="data-v-5e86e283"><view class="data_name data-v-5e86e283">{{info.realname}}</view><block wx:if="{{info.touxian1}}"><view class="data_text data-v-5e86e283">{{info.touxian1}}</view></block><block wx:if="{{info.touxian2}}"><view class="data_text data-v-5e86e283">{{info.touxian2}}</view></block><block wx:if="{{info.touxian3}}"><view class="data_text data-v-5e86e283">{{info.touxian3}}</view></block></view></view><block wx:if="{{info.mid==mid}}"><image class="data_tag _img data-v-5e86e283" src="{{$root.m0}}" alt data-url="edit" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"></image></block><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index"><view class="data_list data-v-5e86e283"><block wx:if="{{index=='tel'}}"><image src="{{item.m1}}" alt class="_img data-v-5e86e283"></image></block><block wx:else><block wx:if="{{index=='weixin'}}"><image src="{{item.m2}}" alt class="_img data-v-5e86e283"></image></block><block wx:else><block wx:if="{{index=='address'}}"><image src="{{item.m3}}" alt class="_img data-v-5e86e283"></image></block><block wx:else><image src="{{item.$orig.icon}}" alt class="_img data-v-5e86e283"></image></block></block></block>{{''+info[index]+''}}</view></block></view><view class="module data-v-5e86e283"><block wx:if="{{mid!=info.mid}}"><view data-event-opts="{{[['tap',[['addfavorite',['$event']]]]]}}" class="module_item data-v-5e86e283" bindtap="__e"><image class="module_img _img data-v-5e86e283" src="{{$root.m4}}" alt></image><view class="module_text data-v-5e86e283">存名片夹</view></view></block><view class="module_item data-v-5e86e283" data-url="favorite" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image class="module_img _img data-v-5e86e283" src="{{$root.m5}}" alt></image><view class="module_text data-v-5e86e283">{{mid!=info.mid?'我的名片夹':'名片夹'}}</view></view><block wx:if="{{$root.m6=='app'}}"><view data-event-opts="{{[['tap',[['shareapp',['$event']]]]]}}" class="module_item data-v-5e86e283" bindtap="__e"><image class="module_img data-v-5e86e283" src="../static/images/card_m2.png"></image><view class="module_text data-v-5e86e283">分享名片</view></view></block><block wx:else><block wx:if="{{$root.m7=='mp'}}"><view data-event-opts="{{[['tap',[['sharemp',['$event']]]]]}}" class="module_item data-v-5e86e283" bindtap="__e"><image class="module_img data-v-5e86e283" src="../static/images/card_m2.png"></image><view class="module_text data-v-5e86e283">分享名片</view></view></block><block wx:else><block wx:if="{{$root.m8=='h5'}}"><view data-event-opts="{{[['tap',[['sharemp',['$event']]]]]}}" class="module_item data-v-5e86e283" bindtap="__e"><image class="module_img data-v-5e86e283" src="../static/images/card_m2.png"></image><view class="module_text data-v-5e86e283">分享名片</view></view></block><block wx:else><button class="module_item data-v-5e86e283" open-type="share"><image class="module_img data-v-5e86e283" src="../static/images/card_m2.png"></image><view class="module_text data-v-5e86e283">分享名片</view></button></block></block></block><block wx:if="{{mid==info.mid}}"><view class="module_item data-v-5e86e283" data-url="{{'readlog?id='+info.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image class="module_img _img data-v-5e86e283" src="{{$root.m9}}" alt></image><view class="module_text data-v-5e86e283">谁看过</view></view></block><block wx:if="{{mid==info.mid}}"><view class="module_item data-v-5e86e283" data-url="{{'favoritelog?id='+info.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image class="module_img _img data-v-5e86e283" src="{{$root.m10}}" alt></image><view class="module_text data-v-5e86e283">谁收藏了</view></view></block><block wx:if="{{$root.m11}}"><view data-event-opts="{{[['tap',[['addPhoneContact',['$event']]]]]}}" class="module_item data-v-5e86e283" bindtap="__e"><image class="module_img _img data-v-5e86e283" src="{{$root.m12}}" alt></image><view class="module_text data-v-5e86e283">存通讯录</view></view></block></view><view class="list data-v-5e86e283"><view class="list_title data-v-5e86e283">联系方式</view><block wx:for="{{$root.l1}}" wx:for-item="item" wx:for-index="index"><view class="list_item data-v-5e86e283"><image class="list_img _img data-v-5e86e283" src="{{item.$orig.icon}}" alt></image><view class="list_lable data-v-5e86e283">{{item.$orig.name}}</view><block wx:if="{{index=='tel'}}"><view class="list_value data-v-5e86e283" data-url="{{'tel:'+info[index]}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">{{info[index]}}</view></block><block wx:else><block wx:if="{{index=='address'&&info.longitude}}"><view class="list_value data-v-5e86e283" data-latitude="{{info.latitude}}" data-longitude="{{info.longitude}}" data-address="{{info.address}}" data-event-opts="{{[['tap',[['openLocation',['$event']]]]]}}" bindtap="__e">{{info[index]}}</view></block><block wx:else><view class="list_value data-v-5e86e283" data-content="{{info[index]}}" data-event-opts="{{[['tap',[['fuzhi',['$event']]]]]}}" bindtap="__e"><text user-select="true" selectable="true" class="data-v-5e86e283">{{info[index]}}</text></view></block></block></view></block></view><view class="person data-v-5e86e283"><view class="person_title data-v-5e86e283"><view class="data-v-5e86e283"></view><text class="data-v-5e86e283">个人简介</text><view class="data-v-5e86e283"></view></view><dp vue-id="a07c6358-1" pagecontent="{{pagecontent}}" class="data-v-5e86e283" bind:__l="__l"></dp></view><view class="opt data-v-5e86e283"><view class="opt_module data-v-5e86e283"><view class="opt_btn data-v-5e86e283" data-url="/pages/index/index" data-opentype="reLaunch" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">回首页</view><block wx:if="{{viewmymp}}"><view class="opt_btn data-v-5e86e283" data-url="index" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">查看我的名片</view></block><block wx:else><view class="opt_btn data-v-5e86e283" data-url="edit" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">{{mid==info.mid?'编辑名片':'创建自己的名片'}}</view></block></view></view></view></view></block>