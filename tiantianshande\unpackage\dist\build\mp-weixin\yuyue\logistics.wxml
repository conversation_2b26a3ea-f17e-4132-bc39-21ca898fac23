<view class="container"><block wx:if="{{isload}}"><block><block wx:if="{{worker_order.status!=0&&worker_order.status!=4}}"><map class="map" longitude="{{binfo.longitude}}" latitude="{{binfo.latitude}}" scale="14" markers="{{[{id:0,latitude:binfo.latitude,longitude:binfo.longitude,iconPath:'/static/peisong/marker_business.png',width:'44',height:'54'},{id:0,latitude:orderinfo.latitude,longitude:orderinfo.longitude,iconPath:'/static/peisong/marker_kehu.png',width:'44',height:'54'},{id:0,latitude:worker.latitude,longitude:worker.longitude,iconPath:'/static/peisong/marker_qishou.png',width:'44',height:'54'}]}}"></map></block><block wx:else><map class="map" longitude="{{binfo.longitude}}" latitude="{{binfo.latitude}}" scale="14" markers="{{[{id:0,latitude:binfo.latitude,longitude:binfo.longitude,iconPath:'/static/peisong/marker_business.png',width:'44',height:'54'},{id:0,latitude:orderinfo.latitude,longitude:orderinfo.longitude,iconPath:'/static/peisong/marker_kehu.png',width:'44',height:'54'}]}}"></map></block><view class="order-box"><view class="head"><block wx:if="{{worker_order.status==3}}"><view class="f1"><image class="img" src="/static/peisong/ps_time.png"></image>已完成</view></block><block wx:if="{{worker_order.status==1}}"><view class="f1"><image class="img" src="/static/peisong/ps_time.png"></image>已接单</view></block><block wx:if="{{worker_order.status==2}}"><view class="f1"><image class="img" src="/static/peisong/ps_time.png"></image>服务中</view></block><view class="flex1"></view></view><view class="content" style="border-bottom:0;"><view class="f1"><view class="t1"><text class="x1">{{worker_order.juli}}</text><text class="x2">{{worker_order.juli_unit}}</text></view><view class="t2"><image class="img" src="/static/peisong/ps_juli.png"></image></view><view class="t3"><text class="x1">{{worker_order.juli2}}</text><text class="x2">{{worker_order.juli2_unit}}</text></view></view><view class="f2"><view class="t1">{{binfo.name}}</view><view class="t2">{{binfo.address}}</view><view class="t3">{{orderinfo.address}}</view></view><view data-event-opts="{{[['tap',[['daohang',['$event']]]]]}}" class="f3" catchtap="__e"><image class="img" src="{{pre_url+'/static/img/peisong/ps_daohang.png'}}"></image></view></view></view><block wx:if="{{$root.g0}}"><view class="service-timeline"><view class="timeline-title">服务进度</view><view class="timeline-container"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="{{['timeline-item',item.$orig.status==='completed'?'completed':'waiting']}}"><view class="timeline-icon"><image class="icon-img" src="{{'/static/peisong/'+item.$orig.icon+'.png'}}"></image><block wx:if="{{index<item.g1-1}}"><view class="timeline-line"></view></block></view><view class="timeline-content"><view class="timeline-header"><text class="timeline-title-text">{{item.$orig.title}}</text><block wx:if="{{item.$orig.time}}"><text class="timeline-time">{{item.$orig.time}}</text></block></view><view class="timeline-desc">{{item.$orig.desc}}</view><block wx:if="{{item.$orig.arrival_photo&&need_arrival_photo==1}}"><view class="timeline-photo"><image class="photo-img" src="{{item.$orig.arrival_photo}}" mode="aspectFill" data-src="{{item.$orig.arrival_photo}}" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image><text class="photo-desc">到达现场照片</text></view></block><block wx:if="{{item.$orig.complete_photo&&need_complete_photo==1}}"><view class="timeline-photo"><image class="photo-img" src="{{item.$orig.complete_photo}}" mode="aspectFill" data-src="{{item.$orig.complete_photo}}" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image><text class="photo-desc">服务完成照片</text></view></block></view></view></block></view></view></block><block wx:if="{{need_location==1&&arrival_info&&arrival_info.arrival_distance}}"><view class="technician-location"><view class="location-title"><text class="icon-location">📍</text><text>技师到达位置</text></view><view class="location-content"><view class="distance-info"><text class="label">距离您:</text><text class="value">{{arrival_info.arrival_distance_text}}</text></view><view class="arrival-time"><text class="label">到达时间:</text><text class="value">{{arrival_info.time}}</text></view></view><block wx:if="{{arrival_info.arrival_photo&&need_arrival_photo==1}}"><view class="location-photo"><image class="photo-img" src="{{arrival_info.arrival_photo}}" mode="aspectFill" data-src="{{arrival_info.arrival_photo}}" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image><text class="photo-desc">到达现场照片</text></view></block></view></block><view class="orderinfo"><view class="box-title">{{"购买清单("+orderinfo.procount+")"}}</view><block wx:for="{{prolist}}" wx:for-item="item" wx:for-index="idx" wx:key="idx"><view class="item"><text class="t1 flex1">{{item.name+" "+item.ggname}}</text><text class="t2 flex0">{{"￥"+item.sell_price+" ×"+item.num+''}}</text></view></block></view><block wx:if="{{worker_order.status!=0}}"><view class="orderinfo"><view class="box-title">服务信息</view><block wx:if="{{worker.realname}}"><view class="item"><text class="t1">服务人员</text><text class="t2"><text style="font-weight:bold;">{{worker.realname}}</text>{{"("+worker.tel+")"}}</text></view></block><view class="item"><text class="t1">接单时间</text><text class="t2">{{$root.m0}}</text></view><block wx:if="{{worker_order.daodiantime}}"><view class="item"><text class="t1">{{yuyue_sign?'出发时间':'到店时间'}}</text><text class="t2">{{$root.m1}}</text></view></block><block wx:if="{{worker_order.sign_time}}"><view class="item"><text class="t1">开始时间</text><text class="t2">{{$root.m2}}</text></view></block><block wx:if="{{worker_order.endtime}}"><view class="item"><text class="t1">完成时间</text><text class="t2">{{$root.m3}}</text></view></block><block wx:if="{{worker_order.arrival_distance&&need_location==1}}"><view class="item"><text class="t1">到达距离</text><text class="t2">{{worker_order.arrival_distance+" 米"}}</text></view></block></view></block><view class="orderinfo"><view class="box-title">订单信息</view><view class="item"><text class="t1">订单编号</text><text class="t2" user-select="true" selectable="true">{{orderinfo.ordernum}}</text></view><view class="item"><text class="t1">下单时间</text><text class="t2">{{$root.m4}}</text></view><view class="item"><text class="t1">支付时间</text><text class="t2">{{$root.m5}}</text></view><view class="item"><text class="t1">支付方式</text><text class="t2">{{orderinfo.paytype}}</text></view><view class="item"><text class="t1">商品金额</text><text class="t2 red">{{"¥"+orderinfo.product_price}}</text></view><view class="item"><text class="t1">实付款</text><text class="t2 red">{{"¥"+orderinfo.totalprice}}</text></view><view class="item"><text class="t1">备注</text><text class="t2 red">{{orderinfo.message?orderinfo.message:'无'}}</text></view></view><view style="width:100%;height:120rpx;"></view><view class="bottom"><block wx:if="{{worker_order.status!=0&&worker.tel}}"><view class="f1" data-tel="{{worker.tel}}" data-event-opts="{{[['tap',[['call',['$event']]]]]}}" bindtap="__e"><image class="img" src="/static/peisong/tel1.png"></image>联系服务人员</view></block><block wx:if="{{worker_order.status!=0}}"><view class="f2" data-tel="{{binfo.tel}}" data-event-opts="{{[['tap',[['call',['$event']]]]]}}" bindtap="__e"><image class="img" src="/static/peisong/tel2.png"></image>联系商家</view></block><block wx:if="{{mid==orderinfo.mid&&worker_order.worker_id>0&&worker_order.status==3}}"><view class="btn1" data-url="{{'commentps?id='+worker_order.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">评价服务人员</view></block></view></block></block><block wx:if="{{loading}}"><loading vue-id="147e1b5a-1" bind:__l="__l"></loading></block><dp-tabbar vue-id="147e1b5a-2" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="147e1b5a-3" data-ref="popmsg" bind:__l="__l"></popmsg></view>