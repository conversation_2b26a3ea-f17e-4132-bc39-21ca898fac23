require('../common/vendor.js');(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pagesB/fenhongdian/fenhongdian"],{"1e0e":function(a,t,e){"use strict";(function(a,t){var n=e("47a9");e("06e9");n(e("3240"));var i=n(e("98e6"));a.__webpack_require_UNI_MP_PLUGIN__=e,t(i.default)}).call(this,e("3223")["default"],e("df3c")["createPage"])},5605:function(a,t,e){"use strict";var n=e("9508"),i=e.n(n);i.a},9508:function(a,t,e){},"98e6":function(a,t,e){"use strict";e.r(t);var n=e("b661"),i=e("beb9");for(var o in i)["default"].indexOf(o)<0&&function(a){e.d(t,a,(function(){return i[a]}))}(o);e("5605");var r=e("828b"),s=Object(r["a"])(i["default"],n["b"],n["c"],!1,null,"218a9568",null,!1,n["a"],void 0);t["default"]=s.exports},b661:function(a,t,e){"use strict";e.d(t,"b",(function(){return i})),e.d(t,"c",(function(){return o})),e.d(t,"a",(function(){return n}));var n={loading:function(){return e.e("components/loading/loading").then(e.bind(null,"ceaa"))}},i=function(){var a=this,t=a.$createElement,e=(a._self._c,a.showSwitch&&"logs"===a.activeTab?0===a.logs.length&&!a.loading:null),n=a.showSwitch&&"logs"===a.activeTab?!a.hasMore&&a.logs.length>0:null,i=a.showSwitch&&"pointRewards"===a.activeTab?0===a.pointRewardLogs.length&&!a.pointRewardLoading:null,o=a.showSwitch&&"pointRewards"===a.activeTab?!a.pointRewardHasMore&&a.pointRewardLogs.length>0:null,r=a.showSwitch&&"rewards"===a.activeTab?0===a.rewardLogs.length&&!a.rewardLoading:null,s=a.showSwitch&&"rewards"===a.activeTab?!a.rewardHasMore&&a.rewardLogs.length>0:null,g=a.showSwitch&&"rank"===a.activeTab?0===a.rankList.length&&!a.rankLoading:null;a.$mp.data=Object.assign({},{$root:{g0:e,g1:n,g2:i,g3:o,g4:r,g5:s,g6:g}})},o=[]},b78a:function(a,t,e){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=getApp(),i={data:function(){return{showSwitch:!1,fenhongNum:0,userInfo:{},stats:{},myRank:0,activeTab:"logs",tabs:[{key:"logs",name:"点数记录"},{key:"pointRewards",name:"点数奖励"},{key:"rewards",name:"现金分红"},{key:"rank",name:"排行榜"}],filterIndex:0,filterOptions:["全部记录","增加记录","减少记录"],filterValues:["","add","sub"],logs:[],page:1,limit:10,loading:!1,hasMore:!0,pointRewardLogs:[],pointRewardPage:1,pointRewardLoading:!1,pointRewardHasMore:!0,rewardLogs:[],rewardPage:1,rewardLoading:!1,rewardHasMore:!0,rankList:[],rankPage:1,rankLoading:!1,rankHasMore:!0,refreshing:!1}},onLoad:function(){this.initData()},onPullDownRefresh:function(){this.initData()},onReachBottom:function(){this.onScrollToLower()},methods:{initData:function(){this.refreshing=!0,this.getData()},getData:function(){var a=this;a.loading=!0,n.post("ApiFenhongdian/getData",{},(function(t){a.loading=!1,a.refreshing=!1,1===t.code?(a.showSwitch=t.data.show_switch,a.fenhongNum=t.data.fenhong_num,a.userInfo={nickname:t.data.nickname,headimg:t.data.headimg},a.showSwitch&&(a.getStats(),a.getLogs(),a.getRankData())):n.alert(t.msg||"获取数据失败")}))},getStats:function(){var a=this;n.post("ApiFenhongdian/getStats",{},(function(t){1===t.status&&(a.stats=t.data)}))},getLogs:function(a){var t=this;if(a=a||!1,!(t.loading||!a&&!t.hasMore&&t.logs.length>0)){t.loading=!0;var e={page:a?t.page:1,limit:t.limit},i=t.filterValues[t.filterIndex];i&&(e.type=i),n.post("ApiFenhongdian/getLogs",e,(function(e){t.loading=!1,1===e.code?(a?t.logs=t.logs.concat(e.data):(t.logs=e.data,t.page=1),t.hasMore=e.data.length===t.limit,a&&t.page++):n.alert(e.msg||"获取记录失败")}))}},getRewardLogs:function(a){var t=this;if(a=a||!1,!(t.rewardLoading||!a&&!t.rewardHasMore&&t.rewardLogs.length>0)){t.rewardLoading=!0;var e={page:a?t.rewardPage:1,limit:t.limit};n.post("ApiFenhongdian/getRewardLogs",e,(function(e){t.rewardLoading=!1,1===e.status&&(a?t.rewardLogs=t.rewardLogs.concat(e.data):(t.rewardLogs=e.data,t.rewardPage=1),t.rewardHasMore=e.data.length===t.limit,a&&t.rewardPage++)}))}},getPointRewardLogs:function(a){var t=this;if(a=a||!1,!(t.pointRewardLoading||!a&&!t.pointRewardHasMore&&t.pointRewardLogs.length>0)){t.pointRewardLoading=!0;var e={page:a?t.pointRewardPage:1,limit:t.limit};n.post("ApiFenhongdian/getPointRewardLogs",e,(function(e){t.pointRewardLoading=!1,1===e.status?(a?t.pointRewardLogs=t.pointRewardLogs.concat(e.data):(t.pointRewardLogs=e.data,t.pointRewardPage=1),t.pointRewardHasMore=e.data.length===t.limit,a&&t.pointRewardPage++):console.log("获取分红点奖励记录失败:",e.msg)}))}},getRankData:function(a){var t=this;if(a=a||!1,!(t.rankLoading||!a&&!t.rankHasMore&&t.rankList.length>0)){t.rankLoading=!0;var e={page:a?t.rankPage:1,limit:t.limit};n.post("ApiFenhongdian/getFenhongDianRank",e,(function(e){t.rankLoading=!1,1===e.status&&(a?t.rankList=t.rankList.concat(e.data):(t.rankList=e.data,t.rankPage=1),t.myRank=e.my_rank||0,t.rankHasMore=e.data.length===t.limit,a&&t.rankPage++)}))}},switchTab:function(a){var t=a.currentTarget.dataset.key;this.activeTab=t,"pointRewards"===t&&0===this.pointRewardLogs.length?this.getPointRewardLogs():"rewards"===t&&0===this.rewardLogs.length?this.getRewardLogs():"rank"===t&&0===this.rankList.length&&this.getRankData()},onFilterChange:function(a){this.filterIndex=a.detail.value,this.filterLogs()},filterLogs:function(){this.logs=[],this.page=1,this.hasMore=!0,this.getLogs()},refreshLogs:function(){this.logs=[],this.page=1,this.hasMore=!0,this.getLogs()},onScrollToLower:function(){"logs"===this.activeTab?this.getLogs(!0):"pointRewards"===this.activeTab?this.getPointRewardLogs(!0):"rewards"===this.activeTab?this.getRewardLogs(!0):"rank"===this.activeTab&&this.getRankData(!0)}}};t.default=i},beb9:function(a,t,e){"use strict";e.r(t);var n=e("b78a"),i=e.n(n);for(var o in n)["default"].indexOf(o)<0&&function(a){e.d(t,a,(function(){return n[a]}))}(o);t["default"]=i.a}},[["1e0e","common/runtime","common/vendor"]]]);