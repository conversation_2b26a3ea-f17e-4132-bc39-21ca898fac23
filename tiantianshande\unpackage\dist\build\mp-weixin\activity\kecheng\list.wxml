<view class="container"><block wx:if="{{isload}}"><block><view class="search-container" style="{{(history_show?'height:100%;':'')}}"><view class="topsearch flex-y-center"><view class="f1 flex-y-center"><image class="img" src="/static/img/search_ico.png"></image><input placeholder="搜索感兴趣的课程" placeholder-style="font-size:24rpx;color:#C2C2C2" data-event-opts="{{[['confirm',[['searchConfirm',['$event']]]],['input',[['searchChange',['$event']]]]]}}" value="{{keyword}}" bindconfirm="__e" bindinput="__e"/></view><view data-event-opts="{{[['tap',[['searchbtn',['$event']]]]]}}" class="search-btn" bindtap="__e"><block wx:if="{{!history_show&&productlisttype=='itemlist'}}"><image style="height:36rpx;width:36rpx;" src="/static/img/show-cascades.png"></image></block><block wx:if="{{!history_show&&productlisttype=='item2'}}"><image style="height:36rpx;width:36rpx;" src="/static/img/show-list.png"></image></block><block wx:if="{{history_show}}"><text>搜索</text></block></view></view><view hidden="{{!(history_show)}}" class="search-history"><view><text class="search-history-title">最近搜索</text><view data-event-opts="{{[['tap',[['deleteSearchHistory',['$event']]]]]}}" class="delete-search-history" bindtap="__e"><image style="width:36rpx;height:36rpx;" src="/static/img/del.png"></image></view></view><view class="search-history-list"><block wx:for="{{history_list}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="search-history-item" data-value="{{item}}" data-event-opts="{{[['tap',[['historyClick',['$event']]]]]}}" bindtap="__e">{{item+''}}</view></block><block wx:if="{{$root.g0}}"><view class="flex-y-center"><image style="width:36rpx;height:36rpx;margin-right:10rpx;" src="/static/img/tanhao.png"></image>暂无记录</view></block></view></view><view hidden="{{!(!history_show)}}" class="search-navbar"><view class="search-navbar-item" style="{{(!field||field=='sort'?'color:'+$root.m0:'')}}" data-field="sort" data-order="desc" data-event-opts="{{[['tap',[['sortClick',['$event']]]]]}}" catchtap="__e">综合</view><view class="search-navbar-item" style="{{(field=='readnum'?'color:'+$root.m1:'')}}" data-field="readnum" data-order="desc" data-event-opts="{{[['tap',[['sortClick',['$event']]]]]}}" catchtap="__e">浏览量</view><view class="search-navbar-item" data-field="price" data-order="{{order=='asc'?'desc':'asc'}}" data-event-opts="{{[['tap',[['sortClick',['$event']]]]]}}" catchtap="__e"><text style="{{(field=='price'?'color:'+$root.m2:'')}}">价格</text><text class="iconfont iconshangla" style="{{(field=='price'&&order=='asc'?'color:'+$root.m3:'')}}"></text><text class="iconfont icondaoxu" style="{{(field=='price'&&order=='desc'?'color:'+$root.m4:'')}}"></text></view><view data-event-opts="{{[['tap',[['showDrawer',['showRight']]]]]}}" class="search-navbar-item flex-x-center flex-y-center" catchtap="__e">筛选<text class="{{['iconfont iconshaixuan '+(showfilter?'active':'')]}}"></text></view></view><uni-drawer class="vue-ref" vue-id="36c3c794-1" mode="right" width="{{280}}" data-ref="showRight" data-event-opts="{{[['^change',[['change',['$event','showRight']]]]]}}" bind:change="__e" bind:__l="__l" vue-slots="{{['default']}}"><view class="filter-scroll-view"><scroll-view class="filter-scroll-view-box" scroll-y="true"><view class="search-filter"><view class="filter-title">筛选</view><view class="filter-content-title">课程分类</view><view class="search-filter-content"><view class="filter-item" style="{{(catchecid==oldcid?'color:'+$root.m5+';background:rgba('+$root.m6+',0.1)':'')}}" data-cid="{{oldcid}}" data-event-opts="{{[['tap',[['cateClick',['$event']]]]]}}" catchtap="__e">全部</view><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><view class="filter-item" style="{{(catchecid==item.$orig.id?'color:'+item.m7+';background:rgba('+item.m8+',0.1)':'')}}" data-cid="{{item.$orig.id}}" data-event-opts="{{[['tap',[['cateClick',['$event']]]]]}}" catchtap="__e">{{item.$orig.name}}</view></block></block></view><block wx:if="{{$root.g1>0}}"><block><view class="filter-content-title">下级分类</view><view class="search-filter-content"><view class="filter-item" style="{{(catchecid2==''?'color:'+$root.m9+';background:rgba('+$root.m10+',0.1)':'')}}" data-cid2 data-event-opts="{{[['tap',[['cate2Click',['$event']]]]]}}" catchtap="__e">全部</view><block wx:for="{{$root.l1}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><view class="filter-item" style="{{(catchecid2==item.$orig.id?'color:'+item.m11+';background:rgba('+item.m12+',0.1)':'')}}" data-cid2="{{item.$orig.id}}" data-event-opts="{{[['tap',[['cate2Click',['$event']]]]]}}" catchtap="__e">{{item.$orig.name}}</view></block></block></view></block></block><view class="search-filter-btn"><view data-event-opts="{{[['tap',[['filterReset',['$event']]]]]}}" class="btn" bindtap="__e">重置</view><view data-event-opts="{{[['tap',[['filterConfirm',['$event']]]]]}}" class="btn2" style="{{'background:'+($root.m13)+';'}}" bindtap="__e">确定</view></view></view></scroll-view></view></uni-drawer></view><view class="product-container"><block wx:if="{{$root.g2}}"><block><block wx:if="{{productlisttype=='item2'}}"><dp-kecheng-item vue-id="36c3c794-2" data="{{datalist}}" sysset="{{sysset}}" menuindex="{{menuindex}}" bind:__l="__l"></dp-kecheng-item></block><block wx:if="{{productlisttype=='itemlist'}}"><dp-kecheng-itemlist vue-id="36c3c794-3" data="{{datalist}}" sysset="{{sysset}}" menuindex="{{menuindex}}" bind:__l="__l"></dp-kecheng-itemlist></block></block></block><block wx:if="{{nomore}}"><nomore vue-id="36c3c794-4" text="没有更多课程了" bind:__l="__l"></nomore></block><block wx:if="{{nodata}}"><nodata vue-id="36c3c794-5" text="没有查找到相关课程" bind:__l="__l"></nodata></block><block wx:if="{{loading}}"><loading vue-id="36c3c794-6" bind:__l="__l"></loading></block></view></block></block><block wx:if="{{loading}}"><loading vue-id="36c3c794-7" bind:__l="__l"></loading></block><dp-tabbar vue-id="36c3c794-8" opt="{{opt}}" data-event-opts="{{[['^getmenuindex',[['getmenuindex']]]]}}" bind:getmenuindex="__e" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="36c3c794-9" data-ref="popmsg" bind:__l="__l"></popmsg></view>