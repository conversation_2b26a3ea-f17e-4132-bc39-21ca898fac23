<view class="container"><block wx:if="{{isload}}"><block><view class="search-bar"><input class="search-input" type="text" placeholder="搜索许愿内容" data-event-opts="{{[['confirm',[['searchWishes',['$event']]]],['input',[['__set_model',['','searchKeyword','$event',[]]]]]]}}" value="{{searchKeyword}}" bindconfirm="__e" bindinput="__e"/><button data-event-opts="{{[['tap',[['searchWishes',['$event']]]]]}}" class="search-btn" bindtap="__e">搜索</button></view><view class="filter-bar"><view class="filter-item"><text class="filter-label">类型:</text><picker value="{{typeIndex}}" range="{{typeOptions}}" data-event-opts="{{[['change',[['onTypeChange',['$event']]]]]}}" bindchange="__e"><view class="picker-text">{{typeOptions[typeIndex]}}</view></picker></view><view class="filter-item"><text class="filter-label">排序:</text><picker value="{{sortIndex}}" range="{{sortOptions}}" data-event-opts="{{[['change',[['onSortChange',['$event']]]]]}}" bindchange="__e"><view class="picker-text">{{sortOptions[sortIndex]}}</view></picker></view></view><view class="wish-list"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="wish-item"><view class="user-info"><image class="user-avatar" src="{{item.$orig.headimg||defaultAvatar}}"></image><view class="user-details"><text class="nickname">{{item.$orig.nickname}}</text><text class="time">{{item.m0}}</text></view><text class="type-tag" style="{{'background-color:'+(item.m1)+';'}}">{{item.m2}}</text></view><block wx:if="{{item.$orig.wish_content}}"><view class="wish-content"><text>{{item.$orig.wish_content}}</text></view></block><view class="wish-footer"><text class="amount" style="color:#ff6b6b;">{{"￥"+item.$orig.amount}}</text><view class="actions"><view class="like-btn" data-id="{{item.$orig.id}}" data-event-opts="{{[['tap',[['toggleLike',['$event']]]]]}}" bindtap="__e"><image class="like-icon" src="{{item.$orig.is_liked?likedIcon:likeIcon}}"></image><text class="like-count">{{item.$orig.like_count||0}}</text></view><button class="bless-btn" style="background:linear-gradient(90deg, #ff6b6b 0%, rgba(255, 107, 107, 0.8) 100%);" data-id="{{item.$orig.id}}" data-event-opts="{{[['tap',[['sendBless',['$event']]]]]}}" bindtap="__e">祝福</button></view></view></view></block></view><block wx:if="{{$root.g0}}"><view class="empty-state"><image class="empty-icon" src="{{pre_url+'/static/img/empty-wishes.png'}}"></image><text class="empty-text">暂无许愿记录</text></view></block></block></block><block wx:if="{{nodata}}"><nodata vue-id="4c131619-1" bind:__l="__l"></nodata></block><block wx:if="{{loading}}"><loading vue-id="4c131619-2" bind:__l="__l"></loading></block><popmsg class="vue-ref" vue-id="4c131619-3" data-ref="popmsg" bind:__l="__l"></popmsg><block wx:if="{{showFilterModal}}"><view data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" class="modal" bindtap="__e"><view data-event-opts="{{[['tap',[['',['$event']]]]]}}" class="filter-modal" catchtap="__e"><view class="modal-header"><view class="modal-title">筛选条件</view><view data-event-opts="{{[['tap',[['e1',['$event']]]]]}}" class="close-btn" bindtap="__e">×</view></view><view class="filter-content"><view class="filter-section"><view class="section-title">供奉类型</view><view class="option-list"><view data-event-opts="{{[['tap',[['selectFilterType',['']]]]]}}" class="{{['option-item',(filterType==='')?'active':'']}}" bindtap="__e">全部</view><view data-event-opts="{{[['tap',[['selectFilterType',['gonghua']]]]]}}" class="{{['option-item',(filterType==='gonghua')?'active':'']}}" bindtap="__e">供花</view><view data-event-opts="{{[['tap',[['selectFilterType',['gongguo']]]]]}}" class="{{['option-item',(filterType==='gongguo')?'active':'']}}" bindtap="__e">供果</view><view data-event-opts="{{[['tap',[['selectFilterType',['shangxiang']]]]]}}" class="{{['option-item',(filterType==='shangxiang')?'active':'']}}" bindtap="__e">上香</view><view data-event-opts="{{[['tap',[['selectFilterType',['xuyuan']]]]]}}" class="{{['option-item',(filterType==='xuyuan')?'active':'']}}" bindtap="__e">许愿</view></view></view><view class="filter-section"><view class="section-title">排序方式</view><view class="option-list"><view data-event-opts="{{[['tap',[['selectSortType',['time_desc']]]]]}}" class="{{['option-item',(sortType==='time_desc')?'active':'']}}" bindtap="__e">最新发布</view><view data-event-opts="{{[['tap',[['selectSortType',['amount_desc']]]]]}}" class="{{['option-item',(sortType==='amount_desc')?'active':'']}}" bindtap="__e">金额最高</view><view data-event-opts="{{[['tap',[['selectSortType',['like_desc']]]]]}}" class="{{['option-item',(sortType==='like_desc')?'active':'']}}" bindtap="__e">点赞最多</view></view></view></view><view class="filter-footer"><view data-event-opts="{{[['tap',[['resetFilter',['$event']]]]]}}" class="reset-btn" bindtap="__e">重置</view><view data-event-opts="{{[['tap',[['applyFilter',['$event']]]]]}}" class="confirm-btn" style="background:linear-gradient(90deg, #ff6b6b 0%, rgba(255, 107, 107, 0.8) 100%);" bindtap="__e">确定</view></view></view></view></block></view>