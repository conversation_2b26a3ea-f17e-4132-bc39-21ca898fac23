<view><block wx:if="{{isload}}"><block><view class="container"><view class="containerbox"><view class="swiper-container"><swiper class="swiper" indicator-dots="{{false}}" autoplay="{{true}}" interval="{{5000}}" data-event-opts="{{[['change',[['swiperChange',['$event']]]]]}}" bindchange="__e"><block wx:for="{{product.pics}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><swiper-item class="swiper-item"><view class="swiper-item-view"><image class="img" src="{{item}}" mode="widthFix"></image></view></swiper-item></block></block></swiper><view class="imageCount">{{current+1+"/"+$root.g0}}</view></view><view class="collage_title"><view class="f1"><view class="t1"><view class="x1">￥</view><view class="x2">{{product.sell_price}}</view><view class="t2">{{"￥"+product.market_price}}</view><view class="x3">{{product.teamnum+"人团"}}</view></view></view><block wx:if="{{product.isktdate==1}}"><view><block wx:if="{{kaituan_status==0||kaituan_status==1}}"><view class="f2"><view class="t1">{{"距"+(kaituan_status==0?'开始':'结束')+"还剩"}}</view><view class="t2" id="djstime"><text class="djsspan">{{djshour}}</text>:<text class="djsspan">{{djsmin}}</text>:<text class="djsspan">{{djssec}}</text></view></view></block><block wx:if="{{kaituan_status==2}}"><view class="f2"><view class="t1">今日已结束</view></view></block></view></block><block wx:else><view><view class="f2">{{"已团"+product.sales+"件"}}</view></view></block></view></view><view class="header"><view class="title"><view class="lef"><text>{{product.name}}</text></view><view data-event-opts="{{[['tap',[['shareClick',['$event']]]]]}}" class="share" bindtap="__e"><image src="/static/img/share.png"></image><text>分享</text></view></view><view class="title2 flex"><view class="p1"><image src="/static/img/pintuan_1.png"></image><view class="t1">参与拼团</view><view class="t1"><text class="t1_1">{{product.teamnum}}</text>人成团</view></view><view class="p1"><image src="/static/img/pintuan_2.png"></image><view class="t1"><text class="t1_1">{{product.gua_num}}</text>人拼中发货</view><view class="t1"><text class="t1_1">{{product.teamnum-product.gua_num}}</text>人未中退款</view></view><view class="p1"><image src="/static/img/pintuan_3.png"></image><view class="t1">未中补贴</view><block wx:if="{{product.bzjl_type==1}}"><view class="t1"><text class="t1_1">{{product.fy_money_val}}</text>元参与奖</view></block><block wx:if="{{product.bzjl_type==2}}"><view class="t1"><text class="t1_1">{{product.bzj_score}}</text>积分</view></block><block wx:if="{{product.bzjl_type==3}}"><view class="t1"><text class="t1_1">{{product.bzj_commission}}</text>元佣金</view></block><block wx:if="{{product.bzjl_type==4}}"><view class="t1"><text class="t1_1"></text>优惠券</view></block></view></view></view><block wx:if="{{teamCount>0}}"><view class="teamlist"><view class="label"><view class="after" style="{{'background:'+($root.m0)+';'}}"></view>{{teamCount+"人在拼单，可直接参与"}}</view><scroll-view class="content" scroll-y="{{true}}"><block wx:for="{{teamList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="item"><view class="f1"><image src="{{item.headimg}}"></image><image class="img1" src="/static/img/wh.png"></image></view><view class="f2"><view class="t1">{{"还差"+(item.teamnum-item.num)+"人拼成"}}</view><view class="t2">{{"剩余"+item.djs}}</view></view><button class="f3" data-btntype="3" data-teamid="{{item.id}}" data-event-opts="{{[['tap',[['buydialogShow',['$event']]]]]}}" bindtap="__e">去参团</button></view></block></scroll-view></view></block><block wx:if="{{shopset.comment==1&&commentcount>0}}"><view class="commentbox"><view class="title"><view class="f1">{{"评价("+commentcount+")"}}</view><view class="f2" data-url="{{'commentlist?proid='+product.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">好评度<text style="{{'color:'+($root.m1)+';'}}">{{product.comment_haopercent+"%"}}</text><image style="width:32rpx;height:32rpx;" src="/static/img/arrowright.png"></image></view></view><view class="comment"><block wx:if="{{$root.g1>0}}"><view class="item"><view class="f1"><image class="t1" src="{{commentlist[0].headimg}}"></image><view class="t2">{{commentlist[0].nickname}}</view><view class="flex1"></view><view class="t3"><block wx:for="{{[0,1,2,3,4]}}" wx:for-item="item2" wx:for-index="index2" wx:key="index2"><image class="img" src="{{'/static/img/star'+(commentlist[0].score>item2?'2':'')+'.png'}}"></image></block></view></view><view class="f2"><text class="t1">{{commentlist[0].content}}</text><view class="t2"><block wx:if="{{commentlist[0].content_pic!=''}}"><block><block wx:for="{{commentlist[0].content_pic}}" wx:for-item="itemp" wx:for-index="index" wx:key="index"><block><view data-url="{{itemp}}" data-urls="{{commentlist[0].content_pic}}" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"><image src="{{itemp}}" mode="widthFix"></image></view></block></block></block></block></view></view><view class="f3" data-url="{{'commentlist?proid='+product.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">查看全部评价</view></view></block><block wx:else><view class="nocomment">暂无评价~</view></block></view></view></block><block wx:if="{{shopset.showjd==1}}"><view class="shop"><image class="p1" src="{{business.logo}}"></image><view class="p2 flex1"><view class="t1">{{business.name}}</view><view class="t2">{{business.desc}}</view></view><button class="p4" style="{{('background:linear-gradient(90deg,'+$root.m2+' 0%,rgba('+$root.m3+',0.8) 100%)')}}" data-url="{{product.bid==0?'/pages/index/index':'/pagesExt/business/index?id='+product.bid}}" data-opentype="reLaunch" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">进入店铺</button></view></block><view class="detail_title"><view class="t1"></view><view class="t2"></view><view class="t0">商品描述</view><view class="t2"></view><view class="t1"></view></view><view class="detail"><dp vue-id="1f52c5de-1" pagecontent="{{pagecontent}}" bind:__l="__l"></dp></view><view style="width:100%;height:70px;"></view><block wx:if="{{product.status==1}}"><view class="{{['bottombar','flex-row',menuindex>-1?'tabbarbot':'notabbarbot']}}"><view class="item" data-url="{{'prolist?bid='+product.bid}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image class="img" src="/static/img/shou.png"></image><view class="t1">首页</view></view><block wx:if="{{kfurl!='contact::'}}"><view class="item" data-url="{{kfurl}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image class="img" src="/static/img/kefu.png"></image><view class="t1">客服</view></view></block><block wx:else><button class="item" open-type="contact"><image class="img" src="/static/img/kefu.png"></image><view class="t1">客服</view></button></block><view data-event-opts="{{[['tap',[['addfavorite',['$event']]]]]}}" class="item" bindtap="__e"><image class="img" src="/static/img/shoucang.png"></image><view class="t1">{{isfavorite?'已收藏':'收藏'}}</view></view><view class="tocart" style="{{'background:'+($root.m4)+';'}}" data-btntype="1" data-event-opts="{{[['tap',[['buydialogShow',['$event']]]]]}}" bindtap="__e"><text>{{"￥"+product.market_price}}</text><text>单独购买</text></view><block wx:if="{{kaituan_status==1&&product.isktdate==1}}"><view class="tobuy" style="{{'background:'+($root.m5)+';'}}" data-btntype="2" data-event-opts="{{[['tap',[['buydialogShow',['$event']]]]]}}" bindtap="__e"><text>{{"￥"+product.sell_price}}</text><text>发起拼团</text></view></block><block wx:else><block wx:if="{{kaituan_status!=1&&product.isktdate==1}}"><view class="tobuy flex-x-center flex-y-center" style="background:#ccc;">发起拼团</view></block><block wx:else><view class="tobuy" style="{{'background:'+($root.m6)+';'}}" data-btntype="2" data-event-opts="{{[['tap',[['buydialogShow',['$event']]]]]}}" bindtap="__e"><text>{{"￥"+product.sell_price}}</text><text>发起拼团</text></view></block></block></view></block><view hidden="{{buydialogHidden}}"><view class="buydialog-mask"><view class="{{['buydialog',menuindex>-1?'tabbarbot':'notabbarbot']}}"><view data-event-opts="{{[['tap',[['buydialogChange',['$event']]]]]}}" class="close" bindtap="__e"><image class="image" src="/static/img/close.png"></image></view><view class="title"><image class="img" src="{{guigelist[ks].pic?guigelist[ks].pic:product.pic}}" data-url="{{guigelist[ks].pic?guigelist[ks].pic:product.pic}}" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image><block wx:if="{{btntype==1}}"><view class="price"><text class="t1">￥</text>{{guigelist[ks].market_price}}</view></block><block wx:else><view class="price"><text class="t1">￥</text>{{guigelist[ks].sell_price+''}}<block wx:if="{{guigelist[ks].market_price>guigelist[ks].sell_price}}"><text class="t2">{{"￥"+guigelist[ks].market_price}}</text></block></view></block><view class="choosename">{{"已选规格: "+guigelist[ks].name}}</view><view class="stock">{{"剩余"+guigelist[ks].stock+"件"}}</view></view><block wx:for="{{guigedata}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="guigelist flex-col"><view class="name">{{item.title}}</view><view class="item flex flex-y-center"><block wx:for="{{item.items}}" wx:for-item="item2" wx:for-index="index2" wx:key="index2"><block><view class="{{['item2 '+(ggselected[item.k]==item2.k?'on':'')]}}" data-itemk="{{item.k}}" data-idx="{{item2.k}}" data-event-opts="{{[['tap',[['ggchange',['$event']]]]]}}" bindtap="__e">{{item2.title}}</view></block></block></view></view></block><view class="buynum flex flex-y-center"><view class="flex1">购买数量：</view><view class="addnum"><view class="minus"><image class="img" src="/static/img/cart-minus.png" data-event-opts="{{[['tap',[['gwcminus',['$event']]]]]}}" bindtap="__e"></image></view><input class="input" type="number" max="1" data-event-opts="{{[['input',[['gwcinput',['$event']]]]]}}" value="{{gwcnum}}" bindinput="__e"/><view class="plus"><image class="img" src="/static/img/cart-plus.png" data-event-opts="{{[['tap',[['gwcplus',['$event']]]]]}}" bindtap="__e"></image></view></view></view><view class="op"><block wx:if="{{btntype==1}}"><block><button class="tobuy" style="{{'background:'+($root.m7)+';'}}" data-type="1" data-event-opts="{{[['tap',[['tobuy',['$event']]]]]}}" bindtap="__e">确定</button></block></block><block wx:if="{{btntype==2}}"><block><button class="tobuy" style="{{'background:'+($root.m8)+';'}}" data-type="2" data-event-opts="{{[['tap',[['tobuy',['$event']]]]]}}" bindtap="__e">下一步</button></block></block><block wx:if="{{btntype==3}}"><block><button class="tobuy" style="{{'background:'+($root.m9)+';'}}" data-type="3" data-event-opts="{{[['tap',[['tobuy',['$event']]]]]}}" bindtap="__e">确 定</button></block></block></view></view></view></view></view><block wx:if="{{sharetypevisible}}"><view class="popup__container"><view data-event-opts="{{[['tap',[['handleClickMask',['$event']]]]]}}" class="popup__overlay" catchtap="__e"></view><view class="popup__modal" style="height:320rpx;min-height:320rpx;"><view class="popup__content"><view class="sharetypecontent"><block wx:if="{{$root.m10=='app'}}"><view data-event-opts="{{[['tap',[['shareapp',['$event']]]]]}}" class="f1" bindtap="__e"><image class="img" src="/static/img/weixin.png"></image><text class="t1">分享给好友</text></view></block><block wx:else><block wx:if="{{$root.m11=='mp'}}"><view data-event-opts="{{[['tap',[['sharemp',['$event']]]]]}}" class="f1" bindtap="__e"><image class="img" src="/static/img/weixin.png"></image><text class="t1">分享给好友</text></view></block><block wx:else><block wx:if="{{$root.m12=='h5'}}"><view data-event-opts="{{[['tap',[['sharemp',['$event']]]]]}}" class="f1" bindtap="__e"><image class="img" src="/static/img/weixin.png"></image><text class="t1">分享给好友</text></view></block><block wx:else><button class="f1" open-type="share"><image class="img" src="/static/img/weixin.png"></image><text class="t1">分享给好友</text></button></block></block></block><view data-event-opts="{{[['tap',[['showPoster',['$event']]]]]}}" class="f2" bindtap="__e"><image class="img" src="/static/img/sharepic.png"></image><text class="t1">生成分享图片</text></view></view></view></view></view></block><block wx:if="{{showposter}}"><view class="posterDialog"><view class="main"><view data-event-opts="{{[['tap',[['posterDialogClose',['$event']]]]]}}" class="close" bindtap="__e"><image class="img" src="/static/img/close.png"></image></view><view class="content"><image class="img" src="{{posterpic}}" mode="widthFix" data-url="{{posterpic}}" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image></view></view></view></block></block></block><block wx:if="{{loading}}"><loading vue-id="1f52c5de-2" bind:__l="__l"></loading></block><dp-tabbar vue-id="1f52c5de-3" opt="{{opt}}" data-event-opts="{{[['^getmenuindex',[['getmenuindex']]]]}}" bind:getmenuindex="__e" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="1f52c5de-4" data-ref="popmsg" bind:__l="__l"></popmsg></view>