<view class="container"><block wx:if="{{isload}}"><block><block wx:if="{{psorder.status!=4}}"><map class="map" longitude="{{binfo.longitude}}" latitude="{{binfo.latitude}}" scale="14" markers="{{[{id:0,latitude:binfo.latitude,longitude:binfo.longitude,iconPath:'/static/peisong/marker_business.png',width:'44',height:'54'},{id:1,latitude:orderinfo.latitude,longitude:orderinfo.longitude,iconPath:'/static/peisong/marker_kehu.png',width:'44',height:'54'},{id:2,latitude:worker.latitude,longitude:worker.longitude,iconPath:'/static/peisong/marker_qishou.png',width:'44',height:'54'}]}}"></map></block><block wx:else><map class="map" longitude="{{binfo.longitude}}" latitude="{{binfo.latitude}}" scale="14" markers="{{[{id:0,latitude:binfo.latitude,longitude:binfo.longitude,iconPath:'/static/peisong/marker_business.png',width:'44',height:'54'},{id:0,latitude:orderinfo.latitude,longitude:orderinfo.longitude,iconPath:'/static/peisong/marker_kehu.png',width:'44',height:'54'}]}}"></map></block><view class="order-box"><view class="head"><block wx:if="{{psorder.fwtype==1}}"><view><block wx:if="{{psorder.status==3}}"><view class="f1"><image class="img" src="/static/peisong/ps_time.png"></image>已完成</view></block><block wx:if="{{psorder.status==1}}"><view class="f1"><image class="img" src="/static/peisong/ps_time.png"></image>{{''+orderinfo.yydate}}<text class="t1">预计上门时间</text></view></block><block wx:if="{{psorder.status==2}}"><view class="f1"><image class="img" src="/static/peisong/ps_time.png"></image><text class="t1" style="margin-left:10rpx;">服务中</text></view></block></view></block><block wx:else><block wx:if="{{psorder.fwtype==2}}"><view><block wx:if="{{psorder.status==3}}"><view class="f1"><image class="img" src="/static/peisong/ps_time.png"></image>已完成</view></block><block wx:else><block wx:if="{{psorder.status==1}}"><view class="f1"><image class="img" src="/static/peisong/ps_time.png"></image>期望上门时间<text class="t1">{{orderinfo.yydate}}</text></view></block><block wx:else><block wx:if="{{psorder.status==2}}"><view class="f1"><image class="img" src="/static/peisong/ps_time.png"></image>已到达，服务中</view></block></block></block></view></block></block><view class="flex1"></view><view class="f2"><text class="t1">{{psorder.ticheng}}</text>元</view></view><view class="content" style="border-bottom:0;"><view class="f1"><view class="t1"><text class="x1">{{psorder.juli}}</text><text class="x2">{{psorder.juli_unit}}</text></view><view class="t2"><image class="img" src="/static/peisong/ps_juli.png"></image></view><view class="t3"><text class="x1">{{psorder.juli2}}</text><text class="x2">{{psorder.juli2_unit}}</text></view></view><view class="f2"><view class="t1">{{binfo.name}}</view><view class="t2">{{binfo.address}}</view><view class="t3">{{orderinfo.address}}</view><view class="t2">{{orderinfo.area}}</view></view><view data-event-opts="{{[['tap',[['daohang',['$event']]]]]}}" class="f3" catchtap="__e"><image class="img" src="{{pre_url+'/static/img/peisong/ps_daohang.png'}}"></image></view></view></view><view class="orderinfo"><view class="box-title">{{"商品清单("+orderinfo.procount+")"}}</view><block wx:for="{{prolist}}" wx:for-item="item" wx:for-index="idx" wx:key="idx"><view class="item"><text class="t1 flex1">{{item.name+" "+item.ggname}}</text><text class="t2 flex0">{{"￥"+item.sell_price+" ×"+item.num+''}}</text></view></block></view><block wx:if="{{psorder.status!=0}}"><view class="orderinfo"><view class="box-title">服务信息</view><view class="item"><text class="t1">用户姓名</text><text class="t2">{{orderinfo.linkman}}</text></view><view class="item"><text class="t1">用户电话</text><text class="t2">{{orderinfo.tel}}</text></view><view class="item"><text class="t1">预约时间</text><text class="t2">{{orderinfo.yydate}}</text></view><view class="item"><text class="t1">接单时间</text><text class="t2">{{$root.m0}}</text></view><block wx:if="{{psorder.daodiantime}}"><view class="item"><text class="t1">{{yuyue_sign?'出发时间':'到店时间'}}</text><text class="t2">{{$root.m1}}</text></view></block><block wx:if="{{psorder.arrival_distance&&psorder.status>=2}}"><view class="item"><text class="t1">到达距离</text><text class="t2">{{$root.m2}}</text></view></block><block wx:if="{{psorder.sign_time}}"><view class="item"><text class="t1">开始时间</text><text class="t2">{{$root.m3}}</text></view></block><block wx:if="{{psorder.endtime}}"><view class="item"><text class="t1">完成时间</text><text class="t2">{{$root.m4}}</text></view></block></view></block><block wx:if="{{$root.g0}}"><view class="orderinfo"><view class="box-title">自定义表单信息</view><block wx:for="{{formdata}}" wx:for-item="item" wx:for-index="idx" wx:key="idx"><view class="item"><text class="t1">{{item[0]}}</text><text class="{{['t2',(item[2]==='input')?'form-input':'',(item[2]==='textarea')?'form-textarea':'',(item[2]==='select')?'form-select':'']}}">{{item[1]||'未填写'}}</text></view></block></view></block><block wx:if="{{psorder.arrival_photo&&psorder.status>=2}}"><view class="orderinfo"><view class="box-title">到达现场照片</view><view class="photo-area"><block wx:for="{{psorder.arrival_photo_array}}" wx:for-item="item" wx:for-index="index" wx:key="index"><image class="service-photo" src="{{item}}" mode="widthFix" data-urls="{{psorder.arrival_photo_array}}" data-current="{{item}}" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image></block></view></view></block><block wx:if="{{psorder.complete_photo&&psorder.status>=3}}"><view class="orderinfo"><view class="box-title">服务完成照片</view><view class="photo-area"><block wx:for="{{psorder.complete_photo_array}}" wx:for-item="item" wx:for-index="index" wx:key="index"><image class="service-photo" src="{{item}}" mode="widthFix" data-urls="{{psorder.complete_photo_array}}" data-current="{{item}}" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image></block></view></view></block><block wx:if="{{$root.g1}}"><view class="orderinfo"><view class="box-title">{{"服务团队（共"+team_workers.total_count+"人）"}}</view><block wx:if="{{team_workers.current_worker}}"><view class="team-member current"><view class="member-header"><image class="member-avatar" src="{{team_workers.current_worker.headimg||'/static/img/default_avatar.png'}}"></image><view class="member-info"><view class="member-name">{{team_workers.current_worker.realname+"（我）"}}</view><view class="{{['member-role',(team_workers.current_worker.worker_role==1)?'leader':'']}}">{{''+team_workers.current_worker.role_text+''}}</view></view><view class="member-status current-status">当前技师</view></view></view></block><block wx:for="{{team_workers.other_workers}}" wx:for-item="member" wx:for-index="index" wx:key="index"><view class="team-member"><view class="member-header"><image class="member-avatar" src="{{member.headimg||'/static/img/default_avatar.png'}}"></image><view class="member-info"><view class="member-name">{{member.realname}}</view><view class="{{['member-role',(member.worker_role==1)?'leader':'']}}">{{''+member.role_text+''}}</view></view><view class="member-contact" data-tel="{{member.tel}}" data-event-opts="{{[['tap',[['call',['$event']]]]]}}" bindtap="__e"><text class="contact-icon">📞</text><text class="contact-text">联系</text></view></view></view></block><view class="team-tip"><text class="tip-text">💡 您可以联系其他技师协调服务进度</text></view></view></block><view class="orderinfo"><view class="box-title">订单信息</view><view class="item"><text class="t1">订单编号</text><text class="t2" user-select="true" selectable="true">{{orderinfo.ordernum}}</text></view><view class="item"><text class="t1">下单时间</text><text class="t2">{{$root.m5}}</text></view><view class="item"><text class="t1">支付时间</text><text class="t2">{{$root.m6}}</text></view><view class="item"><text class="t1">支付方式</text><text class="t2">{{orderinfo.paytype}}</text></view><view class="item"><text class="t1">商品金额</text><text class="t2 red">{{"¥"+orderinfo.product_price}}</text></view><view class="item"><text class="t1">实付款</text><text class="t2 red">{{"¥"+orderinfo.totalprice}}</text></view><block wx:if="{{order.remark}}"><view class="item"><text class="t1">后台备注</text><text class="t2" style="color:#FF6F30;">{{order.remark}}</text></view></block></view><view style="width:100%;height:120rpx;"></view><block wx:if="{{psorder.status!=4}}"><view class="bottom"><block wx:if="{{psorder.status!=0}}"><view class="f1" data-tel="{{orderinfo.tel}}" data-event-opts="{{[['tap',[['call',['$event']]]]]}}" bindtap="__e"><image class="img" src="/static/peisong/tel1.png"></image>联系顾客</view></block><block wx:if="{{psorder.status!=0}}"><view class="f2" data-tel="{{binfo.tel}}" data-event-opts="{{[['tap',[['call',['$event']]]]]}}" bindtap="__e"><image class="img" src="/static/peisong/tel2.png"></image>联系商家</view></block><block wx:if="{{psorder.status==0&&psorder.isqd==1}}"><view class="btn1" data-id="{{psorder.id}}" data-event-opts="{{[['tap',[['qiangdan',['$event']]]]]}}" bindtap="__e">立即抢单</view></block><block wx:if="{{psorder.status==0&&!psorder.isqd}}"><view class="btn1" style="background:#BCBFC7;">{{psorder.djs+"后可抢单"}}</view></block><block wx:if="{{psorder.fwtype==1}}"><block><block wx:if="{{psorder.status==1}}"><view class="btn1" data-id="{{psorder.id}}" data-st="2" data-event-opts="{{[['tap',[['openPunchModal',['$event']]]]]}}" bindtap="__e">顾客已到店</view></block><block wx:if="{{psorder.status==2}}"><view class="btn1" data-id="{{psorder.id}}" data-st="3" data-event-opts="{{[['tap',[['openPunchModal',['$event']]]]]}}" bindtap="__e">我已完成</view></block></block></block><block wx:if="{{psorder.fwtype==2}}"><block><block wx:if="{{psorder.status==1}}"><view class="btn1" data-id="{{psorder.id}}" data-st="2" data-event-opts="{{[['tap',[['openPunchModal',['$event']]]]]}}" bindtap="__e">我已到达</view></block><block wx:if="{{psorder.status==2}}"><view class="btn1" data-id="{{psorder.id}}" data-st="3" data-event-opts="{{[['tap',[['openPunchModal',['$event']]]]]}}" bindtap="__e">服务已完成</view></block><block wx:if="{{psorder.status==3}}"><view data-event-opts="{{[['tap',[['goToOrderList',['$event']]]]]}}" class="btn1" bindtap="__e">返回列表</view></block></block></block></view></block></block></block><block wx:if="{{loading}}"><loading vue-id="68ca7401-1" bind:__l="__l"></loading></block><dp-tabbar vue-id="68ca7401-2" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="68ca7401-3" data-ref="popmsg" bind:__l="__l"></popmsg><block wx:if="{{showPunchModal}}"><view class="punch-modal"><view class="punch-content"><view class="punch-header"><text class="punch-title">{{punchTitle}}</text><view data-event-opts="{{[['tap',[['closePunchModal',['$event']]]]]}}" class="close-btn" bindtap="__e">×</view></view><view class="location-section"><view class="section-title"><text class="icon">📍</text>位置信息</view><block wx:if="{{punchLocationInfo}}"><view class="location-content"><view class="location-status success"><text class="status-text">已获取位置信息</text><text class="location-detail">{{"经度: "+punchLocationInfo.longitude}}</text><text class="location-detail">{{"纬度: "+punchLocationInfo.latitude}}</text></view></view></block><block wx:else><view class="location-content"><view class="{{['location-status',(isLocating)?'loading':'']}}"><text class="status-text">{{isLocating?'获取位置中...':'点击获取位置'}}</text></view><block wx:if="{{!isLocating&&!punchLocationInfo}}"><view data-event-opts="{{[['tap',[['getPunchLocation',['$event']]]]]}}" class="get-location-btn" bindtap="__e">获取位置信息</view></block></view></block></view><view class="photo-section"><view class="section-title"><text class="icon">📷</text>{{''+punchPhotoType+" ("+$root.g2+"/9)"}}</view><view class="photo-content"><block wx:if="{{$root.g3>0}}"><view class="photo-list"><block wx:for="{{punchPhotos}}" wx:for-item="photo" wx:for-index="index" wx:key="index"><view class="photo-item"><image class="preview-image" src="{{photo}}" mode="aspectFill" data-url="{{photo}}" data-event-opts="{{[['tap',[['previewPunchPhoto',['$event']]]]]}}" bindtap="__e"></image><view class="remove-icon" data-index="{{index}}" data-event-opts="{{[['tap',[['removePunchPhoto',['$event']]]]]}}" bindtap="__e">×</view></view></block></view></block><block wx:if="{{$root.g4<9}}"><view data-event-opts="{{[['tap',[['selectPunchPhoto',['$event']]]]]}}" class="photo-placeholder" bindtap="__e"><text class="placeholder-icon">+</text><text class="placeholder-text">点击上传照片</text></view></block></view></view><view class="punch-actions"><view data-event-opts="{{[['tap',[['closePunchModal',['$event']]]]]}}" class="cancel-btn" bindtap="__e">取消</view><view data-event-opts="{{[['tap',[['submitPunchData',['$event']]]]]}}" class="{{['submit-btn',(!canSubmitPunch)?'disabled':'']}}" bindtap="__e">确认提交</view></view></view></view></block></view>