<view class="uni-calendar data-v-3ed38ae4"><block wx:if="{{!insert&&show}}"><view data-event-opts="{{[['tap',[['clean',['$event']]]]]}}" class="{{['uni-calendar__mask','data-v-3ed38ae4',(aniMaskShow)?'uni-calendar--mask-show':'']}}" bindtap="__e"></view></block><block wx:if="{{insert||show}}"><view class="{{['uni-calendar__content','data-v-3ed38ae4',(!insert)?'uni-calendar--fixed':'',(aniMaskShow)?'uni-calendar--ani-show':'']}}"><block wx:if="{{!insert}}"><view class="uni-calendar__header uni-calendar--fixed-top data-v-3ed38ae4"><view data-event-opts="{{[['tap',[['close',['$event']]]]]}}" class="uni-calendar__header-btn-box data-v-3ed38ae4" bindtap="__e"><text class="uni-calendar__header-text uni-calendar--fixed-width data-v-3ed38ae4">{{cancelText}}</text></view><view data-event-opts="{{[['tap',[['confirm',['$event']]]]]}}" class="uni-calendar__header-btn-box data-v-3ed38ae4" bindtap="__e"><text class="uni-calendar__header-text uni-calendar--fixed-width data-v-3ed38ae4">{{okText}}</text></view></view></block><view class="uni-calendar__header data-v-3ed38ae4"><view data-event-opts="{{[['tap',[['pre',['$event']]]]]}}" class="uni-calendar__header-btn-box data-v-3ed38ae4" catchtap="__e"><view class="uni-calendar__header-btn uni-calendar--left data-v-3ed38ae4"></view></view><picker mode="date" value="{{date}}" fields="month" data-event-opts="{{[['change',[['bindDateChange',['$event']]]]]}}" bindchange="__e" class="data-v-3ed38ae4"><text class="uni-calendar__header-text data-v-3ed38ae4">{{(nowDate.year||'')+' / '+(nowDate.month||'')}}</text></picker><view data-event-opts="{{[['tap',[['next',['$event']]]]]}}" class="uni-calendar__header-btn-box data-v-3ed38ae4" catchtap="__e"><view class="uni-calendar__header-btn uni-calendar--right data-v-3ed38ae4"></view></view></view><view class="uni-calendar__box data-v-3ed38ae4"><block wx:if="{{showMonth}}"><view class="uni-calendar__box-bg data-v-3ed38ae4"><text class="uni-calendar__box-bg-text data-v-3ed38ae4">{{nowDate.month}}</text></view></block><view class="uni-calendar__weeks data-v-3ed38ae4"><view class="uni-calendar__weeks-day data-v-3ed38ae4"><text class="uni-calendar__weeks-day-text data-v-3ed38ae4">{{SUNText}}</text></view><view class="uni-calendar__weeks-day data-v-3ed38ae4"><text class="uni-calendar__weeks-day-text data-v-3ed38ae4">{{monText}}</text></view><view class="uni-calendar__weeks-day data-v-3ed38ae4"><text class="uni-calendar__weeks-day-text data-v-3ed38ae4">{{TUEText}}</text></view><view class="uni-calendar__weeks-day data-v-3ed38ae4"><text class="uni-calendar__weeks-day-text data-v-3ed38ae4">{{WEDText}}</text></view><view class="uni-calendar__weeks-day data-v-3ed38ae4"><text class="uni-calendar__weeks-day-text data-v-3ed38ae4">{{THUText}}</text></view><view class="uni-calendar__weeks-day data-v-3ed38ae4"><text class="uni-calendar__weeks-day-text data-v-3ed38ae4">{{FRIText}}</text></view><view class="uni-calendar__weeks-day data-v-3ed38ae4"><text class="uni-calendar__weeks-day-text data-v-3ed38ae4">{{SATText}}</text></view></view><block wx:for="{{weeks}}" wx:for-item="item" wx:for-index="weekIndex" wx:key="weekIndex"><view class="uni-calendar__weeks data-v-3ed38ae4"><block wx:for="{{item}}" wx:for-item="weeks" wx:for-index="weeksIndex" wx:key="weeksIndex"><view class="uni-calendar__weeks-item data-v-3ed38ae4"><calendar-item class="uni-calendar-item--hook data-v-3ed38ae4" vue-id="{{'f18ae516-1-'+weekIndex+'-'+weeksIndex}}" weeks="{{weeks}}" calendar="{{calendar}}" selected="{{selected}}" lunar="{{lunar}}" backColor="{{backColor}}" fontColor="{{fontColor}}" data-event-opts="{{[['^change',[['choiceDate']]]]}}" bind:change="__e" bind:__l="__l"></calendar-item></view></block></view></block></view></view></block></view>