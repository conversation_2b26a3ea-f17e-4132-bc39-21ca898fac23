<view class="container"><block wx:if="{{isload}}"><block><view class="ordertop" style="{{('background:url('+pre_url+'/static/img/ordertop_refund.jpg);background-size:100%')}}"><block wx:if="{{detail.refund_status==0}}"><view class="f1"><view class="t1">已取消</view></view></block><block wx:if="{{detail.refund_status==1}}"><view class="f1"><view class="t1">审核中</view></view></block><block wx:if="{{detail.refund_status==2}}"><view class="f1"><view class="t1">审核通过，已退款</view></view></block><block wx:if="{{detail.refund_status==3}}"><view class="f1"><view class="t1">驳回</view></view></block><block wx:if="{{detail.refund_status==4}}"><view class="f1"><view class="t1">审核通过，待退货</view><view class="t2">联系商家进行退货</view></view></block></view><block wx:if="{{detail.bid>0}}"><view class="btitle flex-y-center" data-url="{{'/pagesExt/business/index?id='+detail.bid}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image style="width:36rpx;height:36rpx;" src="{{detail.binfo.logo}}"></image><view class="flex1" style="padding-left:16rpx;" decode="true" space="true">{{detail.binfo.name}}</view></view></block><view class="product"><block wx:for="{{prolist}}" wx:for-item="item" wx:for-index="idx" wx:key="idx"><view class="content"><view data-url="{{'/shopPackage/shop/product?id='+item.proid}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image src="{{item.pic}}"></image></view><view class="detail"><text class="t1">{{item.name}}</text><text class="t2">{{item.ggname}}</text><view class="t3"><text class="x1 flex1">{{"￥"+item.sell_price}}</text><text class="x2">{{"×"+item.refund_num}}</text></view></view></view></block></view><view class="orderinfo"><view class="item"><text class="t1">退货单编号</text><text class="t2" user-select="true" selectable="true">{{detail.refund_ordernum}}</text></view><view class="item"><text class="t1">订单编号</text><text class="t2" user-select="true" selectable="true" data-url="{{'detail?id='+detail.orderid}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">{{detail.ordernum}}</text></view><view class="item"><text class="t1">申请时间</text><text class="t2">{{detail.createtime}}</text></view></view><view class="orderinfo"><view class="item"><text class="t1">类型</text><text class="t2 red">{{detail.refund_type_label}}</text></view><view class="item"><text class="t1">申请退款金额</text><text class="t2 red">{{"¥"+detail.refund_money}}</text></view><view class="item"><text class="t1">退款状态</text><block wx:if="{{detail.refund_status==0}}"><text class="t2 grey">已取消</text></block><block wx:if="{{detail.refund_status==1}}"><text class="t2 red">审核中</text></block><block wx:if="{{detail.refund_status==2}}"><text class="t2 red">已退款</text></block><block wx:if="{{detail.refund_status==3}}"><text class="t2 red">已驳回</text></block><block wx:if="{{detail.refund_status==4}}"><text class="t2 red">审核通过，待退货</text></block></view><view class="item"><text class="t1">退款原因</text><text class="t2">{{detail.refund_reason}}</text></view><block wx:if="{{detail.refund_checkremark}}"><view class="item"><text class="t1">审核备注</text><text class="t2">{{detail.refund_checkremark}}</text></view></block><block wx:if="{{detail.refund_pics}}"><view class="item"><text class="t1">图片</text><view class="flex" style="flex-wrap:wrap;padding-top:20rpx;" id="content_picpreview"><block wx:for="{{detail.refund_pics}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="layui-imgbox"><view class="layui-imgbox-img"><image src="{{item}}" data-url="{{item}}" mode="widthFix" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image></view></view></block></view></view></block></view><block wx:if="{{detail.freight_type==11}}"><view class="orderinfo"><view class="item"><text class="t1">发货地址</text><text class="t2">{{"¥"+detail.freight_content.send_address+" - "+detail.freight_content.send_tel}}</text></view><view class="item"><text class="t1">收货地址</text><text class="t2">{{"¥"+detail.freight_content.receive_address+" - "+detail.freight_content.receive_tel}}</text></view></view></block><view style="width:100%;height:120rpx;"></view><view class="bottom"><block wx:if="{{detail.refund_status==1||detail.refund_status==4}}"><block><view class="btn2" data-id="{{detail.id}}" data-event-opts="{{[['tap',[['toclose',['$event']]]]]}}" bindtap="__e">取消</view></block></block></view></block></block><block wx:if="{{loading}}"><loading vue-id="56b48f70-1" bind:__l="__l"></loading></block><dp-tabbar vue-id="56b48f70-2" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="56b48f70-3" data-ref="popmsg" bind:__l="__l"></popmsg></view>