<view class="container"><block wx:if="{{isload}}"><block><block wx:for="{{items}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['goToAuthPage',['$0'],[[['items','',index]]]]]]]}}" class="content" bindtap="__e"><view class="f1"><image class="t3" src="{{item.icon}}" mode="aspectFit"></image><view class="text-section"><text class="t1">{{item.title}}</text><text class="t2">{{item.subtitle}}</text></view><text class="flex1"></text></view><view class="{{[item.status?'ribbon-3 certified':'ribbon-3 uncertified']}}"><label class="_span">{{item.status?'已认证':'未认证'}}</label></view></view></block><view style="height:140rpx;"></view><view class="back-btn" data-url="/pages/my/usercenter" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">返回个人中心</view></block></block><block wx:if="{{loading}}"><loading vue-id="2e871a7c-1" bind:__l="__l"></loading></block><dp-tabbar vue-id="2e871a7c-2" opt="{{opt}}" data-event-opts="{{[['^getmenuindex',[['getmenuindex']]]]}}" bind:getmenuindex="__e" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="2e871a7c-3" data-ref="popmsg" bind:__l="__l"></popmsg></view>