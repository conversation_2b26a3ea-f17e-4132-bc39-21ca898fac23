<view class="container"><view class="header"><view class="title">发表评论</view></view><view class="content"><block wx:if="{{training}}"><view class="training-info"><view class="training-title">{{training.name}}</view><block wx:if="{{training.subname}}"><view class="training-desc">{{training.subname}}</view></block></view></block><block wx:if="{{type==1}}"><view class="form-item"><view class="label">{{"回复 "+hfname+"："}}</view></view></block><view class="form-item"><textarea class="textarea" placeholder="写下你的想法..." auto-height="{{true}}" data-event-opts="{{[['input',[['__set_model',['','content','$event',[]]]]]]}}" value="{{content}}" bindinput="__e"></textarea></view><block wx:if="{{canImage}}"><view class="form-item"><view class="label">图片（可选）</view><view class="image-upload"><block wx:if="{{$root.g0<3}}"><view data-event-opts="{{[['tap',[['chooseImage',['$event']]]]]}}" class="upload-btn" bindtap="__e"><image class="add-icon" src="/static/img/add-image.png"></image><text>添加图片</text></view></block><block wx:for="{{images}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="image-item"><image class="image" src="{{item}}" mode="aspectFill"></image><view class="remove-btn" data-index="{{index}}" data-event-opts="{{[['tap',[['removeImage',['$event']]]]]}}" bindtap="__e">×</view></view></block></view></view></block></view><view class="footer"><button class="submit-btn" style="{{'background:'+($root.m0)+';'}}" disabled="{{!$root.g1}}" data-event-opts="{{[['tap',[['submit',['$event']]]]]}}" bindtap="__e">发表评论</button></view><popmsg class="vue-ref" vue-id="c0560dbc-1" data-ref="popmsg" bind:__l="__l"></popmsg></view>