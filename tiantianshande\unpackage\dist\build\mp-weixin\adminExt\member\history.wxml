<view class="container"><block wx:if="{{isload}}"><block><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="item"><view class="product-item2" data-url="{{(item.$orig.type=='shop'?'/pages/':'/activity/')+item.$orig.type+'/product?id='+item.$orig.proid}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="product-pic"><block wx:if="{{item.$orig.product&&item.$orig.product.pic}}"><image src="{{item.$orig.product.pic}}" mode="widthFix"></image></block></view><block wx:if="{{item.$orig.type=='scoreshop'}}"><view class="product-info"><view class="p1">{{item.$orig.product.name}}</view><view class="p2"><text class="t1" style="{{'color:'+(item.m0)+';'}}">{{item.$orig.product.score_price+item.m1}}</text><text class="t2">{{"市场价￥"+item.$orig.product.sell_price}}</text></view><view class="p3"><block wx:if="{{item.$orig.product.sales>0}}"><text class="t1">已兑换<text style="font-size:24rpx;color:#f40;padding:0 2rpx;">{{item.$orig.product.sales}}</text>件</text></block><block wx:else><block wx:if="{{item.$orig.product.sellpoint}}"><text class="t2">{{item.$orig.product.sellpoint}}</text></block></block></view></view></block><block wx:else><view class="product-info"><view class="p1">{{item.$orig.product.name}}</view><view class="p2"><text class="t1" style="{{'color:'+(item.m2)+';'}}"><text style="font-size:22rpx;">￥</text>{{item.$orig.product.sell_price}}</text><block wx:if="{{item.$orig.product.market_price*1>item.$orig.product.sell_price*1}}"><text class="t2">{{"￥"+item.$orig.product.market_price}}</text></block></view><view class="p3"><block wx:if="{{item.$orig.product.sales>0}}"><text class="t1">已售<text style="font-size:24rpx;color:#f40;padding:0 2rpx;">{{item.$orig.product.sales}}</text>件</text></block><block wx:else><block wx:if="{{item.$orig.product.sellpoint}}"><text class="t2">{{item.$orig.product.sellpoint}}</text></block></block></view></view></block></view><view class="foot"><text class="flex1">{{"浏览时间："+item.$orig.createtime}}</text></view></view></block></block></block><block wx:if="{{nomore}}"><nomore vue-id="45143f30-1" bind:__l="__l"></nomore></block><block wx:if="{{nodata}}"><nodata vue-id="45143f30-2" bind:__l="__l"></nodata></block><block wx:if="{{loading}}"><loading vue-id="45143f30-3" bind:__l="__l"></loading></block><dp-tabbar vue-id="45143f30-4" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="45143f30-5" data-ref="popmsg" bind:__l="__l"></popmsg></view>