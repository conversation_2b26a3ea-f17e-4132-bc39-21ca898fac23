(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["activity/seckill/index"],{"16c0":function(t,e,n){"use strict";n.r(e);var i=n("4d5e"),a=n("660e");for(var o in a)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(o);n("6fa8");var l=n("828b"),s=Object(l["a"])(a["default"],i["b"],i["c"],!1,null,null,null,!1,i["a"],void 0);e["default"]=s.exports},1778:function(t,e,n){"use strict";(function(t,e){var i=n("47a9");n("06e9");i(n("3240"));var a=i(n("16c0"));t.__webpack_require_UNI_MP_PLUGIN__=n,e(a.default)}).call(this,n("3223")["default"],n("df3c")["createPage"])},"4d5e":function(t,e,n){"use strict";n.d(e,"b",(function(){return a})),n.d(e,"c",(function(){return o})),n.d(e,"a",(function(){return i}));var i={nodata:function(){return n.e("components/nodata/nodata").then(n.bind(null,"101c"))},nomore:function(){return n.e("components/nomore/nomore").then(n.bind(null,"3892"))},loading:function(){return n.e("components/loading/loading").then(n.bind(null,"ceaa"))},dpTabbar:function(){return n.e("components/dp-tabbar/dp-tabbar").then(n.bind(null,"b875"))},popmsg:function(){return n.e("components/popmsg/popmsg").then(n.bind(null,"2bf2"))}},a=function(){var t=this,e=t.$createElement,n=(t._self._c,t.isload?t.__map(t.datalist,(function(e,n){var i=t.__get_orig(e),a=t.t("color1"),o=t.t("color1"),l=e.starttime<t.nowtime&&3600*t.seckill_duration+1*e.starttime>t.nowtime?t.t("color1"):null,s=e.starttime<t.nowtime&&3600*t.seckill_duration+1*e.starttime>t.nowtime?t.t("color1rgb"):null,r=e.starttime<t.nowtime&&3600*t.seckill_duration+1*e.starttime>t.nowtime||1*e.starttime+3600*t.seckill_duration<t.nowtime?null:t.t("color2");return{$orig:i,m0:a,m1:o,m2:l,m3:s,m4:r}})):null);t.$mp.data=Object.assign({},{$root:{l0:n}})},o=[]},"660e":function(t,e,n){"use strict";n.r(e);var i=n("94d9"),a=n.n(i);for(var o in i)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(o);e["default"]=a.a},"6fa8":function(t,e,n){"use strict";var i=n("d108"),a=n.n(i);a.a},"94d9":function(t,e,n){"use strict";(function(t){Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n=getApp(),i={data:function(){return{opt:{},loading:!1,isload:!1,menuindex:-1,bid:"",st:"all",datalist:[],pagenum:1,navlist:"",activetime:"",activeindex:"",selected:"",top_bar_scroll:"",seckill_duration:"",nowtime:"",nomore:!1,nodata:!1}},onLoad:function(t){this.opt=n.getopts(t),this.st=this.opt.st,this.bid=this.opt.bid||"",this.getdata()},onPullDownRefresh:function(){this.getdata()},onReachBottom:function(){this.nodata||this.nomore||(this.pagenum=this.pagenum+1,this.getDataList(!0))},methods:{getdata:function(){var e=this;e.loading=!0,n.post("ApiSeckill/index",{},(function(i){e.loading=!1,0==i.status?n.alert(i.msg):(e.navlist=i.navlist,e.activetime=i.activetime,e.activeindex=i.selected,e.selected=i.selected,e.top_bar_scroll=(i.selected-2)*t.getSystemInfoSync().windowWidth/750*150,e.seckill_duration=i.seckill_duration,e.nowtime=i.nowtime,e.getDataList()),e.loaded()}))},changetab:function(e){var n=e.currentTarget.dataset.index;t.pageScrollTo({scrollTop:0,duration:0}),this.selected=n,this.getDataList()},getDataList:function(e){e||(this.pagenum=1,this.datalist=[]);var i=this,a=i.selected,o=i.navlist,l=i.pagenum;i.nomore=!1,i.nodata=!1,n.post("ApiSeckill/getprolist",{bid:i.bid,seckill_date:o[a].seckill_date,seckill_time:o[a].seckill_time,pagenum:l},(function(e){t.stopPullDownRefresh();var n=e.data;if(1==l)i.datalist=n,0==n.length&&(i.nodata=!0);else if(0==n.length)i.nomore=!0;else{var a=i.datalist,o=a.concat(n);i.datalist=o}}))}}};e.default=i}).call(this,n("df3c")["default"])},d108:function(t,e,n){}},[["1778","common/runtime","common/vendor"]]]);