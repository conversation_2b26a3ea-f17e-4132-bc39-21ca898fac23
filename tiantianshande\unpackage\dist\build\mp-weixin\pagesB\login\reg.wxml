<view class="container"><block wx:if="{{isload}}"><block><view style="width:100%;height:100%;"><view class="bg_div1" style="{{(loginset_data.bgtype==1?'background:'+loginset_data.bgcolor:'background:url('+loginset_data.bgimg+') no-repeat center;background-size:100% 100%')}}"><view style="overflow:hidden;"><view class="content_div1"><view class="card_div1" style="{{('background:'+loginset_data.cardcolor)}}"><block wx:if="{{logintype!=4&&logintype!=5&&logintype!=6}}"><block><form data-event-opts="{{[['submit',[['formSubmit',['$event']]]],['reset',[['formReset',['$event']]]]]}}" bindsubmit="__e" bindreset="__e"><view class="title1" style="{{('color:'+loginset_data.titlecolor+';text-align:'+loginset_data.titletype+';margin-top: 20rpx;font-size: 40rpx;')}}">注册账号</view><view class="regform" style="margin-top:20rpx;"><block wx:if="{{!show_custom_field}}"><block><view class="form-item"><image class="img" src="{{pre_url+'/static/img/reg-tel.png'}}"></image><input class="input" type="text" placeholder="{{tel_placeholder}}" placeholder-style="font-size:30rpx;color:#B2B5BE" name="tel" value="" data-event-opts="{{[['input',[['telinput',['$event']]]]]}}" bindinput="__e"/></view><block wx:if="{{needsms}}"><view class="form-item"><image class="img" src="{{pre_url+'/static/img/reg-code.png'}}"></image><input class="input" type="text" placeholder="请输入验证码" placeholder-style="font-size:30rpx;color:#B2B5BE" name="smscode" value=""/><view data-event-opts="{{[['tap',[['smscode',['$event']]]]]}}" class="code" style="{{('color:'+loginset_data.codecolor)}}" bindtap="__e">{{smsdjs||'获取验证码'}}</view></view></block><view class="form-item"><image class="img" src="{{pre_url+'/static/img/reg-pwd.png'}}"></image><input class="input" type="text" placeholder="6-16位字母数字组合密码" placeholder-style="font-size:30rpx;color:#B2B5BE" name="pwd" value="" password="{{true}}"/></view><view class="form-item"><image class="img" src="{{pre_url+'/static/img/reg-pwd.png'}}"></image><input class="input" type="text" placeholder="再次输入登录密码" placeholder-style="font-size:30rpx;color:#B2B5BE" name="repwd" value="" password="{{true}}"/></view><block wx:if="{{reg_invite_code&&!parent}}"><view class="form-item"><image class="img" src="{{pre_url+'/static/img/reg-yqcode.png'}}"></image><input class="input" type="text" placeholder="{{'请输入邀请人'+reg_invite_code_text}}" placeholder-style="font-size:30rpx;color:#B2B5BE" name="yqcode" data-event-opts="{{[['input',[['yqinput',['$event']]]]]}}" value="{{yqcode}}" bindinput="__e"/></view></block><block wx:if="{{reg_invite_code&&parent&&reg_invite_code_show==1}}"><view class="form-item" style="color:#666;"><block wx:if="{{reg_invite_code_type==0}}"><block>邀请人：<image style="width:80rpx;height:80rpx;border-radius:50%;" src="{{parent.headimg}}"></image>{{''+parent.nickname+''}}</block></block><block wx:else><block>{{'邀请码：'+parent.yqcode+''}}</block></block></view></block></block></block><block wx:if="{{show_custom_field}}"><block><view class="dp-form-item"><block wx:if="{{showicon}}"><image style="max-width:60rpx;height:60rpx;" src="{{pre_url+'/static/img/reg-tel.png'}}" mode="widthFix"></image></block><view class="label">手机号<text style="color:red;">*</text></view><input class="input" type="text" placeholder="{{tel_placeholder}}" placeholder-style="font-size:30rpx;color:#B2B5BE" name="tel" value="" data-event-opts="{{[['input',[['telinput',['$event']]]]]}}" bindinput="__e"/></view><block wx:if="{{needsms}}"><view class="dp-form-item"><block wx:if="{{showicon}}"><image style="max-width:60rpx;height:60rpx;" src="{{pre_url+'/static/img/reg-code.png'}}" mode="widthFix"></image></block><text class="label">验证码<text style="{{('color:'+loginset_data.cardcolor)}}">*</text></text><view class="tel1" style="display:flex;"><input class="input" style="border:0;padding:0;" type="text" placeholder="请输入验证码" placeholder-style="font-size:30rpx;color:#B2B5BE" name="smscode" value=""/><view data-event-opts="{{[['tap',[['smscode',['$event']]]]]}}" class="code" style="{{('color:'+loginset_data.codecolor)}}" bindtap="__e">{{smsdjs||'获取验证码'}}</view></view></view></block><view class="dp-form-item"><block wx:if="{{showicon}}"><image style="max-width:60rpx;height:60rpx;" src="{{pre_url+'/static/img/reg-pwd.png'}}" mode="widthFix"></image></block><view class="label">密码<text style="color:red;">*</text></view><input class="input" type="text" placeholder="6-16位字母数字组合密码" placeholder-style="font-size:30rpx;color:#B2B5BE" name="pwd" value="" password="{{true}}"/></view><view class="dp-form-item"><block wx:if="{{showicon}}"><image style="max-width:60rpx;height:60rpx;" src="{{pre_url+'/static/img/reg-pwd.png'}}" mode="widthFix"></image></block><view class="label">确认密码<text style="{{('color:'+loginset_data.cardcolor)}}">*</text></view><input class="input" type="text" placeholder="再次输入登录密码" placeholder-style="font-size:30rpx;color:#B2B5BE" name="repwd" value="" password="{{true}}"/></view><block wx:if="{{reg_invite_code&&!parent}}"><view class="dp-form-item"><block wx:if="{{showicon}}"><image style="max-width:60rpx;height:60rpx;" src="{{pre_url+'/static/img/reg-yqcode.png'}}" mode="widthFix"></image></block><text class="label">邀请码<text style="{{('color:'+loginset_data.cardcolor)}}">*</text></text><input class="input" type="text" placeholder="{{'请输入邀请人'+reg_invite_code_text}}" placeholder-style="font-size:30rpx;color:#B2B5BE" name="yqcode" value="{{yqcode}}"/></view></block><block wx:if="{{reg_invite_code&&parent&&reg_invite_code_show==1}}"><view class="dp-form-item" style="color:#666;"><block wx:if="{{showicon}}"><image style="max-width:60rpx;height:60rpx;" src="{{pre_url+'/static/img/reg-yqcode.png'}}" mode="widthFix"></image></block><block wx:if="{{reg_invite_code_type==0}}"><block><view class="label">邀请人</view><image style="width:80rpx;height:80rpx;border-radius:50%;" src="{{parent.headimg}}"></image><view class="overflow_ellipsis">{{parent.nickname+''}}</view></block></block><block wx:else><block><view class="label">邀请码</view>{{parent.yqcode+''}}</block></block></view></block><block wx:if="{{show_custom_field}}"><view class="custom_field"><block wx:for="{{$root.l1}}" wx:for-item="item" wx:for-index="idx" wx:key="idx"><view class="{{['dp-form-item']}}"><block wx:if="{{item.$orig.val8!==undefined&&item.$orig.val8!==null&&showicon}}"><image style="max-width:60rpx;height:60rpx;" src="{{item.$orig.val8}}" mode="widthFix"></image></block><view class="label">{{item.$orig.val1}}<text style="{{'color:'+(item.$orig.val3==1?'red':loginset_data.cardcolor)+';'}}">*</text></view><block wx:if="{{item.$orig.key=='input'||item.$orig.key=='realname'||item.$orig.key=='usercard'}}"><block><block wx:if="{{item.$orig.val5}}"><text style="margin-right:10rpx;">{{item.$orig.val5}}</text></block><input class="input" type="{{item.$orig.input_type}}" name="{{'form'+idx}}" placeholder="{{item.$orig.val2}}" placeholder-style="font-size:30rpx;color:#B2B5BE" data-formidx="{{'form'+idx}}" data-event-opts="{{[['input',[['setfield',['$event']]]]]}}" value="{{custom_formdata['form'+idx]}}" bindinput="__e"/></block></block><block wx:if="{{item.$orig.key=='textarea'}}"><block><textarea class="textarea" name="{{'form'+idx}}" placeholder="{{item.$orig.val2}}" placeholder-style="font-size:30rpx;color:#B2B5BE" data-formidx="{{'form'+idx}}" data-event-opts="{{[['input',[['setfield',['$event']]]]]}}" value="{{custom_formdata['form'+idx]}}" bindinput="__e"></textarea></block></block><block wx:if="{{item.$orig.key=='radio'||item.$orig.key=='sex'}}"><block><radio-group class="flex" style="flex-wrap:wrap;" name="{{'form'+idx}}" data-formidx="{{'form'+idx}}" data-event-opts="{{[['change',[['setfield',['$event']]]]]}}" bindchange="__e"><block wx:for="{{item.$orig.val2}}" wx:for-item="item1" wx:for-index="idx1" wx:key="id"><label class="flex-y-center"><radio class="radio" style="transform:scale(0.8);" value="{{item1}}" checked="{{custom_formdata['form'+idx]&&custom_formdata['form'+idx]==item1?true:false}}"></radio>{{item1+''}}</label></block></radio-group></block></block><block wx:if="{{item.$orig.key=='checkbox'}}"><block><checkbox-group class="flex" style="flex-wrap:wrap;" name="{{'form'+idx}}" data-formidx="{{'form'+idx}}" data-event-opts="{{[['change',[['setfield',['$event']]]]]}}" bindchange="__e"><block wx:for="{{item.l0}}" wx:for-item="item1" wx:for-index="idx1" wx:key="id"><label class="flex-y-center"><checkbox class="checkbox" style="transform:scale(0.8);" value="{{item1.$orig}}" checked="{{item1.m0?true:false}}"></checkbox>{{item1.$orig+''}}</label></block></checkbox-group></block></block><block wx:if="{{item.$orig.key=='selector'}}"><block><picker class="picker" mode="selector" name="{{'form'+idx}}" value="{{editorFormdata[idx]}}" range="{{item.$orig.val2}}" data-idx="{{idx}}" data-formidx="{{'form'+idx}}" data-event-opts="{{[['change',[['editorBindPickerChange',['$event']]]]]}}" bindchange="__e"><block wx:if="{{editorFormdata[idx]||editorFormdata[idx]===0}}"><view>{{''+item.$orig.val2[editorFormdata[idx]]}}</view></block><block wx:else><view style="color:#b2b5be;">请选择</view></block></picker></block></block><block wx:if="{{item.$orig.key=='time'}}"><block><picker class="picker" mode="time" name="{{'form'+idx}}" value="{{custom_formdata['form'+idx]}}" start="{{item.$orig.val2[0]}}" end="{{item.$orig.val2[1]}}" range="{{item.$orig.val2}}" data-idx="{{idx}}" data-formidx="{{'form'+idx}}" data-event-opts="{{[['change',[['editorBindPickerChange',['$event']]]]]}}" bindchange="__e"><block wx:if="{{editorFormdata[idx]}}"><view>{{editorFormdata[idx]}}</view></block><block wx:else><view style="color:#b2b5be;">请选择</view></block></picker></block></block><block wx:if="{{item.$orig.key=='date'||item.$orig.key=='birthday'}}"><block><picker class="picker" mode="date" name="{{'form'+idx}}" value="{{custom_formdata['form'+idx]}}" start="{{item.$orig.val2[0]}}" end="{{item.$orig.val2[1]}}" range="{{item.$orig.val2}}" data-idx="{{idx}}" data-formidx="{{'form'+idx}}" data-event-opts="{{[['change',[['editorBindPickerChange',['$event']]]]]}}" bindchange="__e"><block wx:if="{{editorFormdata[idx]}}"><view>{{editorFormdata[idx]}}</view></block><block wx:else><view style="color:#b2b5be;">请选择</view></block></picker></block></block><block wx:if="{{item.$orig.key=='region'}}"><block><uni-data-picker vue-id="{{'62e0a43b-1-'+idx}}" localdata="{{items}}" popup-title="请选择省市区" placeholder="{{custom_formdata['form'+idx]||'请选择省市区'}}" data-formidx="{{'form'+idx}}" data-event-opts="{{[['^change',[['onchange']]]]}}" bind:change="__e" bind:__l="__l"></uni-data-picker><input style="display:none;" type="text" name="{{'form'+idx}}" value="{{regiondata?regiondata:custom_formdata['form'+idx]}}"/></block></block><block wx:if="{{item.$orig.key=='upload'}}"><block><input style="display:none;" type="text" name="{{'form'+idx}}" value="{{editorFormdata[idx]}}"/><view class="flex" style="flex-wrap:wrap;"><block wx:if="{{editorFormdata[idx]}}"><view class="dp-form-imgbox"><view class="dp-form-imgbox-close" data-idx="{{idx}}" data-formidx="{{'form'+idx}}" data-event-opts="{{[['tap',[['removeimg',['$event']]]]]}}" bindtap="__e"><image class="image" src="{{pre_url+'/static/img/ico-del.png'}}"></image></view><view class="dp-form-imgbox-img"><image class="image" src="{{editorFormdata[idx]}}" data-url="{{editorFormdata[idx]}}" mode="widthFix" data-idx="{{idx}}" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image></view></view></block><block wx:else><view class="dp-form-uploadbtn" style="{{'background:'+('url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 60rpx')+';'+('background-size:'+('80rpx 80rpx')+';')+('background-color:'+('#F3F3F3')+';')}}" data-idx="{{idx}}" data-formidx="{{'form'+idx}}" data-event-opts="{{[['tap',[['editorChooseImage',['$event']]]]]}}" bindtap="__e"></view></block></view></block></block><block wx:if="{{item.$orig.key=='upload_file'}}"><block><input style="display:none;" type="text" name="{{'form'+idx}}" value="{{editorFormdata[idx]}}"/><view class="flex" style="flex-wrap:wrap;"><block wx:if="{{editorFormdata[idx]}}"><view class="dp-form-imgbox"><view class="dp-form-imgbox-close" data-idx="{{idx}}" data-formidx="{{'form'+idx}}" data-event-opts="{{[['tap',[['removeimg',['$event']]]]]}}" bindtap="__e"><image class="image" src="{{pre_url+'/static/img/ico-del.png'}}"></image></view><view class="dp-form-imgbox-img">已上传</view></view></block><block wx:else><view class="dp-form-uploadbtn" style="{{'background:'+('url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 60rpx')+';'+('background-size:'+('80rpx 80rpx')+';')+('background-color:'+('#F3F3F3')+';')}}" data-idx="{{idx}}" data-formidx="{{'form'+idx}}" data-event-opts="{{[['tap',[['editorChooseFile',['$event']]]]]}}" bindtap="__e"></view></block></view></block></block></view></block></view></block><view style="display:none;">{{test}}</view></block></block><block wx:if="{{xystatus==1}}"><view class="xycss1"><checkbox-group data-event-opts="{{[['change',[['isagreeChange',['$event']]]]]}}" style="display:inline-block;" bindchange="__e"><checkbox style="transform:scale(0.6);" value="1" checked="{{isagree}}"></checkbox><text style="{{('color:'+loginset_data.xytipcolor)}}">{{loginset_data.xytipword}}</text></checkbox-group><text data-event-opts="{{[['tap',[['showxieyiFun',['$event']]]]]}}" style="{{('color:'+loginset_data.xycolor)}}" bindtap="__e">{{xyname}}</text><block wx:if="{{xyname2}}"><text data-event-opts="{{[['tap',[['showxieyiFun',['$event']]]]]}}" style="{{('color:'+loginset_data.xytipcolor)}}" bindtap="__e">和</text></block><block wx:if="{{xyname2}}"><text data-event-opts="{{[['tap',[['showxieyiFun2',['$event']]]]]}}" style="{{(loginset_data.xycolor?'color:'+loginset_data.xycolor:'color:'+$root.m1)}}" bindtap="__e">{{xyname2}}</text></block></view></block><block wx:if="{{loginset_data.btntype==1}}"><block><button class="btn1" style="{{('background:rgba('+$root.m2+');color: '+loginset_data.btnwordcolor)}}" form-type="submit">注册</button></block></block><block wx:if="{{loginset_data.btntype==2}}"><block><button class="btn1" style="{{('background-color:'+loginset_data.btncolor+';color:'+loginset_data.btnwordcolor)}}" form-type="submit">注册</button></block></block></view></form><view class="tologin" style="{{('color: '+loginset_data.regpwdbtncolor)}}" data-url="login" data-opentype="redirect" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">已有账号? 前去登录</view><block wx:if="{{logintype_2||logintype_3}}"><block><view style="display:flex;width:420rpx;margin:60rpx auto;"><view class="other_line"></view><view style="margin:0 20rpx;color:#888888;">其他登录方式</view><view class="other_line"></view></view><view class="othertype"><block wx:if="{{logintype_3}}"><view data-event-opts="{{[['tap',[['weixinlogin',['$event']]]]]}}" class="othertype-item" bindtap="__e"><image class="img" src="{{pre_url+'/static/img/login-'+platformimg+'.png'}}"></image><text class="txt" style="color:#888888;">授权登录</text></view></block><block wx:if="{{logintype_7&&alih5}}"><view data-event-opts="{{[['tap',[['alih5login',['$event']]]]]}}" class="othertype-item" bindtap="__e"><image class="img" src="{{pre_url+'/static/img/login-alipay.png'}}"></image><text class="txt" style="color:#888888;">支付宝登录</text></view></block><block wx:if="{{logintype_2}}"><view class="othertype-item" data-url="login?logintype=2" data-opentype="redirect" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image class="img" src="{{pre_url+'/static/img/reg-tellogin.png'}}"></image><text class="txt" style="color:#888888;">手机号登录</text></view></block></view></block></block></block></block><block wx:if="{{logintype==4}}"><block><view class="authlogin"><view class="logo2"><image style="width:100%;height:100%;" src="{{loginset_data.logo}}" class="_img"></image></view><view style="font-size:30rpx;font-weight:bold;line-height:68rpx;">{{'授权登录'+name}}</view><button class="authlogin-btn" style="{{'background:'+('linear-gradient(90deg,'+$root.m3+' 0%,rgba('+$root.m4+',0.8) 100%)')+';'}}" open-type="getPhoneNumber" data-event-opts="{{[['getphonenumber',[['getPhoneNumber',['$event']]]]]}}" bindgetphonenumber="__e">授权绑定手机号</button><block wx:if="{{login_bind==1}}"><button data-event-opts="{{[['tap',[['nobindregister',['$event']]]]]}}" class="authlogin-btn2" bindtap="__e">暂不绑定</button></block></view></block></block><block wx:if="{{logintype==5}}"><block><form data-event-opts="{{[['submit',[['setnicknameregister',['$event']]]],['reset',[['formReset',['$event']]]]]}}" bindsubmit="__e" bindreset="__e"><view style="font-size:30rpx;font-weight:bold;line-height:68rpx;">请设置头像昵称</view><view class="regform"><view class="form-item" style="height:120rpx;line-height:120rpx;"><view class="flex1">头像</view><button style="width:100rpx;height:100rpx;" open-type="chooseAvatar" data-event-opts="{{[['chooseavatar',[['onChooseAvatar',['$event']]]]]}}" bindchooseavatar="__e"><image style="width:100%;height:100%;border-radius:50%;" src="{{headimg||default_headimg}}"></image></button></view><view class="form-item" style="height:120rpx;line-height:120rpx;"><view class="flex1">昵称</view><input class="input" style="text-align:right;" type="nickname" placeholder="请输入昵称" name="nickname" placeholder-style="font-size:30rpx;color:#B2B5BE"/></view><button class="form-btn" style="{{'background:'+('linear-gradient(90deg,'+$root.m5+' 0%,rgba('+$root.m6+',0.8) 100%)')+';'}}" form-type="submit">确定</button><block wx:if="{{login_setnickname==1}}"><button data-event-opts="{{[['tap',[['nosetnicknameregister',['$event']]]]]}}" class="form-btn2" bindtap="__e">暂不设置</button></block></view></form></block></block><block wx:if="{{logintype==6}}"><block><form data-event-opts="{{[['submit',[['setRegisterInvite',['$event']]]],['reset',[['formReset',['$event']]]]]}}" bindsubmit="__e" bindreset="__e"><block wx:if="{{reg_invite_code&&(parent&&reg_invite_code_show==1||!parent)}}"><view style="font-size:30rpx;font-weight:bold;line-height:68rpx;">请填写邀请码</view></block><view class="loginform" style="padding:0;"><block wx:if="{{reg_invite_code&&!parent}}"><view class="form-item"><input class="input" type="text" name="yqcode" placeholder="{{'请输入邀请人'+reg_invite_code_text}}" placeholder-style="font-size:30rpx;color:#B2B5BE" data-event-opts="{{[['input',[['yqinput',['$event']]]]]}}" value="{{yqcode}}" bindinput="__e"/></view></block><block wx:if="{{reg_invite_code&&parent&&reg_invite_code_show==1}}"><view class="form-item" style="display:flex;padding-top:8rpx;align-items:center;"><view style="white-space:nowrap;">邀请人：</view><image style="width:80rpx;height:80rpx;border-radius:50%;margin-right:20rpx;" src="{{parent.headimg}}"></image><view class="overflow_ellipsis">{{parent.nickname+''}}</view></view></block><block wx:if="{{loginset_data.btntype==1}}"><block><button class="btn1" style="{{('background:rgba('+$root.m7+');color: '+loginset_data.btnwordcolor)}}" form-type="submit">确定</button></block></block><block wx:if="{{loginset_data.btntype==2}}"><block><button class="btn1" style="{{('background-color:'+loginset_data.btncolor+';color:'+loginset_data.btnwordcolor)}}" form-type="submit">确定</button></block></block><block wx:if="{{reg_invite_code==1}}"><button data-event-opts="{{[['tap',[['setRegisterInvitePass',['$event']]]]]}}" class="btn1" style="background-color:#EEEEEE;font-size:28rpx;" bindtap="__e">暂不设置</button></block></view></form></block></block></view></view></view></view></view><block wx:if="{{showxieyi}}"><view class="xieyibox"><view class="xieyibox-content"><view style="overflow:scroll;height:100%;"><parse vue-id="62e0a43b-2" content="{{xycontent}}" data-event-opts="{{[['^navigate',[['navigate']]]]}}" bind:navigate="__e" bind:__l="__l"></parse></view><view class="xieyibut-view flex-y-center"><view data-event-opts="{{[['tap',[['closeXieyi',['$event']]]]]}}" class="but-class" style="background:#A9A9A9;" bindtap="__e">不同意并退出</view><view data-event-opts="{{[['tap',[['hidexieyi',['$event']]]]]}}" class="but-class" style="{{'background:'+('linear-gradient(90deg,'+$root.m8+' 0%,rgba('+$root.m9+',0.8) 100%)')+';'}}" bindtap="__e">已阅读并同意</view></view></view></view></block><block wx:if="{{showxieyi2}}"><view class="xieyibox"><view class="xieyibox-content"><view style="overflow:scroll;height:100%;"><parse vue-id="62e0a43b-3" content="{{xycontent2}}" data-event-opts="{{[['^navigate',[['navigate']]]]}}" bind:navigate="__e" bind:__l="__l"></parse></view><view class="xieyibut-view flex-y-center"><view data-event-opts="{{[['tap',[['closeXieyi',['$event']]]]]}}" class="but-class" style="background:#A9A9A9;" bindtap="__e">不同意并退出</view><view data-event-opts="{{[['tap',[['hidexieyi2',['$event']]]]]}}" class="but-class" style="{{'background:'+('linear-gradient(90deg,'+$root.m10+' 0%,rgba('+$root.m11+',0.8) 100%)')+';'}}" bindtap="__e">已阅读并同意</view></view></view></view></block></block></block><block wx:if="{{loading}}"><loading vue-id="62e0a43b-4" bind:__l="__l"></loading></block><dp-tabbar vue-id="62e0a43b-5" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="62e0a43b-6" data-ref="popmsg" bind:__l="__l"></popmsg><wxxieyi vue-id="62e0a43b-7" bind:__l="__l"></wxxieyi></view>