<view><block wx:if="{{isload}}"><block><view class="container"><view class="header"><block wx:if="{{detail.showname==1}}"><text class="title">{{detail.name}}</text></block><block wx:if="{{detail.showsendtime==1||detail.showauthor==1||detail.showreadcount==1}}"><view class="msginfo"><block wx:if="{{detail.showsendtime==1}}"><text class="t1">{{detail.createtime}}</text></block><block wx:if="{{detail.showauthor==1}}"><text class="t2">{{detail.author}}</text></block><block wx:if="{{detail.showreadcount==1}}"><text class="t3">{{"阅读："+detail.readcount}}</text></block></view></block><view style="padding:8rpx 0;"><dp vue-id="9c311f72-1" pagecontent="{{pagecontent}}" bind:__l="__l"></dp></view></view><block wx:if="{{detail.canpl==1}}"><block><view class="plbox"><view class="plbox_title"><text class="t1">评论</text><text>{{"("+plcount+")"}}</text></view><view class="plbox_content"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="idx" wx:key="id"><block><view class="item1 flex"><view class="f1 flex0"><image src="{{item.$orig.headimg}}"></image></view><view class="f2 flex-col"><text class="t1">{{item.$orig.nickname}}</text><view class="t2 plcontent"><parse vue-id="{{'9c311f72-2-'+idx}}" content="{{item.$orig.content}}" bind:__l="__l"></parse></view><block wx:if="{{item.g0>0}}"><block><view class="relist"><block wx:for="{{item.$orig.replylist}}" wx:for-item="hfitem" wx:for-index="index" wx:key="index"><block><view class="item2"><view>{{hfitem.nickname+"："}}</view><view class="f2 plcontent"><parse vue-id="{{'9c311f72-3-'+idx+'-'+index}}" content="{{hfitem.content}}" bind:__l="__l"></parse></view></view></block></block></view></block></block><view class="t3 flex"><text>{{item.$orig.createtime}}</text><view class="flex1"><block wx:if="{{detail.canplrp==1}}"><text class="phuifu" style="cursor:pointer;" data-url="{{'pinglun?type=1&id='+detail.id+'&hfid='+item.$orig.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">回复</text></block></view><view class="flex-y-center pzan" data-id="{{item.$orig.id}}" data-index="{{idx}}" data-event-opts="{{[['tap',[['pzan',['$event']]]]]}}" bindtap="__e"><image src="{{'/static/img/zan-'+(item.$orig.iszan==1?'2':'1')+'.png'}}"></image>{{item.$orig.zan}}</view></view></view></view></block></block></view><block wx:if="{{loading}}"><loading vue-id="9c311f72-4" bind:__l="__l"></loading></block></view><view style="height:160rpx;"></view><view class="pinglun notabbarbot"><view class="pinput" data-url="{{'pinglun?type=0&id='+detail.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">发表评论</view><view class="zan flex-y-center" data-id="{{detail.id}}" data-event-opts="{{[['tap',[['zan',['$event']]]]]}}" bindtap="__e"><image src="{{'/static/img/zan-'+(iszan?'2':'1')+'.png'}}"></image><text style="padding-left:2px;">{{detail.zan}}</text></view><view data-event-opts="{{[['tap',[['shareMessage',['$event']]]]]}}" class="share flex-y-center" bindtap="__e"><image src="/static/img/share.png"></image><text style="padding-left:2px;">分享</text></view></view></block></block></view></block></block><block wx:if="{{loading}}"><loading vue-id="9c311f72-5" bind:__l="__l"></loading></block><dp-tabbar vue-id="9c311f72-6" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="9c311f72-7" data-ref="popmsg" bind:__l="__l"></popmsg></view>