<view class="container"><block wx:if="{{isload}}"><block><form data-event-opts="{{[['submit',[['topay',['$event']]]]]}}" bindsubmit="__e"><block wx:for="{{$root.l1}}" wx:for-item="buydata" wx:for-index="index" wx:key="index"><view class="buydata"><view class="btitle"><image class="img" src="/static/img/ico-shop.png"></image>{{buydata.$orig.business.name+''}}</view><view class="bcontent"><view class="product"><block wx:for="{{buydata.$orig.prodata}}" wx:for-item="item" wx:for-index="index2" wx:key="index2"><view><view class="item flex"><view class="img" data-url="{{'/shopPackage/shop/product?id='+item.product.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><block wx:if="{{item.guige.pic}}"><image src="{{item.guige.pic}}"></image></block><block wx:else><image src="{{item.product.pic}}"></image></block></view><view class="info flex1"><view class="f1">{{item.product.name}}</view><view class="f2">{{"规格："+item.guige.name}}</view><view class="f3"><block wx:if="{{order_change_price}}"><block><input class="inputPrice" type="number" data-price="{{item.guige.sell_price}}" data-index="{{index}}" data-index2="{{index2}}" data-event-opts="{{[['input',[['inputPrice',['$event']]]]]}}" value="{{item.guige.sell_price}}" bindinput="__e"/></block></block><block wx:else><block><text style="font-weight:bold;">{{"￥"+buydata.$orig.product_price}}</text></block></block><text style="padding-left:20rpx;">{{'× '+item.num}}</text></view></view></view></view></block></view><block wx:if="{{buydata.$orig.freightList[buydata.$orig.freightkey].pstype==1}}"><view class="storeitem"><block wx:for="{{buydata.l0}}" wx:for-item="item" wx:for-index="idx" wx:key="idx"><block><block wx:if="{{idx<5||storeshowall==true}}"><view class="radio-item" data-bid="{{buydata.$orig.bid}}" data-index="{{idx}}" data-event-opts="{{[['tap',[['choosestore',['$event']]]]]}}" catchtap="__e"><view class="f1">{{item.$orig.name+''}}</view><text style="color:#f50;">{{item.$orig.juli}}</text><view class="radio" style="{{(buydata.$orig.freightList[buydata.$orig.freightkey].storekey==idx?'background:'+item.m0+';border:0':'')}}"><image class="radio-img" src="/static/img/checkd.png"></image></view></view></block></block></block><block wx:if="{{buydata.g0}}"><view data-event-opts="{{[['tap',[['doStoreShowAll',['$event']]]]]}}" class="storeviewmore" bindtap="__e">- 查看更多 -</view></block></view></block><block wx:if="{{adminset.iszhang!=2}}"><view class="price"><text class="f1">涨幅比例</text><text class="f2">{{adminset.miaosha_zhangfu+"%"}}</text></view></block><view class="price"><text class="f1">委托价格</text><text class="f2">{{"¥"+weituo_price}}</text></view><view class="price"><text class="f1">手续费比例</text><text class="f2">{{adminset.miaosha_sxf+"%"}}</text></view><view class="price"><text class="f1">利润</text><text class="f2">{{"¥"+miaosha_lirun}}</text></view><view class="price"><text class="f1">手续费</text><text class="f2">{{"¥"+shouxufei}}</text></view><view style="display:none;">{{test}}</view><block wx:for="{{buydata.$orig.freightList[buydata.$orig.freightkey].formdata}}" wx:for-item="item" wx:for-index="idx" wx:key="id"><view class="form-item"><view class="label">{{item.val1}}<block wx:if="{{item.val3==1}}"><text style="color:red;">*</text></block></view><block wx:if="{{item.key=='input'}}"><block><input class="input" type="text" name="{{'form'+buydata.$orig.bid+'_'+idx}}" placeholder="{{item.val2}}" placeholder-style="font-size:28rpx"/></block></block><block wx:if="{{item.key=='textarea'}}"><block><textarea class="textarea" name="{{'form'+buydata.$orig.bid+'_'+idx}}" placeholder="{{item.val2}}" placeholder-style="font-size:28rpx"></textarea></block></block><block wx:if="{{item.key=='radio'}}"><block><radio-group class="radio-group" name="{{'form'+buydata.$orig.bid+'_'+idx}}"><block wx:for="{{item.val2}}" wx:for-item="item1" wx:for-index="idx1" wx:key="id"><label class="flex-y-center"><radio class="radio" value="{{item1}}"></radio>{{item1+''}}</label></block></radio-group></block></block><block wx:if="{{item.key=='checkbox'}}"><block><checkbox-group class="checkbox-group" name="{{'form'+buydata.$orig.bid+'_'+idx}}"><block wx:for="{{item.val2}}" wx:for-item="item1" wx:for-index="idx1" wx:key="id"><label class="flex-y-center"><checkbox class="checkbox" value="{{item1}}"></checkbox>{{item1+''}}</label></block></checkbox-group></block></block><block wx:if="{{item.key=='selector'}}"><block><picker class="picker" mode="selector" name="{{'form'+buydata.$orig.bid+'_'+idx}}" value="" range="{{item.val2}}" data-bid="{{buydata.$orig.bid}}" data-idx="{{idx}}" data-event-opts="{{[['change',[['editorBindPickerChange',['$event']]]]]}}" bindchange="__e"><block wx:if="{{buydata.$orig.editorFormdata[idx]||buydata.$orig.editorFormdata[idx]===0}}"><view>{{''+item.val2[buydata.$orig.editorFormdata[idx]]}}</view></block><block wx:else><view>请选择</view></block></picker><text class="iconfont iconjiantou" style="color:#999;font-weight:normal;"></text></block></block><block wx:if="{{item.key=='time'}}"><block><picker class="picker" mode="time" name="{{'form'+buydata.$orig.bid+'_'+idx}}" value="" start="{{item.val2[0]}}" end="{{item.val2[1]}}" range="{{item.val2}}" data-bid="{{buydata.$orig.bid}}" data-idx="{{idx}}" data-event-opts="{{[['change',[['editorBindPickerChange',['$event']]]]]}}" bindchange="__e"><block wx:if="{{buydata.$orig.editorFormdata[idx]}}"><view>{{buydata.$orig.editorFormdata[idx]}}</view></block><block wx:else><view>请选择</view></block></picker><text class="iconfont iconjiantou" style="color:#999;font-weight:normal;"></text></block></block><block wx:if="{{item.key=='date'}}"><block><picker class="picker" mode="date" name="{{'form'+buydata.$orig.bid+'_'+idx}}" value="" start="{{item.val2[0]}}" end="{{item.val2[1]}}" range="{{item.val2}}" data-bid="{{buydata.$orig.bid}}" data-idx="{{idx}}" data-event-opts="{{[['change',[['editorBindPickerChange',['$event']]]]]}}" bindchange="__e"><block wx:if="{{buydata.$orig.editorFormdata[idx]}}"><view>{{buydata.$orig.editorFormdata[idx]}}</view></block><block wx:else><view>请选择</view></block></picker><text class="iconfont iconjiantou" style="color:#999;font-weight:normal;"></text></block></block><block wx:if="{{item.key=='upload'}}"><block><input style="display:none;" type="text" name="{{'form'+buydata.$orig.bid+'_'+idx}}" value="{{buydata.$orig.editorFormdata[idx]}}"/><view class="flex" style="flex-wrap:wrap;padding-top:20rpx;"><block wx:if="{{buydata.$orig.editorFormdata[idx]}}"><view class="form-imgbox"><view class="form-imgbox-img"><image class="image" src="{{buydata.$orig.editorFormdata[idx]}}" data-url="{{buydata.$orig.editorFormdata[idx]}}" mode="widthFix" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image></view></view></block><view class="form-uploadbtn" style="{{'background:'+('url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 50rpx')+';'+('background-size:'+('80rpx 80rpx')+';')+('background-color:'+('#F3F3F3')+';')}}" data-bid="{{buydata.$orig.bid}}" data-idx="{{idx}}" data-event-opts="{{[['tap',[['editorChooseImage',['$event']]]]]}}" bindtap="__e"></view></view></block></block></view></block></view></view></block><view style="width:100%;height:110rpx;"></view><view class="footer flex notabbarbot"><view class="text1 flex1">总计：<text style="font-weight:bold;font-size:36rpx;">{{"￥"+shouxufei}}</text></view><button class="op" style="{{'background:'+('linear-gradient(-90deg,'+$root.m1+' 0%,rgba('+$root.m2+',0.8) 100%)')+';'}}" form-type="submit" disabled="{{submitDisabled}}">委托上架</button></view></form></block></block><block wx:if="{{loading}}"><loading vue-id="ab4d38de-1" bind:__l="__l"></loading></block><dp-tabbar vue-id="ab4d38de-2" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="ab4d38de-3" data-ref="popmsg" bind:__l="__l"></popmsg></view>