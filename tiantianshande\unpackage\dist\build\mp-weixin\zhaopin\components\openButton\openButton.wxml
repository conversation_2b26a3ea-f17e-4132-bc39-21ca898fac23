<view class="data-v-1feb9c64"><block wx:if="{{openType==='getUserInfo'}}"><block class="data-v-1feb9c64"><block wx:if="{{canIUseGetUserProfile}}"><button class="{{['data-v-1feb9c64',qtsclass+' ptp_exposure']}}" data-ptpid="7b59-17c0-90ab-cfc1" hoverClass="none" data-event-opts="{{[['tap',[['getUserProfile',['$event']]]]]}}" bindtap="__e">{{''+buttonName+''}}</button></block><block wx:else><button class="{{['data-v-1feb9c64',qtsclass+' ptp_exposure button']}}" data-ptpid="{{ptpId||'userInfo'}}" hoverClass="none" openType="getUserInfo" data-event-opts="{{[['getuserinfo',[['queryUserInfo',['$event']]]]]}}" bindgetuserinfo="__e">{{''+buttonName+''}}</button></block></block></block><block wx:else><button class="{{['data-v-1feb9c64',qtsclass+' ptp_exposure button']}}" data-ptpid="{{ptpId||'login'}}" hoverClass="none" openType="getPhoneNumber" data-event-opts="{{[['getphonenumber',[['getPhoneNumber',['$event']]]]]}}" bindgetphonenumber="__e">{{''+buttonName+''}}</button></block></view>