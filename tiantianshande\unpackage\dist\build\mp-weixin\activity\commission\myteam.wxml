<view class="container"><block wx:if="{{isload}}"><block><block wx:if="{{userlevel&&userlevel.can_agent==2}}"><dd-tab vue-id="39f2da2b-1" itemdata="{{[$root.m0,$root.m1]}}" itemst="{{['1','2','3']}}" st="{{st}}" isfixed="{{false}}" data-event-opts="{{[['^changetab',[['changetab']]]]}}" bind:changetab="__e" bind:__l="__l"></dd-tab></block><block wx:if="{{userlevel&&userlevel.can_agent==3}}"><dd-tab vue-id="39f2da2b-2" itemdata="{{[$root.m2,$root.m3,$root.m4]}}" itemst="{{['1','2','3']}}" st="{{st}}" isfixed="{{false}}" data-event-opts="{{[['^changetab',[['changetab']]]]}}" bind:changetab="__e" bind:__l="__l"></dd-tab></block><block wx:if="{{team_settings&&team_settings.show_statistics==1}}"><view class="time-filter"><view class="filter-item"><text class="filter-label">开始：</text><picker mode="date" value="{{start_time}}" data-event-opts="{{[['change',[['onStartDateChange',['$event']]]]]}}" bindchange="__e"><view class="date-picker">{{start_time||'请选择'}}</view></picker></view><view class="filter-item"><text class="filter-label">结束：</text><picker mode="date" value="{{end_time}}" data-event-opts="{{[['change',[['onEndDateChange',['$event']]]]]}}" bindchange="__e"><view class="date-picker">{{end_time||'请选择'}}</view></picker></view><view data-event-opts="{{[['tap',[['applyTimeFilter',['$event']]]]]}}" class="filter-btn" style="{{'background:'+($root.m5)+';'+('color:'+('#fff')+';')}}" bindtap="__e">筛选</view><view data-event-opts="{{[['tap',[['resetTimeFilter',['$event']]]]]}}" class="filter-btn reset-btn" bindtap="__e">重置</view></view></block><block wx:if="{{team_settings&&team_settings.show_statistics==1&&statistics}}"><view class="statistics-bar" style="{{'background:'+('linear-gradient(45deg,'+$root.m6+' 0%,rgba('+$root.m7+',0.8) 100%)')+';'}}"><view class="stat-item"><text class="stat-value">{{statistics.total_count||0}}</text><text class="stat-label">总人数</text></view><view class="stat-item"><text class="stat-value">{{"¥"+(statistics.total_amount||'0.00')}}</text><text class="stat-label">总下单金额</text></view></view></block><view class="topsearch flex-y-center"><view class="f1 flex-y-center"><image class="img" src="/static/img/search_ico.png"></image><input placeholder="输入昵称/姓名/手机号搜索" placeholder-style="font-size:24rpx;color:#C2C2C2" data-event-opts="{{[['confirm',[['searchConfirm',['$event']]]],['input',[['searchChange',['$event']]]]]}}" value="{{keyword}}" bindconfirm="__e" bindinput="__e"/></view></view><block wx:if="{{$root.g0}}"><view class="content"><view class="label"><text class="t1">成员信息</text><block wx:if="{{team_settings&&team_settings.show_other_commission==1}}"><text class="t2">{{"来自TA的"+$root.m8}}</text></block></view><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><view class="item"><view class="f1"><image src="{{item.$orig.headimg}}"></image><view class="t2"><text class="x1">{{item.$orig.nickname}}</text><text class="x2">{{item.$orig.createtime}}</text><text class="x2">{{"等级："+item.$orig.levelname}}</text><block wx:if="{{item.$orig.tel}}"><text class="x2">{{"手机号："+item.$orig.tel}}</text></block><block wx:if="{{item.$orig.has_original_recommender==1&&item.$orig.original_recommender}}"><text class="x2">{{"原推荐人："+item.$orig.original_recommender.nickname}}</text></block><block wx:if="{{team_settings&&team_settings.show_member_count==1}}"><text class="x2">{{"下级人数："+item.$orig.downcount}}</text></block><block wx:if="{{team_settings&&team_settings.show_direct_count==1}}"><text class="x2">{{"直推人数："+(item.$orig.direct_count||item.$orig.downcount)}}</text></block><block wx:if="{{team_settings&&team_settings.show_team_performance==1}}"><text class="x2" style="{{'color:'+(item.m9)+';'}}">{{"团队业绩："+item.$orig.tuanduiyeji}}</text></block><block wx:if="{{team_settings&&team_settings.show_consume_amount==1}}"><text class="x2" style="{{'color:'+(item.m10)+';'}}">{{"消费金额：¥"+(item.$orig.consume_amount||'0.00')}}</text></block></view></view><block wx:if="{{team_settings&&team_settings.show_relation_chart==1||userlevel&&(userlevel.team_givemoney==1||userlevel.team_givescore==1||userlevel.team_levelup==1||userlevel.team_daikexiadan==1)}}"><view class="operation-area"><block wx:if="{{team_settings&&team_settings.show_relation_chart==1}}"><view class="op-btn" style="{{'background:'+('linear-gradient(90deg,'+item.m11+' 0%,rgba('+item.m12+',0.8) 100%)')+';'+('color:'+('#fff')+';')}}" data-id="{{item.$orig.id}}" data-event-opts="{{[['tap',[['goToRelationChart',['$event']]]]]}}" bindtap="__e">关系图</view></block></view></block><block wx:if="{{team_settings&&team_settings.show_other_commission==1}}"><view class="f2"><text class="t1" style="{{'color:'+(item.m13)+';'}}">{{"+"+item.$orig.commission}}</text><view class="t3"><block wx:if="{{userlevel&&userlevel.team_givemoney==1}}"><view class="x1" style="{{'border-color:'+(item.m14)+';'+('color:'+(item.m15)+';')}}" data-id="{{item.$orig.id}}" data-event-opts="{{[['tap',[['givemoneyshow',['$event']]]]]}}" bindtap="__e">{{"转"+item.m16}}</view></block><block wx:if="{{userlevel&&userlevel.team_givescore==1}}"><view class="x1" style="{{'border-color:'+(item.m17)+';'+('color:'+(item.m18)+';')}}" data-id="{{item.$orig.id}}" data-event-opts="{{[['tap',[['givescoreshow',['$event']]]]]}}" bindtap="__e">{{"转"+item.m19}}</view></block><block wx:if="{{userlevel&&userlevel.team_levelup==1}}"><view class="x1" style="{{'border-color:'+(item.m20)+';'+('color:'+(item.m21)+';')}}" data-id="{{item.$orig.id}}" data-levelid="{{item.$orig.levelid}}" data-levelsort="{{item.$orig.levelsort}}" data-event-opts="{{[['tap',[['showDialog',['$event']]]]]}}" bindtap="__e">升级</view></block><block wx:if="{{userlevel&&userlevel.team_daikexiadan==1}}"><view class="x1" style="{{'border-color:'+(item.m22)+';'+('color:'+(item.m23)+';')}}" data-id="{{item.$orig.id}}" data-event-opts="{{[['tap',[['goDaigoupick',['$event']]]]]}}" bindtap="__e">帮他下单</view></block></view></view></block></view></block></block></view></block><uni-popup class="vue-ref" vue-id="39f2da2b-3" id="dialogmoneyInput" type="dialog" data-ref="dialogmoneyInput" bind:__l="__l" vue-slots="{{['default']}}"><uni-popup-dialog vue-id="{{('39f2da2b-4')+','+('39f2da2b-3')}}" mode="input" title="转账金额" value="" placeholder="请输入转账金额" data-event-opts="{{[['^confirm',[['givemoney']]]]}}" bind:confirm="__e" bind:__l="__l"></uni-popup-dialog></uni-popup><uni-popup class="vue-ref" vue-id="39f2da2b-5" id="dialogscoreInput" type="dialog" data-ref="dialogscoreInput" bind:__l="__l" vue-slots="{{['default']}}"><uni-popup-dialog vue-id="{{('39f2da2b-6')+','+('39f2da2b-5')}}" mode="input" title="转账数量" value="" placeholder="请输入转账数量" data-event-opts="{{[['^confirm',[['givescore']]]]}}" bind:confirm="__e" bind:__l="__l"></uni-popup-dialog></uni-popup><block wx:if="{{dialogShow}}"><view class="popup__container"><view data-event-opts="{{[['tap',[['showDialog',['$event']]]]]}}" class="popup__overlay" catchtap="__e"></view><view class="popup__modal"><view class="popup__title"><text class="popup__title-text">升级</text><image class="popup__close" style="width:36rpx;height:36rpx;" src="/static/img/close.png" data-event-opts="{{[['tap',[['showDialog',['$event']]]]]}}" catchtap="__e"></image></view><view class="popup__content"><block wx:for="{{$root.l1}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="sheet-item"><text class="item-text flex-item">{{item.$orig.name}}</text><view class="flex1"></view><block wx:if="{{item.$orig.id!=tempLevelid&&item.$orig.sort>tempLevelsort}}"><view style="{{'color:'+(item.m24)+';'}}" data-id="{{item.$orig.id}}" data-name="{{item.$orig.name}}" data-event-opts="{{[['tap',[['changeLevel',['$event']]]]]}}" bindtap="__e">选择</view></block><block wx:else><view style="color:#ccc;">选择</view></block></view></block></view></view></view></block></block></block><block wx:if="{{nodata}}"><nodata vue-id="39f2da2b-7" bind:__l="__l"></nodata></block><block wx:if="{{nomore}}"><nomore vue-id="39f2da2b-8" bind:__l="__l"></nomore></block><block wx:if="{{loading}}"><loading vue-id="39f2da2b-9" bind:__l="__l"></loading></block><dp-tabbar vue-id="39f2da2b-10" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="39f2da2b-11" data-ref="popmsg" bind:__l="__l"></popmsg></view>