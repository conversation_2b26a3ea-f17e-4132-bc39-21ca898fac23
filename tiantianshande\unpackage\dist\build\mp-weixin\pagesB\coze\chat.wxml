<view class="container"><block wx:if="{{isload}}"><block><view class="header"><view class="header-title">扣子AI聊天</view><view class="header-actions"><view data-event-opts="{{[['tap',[['showBotList',['$event']]]]]}}" class="action-btn" bindtap="__e"><text class="iconfont iconjiqiren"></text><text>选择机器人</text></view></view></view><block wx:if="{{currentBot.bot_id}}"><view class="bot-info"><image class="bot-avatar" src="{{currentBot.icon_url||'/static/img/default-bot.png'}}"></image><view class="bot-details"><view class="bot-name">{{currentBot.name}}</view><view class="bot-desc">{{currentBot.description}}</view></view></view></block><scroll-view class="chat-messages" scroll-y="true" scroll-top="{{scrollTop}}" scroll-with-animation="true"><view class="message-list"><block wx:for="{{messageList}}" wx:for-item="message" wx:for-index="index" wx:key="index"><block><view class="{{['message-item',message.role==='user'?'user-message':'bot-message']}}"><view class="message-avatar"><block wx:if="{{message.role==='user'}}"><image src="{{userInfo.avatar||'/static/img/default-user.png'}}"></image></block><block wx:else><image src="{{currentBot.icon_url||'/static/img/default-bot.png'}}"></image></block></view><view class="message-content"><view class="message-text">{{message.content}}</view><view class="message-time">{{message.create_time_text}}</view></view></view></block></block><block wx:if="{{isTyping}}"><view class="message-item bot-message"><view class="message-avatar"><image src="{{currentBot.icon_url||'/static/img/default-bot.png'}}"></image></view><view class="message-content"><view class="typing-indicator"><view class="typing-dot"></view><view class="typing-dot"></view><view class="typing-dot"></view></view></view></view></block></view></scroll-view><view class="input-area"><view class="input-container"><view class="input-box"><input placeholder="请输入消息..." placeholder-style="color:#999" confirm-type="send" disabled="{{isTyping}}" data-event-opts="{{[['confirm',[['sendMessage',['$event']]]],['input',[['__set_model',['','inputMessage','$event',[]]]]]]}}" value="{{inputMessage}}" bindconfirm="__e" bindinput="__e"/></view><view data-event-opts="{{[['tap',[['sendMessage',['$event']]]]]}}" class="{{['send-btn',$root.g0?'active':'']}}" bindtap="__e"><text class="iconfont iconfasong"></text></view></view></view><uni-popup class="vue-ref" vue-id="3761dece-1" type="bottom" data-ref="botListPopup" bind:__l="__l" vue-slots="{{['default']}}"><view class="bot-list-popup"><view class="popup-header"><view class="popup-title">选择机器人</view><view data-event-opts="{{[['tap',[['closeBotList',['$event']]]]]}}" class="popup-close" bindtap="__e"><text class="iconfont iconguanbi"></text></view></view><scroll-view class="bot-list" scroll-y="true"><block wx:for="{{$root.l0}}" wx:for-item="bot" wx:for-index="index" wx:key="index"><block><view class="bot-item" data-bot="{{bot.g1}}" data-event-opts="{{[['tap',[['selectBot',['$event']]]]]}}" bindtap="__e"><image class="bot-item-avatar" src="{{bot.$orig.icon_url||'/static/img/default-bot.png'}}"></image><view class="bot-item-info"><view class="bot-item-name">{{bot.$orig.name}}</view><view class="bot-item-desc">{{bot.$orig.description}}</view></view><block wx:if="{{currentBot.bot_id===bot.$orig.bot_id}}"><view class="bot-item-check"><text class="iconfont iconduihao" style="{{'color:'+(bot.m0)+';'}}"></text></view></block></view></block></block></scroll-view></view></uni-popup></block></block><block wx:if="{{loading}}"><loading vue-id="3761dece-2" bind:__l="__l"></loading></block></view>