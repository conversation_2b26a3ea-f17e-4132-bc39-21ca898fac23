<view class="container"><block wx:if="{{isload}}"><block><view class="orderinfo"><view class="item"><text class="t1">下单人</text><text class="flex1"></text><image style="width:80rpx;height:80rpx;margin-right:8rpx;" src="{{member.headimg}}"></image><text style="height:80rpx;line-height:80rpx;">{{member.nickname}}</text></view><view class="item"><text class="t1">{{$root.m0+"ID"}}</text><text class="t2">{{detail.mid}}</text></view></view><view class="orderinfo"><view class="item"><text class="t1">订单编号</text><text class="t2">{{detail.ordernum}}</text></view><view class="item"><text class="t1">支付单号</text><text class="t2">{{detail.paynum}}</text></view><block wx:if="{{mendian}}"><view class="item"><text class="t1">付款门店</text><text class="t2">{{mendian.name}}</text></view></block><view class="item"><text class="t1">付款金额</text><text class="t2">{{"￥"+detail.money}}</text></view><view class="item"><text class="t1">实付金额</text><text class="t2" style="font-size:32rpx;color:#e94745;">{{"￥"+detail.paymoney}}</text></view><view class="item"><text class="t1">付款方式</text><text class="t2">{{detail.paytype}}</text></view><view class="item"><text class="t1">状态</text><block wx:if="{{detail.status==1}}"><text class="t2" style="color:green;">已付款</text></block><block wx:else><text class="t2" style="color:red;">未付款</text></block></view><block wx:if="{{detail.status>0&&detail.paytime}}"><view class="item"><text class="t1">付款时间</text><text class="t2">{{detail.paytime}}</text></view></block><block wx:if="{{detail.disprice>0}}"><view class="item"><text class="t1">{{$root.m1+"折扣"}}</text><text class="t2">{{"-￥"+detail.disprice}}</text></view></block><block wx:if="{{detail.scoredk>0}}"><view class="item"><text class="t1">{{$root.m2+"抵扣"}}</text><text class="t2">{{"-￥"+detail.scoredk}}</text></view></block><block wx:if="{{detail.couponrid}}"><view class="item"><text class="t1">{{$root.m3+"抵扣"}}</text><text class="t2">{{"-￥"+detail.couponmoney}}</text></view></block><block wx:if="{{couponrecord}}"><view class="item"><text class="t1">{{$root.m4+"名称"}}</text><text class="t2">{{couponrecord.couponname}}</text></view></block><view class="item"><text class="t1">订单备注</text><text class="t2">{{detail.remark}}</text></view></view><block wx:if="{{canrefund}}"><view class="orderinfo"><view class="item"><text class="t1">已退款金额</text><text class="t2">{{"￥"+detail.refund_money}}</text></view><view class="item"><text class="t1">剩余可退</text><text class="t2">{{"￥"+detail.can_refund_money}}</text></view><block wx:if="{{detail.can_refund_money>0}}"><view class="item option"><view data-event-opts="{{[['tap',[['toggleRefund',['$event']]]]]}}" class="btn" style="{{'background:'+($root.m5)+';'}}" bindtap="__e">退 款</view></view></block></view></block><block wx:if="{{isshowrefund}}"><view class="popup__container popup__refund"><view data-event-opts="{{[['tap',[['toggleRefund',['$event']]]]]}}" class="popup__overlay" catchtap="__e"></view><view class="popup__modal"><view class="popup__title"><text class="popup__title-text">备注信息</text><image class="popup__close" style="width:36rpx;height:36rpx;" src="{{pre_url+'/static/img/close.png'}}" data-event-opts="{{[['tap',[['toggleRefund',['$event']]]]]}}" catchtap="__e"></image></view><view class="popup__content"><view class="form-item"><view class="label">退款金额<text class="tips">{{"(可退金额:￥"+detail.can_refund_money+")"}}</text></view><view class="input flex-input"><input type="digit" placeholder-style="font-size:26rpx;color:#999" placeholder="请填写备注信息" data-event-opts="{{[['input',[['__set_model',['','money','$event',[]]]]]]}}" value="{{money}}" bindinput="__e"/><text data-event-opts="{{[['tap',[['allmoney',['$event']]]]]}}" class="alltxt" style="{{'color:'+($root.m6)+';'}}" bindtap="__e">全部</text></view></view><view class="form-item"><text class="label">退款备注</text><textarea class="textarea" placeholder-style="font-size:26rpx;color:#999" placeholder="请填写备注信息" data-event-opts="{{[['input',[['__set_model',['','remark','$event',[]]]]]]}}" value="{{remark}}" bindinput="__e"></textarea></view></view><view class="popup__bottom"><button data-event-opts="{{[['tap',[['refundConfirm',['$event']]]]]}}" class="refund-confirm" style="{{'background:'+($root.m7)+';'}}" bindtap="__e">确 定</button></view></view></view></block></block></block><block wx:if="{{loading}}"><loading vue-id="623d664e-1" bind:__l="__l"></loading></block><dp-tabbar vue-id="623d664e-2" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="623d664e-3" data-ref="popmsg" bind:__l="__l"></popmsg></view>