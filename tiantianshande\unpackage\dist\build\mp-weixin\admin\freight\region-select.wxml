<view class="container"><view class="search-box"><input type="text" placeholder="搜索地区" data-event-opts="{{[['input',[['__set_model',['','searchKey','$event',[]]]]]]}}" value="{{searchKey}}" bindinput="__e"/></view><block wx:if="{{$root.g0>0}}"><view class="selected-box"><view class="selected-title">{{"已选择 "+$root.g1}}</view><view class="selected-list"><block wx:for="{{selectedRegions}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="selected-item"><text>{{item.name}}</text><text data-event-opts="{{[['tap',[['removeRegion',['$0'],[[['selectedRegions','',index]]]]]]]}}" class="delete-icon" bindtap="__e">×</text></view></block></view></view></block><view class="region-tabs"><block wx:for="{{tabs}}" wx:for-item="tab" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['switchTab',[index]]]]]}}" class="{{['tab',(currentTab===index)?'active':'']}}" bindtap="__e"><text>{{tab}}</text></view></block></view><scroll-view class="region-list" scroll-y="{{true}}"><block wx:if="{{currentTab===0}}"><block><block wx:for="{{$root.l0}}" wx:for-item="province" wx:for-index="__i0__" wx:key="id"><view data-event-opts="{{[['tap',[['selectProvince',['$0'],[[['filteredProvinces','id',province.$orig.id]]]]]]]}}" class="region-item" bindtap="__e"><view class="flex-row"><text>{{province.$orig.name}}</text><text data-event-opts="{{[['tap',[['selectAllInProvince',['$0'],[[['filteredProvinces','id',province.$orig.id]]]]]]]}}" class="select-all" catchtap="__e">全选</text></view><block wx:if="{{province.m0}}"><text class="check-icon">✓</text></block></view></block></block></block><block wx:if="{{currentTab===1}}"><block><block wx:for="{{$root.l1}}" wx:for-item="city" wx:for-index="__i1__" wx:key="id"><view data-event-opts="{{[['tap',[['selectCity',['$0'],[[['filteredCities','id',city.$orig.id]]]]]]]}}" class="region-item" bindtap="__e"><text>{{city.$orig.name}}</text><block wx:if="{{city.m1}}"><text class="check-icon">✓</text></block></view></block></block></block></scroll-view><view class="bottom-btns"><button data-event-opts="{{[['tap',[['cancel',['$event']]]]]}}" class="btn cancel" bindtap="__e">取消</button><button data-event-opts="{{[['tap',[['confirm',['$event']]]]]}}" class="btn confirm" bindtap="__e">确定</button></view></view>