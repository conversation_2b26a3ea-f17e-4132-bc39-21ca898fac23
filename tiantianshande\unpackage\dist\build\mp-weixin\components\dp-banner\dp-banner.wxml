<view class="dp-banner" style="{{'background-color:'+(params.bgcolor)+';'+('margin-top:'+(shouldShowTop?'-180rpx':params.margin_y*2.2+'rpx')+';')+('margin-right:'+(params.margin_x*2.2+'rpx')+';')+('margin-bottom:'+('0')+';')+('margin-left:'+(params.margin_x*2.2+'rpx')+';')+('padding:'+(params.padding_y*2.2+'rpx '+params.padding_x*2.2+'rpx')+';')+('height:'+(bannerHeight)+';')+('width:'+('calc(100% - '+params.margin_x*2.2*2+'rpx)')+';')}}"><block wx:if="{{params.bgimg}}"><view class="{{['banner-background',(shouldShowTop)?'is-top':'']}}"><image class="bg-image" src="{{params.bgimg}}" mode="aspectFill"></image></view></block><view class="{{['banner-content',(shouldShowTop)?'is-top':'']}}"><block wx:if="{{params.main_title||params.sub_title}}"><view class="banner-header"><view class="title-box"><block wx:if="{{params.main_title}}"><text class="main-title" style="{{'color:'+(params.main_title_color||'#fff')+';'}}">{{params.main_title}}</text></block><block wx:if="{{params.sub_title}}"><text class="sub-title" style="{{'color:'+(params.sub_title_color||'rgba(255, 255, 255, 0.8)')+';'}}">{{params.sub_title}}</text></block></view><block wx:if="{{params.more_text&&params.hrefurl}}"><view class="more-link" style="{{'color:'+(params.more_text_color||'rgba(255, 255, 255, 0.9)')+';'}}" data-url="{{params.hrefurl}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">{{''+params.more_text+''}}<text class="arrow">></text></view></block></view></block><block wx:if="{{params.style&&params.style==1}}"><block><view class="sbox"><view style="{{'height:'+('40rpx')+';'}}"></view><view class="bgswiper" style="{{'height:'+(params.height*1.2+'rpx')+';'+('background:'+('url('+data[bannerindex].imgurl+')')+';')}}"></view><swiper style="{{'height:'+(params.height*2.2+'rpx')+';'+('padding-top:'+('20rpx')+';')}}" autoplay="{{false}}" circular="{{true}}" displayMultipleItems="1" indicatorActiveColor="white" indicatorDots="{{false}}" nextMargin="80rpx" snapToEdge="true" data-event-opts="{{[['change',[['bannerchange',['$event']]]]]}}" bindchange="__e"><block wx:for="{{data}}" wx:for-item="item" wx:for-index="index" wx:key="id"><swiper-item class="switem" style="{{'height:'+(params.height*2.2+'rpx')+';'+('padding-top:'+('20rpx')+';')}}" data-url="{{item.hrefurl}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image class="{{['sitem',bannerindex==index?'active':'noactive']}}" style="{{'height:'+(params.height*2.2-20+'rpx')+';'}}" mode="scaleToFill" src="{{item.imgurl}}"></image></swiper-item></block></swiper></view></block></block><block wx:else><block><swiper class="dp-banner-swiper" style="{{'height:'+(params.height*2.2+30+'rpx')+';'}}" autoplay="{{true}}" indicator-dots="{{false}}" current="{{0}}" interval="{{params.interval*1000}}" data-event-opts="{{[['change',[['bannerchange',['$event']]]]]}}" bindchange="__e"><block wx:for="{{data}}" wx:for-item="item" wx:for-index="__i0__" wx:key="id"><block><swiper-item><view style="{{'height:'+(params.height*2.2+'rpx')+';'+('border-radius:'+(params.borderradius+'px')+';')+('overflow:'+('hidden')+';')}}" data-url="{{item.hrefurl}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image class="dp-banner-swiper-img" src="{{item.imgurl}}" mode="widthFix"></image></view></swiper-item></block></block></swiper></block></block><block wx:if="{{params.indicatordots=='1'}}"><view class="dp-banner-swiper-pagination" style="{{'justify-content:'+(params.align=='center'?'center':params.align=='left'?'flex-start':'flex-end')+';'+('bottom:'+(params.indicatorBottom||'12px')+';')}}"><block wx:for="{{data}}" wx:for-item="item" wx:for-index="index" wx:key="id"><block><block wx:if="{{params.shape==''}}"><block><block wx:if="{{bannerindex==index}}"><view class="dp-banner-swiper-shape0 dp-banner-swiper-shape0-active" style="{{'background-color:'+(params.indicatoractivecolor)+';'}}"></view></block><block wx:else><view class="dp-banner-swiper-shape0" style="{{'background-color:'+(params.indicatorcolor)+';'}}"></view></block></block></block><block wx:else><block wx:if="{{params.shape=='shape1'}}"><block><block wx:if="{{bannerindex==index}}"><view class="dp-banner-swiper-shape1" style="{{'background-color:'+(params.indicatoractivecolor)+';'}}"></view></block><block wx:else><view class="dp-banner-swiper-shape1" style="{{'background-color:'+(params.indicatorcolor)+';'}}"></view></block></block></block><block wx:else><block wx:if="{{params.shape=='shape2'}}"><block><block wx:if="{{bannerindex==index}}"><view class="dp-banner-swiper-shape2" style="{{'background-color:'+(params.indicatoractivecolor)+';'}}"></view></block><block wx:else><view class="dp-banner-swiper-shape2" style="{{'background-color:'+(params.indicatorcolor)+';'}}"></view></block></block></block><block wx:else><block wx:if="{{params.shape=='shape3'}}"><block><block wx:if="{{bannerindex==index}}"><view class="dp-banner-swiper-shape3" style="{{'background-color:'+(params.indicatoractivecolor)+';'}}"></view></block><block wx:else><view class="dp-banner-swiper-shape3" style="{{'background-color:'+(params.indicatorcolor)+';'}}"></view></block></block></block></block></block></block></block></block></view></block></view></view>