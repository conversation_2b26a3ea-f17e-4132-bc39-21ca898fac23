<view class="container"><block wx:if="{{isload}}"><block><view class="view-show"><view class="topbannerbg" style="{{(business.pic?'background:url('+business.pic+') 100%':'')}}"></view><view class="topbannerbg2"></view><view class="topbanner"><view class="left"><image class="img" src="{{business.logo}}"></image></view><view class="right"><view class="f1">{{business.name}}</view><view class="f2">{{business.desc}}</view><block wx:if="{{business.address}}"><view class="f2" style="opacity:0.9;" data-latitude="{{business.latitude}}" data-longitude="{{business.longitude}}" data-company="{{business.name}}" data-address="{{business.address}}" data-event-opts="{{[['tap',[['openLocation',['$event']]]]]}}" bindtap="__e"><text class="iconfont icondingwei"></text>{{business.address}}</view></block></view></view><view class="navtab"><block wx:if="{{$root.g0}}"><block><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><view class="{{['item '+(st==item.$orig.st?'on':'')]}}" data-st="{{item.$orig.st}}" data-event-opts="{{[['tap',[['changetab',['$event']]]]]}}" bindtap="__e">{{item.$orig.alias?item.$orig.alias:item.$orig.name}}<view class="after" style="{{'background:'+(item.m0)+';'}}"></view></view></block></block></block></block><block wx:else><block><view class="{{['item',st==0?'on':'']}}" data-st="0" data-event-opts="{{[['tap',[['changetab',['$event']]]]]}}" bindtap="__e">商品<view class="after" style="{{'background:'+($root.m1)+';'}}"></view></view><view class="{{['item',st==1?'on':'']}}" data-st="1" data-event-opts="{{[['tap',[['changetab',['$event']]]]]}}" bindtap="__e">商家信息<view class="after" style="{{'background:'+($root.m2)+';'}}"></view></view><block wx:if="{{$root.g1}}"><view class="{{['item',st==2?'on':'']}}" data-st="2" data-event-opts="{{[['tap',[['changetab',['$event']]]]]}}" bindtap="__e">评价<view class="after" style="{{'background:'+($root.m3)+';'}}"></view></view></block></block></block></view><block wx:if="{{st==0}}"><view class="content" style="{{'overflow:hidden;display:flex;margin-top:86rpx;'+('height:'+('calc(100% - '+(menuindex>-1?550:450)+'rpx)')+';')}}"><scroll-view class="nav_left" scrollWithAnimation="{{animation}}" scroll-y="true"><block wx:for="{{$root.l1}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><view class="{{['nav_left_items',index===currentActiveIndex?'active':'']}}" style="{{'color:'+(index===currentActiveIndex?item.m4:'#333')+';'}}" data-root-item-id="{{item.$orig.id}}" data-root-item-index="{{index}}" data-event-opts="{{[['tap',[['clickRootItem',['$event']]]]]}}" bindtap="__e"><view class="before" style="{{'background:'+(item.m5)+';'}}"></view>{{item.$orig.name}}</view></block></block></scroll-view><view class="nav_right"><view class="nav_right-content"><scroll-view class="detail-list" scrollIntoView="{{scrollToViewId}}" scrollWithAnimation="{{animation}}" scroll-y="true" data-event-opts="{{[['scroll',[['scroll',['$event']]]]]}}" bindscroll="__e"><block wx:for="{{$root.l3}}" wx:for-item="detail" wx:for-index="index" wx:key="index"><view class="classification-detail-item"><view class="head" data-id="{{detail.$orig.id}}" id="{{'detail-'+detail.$orig.id}}"><view class="txt">{{detail.$orig.name}}</view></view><view class="product-itemlist"><block wx:for="{{detail.l2}}" wx:for-item="item" wx:for-index="index" wx:key="id"><view class="item"><view class="product-pic" data-url="{{'product?id='+item.$orig.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image class="image" src="{{item.$orig.pic}}" mode="widthFix"></image></view><view class="product-info"><view class="p1"><text>{{item.$orig.name}}</text></view><block wx:if="{{item.$orig.price_type!=1||item.$orig.sell_price>0}}"><view class="p2"><text class="t1" style="{{'color:'+(item.m6)+';'}}"><text style="font-size:20rpx;padding-right:1px;">￥</text>{{item.$orig.sell_price}}</text><block wx:if="{{item.$orig.market_price*1>item.$orig.sell_price*1}}"><text class="t2">{{"￥"+item.$orig.market_price}}</text></block></view></block><view class="p3"><block wx:if="{{item.$orig.sales>0}}"><view class="p3-1"><text style="overflow:hidden;">{{"已售"+item.$orig.sales+"件"}}</text></view></block></view><block wx:if="{{!item.$orig.price_type}}"><view class="addnum"><block wx:if="{{numtotal[item.$orig.id]>0}}"><view class="minus" data-num="-1" data-proid="{{item.$orig.id}}" data-stock="{{item.$orig.stock}}" data-event-opts="{{[['tap',[['addcart',['$event']]]]]}}" catchtap="__e">-</view></block><block wx:if="{{numtotal[item.$orig.id]>0}}"><text class="i">{{numtotal[item.$orig.id]}}</text></block><block wx:if="{{item.$orig.ggcount>1}}"><view class="plus" data-proid="{{item.$orig.id}}" data-stock="{{item.$orig.stock}}" data-event-opts="{{[['tap',[['buydialogChange',['$event']]]]]}}" catchtap="__e">+</view></block><block wx:else><view class="plus" data-num="1" data-proid="{{item.$orig.id}}" data-ggid="{{item.$orig.gglist[0].id}}" data-stock="{{item.$orig.stock}}" data-event-opts="{{[['tap',[['addcart',['$event']]]]]}}" catchtap="__e">+</view></block></view></block></view></view></block></view></view></block></scroll-view></view></view></view></block><block wx:if="{{st==1}}"><view class="content1" style="margin-top:86rpx;padding-top:20rpx;"><view class="item flex-col"><text class="t1">联系电话</text><text class="t2">{{business.tel}}</text></view><view class="item flex-col"><text class="t1">商家地址</text><text class="t2">{{business.address}}</text></view><view class="item flex-col"><text class="t1">商家简介</text><text class="t2">{{business.content}}</text></view><block wx:if="{{bid!=0}}"><view class="item flex-col"><text class="t1">营业时间</text><text class="t2">{{business.start_hours+" 至 "+business.end_hours}}</text></view></block></view></block><block wx:if="{{st==2}}"><view class="content2" style="margin-top:86rpx;padding-top:20rpx;"><view class="comment" style="height:calc(100vh - 460rpx);overflow:scroll;"><block wx:if="{{$root.g2>0}}"><block><view class="title"><view class="f1">{{"评价("+business.comment_num+")"}}</view><view data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" class="f2" bindtap="__e">好评率<text style="{{'color:'+($root.m7)+';'}}">{{business.comment_haopercent+"%"}}</text></view></view><block wx:for="{{commentlist}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="item"><view class="f1"><image class="t1" src="{{item.headimg}}"></image><view class="t2">{{item.nickname}}</view><view class="flex1"></view><view class="t3"><block wx:for="{{[0,1,2,3,4]}}" wx:for-item="item2" wx:for-index="index2" wx:key="index2"><image class="img" src="{{'/static/img/star'+(item.score>item2?'2':'')+'.png'}}"></image></block></view></view><view style="color:#777;font-size:22rpx;">{{item.createtime}}</view><view class="f2"><text class="t1">{{item.content}}</text><view class="t2"><block wx:if="{{item.content_pic!=''}}"><block><block wx:for="{{item.content_pic}}" wx:for-item="itemp" wx:for-index="index" wx:key="index"><block><view data-url="{{itemp}}" data-urls="{{item.content_pic}}" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"><image src="{{itemp}}" mode="widthFix"></image></view></block></block></block></block></view></view><block wx:if="{{item.reply_content}}"><view class="f3"><view class="arrow"></view><view class="t1">{{"商家回复："+item.reply_content}}</view></view></block></view></block><view style="width:100%;height:120rpx;"></view></block></block><block wx:else><block><nodata data-custom-hidden="{{!(comment_nodata)}}" vue-id="279ecd7b-1" bind:__l="__l"></nodata></block></block></view></view></block><block wx:if="{{st==3}}"><view class="content2" style="margin-top:86rpx;padding-top:20rpx;"><view class="paylist" style="height:calc(100vh - 460rpx);overflow:scroll;"><block wx:for="{{$root.l4}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="item" data-id="{{item.$orig.id}}" data-event-opts="{{[['tap',[['createpayorder',['$event']]]]]}}" bindtap="__e"><view class="f1"><image class="image" src="{{item.$orig.pic}}" mode="widthFix"></image></view><view class="f2"><view class="t1"><text>{{item.$orig.name}}</text></view><view class="t2" style="{{'color:'+(item.m8)+';'}}">{{"￥"+item.$orig.market_price}}<text style="padding:0 4rpx;">/</text><text style="font-size:24rpx;">会员价￥</text>{{item.$orig.sell_price}}</view></view><image class="arrowright" src="/static/img/arrowright.png"></image></view></block></view></view></block></view><block wx:if="{{buydialogShow}}"><buydialog vue-id="279ecd7b-2" proid="{{proid}}" menuindex="{{menuindex}}" btntype="1" needaddcart="{{false}}" data-event-opts="{{[['^buydialogChange',[['buydialogChange']]],['^addcart',[['afteraddcart']]]]}}" bind:buydialogChange="__e" bind:addcart="__e" bind:__l="__l"></buydialog></block><view style="height:auto;position:relative;"><view style="width:100%;height:100rpx;"></view><view class="{{['footer','flex',menuindex>-1?'tabbarbot':'notabbarbot']}}"><view data-event-opts="{{[['tap',[['handleClickMask',['$event']]]]]}}" class="cart_ico" style="{{'background:'+('linear-gradient(0deg,'+$root.m9+' 0%,rgba('+$root.m10+',0.8) 100%)')+';'}}" catchtap="__e"><image class="img" src="/static/img/cart.png"></image><block wx:if="{{cartList.total>0}}"><view class="cartnum" style="{{'background:'+($root.m11)+';'}}">{{cartList.total}}</view></block></view><view class="text1">合计</view><view class="text2" style="{{'color:'+($root.m12)+';'}}"><text style="font-size:20rpx;">￥</text>{{cartList.totalprice}}</view><view class="flex1"></view><view data-event-opts="{{[['tap',[['gopay',['$event']]]]]}}" class="op" style="{{'background:'+('linear-gradient(270deg,'+$root.m13+' 0%,rgba('+$root.m14+',0.8) 100%)')+';'}}" bindtap="__e">去结算</view></view></view><block wx:if="{{cartListShow}}"><view class="{{['popup__container',menuindex>-1?'tabbarbot':'notabbarbot']}}" style="margin-bottom:100rpx;"><view data-event-opts="{{[['tap',[['handleClickMask',['$event']]]]]}}" class="{{['popup__overlay',menuindex>-1?'tabbarbot':'notabbarbot']}}" style="margin-bottom:100rpx;" catchtap="__e"></view><view class="popup__modal" style="min-height:400rpx;padding:0;"><view class="popup__title" style="border-bottom:1px solid #EFEFEF;"><text class="popup__title-text" style="color:#323232;font-weight:bold;font-size:32rpx;">购物车</text><view data-event-opts="{{[['tap',[['clearShopCartFn',['$event']]]]]}}" class="popup__close flex-y-center" style="color:#999999;font-size:24rpx;" catchtap="__e"><image style="width:24rpx;height:24rpx;margin-right:6rpx;" src="/static/img/del.png"></image>清空</view></view><view class="popup__content" style="padding:0;"><scroll-view class="prolist" scroll-y="{{true}}"><block wx:for="{{cartList.list}}" wx:for-item="cart" wx:for-index="index" wx:key="index"><block><view class="proitem"><image class="pic flex0" src="{{cart.guige.pic?cart.guige.pic:cart.product.pic}}"></image><view class="con"><view class="f1">{{cart.product.name}}</view><block wx:if="{{cart.guige.name!='默认规格'}}"><view class="f2">{{cart.guige.name}}</view></block><view class="f3" style="color:#ff5555;margin-top:10rpx;font-size:28rpx;">{{"￥"+cart.guige.sell_price}}</view></view><view class="addnum"><view class="minus"><image class="img" src="/static/img/cart-minus.png" data-num="-1" data-proid="{{cart.proid}}" data-ggid="{{cart.ggid}}" data-stock="{{cart.guige.stock}}" data-event-opts="{{[['tap',[['addcart',['$event']]]]]}}" bindtap="__e"></image></view><text class="i">{{cart.num}}</text><view class="plus"><image class="img" src="/static/img/cart-plus.png" data-num="1" data-proid="{{cart.proid}}" data-ggid="{{cart.ggid}}" data-stock="{{cart.guige.stock}}" data-event-opts="{{[['tap',[['addcart',['$event']]]]]}}" bindtap="__e"></image></view></view></view></block></block><block wx:if="{{!$root.g3}}"><block><text class="nopro">暂时没有商品喔~</text></block></block></scroll-view></view></view></view></block></block></block><block wx:if="{{loading}}"><loading vue-id="279ecd7b-3" bind:__l="__l"></loading></block><dp-tabbar vue-id="279ecd7b-4" opt="{{opt}}" data-event-opts="{{[['^getmenuindex',[['getmenuindex']]]]}}" bind:getmenuindex="__e" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="279ecd7b-5" data-ref="popmsg" bind:__l="__l"></popmsg></view>