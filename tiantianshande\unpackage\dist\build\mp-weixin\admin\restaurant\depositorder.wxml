<view class="container"><block wx:if="{{isload}}"><block><dd-tab vue-id="8529da3c-1" itemdata="{{['全部','待审核','寄存中','已取出','驳回']}}" itemst="{{['all','0','1','2','3']}}" st="{{st}}" isfixed="{{true}}" data-event-opts="{{[['^changetab',[['changetab']]]]}}" bind:changetab="__e" bind:__l="__l"></dd-tab><view style="width:100%;height:100rpx;"></view><view class="topsearch flex-y-center"><view class="f1 flex-y-center"><image class="img" src="/static/img/search_ico.png"></image><input placeholder="输入寄存名称、寄存人姓名或手机号搜索" placeholder-style="font-size:24rpx;color:#C2C2C2" data-event-opts="{{[['confirm',[['searchConfirm',['$event']]]]]}}" value="{{keyword}}" bindconfirm="__e"/></view></view><view class="order-content"><block><block wx:for="{{$root.l0}}" wx:for-item="item2" wx:for-index="idx" wx:key="idx"><view class="order-box"><block><view class="content"><view class="pic" data-url="{{'depositorderdetail?id='+item2.$orig.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image class="img" src="{{item2.$orig.pic}}"></image></view><view class="detail" data-url="{{'depositorderdetail?id='+item2.$orig.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><text class="t1">{{item2.$orig.name}}</text><text class="t2">{{"寄存人："+item2.$orig.linkman+" "+item2.$orig.tel}}</text><text class="t2">{{"数量："+item2.$orig.num}}</text><text class="t2">{{"存入时间："+item2.m0}}</text></view><block wx:if="{{item2.$orig.status==0}}"><view class="takeout st0" data-orderid="{{item2.$orig.id}}">待审核</view></block><block wx:if="{{item2.$orig.status==1}}"><view class="takeout" data-orderid="{{item2.$orig.id}}" data-num="{{item2.$orig.num}}" data-event-opts="{{[['tap',[['takeout',['$event']]]]]}}" bindtap="__e"><image class="img" src="/static/img/deposit_takeout.png"></image>取出</view></block><block wx:if="{{item2.$orig.status==2}}"><view class="takeout st2" data-orderid="{{item2.$orig.id}}">已取走</view></block><block wx:if="{{item2.$orig.status==3}}"><view class="takeout st3" data-orderid="{{item2.$orig.id}}">未通过</view></block><block wx:if="{{item2.$orig.status==4}}"><view class="takeout st2" data-orderid="{{item2.$orig.id}}">已过期</view></block></view></block></view></block></block></view><block wx:if="{{boxShow}}"><view data-event-opts="{{[['touchmove',[['disabledScroll',['$event']]]]]}}" catchtouchmove="__e"><view data-event-opts="{{[['tap',[['handleClickMask',['$event']]]]]}}" class="popup__overlay" catchtap="__e"></view><view class="popup__modal"><view class="popup__title"><text class="popup__title-text">请输入取出数量</text><image class="popup__close" style="width:36rpx;height:36rpx;" src="/static/img/close.png" data-event-opts="{{[['tap',[['handleClickMask',['$event']]]]]}}" catchtap="__e"></image></view><view class="popup__content takeoutBox"><form report-submit="true" data-event-opts="{{[['submit',[['formSubmit',['$event']]]],['reset',[['formReset',['$event']]]]]}}" bindsubmit="__e" bindreset="__e"><view class="orderinfo"><view class="item"><text class="t1">取出数量</text><input class="t2" type="text" placeholder="请输入要取出的数量" placeholder-style="font-size:28rpx;color:#BBBBBB" name="numbers" value="{{num}}"/></view></view><button class="btn" style="{{'background:'+($root.m1)+';'}}" form-type="submit">确定</button></form></view></view></view></block><block wx:if="{{nomore}}"><nomore vue-id="8529da3c-2" bind:__l="__l"></nomore></block><block wx:if="{{nodata}}"><nodata vue-id="8529da3c-3" bind:__l="__l"></nodata></block></block></block><block wx:if="{{loading}}"><loading vue-id="8529da3c-4" bind:__l="__l"></loading></block><dp-tabbar vue-id="8529da3c-5" opt="{{opt}}" bind:__l="__l"></dp-tabbar></view>