<view class="container"><block wx:if="{{isload}}"><block><block wx:if="{{chartData}}"><view class="team-stats"><view class="stat-item"><text class="stat-value">{{$root.m0}}</text><text class="stat-label">团队总人数</text></view><view class="stat-item"><text class="stat-value">{{chartData.children?$root.g0:0}}</text><text class="stat-label">直属成员</text></view><view class="stat-divider"></view><view class="stat-item"><text class="stat-value">{{$root.m1}}</text><text class="stat-label">间接成员</text></view></view></block><view class="chart-container"><block wx:if="{{!chartData}}"><text class="empty-text">正在加载关系图数据...</text></block><block wx:else><block wx:if="{{chartData===null}}"><text class="empty-text">该成员没有团队成员</text></block><block wx:else><scroll-view class="relation-tree" scroll-y="true"><view class="tree-root"><view class="member-card root-card"><view class="card-badge">团队长</view><image class="avatar" src="{{chartData.headimg}}" mode="aspectFill"></image><view class="member-info"><text class="nickname">{{chartData.nickname}}</text><text class="level">{{chartData.levelName}}</text></view></view></view><block wx:if="{{$root.g1}}"><view class="tree-connector"><view class="connector-line"></view><view class="connector-dot"></view></view></block><block wx:if="{{$root.g2}}"><view class="children-container"><view class="children-label">{{"团队成员 ("+$root.g3+"人)"}}</view><view class="children-grid"><block wx:for="{{$root.l0}}" wx:for-item="child" wx:for-index="index" wx:key="id"><view class="child-node"><view class="member-card"><image class="avatar" src="{{child.$orig.headimg}}" mode="aspectFill"></image><view class="member-info"><text class="nickname">{{child.$orig.nickname}}</text><text class="level">{{child.$orig.levelName}}</text><block wx:if="{{child.g4}}"><view class="sub-count"><image class="sub-icon" src="/static/img/team_icon.png" mode="aspectFit"></image><text>{{child.g5+"人"}}</text></view></block></view></view><block wx:if="{{child.g6}}"><view data-event-opts="{{[['tap',[['toggleExpand',['$0'],[[['chartData.children','id',child.$orig.id,'id']]]]]]]}}" class="expand-btn" bindtap="__e"><text>{{expandedNodes[child.$orig.id]?'收起':'展开'}}</text><text class="expand-icon">{{expandedNodes[child.$orig.id]?'∧':'∨'}}</text></view></block><block wx:if="{{child.g7}}"><view class="grandchildren-list"><block wx:for="{{child.$orig.children}}" wx:for-item="grandchild" wx:for-index="idx" wx:key="id"><view class="grandchild-item"><image class="mini-avatar" src="{{grandchild.headimg}}" mode="aspectFill"></image><view class="grandchild-info"><text class="grandchild-name">{{grandchild.nickname}}</text><text class="grandchild-level">{{grandchild.levelName}}</text></view></view></block></view></block></view></block></view></view></block></scroll-view></block></block></view></block></block><block wx:if="{{loading}}"><loading vue-id="63cc30f2-1" bind:__l="__l"></loading></block><popmsg class="vue-ref" vue-id="63cc30f2-2" data-ref="popmsg" bind:__l="__l"></popmsg></view>