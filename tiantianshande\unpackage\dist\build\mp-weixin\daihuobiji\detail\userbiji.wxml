<view class="page flex-col"><view class="box_6 flex-col" style="{{('background: linear-gradient(180deg, '+$root.m0+', '+$root.m1+')')}}"><view class="nav-bar_1 _div"></view><view class="box_7 flex-row justify-between"><view class="single-avatar_1 flex-col"><image class="image_1 weui_avatar_circle" referrerpolicy="no-referrer" src="{{pagecontent.headimg}}"></image></view><view class="text-wrapper_3 flex-col justify-between"><text class="text_3">{{pagecontent.nickname}}</text><text class="text_4">{{pagecontent.level_name}}</text><text class="text_4">{{"用户ID:"+pagecontent.id}}</text></view></view><view class="user-intro-section"><text class="text_5 user-intro">{{limitedIntro}}</text></view><view class="box_8 flex-row justify-between"><view class="text-group_4 flex-col justify-between" data-url="/daihuobiji/detail/member/follower" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><text class="text_18">{{pagecontent.following_count||0}}</text><text class="text_19">我的关注</text></view><view class="text-group_5 flex-col justify-between" data-url="/daihuobiji/detail/member/following" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><text class="text_20">{{pagecontent.followers_count||0}}</text><text class="text_21">关注我的</text></view><view class="text-wrapper_4 flex-col justify-between"><text class="text_22">{{pagecontent.money||0}}</text><text class="text_23">积分/余额</text></view></view><view class="box_9 flex-row"><button data-event-opts="{{[['tap',[['onClick_2',['$event']]]]]}}" class="button_1 flex-col" bindtap="__e"><text class="text_24">我的简介</text></button><view data-event-opts="{{[['tap',[['onClick_1',['$event']]]]]}}" class="icon_7 flex-col" bindtap="__e"><image class="label_2" referrerpolicy="no-referrer" src="../../static/img/set.png"></image></view><button data-event-opts="{{[['tap',[['LinkTo',['$event']]]]]}}" class="button_11 flex-col" bindtap="__e"><text class="text_24" style="font-weight:700 !important;">切换身份</text></button></view><view class="group_3 flex-row"><view style="text-align:center;"><view class="text_6">{{pagecontent.citationCount||0}}</view><view class="text_7">被引用次数</view></view><view style="width:0.5px;background-color:#eaeaea;height:32px;"></view><view data-event-opts="{{[['tap',[['goToFatielog',['$event']]]]]}}" style="text-align:center;" bindtap="__e"><view class="text_6">{{pagecontent.comment_count||0}}</view><view class="text_7">我的评论</view></view><view style="width:0.5px;background-color:#eaeaea;height:32px;"></view><view style="text-align:center;" data-url="/daihuobiji/detail/fatielog" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="text_6">{{pagecontent.note_count||0}}</view><view class="text_7">我的笔记</view></view></view></view><view class="flex-col"><view class="text-wrapper_5 flex-row justify-between"><text data-event-opts="{{[['tap',[['switchTab',['notes']]]]]}}" class="{{['text_10',(activeTab==='notes')?'active':'']}}" style="{{('color: '+(activeTab==='notes'?$root.m2:'#999999'))}}" bindtap="__e">笔记</text><text data-event-opts="{{[['tap',[['switchTab',['favorites']]]]]}}" class="{{['text_11',(activeTab==='favorites')?'active':'']}}" style="{{('color: '+(activeTab==='favorites'?$root.m3:'#999999'))}}" bindtap="__e">点赞</text></view><view style="padding:24rpx 24rpx 60rpx 24rpx;margin-top:15rpx;"><view class="list"><block wx:for="{{noteList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block wx:if="{{activeTab==='notes'}}"><view class="pbl"><view data-event-opts="{{[['tap',[['goItem',['$0'],[[['noteList','',index]]]]]]]}}" class="image" bindtap="__e"><image fade-show="{{true}}" lazy-load="{{true}}" lazy-load-margin="{{0}}" mode="widthFix" src="{{item.coverimg}}"></image></view><view data-event-opts="{{[['tap',[['goItem',['$0'],[[['noteList','',index]]]]]]]}}" class="title" bindtap="__e"><rich-text nodes="{{item.title}}"></rich-text></view><view data-event-opts="{{[['tap',[['goItem',['$0'],[[['noteList','',index]]]]]]]}}" style="display:flex;align-items:center;justify-content:space-between;padding:10px;color:#aaa;" bindtap="__e"><view style="display:flex;align-items:center;width:60%;"><image style="width:20px;height:20px;border-radius:50px;" src="{{item.headimg}}" class="_img"></image><view style="font-size:10px;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;margin-left:5px;">{{''+item.nickname+''}}</view></view><view style="display:flex;align-items:center;"><image style="width:12px;height:12px;" src="../../static/restaurant/like1.png"></image><view style="font-size:10px;margin-left:5px;">{{item.zan}}</view></view></view></view></block></block><block wx:for="{{collectList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block wx:if="{{activeTab==='favorites'}}"><view class="pbl"><view data-event-opts="{{[['tap',[['goItem',['$0'],[[['collectList','',index]]]]]]]}}" class="image" bindtap="__e"><image fade-show="{{true}}" lazy-load="{{true}}" lazy-load-margin="{{0}}" mode="widthFix" src="{{item.coverimg}}"></image></view><view data-event-opts="{{[['tap',[['goItem',['$0'],[[['collectList','',index]]]]]]]}}" class="title" bindtap="__e"><rich-text nodes="{{item.title}}"></rich-text></view><view data-event-opts="{{[['tap',[['goItem',['$0'],[[['collectList','',index]]]]]]]}}" style="display:flex;align-items:center;justify-content:space-between;padding:10px;color:#aaa;" bindtap="__e"><view style="display:flex;align-items:center;width:60%;"><image style="width:20px;height:20px;border-radius:50px;" src="{{item.headimg}}" class="_img"></image><view style="font-size:10px;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;margin-left:5px;">{{''+item.nickname+''}}</view></view><view style="display:flex;align-items:center;"><image style="width:12px;height:12px;" src="../../static/restaurant/like1.png"></image><view style="font-size:10px;margin-left:5px;">{{item.zan}}</view></view></view></view></block></block></view><block wx:if="{{!loading}}"><uni-load-more vue-id="41f8d908-1" status="{{loadStatus}}" bind:__l="__l"></uni-load-more></block></view></view><block wx:if="{{loading}}"><loading vue-id="41f8d908-2" bind:__l="__l"></loading></block><dp-tabbar vue-id="41f8d908-3" opt="{{opt}}" data-event-opts="{{[['^getmenuindex',[['getmenuindex']]]]}}" bind:getmenuindex="__e" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="41f8d908-4" data-ref="popmsg" bind:__l="__l"></popmsg></view>