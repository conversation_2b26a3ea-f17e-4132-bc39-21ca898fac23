<view><block wx:if="{{isload}}"><block><view class="orderinfo"><view class="item"><text class="t1 flex1">{{$root.m0+"信息"}}</text><view class="t2 flex-y-center" style="flex:0;"><image style="width:80rpx;height:80rpx;margin-right:8rpx;" src="{{info.headimg}}"></image>{{''+info.nickname}}</view></view></view><view class="orderinfo"><view class="item"><text class="t1">提现金额</text><text class="t2">{{"￥"+info.txmoney}}</text></view><view class="item"><text class="t1">打款金额</text><text class="t2">{{"￥"+info.money}}</text></view><view class="item"><text class="t1">提现方式</text><text class="t2">{{info.paytype}}</text></view><block wx:if="{{info.paytype=='支付宝'}}"><view class="item"><text class="t1">支付宝账号</text><text class="t2">{{info.aliaccount}}</text></view></block><block wx:if="{{info.paytype=='银行卡'}}"><view class="item"><text class="t1">开户行</text><text class="t2">{{info.bankname}}</text></view></block><block wx:if="{{info.paytype=='银行卡'}}"><view class="item"><text class="t1">持卡人</text><text class="t2">{{info.bankcarduser}}</text></view></block><block wx:if="{{info.paytype=='银行卡'}}"><view class="item"><text class="t1">卡号</text><text class="t2">{{info.bankcardnum}}</text></view></block><view class="item"><text class="t1">状态</text><block wx:if="{{info.status==0}}"><text class="t2">审核中</text></block><block wx:if="{{info.status==1}}"><text class="t2">已审核</text></block><block wx:if="{{info.status==2}}"><text class="t2">已驳回</text></block><block wx:if="{{info.status==3}}"><text class="t2">已打款</text></block></view></view><view style="width:100%;height:120rpx;"></view><view class="bottom"><block wx:if="{{info.status==0}}"><view class="btn" data-id="{{info.id}}" data-event-opts="{{[['tap',[['shenhepass',['$event']]]]]}}" bindtap="__e">审核通过</view></block><block wx:if="{{info.status==0}}"><view class="btn" data-id="{{info.id}}" data-event-opts="{{[['tap',[['shenhenopass',['$event']]]]]}}" bindtap="__e">审核驳回</view></block><block wx:if="{{info.status==1}}"><view class="btn" data-id="{{info.id}}" data-event-opts="{{[['tap',[['setydk',['$event']]]]]}}" bindtap="__e">改为已打款</view></block><block wx:if="{{info.status==1&&(info.paytype=='微信钱包'||info.paytype=='银行卡')}}"><view class="btn" data-id="{{info.id}}" data-event-opts="{{[['tap',[['wxdakuan',['$event']]]]]}}" bindtap="__e">微信打款</view></block></view></block></block><popmsg class="vue-ref" vue-id="163279fe-1" data-ref="popmsg" bind:__l="__l"></popmsg></view>