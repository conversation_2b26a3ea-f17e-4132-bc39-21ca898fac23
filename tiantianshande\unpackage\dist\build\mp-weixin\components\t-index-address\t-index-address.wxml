<view class="address-component"><view class="address-component__main"><scroll-view class="address-component__main-list" scroll-top="{{scrollTop}}" scroll-into-view="{{scrollIntoView}}" scroll-with-animation="{{true}}" scroll-y="{{true}}"><block wx:for="{{keys}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="address-component__main-item" id="{{'index-'+item}}"><view class="address-component__main-title">{{item}}</view><block wx:for="{{formatData[item]}}" wx:for-item="town" wx:for-index="idx" wx:key="idx"><view data-event-opts="{{[['tap',[['handleTap',['$0'],[[['formatData.'+item+'','',idx]]]]]]]}}" class="address-component__main-row" bindtap="__e"><view class="address-component__main-text">{{town.name}}</view></view></block></view></block></scroll-view><view class="address-component__letter"><block wx:for="{{keys}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['handleSelect',['$0'],[[['keys','',index]]]]]]]}}" class="address-component__letter-item" bindtap="__e">{{''+item+''}}</view></block></view></view></view>