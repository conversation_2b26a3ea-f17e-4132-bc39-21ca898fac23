<view class="container"><block wx:if="{{isload}}"><block><view class="content"><view class="top flex"><view class="headimg"><image src="{{data.avatar}}"></image></view><view class="right"><view class="t1"><text class="bold">{{data.name}}</text><text>{{data.juli+"km"}}</text></view><view class="t3"><view class="t11"><text>{{"服务单量："+data.recentOrderTotalDesc}}</text></view><view class="t11">服务状态：<text class="statusdesc">{{data.statusDesc}}</text></view><view class="t11">{{"最后上线时间："+data.lastOnlineTime+''}}</view></view></view></view><block wx:if="{{data.desc}}"><view class="desc">{{''+data.desc+''}}</view></block><view class="list"><view class="tab"><view class="{{['item '+(curTopIndex==0?'on':'')]}}" data-index="{{0}}" data-event-opts="{{[['tap',[['switchTopTab',['$event']]]]]}}" bindtap="__e">服务项目<view class="after" style="{{'background:'+($root.m0)+';'}}"></view></view></view><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block wx:if="{{curTopIndex==0&&item.$orig.price>0}}"><view class="content2 flex" data-id="{{item.$orig.id}}"><view class="f1" data-url="{{'product2?skillid='+item.$orig.skillId+'&masterid='+data.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="headimg2"><image src="{{set.pic}}"></image></view><view class="text1"><view class="text2 flex"><text class="t1">{{item.$orig.firstCategoryName+" "+item.$orig.secondCategoryName+''}}</text><view class="text3"><text class="t4"><text class="price">{{item.$orig.price+item.$orig.unit}}</text></text></view></view><view class="textdesc"><text>{{set.desc}}</text></view><view class="flex" style="justify-content:space-between;"><view style="font-size:24rpx;margin:20rpx 30rpx;">{{"服务类型："+item.$orig.serviceType}}</view><view class="yuyue" style="{{'background:'+(item.m1)+';'}}" data-url="{{'product2?skillid='+item.$orig.skillId+'&masterid='+data.id+'&masterName='}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">预约</view></view></view></view></view></block></block></view></view><block wx:if="{{nodata}}"><nodata vue-id="4434a3e2-1" bind:__l="__l"></nodata></block><view style="height:140rpx;"></view></block></block><block wx:if="{{loading}}"><loading vue-id="4434a3e2-2" bind:__l="__l"></loading></block><dp-tabbar vue-id="4434a3e2-3" opt="{{opt}}" data-event-opts="{{[['^getmenuindex',[['getmenuindex']]]]}}" bind:getmenuindex="__e" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="4434a3e2-4" data-ref="popmsg" bind:__l="__l"></popmsg></view>