<view class="container"><block wx:if="{{isload}}"><block><view class="search-container"><view class="topsearch flex-y-center"><view class="f1 flex-y-center"><image class="img" src="/static/img/search_ico.png"></image><input placeholder="搜索" placeholder-style="font-size:24rpx;color:#C2C2C2" confirm-type="search" data-event-opts="{{[['confirm',[['search',['$event']]]]]}}" value="{{keyword}}" bindconfirm="__e"/></view></view></view><view class="ind_business"><view class="ind_buslist" id="datalist"><block wx:for="{{datalist}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><view><view class="label"><text class="t1">{{item.nickname+''}}</text><text class="t2">{{item.tuozhancate}}</text></view><view class="divider"></view><block wx:if="{{showtype==0}}"><block><view class="icon-container"><view class="icon-item" data-url="{{'/pagesExt/tuozhanyuan/myteam?mingxiid='+item.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image class="icon-image" src="/pagesExt/static/images/renyuan.png"></image><text class="label">人员管理</text></view><view class="icon-item" data-url="{{'/pagesExt/tuozhanyuan/tuozhanfeimingxi?mingxiid='+item.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image class="icon-image" src="/pagesExt/static/images/yue.png"></image><text class="label">余额明细</text></view><view class="icon-item" data-url="{{'/pagesExt/tuozhanyuan/shiyebutongji?mingxiid='+item.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image class="icon-image" src="/pagesExt/static/images/tongji.png"></image><text class="label">统计</text></view></view></block></block></view></block></block><block wx:if="{{nomore}}"><nomore vue-id="17f40cc0-1" bind:__l="__l"></nomore></block><block wx:if="{{nodata}}"><nodata vue-id="17f40cc0-2" bind:__l="__l"></nodata></block></view></view><block wx:if="{{buydialogShow}}"><buydialog vue-id="17f40cc0-3" proid="{{proid}}" menuindex="{{menuindex}}" data-event-opts="{{[['^addcart',[['addcart']]],['^buydialogChange',[['buydialogChange']]]]}}" bind:addcart="__e" bind:buydialogChange="__e" bind:__l="__l"></buydialog></block></block></block><uni-popup class="vue-ref" vue-id="17f40cc0-4" id="dialogInput" type="dialog" data-ref="dialogInput" bind:__l="__l" vue-slots="{{['default']}}"><uni-popup-dialog vue-id="{{('17f40cc0-5')+','+('17f40cc0-4')}}" mode="input" title="额度充值" value="" placeholder="请输入转入额度" data-event-opts="{{[['^confirm',[['tomonenyconfirm']]]]}}" bind:confirm="__e" bind:__l="__l"></uni-popup-dialog></uni-popup><block wx:if="{{clistshow}}"><view class="popup__container"><view data-event-opts="{{[['tap',[['changeClistDialog',['$event']]]]]}}" class="popup__overlay" catchtap="__e"></view><view class="popup__modal"><view class="popup__title"><text class="popup__title-text">请选择拓展员分类</text><image class="popup__close" style="width:36rpx;height:36rpx;" src="/static/img/close.png" data-event-opts="{{[['tap',[['changeClistDialog',['$event']]]]]}}" catchtap="__e"></image></view><view class="popup__content"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="id"><block><view class="clist-item" data-id="{{item.$orig.id}}" data-event-opts="{{[['tap',[['cidsChange',['$event']]]]]}}" bindtap="__e"><view class="flex1">{{item.$orig.name+"-"+item.$orig.id}}</view><view class="radio" style="{{(item.m0?'background:'+item.m1+';border:0':'')}}"><image class="radio-img" src="/static/img/checkd.png"></image></view></view></block></block></view><view class="uni-dialog-button-group"><view data-event-opts="{{[['tap',[['changeClistDialog',['$event']]]]]}}" class="uni-dialog-button" catchtap="__e"><text class="uni-dialog-button-text">取消</text></view><view data-event-opts="{{[['tap',[['dialogDetailtxtConfirm',['$event']]]]]}}" class="uni-dialog-button uni-border-left" bindtap="__e"><text class="uni-dialog-button-text uni-button-color">确定</text></view></view><view data-event-opts="{{[['tap',[['changeClistDialog',['$event']]]]]}}" class="uni-popup-dialog__close" catchtap="__e"><label class="uni-popup-dialog__close-icon _span"></label></view></view></view></block><block wx:if="{{loading}}"><loading vue-id="17f40cc0-6" bind:__l="__l"></loading></block><dp-tabbar vue-id="17f40cc0-7" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="17f40cc0-8" data-ref="popmsg" bind:__l="__l"></popmsg></view>