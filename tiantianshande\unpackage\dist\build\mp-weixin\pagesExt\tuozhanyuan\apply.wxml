<view><block wx:if="{{isload}}"><block><block wx:if="{{info.id&&info.status==2}}"><view style="color:red;padding:10rpx 30rpx;margin-top:20rpx;"><parse vue-id="ac5883ce-1" content="{{tset.verify_reject||'审核未通过：'}}" bind:__l="__l"></parse>{{info.reason+'，请修改后重新提交'}}</view></block><block wx:if="{{info.id&&info.status==0}}"><view style="color:red;padding:10rpx 30rpx;margin-top:20rpx;"><parse vue-id="ac5883ce-2" content="{{tset.verify_notice||'您的拓展员申请已提交成功，请耐心等待审核，平台将于3个工作日内联系您核实信息，请留意来电'}}" bind:__l="__l"></parse></view></block><block wx:if="{{info.id&&info.status==1}}"><view style="color:red;padding:10rpx 30rpx;margin-top:20rpx;"><parse vue-id="ac5883ce-3" content="{{tset.verify_success||'恭喜您审核通过！您已成为拓展员'}}" bind:__l="__l"></parse></view></block><view style="color:red;padding:10rpx 30rpx;margin-top:20rpx;"><parse vue-id="ac5883ce-4" content="{{tset.verify_normal||'温馨提示：成为拓展员后可享受推广分佣权益'}}" bind:__l="__l"></parse></view><form data-event-opts="{{[['submit',[['subform',['$event']]]]]}}" bindsubmit="__e"><view class="apply_box"><view class="apply_item"><view>真实姓名<text style="color:red;">*</text></view><view class="flex-y-center"><input type="text" name="realname" placeholder="请填写真实姓名" value="{{info.realname}}"/></view></view><view class="apply_item"><view>联系电话<text style="color:red;">*</text></view><view class="flex-y-center"><input type="text" name="tel" placeholder="请填写手机号码" value="{{info.tel}}"/></view></view><view class="apply_item"><view>微信号<text style="color:red;">*</text></view><view class="flex-y-center"><input type="text" name="wechat" placeholder="请填写微信号" value="{{info.wechat}}"/></view></view><view class="apply_item"><view>邀请码</view><view class="flex-y-center"><input type="text" name="invite_code" placeholder="请填写邀请人邀请码（可选）" value="{{info.invite_code}}"/></view></view></view><block wx:if="{{!simpleApply}}"><view class="apply_box"><view class="apply_item"><view>申请等级<text style="color:red;">*</text></view><view><picker value="{{levelIndex}}" range="{{levelNames}}" data-event-opts="{{[['change',[['levelChange',['$event']]]]]}}" bindchange="__e"><view class="picker">{{levelNames[levelIndex]||'请选择申请等级'}}</view></picker></view></view><view class="apply_item"><view style="white-space:nowrap;min-width:120rpx;">申请理由<text style="color:red;">*</text></view><view class="reason-container"><view class="flex-y-center"><textarea style="height:160rpx;font-size:28rpx;line-height:1.5;" name="reason" placeholder="请详细说明您的申请理由，如：推广经验、客户资源、市场优势等" value="{{info.reason}}"></textarea></view></view></view><view class="apply_item"><view>详细地址<text style="color:red;">*</text></view><view class="flex-y-center"><input type="text" name="address" placeholder="请输入详细地址" value="{{info.address}}"/></view></view></view></block><block wx:if="{{showApplyConditions&&!simpleApply}}"><view class="apply_box"><view class="apply_item" style="border-bottom:0;"><text style="color:#333;font-weight:bold;">申请条件</text></view><block wx:for="{{applyConditionsList}}" wx:for-item="condition" wx:for-index="index" wx:key="index"><view class="condition-item"><view class="condition-text">{{condition.name}}</view><view data-event-opts="{{[['tap',[['clickCondition',['$0',index],[[['applyConditionsList','',index]]]]]]]}}" class="{{['condition-status',condition.completed?'completed':'incomplete']}}" bindtap="__e">{{''+(condition.completed?'已满足':'未满足')+''}}<block wx:if="{{!condition.completed&&condition.type==='buy_product'}}"><text style="font-size:20rpx;margin-left:8rpx;">(点击购买)</text></block></view></view></block><view style="padding:10rpx 0;font-size:24rpx;color:#999;"><text>注：申请此等级需要满足以上所有条件</text></view></view></block><block wx:if="{{showUpgradeConditions&&!simpleApply}}"><view class="apply_box"><view class="apply_item" style="border-bottom:0;"><text style="color:#333;font-weight:bold;">升级条件</text></view><block wx:for="{{upgradeConditionsList}}" wx:for-item="condition" wx:for-index="index" wx:key="index"><view class="condition-item"><view class="condition-text">{{condition.name}}</view><view data-event-opts="{{[['tap',[['clickUpgradeCondition',['$0',index],[[['upgradeConditionsList','',index]]]]]]]}}" class="{{['condition-status',condition.completed?'completed':'incomplete']}}" bindtap="__e">{{''+(condition.completed?'已完成':'未完成')+''}}<block wx:if="{{!condition.completed&&condition.type==='buy_product'}}"><text style="font-size:20rpx;margin-left:8rpx;">(点击购买)</text></block></view></view></block><view style="padding:10rpx 0;font-size:24rpx;color:#999;"><text>注：成为此等级后，满足以上条件可升级到更高等级</text></view></view></block><block wx:if="{{!simpleApply}}"><view class="apply_box"><view class="apply_item" style="border-bottom:0;"><text>身份证正面<text style="color:red;">*</text></text></view><view class="flex" style="flex-wrap:wrap;padding-bottom:20rpx;"><block wx:for="{{idcard_front}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="layui-imgbox"><view class="layui-imgbox-close" data-index="{{index}}" data-field="idcard_front" data-event-opts="{{[['tap',[['removeimg',['$event']]]]]}}" bindtap="__e"><image src="/static/img/ico-del.png"></image></view><view class="layui-imgbox-img"><image src="{{item}}" data-url="{{item}}" mode="widthFix" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image></view></view></block><block wx:if="{{$root.g0==0}}"><view class="uploadbtn" style="{{('background:url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 60rpx;background-size:80rpx 80rpx;background-color:#F3F3F3;')}}" data-field="idcard_front" data-event-opts="{{[['tap',[['uploadimg',['$event']]]]]}}" bindtap="__e"></view></block></view><input type="text" hidden="true" name="idcard_front" maxlength="-1" value="{{$root.g1}}"/></view></block><block wx:if="{{!simpleApply}}"><view class="apply_box"><view class="apply_item" style="border-bottom:0;"><text>身份证反面<text style="color:red;">*</text></text></view><view class="flex" style="flex-wrap:wrap;padding-bottom:20rpx;"><block wx:for="{{idcard_back}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="layui-imgbox"><view class="layui-imgbox-close" data-index="{{index}}" data-field="idcard_back" data-event-opts="{{[['tap',[['removeimg',['$event']]]]]}}" bindtap="__e"><image src="/static/img/ico-del.png"></image></view><view class="layui-imgbox-img"><image src="{{item}}" data-url="{{item}}" mode="widthFix" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image></view></view></block><block wx:if="{{$root.g2==0}}"><view class="uploadbtn" style="{{('background:url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 60rpx;background-size:80rpx 80rpx;background-color:#F3F3F3;')}}" data-field="idcard_back" data-event-opts="{{[['tap',[['uploadimg',['$event']]]]]}}" bindtap="__e"></view></block></view><input type="text" hidden="true" name="idcard_back" maxlength="-1" value="{{$root.g3}}"/></view></block><block wx:if="{{!simpleApply}}"><view class="apply_box"><view class="apply_item" style="border-bottom:0;"><text>手持身份证照片<text style="color:red;">*</text></text></view><view class="flex" style="flex-wrap:wrap;padding-bottom:20rpx;"><block wx:for="{{idcard_hold}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="layui-imgbox"><view class="layui-imgbox-close" data-index="{{index}}" data-field="idcard_hold" data-event-opts="{{[['tap',[['removeimg',['$event']]]]]}}" bindtap="__e"><image src="/static/img/ico-del.png"></image></view><view class="layui-imgbox-img"><image src="{{item}}" data-url="{{item}}" mode="widthFix" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image></view></view></block><block wx:if="{{$root.g4==0}}"><view class="uploadbtn" style="{{('background:url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 60rpx;background-size:80rpx 80rpx;background-color:#F3F3F3;')}}" data-field="idcard_hold" data-event-opts="{{[['tap',[['uploadimg',['$event']]]]]}}" bindtap="__e"></view></block></view><input type="text" hidden="true" name="idcard_hold" maxlength="-1" value="{{$root.g5}}"/></view></block><block wx:if="{{!simpleApply}}"><view class="apply_box"><view class="apply_item" style="border-bottom:0;"><text>其他证明材料</text></view><view class="flex" style="flex-wrap:wrap;padding-bottom:20rpx;"><block wx:for="{{other_files}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="layui-imgbox"><view class="layui-imgbox-close" data-index="{{index}}" data-field="other_files" data-event-opts="{{[['tap',[['removeimg',['$event']]]]]}}" bindtap="__e"><image src="/static/img/ico-del.png"></image></view><view class="layui-imgbox-img"><image src="{{item}}" data-url="{{item}}" mode="widthFix" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image></view></view></block><view class="uploadbtn" style="{{('background:url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 60rpx;background-size:80rpx 80rpx;background-color:#F3F3F3;')}}" data-field="other_files" data-event-opts="{{[['tap',[['uploadimg',['$event']]]]]}}" bindtap="__e"></view></view><input type="text" hidden="true" name="other_files" maxlength="-1" value="{{$root.g6}}"/></view></block><block wx:if="{{tset.xieyi_show==1}}"><block><block wx:if="{{!info.id||info.status==2}}"><view class="flex-y-center" style="margin-left:20rpx;color:#999;"><checkbox-group data-event-opts="{{[['change',[['isagreeChange',['$event']]]]]}}" bindchange="__e"><label class="flex-y-center"><checkbox value="1" checked="{{isagree}}"></checkbox>阅读并同意</label></checkbox-group><text data-event-opts="{{[['tap',[['showxieyiFun',['$event']]]]]}}" style="color:#666;" bindtap="__e">《拓展员入驻协议》</text></view></block></block></block><view style="padding:30rpx 0;"><block wx:if="{{!info.id||info.status==2}}"><button class="set-btn" style="{{('background:linear-gradient(90deg,'+$root.m0+' 0%,rgba('+$root.m1+',0.8) 100%)')}}" form-type="submit">提交申请</button></block></view></form><view class="content"><view data-event-opts="{{[['tap',[['goBack',['$event']]]]]}}" class="back-button" bindtap="__e"><text class="t1">返回</text></view></view><view style="width:100%;height:100%;position:fixed;top:0;left:0;z-index:99;background:rgba(0,0,0,0.7);" id="xieyi" hidden="{{!showxieyi}}"><view style="width:90%;margin:0 auto;height:85%;margin-top:10%;background:#fff;color:#333;padding:5px 10px 50px 10px;position:relative;border-radius:2px;"><view style="overflow:scroll;height:100%;"><parse vue-id="ac5883ce-5" content="{{tset.xieyi}}" bind:__l="__l"></parse></view><view data-event-opts="{{[['tap',[['hidexieyi',['$event']]]]]}}" style="position:absolute;z-index:9999;bottom:10px;left:0;right:0;margin:0 auto;text-align:center;" bindtap="__e">已阅读并同意</view></view></view></block></block><block wx:if="{{loading}}"><loading vue-id="ac5883ce-6" bind:__l="__l"></loading></block><dp-tabbar vue-id="ac5883ce-7" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="ac5883ce-8" data-ref="popmsg" bind:__l="__l"></popmsg></view>