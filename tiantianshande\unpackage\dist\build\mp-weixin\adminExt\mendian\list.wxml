<view class="container"><block wx:if="{{isload}}"><block><dd-tab vue-id="f84f56fc-1" itemdata="{{['待审核','已通过','已驳回']}}" itemst="{{['0','1','2']}}" st="{{st}}" data-event-opts="{{[['^changetab',[['changetab']]]]}}" bind:changetab="__e" bind:__l="__l"></dd-tab><block wx:if="{{$root.g0}}"><view class="content"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><view class="item" data-url="{{'detail?id='+item.$orig.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view data-event-opts="{{[['tap',[['toteam',['$0'],[[['datalist','',index,'id']]]]]]]}}" class="itemL" bindtap="__e"><view class="f1"><image class="lgimg" src="{{item.$orig.headimg}}"></image><view class="t2"><text class="x1">{{item.$orig.xqname}}</text><block wx:if="{{item.$orig.tel}}"><text class="x2">{{"手机号："+item.$orig.tel}}</text></block><text class="x2">{{"申请时间："+item.m0}}</text></view></view><view class="op"><block wx:if="{{item.$orig.check_status==1}}"><view class="st1">已通过</view></block><block wx:if="{{item.$orig.check_status==0}}"><view class="st0">待审核</view></block><block wx:if="{{item.$orig.check_status==2}}"><view class="st2">已驳回</view></block></view><block wx:if="{{!item.$orig.check_status}}"><button class="btn" style="{{('background:rgba('+item.m1+',0.9)')}}" data-id="{{item.$orig.id}}" data-event-opts="{{[['tap',[['showaudit',['$event']]]]]}}" bindtap="__e">审 核</button></block></view></view></block></block></view></block><block wx:if="{{isshowaudit}}"><view data-event-opts="{{[['tap',[['hideaudit',['$event']]]]]}}" class="alert_popup" bindtap="__e"><view data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" class="alert_popup_content" catchtap="__e"><view class="form-item"><text class="form-label">审核结果：</text><view class="form-radio"><label data-event-opts="{{[['tap',[['changeAudit',[1]]]]]}}" class="radio" bindtap="__e"><radio style="transform:scale(0.8);" color="{{$root.m2}}" value="1" checked="{{auditst==1?true:false}}"></radio>审核通过</label><label data-event-opts="{{[['tap',[['changeAudit',[2]]]]]}}" class="radio" bindtap="__e"><radio style="transform:scale(0.8);" color="{{$root.m3}}" value="2" checked="{{auditst==2?true:false}}"></radio>审核拒绝</label></view></view><block wx:if="{{auditst==2}}"><view class="form-item"><text class="form-label">驳回原因：</text><view class="form-txt"><textarea style="width:450rpx;" auto-height data-event-opts="{{[['input',[['__set_model',['','auditremark','$event',[]]]]]]}}" value="{{auditremark}}" bindinput="__e"></textarea></view></view></block><view class="form-sub"><button data-event-opts="{{[['tap',[['auditSub',['$event']]]]]}}" class="btn" style="{{'background:'+($root.m4)+';'}}" bindtap="__e">确 定</button></view></view></view></block></block></block><block wx:if="{{nodata}}"><nodata vue-id="f84f56fc-2" bind:__l="__l"></nodata></block><block wx:if="{{nomore}}"><nomore vue-id="f84f56fc-3" bind:__l="__l"></nomore></block><block wx:if="{{loading}}"><loading vue-id="f84f56fc-4" bind:__l="__l"></loading></block><dp-tabbar vue-id="f84f56fc-5" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="f84f56fc-6" data-ref="popmsg" bind:__l="__l"></popmsg></view>