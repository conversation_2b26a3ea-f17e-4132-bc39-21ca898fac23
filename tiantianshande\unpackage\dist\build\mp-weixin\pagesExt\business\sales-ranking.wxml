<view class="data-ranking-container"><view class="banner-area"><view class="radial-bg"></view><view class="title-area"><text class="main-title">数据榜</text></view><view class="trophy-container"><view class="trophy-icon"><view class="trophy-cup"><view class="trophy-top"></view><view class="trophy-middle"></view><view class="trophy-bottom"></view><view class="trophy-base"></view></view></view><view class="trophy-decorations"><view class="decoration left-leaf"></view><view class="decoration right-leaf"></view><view class="decoration top-sparkle"></view><view class="decoration bottom-sparkle"></view></view><view class="banner-flags"><view class="flag-string"></view><view class="flag flag-1"></view><view class="flag flag-2"></view><view class="flag flag-3"></view><view class="flag flag-4"></view><view class="flag flag-5"></view></view><text class="top-text">TOP</text></view></view><view class="type-switcher"><view data-event-opts="{{[['tap',[['switchRankingType',['business']]]]]}}" class="{{['type-item',(rankingType==='business')?'active':'']}}" bindtap="__e">队伍排名</view><view data-event-opts="{{[['tap',[['switchRankingType',['category']]]]]}}" class="{{['type-item',(rankingType==='category')?'active':'']}}" bindtap="__e">学校排名</view></view><view class="filter-bar"><view class="filter-item date-type"><picker mode="selector" range="{{dateTypeOptions}}" range-key="label" data-event-opts="{{[['change',[['handleDateTypeChange',['$event']]]]]}}" bindchange="__e"><view class="picker-content"><text>{{currentDateType.label}}</text><text class="iconfont iconxiala"></text></view></picker></view><view class="filter-item date-picker"><picker mode="{{datePickerMode}}" value="{{dateValue}}" fields="{{dateFields}}" data-event-opts="{{[['change',[['handleDateChange',['$event']]]]]}}" bindchange="__e"><view class="picker-content"><text>{{displayDateValue||'选择日期'}}</text><text class="iconfont iconxiala"></text></view></picker></view><block wx:if="{{rankingType==='business'}}"><view class="filter-item category-picker"><picker mode="selector" range="{{categoryOptions}}" range-key="name" data-event-opts="{{[['change',[['handleCategoryChange',['$event']]]]]}}" bindchange="__e"><view class="picker-content"><text>{{currentCategory.name||'全部学校'}}</text><text class="iconfont iconxiala"></text></view></picker></view></block></view><view class="search-bar"><input class="search-input" type="text" placeholder="{{rankingType==='business'?'搜索队伍名称':'搜索学校名称'}}" data-event-opts="{{[['confirm',[['handleSearch',['$event']]]],['input',[['__set_model',['','keyword','$event',[]]]]]]}}" value="{{keyword}}" bindconfirm="__e" bindinput="__e"/><button data-event-opts="{{[['tap',[['handleSearch',['$event']]]]]}}" class="search-btn" bindtap="__e">搜索</button></view><view class="ranking-list"><view class="list-header"><view class="header-rank">排名</view><view class="header-info">{{rankingType==='business'?'队伍信息':'学校信息'}}</view><view class="header-sales">销售总金额(元)</view></view><block wx:if="{{$root.g0>0}}"><scroll-view class="list-content" scroll-y="{{true}}"><block wx:for="{{rankingList}}" wx:for-item="item" wx:for-index="index"><view class="list-item"><view class="{{['rank-num',(item.rank<=3)?'top-rank':'']}}">{{item.rank}}</view><view class="item-info"><block wx:if="{{rankingType==='business'}}"><image class="item-logo" src="{{item.logo||'/static/img/shop_addr.png'}}" mode="aspectFill"></image><text class="item-name">{{item.name}}</text></block><block wx:else><text class="item-name">{{item.name}}</text></block></view><view class="item-sales">{{"¥ "+item.sales_price}}</view></view></block></scroll-view></block><block wx:else><view class="empty-state"><image class="empty-icon" src="/static/img/order.png" mode="aspectFit"></image><text class="empty-text">暂无数据</text></view></block></view><block wx:if="{{totalCount>0}}"><view class="pagination"><view class="page-info">{{"共 "+totalCount+" 条"}}</view><view class="page-controls"><view data-event-opts="{{[['tap',[['prevPage',['$event']]]]]}}" class="{{['page-btn','prev',(currentPage<=1)?'disabled':'']}}" bindtap="__e">上一页</view><view class="page-current">{{currentPage+" / "+totalPages}}</view><view data-event-opts="{{[['tap',[['nextPage',['$event']]]]]}}" class="{{['page-btn','next',(currentPage>=totalPages)?'disabled':'']}}" bindtap="__e">下一页</view></view></view></block></view>