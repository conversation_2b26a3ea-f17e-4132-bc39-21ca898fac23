<view class="container"><form report-submit="true" data-event-opts="{{[['submit',[['formSubmit',['$event']]]],['reset',[['formReset',['$event']]]]]}}" bindsubmit="__e" bindreset="__e"><view class="form-item"><text class="label">退款原因</text><view class="input-item"><textarea placeholder="请输入退款原因" placeholder-style="color:#999;" name="reason"></textarea></view></view><button class="ref-btn" form-type="submit">确定</button></form><block wx:if="{{loading}}"><loading vue-id="2f8ca7f1-1" bind:__l="__l"></loading></block><dp-tabbar vue-id="2f8ca7f1-2" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="2f8ca7f1-3" data-ref="popmsg" bind:__l="__l"></popmsg></view>