<view class="view-width"><block wx:if="{{isload}}"><block><view class="head-class" style="{{'background:'+('url('+set.bgimg+')')+';'+('background-size:'+('cover')+';')+('background-repeat:'+('no-repeat')+';')}}"><view style="{{'height:'+(44+statusBarHeight+'px')+';'}}"></view><view class="head-view flex-bt flex-y-center"><view class="avat-view"><view class="user-info flex-row" data-url="{{uinfo.bid==0?'/pages/index/index':'/pagesExt/business/index?id='+uinfo.bid}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><text class="nickname">{{set.name}}</text><block wx:if="{{uinfo.bid>0}}"><text class="nickname" style="font-weight:normal;">{{"(ID:"+uinfo.bid+")"}}</text></block></view><image class="imgback" src="{{pre_url+'/static/img/location/right-black.png'}}"></image></view><view class="option-img-view"><block wx:if="{{auth_data.hexiao_auth_data}}"><view data-event-opts="{{[['tap',[['saoyisao',['$event']]]]]}}" class="setup-view" style="margin-right:28rpx;" bindtap="__e"><image src="{{pre_url+'/static/img/adminExt/saoyisao.png'}}"></image></view></block><view class="setup-view" data-url="setpage" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image class="setup-img" src="{{pre_url+'/static/img/adminExt/setup.png'}}"></image></view></view></view><block wx:if="{{auth_data.finance}}"><view class="today-data flex-bt flex-y-center"><view class="option-view flex-col flex-x-center"><view data-event-opts="{{[['tap',[['explanation',['$event']]]]]}}" class="title-text flex flex-y-center flex-x-center" bindtap="__e">今日收款<image class="title-icon" src="{{pre_url+'/static/img/adminExt/jieshiicon.png'}}"></image></view><view class="flex-y-center flex-x-center"><text class="unit-money">￥</text><text class="num-text">{{today_money}}</text></view></view><view class="option-view flex-col flex-x-center"><text class="title-text">今日订单</text><view class="flex-y-center flex-x-center"><text class="num-text">{{today_order_count}}</text></view></view><block wx:if="{{$root.m0}}"><view class="option-view flex-col flex-x-center"><text class="title-text">核销次数</text><view class="flex-y-center flex-x-center"><text class="num-text">{{uinfo.hexiao_num}}</text></view></view></block></view></block><block wx:if="{{auth_data.order}}"><block><block wx:if="{{showshoporder}}"><view class="mall-orders flex-col width"><view class="order-title flex-bt"><view class="title-text flex-y-center"><image class="left-img" src="{{pre_url+'/static/img/adminExt/titletips.png'}}"></image>商城订单</view><view class="all-text flex-y-center" data-url="../order/shoporder" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">全部订单<image class="right-img" src="{{pre_url+'/static/img/adminExt/jiantou.png'}}"></image></view></view><view class="order-list flex-bt"><view class="option-order flex-col" data-url="../order/shoporder?st=0" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><text class="num-text">{{count0}}</text><text class="title-text">待付款</text></view><view class="option-order flex-col" data-url="../order/shoporder?st=1" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><text class="num-text">{{count1}}</text><text class="title-text">待发货</text></view><view class="option-order flex-col" data-url="../order/shoporder?st=2" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><text class="num-text">{{count2}}</text><text class="title-text">待收货</text></view><view class="option-order flex-col" data-url="../order/shopRefundOrder" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><text class="num-text">{{count4}}</text><text class="title-text">退款/售后</text></view><view class="option-order flex-col" data-url="../order/shoporder?st=3" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><text class="num-text">{{count3}}</text><text class="title-text">已完成</text></view></view></view></block></block></block><view class="meun-view flex-aw"><block wx:if="{{auth_data.hexiao_auth_data}}"><view class="meun-options flex-col flex-x-center" data-url="../hexiao/record" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image class="menu-img" src="{{pre_url+'/static/img/adminExt/menu1.png'}}"></image><text class="menu-text">核销记录</text></view></block><block wx:if="{{auth_data.product}}"><block><view class="meun-options flex-col flex-x-center" data-url="../product/index" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image class="menu-img" src="{{pre_url+'/static/img/adminExt/menu2.png'}}"></image><text class="menu-text">商品管理</text></view></block></block><block wx:if="{{uinfo.shownotice}}"><view class="meun-options flex-col flex-x-center" data-url="../index/setnotice" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image class="menu-img" src="{{pre_url+'/static/img/adminExt/menu3.png'}}"></image><text class="menu-text">消息通知</text></view></block><view class="meun-options flex-col flex-x-center" data-url="login" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image class="menu-img" src="{{pre_url+'/static/img/adminExt/menu4.png'}}"></image><text class="menu-text">切换账号</text></view><view class="meun-options flex-col flex-x-center" data-url="setpwd" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image class="menu-img" src="{{pre_url+'/static/img/adminExt/menu5.png'}}"></image><text class="menu-text">修改密码</text></view></view></view><block wx:if="{{auth_data.restaurant_product||auth_data.restaurant_table||auth_data.restaurant_tableWaiter}}"><block><view class="menu-manage flex-col"><view class="menu-title">菜品管理</view><view class="menu-list width"><block wx:if="{{auth_data.restaurant_product}}"><view class="meun-list-options flex-col flex-x-center flex-y-center" data-url="../restaurant/product/edit" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image class="menu-img" src="{{pre_url+'/static/img/adminExt/dishm1.png'}}"></image><text class="menu-text">添加菜品</text></view></block><block wx:if="{{auth_data.restaurant_product}}"><view class="meun-list-options flex-col flex-x-center flex-y-center" data-url="../restaurant/product/index" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image class="menu-img" src="{{pre_url+'/static/img/adminExt/dishm2.png'}}"></image><text class="menu-text">菜品列表</text></view></block><block wx:if="{{auth_data.restaurant_product}}"><view class="meun-list-options flex-col flex-x-center flex-y-center" data-url="../restaurant/category/index" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image class="menu-img" src="{{pre_url+'/static/img/adminExt/dishm6.png'}}"></image><text class="menu-text">菜品分类</text></view></block><block wx:if="{{auth_data.restaurant_product}}"><view class="meun-list-options flex-col flex-x-center flex-y-center" data-url="../restaurant/category/edit" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image class="menu-img" src="{{pre_url+'/static/img/adminExt/dishm4.png'}}"></image><text class="menu-text">添加分类</text></view></block><block wx:if="{{auth_data.restaurant_table}}"><view class="meun-list-options flex-col flex-x-center flex-y-center" data-url="../restaurant/table" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image class="menu-img" src="{{pre_url+'/static/img/adminExt/dishm5.png'}}"></image><text class="menu-text">餐桌编辑</text></view></block><block wx:if="{{auth_data.restaurant_table}}"><view class="meun-list-options flex-col flex-x-center flex-y-center" data-url="../restaurant/tableCategory" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image class="menu-img" src="{{pre_url+'/static/img/adminExt/dishm6.png'}}"></image><text class="menu-text">餐桌分类</text></view></block><block wx:if="{{auth_data.restaurant_tableWaiter}}"><view class="meun-list-options flex-col flex-x-center flex-y-center" data-url="../restaurant/tableWaiter" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image class="menu-img" src="{{pre_url+'/static/img/adminExt/dishm3.png'}}"></image><text class="menu-text">点餐清台</text></view></block></view></view></block></block><block wx:if="{{custom.mendian_upgrade}}"><block><view class="menu-manage flex-col"><view class="menu-title">{{$root.m1+"管理"}}</view><view class="menu-list width"><block><view class="meun-list-options flex-col flex-x-center flex-y-center" data-url="/adminExt/mendian/list" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image class="menu-img" src="{{pre_url+'/static/img/adminExt/dismendian.png'}}"></image><text class="menu-text">{{$root.m2+"列表"}}</text></view><view class="meun-list-options flex-col flex-x-center flex-y-center" data-url="/adminExt/mendian/withdrawlog" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image class="menu-img" src="{{pre_url+'/static/img/adminExt/dishm8.png'}}"></image><text class="menu-text">{{$root.m3+"佣金提现"}}</text></view></block></view></view></block></block><block wx:if="{{auth_data.restaurant_takeaway||auth_data.restaurant_shop||auth_data.restaurant_booking||auth_data.restaurant_deposit||auth_data.restaurant_queue}}"><block><view class="menu-manage flex-col"><view class="menu-title">外卖管理</view><view class="menu-list width"><block><block wx:if="{{auth_data.restaurant_takeaway}}"><view class="meun-list-options flex-col flex-x-center flex-y-center" data-url="../restaurant/takeawayorder" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image class="menu-img" src="{{pre_url+'/static/img/adminExt/dishm7.png'}}"></image><text class="menu-text">外卖订单</text></view></block><block wx:if="{{auth_data.restaurant_shop}}"><view class="meun-list-options flex-col flex-x-center flex-y-center" data-url="../restaurant/shoporder" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image class="menu-img" src="{{pre_url+'/static/img/adminExt/dishm8.png'}}"></image><text class="menu-text">点餐订单</text></view></block><block wx:if="{{auth_data.restaurant_booking}}"><view class="meun-list-options flex-col flex-x-center flex-y-center" data-url="../restaurant/bookingorder" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image class="menu-img" src="{{pre_url+'/static/img/adminExt/dishm9.png'}}"></image><text class="menu-text">预定订单</text></view></block><block wx:if="{{auth_data.restaurant_booking}}"><view class="meun-list-options flex-col flex-x-center flex-y-center" data-url="../restaurant/booking" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image class="menu-img" src="{{pre_url+'/static/img/adminExt/wm1.png'}}"></image><text class="menu-text">添加预定</text></view></block><block wx:if="{{auth_data.restaurant_queue}}"><view class="meun-list-options flex-col flex-x-center flex-y-center" data-url="../restaurant/queue" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image class="menu-img" src="{{pre_url+'/static/img/adminExt/wm3.png'}}"></image><text class="menu-text">排队叫号</text></view></block><block wx:if="{{auth_data.restaurant_queue}}"><view class="meun-list-options flex-col flex-x-center flex-y-center" data-url="../restaurant/queueCategory" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image class="menu-img" src="{{pre_url+'/static/img/adminExt/wm4.png'}}"></image><text class="menu-text">排队管理</text></view></block><block wx:if="{{auth_data.restaurant_deposit}}"><view class="meun-list-options flex-col flex-x-center flex-y-center" data-url="../restaurant/depositorder" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image class="menu-img" src="{{pre_url+'/static/img/adminExt/wm2.png'}}"></image><text class="menu-text">寄存订单</text></view></block></block></view></view></block></block><block wx:if="{{showmdmoney==1&&(auth_data.mendian_mdmoneylog||auth_data.mendian_mdwithdraw||auth_data.mendian_mdwithdrawlog)}}"><block><view class="menu-manage flex-col"><view class="menu-title">门店余额</view><view class="menu-list width"><block><block wx:if="{{auth_data.mendian_mdmoneylog}}"><view class="meun-list-options flex-col flex-x-center flex-y-center" data-url="../finance/mdmoneylog" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image class="menu-img" src="{{pre_url+'/static/img/adminExt/financenbg7.png'}}"></image><text class="menu-text">余额明细</text></view></block><block wx:if="{{auth_data.mendian_mdwithdraw}}"><view class="meun-list-options flex-col flex-x-center flex-y-center" data-url="../finance/mdwithdraw" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image class="menu-img" src="{{pre_url+'/static/img/adminExt/financenbg8.png'}}"></image><text class="menu-text">余额提现</text></view></block><block wx:if="{{auth_data.mendian_mdwithdrawlog}}"><view class="meun-list-options flex-col flex-x-center flex-y-center" data-url="../finance/mdwithdrawlog" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image class="menu-img" src="{{pre_url+'/static/img/adminExt/financenbg8.png'}}"></image><text class="menu-text">提现记录</text></view></block></block></view></view></block></block><block wx:if="{{shopDataShow}}"><view class="menu-manage"><view class="divider-div"></view><view class="tab-div flex-y-center"><block wx:if="{{auth_data.order&&(showcollageorder||showkanjiaorder||showseckillorder||showscoreshoporder||showluckycollageorder||showyuyueorder||showCycleorder)}}"><view data-event-opts="{{[['tap',[['tabChange',[1]]]]]}}" class="tab-options flex-col" bindtap="__e"><view class="{{[(tabIndex==1)?'tab-options-active':'']}}">商城订单</view><block wx:if="{{tabIndex==1}}"><view class="color-bar"></view></block></view></block><block wx:if="{{auth_data.restaurant_takeaway||auth_data.restaurant_shop||auth_data.restaurant_booking||auth_data.restaurant_queue||auth_data.restaurant_deposit}}"><view data-event-opts="{{[['tap',[['tabChange',[2]]]]]}}" class="tab-options flex-col" bindtap="__e"><view class="{{[(tabIndex==2)?'tab-options-active':'']}}">餐饮订单</view><block wx:if="{{tabIndex==2}}"><view class="color-bar"></view></block></view></block><block wx:if="{{auth_data.restaurant_product||auth_data.restaurant_tableWaiter}}"><view data-event-opts="{{[['tap',[['tabChange',[0]]]]]}}" class="tab-options flex-col" bindtap="__e"><view class="{{[(tabIndex==0)?'tab-options-active':'']}}">餐饮数据</view><block wx:if="{{tabIndex==0}}"><view class="color-bar"></view></block></view></block><block wx:if="{{auth_data.hotel_order}}"><view data-event-opts="{{[['tap',[['tabChange',[4]]]]]}}" class="tab-options flex-col" bindtap="__e"><view class="{{[(tabIndex==4)?'tab-options-active':'']}}">{{hotel.text['酒店']+"订单"}}</view><block wx:if="{{tabIndex==4}}"><view class="color-bar"></view></block></view></block></view><block wx:if="{{auth_data.restaurant_product||auth_data.restaurant_tableWaiter}}"><block><block wx:if="{{tabIndex==0}}"><view class="data-div flex-col"><view class="data-div-list"><block wx:if="{{auth_data.restaurant_product}}"><view class="data-div-options" data-url="../restaurant/product/index" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="title-text">菜品</view><view class="num-text">{{restaurant_product_count}}</view></view></block><block wx:if="{{auth_data.restaurant_product}}"><view class="border-bar-div"></view></block><block wx:if="{{auth_data.restaurant_tableWaiter}}"><view class="data-div-options" data-url="../restaurant/tableWaiter" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="title-text">餐桌</view><view class="num-text">{{restaurant_table_count}}</view></view></block></view></view></block></block></block><block wx:if="{{auth_data.order}}"><block><block wx:if="{{tabIndex==1}}"><view class="data-div flex-col"><view class="data-div-list"><block wx:if="{{showcollageorder}}"><view class="data-div-options" data-url="../order/collageorder" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="title-text">拼团订单</view><view class="num-text">{{collageCount}}</view></view></block><block wx:if="{{showcollageorder}}"><view class="border-bar-div"></view></block><block wx:if="{{showkanjiaorder}}"><view class="data-div-options" data-url="../order/kanjiaorder" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="title-text">砍价订单</view><view class="num-text">{{kanjiaCount}}</view></view></block><block wx:if="{{showkanjiaorder}}"><view class="border-bar-div"></view></block><block wx:if="{{showseckillorder}}"><view class="data-div-options" data-url="../order/seckillorder" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="title-text">秒杀订单</view><view class="num-text">{{seckillCount}}</view></view></block><block wx:if="{{showseckillorder}}"><view class="border-bar-div"></view></block><block wx:if="{{showtuangouorder}}"><view class="data-div-options" data-url="../order/tuangouorder" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="title-text">团购订单</view><view class="num-text">{{tuangouCount}}</view></view></block><block wx:if="{{showtuangouorder}}"><view class="border-bar-div"></view></block><block wx:if="{{showscoreshoporder}}"><view class="data-div-options" data-url="../order/scoreshoporder" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="title-text">{{$root.m4+"商城订单"}}</view><view class="num-text">{{scoreshopCount}}</view></view></block><block wx:if="{{showscoreshoporder}}"><view class="border-bar-div"></view></block><block wx:if="{{showluckycollageorder}}"><view class="data-div-options" data-url="../order/luckycollageorder" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="title-text">幸运拼团订单</view><view class="num-text">{{luckycollageCount}}</view></view></block><block wx:if="{{showluckycollageorder}}"><view class="border-bar-div"></view></block><block wx:if="{{showyuyueorder}}"><view class="data-div-options" data-url="../order/yuyueorder" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="title-text">预约订单</view><view class="num-text">{{yuyueorderCount}}</view></view></block><block wx:if="{{showyuyueorder}}"><view class="border-bar-div"></view></block><block wx:if="{{showCycleorder}}"><view class="data-div-options" data-url="../order/cycleorder" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="title-text">周期购订单</view><view class="num-text">{{cycleCount}}</view></view></block></view></view></block></block></block><block wx:if="{{auth_data.restaurant_takeaway||auth_data.restaurant_shop||auth_data.restaurant_booking||auth_data.restaurant_queue||auth_data.restaurant_deposit}}"><block><block wx:if="{{tabIndex==2}}"><view class="data-div flex-col"><view class="data-div-list"><block wx:if="{{auth_data.restaurant_takeaway}}"><view class="data-div-options" data-url="../restaurant/takeawayorder" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="title-text">外卖订单</view><view class="num-text">{{restaurant_takeaway_count}}</view></view></block><block wx:if="{{auth_data.restaurant_takeaway}}"><view class="border-bar-div"></view></block><block wx:if="{{auth_data.restaurant_shop}}"><view class="data-div-options" data-url="../restaurant/shoporder" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="title-text">点餐订单</view><view class="num-text">{{restaurant_shop_count}}</view></view></block><block wx:if="{{auth_data.restaurant_shop}}"><view class="border-bar-div"></view></block><block wx:if="{{auth_data.restaurant_booking}}"><view class="data-div-options" data-url="../restaurant/bookingorder" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="title-text">预定订单</view><view class="num-text">{{restaurant_booking_count}}</view></view></block><block wx:if="{{auth_data.restaurant_booking}}"><view class="border-bar-div"></view></block><block wx:if="{{auth_data.restaurant_queue}}"><view class="data-div-options" data-url="../restaurant/queue" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="title-text">排队叫号</view><view class="num-text">{{restaurant_queue}}</view></view></block><block wx:if="{{auth_data.restaurant_queue}}"><view class="border-bar-div"></view></block><block wx:if="{{auth_data.restaurant_deposit}}"><view class="data-div-options" data-url="../restaurant/depositorder" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="title-text">寄存订单</view><view class="num-text">{{restaurant_deposit}}</view></view></block></view></view></block></block></block><block wx:if="{{auth_data.hotel_order}}"><block><block wx:if="{{tabIndex==4}}"><view class="data-div flex-col"><view class="data-div-list hotelorder"><block wx:if="{{auth_data.hotel_order}}"><view class="data-div-options" data-url="/adminExt/hotel/orderlist?st=all" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="title-text">全部</view><view class="num-text">{{hotel.hotelCount}}</view></view></block><block wx:if="{{auth_data.hotel_order}}"><view class="data-div-options" data-url="/adminExt/hotel/orderlist?st=1" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="title-text">待确认</view><view class="num-text">{{hotel.hotelOrderCount1}}</view></view></block><block wx:if="{{auth_data.hotel_order}}"><view class="data-div-options" data-url="/adminExt/hotel/orderlist?st=2" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="title-text">待入住</view><view class="num-text">{{hotel.hotelOrderCount2}}</view></view></block><block wx:if="{{auth_data.hotel_order}}"><view class="data-div-options" data-url="/adminExt/hotel/orderlist?st=3" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="title-text">已到店</view><view class="num-text">{{hotel.hotelOrderCount3}}</view></view></block><block wx:if="{{auth_data.hotel_order}}"><view class="data-div-options" data-url="/adminExt/hotel/orderlist?st=4" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="title-text">已离店</view><view class="num-text">{{hotel.hotelOrderCount4}}</view></view></block></view></view></block></block></block></view></block><block wx:if="{{false}}"><view class="menu-manage flex-col"><view class="menu-title">商家数据</view><view class="mer-list"><view class="merchant-view cysj-text" style="{{'background:'+('url('+pre_url+'/static/img/adminExt/merbg1.png)')+';'+('background-size:'+('cover')+';')+('background-repeat:'+('no-repeat')+';')}}"><view class="mer-title">餐饮数据</view><block><block wx:if="{{auth_data.restaurant_product}}"><view class="mer-options" data-url="../restaurant/product/index" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">菜品列表:<text>{{restaurant_product_count}}</text></view></block><block wx:if="{{auth_data.restaurant_tableWaiter}}"><view class="mer-options" data-url="../restaurant/tableWaiter" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">餐桌管理:<text>{{restaurant_table_count}}</text></view></block></block></view><view class="merchant-view" style="{{'background:'+('url('+pre_url+'/static/img/adminExt/merbg2.png)')+';'+('background-size:'+('cover')+';')+('background-repeat:'+('no-repeat')+';')}}"><view class="mer-title">商城订单</view><scroll-view class="scroll-Y scdd-text" scroll-y="true"><block wx:if="{{auth_data.order}}"><block><block wx:if="{{showcollageorder}}"><view class="mer-options" data-url="../order/collageorder" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">拼团订单:<text>{{collageCount}}</text></view></block><block wx:if="{{showkanjiaorder}}"><view class="mer-options" data-url="../order/kanjiaorder" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">砍价订单:<text>{{kanjiaCount}}</text></view></block><block wx:if="{{showseckillorder}}"><view class="mer-options" data-url="../order/seckillorder" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">秒杀订单:<text>{{seckillCount}}</text></view></block><block wx:if="{{showtuangouorder}}"><view class="mer-options" data-url="../order/tuangouorder" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">团购订单:<text>{{tuangouCount}}</text></view></block><block wx:if="{{showscoreshoporder}}"><view class="mer-options" data-url="../order/scoreshoporder" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">{{$root.m5+"商城订单:"}}<text>{{scoreshopCount}}</text></view></block><block wx:if="{{showluckycollageorder}}"><view class="mer-options" data-url="../order/luckycollageorder" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">幸运拼团订单:<text>{{luckycollageCount}}</text></view></block><block wx:if="{{showyuyueorder}}"><view class="mer-options" data-url="../order/yuyueorder" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">预约订单:<text>{{yuyueorderCount}}</text></view></block><block wx:if="{{showCycleorder}}"><view class="mer-options" data-url="../order/cycleorder" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">周期购订单:<text>{{cycleCount}}</text></view></block></block></block><block wx:if="{{scoreshop_product}}"><block></block></block></scroll-view></view><view class="merchant-view" style="{{'background:'+('url('+pre_url+'/static/img/adminExt/merbg3.png)')+';'+('background-size:'+('cover')+';')+('background-repeat:'+('no-repeat')+';')}}"><view class="mer-title">外卖订单</view><scroll-view class="scroll-Y wmdd-text" scroll-y="true"><block wx:if="{{auth_data.restaurant_takeaway}}"><view class="mer-options" data-url="../restaurant/takeawayorder" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">外卖订单:<text>{{restaurant_takeaway_count}}</text></view></block><block wx:if="{{auth_data.restaurant_shop}}"><view class="mer-options" data-url="../restaurant/shoporder" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">点餐订单:<text>{{restaurant_shop_count}}</text></view></block><block wx:if="{{auth_data.restaurant_booking}}"><view class="mer-options" data-url="../restaurant/bookingorder" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">预定订单:<text>{{restaurant_booking_count}}</text></view></block><block wx:if="{{auth_data.restaurant_queue}}"><view class="mer-options" data-url="../restaurant/queue" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">排队叫号:<text>{{restaurant_queue}}</text></view></block><block wx:if="{{auth_data.restaurant_deposit}}"><view class="mer-options" data-url="../restaurant/depositorder" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">寄存订单:<text>{{restaurant_deposit}}</text></view></block></scroll-view></view></view></view></block><view class="menu-manage flex-col"><view class="menu-list width"><block wx:if="{{$root.m6}}"><view class="meun-list-options flex-col flex-x-center flex-y-center" data-url="/adminExt/coupon/index" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image class="menu-img" src="{{pre_url+'/static/img/adminExt/wm5.png'}}"></image><text class="menu-text">{{$root.m7}}</text></view></block><block wx:if="{{$root.m8}}"><view class="meun-list-options flex-col flex-x-center flex-y-center" data-url="/adminExt/coupon/restaurantList" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image class="menu-img" src="{{pre_url+'/static/img/adminExt/wm5.png'}}"></image><text class="menu-text">餐饮优惠券</text></view></block><block wx:if="{{auth_data.order}}"><block><block wx:if="{{showyuekeorder}}"><view class="meun-list-options flex-col flex-x-center flex-y-center" data-url="../order/yuekeorder" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image class="menu-img" src="{{pre_url+'/static/img/adminExt/wm5.png'}}"></image><text class="menu-text">约课记录</text></view></block><block wx:if="{{showformlog}}"><view class="meun-list-options flex-col flex-x-center flex-y-center" data-url="../form/formlog" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image class="menu-img" src="{{pre_url+'/static/img/adminExt/wm8.png'}}"></image><text class="menu-text">表单提交记录</text></view></block><block wx:if="{{showmaidanlog}}"><view class="meun-list-options flex-col flex-x-center flex-y-center" data-url="/adminExt/order/maidanindex" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image class="menu-img" src="{{pre_url+'/static/img/adminExt/wm9.png'}}"></image><text class="menu-text">买单统计</text></view></block></block></block><block wx:if="{{$root.m9}}"><view class="meun-list-options flex-col flex-x-center flex-y-center" data-url="/adminExt/huodongbaoming/order" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image class="menu-img" src="{{pre_url+'/static/img/adminExt/wm9.png'}}"></image><text class="menu-text">活动报名订单</text></view></block><block wx:if="{{custom.showHealth}}"><view class="meun-list-options flex-col flex-x-center flex-y-center" data-url="/adminExt/health/record" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image class="menu-img" src="{{pre_url+'/static/img/adminExt/wm5.png'}}"></image><text class="menu-text">量表填写记录</text></view></block><block wx:if="{{showworkorder}}"><view class="meun-list-options flex-col flex-x-center flex-y-center" data-url="../workorder/category" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image class="menu-img" src="{{pre_url+'/static/img/adminExt/wm5.png'}}"></image><text class="menu-text">工单</text></view></block><block wx:if="{{scoreshop_product}}"><view class="meun-list-options flex-col flex-x-center flex-y-center" data-url="../scoreproduct/index" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image class="menu-img" src="{{pre_url+'/static/img/adminExt/wm5.png'}}"></image><text class="menu-text">兑换商品列表</text></view></block><block wx:if="{{$root.m10}}"><view class="meun-list-options flex-col flex-x-center flex-y-center" data-url="../member/code" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image class="menu-img" src="{{pre_url+'/static/img/adminExt/wm6.png'}}"></image><text class="menu-text">会员消费</text></view></block><block wx:if="{{showbusinessqr}}"><view class="meun-list-options flex-col flex-x-center flex-y-center" data-url="businessqr" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image class="menu-img" src="{{pre_url+'/static/img/adminExt/wm5.png'}}"></image><text class="menu-text">推广码</text></view></block><block wx:if="{{showworkadd}}"><view class="meun-list-options flex-col flex-x-center flex-y-center" data-url="{{'/pagesA/workorder/category?bid='+uinfo.bid}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image class="menu-img" src="{{pre_url+'/static/img/adminExt/wm5.png'}}"></image><text class="menu-text">工单提交</text></view></block><block wx:if="{{auth_data.product&&add_product}}"><view class="meun-list-options flex-col flex-x-center flex-y-center" data-url="../product/edit" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image class="menu-img" src="{{pre_url+'/static/img/adminExt/wm7.png'}}"></image><text class="menu-text">添加商品</text></view></block><block wx:if="{{scoreshop_product}}"><view class="meun-list-options flex-col flex-x-center flex-y-center" data-url="../scoreproduct/edit" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image class="menu-img" src="{{pre_url+'/static/img/adminExt/wm5.png'}}"></image><text class="menu-text">添加兑换商品</text></view></block><block wx:if="{{auth_data.order}}"><block><block wx:if="{{searchmember}}"><view class="meun-list-options flex-col flex-x-center flex-y-center" data-url="/activity/searchmember/searchmember" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image class="menu-img" src="{{pre_url+'/static/img/adminExt/wm5.png'}}"></image><text class="menu-text">一键查看</text></view></block></block></block><block wx:if="{{$root.m11}}"><view class="meun-list-options flex-col flex-x-center flex-y-center" data-url="../yingxiao/queueFree" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image class="menu-img" src="{{pre_url+'/static/img/adminExt/wm5.png'}}"></image><text class="menu-text">排队记录</text></view></block><block wx:if="{{$root.m12}}"><view class="meun-list-options flex-col flex-x-center flex-y-center" data-url="/adminExt/queuefree/queueFreeSet" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image class="menu-img" src="{{pre_url+'/static/img/adminExt/wm5.png'}}"></image><text class="menu-text">排队设置</text></view></block><block wx:if="{{show_categroy_business}}"><block><block wx:if="{{auth_data.product}}"><view class="meun-list-options flex-col flex-x-center flex-y-center" data-url="../product/category2/index" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image class="menu-img" src="{{pre_url+'/static/img/adminExt/wm2.png'}}"></image><text class="menu-text">商品分类</text></view></block><block wx:if="{{auth_data.product}}"><view class="meun-list-options flex-col flex-x-center flex-y-center" data-url="../product/category2/edit?id=" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image class="menu-img" src="{{pre_url+'/static/img/adminExt/wm1.png'}}"></image><text class="menu-text">添加商品分类</text></view></block></block></block><block wx:if="{{$root.m13}}"><view class="meun-list-options flex-col flex-x-center flex-y-center" data-url="../order/dkorder" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image class="menu-img" src="{{pre_url+'/static/img/adminExt/wm5.png'}}"></image><text class="menu-text">代客下单</text></view></block><block wx:if="{{$root.m14}}"><view class="meun-list-options flex-col flex-x-center flex-y-center" data-url="/adminExt/shop/shopstock" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image class="menu-img" src="{{pre_url+'/static/img/adminExt/wm5.png'}}"></image><text class="menu-text">库存录入</text></view></block><block wx:if="{{$root.m15}}"><view class="meun-list-options flex-col flex-x-center flex-y-center" data-url="../business/index" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image class="menu-img" src="{{pre_url+'/static/img/adminExt/wm5.png'}}"></image><text class="menu-text">商家列表</text></view></block><block wx:if="{{auth_data.device_addstock}}"><view class="meun-list-options flex-col flex-x-center flex-y-center" data-url="/pagesB/adminExt/pickupdevice" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image class="menu-img" src="{{pre_url+'/static/img/adminExt/dishm6.png'}}"></image><text class="menu-text">商品柜设备</text></view></block><block wx:if="{{$root.m16}}"><view class="meun-list-options flex-col flex-x-center flex-y-center" data-url="/pagesC/invoicebaoxiao/adminrecordlist" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image class="menu-img" src="{{pre_url+'/static/img/adminExt/dishm8.png'}}"></image><text class="menu-text">发票报销记录</text></view></block><block wx:if="{{auth_data.qrcode_variable_maidan}}"><view class="meun-list-options flex-col flex-x-center flex-y-center" data-url="/pagesC/qrcodevar/index" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image class="menu-img" src="{{pre_url+'/static/img/adminExt/menu1.png'}}"></image><text class="menu-text">绑定收款码</text></view></block><block wx:if="{{$root.m17}}"><view class="meun-list-options flex-col flex-x-center flex-y-center" data-url="/adminExt/set/qrcodeShop" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image class="menu-img" src="{{pre_url+'/static/img/adminExt/menu1.png'}}"></image><text class="menu-text">店铺二维码</text></view></block></view></view><view class="tabbar"><view class="tabbar-bot"></view><view class="tabbar-bar" style="background-color:#ffffff;"><block wx:if="{{auth_data.member}}"><view class="tabbar-item" data-url="../member/index" data-opentype="reLaunch" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="tabbar-image-box"><image class="tabbar-icon" src="{{pre_url+'/static/img/adminExt/member.png?v=1'}}"></image></view><view class="tabbar-text">{{$root.m18}}</view></view></block><block wx:if="{{auth_data.zixun}}"><view class="tabbar-item" data-url="../kefu/index" data-opentype="reLaunch" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="tabbar-image-box"><image class="tabbar-icon" src="{{pre_url+'/static/img/adminExt/zixun.png?v=1'}}"></image></view><view class="tabbar-text">咨询</view></view></block><block wx:if="{{auth_data.finance}}"><view class="tabbar-item" data-url="../finance/index" data-opentype="reLaunch" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="tabbar-image-box"><image class="tabbar-icon" src="{{pre_url+'/static/img/adminExt/finance.png?v=1'}}"></image></view><view class="tabbar-text">财务</view></view></block><view class="tabbar-item" data-url="../index/index" data-opentype="reLaunch" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="tabbar-image-box"><image class="tabbar-icon" src="{{pre_url+'/static/img/adminExt/my2.png?v=1'}}"></image></view><view class="tabbar-text active">我的</view></view></view></view></block></block><popmsg class="vue-ref" vue-id="4efcca20-1" data-ref="popmsg" bind:__l="__l"></popmsg><block wx:if="{{loading}}"><loading vue-id="4efcca20-2" bind:__l="__l"></loading></block></view>