<view class="calendar-page"><view class="header"><view class="month-selector"><view data-event-opts="{{[['tap',[['changeMonth',[-1]]]]]}}" class="selector-btn prev" bindtap="__e"><text>上一个月</text></view><view class="current-month">{{currentYear+"年"+currentMonth+"月"}}</view><view data-event-opts="{{[['tap',[['changeMonth',[1]]]]]}}" class="selector-btn next" bindtap="__e"><text>下一个月</text></view></view></view><view class="calendar"><view class="calendar-header"><block wx:for="{{weekdays}}" wx:for-item="day" wx:for-index="index" wx:key="index"><view class="weekday">{{day}}</view></block></view><view class="calendar-body"><block wx:for="{{$root.l0}}" wx:for-item="day" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" data-event-params="{{({day:day.$orig})}}" class="{{['calendar-day',(!day.$orig.date)?'empty':'',(day.$orig.isToday)?'today':'',(day.$orig.count>0)?'has-orders':'']}}" style="{{(day.$orig.isToday?'background-color:rgba('+day.m0+',0.1)':'')}}" bindtap="__e"><text class="day-number" style="{{(day.$orig.isToday?'color:'+day.m1:'')}}">{{day.$orig.day}}</text><block wx:if="{{day.$orig.count>0}}"><view class="order-dots"><block wx:if="{{day.$orig.status_count&&day.$orig.status_count['1']>0}}"><view class="dot status-1" style="{{('opacity:'+(day.$orig.status_count['1']>0?1:0.3))}}"></view></block><block wx:if="{{day.$orig.status_count&&day.$orig.status_count['2']>0}}"><view class="dot status-2" style="{{('opacity:'+(day.$orig.status_count['2']>0?1:0.3))}}"></view></block><block wx:if="{{day.$orig.status_count&&day.$orig.status_count['3']>0}}"><view class="dot status-3" style="{{('opacity:'+(day.$orig.status_count['3']>0?1:0.3))}}"></view></block><block wx:if="{{day.$orig.status_count&&(day.$orig.status_count['4']>0||day.$orig.status_count['5']>0)}}"><view class="dot status-4" style="{{('opacity:'+(day.$orig.status_count['4']>0||day.$orig.status_count['5']>0?1:0.3))}}"></view></block></view></block><block wx:if="{{day.$orig.count>0}}"><view class="order-count" style="{{('background-color:'+day.m2)}}">{{day.$orig.count}}</view></block></view></block></view></view><view class="legend"><view class="legend-item"><view class="dot status-1"></view><text>已支付</text></view><view class="legend-item"><view class="dot status-2"></view><text>已派单</text></view><view class="legend-item"><view class="dot status-3"></view><text>已完成</text></view><view class="legend-item"><view class="dot status-4"></view><text>评价</text></view></view><block wx:if="{{showOrderList}}"><view class="order-list-popup"><view class="popup-header"><text class="date-text">{{selectedDate}}</text><text data-event-opts="{{[['tap',[['closeOrderList',['$event']]]]]}}" class="close-btn" bindtap="__e">×</text></view><view class="status-filter"><block wx:for="{{$root.l1}}" wx:for-item="status" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['filterByStatus',['$0'],[[['statusOptions','',index,'value']]]]]]]}}" class="{{['filter-item',(selectedStatus===status.$orig.value)?'active':'']}}" style="{{(selectedStatus===status.$orig.value?'background-color:'+status.m3:'')}}" bindtap="__e">{{''+status.$orig.label+''}}</view></block></view><scroll-view class="order-list" scroll-y="{{true}}"><block wx:if="{{$root.g0===0}}"><view class="no-orders"><text>当天没有订单</text></view></block><block wx:for="{{$root.l2}}" wx:for-item="item" wx:for-index="index" wx:key="id"><block><view data-event-opts="{{[['tap',[['viewOrderDetail',['$0'],[[['dayOrders','id',item.$orig.id,'id']]]]]]]}}" class="order-box" bindtap="__e"><view class="head"><view class="f1"><image class="img" src="{{pre_url+'/static/peisong/ps_time.png'}}"></image><block wx:if="{{item.$orig.status==1}}"><text>已支付</text></block><block wx:else><block wx:if="{{item.$orig.status==2}}"><text>已派单</text></block><block wx:else><block wx:if="{{item.$orig.status==3}}"><text>已完成</text></block><block wx:else><block wx:if="{{item.$orig.status==4}}"><text>待评价</text></block><block wx:else><block wx:if="{{item.$orig.status==5}}"><text>已评价</text></block><block wx:else><text>未知状态</text></block></block></block></block></block><text class="t1" style="{{('color:'+item.m4)}}">{{item.$orig.yy_time}}</text></view><view class="flex1"></view><view class="f2"><text class="t1">{{item.$orig.product_price}}</text>元</view></view><view class="content"><view class="content-info"><view class="info-item"><text class="label">订单号：</text><text class="value">{{item.$orig.ordernum}}</text></view><view class="info-item"><text class="label">商品名称：</text><text class="value">{{item.$orig.product_name}}</text></view><view class="info-item"><text class="label">客户信息：</text><text class="value">{{item.$orig.member_name+" "+item.$orig.member_tel}}</text></view><view class="info-item"><text class="label">地址：</text><text class="value">{{item.$orig.address}}</text></view><view class="info-item"><text class="label">下单时间：</text><text class="value">{{item.$orig.create_time_formatted}}</text></view></view></view><view class="op"><block wx:if="{{item.$orig.status==1}}"><view class="t1">等待服务</view></block><block wx:if="{{item.$orig.status==2}}"><view class="t1">服务中</view></block><block wx:if="{{item.$orig.status==3}}"><view class="t1">已完成</view></block><block wx:if="{{item.$orig.status==4||item.$orig.status==5}}"><view class="t1">已评价</view></block><view class="flex1"></view><block wx:if="{{item.$orig.status==1}}"><view data-event-opts="{{[['tap',[['viewOrderDetail',['$0'],[[['dayOrders','id',item.$orig.id,'id']]]]]]]}}" class="btn1" style="{{('background:'+item.m5)}}" catchtap="__e">开始服务</view></block><block wx:if="{{item.$orig.status==2}}"><view data-event-opts="{{[['tap',[['viewOrderDetail',['$0'],[[['dayOrders','id',item.$orig.id,'id']]]]]]]}}" class="btn1" style="{{('background:'+item.m6)}}" catchtap="__e">完成服务</view></block><block wx:if="{{item.$orig.status==3}}"><view data-event-opts="{{[['tap',[['viewOrderDetail',['$0'],[[['dayOrders','id',item.$orig.id,'id']]]]]]]}}" class="btn1" style="{{('background:'+item.m7)}}" catchtap="__e">查看详情</view></block><block wx:if="{{item.$orig.status==4||item.$orig.status==5}}"><view data-event-opts="{{[['tap',[['viewOrderDetail',['$0'],[[['dayOrders','id',item.$orig.id,'id']]]]]]]}}" class="btn1" style="{{('background:'+item.m8)}}" catchtap="__e">查看评价</view></block></view></view></block></block></scroll-view></view></block><block wx:if="{{showComment}}"><view class="comment-popup"><view class="popup-header"><text class="title">评价详情</text><text data-event-opts="{{[['tap',[['closeComment',['$event']]]]]}}" class="close-btn" bindtap="__e">×</text></view><scroll-view class="comment-content" scroll-y="{{true}}"><block wx:if="{{!commentData||!commentData.worker_comment&&!commentData.product_comment}}"><view class="no-comment"><text>暂无评价信息</text></view></block><block wx:else><view><block wx:if="{{commentData.worker_comment}}"><view class="comment-section"><view class="section-title">服务评价</view><view class="comment-stars"><block wx:for="{{5}}" wx:for-item="n" wx:for-index="__i0__" wx:key="*this"><text class="{{['star',(n<=commentData.worker_comment.star)?'active':'']}}">★</text></block><text class="level-text">{{$root.m9}}</text></view><view class="comment-text">{{commentData.worker_comment.content}}</view><block wx:if="{{$root.g1}}"><view class="comment-images"><block wx:for="{{commentData.worker_comment.content_pic_array}}" wx:for-item="img" wx:for-index="idx" wx:key="idx"><image src="{{img}}" mode="aspectFill" data-event-opts="{{[['tap',[['previewImage',['$0',idx],['commentData.worker_comment.content_pic_array']]]]]}}" bindtap="__e"></image></block></view></block></view></block><block wx:if="{{commentData.product_comment}}"><view class="comment-section"><view class="section-title">商品评价</view><view class="comment-stars"><block wx:for="{{5}}" wx:for-item="n" wx:for-index="__i1__" wx:key="*this"><text class="{{['star',(n<=commentData.product_comment.score)?'active':'']}}">★</text></block></view><view class="comment-text">{{commentData.product_comment.content}}</view><block wx:if="{{$root.g2}}"><view class="comment-images"><block wx:for="{{commentData.product_comment.content_pic_array}}" wx:for-item="img" wx:for-index="idx" wx:key="idx"><image src="{{img}}" mode="aspectFill" data-event-opts="{{[['tap',[['previewImage',['$0',idx],['commentData.product_comment.content_pic_array']]]]]}}" bindtap="__e"></image></block></view></block><block wx:if="{{commentData.product_comment.reply_content}}"><view class="reply-section"><view class="reply-title" style="{{('color:'+$root.m10)}}">商家回复</view><view class="reply-text">{{commentData.product_comment.reply_content}}</view></view></block></view></block></view></block></scroll-view></view></block><view class="tabbar"><view class="tabbar-bot"></view><view class="tabbar-bar" style="background-color:#ffffff;"><view class="tabbar-item" data-url="dating" data-opentype="reLaunch" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="tabbar-image-box"><image class="tabbar-icon" src="{{pre_url+'/static/img/peisong/home.png'}}"></image></view><view class="tabbar-text">大厅</view></view><view class="tabbar-item" data-url="jdorderlist" data-opentype="reLaunch" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="tabbar-image-box"><image class="tabbar-icon" src="{{pre_url+'/static/img/peisong/order.png'}}"></image></view><view class="tabbar-text">订单</view></view><view class="tabbar-item" data-url="jdorderlist?st=3" data-opentype="reLaunch" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="tabbar-image-box"><image class="tabbar-icon" src="{{pre_url+'/static/img/peisong/orderwc.png'}}"></image></view><view class="tabbar-text">已完成</view></view><block wx:if="{{showform}}"><view class="tabbar-item" data-url="formlog" data-opentype="reLaunch" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="tabbar-image-box"><image class="tabbar-icon" src="{{pre_url+'/static/img/peisong/dangan.png'}}"></image></view><view class="tabbar-text">档案</view></view></block><view class="tabbar-item" data-url="calendar" data-opentype="reLaunch" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="tabbar-image-box"><image class="tabbar-icon" src="{{pre_url+'/static/img/peisong/calendar.png'}}"></image></view><view class="tabbar-text active" style="{{('color:'+$root.m11)}}">日历</view></view></view></view></view>