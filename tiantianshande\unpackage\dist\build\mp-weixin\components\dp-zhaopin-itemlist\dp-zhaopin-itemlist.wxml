<view style="width:100%;"><view class="dp-product-zhaopin"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="id"><view class="item" data-url="{{'/zhaopin/partdetails?id='+item.$orig[zwid]}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="item1 flex"><view class="product-pic"><image class="image" src="{{item.$orig.thumb}}" mode="aspectFit"></image><block wx:if="{{item.$orig.apply_tag}}"><text class="renzheng">担保企业</text></block></view><view class="product-info flex1"><view class="p1">{{item.$orig.title}}</view><view class="p2">{{item.$orig.salary}}</view><view class="p3"><view class="flex"><block wx:if="{{item.$orig.cname}}"><view class="tagitem">{{item.$orig.cname}}</view></block><block wx:if="{{item.$orig.num}}"><view class="tagitem">{{item.$orig.num+"人"}}</view></block><block wx:if="{{item.$orig.experience}}"><view class="tagitem">{{item.$orig.experience}}</view></block><block wx:if="{{item.$orig.education}}"><view class="tagitem">{{item.$orig.education}}</view></block></view></view><block wx:if="{{showaddress&&item.$orig.area}}"><view class="p4 flex-s"><image src="../../static/img/address3.png"></image><text>{{item.$orig.area}}</text></view></block></view></view><block wx:if="{{item.g0}}"><view class="item2 flex"><block wx:for="{{item.$orig.welfare}}" wx:for-item="wf" wx:for-index="wk" wx:key="wk"><view class="tagitem">{{wf}}</view></block></view></block></view></block></view></view>