<view class="container"><block wx:if="{{isload}}"><block><form data-event-opts="{{[['submit',[['formSubmit',['$event']]]]]}}" bindsubmit="__e"><view class="mymoney" style="{{'background:'+($root.m0)+';'}}"><view class="f1">{{"可用"+$root.m1}}</view><view class="f2"><text style="font-size:26rpx;">￥</text>{{userinfo.duihuanfen}}</view><view class="f3" data-url="/pagesExb/money/moneylog?st=11" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><text>{{$root.m2+"明细"}}</text><text class="iconfont iconjiantou" style="font-size:20rpx;"></text></view></view><button data-event-opts="{{[['tap',[['formSubmit',['$event']]]]]}}" class="btn" style="{{'background:'+($root.m3)+';'}}" bindtap="__e">兑换账号</button><button data-event-opts="{{[['tap',[['formSubmit2',['$event']]]]]}}" class="btn" style="{{'background:'+($root.m4)+';'}}" bindtap="__e">收集收益</button><view class="content"><view class="info-item"><view>兑换账号信息</view></view><view class="info-item"><view class="t1">账号id</view><view class="t2">收益</view><view class="t2">轮</view></view><block wx:for="{{datalist}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="info-item"><view class="t1">{{item.id}}</view><view class="t2">{{item.yongjin}}</view><view class="t2">{{item.lunshu}}</view></view></block></view></form></block></block><block wx:if="{{loading}}"><loading vue-id="00399ee6-1" bind:__l="__l"></loading></block><dp-tabbar vue-id="00399ee6-2" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="00399ee6-3" data-ref="popmsg" bind:__l="__l"></popmsg></view>