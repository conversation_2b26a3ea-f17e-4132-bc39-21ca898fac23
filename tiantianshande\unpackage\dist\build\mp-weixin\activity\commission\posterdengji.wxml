<view class="container"><block wx:if="{{isload}}"><block><block wx:if="{{postercount>1}}"><block><view class="arrowleft" data-type="-1" data-event-opts="{{[['tap',[['changeposter',['$event']]]]]}}" bindtap="__e"><text class="iconfont iconjiantou"></text></view><view class="arrowright" data-type="1" data-event-opts="{{[['tap',[['changeposter',['$event']]]]]}}" bindtap="__e"><text class="iconfont iconjiantou"></text></view></block></block><image class="sjew-img" src="{{poster}}" data-url="{{poster}}" mode="widthFix" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image><view class="sjew-box"><view class="sjew-tle flex-y-center">{{"如何推荐好友拿"+$root.m0}}</view><view class="sjew-sp1"><text>{{guize}}</text></view></view><view class="sjew-he"></view><view class="{{['sjew-bottom',menuindex>-1?'tabbarbot':'notabbarbot']}}"><view class="sjew-bottom-content"><view class="sjew-bot-a2" style="{{'background:'+($root.m1)+';'}}" data-url="{{poster}}" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e">分享图片</view></view></view></block></block><block wx:if="{{loading}}"><loading vue-id="c0724ce4-1" bind:__l="__l"></loading></block><dp-tabbar vue-id="c0724ce4-2" opt="{{opt}}" data-event-opts="{{[['^getmenuindex',[['getmenuindex']]]]}}" bind:getmenuindex="__e" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="c0724ce4-3" data-ref="popmsg" bind:__l="__l"></popmsg></view>