<view class="container"><block wx:if="{{isload}}"><block><view class="orderinfo"><view class="item"><text class="t1">申请人</text><text class="flex1"></text><image style="width:80rpx;height:80rpx;margin-right:8rpx;" src="{{detail.headimg}}"></image><text style="height:80rpx;line-height:80rpx;">{{detail.nickname?detail.nickname:''}}</text></view><view class="item"><text class="t1">姓名</text><text class="t2">{{detail.name}}</text></view><view class="item"><text class="t1">电话</text><text class="t2">{{detail.tel?detail.tel:''}}</text></view><view class="item"><text class="t1">社区名称</text><text class="t2">{{detail.xqname}}</text></view><view class="item"><text class="t1">详细地址</text><text class="t2">{{detail.province+detail.city+detail.district+detail.street}}</text></view><view class="item"><text class="t1">申请时间</text><text class="t2">{{detail.createtime}}</text></view></view><view class="orderinfo"><view class="item"><text class="t1">状态</text><block wx:if="{{detail.check_status==0}}"><text class="t2 st0">待审核</text></block><block wx:if="{{detail.check_status==1}}"><text class="t2 st1">已通过</text></block><block wx:if="{{detail.check_status==2}}"><text class="t2 st2">已驳回</text></block></view><block wx:if="{{detail.check_status==2}}"><view class="item"><text class="t1">驳回原因</text><block wx:if="{{detail.reason}}"><text class="t2">{{detail.reason}}</text></block></view></block></view><view style="width:100%;height:160rpx;"></view><block wx:if="{{detail.check_status==0}}"><view class="bottom notabbarbot"><block wx:if="{{detail.check_status==0}}"><view class="btn2" data-id="{{detail.id}}" data-st="2" data-event-opts="{{[['tap',[['checkstatus',['$event']]]]]}}" bindtap="__e">驳回</view></block><block wx:if="{{detail.check_status==0}}"><view class="btn2" data-id="{{detail.id}}" data-st="1" data-event-opts="{{[['tap',[['checkstatus',['$event']]]]]}}" bindtap="__e">通过</view></block></view></block><uni-popup class="vue-ref" vue-id="6f94502f-1" id="dialogSetremark" type="dialog" data-ref="dialogSetremark" bind:__l="__l" vue-slots="{{['default']}}"><uni-popup-dialog vue-id="{{('6f94502f-2')+','+('6f94502f-1')}}" mode="input" title="驳回原因" value="{{detail.remark}}" placeholder="请输入驳回原因" data-event-opts="{{[['^confirm',[['setremarkconfirm']]]]}}" bind:confirm="__e" bind:__l="__l"></uni-popup-dialog></uni-popup></block></block><block wx:if="{{loading}}"><loading vue-id="6f94502f-3" bind:__l="__l"></loading></block><dp-tabbar vue-id="6f94502f-4" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="6f94502f-5" data-ref="popmsg" bind:__l="__l"></popmsg></view>