<view class="container"><block wx:if="{{isload}}"><block><view class="merchant-info-card"><view class="merchant-header"><view class="merchant-avatar"><image class="avatar-img" src="{{merchant.logo||'/static/img/default_merchant.png'}}"></image></view><view class="merchant-info"><view class="merchant-name">{{merchant.name}}</view><view class="merchant-category">{{merchant.category_name||'未分类'}}</view><view class="merchant-status"><text class="status-badge" style="{{'background:'+(merchant.status_color)+';'}}">{{''+merchant.status_text+''}}</text></view></view></view><view class="merchant-details"><block wx:if="{{merchant.address}}"><view class="detail-item"><text class="detail-label">地址：</text><text class="detail-value">{{merchant.address}}</text></view></block><block wx:if="{{merchant.phone}}"><view class="detail-item"><text class="detail-label">电话：</text><text class="detail-value">{{merchant.phone}}</text></view></block><block wx:if="{{merchant.description}}"><view class="detail-item"><text class="detail-label">简介：</text><text class="detail-value">{{merchant.description}}</text></view></block><view class="detail-item"><text class="detail-label">注册时间：</text><text class="detail-value">{{merchant.createtime_format}}</text></view><view class="detail-item"><text class="detail-label">账户余额：</text><text class="detail-value highlight">{{"¥"+merchant.money}}</text></view></view><view class="action-buttons"><block wx:if="{{merchant.phone}}"><view data-event-opts="{{[['tap',[['callMerchant',['$event']]]]]}}" class="action-btn primary" bindtap="__e"><text class="iconfont icondianhua"></text><text>拨打电话</text></view></block><block wx:if="{{merchant.longitude&&merchant.latitude}}"><view data-event-opts="{{[['tap',[['viewLocation',['$event']]]]]}}" class="action-btn" bindtap="__e"><text class="iconfont iconweizhi"></text><text>查看位置</text></view></block></view></view><view class="stats-container"><view class="stats-title">经营数据</view><view class="stats-grid"><view class="stat-card"><text class="stat-number">{{statistics.today_orders}}</text><text class="stat-label">今日订单</text></view><view class="stat-card"><text class="stat-number">{{"¥"+statistics.today_amount}}</text><text class="stat-label">今日交易额</text></view><view class="stat-card"><text class="stat-number">{{statistics.month_orders}}</text><text class="stat-label">本月订单</text></view><view class="stat-card"><text class="stat-number">{{"¥"+statistics.month_amount}}</text><text class="stat-label">本月交易额</text></view><view class="stat-card"><text class="stat-number">{{statistics.total_orders}}</text><text class="stat-label">总订单数</text></view><view class="stat-card"><text class="stat-number">{{"¥"+statistics.total_amount}}</text><text class="stat-label">总交易额</text></view></view></view><block wx:if="{{$root.g0>0}}"><view class="recent-orders"><view class="section-header"><text class="section-title">最近订单</text><text class="section-more" data-url="{{'merchant_orders?merchant_id='+merchant.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">查看更多</text></view><view class="order-list"><block wx:for="{{recent_orders}}" wx:for-item="order" wx:for-index="index" wx:key="index"><view class="order-item"><view class="order-header"><view class="order-user"><image class="user-avatar" src="{{order.headimg||'/static/img/default_avatar.png'}}"></image><text class="user-name">{{order.nickname||'用户'}}</text></view><view class="order-status"><text class="status-text">{{order.status_text}}</text></view></view><view class="order-content"><view class="order-info"><text class="order-sn">{{"订单号："+order.ordersn}}</text><text class="order-time">{{order.createtime_format}}</text></view><view class="order-amount"><text class="amount-text">{{"¥"+order.totalprice}}</text></view></view></view></block></view></view></block><block wx:if="{{$root.g1===0}}"><view class="empty-orders"><text class="empty-text">暂无订单记录</text></view></block></block></block><block wx:if="{{loading}}"><loading vue-id="67da3a17-1" bind:__l="__l"></loading></block><dp-tabbar vue-id="67da3a17-2" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="67da3a17-3" data-ref="popmsg" bind:__l="__l"></popmsg></view>