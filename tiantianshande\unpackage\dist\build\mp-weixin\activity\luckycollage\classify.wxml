<view class="container"><block wx:if="{{isload}}"><block><view class="search-container" data-url="{{'prolist?bid='+bid}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e"><view class="search-box"><image class="img" src="/static/img/search_ico.png"></image><view class="search-text">搜索感兴趣的商品</view></view></view><view class="content-container"><view class="nav_left"><view class="{{['nav_left_items '+(curIndex==-1?'active':'')]}}" data-index="-1" data-id="0" data-event-opts="{{[['tap',[['switchRightTab',['$event']]]]]}}" bindtap="__e"><view class="before" style="{{'background:'+($root.m0)+';'}}"></view>全部</view><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><view class="{{['nav_left_items '+(curIndex==index?'active':'')]}}" data-index="{{index}}" data-id="{{item.$orig.id}}" data-event-opts="{{[['tap',[['switchRightTab',['$event']]]]]}}" bindtap="__e"><view class="before" style="{{'background:'+(item.m1)+';'}}"></view>{{item.$orig.name}}</view></block></block></view><view class="nav_right"><view class="nav_right-content"><view class="nav-pai"><view class="nav-paili" style="{{(!field||field=='sort'?'color:'+$root.m2:'')}}" data-field="sort" data-order="desc" data-event-opts="{{[['tap',[['changeOrder',['$event']]]]]}}" bindtap="__e">综合</view><view class="nav-paili" style="{{(field=='sales'?'color:'+$root.m3:'')}}" data-field="sales" data-order="desc" data-event-opts="{{[['tap',[['changeOrder',['$event']]]]]}}" bindtap="__e">销量</view><view class="nav-paili" data-field="sell_price" data-order="{{order=='asc'?'desc':'asc'}}" data-event-opts="{{[['tap',[['changeOrder',['$event']]]]]}}" bindtap="__e"><text style="{{(field=='sell_price'?'color:'+$root.m4:'')}}">价格</text><text class="iconfont iconshangla" style="{{(field=='sell_price'&&order=='asc'?'color:'+$root.m5:'')}}"></text><text class="iconfont icondaoxu" style="{{(field=='sell_price'&&order=='desc'?'color:'+$root.m6:'')}}"></text></view></view><block wx:if="{{$root.g0}}"><view class="classify-ul"><view class="flex" style="width:100%;overflow-y:hidden;overflow-x:scroll;"><view class="classify-li" style="{{(curIndex2==-1?'color:'+$root.m7+';background:rgba('+$root.m8+',0.2)':'')}}" data-id="{{clist[curIndex].id}}" data-index="-1" data-event-opts="{{[['tap',[['changeCTab',['$event']]]]]}}" bindtap="__e">全部</view><block wx:for="{{$root.l1}}" wx:for-item="item" wx:for-index="idx2" wx:key="idx2"><block><view class="classify-li" style="{{(curIndex2==idx2?'color:'+item.m9+';background:rgba('+item.m10+',0.2)':'')}}" data-id="{{item.$orig.id}}" data-index="{{idx2}}" data-event-opts="{{[['tap',[['changeCTab',['$event']]]]]}}" bindtap="__e">{{item.$orig.name}}</view></block></block></view></view></block><scroll-view class="classify-box" scroll-y="true" data-event-opts="{{[['scrolltolower',[['scrolltolower',['$event']]]]]}}" bindscrolltolower="__e"><view class="product-itemlist"><block wx:for="{{$root.l2}}" wx:for-item="item" wx:for-index="index" wx:key="id"><view class="item" data-url="{{''+(item.$orig.linktype==1?'product2':'product')+'?id='+item.$orig.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="flex"><view class="product-pic"><image class="image" src="{{item.$orig.pic}}" mode="widthFix"></image></view><view class="product-info"><view class="p1"><text class="team_text">{{item.$orig.teamnum+"人拼"}}</text><text class="name_1">{{item.$orig.name}}</text></view><view class="p2 flex"><view><text class="t1" style="{{'color:'+(item.m11)+';'}}"><text style="font-size:20rpx;padding-right:1px;">￥</text>{{item.$orig.sell_price}}</text><block wx:if="{{item.$orig.market_price*1>item.$orig.sell_price*1}}"><text class="t2">{{"￥"+item.$orig.market_price}}</text></block></view><block wx:if="{{item.$orig.sales>0}}"><view class="p3-1"><text style="overflow:hidden;">{{"已拼"+item.$orig.sales}}</text></view></block></view></view></view><view class="desc"><text>{{item.$orig.teamnum+"人拼团 "+item.$orig.gua_num+"人得商品"}}</text><block wx:if="{{item.$orig.linktype==1}}"><view><block wx:if="{{item.$orig.teamnum-item.$orig.gua_num>0&&item.$orig.bzjl_type==1}}"><text>{{item.$orig.teamnum-item.$orig.gua_num+"人"+(item.$orig.teamnum-item.$orig.gua_num>1?'各':'')+"得"+item.$orig.money+"元参与奖"}}</text></block><block wx:if="{{item.$orig.teamnum-item.$orig.gua_num>0&&item.$orig.bzjl_type==2}}"><text>{{item.$orig.teamnum-item.$orig.gua_num+"人"+(item.$orig.teamnum-item.$orig.gua_num>1?'各':'')+"得"+item.$orig.bzj_score+"积分"}}</text></block><block wx:if="{{item.$orig.teamnum-item.$orig.gua_num>0&&item.$orig.bzjl_type==3}}"><text>{{item.$orig.teamnum-item.$orig.gua_num+"人"+(item.$orig.teamnum-item.$orig.gua_num>1?'各':'')+"得"+item.$orig.bzj_commission+"元佣金"}}</text></block></view></block><block wx:else><view><block wx:if="{{item.$orig.teamnum-item.$orig.gua_num>0}}"><text>{{item.$orig.teamnum-item.$orig.gua_num+"人"+(item.$orig.teamnum-item.$orig.gua_num>1?'各':'')+"得"+item.$orig.money+"元参与奖"}}</text></block></view></block></view></view></block></view><block wx:if="{{nomore}}"><nomore vue-id="a6bf6f60-1" text="没有更多商品了" bind:__l="__l"></nomore></block><block wx:if="{{nodata}}"><nodata vue-id="a6bf6f60-2" text="暂无相关商品" bind:__l="__l"></nodata></block><view style="width:100%;height:100rpx;"></view></scroll-view></view></view></view><block wx:if="{{buydialogShow}}"><buydialog vue-id="a6bf6f60-3" proid="{{proid}}" menuindex="{{menuindex}}" data-event-opts="{{[['^buydialogChange',[['buydialogChange']]]]}}" bind:buydialogChange="__e" bind:__l="__l"></buydialog></block></block></block><block wx:if="{{loading}}"><loading vue-id="a6bf6f60-4" loadstyle="left:62.5%" bind:__l="__l"></loading></block><dp-tabbar vue-id="a6bf6f60-5" opt="{{opt}}" data-event-opts="{{[['^getmenuindex',[['getmenuindex']]]]}}" bind:getmenuindex="__e" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="a6bf6f60-6" data-ref="popmsg" bind:__l="__l"></popmsg></view>