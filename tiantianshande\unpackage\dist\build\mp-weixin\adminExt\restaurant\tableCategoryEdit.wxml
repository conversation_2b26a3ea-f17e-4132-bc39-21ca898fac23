<view><block wx:if="{{isload}}"><block><form data-event-opts="{{[['submit',[['subform',['$event']]]]]}}" bindsubmit="__e"><view class="form-box"><view class="form-item"><view class="f1">餐桌分类名称<text style="color:red;">*</text></view><view class="f2"><input type="text" name="name" placeholder="请填写餐桌分类名称" placeholder-style="color:#888" value="{{info.name}}"/></view></view><view class="form-item"><view class="f1">最低消费</view><view class="f2"><input type="text" name="limit_fee" placeholder placeholder-style="color:#888" value="{{info.limit_fee}}"/></view></view><view class="form-item"><view class="f1">预定费</view><view class="f2"><input type="text" name="booking_fee" placeholder placeholder-style="color:#888" value="{{info.booking_fee}}"/></view></view><view class="form-item"><view class="f1">座位数</view><view class="f2"><input type="text" name="seat" placeholder placeholder-style="color:#888" value="{{info.seat}}"/></view></view></view><view class="form-box"><view class="form-item"><view class="f1">排序</view><view class="f2"><input type="text" name="sort" placeholder="用于排序,越大越靠前" placeholder-style="color:#888" value="{{info.sort}}"/></view></view><view class="form-item"><view>状态<text style="color:red;">*</text></view><view><radio-group class="radio-group" name="status" data-event-opts="{{[['change',[['',['$event']]]]]}}" bindchange="__e"><label><radio value="1" checked="{{info.status==1?true:false}}"></radio>显示</label><label><radio value="0" checked="{{!info||info.status==0?true:false}}"></radio>隐藏</label></radio-group></view></view></view><button class="savebtn" style="{{('background:linear-gradient(90deg,'+$root.m0+' 0%,rgba('+$root.m1+',0.8) 100%)')}}" form-type="submit">提交</button><block wx:if="{{info.id}}"><button data-event-opts="{{[['tap',[['todel',['$event']]]]]}}" class="button text-btn" bindtap="__e">删除</button></block><view style="height:50rpx;"></view></form></block></block><block wx:if="{{loading}}"><loading vue-id="e06cb5ba-1" bind:__l="__l"></loading></block></view>