<view class="container"><block wx:if="{{showStartOverlay}}"><view data-event-opts="{{[['tap',[['startSystem',['$event']]]]]}}" class="start-overlay" bindtap="__e"><view class="start-content"><view class="start-logo"><text class="logo-icon">🚀</text><text class="logo-text">梦想方舟</text><text class="logo-subtitle">DREAM ARK PROJECT</text></view><view data-event-opts="{{[['tap',[['startSystem',['$event']]]]]}}" class="start-btn" style="{{'background:'+('linear-gradient(45deg,'+$root.m0+' 0%,rgba('+$root.m1+',0.8) 100%)')+';'}}" catchtap="__e"><text class="btn-icon">⚡</text><text class="btn-text">启动系统</text></view><view class="start-tips"><text class="tip-text">准备开启时空对话之旅</text></view></view></view></block><block wx:if="{{showTimePortalOverlay}}"><view class="time-portal-overlay"><view class="portal-content"><view class="portal-animation"><view class="portal-ring portal-ring-1"></view><view class="portal-ring portal-ring-2"></view><view class="portal-ring portal-ring-3"></view><view class="portal-center"><text class="portal-icon">🌀</text></view></view><text class="portal-text">正在穿越时空...</text><text class="portal-subtitle">即将进入2049年</text></view></view></block><canvas class="particles-canvas" style="{{'width:'+(canvasWidth+'px')+';'+('height:'+(canvasHeight+'px')+';')}}" canvas-id="particlesCanvas"></canvas><view class="bg-grid"></view><view class="bg-circles"></view><view class="bg-stars"></view><view class="console"><view class="machine-frame"></view><view class="status-lights"><view class="status-light active"></view><view class="status-light active"></view><view class="status-light standby"></view></view><view class="title-section"><text class="main-title" style="{{'color:'+($root.m2)+';'}}">梦想方舟计划</text><text class="subtitle">DREAM ARK PROJECT</text><view class="title-line" style="{{'background:'+($root.m3)+';'}}"></view></view><view class="ai-assistant"><view class="assistant-avatar"><view class="avatar-ring" style="{{'border-color:'+($root.m4)+';'}}"><view class="avatar-core"><text class="avatar-icon">🤖</text></view><view class="{{['avatar-pulse',(isRobotSpeaking)?'speaking':'']}}"></view></view><text class="assistant-name" style="{{'color:'+($root.m5)+';'}}">明日萌像</text><text class="assistant-role">时空舟长</text></view><view class="dialogue-output"><view class="output-header"><text class="output-label">系统消息</text><view class="{{['output-status',(isRobotSpeaking)?'active':'']}}"><text class="status-dot"></text><text class="status-text">{{isRobotSpeaking?'正在输出':'待机中'}}</text></view></view><view class="output-content"><text class="output-text">{{displayText}}</text><block wx:if="{{showCursor}}"><text class="typing-cursor">|</text></block></view></view><view class="hologram-display"><view class="hologram-grid"><block wx:for="{{hologramIcons}}" wx:for-item="icon" wx:for-index="index" wx:key="index"><view class="hologram-item"><text class="hologram-icon">{{icon}}</text></view></block></view></view></view><view class="system-status"><view class="status-header"><text class="status-title">系统状态</text><view class="status-indicator online"><text class="indicator-dot"></text><text class="indicator-text">在线</text></view></view><view class="status-grid"><view class="status-card"><text class="card-value" style="{{'color:'+($root.m6)+';'}}">98%</text><text class="card-label">系统准备</text><view class="card-progress"><view class="progress-bar" style="{{'background:'+($root.m7)+';'+('width:'+('98%')+';')}}"></view></view></view><view class="status-card"><text class="card-value" style="{{'color:'+($root.m8)+';'}}">∞</text><text class="card-label">时间能量</text><view class="card-icon">⚡</view></view><view class="status-card"><text class="card-value" style="{{'color:'+($root.m9)+';'}}">2049</text><text class="card-label">目标年代</text><view class="card-icon">🎯</view></view></view></view><view class="control-panel"><block wx:if="{{hasDialogueData}}"><view class="main-controls"><button data-event-opts="{{[['tap',[['showClearDataDialog',['$event']]]]]}}" class="secondary-btn" bindtap="__e"><text class="btn-icon">🗑️</text><text class="btn-text">清空已有数据</text></button></view></block><view class="auxiliary-controls"><view data-event-opts="{{[['tap',[['toggleAudio',['$event']]]]]}}" class="control-item" bindtap="__e"><text class="control-icon">{{audioEnabled?'🔊':'🔇'}}</text><text class="control-label">音效</text></view><view data-event-opts="{{[['tap',[['adminClear',['$event']]]]]}}" class="control-item admin-control" bindtap="__e"><text class="control-icon">⚙️</text><text class="control-label">管理</text></view></view></view></view><block wx:if="{{showStartButton}}"><view class="floating-bottom-panel"><block wx:if="{{autoJumpCountdown>0}}"><view class="floating-countdown"><view class="countdown-ring"><view class="countdown-circle" style="{{'background:'+('conic-gradient('+$root.m10+' '+(360-autoJumpCountdown/3*360)+'deg, rgba(0,247,255,0.2) 0deg)')+';'}}"><text class="countdown-number">{{autoJumpCountdown}}</text></view></view><view class="countdown-info"><text class="countdown-title">自动穿越倒计时</text><text class="countdown-subtitle">{{autoJumpCountdown+"秒后启动时空对话"}}</text></view><button data-event-opts="{{[['tap',[['cancelAutoJump',['$event']]]]]}}" class="countdown-cancel" bindtap="__e"><text class="cancel-icon">⏸</text></button></view></block><view class="floating-start-container"><button data-event-opts="{{[['tap',[['startDialogue',['$event']]]]]}}" class="floating-start-btn" style="{{'background:'+('linear-gradient(135deg,'+$root.m11+' 0%,rgba('+$root.m12+',0.8) 50%,'+$root.m13+' 100%)')+';'}}" bindtap="__e"><view class="btn-decoration"><view class="deco-grid"></view><view class="deco-particles"><block wx:for="{{6}}" wx:for-item="i" wx:for-index="__i0__" wx:key="*this"><view class="deco-particle"></view></block></view></view><view class="btn-content"><view class="btn-icon-wrapper"><text class="btn-icon">🚀</text><view class="icon-glow" style="{{'box-shadow:'+('0 0 20rpx '+$root.m14)+';'}}"></view></view><view class="btn-text-wrapper"><text class="btn-text">启动时空对话</text><text class="btn-subtitle">LAUNCH TIME DIALOGUE</text></view><view class="btn-arrow"><text class="arrow-icon">→</text></view></view><view class="btn-scan-line" style="{{'background:'+($root.m15)+';'}}"></view></button></view></view></block><view class="footer-info"><view class="info-line"><text class="info-status" style="{{'color:'+($root.m16)+';'}}">● ARK SYSTEM ONLINE</text><text class="info-divider">|</text><text class="info-captain">舟长"明日萌像"已就位</text></view><view class="info-copyright"><text class="copyright-text">COPYRIGHT © 2049 DREAM ARK PROJECT</text></view></view></view>