(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["activity/ggk/myprize"],{3818:function(t,n,o){},"6f26":function(t,n,o){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;var a=getApp(),e={data:function(){return{opt:{},loading:!1,isload:!1,menuindex:-1,pre_url:a.globalData.pre_url,st:0,datalist:[],pagenum:1,maskshow:!1,record:"",info:{},formdata:""}},onLoad:function(t){this.opt=a.getopts(t),this.getdata()},onPullDownRefresh:function(){this.getdata()},methods:{getdata:function(){var t=this;t.loading=!0,a.get("ApiChoujiang/myprize",{hid:t.opt.hid},(function(n){t.loading=!1,n.info.formcontent=JSON.parse(n.info.formcontent),t.info=n.info,t.datalist=n.datalist,t.loaded()}))},duijiang:function(t){var n=t.currentTarget.dataset.k,o=(t.currentTarget.dataset.id,this.datalist[n]),a=JSON.parse(o.formdata);console.log(a),this.record=o,this.formdata=a,this.maskshow=!0},changemaskshow:function(){this.maskshow=!this.maskshow},formsub:function(t){var n=this;console.log(t);for(var o=t.detail.value,e=n.info.formcontent,i=n.record,r={},s=0;s<e.length;s++){if(1==e[s].val3&&(""===o["form"+s]||void 0===o["form"+s]||0==o["form"+s].length))return void a.alert(e[s].val1+" 必填");"switch"==e[s].key&&(0==o["form"+s]?o["form"+s]="否":o["form"+s]="是"),"selector"==e[s].key&&(o["form"+s]=e[s].val2[o["form"+s]]);r[e[s].val1]=o["form"+s]}console.log(r),a.post("ApiChoujiang/subinfo/rid/"+i.id,{formcontent:r},(function(t){0==t.status?a.alert(t.msg):(n.changemaskshow(),a.success(t.msg),n.getdata())}))}}};n.default=e},"720a":function(t,n,o){"use strict";o.r(n);var a=o("6f26"),e=o.n(a);for(var i in a)["default"].indexOf(i)<0&&function(t){o.d(n,t,(function(){return a[t]}))}(i);n["default"]=e.a},8722:function(t,n,o){"use strict";var a=o("3818"),e=o.n(a);e.a},cf793:function(t,n,o){"use strict";o.r(n);var a=o("e267"),e=o("720a");for(var i in e)["default"].indexOf(i)<0&&function(t){o.d(n,t,(function(){return e[t]}))}(i);o("8722");var r=o("828b"),s=Object(r["a"])(e["default"],a["b"],a["c"],!1,null,null,null,!1,a["a"],void 0);n["default"]=s.exports},d0df:function(t,n,o){"use strict";(function(t,n){var a=o("47a9");o("06e9");a(o("3240"));var e=a(o("cf793"));t.__webpack_require_UNI_MP_PLUGIN__=o,n(e.default)}).call(this,o("3223")["default"],o("df3c")["createPage"])},e267:function(t,n,o){"use strict";o.d(n,"b",(function(){return e})),o.d(n,"c",(function(){return i})),o.d(n,"a",(function(){return a}));var a={loading:function(){return o.e("components/loading/loading").then(o.bind(null,"ceaa"))},dpTabbar:function(){return o.e("components/dp-tabbar/dp-tabbar").then(o.bind(null,"b875"))},popmsg:function(){return o.e("components/popmsg/popmsg").then(o.bind(null,"2bf2"))}},e=function(){var t=this.$createElement,n=(this._self._c,this.isload?this.datalist.length:null);this.$mp.data=Object.assign({},{$root:{g0:n}})},i=[]}},[["d0df","common/runtime","common/vendor"]]]);