<view class="container"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="idx" wx:key="idx"><view class="busbox"><view class="businfo"><view class="f1"><image class="image" src="{{item.$orig.logo}}"></image><block wx:if="{{item.$orig.status==0}}"><view class="icon">报名中</view></block></view><view class="f2"><view class="title">{{item.$orig.title}}</view><view class="address">{{item.$orig.event_date}}</view><view class="address">{{item.$orig.distance+" "+item.$orig.venues_title}}</view><view class="address"><image class="img" src="{{item.$orig.headimg}}"></image><text>{{item.$orig.nickname}}</text></view></view></view><view class="tab"><view class="imgs"><image class="img" src="{{item.$orig.headimg}}"></image><block wx:for="{{item.$orig.statistics}}" wx:for-item="m" wx:for-index="n" wx:key="n"><image class="img" src="{{m.headimg}}"></image></block><text>{{item.g0+1+"人已上车"}}</text></view><view data-event-opts="{{[['tap',[['goDeatail',['$0'],[[['datalist','',idx,'id']]]]]]]}}" class="btn" style="{{('background:'+primary_color)}}" bindtap="__e">立即上车</view></view></view></block><block wx:if="{{nomore}}"><nomore vue-id="361272da-1" bind:__l="__l"></nomore></block><block wx:if="{{nodata}}"><nodata vue-id="361272da-2" bind:__l="__l"></nodata></block></view>