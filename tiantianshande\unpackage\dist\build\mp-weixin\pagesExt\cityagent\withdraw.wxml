<view class="container"><block wx:if="{{isload}}"><block><form data-event-opts="{{[['submit',[['formSubmit',['$event']]]]]}}" bindsubmit="__e"><view class="mymoney" style="{{'background:'+($root.m0)+';'}}"><view class="f1">我的可提现余额</view><view class="f2"><text style="font-size:26rpx;">￥</text>{{agentInfo&&agentInfo.money||'0.00'}}</view><view class="f3" data-url="withdrawlog?st=1" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><text>提现记录</text><text class="iconfont iconjiantou" style="font-size:20rpx;"></text></view></view><view class="content2"><view class="item2"><view class="f1">提现金额(元)</view></view><view class="item3"><view class="f1">￥</view><view class="f2"><input class="input" type="digit" name="money" value="" placeholder="请输入提现金额" placeholder-style="color:#999;font-size:40rpx" data-event-opts="{{[['input',[['moneyinput',['$event']]]]]}}" bindinput="__e"/></view></view><block wx:if="{{sysset.withdrawfee>0||sysset.withdrawmin>0}}"><view class="item4"><block wx:if="{{sysset.withdrawmin>0}}"><text style="margin-right:10rpx;">{{"最低提现金额"+sysset.withdrawmin+'元'}}</text></block><block wx:if="{{sysset.withdrawfee>0}}"><text>{{"提现手续费"+sysset.withdrawfee+'%'}}</text></block></view></block></view><view class="withdrawtype"><view class="f1">选择提现方式：</view><view class="f2"><block wx:if="{{sysset.withdraw_weixin==1}}"><view class="item" data-paytype="微信钱包" data-event-opts="{{[['tap',[['changeradio',['$event']]]]]}}" catchtap="__e"><view class="t1"><image class="img" src="/static/img/withdraw-weixin.png"></image>微信钱包</view><view class="radio" style="{{(paytype=='微信钱包'?'background:'+$root.m1+';border:0':'')}}"><image class="radio-img" src="/static/img/checkd.png"></image></view></view></block><block wx:if="{{sysset.withdraw_aliaccount==1}}"><label class="item" data-paytype="支付宝" data-event-opts="{{[['tap',[['changeradio',['$event']]]]]}}" catchtap="__e"><view class="t1"><image class="img" src="/static/img/withdraw-alipay.png"></image>支付宝</view><view class="radio" style="{{(paytype=='支付宝'?'background:'+$root.m2+';border:0':'')}}"><image class="radio-img" src="/static/img/checkd.png"></image></view></label></block><block wx:if="{{sysset.withdraw_bankcard==1}}"><label class="item" data-paytype="银行卡" data-event-opts="{{[['tap',[['changeradio',['$event']]]]]}}" catchtap="__e"><view class="t1"><image class="img" src="/static/img/withdraw-cash.png"></image>银行卡</view><view class="radio" style="{{(paytype=='银行卡'?'background:'+$root.m3+';border:0':'')}}"><image class="radio-img" src="/static/img/checkd.png"></image></view></label></block></view></view><block wx:if="{{paytype=='支付宝'}}"><view class="account-form"><view class="form"><view class="form-item"><view class="label">账户名</view><input class="input" type="text" placeholder="请输入支付宝账户名" placeholder-style="color:#BBBBBB;font-size:28rpx" data-event-opts="{{[['input',[['__set_model',['$0','aliaccountname','$event',[]],['accountInfo']]]]]}}" value="{{accountInfo.aliaccountname}}" bindinput="__e"/></view><view class="form-item"><view class="label">支付宝账号</view><input class="input" type="text" placeholder="请输入支付宝账号" placeholder-style="color:#BBBBBB;font-size:28rpx" data-event-opts="{{[['input',[['__set_model',['$0','aliaccount','$event',[]],['accountInfo']]]]]}}" value="{{accountInfo.aliaccount}}" bindinput="__e"/></view></view></view></block><block wx:if="{{paytype=='银行卡'}}"><view class="account-form"><view class="form"><view class="form-item"><view class="label">持卡人姓名</view><input class="input" type="text" placeholder="请输入持卡人姓名" placeholder-style="color:#BBBBBB;font-size:28rpx" data-event-opts="{{[['input',[['__set_model',['$0','bankcarduser','$event',[]],['accountInfo']]]]]}}" value="{{accountInfo.bankcarduser}}" bindinput="__e"/></view><view class="form-item"><view class="label">银行卡号</view><input class="input" type="text" placeholder="请输入银行卡号" placeholder-style="color:#BBBBBB;font-size:28rpx" data-event-opts="{{[['input',[['__set_model',['$0','bankcardnum','$event',[]],['accountInfo']]]]]}}" value="{{accountInfo.bankcardnum}}" bindinput="__e"/></view><view class="form-item"><view class="label">开户银行</view><input class="input" type="text" placeholder="请输入开户银行" placeholder-style="color:#BBBBBB;font-size:28rpx" data-event-opts="{{[['input',[['__set_model',['$0','bankname','$event',[]],['accountInfo']]]]]}}" value="{{accountInfo.bankname}}" bindinput="__e"/></view><view class="form-item"><view class="label">开户地址</view><input class="input" type="text" placeholder="请输入开户地址（可选）" placeholder-style="color:#BBBBBB;font-size:28rpx" data-event-opts="{{[['input',[['__set_model',['$0','bankaddress','$event',[]],['accountInfo']]]]]}}" value="{{accountInfo.bankaddress}}" bindinput="__e"/></view></view></view></block><button data-event-opts="{{[['tap',[['formSubmit',['$event']]]]]}}" class="btn" style="{{'background:'+($root.m4)+';'}}" bindtap="__e">立即提现</button></form></block></block><block wx:if="{{loading}}"><loading vue-id="34590b73-1" bind:__l="__l"></loading></block><dp-tabbar vue-id="34590b73-2" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="34590b73-3" data-ref="popmsg" bind:__l="__l"></popmsg></view>