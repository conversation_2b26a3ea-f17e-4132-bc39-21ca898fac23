<view class="foot-box"><view style="display:none;"><text>{{"模板ID: "+partJobVoClone.template.templateId}}</text><text>{{"按钮状态: "+partJobVoClone.buttonStatus}}</text><text>{{"是否已报名: "+partJobVoClone.hasApply}}</text></view><block wx:if="{{agreementVo.result===2}}"><view class="foot-agreement"><view class="foot-agreement-left"><text class="ellipsis">团团建议先阅读</text><text class="foot-agreement-green ellipsis ptp_exposure" data-ptpid="m1dr-oerp-qafg-nvde" data-event-opts="{{[['tap',[['jumpToAgreement',['$event']]]]]}}" bindtap="__e">{{"《"+agreementVo.title+"》"}}</text>哦～</view></view></block><block wx:if="{{partJobVoClone.template.templateId!==16&&partJobVoClone.template.templateId!==13}}"><view class="foot-apply"><view class="action-buttons"><view class="relative"><view class="shareCode ptp_exposure" data-ptpid="63bf-1709-ae9f-e11b" data-event-opts="{{[['tap',[['collect',['$event']]]]]}}" bindtap="__e"><view class="{{['shareview-image iconfont '+collectImgClone]}}"></view><view class="shareview">收藏</view></view></view><view class="relative"><view data-event-opts="{{[['tap',[['handleShare',['$event']]]]]}}" class="shareCode ptp_exposure" bindtap="__e"><view class="shareview-image iconfont iconshare"></view><view class="shareview">分享</view></view></view></view><block wx:if="{{partJobVoClone.hasApply&&partJobVoClone.buttonStatus!==9}}"><view class="applybtn ptp_exposure" style="{{'background:'+($root.m0)+';'}}" data-ptpid="b9e6-1750-b71f-2f5e" data-event-opts="{{[['tap',[['jumpToReport',['$event']]]]]}}" bindtap="__e">查看报名</view></block><block wx:if="{{partJobVoClone.buttonStatus===3}}"><view class="applybtn gray">已结束</view></block><block wx:if="{{partJobVoClone.buttonStatus===4}}"><view class="applybtn gray">已暂停</view></block><view class="relative"><block wx:if="{{!hasToken}}"><open-button class="login-mask" vue-id="942c762c-1" openType="getPhoneNumber" ptpId="26ca-1dd6-b35f-b3d0-0" data-event-opts="{{[['^initData',[['onPromptlyClicked']]]]}}" bind:initData="__e" bind:__l="__l"></open-button></block><block wx:if="{{partJobVoClone.buttonStatus===6}}"><view class="applybtn ptp_exposure" style="{{'background:'+($root.m1)+';'}}" data-ptpid="26ca-1dd6-b35f-b3d0" id="pid=26ca-1dd6-b35f-b3d0" data-event-opts="{{[['tap',[['onPromptly',['$event']]]]]}}" bindtap="__e">{{''+(partJobVoClone.is_apply?'已报名':agreementVo.result===2?'我已同意，立即报名':'立即报名')+''}}</view></block><block wx:if="{{partJobVoClone.buttonStatus===7}}"><view class="applybtn yellow ptp_exposure" data-ptpid="e43c-1674-9609-8478" data-event-opts="{{[['tap',[['onPromptly',['$event']]]]]}}" bindtap="__e"><view>排队报名</view><block wx:if="{{partJobVoClone.queueCount>=20}}"><view class="applybtn-desc">{{"（前面有"+partJobVoClone.queueCount+"人待录取）"}}</view></block></view></block></view></view></block><block wx:if="{{partJobVoClone.template.templateId===13}}"><view class="foot-apply"><block wx:if="{{partJobVoClone.jobFeeVO.rushStatus===1}}"><view class="apply-price">{{"¥ "+partJobVoClone.jobFeeVO.feeRushPrice}}</view></block><block wx:if="{{partJobVoClone.hasApply&&partJobVoClone.buttonStatus!==9}}"><view class="{{['course-apply-btn '+(partJobVoClone.jobFeeVO.rushStatus!==1?'width-686':'')+' ptp_exposure']}}" style="{{'background:'+($root.m2)+';'}}" data-ptpid="b9e6-1750-b71f-2f5e" data-event-opts="{{[['tap',[['jumpToReport',['$event']]]]]}}" bindtap="__e">查看报名</view></block><block wx:if="{{partJobVoClone.buttonStatus===3}}"><view class="{{['course-apply-btn '+(partJobVoClone.jobFeeVO.rushStatus!==1?'width-686':'')+' gray']}}">已结束</view></block><block wx:if="{{partJobVoClone.buttonStatus===4}}"><view class="{{['course-apply-btn '+(partJobVoClone.jobFeeVO.rushStatus!==1?'width-686':'')+' gray']}}">已暂停</view></block><block wx:if="{{partJobVoClone.buttonStatus===6}}"><view class="relative"><block wx:if="{{!hasToken}}"><open-button class="login-mask" vue-id="942c762c-2" openType="getPhoneNumber" ptpId="dkfi-dp01-cjgf-k2jf-0" data-event-opts="{{[['^initData',[['onPromptlyClicked']]]]}}" bind:initData="__e" bind:__l="__l"></open-button></block><block wx:if="{{partJobVoClone.jobFeeVO.rushStatus===1}}"><view class="{{['course-apply-btn '+(partJobVoClone.jobFeeVO.rushStatus!==1?'width-686':'')+' ptp_exposure']}}" style="{{'background:'+($root.m3)+';'}}" data-ptpid="9f71-dk1n-zbcn-du1n" data-event-opts="{{[['tap',[['onPromptly',['$event']]]]]}}" bindtap="__e">{{''+partJobVoClone.jobFeeVO.buttons.btn2+''}}</view></block><block wx:else><view class="{{['course-apply-btn '+(partJobVoClone.jobFeeVO.rushStatus!==1?'width-686':'')+' ptp_exposure']}}" style="{{'background:'+($root.m4)+';'}}" data-ptpid="dkfi-dp01-cjgf-k2jf" data-event-opts="{{[['tap',[['onPromptly',['$event']]]]]}}" bindtap="__e">{{''+partJobVoClone.jobFeeVO.buttons.btn1+''}}</view></block></view></block><block wx:if="{{partJobVoClone.buttonStatus===9}}"><view class="{{['course-apply-btn '+(partJobVoClone.jobFeeVO.rushStatus!==1?'width-686':'')+' ptp_exposure']}}" data-ptpid="127f-1kfp-0281-jkd1" data-event-opts="{{[['tap',[['payHandle',['$event']]]]]}}" bindtap="__e">待支付</view></block><block wx:if="{{partJobVoClone.buttonStatus===7}}"><view class="relative"><block wx:if="{{!hasToken}}"><open-button class="login-mask" vue-id="942c762c-3" openType="getPhoneNumber" ptpId="e43c-1674-9609-8478-0" data-event-opts="{{[['^initData',[['onPromptlyClicked']]]]}}" bind:initData="__e" bind:__l="__l"></open-button></block><view class="{{['course-apply-btn '+(partJobVoClone.jobFeeVO.rushStatus!==1?'width-686':'')+' column ptp_exposure']}}" data-ptpid="e43c-1674-9609-8478" data-event-opts="{{[['tap',[['onPromptly',['$event']]]]]}}" bindtap="__e"><view>排队报名</view><block wx:if="{{partJobVoClone.queueCount>=20}}"><view class="applybtn-desc">{{"（前面有"+partJobVoClone.queueCount+"人待录取）"}}</view></block></view></view></block></view></block><block wx:if="{{partJobVoClone.template.templateId===16}}"><view class="foot-apply"><block wx:if="{{partJobVoClone.hasApply&&partJobVoClone.buttonStatus!==9}}"><view class="apply-btn ptp_exposure" style="{{'background:'+($root.m5)+';'}}" data-ptpid="b9e6-1750-b71f-2f5e" id="pid=b9e6-1750-b71f-2f5e" data-event-opts="{{[['tap',[['jumpToReport',['$event']]]]]}}" bindtap="__e">查看报名</view></block><block wx:if="{{partJobVoClone.buttonStatus===3}}"><view class="apply-btn gray">已结束</view></block><block wx:if="{{partJobVoClone.buttonStatus===4}}"><view class="apply-btn gray">已暂停</view></block><view class="relative"><block wx:if="{{!hasToken}}"><open-button class="login-mask" vue-id="942c762c-4" openType="getPhoneNumber" ptpId="9f71-dk1n-zbcn-du1n-0" data-event-opts="{{[['^initData',[['onPromptlyClicked']]]]}}" bind:initData="__e" bind:__l="__l"></open-button></block><block wx:if="{{partJobVoClone.buttonStatus===6}}"><view class="apply-btn ptp_exposure" style="{{'background:'+($root.m6)+';'}}" data-ptpid="9f71-dk1n-zbcn-du1n" data-event-opts="{{[['tap',[['onPromptly',['$event']]]]]}}" bindtap="__e">{{''+(partJobVoClone.is_apply?'已报名':agreementVo.result===2?baseInfo.noProtocolBtnText||baseInfo.btnText:baseInfo.btnText)+''}}</view></block><block wx:if="{{partJobVoClone.buttonStatus===7}}"><view class="apply-btn column ptp_exposure" data-ptpid="e43c-1674-9609-8478" data-event-opts="{{[['tap',[['onPromptly',['$event']]]]]}}" bindtap="__e"><view>排队报名</view><block wx:if="{{partJobVoClone.queueCount>=20}}"><view class="applybtn-desc">{{"（前面有"+partJobVoClone.queueCount+"人待录取）"}}</view></block></view></block></view></view></block></view>