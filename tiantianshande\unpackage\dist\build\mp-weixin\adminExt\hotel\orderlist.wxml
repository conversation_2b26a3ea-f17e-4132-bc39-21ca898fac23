<view class="container"><block wx:if="{{isload}}"><block><dd-tab vue-id="98b6d1b8-1" itemdata="{{['全部','待确认','待入住','已到店','已离店','已取消']}}" itemst="{{['all','1','2','3','4','-1']}}" st="{{st}}" isfixed="{{true}}" data-event-opts="{{[['^changetab',[['changetab']]]]}}" bind:changetab="__e" bind:__l="__l"></dd-tab><view style="width:100%;height:100rpx;"></view><view class="topsearch flex-y-center"><view class="f1 flex-y-center"><image class="img" src="{{pre_url+'/static/img/search_ico.png'}}"></image><input placeholder="输入关键字搜索" placeholder-style="font-size:24rpx;color:#C2C2C2" data-event-opts="{{[['confirm',[['searchConfirm',['$event']]]]]}}" value="{{keyword}}" bindconfirm="__e"/></view></view><view class="order-content"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><view class="order-box"><view class="head" data-url="{{'orderdetail?id='+item.$orig.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e"><view class="f1"><image src="{{pre_url+'/static/img/ico-shop.png'}}"></image>{{''+item.$orig.title}}</view><view class="flex1"></view><block wx:if="{{item.$orig.status==0}}"><text class="st0">待付款</text></block><block wx:if="{{item.$orig.status==1}}"><block><text class="st1">待确认</text></block></block><block wx:if="{{item.$orig.status==1&&item.$orig.refund_status==1}}"><text class="st1">退款审核中</text></block><block wx:if="{{item.$orig.status==2}}"><text class="st2">待入住</text></block><block wx:if="{{item.$orig.status==4&&item.$orig.iscomment==0}}"><text class="st3">待评价</text></block><block wx:if="{{item.$orig.status==4&&item.$orig.iscomment==1}}"><text class="st3">已完成</text></block><block wx:if="{{item.$orig.status==3}}"><text class="st4">已到店</text></block><block wx:if="{{item.$orig.status==-1}}"><text class="st4">订单已关闭</text></block></view><view class="content" style="border-bottom:none;" data-url="{{'orderdetail?id='+item.$orig.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e"><view><image src="{{item.$orig.pic}}"></image></view><view class="detail"><text class="t1">{{item.$orig.titel}}</text><text class="t1">{{"入住日期："+item.$orig.in_date}}</text><text class="t1">{{"离店日期："+item.$orig.leave_date}}</text><view class="t3"><block wx:if="{{item.$orig.isbefore==1}}"><block><block wx:if="{{item.$orig.real_usemoney>0&&item.$orig.real_roomprice>0}}"><text class="x1 flex1">{{"实付房费："+item.$orig.real_usemoney+item.m0+" + ￥"+item.$orig.real_roomprice}}</text></block><block wx:else><block wx:if="{{item.$orig.real_usemoney>0&&item.$orig.real_roomprice==0}}"><text class="x1 flex1">{{"实付房费：￥"+item.$orig.real_usemoney+item.m1}}</text></block><block wx:else><text class="x1 flex1">{{"实付房费：￥"+item.$orig.real_roomprice}}</text></block></block></block></block><block wx:else><block><block wx:if="{{item.$orig.use_money>0&&item.$orig.leftmoney>0}}"><text class="x1 flex1">{{"房费："+item.$orig.use_money+item.m2+" + ￥"+item.$orig.leftmoney}}</text></block><block wx:else><block wx:if="{{item.$orig.use_money>0&&item.$orig.leftmoney==0}}"><text class="x1 flex1">{{"房费：￥"+item.$orig.use_money+item.m3}}</text></block><block wx:else><text class="x1 flex1">{{"房费：￥"+item.$orig.sell_price}}</text></block></block></block></block></view></view></view><view class="bottom" style="display:flex;justify-content:space-between;"><text>{{"共"+item.$orig.daycount+'晚'}}<block wx:if="{{item.$orig.use_money>0&&item.$orig.leftmoney>0}}"><block>{{'实付: 押金￥'+item.$orig.yajin_money+"+"+text['服务费']+"￥"+item.$orig.fuwu_money+"+房费￥"+item.$orig.leftmoney+"+"+(item.$orig.use_money?item.$orig.use_money:0)+item.m4+''}}</block></block><block wx:else><block wx:if="{{item.$orig.use_money>0&&item.$orig.leftmoney==0}}"><block>{{'实付: 押金￥'+item.$orig.yajin_money+"+"+text['服务费']+"￥"+item.$orig.fuwu_money+"+房费"+(item.$orig.use_money?item.$orig.use_money:0)+item.m5+''}}</block></block><block wx:else><block>{{'实付:￥'+item.$orig.totalprice+''}}</block></block></block></text></view><block wx:if="{{item.$orig.refund_status>0}}"><view class="bottom"><block wx:if="{{item.$orig.refund_status==1}}"><text style="color:red;">{{'退款中￥'+item.$orig.refund_money}}</text></block><block wx:if="{{item.$orig.refund_status==2}}"><text style="color:red;">{{'已退款￥'+item.$orig.refund_money}}</text></block><block wx:if="{{item.$orig.refund_status==3}}"><text style="color:red;">退款申请已驳回</text></block></view></block><view class="op"><block wx:if="{{item.$orig.status>=4&&item.$orig.yajin_money>0}}"><view class="bottom1"><block wx:if="{{item.$orig.yajin_refund_status==0}}"><text style="color:red;">押金待申请</text></block><block wx:if="{{item.$orig.yajin_refund_status==1}}"><text style="color:red;">押金待审核</text></block><block wx:if="{{item.$orig.yajin_refund_status==2}}"><text style="color:red;">押金已退款</text></block><block wx:if="{{item.$orig.yajin_refund_status==-1}}"><text style="color:red;">退款申请已驳回</text></block></view></block><block wx:if="{{item.$orig.yajin_refund_status==1}}"><view class="btn2" data-url="{{'refundyajin?orderid='+item.$orig.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">退押金</view></block><block wx:if="{{item.$orig.status==1}}"><view class="btn2" data-id="{{item.$orig.id}}" data-event-opts="{{[['tap',[['confirmorder',['$event']]]]]}}" catchtap="__e">确认订单</view></block><block wx:if="{{item.$orig.status==2}}"><view class="btn2" data-id="{{item.$orig.id}}" data-event-opts="{{[['tap',[['qrdaodian',['$event']]]]]}}" catchtap="__e">确认到店</view></block><block wx:if="{{item.$orig.status==3}}"><view class="btn2" data-id="{{item.$orig.id}}" data-event-opts="{{[['tap',[['qrlidian',['$event']]]]]}}" catchtap="__e">确认离店</view></block><view class="btn2" data-url="{{'orderdetail?id='+item.$orig.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e">详情</view></view><view class="bottom flex-y-center"><image style="width:40rpx;height:40rpx;border-radius:50%;margin-right:10rpx;" src="{{item.$orig.member.headimg}}"></image><text style="font-weight:bold;color:#333;margin-right:8rpx;">{{item.$orig.member.nickname}}</text>{{"(ID:"+item.$orig.mid+')'}}</view></view></block></block></view><uni-popup class="vue-ref" vue-id="98b6d1b8-2" id="dialogLeave" type="dialog" data-ref="dialogLeave" bind:__l="__l" vue-slots="{{['default']}}"><view class="uni-popup-dialog"><view class="uni-dialog-title"><text class="uni-dialog-title-text">确认离店</text></view><view class="uni-dialog-content"><view class="uni-list-cell-db"><label>离店日期</label><picker mode="date" value="{{date}}" start="{{startDate}}" end="{{endDate}}" data-event-opts="{{[['change',[['bindDateChange',['$event']]]]]}}" bindchange="__e"><view class="uni-input">{{date}}</view></picker></view></view><view class="uni-dialog-button-group"><view data-event-opts="{{[['tap',[['dialogLeaveClose',['$event']]]]]}}" class="uni-dialog-button" bindtap="__e"><text class="uni-dialog-button-text">取消</text></view><view data-event-opts="{{[['tap',[['confirmleave',['$event']]]]]}}" class="uni-dialog-button uni-border-left" bindtap="__e"><text class="uni-dialog-button-text uni-button-color">确定</text></view></view></view></uni-popup><block wx:if="{{nomore}}"><nomore vue-id="98b6d1b8-3" bind:__l="__l"></nomore></block><block wx:if="{{nodata}}"><nodata vue-id="98b6d1b8-4" bind:__l="__l"></nodata></block></block></block><block wx:if="{{loading}}"><loading vue-id="98b6d1b8-5" bind:__l="__l"></loading></block><dp-tabbar vue-id="98b6d1b8-6" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="98b6d1b8-7" data-ref="popmsg" bind:__l="__l"></popmsg></view>