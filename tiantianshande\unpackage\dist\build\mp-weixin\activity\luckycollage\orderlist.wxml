<view class="container"><block wx:if="{{isload}}"><block><dd-tab vue-id="3da3c1dc-1" itemdata="{{['全部','待付款','待发货','待收货','已完成','退款']}}" itemst="{{['all','0','1','2','3','10']}}" st="{{st}}" isfixed="{{true}}" data-event-opts="{{[['^changetab',[['changetab']]]]}}" bind:changetab="__e" bind:__l="__l"></dd-tab><view style="width:100%;height:100rpx;"></view><view class="topsearch flex-y-center"><view class="f1 flex-y-center"><image class="img" src="/static/img/search_ico.png"></image><input placeholder="输入关键字搜索" placeholder-style="font-size:24rpx;color:#C2C2C2" data-event-opts="{{[['confirm',[['searchConfirm',['$event']]]]]}}" value="{{keyword}}" bindconfirm="__e"/></view></view><view class="order-content"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><view class="order-box" data-url="{{'orderdetail?id='+item.$orig.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e"><view class="head"><text class="flex1">{{"订单号："+item.$orig.ordernum}}</text><block wx:if="{{item.$orig.status==0}}"><text class="st0">待付款</text></block><block wx:if="{{item.$orig.status==1}}"><block><block wx:if="{{item.$orig.buytype!=1}}"><block><block wx:if="{{item.$orig.team.status==1}}"><text class="st1">拼团中</text></block><block wx:if="{{item.$orig.team.status==2&&item.$orig.freight_type!=1}}"><text class="st1">拼团成功,待发货</text></block><block wx:if="{{item.$orig.team.status==2&&item.$orig.freight_type==1}}"><text class="st1">拼团成功,待提货</text></block><block wx:if="{{item.$orig.team.status==3}}"><text class="st4">拼团失败,已退款</text></block></block></block><block wx:else><block><block wx:if="{{item.$orig.freight_type!=1}}"><text class="st1">待发货</text></block><block wx:if="{{item.$orig.freight_type==1}}"><text class="st1">待提货</text></block></block></block></block></block><block wx:if="{{item.$orig.status==2}}"><text class="st2">待收货</text></block><block wx:if="{{item.$orig.status==3}}"><text class="st3">已完成</text></block><block wx:if="{{item.$orig.status==4}}"><text class="st4">已关闭</text></block></view><view class="content" style="border-bottom:none;"><view data-url="{{'product?id='+item.$orig.proid}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e"><image src="{{item.$orig.propic}}"></image></view><view class="detail"><text class="t1">{{item.$orig.proname}}</text><text class="t2">{{item.$orig.ggname}}</text><view class="t3"><text class="x1 flex1">{{"￥"+item.$orig.sell_price}}</text><text class="x2">{{"×"+item.$orig.num}}</text></view></view></view><view class="bottom"><text>{{"共计"+item.$orig.num+"件商品 实付:￥"+item.$orig.totalprice}}</text><block wx:if="{{item.$orig.refund_status==1}}"><text style="color:red;">{{'退款中￥'+item.$orig.refund_money}}</text></block><block wx:if="{{item.$orig.refund_status==2}}"><text style="color:red;">{{'已退款￥'+item.$orig.refund_money}}</text></block><block wx:if="{{item.$orig.refund_status==3}}"><text style="color:red;">退款申请已驳回</text></block></view><view class="op"><block wx:if="{{item.g0}}"><block><view class="btn2" data-url="{{'/pagesExt/order/invoice?type=lucky_collage&orderid='+item.$orig.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">发票</view></block></block><block wx:if="{{is_more==1&&item.$orig.status!=0}}"><view class="btn2" data-id="{{item.$orig.proid}}" data-num="{{item.$orig.num}}" data-buytype="{{item.$orig.buytype}}" data-ggid="{{item.$orig.ggid}}" data-event-opts="{{[['tap',[['more_one',['$event']]]]]}}" catchtap="__e">再来一单</view></block><view class="btn2" data-url="{{'orderdetail?id='+item.$orig.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e">详情</view><block wx:if="{{item.$orig.buytype!=1}}"><view class="btn2" data-url="{{'team?teamid='+item.$orig.teamid}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e">查看团</view></block><block wx:if="{{item.$orig.status==0}}"><block><view class="btn2" data-id="{{item.$orig.id}}" data-event-opts="{{[['tap',[['toclose',['$event']]]]]}}" catchtap="__e">关闭订单</view><view class="btn1" style="{{'background:'+(item.m0)+';'}}" data-url="{{'/pages/pay/pay?id='+item.$orig.payorderid}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e">去付款</view></block></block><block wx:if="{{item.$orig.status==2}}"><block><block wx:if="{{item.$orig.freight_type!=3&&item.$orig.freight_type!=4}}"><view class="btn2" data-url="{{'/pagesExt/order/logistics?express_com='+item.$orig.express_com+'&express_no='+item.$orig.express_no}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e">查看物流</view></block><view class="btn1" style="{{'background:'+(item.m1)+';'}}" data-id="{{item.$orig.id}}" data-event-opts="{{[['tap',[['orderCollect',['$event']]]]]}}" catchtap="__e">确认收货</view></block></block><block wx:if="{{item.$orig.status==4}}"><block><view class="btn2" data-id="{{item.$orig.id}}" data-event-opts="{{[['tap',[['todel',['$event']]]]]}}" catchtap="__e">删除订单</view></block></block></view></view></block></block></view><block wx:if="{{nomore}}"><nomore vue-id="3da3c1dc-2" bind:__l="__l"></nomore></block><block wx:if="{{nodata}}"><nodata vue-id="3da3c1dc-3" bind:__l="__l"></nodata></block></block></block><block wx:if="{{loading}}"><loading vue-id="3da3c1dc-4" bind:__l="__l"></loading></block><dp-tabbar vue-id="3da3c1dc-5" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="3da3c1dc-6" data-ref="popmsg" bind:__l="__l"></popmsg><uni-popup class="vue-ref" vue-id="3da3c1dc-7" type="center" data-ref="more_one" bind:__l="__l" vue-slots="{{['default']}}"><uni-popup-dialog vue-id="{{('3da3c1dc-8')+','+('3da3c1dc-7')}}" mode="input" message="成功消息" duration="{{2000}}" valueType="number" before-close="{{true}}" data-event-opts="{{[['^close',[['close']]],['^confirm',[['confirm']]]]}}" bind:close="__e" bind:confirm="__e" bind:__l="__l"></uni-popup-dialog></uni-popup></view>