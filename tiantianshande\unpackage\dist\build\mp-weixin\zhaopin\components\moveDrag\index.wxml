<block wx:if="{{$root.g0}}"><view class="move-box" disableScroll="{{$root.g1<=4}}"><view class="{{['content-move-box '+($root.g2?'gray':'')]}}" style="{{('transform: translateY('+top+'px)')}}"><view data-event-opts="{{[['touchend',[['moveEnd',['$event']]]],['touchmove',[['moveChange',['$event']]]],['touchstart',[['moveStart',['$event']]]]]}}" catchtouchend="__e" catchtouchmove="__e" catchtouchstart="__e"><view class="content-dot"></view><block wx:if="{{page!=='newLabel'}}"><view class="content-title"><block wx:if="{{icon}}"><image class="content-icon" src="{{icon}}"></image></block>{{''+title+''}}</view></block><slot name="filter"></slot></view><scroll-view class="{{['content-box '+($root.g3?'':'gray')]}}" style="{{('height: '+(compensateHeight+height)+'px')}}" lowerThreshold="{{100}}" scrollY="{{true}}" trapScroll="{{true}}" data-event-opts="{{[['scrollToLower',[['loadmore',['$event']]]]]}}" bindscrollToLower="__e"><block wx:if="{{$root.g4>0}}"><paging vue-id="6e1f9319-1" isEnd="{{isEnd}}" isLoading="{{isLoading}}" bind:__l="__l" vue-slots="{{['default']}}"><block wx:for="{{list}}" wx:for-item="item" wx:for-index="index" wx:key="partJobId"><regular-item vue-id="{{('6e1f9319-2-'+index)+','+('6e1f9319-1')}}" data="{{item}}" index="{{index}}" lazyload="{{index>=5}}" noMarginBottom="{{true}}" page="{{page}}" ptpId="{{ptpId}}" bind:__l="__l"></regular-item></block></paging></block><block wx:if="{{$root.g5}}"><block><image class="{{['blank-pic '+(page==='newLabel'?'small':'')]}}" mode="scaleToFill" src="https://qiniu-image.qtshe.com/719default-page1.png"></image><block wx:if="{{mainTitle}}"><view class="content-main-title">{{mainTitle}}</view></block><view class="blank-text">{{blankText}}</view><slot name="entranceList"></slot></block></block></scroll-view></view></view></block>