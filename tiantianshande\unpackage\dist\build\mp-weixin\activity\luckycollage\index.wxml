<view class="container"><block wx:if="{{isload}}"><block><view class="navbg"></view><view class="nav"><scroll-view scroll-x="true" scroll-left="{{top_bar_scroll}}"><view class="f1"><block wx:for="{{navlist}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><view class="{{['item '+(selected==index?'active':'')]}}" data-index="{{index}}" data-event-opts="{{[['tap',[['changetab',['$event']]]]]}}" bindtap="__e"><view class="t3">{{item.seckill_date_m}}</view><view class="t1">{{item.showtime}}</view><block wx:if="{{item.active==-1}}"><view class="t2">已结束</view></block><block wx:if="{{item.active==0}}"><view class="t2">已开抢</view></block><block wx:if="{{item.active==1}}"><view class="t2">拼团中</view></block><block wx:if="{{item.active==2}}"><view class="t2">即将开始</view></block></view></block></block></view></scroll-view></view><view class="content"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="item"><image class="f1" mode="widthFix" src="{{item.$orig.pic}}" data-url="{{'product2?id='+item.$orig.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"></image><view class="f2"><text class="t1">{{item.$orig.name}}</text><view class="t3"><view class="text1"><text class="x1" style="{{'color:'+(item.m0)+';'}}"><text style="font-size:24rpx;">￥</text>{{item.$orig.sell_price}}</text><text class="x2">{{"￥"+item.$orig.market_price}}</text></view><block wx:if="{{item.$orig.istatus==1}}"><button class="x3" style="{{'background:'+('linear-gradient(90deg,'+item.m1+' 0%,rgba('+item.m2+',0.8) 100%)')+';'}}" data-url="{{'product2?id='+item.$orig.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">立即抢购</button></block><block wx:else><block wx:if="{{item.$orig.istatus==-1}}"><button class="x3 xx1" data-url="{{'product2?id='+item.$orig.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">去看看</button></block><block wx:else><button class="x3" style="{{'background:'+(item.m3)+';'}}" data-url="{{'product2?id='+item.$orig.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">抢先看看</button></block></block></view></view></view></block><block wx:if="{{nodata}}"><view class="item" style="display:block;"><nodata vue-id="e27f1294-1" bind:__l="__l"></nodata></view></block><block wx:if="{{nomore}}"><nomore vue-id="e27f1294-2" bind:__l="__l"></nomore></block></view></block></block><block wx:if="{{loading}}"><loading vue-id="e27f1294-3" bind:__l="__l"></loading></block><dp-tabbar vue-id="e27f1294-4" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="e27f1294-5" data-ref="popmsg" bind:__l="__l"></popmsg></view>