<view class="container"><block wx:if="{{isload}}"><block><view class="view-show"><view class="search-container" data-url="/activity/kecheng/list" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e"><view class="search-box"><image class="img" src="/static/img/search_ico.png"></image><view class="search-text">搜索感兴趣的课程</view></view></view><scroll-view class="top-nav" scroll-x="true" scroll-left="{{scrollLeft}}" scroll-with-animation="{{true}}" data-event-opts="{{[['scroll',[['onNavScroll',['$event']]]]]}}" bindscroll="__e"><block wx:for="{{data}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="{{['nav-item',index===currentActiveIndex?'active':'']}}" data-root-item-id="{{item.id}}" data-root-item-index="{{index}}" id="{{'nav-item-'+index}}" data-event-opts="{{[['tap',[['clickRootItem',['$event']]]]]}}" bindtap="__e">{{''+item.name+''}}<block wx:if="{{item.course_count>0}}"><view class="course-count-badge">{{item.course_count}}</view></block></view></block></scroll-view><swiper class="content-swiper" style="{{'height:'+(swiperHeight+'px')+';'}}" current="{{currentActiveIndex}}" data-event-opts="{{[['change',[['onSwiperChange',['$event']]]]]}}" bindchange="__e"><block wx:for="{{$root.l2}}" wx:for-item="item" wx:for-index="tabIndex" wx:key="tabIndex"><swiper-item class="swiper-item"><scroll-view class="{{['course-list',menuindex>-1?'tabbarbot':'notabbarbot']}}" scroll-y="true"><block wx:for="{{item.l1}}" wx:for-item="detail" wx:for-index="index" wx:key="index"><view class="course-item"><view class="course-header"><view class="txt">{{detail.$orig.name}}</view><view class="show-all" data-key="{{index}}" data-id="{{detail.$orig.id}}" data-event-opts="{{[['tap',[['hideshow',['$event']]]]]}}" bindtap="__e">{{detail.$orig.ishide!=1?'收起':'展开'}}</view></view><block wx:if="{{detail.$orig.ishide!=1}}"><view class="course-content"><block wx:if="{{detail.g0>0}}"><block><block wx:for="{{detail.l0}}" wx:for-item="item" wx:for-index="itemIndex" wx:key="itemIndex"><view class="course-card" data-url="{{'/activity/kecheng/product?id='+item.$orig.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e"><view class="course-pic"><image class="img" src="{{item.$orig.pic}}"></image></view><view class="course-info"><view class="course-name">{{item.$orig.name}}</view><view class="course-bottom"><block wx:if="{{item.$orig.price>0}}"><view class="course-price">{{"￥"+item.$orig.price}}</view></block><block wx:else><view class="course-price free">免费</view></block><view class="course-count">{{item.m0+"人学习"}}</view></view></view></view></block></block></block><block wx:else><view class="no-course">暂无课程</view></block></view></block></view></block><block wx:if="{{nodata&&tabIndex===currentActiveIndex}}"><nodata vue-id="{{'2fae08d6-1-'+tabIndex}}" bind:__l="__l"></nodata></block></scroll-view></swiper-item></block></swiper></view></block></block><view style="display:none;">{{test}}</view><block wx:if="{{loading}}"><loading vue-id="2fae08d6-2" loadstyle="left:62.5%" bind:__l="__l"></loading></block><dp-tabbar vue-id="2fae08d6-3" opt="{{opt}}" data-event-opts="{{[['^getmenuindex',[['getmenuindex']]]]}}" bind:getmenuindex="__e" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="2fae08d6-4" data-ref="popmsg" bind:__l="__l"></popmsg></view>