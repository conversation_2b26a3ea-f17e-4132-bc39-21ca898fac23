<view><block wx:if="{{isload}}"><block><view class="container"><view class="topsearch flex-y-center"><view class="f1 flex-y-center"><image class="img" src="/static/img/search_ico.png"></image><input placeholder="输入商品名称" placeholder-style="font-size:24rpx;color:#C2C2C2" data-event-opts="{{[['confirm',[['searchConfirm',['$event']]]],['input',[['searchChange',['$event']]]]]}}" value="{{keyword}}" bindconfirm="__e" bindinput="__e"/></view></view><view class="datalist flex"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><view class="collage-product" data-url="{{linktype+'?id='+item.$orig.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="product-pic"><image src="{{item.$orig.pic}}" mode="widthFix"></image><view class="desc"><block wx:if="{{item.$orig.gua_num>0}}"><text>{{item.$orig.teamnum+"人拼团 "+item.$orig.gua_num+"人得商品"}}</text></block><block wx:else><text style="line-height:80rpx;">{{item.$orig.teamnum+"人拼团 "+item.$orig.gua_num+"人得商品"}}</text></block><block wx:if="{{item.$orig.linktype==1}}"><view><block wx:if="{{item.$orig.teamnum-item.$orig.gua_num>0&&item.$orig.bzjl_type==1}}"><text>{{item.$orig.teamnum-item.$orig.gua_num+"人"+(item.$orig.teamnum-item.$orig.gua_num>1?'各':'')+"得"+item.$orig.money+"元参与奖"}}</text></block><block wx:if="{{item.$orig.teamnum-item.$orig.gua_num>0&&item.$orig.bzjl_type==2}}"><text>{{item.$orig.teamnum-item.$orig.gua_num+"人"+(item.$orig.teamnum-item.$orig.gua_num>1?'各':'')+"得"+item.$orig.bzj_score+"积分"}}</text></block><block wx:if="{{item.$orig.teamnum-item.$orig.gua_num>0&&item.$orig.bzjl_type==3}}"><text>{{item.$orig.teamnum-item.$orig.gua_num+"人"+(item.$orig.teamnum-item.$orig.gua_num>1?'各':'')+"得"+item.$orig.bzj_commission+"元佣金"}}</text></block><block wx:if="{{item.$orig.teamnum-item.$orig.gua_num>0&&item.$orig.bzjl_type==4}}"><text>{{item.$orig.teamnum-item.$orig.gua_num+"人"+(item.$orig.teamnum-item.$orig.gua_num>1?'各':'')+"得"+item.$orig.money+"优惠券"}}</text></block></view></block><block wx:else><view><block wx:if="{{item.$orig.teamnum-item.$orig.gua_num>0}}"><text>{{item.$orig.teamnum-item.$orig.gua_num+"人"+(item.$orig.teamnum-item.$orig.gua_num>1?'各':'')+"得"+item.$orig.money+"元参与奖"}}</text></block></view></block></view></view><view class="product-info"><view class="p1">{{item.$orig.name}}</view><view class="p2"><view class="p2-1"><text class="t1" style="{{'color:'+(item.m0)+';'}}"><text style="font-size:24rpx;">￥</text>{{item.$orig.sell_price}}</text><block wx:if="{{item.$orig.market_price*1>item.$orig.sell_price*1}}"><text class="t2">{{"￥"+item.$orig.market_price}}</text></block></view></view><view class="p3"><view class="t1"><text class="team_text">{{item.$orig.teamnum+"人拼"}}</text>已拼成<text style="font-size:32rpx;color:#f40;padding:0 2rpx;">{{item.$orig.sales}}</text>件</view></view></view></view></block></block></view><block wx:if="{{nomore}}"><nomore vue-id="19a29fdd-1" bind:__l="__l"></nomore></block><block wx:if="{{nodata}}"><nodata vue-id="19a29fdd-2" bind:__l="__l"></nodata></block></view><button class="covermy" data-url="orderlist" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">我的拼团</button></block></block><block wx:if="{{loading}}"><loading vue-id="19a29fdd-3" bind:__l="__l"></loading></block><dp-tabbar vue-id="19a29fdd-4" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="19a29fdd-5" data-ref="popmsg" bind:__l="__l"></popmsg></view>