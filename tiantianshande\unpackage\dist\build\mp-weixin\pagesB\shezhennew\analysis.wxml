<view class="page"><view class="custom-navbar"><view data-event-opts="{{[['tap',[['goBack',['$event']]]]]}}" class="navbar-left" bindtap="__e"><text class="back-arrow">‹</text></view><view class="navbar-center"><text class="navbar-title">AI在线舌诊</text></view><view class="navbar-right"><text data-event-opts="{{[['tap',[['showTipModal',['$event']]]]]}}" class="test-btn" bindtap="__e">测试</text><text data-event-opts="{{[['tap',[['openQuestionModal',['$event']]]]]}}" class="test-btn" bindtap="__e">测试2</text></view></view><view class="content-container"><view class="image-section"><view class="tongue-display"><image class="tongue-image" src="{{tongueImageUrl||pre_url+'/static/img/shezhen-analysis-frame.png'}}"></image><block wx:if="{{currentStep<=6}}"><view class="scan-effect"><view class="scan-line"></view><view class="scan-border"></view></view></block></view></view><view class="analysis-section"><view class="analysis-header"><text class="analysis-title">智能分析中</text></view><view class="progress-list"><block wx:for="{{displayedSteps}}" wx:for-item="step" wx:for-index="__i0__" wx:key="id"><view class="{{['progress-item',(step.completed)?'completed':'',(currentStep===step.id)?'active':'']}}"><text class="progress-text">{{step.text}}</text><view class="progress-indicator"><block wx:if="{{currentStep<step.id}}"><view class="status-circle"></view></block><block wx:else><block wx:if="{{currentStep===step.id&&!step.completed}}"><view class="status-loading"></view></block><block wx:else><block wx:if="{{step.completed}}"><image class="status-check" src="{{pre_url+'/static/img/shezhen-duihao.png'}}"></image></block></block></block></view></view></block></view></view></view><block wx:if="{{showModal}}"><view data-event-opts="{{[['tap',[['hideModal',['$event']]]]]}}" class="modal-overlay" bindtap="__e"><view data-event-opts="{{[['tap',[['',['$event']]]]]}}" class="modal-container" catchtap="__e"><view class="modal-header"><text class="modal-title">AI在线舌诊</text></view><view class="modal-tip-header"><image class="tip-warning-icon" src="{{pre_url+'/static/img/shezhen-warning-icon.png'}}"></image><view class="tip-title-row"><text class="tip-title">温馨提示</text><image class="tip-close-icon" src="{{pre_url+'/static/img/shezhen-close-icon.png'}}" data-event-opts="{{[['tap',[['hideModal',['$event']]]]]}}" bindtap="__e"></image></view></view><view class="modal-content"><text class="tip-content">请拍摄带有舌头，清晰的彩色照片，或使用后置摄像头拍摄</text></view><view class="modal-actions"><view data-event-opts="{{[['tap',[['retakePhoto',['$event']]]]]}}" class="action-btn primary" bindtap="__e"><text class="btn-text">重新拍摄</text></view><view data-event-opts="{{[['tap',[['switchToBackCamera',['$event']]]]]}}" class="action-btn secondary" bindtap="__e"><text class="btn-text secondary">切换后置摄像头拍摄</text></view></view></view></view></block><block wx:if="{{showQuestionModal}}"><view data-event-opts="{{[['tap',[['hideQuestionModal',['$event']]]]]}}" class="question-modal-overlay" bindtap="__e"><view data-event-opts="{{[['tap',[['',['$event']]]]]}}" class="question-modal-container" catchtap="__e"><view class="background-analysis"><view class="bg-image-section"><image class="bg-tongue-image" src="{{tongueImageUrl||pre_url+'/static/img/shezhen-analysis-frame.png'}}"></image></view><view class="bg-analysis-section"><view class="bg-analysis-header"><text class="bg-analysis-title">智能分析中</text></view><view class="bg-progress-list"><view class="bg-progress-item"><text class="bg-progress-text">解析舌色...</text><view class="bg-status-loading"></view></view><view class="bg-progress-item"><text class="bg-progress-text">解析舌苔...</text><image class="bg-status-check" src="{{pre_url+'/static/img/shezhen-duihao.png'}}"></image></view><view class="bg-progress-item"><text class="bg-progress-text">解析舌型...</text><image class="bg-status-check" src="{{pre_url+'/static/img/shezhen-duihao.png'}}"></image></view><view class="bg-progress-item"><text class="bg-progress-text">分析体质...</text><image class="bg-status-check" src="{{pre_url+'/static/img/shezhen-duihao.png'}}"></image></view></view></view></view><view class="question-card"><view class="question-header"><text class="question-header-text">再答1题领取报告</text></view><view class="question-content"><text class="question-text">是否感觉舌头黏糊糊的？</text><view class="answer-buttons"><view data-event-opts="{{[['tap',[['selectAnswer',['yes']]]]]}}" class="{{['answer-btn',(selectedAnswer==='yes')?'selected':'']}}" bindtap="__e"><text class="answer-text">有</text></view><view data-event-opts="{{[['tap',[['selectAnswer',['no']]]]]}}" class="{{['answer-btn',(selectedAnswer==='no')?'selected':'']}}" bindtap="__e"><text class="answer-text">没有</text></view></view></view></view></view></view></block></view>