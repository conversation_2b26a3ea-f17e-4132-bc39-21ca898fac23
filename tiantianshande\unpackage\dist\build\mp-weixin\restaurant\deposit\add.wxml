<view class="container"><block wx:if="{{isload}}"><block><form data-event-opts="{{[['submit',[['formSubmit',['$event']]]]]}}" bindsubmit="__e"><view class="form"><view class="form-item"><text class="label">物品名称</text><input class="input" type="text" placeholder="请输入物品名称" name="name" placeholder-style="color:#BBBBBB;font-size:28rpx"/></view><view class="form-item"><text class="label">寄存数量</text><input class="input" type="text" placeholder="请输入寄存数量" name="num" placeholder-style="color:#BBBBBB;font-size:28rpx"/></view><view class="form-item"><text class="label">寄存人</text><input class="input" type="text" placeholder="请输入姓名" name="linkman" placeholder-style="color:#BBBBBB;font-size:28rpx"/></view><view class="form-item"><text class="label">手机号</text><input class="input" type="text" placeholder="请输入手机号" name="tel" placeholder-style="color:#BBBBBB;font-size:28rpx"/></view></view><view class="form"><view class="form-item"><text class="label">备注</text><input class="input" type="text" placeholder="如您有其他需求请填写" name="message" placeholder-style="color:#BBBBBB;font-size:28rpx"/></view></view><view class="form"><view class="flex-col"><text class="label" style="height:98rpx;line-height:98rpx;font-size:30rpx;">寄存拍照</text><view class="flex" style="flex-wrap:wrap;padding-bottom:20rpx;"><block wx:for="{{pic}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="layui-imgbox"><view class="layui-imgbox-close" data-index="{{index}}" data-field="pic" data-event-opts="{{[['tap',[['removeimg',['$event']]]]]}}" bindtap="__e"><image src="/static/img/ico-del.png"></image></view><view class="layui-imgbox-img"><image src="{{item}}" data-url="{{item}}" mode="widthFix" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image></view></view></block><block wx:if="{{$root.g0==0}}"><view class="uploadbtn" style="{{('background:url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 60rpx;background-size:80rpx 80rpx;background-color:#F3F3F3;')}}" data-field="pic" data-event-opts="{{[['tap',[['uploadimg',['$event']]]]]}}" bindtap="__e"></view></block></view><input type="text" hidden="true" name="pic" maxlength="-1" value="{{$root.g1}}"/></view></view><button class="btn" style="{{('background:linear-gradient(90deg,'+$root.m0+' 0%,rgba('+$root.m1+',0.8) 100%)')}}" form-type="submit">提交寄存</button></form></block></block><block wx:if="{{loading}}"><loading vue-id="110b249a-1" bind:__l="__l"></loading></block><dp-tabbar vue-id="110b249a-2" opt="{{opt}}" data-event-opts="{{[['^getmenuindex',[['getmenuindex']]]]}}" bind:getmenuindex="__e" bind:__l="__l"></dp-tabbar></view>