require('../common/vendor.js');(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["admin/order/cycleplandetail"],{"06a9":function(t,e,n){"use strict";n.r(e);var i=n("9c08"),o=n.n(i);for(var a in i)["default"].indexOf(a)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(a);e["default"]=o.a},2774:function(t,e,n){},7632:function(t,e,n){"use strict";(function(t,e){var i=n("47a9");n("06e9");i(n("3240"));var o=i(n("976f"));t.__webpack_require_UNI_MP_PLUGIN__=n,e(o.default)}).call(this,n("3223")["default"],n("df3c")["createPage"])},"77f5":function(t,e,n){"use strict";var i=n("dfe6"),o=n.n(i);o.a},"976f":function(t,e,n){"use strict";n.r(e);var i=n("be45"),o=n("06a9");for(var a in o)["default"].indexOf(a)<0&&function(t){n.d(e,t,(function(){return o[t]}))}(a);n("bb9f"),n("77f5");var s=n("828b"),r=Object(s["a"])(o["default"],i["b"],i["c"],!1,null,"8528e35a",null,!1,i["a"],void 0);e["default"]=r.exports},"9c08":function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var i=getApp(),o={data:function(){return{isload:!1,detail:{},order:{},storeinfo:{},pre_url:i.globalData.pre_url,advance_days:"",optionList:[{title:"顺延一日",text:"默认收获日顺延一日",value:1},{title:"顺延二日",text:"默认收获日顺延二日",value:2},{title:"顺延三日",text:"默认收获日顺延三日",value:3}],listIndex:null,alertStatus:!1}},onLoad:function(t){this.opt=i.getopts(t),this.getdata()},methods:{showhxqr:function(){this.$refs.dialogHxqr.open()},closeHxqr:function(){this.$refs.dialogHxqr.close()},orderCollect:function(t){var e=this;i.confirm("确定已收到货吗?",(function(){i.showLoading(),i.post("ApiAdminOrder/cycleorderStageCollect",{id:e.opt.id},(function(t){i.showLoading(!1),i.success(t.msg),setTimeout((function(){e.getdata()}),1e3)}))}))},orderhexiao:function(t){var e=this;i.confirm("确定客户已取货吗?",(function(){i.showLoading(),i.post("ApiAdminOrder/cycleorderHexiao",{id:e.opt.id},(function(t){i.showLoading(!1),i.success(t.msg),setTimeout((function(){e.getdata()}),1e3)}))}))},logistics:function(t){var e=t.currentTarget.dataset.express_com,n=t.currentTarget.dataset.express_no;i.goto("/pagesExt/cycle/logistics?express_com="+e+"&express_no="+n)},getdata:function(){var t=this;i.showLoading(),i.get("ApiCycle/getCycleDetail",{id:t.opt.id},(function(e){t.detail=e.data,t.order=e.data.order,t.storeinfo=e.data.storeinfo,i.showLoading(!1),t.isload=!0}))},alertClick:function(){this.alertStatus?this.alertStatus=!1:this.alertStatus=!0},itemClick:function(t){this.listIndex=t,this.advance_days=this.optionList[this.listIndex].value},advanceDays:function(){var t=this;i.showLoading(),i.post("ApiCycle/advanceDays",{id:t.opt.id,days:this.advance_days},(function(e){i.success(e.msg),t.alertStatus=!1,t.getdata(),i.showLoading(!1)}))}}};e.default=o},bb9f:function(t,e,n){"use strict";var i=n("2774"),o=n.n(i);o.a},be45:function(t,e,n){"use strict";n.d(e,"b",(function(){return o})),n.d(e,"c",(function(){return a})),n.d(e,"a",(function(){return i}));var i={uniPopup:function(){return Promise.all([n.e("common/vendor"),n.e("components/uni-popup/uni-popup")]).then(n.bind(null,"ca44a"))}},o=function(){var t=this.$createElement;this._self._c},a=[]},dfe6:function(t,e,n){}},[["7632","common/runtime","common/vendor"]]]);