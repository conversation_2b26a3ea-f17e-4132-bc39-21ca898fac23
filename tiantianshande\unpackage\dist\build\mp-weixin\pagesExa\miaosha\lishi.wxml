<view class="container"><block wx:if="{{isload}}"><block><view class="total-info"><text>{{"历史交易总数："+totalCount+" 笔"}}</text><text>{{"历史交易总额：￥"+totalAmount+" 元"}}</text></view><view class="order-content"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><view class="order-box"><view class="order-head"><view class="order-info"><text class="order-id">{{"订单号："+item.$orig.order_id}}</text><text class="order-time">{{"清算时间："+item.m0}}</text></view><text class="changci-name">{{item.$orig.changci_name}}</text></view><view class="order-content-details"><image class="product-image" src="{{item.$orig.goods_pic}}"></image><view class="detail-info"><text class="detail-name">{{item.$orig.goods_name}}</text><view class="user-info"><text>{{"买家ID："+item.$orig.buyer_id}}</text><text>{{"卖家ID："+item.$orig.seller_id}}</text></view><text class="detail-price">{{"成交价：￥"+item.$orig.price}}</text></view></view><block wx:if="{{item.$orig.express_no}}"><view class="express-info"><view class="express-title">物流信息</view><view class="express-detail"><view class="express-item"><text class="express-label">物流公司：</text><text class="express-value">{{item.$orig.express_company||'暂无'}}</text></view><view class="express-item"><text class="express-label">物流单号：</text><text class="express-value">{{item.$orig.express_no}}</text><text data-event-opts="{{[['tap',[['copyExpress',['$0'],[[['datalist','',index,'express_no']]]]]]]}}" class="copy-btn" bindtap="__e">复制</text></view><view class="express-item"><text class="express-label">发货时间：</text><text class="express-value">{{item.m1}}</text></view></view></view></block></view></block></block></view><block wx:if="{{nomore}}"><nomore vue-id="0af7dd13-1" bind:__l="__l"></nomore></block><block wx:if="{{nodata}}"><nodata vue-id="0af7dd13-2" bind:__l="__l"></nodata></block></block></block><block wx:if="{{loading}}"><loading vue-id="0af7dd13-3" bind:__l="__l"></loading></block><dp-tabbar vue-id="0af7dd13-4" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="0af7dd13-5" data-ref="popmsg" bind:__l="__l"></popmsg></view>