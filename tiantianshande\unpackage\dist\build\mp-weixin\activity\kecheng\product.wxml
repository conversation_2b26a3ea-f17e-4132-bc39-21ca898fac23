<view><block wx:if="{{isload}}"><block><view class="swiper-container"><swiper class="swiper" indicator-dots="{{false}}" autoplay="{{true}}" interval="{{500000}}" data-event-opts="{{[['change',[['swiperChange',['$event']]]]]}}" bindchange="__e"><block wx:for="{{product.pics}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><swiper-item class="swiper-item"><view class="swiper-item-view"><image class="img" src="{{item}}" mode="widthFix"></image></view></swiper-item></block></block></swiper><view class="imageCount">{{current+1+"/"+$root.g0}}</view></view><view class="header"><view class="price_share"><view class="title">{{product.name}}</view><view data-event-opts="{{[['tap',[['shareClick',['$event']]]]]}}" class="share" bindtap="__e"><image class="img" src="{{pre_url+'/static/img/share.png'}}"></image><text class="txt">分享</text></view></view><view class="pricebox flex"><view class="price"><block wx:if="{{product.price>0}}"><view class="f1" style="{{'color:'+($root.m0)+';'}}">￥<text style="font-size:36rpx;">{{product.price}}</text></view></block><block wx:else><view class="f1" style="{{'color:'+($root.m1)+';'}}"><text style="font-size:36rpx;">免费</text></view></block><block wx:if="{{product.market_price>0}}"><view class="f2">{{"￥"+product.market_price}}</view></block></view><view class="sales_stock"><view class="f1">{{product.count+"节课"}}<block wx:if="{{sysset&&sysset.show_join_num==1}}"><block><text style="margin:0 6rpx;">|</text>{{"已有"+product.join_num+"人学习"}}</block></block></view></view></view><block wx:if="{{kechengset.showcommission==1&&product.commission>0}}"><view class="commission" style="{{'background:'+('rgba('+$root.m2+',0.1)')+';'+('color:'+($root.m3)+';')}}">{{"分享好友购买预计可得"+$root.m4+"："}}<text style="font-weight:bold;padding:0 2px;">{{product.commission}}</text>{{product.commission_desc}}</view></block><block wx:if="{{kechengset.show_lvupsavemoney==1&&product.upgrade_text&&product.price>0}}"><view class="upsavemoney shining-effect" style="{{'background:'+('linear-gradient(90deg, rgb(255, 180, 153) 0%, #ffcaa8 100%)')+';'+('color:'+('#653a2b')+';')}}"><view class="flex1">{{product.upgrade_text+''}}</view><view style="margin-left:20rpx;font-weight:bold;display:flex;align-items:center;color:#ca4312;" data-url="/pagesExa/my/levelup" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">立即升级<image style="width:30rpx;height:30rpx;" src="{{pre_url+'/static/img/arrowright2.png'}}"></image></view></view></block></view><view class="detail"><view class="detail_title"><view class="order-tab2"><view class="{{['item '+(curTopIndex==1?'on':'')]}}" data-index="{{1}}" data-event-opts="{{[['tap',[['switchTopTab',['$event']]]]]}}" bindtap="__e">课程介绍<view class="after" style="{{'background:'+($root.m5)+';'}}"></view></view><view class="{{['item '+(curTopIndex==2?'on':'')]}}" data-index="{{2}}" data-event-opts="{{[['tap',[['switchTopTab',['$event']]]]]}}" bindtap="__e">课程目录<view class="after" style="{{'background:'+($root.m6)+';'}}"></view></view><block wx:if="{{$root.g1}}"><view class="{{['item '+(curTopIndex==3?'on':'')]}}" data-index="{{3}}" data-event-opts="{{[['tap',[['switchTopTab',['$event']]]]]}}" bindtap="__e">训练营<view class="after" style="{{'background:'+($root.m7)+';'}}"></view></view></block></view></view><block wx:if="{{curTopIndex==1}}"><block><dp vue-id="640b8f79-1" pagecontent="{{pagecontent}}" bind:__l="__l"></dp></block></block><block wx:if="{{curTopIndex==2}}"><block><block wx:for="{{datalist}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="mulubox flex" data-mianfei="{{item.ismianfei}}" data-url="{{'mldetail?id='+item.id+'&kcid='+item.kcid}}" data-event-opts="{{[['tap',[['todetail',['$event']]]]]}}" bindtap="__e"><view class="left_box"><block wx:if="{{item.kctype==1}}"><image src="{{pre_url+'/static/img/tw_icon.png'}}"></image></block><block wx:if="{{item.kctype==2}}"><image src="{{pre_url+'/static/img/mp3_icon.png'}}"></image></block><block wx:if="{{item.kctype==3}}"><image src="{{pre_url+'/static/img/video_icon.png'}}"></image></block></view><view class="right_box flex"><view class="title_box"><view class="t1">{{''+item.name}}</view><view><block wx:if="{{item.kctype==1}}"><text class="t2">图文课程</text></block><block wx:if="{{item.kctype==2}}"><text class="t2">音频课程</text></block><block wx:if="{{item.kctype==3}}"><text class="t2">视频课程</text></block><block wx:if="{{item.kctype!=1}}"><text class="t2">{{'时长: '+(item.duration?item.duration:'未知')}}</text></block></view></view><block wx:if="{{item.ismianfei&&product.price>0}}"><view class="skbtn">试看</view></block><block wx:if="{{product.price==0}}"><view class="skbtn">免费</view></block></view></view></block><block wx:if="{{$root.g2}}"><view data-event-opts="{{[['tap',[['loadMore',['$event']]]]]}}" class="load-more-btn" style="{{'background:'+($root.m8)+';'}}" bindtap="__e"><block wx:if="{{loading}}"><view class="load-icon"></view></block>{{''+(loading?'加载中':'加载更多')+''}}</view></block><block wx:if="{{nomore}}"><nomore vue-id="640b8f79-2" text="没有更多课程了" bind:__l="__l"></nomore></block><block wx:if="{{nodata}}"><nodata vue-id="640b8f79-3" text="没有查找到相关课程" bind:__l="__l"></nodata></block></block></block><block wx:if="{{curTopIndex==3}}"><block><view class="article-list"><block wx:for="{{bind_articles}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="article-item" data-id="{{item.id}}" data-event-opts="{{[['tap',[['toArticle',['$event']]]]]}}" bindtap="__e"><view class="article-content"><view class="article-title">{{item.name}}</view><view class="article-desc">{{item.subname}}</view></view><view class="article-image"><image src="{{item.pic}}" mode="aspectFill"></image></view></view></block></view><block wx:if="{{$root.g3===0}}"><nodata vue-id="640b8f79-4" text="暂无训练营相关文章" bind:__l="__l"></nodata></block></block></block></view><view><block wx:if="{{$root.g4>0}}"><view class="xihuan"><view class="xihuan-line"></view><view class="xihuan-text"><image class="img" src="{{pre_url+'/static/img/xihuan.png'}}"></image><text class="txt">为您推荐</text></view><view class="xihuan-line"></view></view></block><view class="prolist"><dp-kecheng-item vue-id="640b8f79-5" data="{{tjdatalist}}" menuindex="{{menuindex}}" data-event-opts="{{[['^addcart',[['addcart']]]]}}" bind:addcart="__e" bind:__l="__l"></dp-kecheng-item></view></view><block wx:if="{{noteDialogVisible}}"><view class="popup__container"><view data-event-opts="{{[['tap',[['hideNoteDialog',['$event']]]]]}}" class="popup__overlay" catchtap="__e"></view><view class="popup__modal note-modal"><view class="popup__title"><text class="popup__title-text">我的课程笔记</text><image class="popup__close" style="width:36rpx;height:36rpx;" src="{{pre_url+'/static/img/close.png'}}" data-event-opts="{{[['tap',[['hideNoteDialog',['$event']]]]]}}" catchtap="__e"></image></view><view class="popup__content"><view class="note-tabs"><view class="{{['tab-item '+(noteTabIndex==1?'active':'')]}}" data-index="{{1}}" data-event-opts="{{[['tap',[['switchNoteTab',['$event']]]]]}}" bindtap="__e"><text>添加笔记</text></view><view class="{{['tab-item '+(noteTabIndex==2?'active':'')]}}" data-index="{{2}}" data-event-opts="{{[['tap',[['switchNoteTab',['$event']]]]]}}" bindtap="__e"><text>我的笔记</text></view></view><block wx:if="{{noteTabIndex==1}}"><view class="add-note-content"><view class="form-group"><view class="form-label">笔记内容</view><textarea class="note-textarea" placeholder="请输入您的学习笔记..." maxlength="1000" show-count="{{true}}" data-event-opts="{{[['input',[['__set_model',['$0','content','$event',[]],['noteForm']]]]]}}" value="{{noteForm.content}}" bindinput="__e"></textarea></view><view class="form-group"><view class="form-label">学习进度</view><view class="progress-input"><slider value="{{noteForm.study_progress}}" min="{{0}}" max="{{100}}" step="{{1}}" show-value="{{true}}" activeColor="#FF5347" data-event-opts="{{[['change',[['onProgressChange',['$event']]]]]}}" bindchange="__e"></slider><text class="progress-text">{{noteForm.study_progress+"%"}}</text></view></view><view class="form-actions"><button data-event-opts="{{[['tap',[['resetNoteForm',['$event']]]]]}}" class="btn-cancel" bindtap="__e">重置</button><button data-event-opts="{{[['tap',[['submitNote',['$event']]]]]}}" class="btn-submit" style="{{'background:'+($root.m9)+';'}}" bindtap="__e">{{''+(editingNoteId?'更新笔记':'保存笔记')+''}}</button></view></view></block><block wx:if="{{noteTabIndex==2}}"><view class="my-notes-content"><view class="notes-filter"><view class="filter-item"><text class="filter-label">筛选：</text><picker value="{{filterIndex}}" range="{{filterOptions}}" data-event-opts="{{[['change',[['onFilterChange',['$event']]]]]}}" bindchange="__e"><view class="picker-text">{{filterOptions[filterIndex]}}</view></picker></view></view><block wx:if="{{$root.g5>0}}"><view class="notes-list"><block wx:for="{{$root.l0}}" wx:for-item="note" wx:for-index="index" wx:key="id"><view class="note-item"><view class="note-header"><view class="note-time">{{note.$orig.createtime_format}}</view><view class="note-actions"><text class="action-btn edit-btn" data-note="{{note.g6}}" data-event-opts="{{[['tap',[['editNote',['$event']]]]]}}" bindtap="__e">编辑</text><text class="action-btn delete-btn" data-id="{{note.$orig.id}}" data-event-opts="{{[['tap',[['deleteNote',['$event']]]]]}}" bindtap="__e">删除</text></view></view><view class="note-content">{{note.$orig.content}}</view><view class="note-info"><text class="note-progress">{{"学习进度: "+note.$orig.study_progress+"%"}}</text><block wx:if="{{note.$orig.chapter_name}}"><text class="note-chapter">{{"章节: "+note.$orig.chapter_name}}</text></block></view></view></block></view></block><block wx:else><view class="no-notes"><image class="empty-icon" src="{{pre_url+'/static/img/empty.png'}}"></image><text class="empty-text">暂无笔记记录</text><text class="empty-tip">开始学习并记录您的想法吧！</text></view></block><block wx:if="{{$root.g7}}"><view data-event-opts="{{[['tap',[['loadMoreNotes',['$event']]]]]}}" class="load-more-notes" bindtap="__e"><text>{{loadingNotes?'加载中...':'加载更多'}}</text></view></block></view></block></view></view></view></block><view style="width:100%;height:140rpx;"></view><block wx:if="{{product.status==1}}"><view class="{{['bottombar','flex-row',menuindex>-1?'tabbarbot':'notabbarbot']}}"><view class="f1"><view class="item" data-url="/pages/index/index" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image class="img" src="{{pre_url+'/static/img/shou.png'}}"></image><view class="t1">首页</view></view><block wx:if="{{kfurl!='contact::'}}"><view class="item" data-url="{{kfurl}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image class="img" src="{{pre_url+'/static/img/kefu.png'}}"></image><view class="t1">客服</view></view></block><block wx:else><button class="item" open-type="contact"><image class="img" src="{{pre_url+'/static/img/kefu.png'}}"></image><view class="t1">客服</view></button></block><view data-event-opts="{{[['tap',[['addfavorite',['$event']]]]]}}" class="item" bindtap="__e"><image class="img" src="{{pre_url+'/static/img/shoucang.png'}}"></image><view class="t1">{{isfavorite?'已收藏':'收藏'}}</view></view></view><view class="op"><block wx:if="{{product.price==0||product.ispay==1}}"><view class="tobuy flex-x-center flex-y-center" style="{{'background:'+($root.m10)+';'}}" data-url="{{'mldetail?kcid='+product.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">立即学习</view></block><block wx:else><view data-event-opts="{{[['tap',[['tobuy',['$event']]]]]}}" class="tobuy flex-x-center flex-y-center" style="{{'background:'+($root.m11)+';'}}" bindtap="__e">立即购买</view></block></view></view></block><scrolltop vue-id="640b8f79-6" isshow="{{scrolltopshow}}" bind:__l="__l"></scrolltop><block wx:if="{{sharetypevisible}}"><view class="popup__container"><view data-event-opts="{{[['tap',[['handleClickMask',['$event']]]]]}}" class="popup__overlay" catchtap="__e"></view><view class="popup__modal" style="height:320rpx;min-height:320rpx;"><view class="popup__content"><view class="sharetypecontent"><block wx:if="{{$root.m12=='app'}}"><view data-event-opts="{{[['tap',[['shareapp',['$event']]]]]}}" class="f1" bindtap="__e"><image class="img" src="{{pre_url+'/static/img/weixin.png'}}"></image><text class="t1">分享给好友</text></view></block><block wx:else><block wx:if="{{$root.m13=='mp'}}"><view data-event-opts="{{[['tap',[['sharemp',['$event']]]]]}}" class="f1" bindtap="__e"><image class="img" src="{{pre_url+'/static/img/weixin.png'}}"></image><text class="t1">分享给好友</text></view></block><block wx:else><block wx:if="{{$root.m14=='h5'}}"><view data-event-opts="{{[['tap',[['sharemp',['$event']]]]]}}" class="f1" bindtap="__e"><image class="img" src="{{pre_url+'/static/img/weixin.png'}}"></image><text class="t1">分享给好友</text></view></block><block wx:else><button class="f1" open-type="share"><image class="img" src="{{pre_url+'/static/img/weixin.png'}}"></image><text class="t1">分享给好友</text></button></block></block></block><view data-event-opts="{{[['tap',[['showPoster',['$event']]]]]}}" class="f2" bindtap="__e"><image class="img" src="{{pre_url+'/static/img/sharepic.png'}}"></image><text class="t1">生成分享图片</text></view></view></view></view></view></block><block wx:if="{{showposter}}"><view class="posterDialog"><view class="main"><view data-event-opts="{{[['tap',[['posterDialogClose',['$event']]]]]}}" class="close" bindtap="__e"><image class="img" src="{{pre_url+'/static/img/close.png'}}"></image></view><view class="content"><image class="img" src="{{posterpic}}" mode="widthFix" data-url="{{posterpic}}" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image></view></view></view></block></block></block><block wx:if="{{loading}}"><loading vue-id="640b8f79-7" bind:__l="__l"></loading></block><dp-tabbar vue-id="640b8f79-8" opt="{{opt}}" data-event-opts="{{[['^getmenuindex',[['getmenuindex']]]]}}" bind:getmenuindex="__e" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="640b8f79-9" data-ref="popmsg" bind:__l="__l"></popmsg></view>