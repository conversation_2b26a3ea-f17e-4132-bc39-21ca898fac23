<view class="container"><block wx:if="{{isload}}"><block><view><view class="search-container" style="{{'background:'+($root.m0)+';'}}"><view class="search-box"><picker style="font-size:28rpx;padding:10rpx;height:70rpx;border-radius:4px;flex:1;" value="{{express_index}}" range="{{expressdata}}" data-event-opts="{{[['change',[['expresschange',['$event']]]]]}}" bindchange="__e"><view class="picker" value="{{express_index}}" range="{{expressdata}}" data-event-opts="{{[['change',[['expresschange',['$event']]]]]}}" bindchange="__e">{{expressdata[express_index]?expressdata[express_index]:'请选择快递公司'}}</view></picker><image class="img" src="/static/img/exp_xiala.png"></image><input class="search-text" placeholder="输入快递单号或扫码查询" placeholder-style="color:#fff;font-size:24rpx" data-event-opts="{{[['confirm',[['searchConfirm',['$event']]]],['input',[['searchChange',['$event']]]]]}}" value="{{keyword}}" bindconfirm="__e" bindinput="__e"/><view data-event-opts="{{[['tap',[['saoyisao',['$event']]]]]}}" class="set" bindtap="__e"><image class="img" src="/static/img/ico-scan.png"></image></view></view></view><view class="wrap"><view class="top_title flex"><view class="tab" data-url="mail" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image src="{{pre_url+'/static/img/exp_mail.png'}}"></image><text>我要寄件</text></view><view class="tab" data-url="/pages/my/usercenter" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image src="{{pre_url+'/static/img/exp_my.png'}}"></image><text>个人中心</text></view></view><view class="list_box"><view class="tab-box flex"><view class="{{['tab2 '+(curTopIndex==1?'on':'')]}}" data-index="{{1}}" data-event-opts="{{[['tap',[['switchTopTab',['$event']]]]]}}" bindtap="__e">最近查询</view><view class="{{['tab2 '+(curTopIndex==2?'on':'')]}}" data-index="{{2}}" data-event-opts="{{[['tap',[['switchTopTab',['$event']]]]]}}" bindtap="__e">我的寄件</view></view><block wx:for="{{datalist}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block wx:if="{{curTopIndex==1}}"><view class="list" data-url="{{'/activity/express/logistics?com='+item.company+'&num='+item.num}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="text1 flex"><view>{{item.company+":"+item.num}}<image class="fuzhi" src="/static/img/exp_fuzhi.png"></image></view><text class="t3">查</text></view><view class="text2 flex"><view class="t1_box"><text>{{item.text}}</text></view></view></view></block></block><block wx:for="{{datalist}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block wx:if="{{curTopIndex==2}}"><view class="list" data-url="{{'/activity/express/kddetail?id='+item.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="text1 flex"><view class="com">{{item.company}}</view><text class="t4">{{item.status}}</text></view><view class="text2 flex"><view class="t2_box"><text>{{item.sendManPrintCity}}</text><text class="t2">{{item.sendManName}}</text></view><view class="t2_box"><image class="jiantou" src="/static/img/jiantou.png"></image></view><view class="t2_box"><text>{{item.recManPrintCity}}</text><text class="t2">{{item.recManName}}</text></view></view></view></block></block><block wx:if="{{nodata}}"><nodata vue-id="5f3f4125-1" bind:__l="__l"></nodata></block></view></view></view></block></block><block wx:if="{{loading}}"><loading vue-id="5f3f4125-2" bind:__l="__l"></loading></block><dp-tabbar vue-id="5f3f4125-3" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="5f3f4125-4" data-ref="popmsg" bind:__l="__l"></popmsg></view>