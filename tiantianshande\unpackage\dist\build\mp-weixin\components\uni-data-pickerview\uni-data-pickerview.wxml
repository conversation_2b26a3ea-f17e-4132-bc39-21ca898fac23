<view class="uni-data-pickerview data-v-fd012dd8"><scroll-view class="selected-area data-v-fd012dd8" scroll-x="true" scroll-y="false" show-scrollbar="{{false}}"><view class="selected-list data-v-fd012dd8"><block wx:for="{{selected}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block wx:if="{{item.text}}"><view data-event-opts="{{[['tap',[['handleSelect',[index]]]]]}}" class="{{['selected-item','data-v-fd012dd8',(index==selectedIndex)?'selected-item-active':'']}}" bindtap="__e"><text class="data-v-fd012dd8">{{item.text}}</text></view></block></block></view></scroll-view><view class="tab-c data-v-fd012dd8"><block wx:for="{{$root.l1}}" wx:for-item="child" wx:for-index="i" wx:key="i"><block wx:if="{{i==selectedIndex}}"><scroll-view class="list data-v-fd012dd8" scroll-y="{{true}}"><block wx:for="{{child.l0}}" wx:for-item="item" wx:for-index="j" wx:key="j"><view data-event-opts="{{[['tap',[['handleNodeClick',['$0',i,j],[[['dataList','',i],['','',j]]]]]]]}}" class="{{['item','data-v-fd012dd8',(!!item.$orig.disable)?'is-disabled':'']}}" bindtap="__e"><text class="item-text data-v-fd012dd8">{{item.$orig.text}}</text><block wx:if="{{item.g0}}"><view class="check data-v-fd012dd8"></view></block></view></block></scroll-view></block></block><block wx:if="{{loading}}"><view class="loading-cover data-v-fd012dd8"><uni-load-more class="load-more data-v-fd012dd8" vue-id="78e2714e-1" contentText="{{loadMore}}" status="loading" bind:__l="__l"></uni-load-more></view></block><block wx:if="{{errorMessage}}"><view class="error-message data-v-fd012dd8"><text class="error-text data-v-fd012dd8">{{errorMessage}}</text></view></block></view></view>