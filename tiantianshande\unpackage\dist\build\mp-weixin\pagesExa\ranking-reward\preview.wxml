<view class="container"><view class="rule-header"><view class="rule-name">{{ruleInfo.name}}</view><view class="rule-desc"><text class="desc-label">奖励模式：</text><block wx:if="{{ruleInfo.reward_mode===1}}"><text>根据自身消费</text></block><block wx:else><block wx:if="{{ruleInfo.reward_mode===2}}"><text>根据排名</text></block></block></view><view class="rule-desc"><text class="desc-label">排名类型：</text><text>{{ruleInfo.rank_type_text}}</text></view><view class="rule-desc"><text class="desc-label">奖励比例：</text><text>{{ruleInfo.total_reward_rate+"%"}}</text></view></view><block wx:if="{{hasRankInfo}}"><view class="my-rank-info"><view class="rank-title">我的排名</view><view class="rank-content"><view class="rank-num-box"><text class="rank-num">{{myRankInfo.rank||'--'}}</text><text class="rank-label">当前排名</text></view><view class="rank-divider"></view><view class="rank-num-box"><text class="rank-num">{{myRankInfo.count_num||0}}</text><text class="rank-label">{{ruleInfo.rank_type===1?'下级人数':'特定等级人数'}}</text></view><view class="rank-divider"></view><view class="rank-num-box"><text class="rank-num reward-color">{{myRankInfo.reward_amount||'0.00'}}</text><text class="rank-label">预计奖励</text></view></view></view></block><view class="pool-info"><view class="pool-flex"><view class="pool-divider"></view><view class="pool-item"><view class="pool-label">分红池总额</view><view class="pool-value reward-color">{{"¥"+(pool_amount||'0.00')}}</view></view></view></view><view class="period-selector"><picker mode="date" fields="month" value="{{selectedDate}}" data-event-opts="{{[['change',[['periodChange',['$event']]]]]}}" bindchange="__e"><view class="picker-box"><text>{{selectedPeriodText}}</text><text class="picker-arrow">▼</text></view></picker></view><view class="rank-list"><view class="list-header"><text class="header-item rank-col">排名</text><text class="header-item member-col">会员信息</text><text class="header-item count-col">{{ruleInfo.rank_type===1?'下级人数':'特定等级人数'}}</text><text class="header-item amount-col">奖励金额</text></view><block wx:if="{{$root.g0===0}}"><view class="no-data"><text>暂无排名数据</text></view></block><block wx:for="{{rankingList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="{{['list-item',(item.is_me)?'my-rank':'']}}"><view class="item-col rank-col"><view class="{{['rank-tag '+(index<3?'rank-'+(index+1):'')]}}">{{item.rank}}</view></view><view class="item-col member-col"><view class="member-info"><image class="member-avatar" src="{{item.avatar||'/static/images/default-avatar.png'}}" mode="aspectFill"></image><text class="member-name">{{item.member_info}}</text></view></view><view class="item-col count-col">{{item.count_num}}</view><view class="item-col amount-col reward-color">{{item.reward_amount}}</view></view></block></view><block wx:if="{{totalPages>1}}"><view class="pagination"><view data-event-opts="{{[['tap',[['prevPage',['$event']]]]]}}" class="{{['page-btn','prev-btn',(currentPage<=1)?'disabled':'']}}" bindtap="__e">上一页</view><view class="page-info">{{currentPage+"/"+totalPages}}</view><view data-event-opts="{{[['tap',[['nextPage',['$event']]]]]}}" class="{{['page-btn','next-btn',(currentPage>=totalPages)?'disabled':'']}}" bindtap="__e">下一页</view></view></block></view>