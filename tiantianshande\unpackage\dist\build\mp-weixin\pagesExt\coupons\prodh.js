require('../common/vendor.js');(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pagesExt/coupons/prodh"],{1392:function(t,e,a){"use strict";a.d(e,"b",(function(){return r})),a.d(e,"c",(function(){return o})),a.d(e,"a",(function(){return i}));var i={loading:function(){return a.e("components/loading/loading").then(a.bind(null,"ceaa"))},dpTabbar:function(){return a.e("components/dp-tabbar/dp-tabbar").then(a.bind(null,"b875"))},popmsg:function(){return a.e("components/popmsg/popmsg").then(a.bind(null,"2bf2"))}},r=function(){var t=this,e=t.$createElement,a=(t._self._c,t.isload?t.__map(t.allbuydata,(function(e,a){var i=t.__get_orig(e),r=t.__map(e.prodata,(function(e,a){var i=t.__get_orig(e),r=t.needChoose&&t.chooseIndex===a?t.t("color1"):null;return{$orig:i,m0:r}})),o=t.__map(e.freightList,(function(a,i){var r=t.__get_orig(a),o=e.freightkey==i?t.t("color1"):null,n=e.freightkey==i?t.t("color1rgb"):null;return{$orig:r,m1:o,m2:n}})),n=1==e.freightList[e.freightkey].minpriceset&&e.freightList[e.freightkey].minprice>0&&e.freightList[e.freightkey].minprice>e.product_price?(e.freightList[e.freightkey].minprice-e.product_price).toFixed(2):null,s=1==e.freightList[e.freightkey].pstype?t.__map(e.freightList[e.freightkey].storedata,(function(a,i){var r=t.__get_orig(a),o=e.freightList[e.freightkey].storekey==i?t.t("color1"):null;return{$orig:r,m3:o}})):null;return{$orig:i,l0:r,l1:o,g0:n,l2:s}})):null),i=t.isload?t.t("color1"):null,r=t.isload?t.t("color1rgb"):null,o=t.isload&&t.pstimeDialogShow?t.__map(t.allbuydata[t.nowbid].freightList[t.allbuydata[t.nowbid].freightkey].pstimeArr,(function(e,a){var i=t.__get_orig(e),r=t.allbuydata[t.nowbid].freight_time==e.value?t.t("color1"):null;return{$orig:i,m6:r}})):null;t.$mp.data=Object.assign({},{$root:{l3:a,m4:i,m5:r,l4:o}})},o=[]},"6a25":function(t,e,a){},"75d6":function(t,e,a){"use strict";(function(t){Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var a=getApp(),i={data:function(){return{opt:{},loading:!1,isload:!1,menuindex:-1,pre_url:a.globalData.pre_url,test:"test",havetongcheng:0,address:[],usescore:0,scoredk_money:0,totalprice:"0.00",bid:0,nowbid:0,needaddress:1,linkman:"",tel:"",userinfo:{},pstimeDialogShow:!1,pstimeIndex:-1,manjian_money:0,cxid:0,latitude:"",longitude:"",allbuydata:"",alltotalprice:"",needChoose:!1,chooseIndex:"",chooseInfo:""}},onLoad:function(t){this.opt=a.getopts(t),this.getdata()},onPullDownRefresh:function(){this.getdata()},methods:{getdata:function(){var t=this;a.get("ApiLipin2/prodh",{dhcode:t.opt.dhcode,cardno:t.opt.cardno},(function(e){0!=e.status?(t.havetongcheng=e.havetongcheng,t.address=e.address,t.linkman=e.linkman,t.tel=e.tel,t.userinfo=e.userinfo,t.allbuydata=e.allbuydata,t.needLocation=e.needLocation,t.calculatePrice(),t.loaded(),1==e.needLocation&&a.getLocation((function(e){var a=e.latitude,i=e.longitude;t.latitude=a,t.longitude=i;var r=t.allbuydata;for(var o in r){var n=r[o].freightList;for(var s in n)if(1==n[s].pstype){var l=n[s].storedata;if(l){for(var d in l)if(a&&i&&l[d].latitude&&l[d].longitude){var u=t.getDistance(a,i,l[d].latitude,l[d].longitude);l[d].juli=u}for(var d in l.sort((function(t,e){return t["juli"]-e["juli"]})),l)l[d].juli&&(l[d].juli=l[d].juli+"千米");r[o].freightList[s].storedata=l}}}t.allbuydata=r})),1==e.hdinfo.num_type&&(t.needChoose=!0)):e.msg?a.alert(e.msg,(function(){e.url?a.goto(e.url):a.goback()})):e.url?a.goto(e.url):a.alert("您没有权限购买该商品")}))},chooseClick:function(t,e){this.chooseIndex=e,this.chooseInfo=t},scoredk:function(t){var e=t.detail.value[0];e||(e=0),this.usescore=e,this.calculatePrice()},inputLinkman:function(t){this.linkman=t.detail.value},inputTel:function(t){this.tel=t.detail.value},inputfield:function(t){var e=this.allbuydata,a=t.currentTarget.dataset.bid,i=t.currentTarget.dataset.field;e[a][i]=t.detail.value,this.allbuydata=e},chooseAddress:function(){a.goto("/pages/address/address?fromPage=buy&type="+(1==this.havetongcheng?"1":"0"))},calculatePrice:function(){this.address;var t=this.allbuydata,e=0,a=0;for(var i in t){var r=parseFloat(t[i].product_price),o=(parseFloat(t[i].manjian_money),parseFloat(t[i].coupon_money),t[i].freightList[t[i].freightkey]),n=o?o["freight_price"]:0;1!=o.pstype&&3!=o.pstype&&4!=o.pstype&&(a=1),4==t[i].coupontype&&(n=0,0);var s=r;s<0&&(s=0),s+=n,t[i].freight_price=n.toFixed(2),t[i].totalprice=s.toFixed(2),e+=s,n}this.needaddress=a,e<0&&(e=0),e=e.toFixed(2),this.alltotalprice=e,this.allbuydata=t},changeFreight:function(t){var e=this.allbuydata,a=t.currentTarget.dataset.bid,i=t.currentTarget.dataset.index;e[a].freightList;e[a].freightkey=i,this.allbuydata=e,this.calculatePrice(),this.allbuydata[a].editorFormdata=[]},chooseFreight:function(e){var a=this,i=a.allbuydata,r=e.currentTarget.dataset.bid;console.log(r),console.log(i);for(var o=i[r].freightList,n=[],s=0;s<o.length;s++)n.push(o[s].name);t.showActionSheet({itemList:n,success:function(t){t.tapIndex>=0&&(i[r].freightkey=t.tapIndex,a.allbuydata=i,a.calculatePrice())}})},choosePstime:function(t){for(var e=this.allbuydata,i=t.currentTarget.dataset.bid,r=e[i].freightkey,o=e[i].freightList,n=(o[r],o[r].pstimeArr),s=[],l=0;l<n.length;l++)s.push(n[l].title);0!=s.length?(this.nowbid=i,this.pstimeDialogShow=!0,this.pstimeIndex=-1):a.alert("当前没有可选"+(1==o[r].pstype?"取货":"配送")+"时间段")},pstimeRadioChange:function(t){var e=this.allbuydata,a=t.currentTarget.dataset.index;console.log(a);var i=this.nowbid,r=e[i].freightkey,o=e[i].freightList,n=(o[r],o[r].pstimeArr),s=n[a];e[i].pstimetext=s.title,e[i].freight_time=s.value,this.allbuydata=e,this.pstimeDialogShow=!1},hidePstimeDialog:function(){this.pstimeDialogShow=!1},choosestore:function(t){var e=t.currentTarget.dataset.bid,a=t.currentTarget.dataset.index,i=this.allbuydata,r=i[e],o=r.freightkey;i[e].freightList[o].storekey=a,this.allbuydata=i},topay:function(t){var e=this.needaddress,i=this.address.id,r=this.linkman,o=this.tel,n=this.usescore,s=this.opt.frompage?this.opt.frompage:"",l=this.allbuydata;if(0==e&&(i=0),1!=e||void 0!=i)if(this.needChoose&&""==this.chooseInfo)a.error("请选择商品");else{var d=[];for(var u in l){var c=l[u].freightkey;if(1==l[u].freightList[c].pstimeset&&""==l[u].freight_time)return void a.error("请选择"+(1==l[u].freightList[c].pstype?"取货":"配送")+"时间");if(1==l[u].freightList[c].pstype)var h=l[u].freightList[c].storekey,f=l[u].freightList[c].storedata[h].id;else f=0;for(var g=l[u].freightList[c].formdata,p=t.detail.value,m={},b=0;b<g.length;b++){var v="form"+l[u].bid+"_"+b;if(1==g[b].val3&&(""===p[v]||void 0===p[v]||0==p[v].length))return void a.alert(g[b].val1+" 必填");"selector"==g[b].key&&(p[v]=g[b].val2[p[v]]),m["form"+b]=p[v]}var y=l[u].prodatastr;if(this.needChoose){var _=this.chooseInfo.product.id+","+this.chooseInfo.guige.id+","+this.chooseInfo.num;y=_}d.push({bid:l[u].bid,prodata:y,cuxiaoid:l[u].cuxiaoid,couponrid:l[u].couponrid,freight_id:l[u].freightList[c].id,freight_time:l[u].freight_time,storeid:f,formdata:m})}a.showLoading("提交中"),a.post("ApiLipin2/createOrder",{dhcode:this.opt.dhcode,cardno:this.opt.cardno,frompage:s,buydata:d,addressid:i,linkman:r,tel:o,usescore:n},(function(t){a.showLoading(!1),0!=t.status?a.alert(t.msg,(function(){a.goto("/pages/my/usercenter","reLaunch")})):a.error(t.msg)}))}else a.error("请选择收货地址")},handleClickMask:function(){},openLocation:function(e){var a=this.allbuydata,i=e.currentTarget.dataset.bid,r=e.currentTarget.dataset.freightkey,o=e.currentTarget.dataset.storekey,n=a[i].freightList[r],s=n.storedata[o];console.log(s);var l=parseFloat(s.latitude),d=parseFloat(s.longitude),u=s.name;t.openLocation({latitude:l,longitude:d,name:u,scale:13})},editorChooseImage:function(t){var e=this,i=t.currentTarget.dataset.bid,r=t.currentTarget.dataset.idx,o=e.allbuydata[i].editorFormdata;o||(o=[]),a.chooseImage((function(t){o[r]=t[0],console.log(o),e.allbuydata[i].editorFormdata=o,e.test=Math.random()}))},editorBindPickerChange:function(t){var e=t.currentTarget.dataset.bid,a=t.currentTarget.dataset.idx,i=t.detail.value,r=this.allbuydata[e].editorFormdata;r||(r=[]),r[a]=i,console.log(r),this.allbuydata[e].editorFormdata=r,this.test=Math.random()}}};e.default=i}).call(this,a("df3c")["default"])},"81f9":function(t,e,a){"use strict";var i=a("6a25"),r=a.n(i);r.a},a108:function(t,e,a){"use strict";a.r(e);var i=a("1392"),r=a("a2b0");for(var o in r)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return r[t]}))}(o);a("81f9");var n=a("828b"),s=Object(n["a"])(r["default"],i["b"],i["c"],!1,null,null,null,!1,i["a"],void 0);e["default"]=s.exports},a2b0:function(t,e,a){"use strict";a.r(e);var i=a("75d6"),r=a.n(i);for(var o in i)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(o);e["default"]=r.a},fae3:function(t,e,a){"use strict";(function(t,e){var i=a("47a9");a("06e9");i(a("3240"));var r=i(a("a108"));t.__webpack_require_UNI_MP_PLUGIN__=a,e(r.default)}).call(this,a("3223")["default"],a("df3c")["createPage"])}},[["fae3","common/runtime","common/vendor"]]]);