<view class="container"><block wx:if="{{isload}}"><block><view class="search-container"><view class="topsearch flex-y-center"><view class="f1 flex-y-center"><image class="img" src="/static/img/search_ico.png"></image><input placeholder="搜索你感兴趣的商家" placeholder-style="font-size:24rpx;color:#C2C2C2" confirm-type="search" data-event-opts="{{[['confirm',[['search',['$event']]]]]}}" value="{{keyword}}" bindconfirm="__e"/></view></view></view><view class="ind_business"><view class="ind_buslist" id="datalist"><block wx:for="{{datalist}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><view data-url="{{'/pages/index/index?select_bid='+item.id}}" data-event-opts="{{[['tap',[['selectBusiness',['$event']]]]]}}" bindtap="__e"><view class="ind_busbox flex1 flex-row"><view><view class="bus_title">{{item.name}}</view><view class="bus_address"><text class="x1">{{item.address}}</text><text class="x2">{{item.juli}}</text><text class="x2" data-phone="{{item.tel}}" data-event-opts="{{[['tap',[['phone',['$event']]]]]}}" catchtap="__e">{{item.tel}}</text></view></view></view></view></block></block><block wx:if="{{nomore}}"><nomore vue-id="2d306e4a-1" bind:__l="__l"></nomore></block><block wx:if="{{nodata}}"><nodata vue-id="2d306e4a-2" bind:__l="__l"></nodata></block></view></view></block></block><block wx:if="{{loading}}"><loading vue-id="2d306e4a-3" bind:__l="__l"></loading></block><dp-tabbar vue-id="2d306e4a-4" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="2d306e4a-5" data-ref="popmsg" bind:__l="__l"></popmsg></view>