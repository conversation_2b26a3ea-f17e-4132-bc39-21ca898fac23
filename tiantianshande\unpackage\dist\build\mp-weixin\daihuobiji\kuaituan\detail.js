require('../common/vendor.js');(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["daihuobiji/kuaituan/detail"],{"03cf":function(t,e,n){},"442f":function(t,e,n){},"53be":function(t,e,n){"use strict";n.d(e,"b",(function(){return o})),n.d(e,"c",(function(){return a})),n.d(e,"a",(function(){return i}));var i={dp:function(){return n.e("components/dp/dp").then(n.bind(null,"1506"))},buydialog:function(){return n.e("components/buydialog/buydialog").then(n.bind(null,"e5c3"))},uniPopup:function(){return Promise.all([n.e("common/vendor"),n.e("components/uni-popup/uni-popup")]).then(n.bind(null,"ca44a"))}},o=function(){var t=this,e=t.$createElement,n=(t._self._c,t.pinglunlist.length),i=t.bijis.length,o=t.t("color1"),a=t.t("color1rgb"),s=t.products.length,c=t.sharetypevisible?t.getplatform():null,u=t.sharetypevisible&&"app"!=c?t.getplatform():null,r=t.sharetypevisible&&"app"!=c&&"mp"!=u?t.getplatform():null,l=t.showposter?t.t("color1"):null,d=t.showposter?t.t("color1"):null,p=t.showposter?t.t("color1"):null,h=t.products.length;t.$mp.data=Object.assign({},{$root:{g0:n,g1:i,m0:o,m1:a,g2:s,m2:c,m3:u,m4:r,m5:l,m6:d,m7:p,g3:h}})},a=[]},"5d43":function(t,e,n){"use strict";n.r(e);var i=n("53be"),o=n("857e");for(var a in o)["default"].indexOf(a)<0&&function(t){n.d(e,t,(function(){return o[t]}))}(a);n("994d"),n("5fec");var s=n("828b"),c=Object(s["a"])(o["default"],i["b"],i["c"],!1,null,null,null,!1,i["a"],void 0);e["default"]=c.exports},"5fec":function(t,e,n){"use strict";var i=n("03cf"),o=n.n(i);o.a},"857e":function(t,e,n){"use strict";n.r(e);var i=n("ff3d"),o=n.n(i);for(var a in i)["default"].indexOf(a)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(a);e["default"]=o.a},"994d":function(t,e,n){"use strict";var i=n("442f"),o=n.n(i);o.a},fc25:function(t,e,n){"use strict";(function(t,e){var i=n("47a9");n("06e9");i(n("3240"));var o=i(n("5d43"));t.__webpack_require_UNI_MP_PLUGIN__=n,e(o.default)}).call(this,n("3223")["default"],n("df3c")["createPage"])},ff3d:function(t,e,n){"use strict";(function(t){var i=n("47a9");Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var o=i(n("7eb4")),a=i(n("ee10")),s=i(n("7ca3")),c=getApp(),u={data:function(){var t;return t={tid:0,detail:"",opt:"",pics:[],products:[],ind:0,pinglunlist:[],tuanzhang:"",rich_content:"",content:"",pagecontent:"",pagecontentm:"",sharetypevisible:!1,buydialogShow:!1,btntype:1,showLinkStatus:!1,menuindex:-1,cartnum:"",product_id:"",kfurl:"",timer:"",rid:1,bijis:[],cart_num:0,is_h5:!1,is_show:!0,currentProductIndex:0,showposter:!1,posterpic:"",matchedData:[],shopAllSelected:!1},(0,s.default)(t,"cart_num",0),(0,s.default)(t,"totalCommission",0),t},computed:{isAnyItemSelected:function(){return this.products.some((function(t){return t.selected}))}},onLoad:function(t){this.opt=t,this.tid=this.opt.id,this.getdata()},onPullDownRefresh:function(){this.getdata()},onShow:function(){this.getCart()},methods:{openShop:function(){this.$refs.shopShow.open("top"),this.getCart()},toggleSelection:function(t){this.$set(t,"selected",!t.selected),this.shopAllSelected=this.products.every((function(t){return t.selected}))},shopAllSelectedClick:function(){var t=this;this.shopAllSelected=!this.shopAllSelected,this.products.forEach((function(e){e.selected=t.shopAllSelected}))},shop:function(){var t=this;this.isAnyItemSelected?(this.products.forEach(function(){var e=(0,a.default)(o.default.mark((function e(n){return o.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,t.addCart(n.id);case 2:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}()),c.success("添加成功"),this.$refs.shopShow.close()):c.success("请选择商品")},addCartOne:function(t){var e=this;return(0,a.default)(o.default.mark((function n(){return o.default.wrap((function(n){while(1)switch(n.prev=n.next){case 0:e.addCart(t.id),c.success("添加成功");case 2:case"end":return n.stop()}}),n)})))()},addCart:function(t){var e=this;return new Promise((function(n,i){c.post("ApiShop/addcart",{proid:t,num:1},(function(t){e.cart_num++,n("添加成功")}))}))},previewImage:function(e){var n=[];n.push(e),t.previewImage({current:1,urls:n})},onSwiperChange:function(t){this.currentProductIndex=t.detail.current},buySameProduct:function(){var e=this.products[this.currentProductIndex];e&&e.id?t.navigateTo({url:"/shopPackage/shop/product?id="+e.id}):t.showToast({title:"未找到商品",icon:"none"})},goBack:function(){t.navigateBack()},gotoItem:function(e){t.navigateTo({url:"/shopPackage/shop/product?id="+e})},scrollToElement:function(e){t.createSelectorQuery().select("#"+e).boundingClientRect((function(e){e&&t.pageScrollTo({scrollTop:e.top+t.getSystemInfoSync().screenHeight/2,duration:300})})).exec()},addBuy:function(t){this.product_id=t,this.buydialogShow=!this.buydialogShow},addcart:function(t){console.log(t),this.cartnum=this.cartnum+t.num},showLinkChange:function(){this.showLinkStatus=!this.showLinkStatus},buydialogChange:function(t){var e=this;e.buydialogShow||(e.btntype=t.currentTarget.dataset.btntype),e.buydialogShow=!e.buydialogShow,setTimeout((function(){e.getCart()}),1e3)},shareClick:function(){this.sharetypevisible=!0},handleClickMask:function(){this.sharetypevisible=!1},gotokfurl:function(){t.navigateTo({url:"../../pagesExt/kefu/index?bid="+this.opt.id})},openAll:function(){this.pagecontentm=this.pagecontent,this.is_show=!1},getdata:function(){var t=this;t.pagenum=1,t.loading=!0,c.get("Apidaihuoyiuan/detail",{pid:0,id:this.opt.id},(function(e){console.log(e),t.detail=e.detail,t.rich_content=e.detail.rich_content,t.pics=e.detail.pic2.split(","),t.products=e.products,t.product_id=t.products[0].id,t.kfurl="/pagesExt/kefu/index?bid="+e.detail.bid,t.tuanzhang=e.tuanzhang,t.content=e.pagecontent[0].content,t.pagecontent=e.pagecontent;var n=[];e.pagecontent.filter((function(t,e){e<2&&n.push(t)})),t.pagecontentm=n,e.pinglunlist.filter((function(t){t.pics=t.content_pic.split(",")})),t.pinglunlist=e.pinglunlist,t.bijis=e.bijis,t.totalCommission=e.totalCommission}))},getCart:function(){var t=this;c.get("ApiShop/cart",{},(function(e){console.log(e.cartlist[0].prolist.length),e.cartlist.length>0&&(t.cart_num=e.cartlist[0].prolist.length)}))},showPoster:function(){var t=this;t.showposter=!0,t.sharetypevisible=!1,c.showLoading("努力生成中"),c.post("ApiTuanzhang/createposter",{pid:t.tid},(function(e){c.showLoading(!1),0==e.status?c.alert(e.msg):t.posterpic=e.poster}))},baocun:function(){t.showLoading(),t.saveImageToPhotosAlbum({filePath:this.posterpic,success:function(){t.hideLoading(),t.showToast({title:"保存成功",icon:"success",duration:2e3})},fail:function(e){t.hideLoading(),"saveImageToPhotosAlbum:fail auth deny"===e.errMsg&&t.openSetting({success:function(e){e.authSetting["scope.writePhotosAlbum"]?t.showToast({title:"您已授权成功，请重新保存海报",icon:"success",duration:2e3}):t.showToast({title:"尚未授权，无法保存海报",icon:"none",duration:2e3})}})}})},posterDialogClose:function(){this.showposter=!1},sharemp:function(){c.error("点击右上角发送给好友或分享到朋友圈"),this.sharetypevisible=!1},shareapp:function(){var e=this;t.showActionSheet({itemList:["发送给微信好友","分享到微信朋友圈"],success:function(n){if(n.tapIndex>=0){var i="WXSceneSession";1==n.tapIndex&&(i="WXSenceTimeline");var o={provider:"weixin",type:0};o.scene=i,o.title=e.product.name,o.href=c.globalData.pre_url+"/h5/"+c.globalData.aid+".html#/activity/luckycollage/product?scene=id_"+e.product.id+"-pid_"+c.globalData.mid,o.imageUrl=e.product.pic;var a=c.globalData.initdata.sharelist;if(a)for(var s=0;s<a.length;s++)if("/activity/luckycollage/product"==a[s]["indexurl"]&&(o.title=a[s].title,o.summary=a[s].desc,o.imageUrl=a[s].pic,a[s].url)){var u=a[s].url;0===u.indexOf("/")&&(u=c.globalData.pre_url+"/h5/"+c.globalData.aid+".html#"+u),c.globalData.mid>0&&(u+=(-1===u.indexOf("?")?"?":"&")+"pid="+c.globalData.mid),o.href=u}t.share(o)}}})}}};e.default=u}).call(this,n("df3c")["default"])}},[["fc25","common/runtime","common/vendor"]]]);