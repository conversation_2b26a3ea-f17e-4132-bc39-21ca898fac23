<view class="container" style="{{'background-color:'+('#f7f8fa')+';'}}"><block wx:if="{{isload}}"><block><view><view class="status-tabs" style="{{'background-color:'+('#fff')+';'+('box-shadow:'+('0 2rpx 10rpx rgba(0,0,0,0.05)')+';')}}"><view data-event-opts="{{[['tap',[['switchStatusType',['waiting']]]]]}}" class="{{['tab-item',(statusType==='waiting')?'active':'']}}" style="{{(statusType==='waiting'?{color:$root.m0,borderBottomColor:$root.m1}:null)}}" bindtap="__e">待服务</view><view data-event-opts="{{[['tap',[['switchStatusType',['serving']]]]]}}" class="{{['tab-item',(statusType==='serving')?'active':'']}}" style="{{(statusType==='serving'?{color:$root.m2,borderBottomColor:$root.m3}:null)}}" bindtap="__e">服务中</view><view data-event-opts="{{[['tap',[['switchStatusType',['completed']]]]]}}" class="{{['tab-item',(statusType==='completed')?'active':'']}}" style="{{(statusType==='completed'?{color:$root.m4,borderBottomColor:$root.m5}:null)}}" bindtap="__e">已完成</view><view data-event-opts="{{[['tap',[['switchStatusType',['transferred']]]]]}}" class="{{['tab-item',(statusType==='transferred')?'active':'']}}" style="{{(statusType==='transferred'?{color:$root.m6,borderBottomColor:$root.m7}:null)}}" bindtap="__e">被转派</view><view data-event-opts="{{[['tap',[['switchStatusType',['all']]]]]}}" class="{{['tab-item',(statusType==='all')?'active':'']}}" style="{{(statusType==='all'?{color:$root.m8,borderBottomColor:$root.m9}:null)}}" bindtap="__e">全部</view></view><view class="search-container" style="{{'box-shadow:'+('0 2rpx 10rpx rgba(0,0,0,0.05)')+';'}}"><view class="search-box" style="{{'background-color:'+('#f0f2f5')+';'}}"><image class="img" src="/static/img/search_ico.png"></image><input class="search-text" placeholder="搜索商家" placeholder-style="color:#aaa;font-size:24rpx" data-event-opts="{{[['confirm',[['searchConfirm',['$event']]]]]}}" bindconfirm="__e"/></view></view><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="id"><block><view class="order-box" style="{{'box-shadow:'+('0 2rpx 12rpx rgba(0,0,0,0.03)')+';'}}" data-url="{{'jdorderdetail?id='+item.$orig.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="head"><view class="status-container"><block wx:if="{{item.$orig.fwtype==1}}"><view><block wx:if="{{item.$orig.status==3}}"><view class="f1"><image class="img" src="/static/peisong/ps_time.png"></image><text style="{{'color:'+(item.m10)+';'}}">已完成</text></view></block><block wx:if="{{item.$orig.status==1}}"><view class="f1"><image class="img" src="/static/peisong/ps_time.png"></image><text class="t1" style="{{'color:'+(item.m11)+';'}}">等待客户上门</text></view></block><block wx:if="{{item.$orig.status==2}}"><view class="f1"><image class="img" src="/static/peisong/ps_time.png"></image><text class="t1" style="{{'color:'+(item.m12)+';'}}">服务中</text></view></block><block wx:if="{{item.$orig.status==-1}}"><view class="f1"><image class="img" src="/static/peisong/ps_time.png"></image><text style="{{'color:'+('#ff4d4f')+';'}}">已取消</text></view></block><block wx:if="{{item.$orig.status==-2}}"><view class="f1"><image class="img" src="/static/peisong/ps_time.png"></image><text style="{{'color:'+('#f57c00')+';'}}">已改派</text></view></block></view></block><block wx:else><block wx:if="{{item.$orig.fwtype==2}}"><view><block wx:if="{{item.$orig.status==3}}"><view class="f1"><image class="img" src="/static/peisong/ps_time.png"></image><text style="{{'color:'+(item.m13)+';'}}">已完成</text></view></block><block wx:else><block wx:if="{{item.$orig.status==1}}"><view class="f1"><image class="img" src="/static/peisong/ps_time.png"></image>期望上门时间<text class="t1" style="{{'color:'+(item.m14)+';'}}">{{item.$orig.orderinfo.yydate}}</text></view></block><block wx:else><block wx:if="{{item.$orig.status==2}}"><block><block wx:if="{{showaddmoney}}"><view class="f1"><block wx:if="{{!item.$orig.sign_status}}"><block><image class="img" src="/static/peisong/ps_time.png"></image><text style="{{'color:'+(item.m15)+';'}}">已到达，等待服务</text></block></block><block wx:else><block><image class="img" src="/static/peisong/ps_time.png"></image><text style="{{'color:'+(item.m16)+';'}}">已到达，正在服务</text></block></block></view></block><block wx:else><view class="f1"><image class="img" src="/static/peisong/ps_time.png"></image><text style="{{'color:'+(item.m17)+';'}}">已到达，服务中</text></view></block></block></block></block></block><block wx:if="{{item.$orig.status==-1}}"><view class="f1"><image class="img" src="/static/peisong/ps_time.png"></image><text style="{{'color:'+('#ff4d4f')+';'}}">已取消</text></view></block><block wx:if="{{item.$orig.status==-2}}"><view class="f1"><image class="img" src="/static/peisong/ps_time.png"></image><text style="{{'color:'+('#f57c00')+';'}}">已改派</text></view></block></view></block></block><view class="order-num">{{"订单号: "+item.$orig.ordernum}}</view></view><view class="flex1"></view><view class="f2" style="{{'color:'+('#ff7a45')+';'}}"><text class="t1">{{item.$orig.ticheng}}</text>元</view></view><view class="content"><view class="f1"><view class="t1"><text class="x1" style="{{'color:'+('#ff7a45')+';'}}">{{item.$orig.juli}}</text><text class="x2">{{item.$orig.juli_unit}}</text></view><view class="t2"><image class="img" src="/static/peisong/ps_juli.png"></image></view><view class="t3"><text class="x1" style="{{'color:'+('#ff7a45')+';'}}">{{item.$orig.juli2}}</text><text class="x2">{{item.$orig.juli2_unit}}</text></view></view><view class="f2"><view class="t1">{{item.$orig.binfo.name}}</view><view class="t2">{{item.$orig.binfo.address}}</view><view class="t3">{{item.$orig.orderinfo.address}}</view><view class="t2">{{item.$orig.orderinfo.area}}</view></view><view class="f3" data-index="{{index}}" data-event-opts="{{[['tap',[['daohang',['$event']]]]]}}" catchtap="__e"><image class="img" src="{{pre_url+'/static/img/peisong/ps_daohang.png'}}"></image></view></view><block wx:if="{{item.$orig.fwtype==1}}"><view class="op"><block wx:if="{{item.$orig.status==1}}"><view class="t1" style="{{'color:'+(item.m18)+';'}}">已接单，待顾客上门</view></block><block wx:if="{{item.$orig.status==2}}"><view class="t1" style="{{'color:'+(item.m19)+';'}}">顾客已到达</view></block><block wx:if="{{item.$orig.status==3}}"><view class="t1" style="{{'color:'+(item.m20)+';'}}">已完成</view></block><view class="flex1"></view><block wx:if="{{item.$orig.status==1}}"><view class="btn1" style="{{('background:linear-gradient(90deg,'+item.m21+' 0%,rgba('+item.m22+',0.8) 100%)')}}" data-id="{{item.$orig.id}}" data-st="2" data-event-opts="{{[['tap',[['setst',['$event']]]]]}}" catchtap="__e">顾客已到达</view></block><block wx:if="{{item.$orig.status==2}}"><view class="btn1" style="{{('background:linear-gradient(90deg,'+item.m23+' 0%,rgba('+item.m24+',0.8) 100%)')}}" data-id="{{item.$orig.id}}" data-st="3" data-event-opts="{{[['tap',[['setst',['$event']]]]]}}" catchtap="__e">我已完成</view></block></view></block><block wx:else><block wx:if="{{item.$orig.fwtype==2}}"><view class="op"><block wx:if="{{item.$orig.status==1}}"><view class="t1" style="{{'color:'+(item.m25)+';'}}">已接单，等待上门</view></block><block wx:if="{{item.$orig.status==2}}"><view class="t1" style="{{'color:'+(item.m26)+';'}}">{{"已到达，共用时"+item.$orig.useminute+"分钟"}}</view></block><block wx:if="{{item.$orig.status==3}}"><view class="t1" style="{{'color:'+(item.m27)+';'}}">已完成</view></block><view class="flex1"></view><block wx:if="{{showaddmoney}}"><block><block wx:if="{{item.$orig.sign_status==1&&item.$orig.status==2&&item.$orig.addprice<=0}}"><view class="btn1 btn2" style="{{'background-color:'+('rgba('+item.m28+',0.1)')+';'+('color:'+(item.m29)+';')+('border-color:'+(item.m30)+';')}}" data-id="{{item.$orig.id}}" data-event-opts="{{[['tap',[['addmoney',['$event']]]]]}}" catchtap="__e">补差价</view></block><block wx:if="{{item.$orig.status==1}}"><view class="btn1" style="{{('background:linear-gradient(90deg,'+item.m31+' 0%,rgba('+item.m32+',0.8) 100%)')}}" data-id="{{item.$orig.id}}" data-st="2" data-event-opts="{{[['tap',[['setst',['$event']]]]]}}" catchtap="__e">出发</view></block><block wx:if="{{item.$orig.addprice>0}}"><view class="btn1 btn2" style="{{'background-color:'+('rgba('+item.m33+',0.1)')+';'+('color:'+(item.m34)+';')+('border-color:'+(item.m35)+';')}}" data-id="{{item.$orig.id}}" data-key="{{index}}" data-event-opts="{{[['tap',[['showpaycode',['$event']]]]]}}" catchtap="__e">查看补余款</view></block><block wx:if="{{!item.$orig.sign_status&&item.$orig.status==2}}"><view class="btn1" style="{{('background:linear-gradient(90deg,'+item.m36+' 0%,rgba('+item.m37+',0.8) 100%)')}}" data-id="{{item.$orig.id}}" data-st="5" data-event-opts="{{[['tap',[['setst',['$event']]]]]}}" catchtap="__e">开始服务</view></block><block wx:if="{{item.$orig.sign_status==1&&item.$orig.status==2}}"><view class="btn1" style="{{('background:linear-gradient(90deg,'+item.m38+' 0%,rgba('+item.m39+',0.8) 100%)')}}" data-id="{{item.$orig.id}}" data-st="3" data-event-opts="{{[['tap',[['setst',['$event']]]]]}}" catchtap="__e">服务完成</view></block></block></block><block wx:else><block><block wx:if="{{item.$orig.status==1}}"><view class="btn1" style="{{('background:linear-gradient(90deg,'+item.m40+' 0%,rgba('+item.m41+',0.8) 100%)')}}" data-id="{{item.$orig.id}}" data-st="2" data-event-opts="{{[['tap',[['setst',['$event']]]]]}}" catchtap="__e">我已到达</view></block><block wx:if="{{item.$orig.status==2}}"><view class="btn1" style="{{('background:linear-gradient(90deg,'+item.m42+' 0%,rgba('+item.m43+',0.8) 100%)')}}" data-id="{{item.$orig.id}}" data-st="3" data-event-opts="{{[['tap',[['setst',['$event']]]]]}}" catchtap="__e">我已完成</view></block></block></block></view></block></block></view></block></block><block wx:if="{{nomore}}"><nomore vue-id="258944d4-1" bind:__l="__l"></nomore></block><block wx:if="{{nodata}}"><nodata vue-id="258944d4-2" bind:__l="__l"></nodata></block></view><block wx:if="{{showtabbar}}"><view class="tabbar"><view class="tabbar-bot"></view><view class="tabbar-bar" style="background-color:#ffffff;"><view class="tabbar-item" data-url="dating" data-opentype="reLaunch" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="tabbar-image-box"><image class="tabbar-icon" src="{{pre_url+'/static/img/peisong/home.png'}}"></image></view><view class="tabbar-text">大厅</view></view><view class="tabbar-item" data-url="jdorderlist" data-opentype="reLaunch" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="tabbar-image-box"><image class="tabbar-icon" src="{{pre_url+'/static/img/peisong/order'+(st!=3?'2':'')+'.png'}}"></image></view><view class="{{['tabbar-text',st!=3?'active':'']}}" style="{{(st!=3?{color:$root.m44}:null)}}">订单</view></view><view class="tabbar-item" data-url="jdorderlist?st=3" data-opentype="reLaunch" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="tabbar-image-box"><image class="tabbar-icon" src="{{pre_url+'/static/img/peisong/orderwc'+(st==3?'2':'')+'.png'}}"></image></view><view class="{{['tabbar-text',st==3?'active':'']}}" style="{{(st==3?{color:$root.m45}:null)}}">已完成</view></view><block wx:if="{{showform}}"><view class="tabbar-item" data-url="formlog" data-opentype="reLaunch" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="tabbar-image-box"><image class="tabbar-icon" src="{{pre_url+'/static/img/peisong/dangan.png'}}"></image></view><view class="tabbar-text">档案</view></view></block><view class="tabbar-item" data-url="my" data-opentype="reLaunch" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="tabbar-image-box"><image class="tabbar-icon" src="{{pre_url+'/static/img/peisong/my.png'}}"></image></view><view class="tabbar-text">我的</view></view></view></view></block><block wx:if="{{showmodal}}"><view class="modal"><view class="addmoney" style="{{'border-radius:'+('16rpx')+';'+('box-shadow:'+('0 8rpx 20rpx rgba(0,0,0,0.1)')+';')}}"><view class="title" style="{{'border-bottom:'+('1rpx solid #f0f0f0')+';'}}">{{(addprice>0?'修改':'创建')+"补余款"}}</view><view class="item"><label class="label">金额：</label><input type="text" name="blance_price" placeholder="请输入补余款金额" placeholder-style="font-size:24rpx" data-event-opts="{{[['input',[['bindMoney',['$event']]]]]}}" value="{{addprice}}" bindinput="__e"/>元</view><view class="btn"><button data-event-opts="{{[['tap',[['cancel',['$event']]]]]}}" class="btn-cancel" style="{{'border-radius:'+('50rpx')+';'}}" bindtap="__e">取消</button><button data-event-opts="{{[['tap',[['addconfirm',['$event']]]]]}}" class="confirm" style="{{('background:linear-gradient(90deg,'+$root.m46+' 0%,rgba('+$root.m47+',0.8) 100%);border-radius:50rpx')}}" catchtap="__e">确定</button></view></view></view></block><block wx:if="{{showpaycodes}}"><view class="modal"><view class="addmoney" style="{{'border-radius:'+('16rpx')+';'+('box-shadow:'+('0 8rpx 20rpx rgba(0,0,0,0.1)')+';')}}"><view class="title" style="{{'border-bottom:'+('1rpx solid #f0f0f0')+';'}}">查看补余款</view><view class="item"><label>金额：</label><text class="price">{{addprice}}</text>元</view><view class="item" style="padding-top:0;"><label>支付状态：</label><block wx:if="{{addmoneystatus==1}}"><text class="t2" style="{{'color:'+($root.m48)+';'}}">已支付</text></block><block wx:if="{{addmoneystatus==0}}"><text class="t2" style="color:#ff4d4f;">待支付</text></block></view><view class="qrcode"><image src="{{paycode}}"></image></view><view class="btn"><button data-event-opts="{{[['tap',[['cancel',['$event']]]]]}}" class="btn-cancel" style="{{'border-radius:'+('50rpx')+';'}}" bindtap="__e">关闭</button><block wx:if="{{addmoneystatus==0}}"><button class="btn-update" style="{{('background:linear-gradient(90deg,'+$root.m49+' 0%,rgba('+$root.m50+',0.8) 100%);border-radius:50rpx')}}" data-key="{{index}}" data-id="{{id}}" data-event-opts="{{[['tap',[['update',['$event']]]]]}}" bindtap="__e">修改差价</button></block></view></view></view></block><block wx:if="{{showPunchModal}}"><view class="punch-modal"><view class="punch-content" style="{{'border-radius:'+('16rpx')+';'+('box-shadow:'+('0 10rpx 30rpx rgba(0,0,0,0.15)')+';')}}"><view class="punch-header"><text class="punch-title" style="{{'color:'+($root.m51)+';'}}">{{punchTitle}}</text><view data-event-opts="{{[['tap',[['closePunchModal',['$event']]]]]}}" class="close-btn" bindtap="__e">×</view></view><view class="location-section" style="{{'background-color:'+('#f8f8fa')+';'}}"><view class="section-title"><text class="icon">📍</text>位置信息</view><block wx:if="{{punchLocationInfo}}"><view class="location-content"><view class="location-status success" style="{{'border-left-color:'+($root.m52)+';'}}"><text class="status-text" style="{{'color:'+($root.m53)+';'}}">已获取位置信息</text><text class="location-detail">{{"经度: "+punchLocationInfo.longitude}}</text><text class="location-detail">{{"纬度: "+punchLocationInfo.latitude}}</text></view></view></block><block wx:else><view class="location-content"><view class="{{['location-status',(isLocating)?'loading':'']}}"><text class="status-text">{{isLocating?'获取位置中...':'点击获取位置'}}</text></view><block wx:if="{{!isLocating&&!punchLocationInfo}}"><view data-event-opts="{{[['tap',[['getPunchLocation',['$event']]]]]}}" class="get-location-btn" style="{{('background:linear-gradient(90deg,'+$root.m54+' 0%,rgba('+$root.m55+',0.8) 100%)')}}" bindtap="__e">获取位置信息</view></block></view></block></view><view class="photo-section" style="{{'background-color:'+('#f8f8fa')+';'}}"><view class="section-title"><text class="icon">📷</text>{{''+punchPhotoType+" ("+$root.g0+"/9)"}}</view><view class="photo-content"><block wx:if="{{$root.g1>0}}"><view class="photo-list"><block wx:for="{{punchPhotos}}" wx:for-item="photo" wx:for-index="index" wx:key="index"><view class="photo-item"><image class="preview-image" src="{{photo}}" mode="aspectFill" data-url="{{photo}}" data-event-opts="{{[['tap',[['previewPunchPhoto',['$event']]]]]}}" bindtap="__e"></image><view class="remove-icon" data-index="{{index}}" data-event-opts="{{[['tap',[['removePunchPhoto',['$event']]]]]}}" bindtap="__e">×</view></view></block></view></block><block wx:if="{{$root.g2<9}}"><view data-event-opts="{{[['tap',[['selectPunchPhoto',['$event']]]]]}}" class="photo-placeholder" bindtap="__e"><text class="placeholder-icon">+</text><text class="placeholder-text">点击上传照片</text></view></block></view></view><view class="punch-actions"><view data-event-opts="{{[['tap',[['closePunchModal',['$event']]]]]}}" class="cancel-btn" style="{{'border-radius:'+('50rpx')+';'}}" bindtap="__e">取消</view><view data-event-opts="{{[['tap',[['submitPunchData',['$event']]]]]}}" class="{{['submit-btn',(!canSubmitPunch)?'disabled':'']}}" style="{{(canSubmitPunch?'background:linear-gradient(90deg,'+$root.m56+' 0%,rgba('+$root.m57+',0.8) 100%);border-radius:50rpx':'border-radius:50rpx')}}" bindtap="__e">确认提交</view></view></view></view></block></block></block><block wx:if="{{loading}}"><loading vue-id="258944d4-3" bind:__l="__l"></loading></block><dp-tabbar vue-id="258944d4-4" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="258944d4-5" data-ref="popmsg" bind:__l="__l"></popmsg><view style="display:none;">{{timestamp}}</view></view>