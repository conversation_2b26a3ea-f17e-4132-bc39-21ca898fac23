(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["yuyue/packagebuy"],{"0a3d":function(o,e,n){},"115b":function(o,e,n){"use strict";(function(o){Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n=getApp(),a={components:{},data:function(){return{packageId:null,orderData:{},packageInfo:{},businessInfo:{},couponList:[],couponCount:0,linkman:"",tel:"",remark:"",selectedCoupon:null,loading:!0,submitting:!1}},computed:{coupon_money:function(){return this.selectedCoupon?parseFloat(this.selectedCoupon.money||0):0},leveldk_money:function(){return this.orderData.userinfo?parseFloat(this.orderData.userinfo.leveldk_money||0):0},package_total_price:function(){return parseFloat(this.packageInfo.product_price||this.packageInfo.sell_price||0)},actual_price:function(){var o=this.package_total_price-this.leveldk_money-this.coupon_money;return o=o<0?0:o,o.toFixed(2)}},onLoad:function(e){e.package_id?(this.packageId=e.package_id,this.getBuyData()):(n.error("缺少套餐ID",(function(){n.goback()})),this.loading=!1),o.setNavigationBarTitle({title:"确认订单"})},methods:{getBuyData:function(){var o=this;o.loading=!0,n.post("ApiYuyuePackage/buy",{package_id:o.packageId},(function(e){if(console.log("[getBuyData] ApiYuyuePackage/buy 返回: ",JSON.stringify(e)),o.loading=!1,1==e.status&&e.allbuydata&&Array.isArray(e.allbuydata)&&e.allbuydata.length>0){o.orderData=e,o.linkman=e.linkman||"",o.tel=e.tel||"";var a=e.allbuydata[0];if(console.log("[getBuyData] 处理 buyItem: ",JSON.stringify(a)),o.couponList=a.couponList||[],o.couponCount=a.couponCount||0,o.orderData.userinfo||(o.orderData.userinfo={}),o.orderData.userinfo.leveldk_money=a.leveldk_money||0,console.log("[getBuyData] 更新 userinfo: ",JSON.stringify(o.orderData.userinfo)),a.prodata&&Array.isArray(a.prodata)&&a.prodata.length>0){var t=a.prodata[0];if(console.log("[getBuyData] 处理 productInfo: ",JSON.stringify(t)),t.package){var i=t.package;console.log("[getBuyData] 提取 packageFromServer: ",JSON.stringify(i)),o.packageInfo={id:i.id,name:i.name,pic:i.pic,sell_price:void 0!==a.sell_price?a.sell_price:i.sell_price,product_price:void 0!==a.product_price?a.product_price:i.product_price||i.sell_price},o.packageInfo.pic&&o.packageInfo.pic.startsWith("https://localhost")&&(console.log("[getBuyData] 清理套餐图片前缀"),o.packageInfo.pic=o.packageInfo.pic.substring("https://localhost".length)),console.log("[getBuyData] 构建的 packageInfo: ",JSON.stringify(o.packageInfo))}else console.error("[getBuyData] buyItem.prodata[0] 中缺少 package 对象"),n.error("加载购买信息失败[pke]")}else console.error("[getBuyData] buyItem.prodata 结构错误或为空"),n.error("加载购买信息失败[pdi]");o.businessInfo={id:a.bid},console.log("[getBuyData] 存储 businessInfo: ",JSON.stringify(o.businessInfo))}else console.error("[getBuyData] 接口状态错误或 allbuydata 结构不符: ",e),n.error(e.msg||"加载订单信息失败",(function(){}))}),(function(e){o.loading=!1,console.error("[getBuyData] ApiYuyuePackage/buy 请求失败: ",e),n.error("请求失败",(function(){}))}))},chooseCoupon:function(){this.couponList.length>0?this.$refs.couponPopup.open():n.toast("暂无可用优惠券")},closeCouponPopup:function(){this.$refs.couponPopup.close()},selectCoupon:function(o){this.selectedCoupon=o,this.closeCouponPopup()},submitOrder:function(){var o=this;if(o.linkman)if(o.tel&&/^1[3-9]\d{9}$/.test(o.tel)){if(!o.submitting){o.submitting=!0,n.showLoading("提交中..."),console.log("[submitOrder] 当前 packageInfo:",JSON.stringify(o.packageInfo));var e=[];if(console.log("[submitOrder] 检查条件: businessInfo.id=",o.businessInfo.id,"packageId=",o.packageId),"number"!==typeof o.businessInfo.id||!o.packageId)return n.showLoading(!1),o.submitting=!1,void n.error("订单信息不完整");e.push({bid:o.businessInfo.id,prodatastr:o.packageId,couponrid:o.selectedCoupon?o.selectedCoupon.couponrid:0,num:1,product_price:o.packageInfo.product_price});var a={buydata:e,linkman:o.linkman,tel:o.tel,remark:o.remark};console.log("[submitOrder] 最终提交参数 params:",JSON.stringify(a)),n.post("ApiYuyuePackage/createOrder",a,(function(e){if(n.showLoading(!1),o.submitting=!1,1==e.status){e.order_id;o.actual_price>0&&e.payorderid?(console.log("[submitOrder] 跳转支付页面, payorderid=",e.payorderid),o.submitting=!0,n.goto("/pages/pay/pay?id="+e.payorderid)):n.success("下单成功",(function(){n.goto("/pages/my/packageorderlist")}))}else n.error(e.msg||"创建订单失败")}),(function(){n.showLoading(!1),o.submitting=!1,n.error("请求失败")}))}}else n.error("请填写正确的手机号");else n.error("请填写联系人")}}};e.default=a}).call(this,n("df3c")["default"])},"410c":function(o,e,n){"use strict";(function(o,e){var a=n("47a9");n("06e9");a(n("3240"));var t=a(n("72aa"));o.__webpack_require_UNI_MP_PLUGIN__=n,e(t.default)}).call(this,n("3223")["default"],n("df3c")["createPage"])},5094:function(o,e,n){"use strict";var a=n("0a3d"),t=n.n(a);t.a},"72aa":function(o,e,n){"use strict";n.r(e);var a=n("e636"),t=n("92801");for(var i in t)["default"].indexOf(i)<0&&function(o){n.d(e,o,(function(){return t[o]}))}(i);n("5094");var r=n("828b"),u=Object(r["a"])(t["default"],a["b"],a["c"],!1,null,null,null,!1,a["a"],void 0);e["default"]=u.exports},92801:function(o,e,n){"use strict";n.r(e);var a=n("115b"),t=n.n(a);for(var i in a)["default"].indexOf(i)<0&&function(o){n.d(e,o,(function(){return a[o]}))}(i);e["default"]=t.a},e636:function(o,e,n){"use strict";n.d(e,"b",(function(){return t})),n.d(e,"c",(function(){return i})),n.d(e,"a",(function(){return a}));var a={uniPopup:function(){return Promise.all([n.e("common/vendor"),n.e("components/uni-popup/uni-popup")]).then(n.bind(null,"ca44a"))}},t=function(){var o=this,e=o.$createElement,n=(o._self._c,!o.loading&&o.packageInfo.id?o.t("color1"):null),a=!o.loading&&o.packageInfo.id?o.couponList.length:null,t=!o.loading&&o.packageInfo.id&&o.orderData.userinfo&&o.orderData.userinfo.leveldk_money>0?o.t("color1"):null,i=!o.loading&&o.packageInfo.id&&o.coupon_money>0?o.t("color1"):null,r=!o.loading&&o.packageInfo.id?o.t("color1"):null,u=!o.loading&&o.packageInfo.id?o.t("color1"):null,c=!o.loading&&o.packageInfo.id?o.t("color1"):null,l=!o.loading&&o.packageInfo.id?o.__map(o.couponList,(function(e,n){var a=o.__get_orig(e),t=o.selectedCoupon&&o.selectedCoupon.couponrid===e.couponrid?o.t("color1"):null,i=o.selectedCoupon&&o.selectedCoupon.couponrid===e.couponrid?o.t("color1"):null;return{$orig:a,m6:t,m7:i}})):null,s=o.loading||!o.packageInfo.id||o.selectedCoupon?null:o.t("color1"),p=o.loading||!o.packageInfo.id||o.selectedCoupon?null:o.t("color1");o.$mp.data=Object.assign({},{$root:{m0:n,g0:a,m1:t,m2:i,m3:r,m4:u,m5:c,l0:l,m8:s,m9:p}})},i=[]}},[["410c","common/runtime","common/vendor"]]]);