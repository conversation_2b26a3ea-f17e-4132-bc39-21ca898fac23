<view class="container"><block wx:if="{{isload}}"><block><form data-event-opts="{{[['submit',[['formSubmit',['$event']]]],['reset',[['formReset',['$event']]]]]}}" bindsubmit="__e" bindreset="__e"><view class="form"><view class="form-item"><input class="input" type="text" placeholder="请输入您的职业" placeholder-style="color:#BBBBBB;font-size:28rpx" name="profession" data-event-opts="{{[['input',[['__set_model',['','profession','$event',[]]]]]]}}" value="{{profession}}" bindinput="__e"/></view></view><button class="set-btn" style="{{'background:'+('linear-gradient(90deg,'+$root.m0+' 0%,rgba('+$root.m1+',0.8) 100%)')+';'}}" form-type="submit">保 存</button></form></block></block><block wx:if="{{loading}}"><loading vue-id="5cb15b64-1" bind:__l="__l"></loading></block><dp-tabbar vue-id="5cb15b64-2" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="5cb15b64-3" data-ref="popmsg" bind:__l="__l"></popmsg></view>