<view style="width:100%;"><view class="dp-product-itemlist"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="id"><view class="item" style="{{'background-color:'+(probgcolor)+';'}}" data-url="{{'/pagesExa/tuanzhang/product?id='+item.$orig[idfield]+'&bid='+item.m0}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="product-pic"><image class="image" src="{{item.$orig.pic}}" mode="widthFix"></image><block wx:if="{{saleimg!=''}}"><image class="saleimg" src="{{saleimg}}" mode="widthFix"></image></block></view><view class="product-info"><block wx:if="{{showname==1}}"><view class="p1">{{item.$orig.name}}</view></block><block wx:if="{{showprice!='0'&&(item.$orig.price_type!=1||item.$orig.sell_price>0)}}"><view class="p2"><text class="t1" style="{{'color:'+(item.m1)+';'}}"><text style="font-size:24rpx;padding-right:1px;">￥</text>{{item.$orig.sell_price}}</text><block wx:if="{{showprice=='1'&&item.$orig.market_price*1>item.$orig.sell_price*1}}"><text class="t2">{{"￥"+item.$orig.market_price}}</text></block><block wx:if="{{item.$orig.juli}}"><text class="t3">{{item.$orig.juli}}</text></block></view></block><block wx:if="{{item.$orig.xunjia_text&&item.$orig.price_type==1&&item.$orig.sell_price<=0}}"><view class="p2" style="height:50rpx;line-height:44rpx;"><text class="t1" style="{{'color:'+(item.m2)+';'+('font-size:'+('30rpx')+';')}}">询价</text><block wx:if="{{item.$orig.xunjia_text&&item.$orig.price_type==1}}"><block><view class="lianxi" style="{{'background:'+(item.m3)+';'}}" data-lx_name="{{item.$orig.lx_name}}" data-lx_bid="{{item.$orig.lx_bid}}" data-lx_bname="{{item.$orig.lx_bname}}" data-lx_tel="{{item.$orig.lx_tel}}" data-btntype="2" data-event-opts="{{[['tap',[['showLinkChange',['$event']]]]]}}" catchtap="__e">{{item.$orig.xunjia_text?item.$orig.xunjia_text:'联系TA'}}</view></block></block></view></block><block wx:if="{{item.$orig.merchant_name}}"><view class="p1" style="color:#666;font-size:24rpx;white-space:nowrap;text-overflow:ellipsis;margin-top:6rpx;height:30rpx;line-height:30rpx;font-weight:normal;"><text>{{item.$orig.merchant_name}}</text></view></block><block wx:if="{{item.$orig.main_business}}"><view class="p1" style="color:#666;font-size:24rpx;margin-top:4rpx;font-weight:normal;"><text>{{item.$orig.main_business}}</text></view></block><view class="p3"><block wx:if="{{showsales=='1'&&item.$orig.sales>0}}"><view class="p3-1"><text style="overflow:hidden;">{{"已售"+item.$orig.sales+"件"}}</text></view></block></view><block wx:if="{{(showsales!='1'||item.$orig.sales<=0)&&item.$orig.main_business}}"><view style="height:44rpx;"></view></block><block wx:if="{{showcart==1&&!item.$orig.price_type}}"><view class="p4" style="{{'background:'+('rgba('+item.m4+',0.1)')+';'+('color:'+(item.m5)+';')}}" data-proid="{{item.$orig[idfield]}}" data-bid="{{item.$orig.bid}}" data-event-opts="{{[['tap',[['buydialogChange',['$event']]]]]}}" catchtap="__e"><text class="iconfont icon_gouwuche"></text></view></block><block wx:if="{{showcart==2&&!item.$orig.price_type}}"><view class="p4" style="{{'background:'+('rgba('+item.m6+',0.1)')+';'+('color:'+(item.m7)+';')}}" data-proid="{{item.$orig[idfield]}}" data-event-opts="{{[['tap',[['buydialogChange',['$event']]]]]}}" catchtap="__e"><image class="img" src="{{cartimg}}"></image></view></block></view></view></block></view><block wx:if="{{buydialogShow}}"><buy-dialog vue-id="5a303d86-1" proid="{{proid}}" bid="{{currentBid}}" menuindex="{{menuindex}}" data-event-opts="{{[['^buydialogChange',[['buydialogChange']]]]}}" bind:buydialogChange="__e" bind:__l="__l"></buy-dialog></block><block wx:if="{{showLinkStatus}}"><view class="posterDialog linkDialog"><view class="main"><view data-event-opts="{{[['tap',[['showLinkChange',['$event']]]]]}}" class="close" bindtap="__e"><image class="img" src="/static/img/close.png"></image></view><view class="content"><view class="title">{{lx_name}}</view><block wx:if="{{lx_bid>0}}"><view class="row"><view class="f1">店铺名称</view><view class="f2" data-url="{{'/pagesExt/business/index?id='+lx_bid}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">{{lx_bname}}<image class="image" src="/static/img/arrowright.png"></image></view></view></block><block wx:if="{{lx_tel}}"><view class="row"><view class="f1">联系电话</view><view class="f2" style="{{'color:'+($root.m8)+';'}}" data-url="{{'tel::'+lx_tel}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">{{lx_tel}}<image class="copyicon" src="/static/img/copy.png" data-text="{{lx_tel}}" data-event-opts="{{[['tap',[['copy',['$event']]]]]}}" catchtap="__e"></image></view></view></block></view></view></view></block></view>