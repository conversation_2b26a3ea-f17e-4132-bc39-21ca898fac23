<view class="container"><block wx:if="{{isload}}"><block><view class="search-container"><view class="topsearch flex-y-center"><view class="f1 flex-y-center"><image class="img" src="/static/img/search_ico.png"></image><input placeholder="搜索你感兴趣的商家" placeholder-style="font-size:24rpx;color:#C2C2C2" confirm-type="search" data-event-opts="{{[['confirm',[['search',['$event']]]]]}}" value="{{keyword}}" bindconfirm="__e"/></view></view><view class="search-navbar"><view class="search-navbar-item" style="{{(field=='juli'?'color:'+$root.m0:'')}}" data-field="juli" data-order="asc" data-event-opts="{{[['tap',[['sortClick',['$event']]]]]}}" catchtap="__e">距离最近</view><view class="search-navbar-item" style="{{(field=='comment_score'?'color:'+$root.m1:'')}}" data-field="comment_score" data-order="desc" data-event-opts="{{[['tap',[['sortClick',['$event']]]]]}}" catchtap="__e">评分排序</view><view class="search-navbar-item" data-field="sales" data-order="{{order=='asc'?'desc':'asc'}}" data-event-opts="{{[['tap',[['sortClick',['$event']]]]]}}" catchtap="__e"><text style="{{(field=='sales'?'color:'+$root.m2:'')}}">销量排序</text><text class="iconfont iconshangla" style="{{(field=='sales'&&order=='asc'?'color:'+$root.m3:'')}}"></text><text class="iconfont icondaoxu" style="{{(field=='sales'&&order=='desc'?'color:'+$root.m4:'')}}"></text></view><view class="search-navbar-item" data-field="sales_price" data-order="{{order=='asc'?'desc':'asc'}}" data-event-opts="{{[['tap',[['sortClick',['$event']]]]]}}" catchtap="__e"><text style="{{(field=='sales_price'?'color:'+$root.m5:'')}}">销售额</text><text class="iconfont iconshangla" style="{{(field=='sales_price'&&order=='asc'?'color:'+$root.m6:'')}}"></text><text class="iconfont icondaoxu" style="{{(field=='sales_price'&&order=='desc'?'color:'+$root.m7:'')}}"></text></view><view data-event-opts="{{[['tap',[['showDrawer',['showRight']]]]]}}" class="search-navbar-item flex-x-center flex-y-center" catchtap="__e">筛选<text class="{{['iconfont iconshaixuan '+(showfilter?'active':'')]}}"></text></view></view></view><uni-drawer class="vue-ref" vue-id="0e6da2ff-1" mode="right" width="{{280}}" data-ref="showRight" data-event-opts="{{[['^change',[['change',['$event','showRight']]]]]}}" bind:change="__e" bind:__l="__l" vue-slots="{{['default']}}"><view class="filter-scroll-view"><view class="filter-title">筛选</view><scroll-view class="filter-scroll-view-box" scroll-y="true"><view class="search-filter"><view class="filter-content-title">商家分类</view><view class="search-filter-content"><view class="filter-item" style="{{(catchecid==oldcid?'color:'+$root.m8+';background:rgba('+$root.m9+',0.1)':'')}}" data-cid="{{oldcid}}" data-event-opts="{{[['tap',[['cateClick',['$event']]]]]}}" catchtap="__e">全部</view><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><view class="filter-item" style="{{(catchecid==item.$orig.id?'color:'+item.m10+';background:rgba('+item.m11+',0.1)':'')}}" data-cid="{{item.$orig.id}}" data-event-opts="{{[['tap',[['cateClick',['$event']]]]]}}" catchtap="__e">{{item.$orig.name}}</view></block></block></view></view></scroll-view><view class="search-filter-btn"><view data-event-opts="{{[['tap',[['filterReset',['$event']]]]]}}" class="btn" bindtap="__e">重置</view><view data-event-opts="{{[['tap',[['filterConfirm',['$event']]]]]}}" class="btn2" style="{{'background:'+($root.m12)+';'}}" bindtap="__e">确定</view></view></view></uni-drawer><view class="ind_business"><view class="ind_buslist" id="datalist"><block wx:for="{{$root.l2}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><view data-url="{{'/pagesExt/business/index?id='+item.$orig.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="ind_busbox flex1 flex-row"><view class="ind_buspic flex0"><image src="{{item.$orig.logo}}"></image></view><view class="flex1"><view class="bus_title">{{item.$orig.name}}</view><view class="bus_sales">{{field=='sales_price'?'销售额：￥'+item.$orig.sales_price:'销量：'+item.$orig.sales}}</view><view class="bus_score"><block wx:for="{{[0,1,2,3,4]}}" wx:for-item="item2" wx:for-index="index2"><image class="img" src="{{'/static/img/star'+(item.$orig.comment_score>item2?'2':'')+'.png'}}"></image></block><view class="txt">{{item.$orig.comment_score+"分"}}</view></view><block wx:if="{{item.$orig.address}}"><view class="bus_address" data-latitude="{{item.$orig.latitude}}" data-longitude="{{item.$orig.longitude}}" data-company="{{item.$orig.name}}" data-address="{{item.$orig.address}}" data-event-opts="{{[['tap',[['openLocation',['$event']]]]]}}" catchtap="__e"><image style="width:26rpx;height:26rpx;margin-right:10rpx;" src="/static/img/b_addr.png"></image><text class="x1">{{item.$orig.address}}</text><text class="x2">{{item.$orig.juli}}</text></view></block><block wx:if="{{item.$orig.tel}}"><view class="bus_address" data-phone="{{item.$orig.tel}}" data-event-opts="{{[['tap',[['phone',['$event']]]]]}}" catchtap="__e"><image style="width:26rpx;height:26rpx;margin-right:10rpx;" src="/static/img/b_tel.png"></image><text class="x1">{{"联系电话："+item.$orig.tel}}</text></view></block><block wx:if="{{showtype==0}}"><block><scroll-view style="width:510rpx;" scroll-x="true"><block wx:if="{{item.g0}}"><view class="prolist"><block wx:for="{{item.$orig.prolist}}" wx:for-item="item2" wx:for-index="index2"><view class="product" data-url="{{'/shopPackage/shop/product?id='+item2.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e"><image class="f1" src="{{item2.pic}}"></image><view class="f2">{{"￥"+item2.sell_price}}</view></view></block><block wx:if="{{item.g1}}"><block><block wx:for="{{item.$orig.restaurantProlist}}" wx:for-item="item3" wx:for-index="index3" wx:key="index3"><view class="product" data-url="{{'/restaurant/takeaway/product?id='+item3.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e"><image class="f1" src="{{item3.pic}}"></image><view class="f2">{{"￥"+item3.sell_price}}</view></view></block></block></block></view></block></scroll-view></block></block><block wx:if="{{showtype==1}}"><block><block wx:if="{{item.g2}}"><view class="prolist2"><block wx:for="{{item.l1}}" wx:for-item="item2" wx:for-index="index2"><view class="product" data-url="{{'/shopPackage/shop/product?id='+item2.$orig.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e"><image class="f1" src="{{item2.$orig.pic}}"></image><view class="f2">{{"￥"+item2.$orig.sell_price}}</view><block wx:if="{{item2.$orig.market_price}}"><view class="f3">{{"￥"+item2.$orig.market_price}}</view></block><view class="f4">{{"已售"+item2.$orig.sales+"件"}}</view><view class="f5" style="{{'background:'+('rgba('+item2.m13+',0.1)')+';'+('color:'+(item2.m14)+';')}}" data-proid="{{item2.$orig.id}}" data-event-opts="{{[['tap',[['buydialogChange',['$event']]]]]}}" catchtap="__e"><text class="iconfont icon_gouwuche"></text></view></view></block></view></block></block></block></view></view></view></block></block><block wx:if="{{nomore}}"><nomore vue-id="0e6da2ff-2" bind:__l="__l"></nomore></block><block wx:if="{{nodata}}"><nodata vue-id="0e6da2ff-3" bind:__l="__l"></nodata></block></view></view><block wx:if="{{buydialogShow}}"><buydialog vue-id="0e6da2ff-4" proid="{{proid}}" menuindex="{{menuindex}}" data-event-opts="{{[['^addcart',[['addcart']]],['^buydialogChange',[['buydialogChange']]]]}}" bind:addcart="__e" bind:buydialogChange="__e" bind:__l="__l"></buydialog></block></block></block><block wx:if="{{loading}}"><loading vue-id="0e6da2ff-5" bind:__l="__l"></loading></block><dp-tabbar vue-id="0e6da2ff-6" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="0e6da2ff-7" data-ref="popmsg" bind:__l="__l"></popmsg></view>