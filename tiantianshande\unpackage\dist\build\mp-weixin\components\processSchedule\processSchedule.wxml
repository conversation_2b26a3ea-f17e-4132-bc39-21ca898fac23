<view class="schedule"><block wx:for="{{$root.l0}}" wx:for-item="step" wx:for-index="index" wx:key="index"><view class="{{['schedule-list',[(step.$orig.statusCondition)?'select':'']]}}"><view class="line-box"><view class="check"><view class="check-box"></view></view><block wx:if="{{index<$root.g0-1}}"><view class="line"></view></block></view><view class="right-box"><view class="title info">{{step.$orig.title}}</view><view class="flex"><view style="width:100%;"><view class="info">状态：<block wx:if="{{step.$orig.isStatusHidden}}"><view>——</view></block><view class="{{['status-i',step.$orig.statusClass]}}">{{''+step.$orig.statusText+''}}<block wx:if="{{step.$orig.statusClass}}"><image class="icon" src="{{step.m0}}"></image></block></view></view><block wx:if="{{step.$orig.hasDownloadButton&&index!==2}}"><view class="info tip">{{data.original_filename+''}}</view></block><view class="info">{{"备注："+step.$orig.remark}}</view><view class="info">{{"时间："+step.$orig.timeStatus}}</view></view><block wx:if="{{step.$orig.hasDownloadButton&&index===1}}"><button data-event-opts="{{[['tap',[['downloadFile',['$event']]]]]}}" class="btn" bindtap="__e">下载供电方案</button></block><block wx:if="{{step.$orig.hasDownloadButton&&index===2}}"><button data-event-opts="{{[['tap',[['applyVoltage',['$event']]]]]}}" class="btn" bindtap="__e">申请供电</button></block></view></view></view></block></view>