<view class="container"><block wx:if="{{isload}}"><block><dd-tab vue-id="55e23715-1" itemdata="{{['全部','待处理','处理中 ','已完成','待支付']}}" itemst="{{['all','0','1','2','10']}}" st="{{st}}" isfixed="{{true}}" data-event-opts="{{[['^changetab',[['changetab']]]]}}" bind:changetab="__e" bind:__l="__l"></dd-tab><view style="width:100%;height:90rpx;"></view><view class="content" id="datalist"><block wx:for="{{datalist}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="item"><view class="f1" data-url="{{'formdetail?id='+item.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e"><view class="flex" style="justify-content:space-between;"><text class="t1">{{item.title}}</text><view class="f2"><block wx:if="{{item.status==0&&(!item.payorderid||item.paystatus==1)}}"><text class="t1" style="color:#88e;" data-url="{{'formdetail?id='+item.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">立即处理</text></block><block wx:if="{{item.status==0&&item.payorderid&&item.paystatus==0}}"><text class="t1" style="color:red;">待支付</text></block><block wx:if="{{item.status==1}}"><text class="t1" style="color:green;" data-url="{{'formdetail?id='+item.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">立即处理</text></block><block wx:if="{{item.status==2}}"><text class="t1" style="color:green;">已完成</text></block><block wx:if="{{item.status==-1}}"><text class="t1" style="color:red;">已驳回</text></block></view></view><view class="flex" style="justify-content:space-between;margin-top:20rpx;"><view data-url="{{'formdetail?id='+item.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e"><text class="t2">{{"提交时间："+item.createtime}}</text><block wx:if="{{item.paynum}}"><text class="t2" user-select="true" selectable="true">{{item.paynum}}</text></block></view><view class="jindu" data-id="{{item.id}}" data-status="{{item.status}}" data-event-opts="{{[['tap',[['jindu',['$event']]]]]}}" catchtap="__e">查看进度</view></view></view></view></block></view><block wx:if="{{ishowjindu}}"><view class="modal"><view class="modal_jindu"><view data-event-opts="{{[['tap',[['closejd',['$event']]]]]}}" class="close" bindtap="__e"><image src="../../static/img/close.png"></image></view><block wx:if="{{$root.g0>0}}"><block><block wx:for="{{$root.l1}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="item" style="display:flex;"><view class="f1"><image src="{{'/static/img/jindu'+(index==0?'2':'1')+'.png'}}"></image></view><view class="f2"><text class="t2">{{'时间：'+item.$orig.time}}</text><text class="t1">{{item.$orig.desc+"("+item.$orig.remark+')'}}</text><block wx:for="{{item.$orig.content_pic}}" wx:for-item="pic" wx:for-index="ind"><block wx:if="{{item.g1>0}}"><view><view class="layui-imgbox-img"><image src="{{pic}}" data-url="{{pic}}" mode="widthFix" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image></view></view></block></block><block wx:for="{{item.l0}}" wx:for-item="hf" wx:for-index="hfindex" wx:key="hfindex"><view><block wx:if="{{hf.$orig.hfremark}}"><view class="t3">{{"用户回复："+hf.$orig.hfremark+''}}</view></block><block wx:if="{{hf.$orig.hftime}}"><view class="t4">{{"回复时间："+hf.$orig.hftime+''}}</view></block><block wx:for="{{hf.$orig.hfcontent_pic}}" wx:for-item="pic2" wx:for-index="ind2"><block wx:if="{{hf.g2>0}}"><view><view class="layui-imgbox-img"><image src="{{pic2}}" data-url="{{pic2}}" mode="widthFix" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image></view></view></block></block></view></block></view></view></block></block></block><block wx:else><block wx:if="{{statuss==-1}}"><block><view style="font-size:14px;color:#f05555;padding:10px;">工单已驳回</view></block></block><block wx:else><block><view style="font-size:14px;color:#f05555;padding:10px;">等待处理</view></block></block></block></view></view></block><block wx:if="{{showstatus}}"><view class="modal"><view class="modal_jindu"><form data-event-opts="{{[['submit',[['formsubmit',['$event']]]]]}}" bindsubmit="__e"><view data-event-opts="{{[['tap',[['close',['$event']]]]]}}" class="close" bindtap="__e"><image src="../../static/img/close.png"></image></view><view class="title">选择流程</view><view class="uni-list"><radio-group name="liucheng"><block wx:for="{{lclist}}" wx:for-item="item" wx:for-index="index" wx:key="index"><label class="uni-list-cell uni-list-cell-pd"><view><radio style="transform:scale(0.7);" value="{{''+item.id}}"></radio></view><view>{{item.name}}</view></label></block></radio-group><view class="beizhu flex"><label>备注:</label><textarea style="height:100rpx;" placeholder="输入内容" name="content" maxlength="-1"></textarea></view></view><button class="btn" form-type="submit">提交</button></form></view></view></block></block></block><block wx:if="{{nodata}}"><nodata vue-id="55e23715-2" bind:__l="__l"></nodata></block><block wx:if="{{nomore}}"><nomore vue-id="55e23715-3" bind:__l="__l"></nomore></block><block wx:if="{{loading}}"><loading vue-id="55e23715-4" bind:__l="__l"></loading></block><dp-tabbar vue-id="55e23715-5" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="55e23715-6" data-ref="popmsg" bind:__l="__l"></popmsg></view>