(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["components/huodongbmbuydialog/huodongbmbuydialog"],{"45f7":function(t,i,n){"use strict";var e=n("46a5"),u=n.n(e);u.a},"46a5":function(t,i,n){},"76b0":function(t,i,n){"use strict";n.d(i,"b",(function(){return u})),n.d(i,"c",(function(){return o})),n.d(i,"a",(function(){return e}));var e={loading:function(){return n.e("components/loading/loading").then(n.bind(null,"ceaa"))}},u=function(){var t=this,i=t.$createElement,n=(t._self._c,t.isload?t.t("color1"):null),e=t.isload&&t.nowguige.score_price>0&&t.nowguige.sell_price>0?t.t("积分"):null,u=t.isload&&!(t.nowguige.score_price>0&&t.nowguige.sell_price>0)&&t.nowguige.score_price>0?t.t("积分"):null,o=t.isload?t.t("color1"):null;t.$mp.data=Object.assign({},{$root:{m0:n,m1:e,m2:u,m3:o}})},o=[]},7890:function(t,i,n){"use strict";n.r(i);var e=n("76b0"),u=n("dc9c");for(var o in u)["default"].indexOf(o)<0&&function(t){n.d(i,t,(function(){return u[t]}))}(o);n("45f7");var a=n("828b"),r=Object(a["a"])(u["default"],e["b"],e["c"],!1,null,null,null,!1,e["a"],void 0);i["default"]=r.exports},d7aa:function(t,i,n){"use strict";Object.defineProperty(i,"__esModule",{value:!0}),i.default=void 0;var e=getApp(),u={data:function(){return{ks:"",product:{},guigelist:{},guigedata:{},ggselected:{},nowguige:{},gwcnum:1,isload:!1,loading:!1,canaddcart:!0,pre_url:e.globalData.pre_url}},props:{btntype:{default:0},menuindex:{default:-1},controller:{default:"ApiHuodongBaoming"},needaddcart:{default:!0},proid:{},isfuwu:!1},mounted:function(){this.getdata()},methods:{getdata:function(){var t=this;t.loading=!0,e.post(this.controller+"/getproductdetail",{id:t.proid},(function(i){t.loading=!1,t.product=i.product,t.gwcnum=1,t.guigelist=i.guigelist,t.guigedata=i.guigedata;for(var n=i.guigedata,e=[],u=0;u<n.length;u++)e.push(0);t.ks=e.join(","),t.nowguige=t.guigelist[t.ks],t.ggselected=e,t.isload=!0,3!=t.product.freighttype&&4!=t.product.freighttype||(t.canaddcart=!1)}))},buydialogChange:function(){this.$emit("buydialogChange")},ggchange:function(t){var i=t.currentTarget.dataset.idx,n=t.currentTarget.dataset.itemk,e=this.ggselected;e[n]=i;var u=e.join(",");this.ggselected=e,this.ks=u,this.nowguige=this.guigelist[this.ks]},addtobuy:function(t){var i=this.ks,n=this.product.id,u=this.guigelist[i].id,o=this.gwcnum;o<1&&(o=1);var a=n+","+u+","+o;u&&void 0!=u?e.goto("/pagesB/huodongbaoming/buy?prodata="+a):e.error("请选择服务")},gwcplus:function(t){var i=this.gwcnum+1;this.ks;this.product.perlimit>0&&i>this.product.perlimit?e.error("该服务最多购买"+this.product.perlimit+"份"):this.gwcnum=this.gwcnum+1},gwcminus:function(t){var i=this.gwcnum-1;this.ks;i<=0||(this.gwcnum=this.gwcnum-1)},gwcinput:function(t){console.log(t);this.ks;var i=parseInt(t.detail.value);if(i<1)return 1;console.log(i),this.gwcnum=i}}};i.default=u},dc9c:function(t,i,n){"use strict";n.r(i);var e=n("d7aa"),u=n.n(e);for(var o in e)["default"].indexOf(o)<0&&function(t){n.d(i,t,(function(){return e[t]}))}(o);i["default"]=u.a}}]);
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'components/huodongbmbuydialog/huodongbmbuydialog-create-component',
    {
        'components/huodongbmbuydialog/huodongbmbuydialog-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('df3c')['createComponent'](__webpack_require__("7890"))
        })
    },
    [['components/huodongbmbuydialog/huodongbmbuydialog-create-component']]
]);
