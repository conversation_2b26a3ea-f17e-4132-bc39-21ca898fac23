(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["zhaopin/company"],{"22e5":function(t,e,o){"use strict";o.r(e);var n=o("ca24"),a=o.n(n);for(var i in n)["default"].indexOf(i)<0&&function(t){o.d(e,t,(function(){return n[t]}))}(i);e["default"]=a.a},"3085c":function(t,e,o){"use strict";o.d(e,"b",(function(){return n})),o.d(e,"c",(function(){return a})),o.d(e,"a",(function(){}));var n=function(){var t=this,e=t.$createElement,o=(t._self._c,t.isLoaded?t.bannerList.length:null),n=t.isLoaded&&o>0?t.bannerList.length:null,a=t.isLoaded&&o>0&&n>1?t.bannerList.length:null,i=t.isLoaded&&t.companyData.style?t.companyData.team.length:null;t.$mp.data=Object.assign({},{$root:{g0:o,g1:n,g2:a,g3:i}})},a=[]},8224:function(t,e,o){},ca24:function(t,e,o){"use strict";(function(t){var n=o("47a9");Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var a=n(o("af34")),i=getApp(),c={components:{moveDrag:function(){o.e("zhaopin/components/moveDrag/index").then(function(){return resolve(o("fbdf"))}.bind(null,o)).catch(o.oe)},rate:function(){o.e("zhaopin/components/rate/index").then(function(){return resolve(o("903e"))}.bind(null,o)).catch(o.oe)}},data:function(){return{loading:!1,isLoaded:!1,opt:{},companyId:"",companyName:"",companyData:{},time:"--",pageNum:1,pageSize:10,jobList:[],current:0,bannerList:[{url:"https://qiniu-image.qtshe.com/20201210_companyBg.png",type:"pic"}],assistList:[],isShowAll:!1,autoplay:!1,totalCount:0,swiperAutoplay:!0,scrollTop:0,disableScroll:!1,moduleList:[],isEnd:!1,isModuleLoading:!1}},onLoad:function(t){this.opt=i.getopts(t),this.getCompanyDetail(),this.getCompanyPositions()},onShow:function(){},onReachBottom:function(){},methods:{getCompanyDetail:function(){var e=this;e.loading=!0,i.get("ApiZhaopin/getCompanyDetail",{id:e.opt.id},(function(o){if(console.log("企业详情接口返回:",o),e.loading=!1,1===o.status){var n=o.data;e.companyName=n.name,n.photos?e.bannerList=n.photos.split(",").map((function(t){return{url:t,type:"pic"}})):e.bannerList=[{url:n.logo||"https://qiniu-image.qtshe.com/20201210_companyBg.png",type:"pic"}],e.companyData={style:0,serviceUrl:null,team:null,logo:n.logo||"https://qiniu-image.qtshe.com/company_logo_default.png",name:n.name,companyEyeCheckInfo:{type:1,regStatus:n.nature||"--",fromTime:new Date(n.create_time).getTime()||"--",regLocation:n.address||"--",creditCode:n.credit_code||"--",orgNumber:n.org_number||"--",businessScope:n.introduction||"--"},shareContentClassifys:{weixinTalk:"".concat(n.position_count,"个职位招聘中，点击查看更多招聘信息！"),weixinFriend:"【".concat(n.name,"】").concat(n.position_count,"个职位招聘中，点击查看更多招聘信息！"),sinaWb:"【".concat(n.name,"】").concat(n.position_count,"个职位招聘中，点击查看更多招聘信息！"),qqshare:"".concat(n.position_count,"个职位招聘中，点击查看更多招聘信息！"),qqtalk:"".concat(n.position_count,"个职位招聘中，点击查看更多招聘信息！")}},e.isLoaded=!0}else t.showToast({title:o.msg||"获取企业详情失败",icon:"none"})}))},getCompanyPositions:function(){var e=this;e.isModuleLoading=!0,i.get("ApiZhaopin/getCompanyPositions",{company_id:e.opt.id,page:e.pageNum,limit:e.pageSize},(function(o){if(console.log("企业职位列表接口返回:",o),e.isModuleLoading=!1,1===o.status){var n=o.data,i=n.list.map((function(t){return{addressDetail:t.work_address,partJobId:t.id,title:t.title,titleSimple:t.title,salary:t.salary,clearingForm:{key:"0",value:"其他"},welfare:Object.values(t.formatted_options||{}).flat().join(","),companyName:n.company.name,logo:n.company.logo,dataSource:0,jobLineType:1,category:1,entryCount:t.views||0,companyType:{key:"1",value:"企业"},brandName:n.company.name,companyLogo:n.company.logo,parentClassId:10139,classId:10465,c1:118e6,c2:118102e3,c3:*********,labelList:{serviceLabels:[{labelId:61,labelName:"企业认证",labelStyle:'{"id":10,"icon":"","color":"#72AAFA","borderColor":"#72AAFA","background":"#FFFFFF"}'}],descLabels:[{labelId:19,labelName:"最新发布",labelStyle:'{"id":6,"icon":"","color":"#FA5555","borderColor":"#FEEEEE","background":"#FEEEEE"}'}]},listStyle:3,jobIntroduction:[{title:"学历要求",introductionDesc:t.education},{title:"经验要求",introductionDesc:t.experience}],newJobIntroduction:t.description?t.description.replace(/<[^>]+>/g,"").slice(0,50)+"...":"",ptpModParam:{dataSource:0}}}));1===e.pageNum?e.moduleList=i:e.moduleList=[].concat((0,a.default)(e.moduleList),(0,a.default)(i)),e.totalCount=n.total,e.isEnd=e.moduleList.length>=n.total}else t.showToast({title:o.msg||"获取职位列表失败",icon:"none"})}))},loadmore:function(){this.isEnd||this.isModuleLoading||(this.pageNum++,this.getCompanyPositions())},showAllHandle:function(){this.isShowAll=!0},currentHandle:function(t){},getNetworkType:function(){},videoPlayHandle:function(){},videoPauseHandle:function(){},onShareAppMessage:function(){return{title:"青团社兼职",path:"/zhaopin/company?companyId="+this.opt.id}},disableScrollControl:function(t){this.disableScroll=t},saveRef:function(t){}}};e.default=c}).call(this,o("df3c")["default"])},de62:function(t,e,o){"use strict";(function(t,e){var n=o("47a9");o("06e9");n(o("3240"));var a=n(o("f7be"));t.__webpack_require_UNI_MP_PLUGIN__=o,e(a.default)}).call(this,o("3223")["default"],o("df3c")["createPage"])},ec10:function(t,e,o){"use strict";var n=o("8224"),a=o.n(n);a.a},f7be:function(t,e,o){"use strict";o.r(e);var n=o("3085c"),a=o("22e5");for(var i in a)["default"].indexOf(i)<0&&function(t){o.d(e,t,(function(){return a[t]}))}(i);o("ec10");var c=o("828b"),l=Object(c["a"])(a["default"],n["b"],n["c"],!1,null,"7d6d2896",null,!1,n["a"],void 0);e["default"]=l.exports}},[["de62","common/runtime","common/vendor"]]]);