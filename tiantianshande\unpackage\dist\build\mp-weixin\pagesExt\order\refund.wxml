<view class="container"><block wx:if="{{isload}}"><block><form report-submit="true" data-event-opts="{{[['submit',[['formSubmit',['$event']]]],['reset',[['formReset',['$event']]]]]}}" bindsubmit="__e" bindreset="__e"><view class="form-content"><view class="form-item"><text class="label">退款商品</text></view><view class="product"><block wx:for="{{prolist}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="content"><view data-url="{{'/shopPackage/shop/product?id='+item.proid}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image src="{{item.pic}}"></image></view><view class="detail"><text class="t1">{{item.name}}</text><text class="t2">{{item.ggname}}</text><view class="t3"><text class="x1 flex1">{{"￥"+item.sell_price}}</text><view class="num-wrap"><view class="addnum"><view class="minus" data-index="{{index}}" data-ogid="{{item.id}}" data-num="{{refundNum[index].num}}" data-event-opts="{{[['tap',[['gwcminus',['$event']]]]]}}" bindtap="__e"><image class="img" src="/static/img/cart-minus.png"></image></view><input class="input" type="number" data-index="{{index}}" data-ogid="{{item.id}}" data-max="{{item.num-item.refund_num}}" data-num="{{refundNum[index].num}}" data-event-opts="{{[['blur',[['gwcinput',['$event']]]]]}}" value="{{refundNum[index].num}}" bindblur="__e"/><view class="plus" data-index="{{index}}" data-ogid="{{item.id}}" data-max="{{item.num-item.refund_num}}" data-num="{{refundNum[index].num}}" data-event-opts="{{[['tap',[['gwcplus',['$event']]]]]}}" bindtap="__e"><image class="img" src="/static/img/cart-plus.png"></image></view></view><view class="text-desc">{{"申请数量：最多可申请"+item.canRefundNum+"件"}}</view></view></view></view></view></block></view></view><view class="form-content"><block wx:if="{{opt.type=='refund'}}"><view class="form-item" style="display:none;"><text class="label">货物状态</text><view class="input-item"><picker style="height:80rpx;line-height:80rpx;border-bottom:1px solid #EEEEEE;" value="{{cindex}}" range="{{cateArr}}" name="receive" data-event-opts="{{[['change',[['pickerChange',['$event']]]]]}}" bindchange="__e"><view class="picker">{{cindex==-1?'请选择':cateArr[cindex]}}</view></picker></view></view></block><view class="form-item"><text class="label">退款原因</text><view class="input-item"><textarea placeholder="请输入退款原因" placeholder-style="color:#999;" name="reason" data-event-opts="{{[['input',[['reasonInput',['$event']]]]]}}" bindinput="__e"></textarea></view></view><view class="form-item"><text class="label">退款金额(元)</text><view class="flex"><input name="money" type="digit" placeholder="请输入退款金额" placeholder-style="color:#999;" data-event-opts="{{[['input',[['moneyInput',['$event']]]]]}}" value="{{money}}" bindinput="__e"/></view></view><view class="form-item flex-col"><view class="label">上传图片(最多三张)</view><view class="flex" style="flex-wrap:wrap;padding-top:20rpx;" id="content_picpreview"><block wx:for="{{content_pic}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="layui-imgbox"><view class="layui-imgbox-close" data-index="{{index}}" data-field="content_pic" data-event-opts="{{[['tap',[['removeimg',['$event']]]]]}}" bindtap="__e"><image src="/static/img/ico-del.png"></image></view><view class="layui-imgbox-img"><image src="{{item}}" data-url="{{item}}" mode="widthFix" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image></view></view></block><block wx:if="{{$root.g0<3}}"><view class="uploadbtn" style="{{('background:url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 60rpx;background-size:80rpx 80rpx;background-color:#F3F3F3;')}}" data-field="content_pic" data-event-opts="{{[['tap',[['uploadimg',['$event']]]]]}}" bindtap="__e"></view></block></view></view></view><button data-event-opts="{{[['tap',[['formSubmit',['$event']]]]]}}" class="btn" style="{{'background:'+($root.m0)+';'}}" bindtap="__e">确定</button><view style="padding-top:30rpx;"></view></form></block></block><block wx:if="{{loading}}"><loading vue-id="15f83a59-1" bind:__l="__l"></loading></block><dp-tabbar vue-id="15f83a59-2" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="15f83a59-3" data-ref="popmsg" bind:__l="__l"></popmsg></view>