(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["zhaopin/jobMatch"],{1705:function(t,e,r){"use strict";r.d(e,"b",(function(){return a})),r.d(e,"c",(function(){return n})),r.d(e,"a",(function(){}));var a=function(){var t=this,e=t.$createElement,r=(t._self._c,t.__map(t.filterTags,(function(e,r){var a=t.__get_orig(e),n=e.active?t.t("color1"):null;return{$orig:a,m0:n}}))),a=t.t("color1rgb"),n=t.t("color1"),o=t.t("color1"),i=t.__map(t.matchedJobs,(function(e,r){var a=t.__get_orig(e),n=t.t("color1rgb");return{$orig:a,m3:n}}));t.$mp.data=Object.assign({},{$root:{l0:r,m1:a,m2:n,m4:o,l1:i}})},n=[]},2847:function(t,e,r){"use strict";r.r(e);var a=r("bf43"),n=r.n(a);for(var o in a)["default"].indexOf(o)<0&&function(t){r.d(e,t,(function(){return a[t]}))}(o);e["default"]=n.a},"3f47":function(t,e,r){},8526:function(t,e,r){"use strict";r.r(e);var a=r("1705"),n=r("2847");for(var o in n)["default"].indexOf(o)<0&&function(t){r.d(e,t,(function(){return n[t]}))}(o);r("a433");var i=r("828b"),c=Object(i["a"])(n["default"],a["b"],a["c"],!1,null,"1ef2ccde",null,!1,a["a"],void 0);e["default"]=c.exports},a433:function(t,e,r){"use strict";var a=r("3f47"),n=r.n(a);n.a},bf43:function(t,e,r){"use strict";(function(t){var a=r("47a9");Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n=a(r("7eb4")),o=a(r("7ca3")),i=a(r("ee10"));function c(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);e&&(a=a.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,a)}return r}function s(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?c(Object(r),!0).forEach((function(e){(0,o.default)(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):c(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}var l=getApp(),u={data:function(){return{matchStats:{totalJobs:0,highMatch:0,salary:"0"},matchDistribution:[{type:"very-high",label:"90%+",percentage:0},{type:"high",label:"80-89%",percentage:0},{type:"medium",label:"60-79%",percentage:0},{type:"low",label:"60%以下",percentage:0}],filterTags:[{name:"全部",active:!0,field:"",order:""},{name:"薪资优先",active:!1,field:"salary",order:"desc"},{name:"距离优先",active:!1,field:"distance",order:"asc"},{name:"最新发布",active:!1,field:"time",order:"desc"}],matchedJobs:[],loading:!1,currentPage:1,pageSize:10,filterData:null,matchResult:null,longitude:"",latitude:""}},onLoad:function(e){if(console.log("页面加载参数:",e),this.getUserLocation(),"filter"===e.from){var r=t.getStorageSync("jobMatchFilterData"),a=t.getStorageSync("jobMatchResult");console.log("从缓存获取的筛选数据:",r),console.log("从缓存获取的匹配结果:",a),r&&(this.filterData=r),a&&1===a.status?this.processMatchResult(a):this.fetchMatchedJobs(),t.removeStorageSync("jobMatchFilterData"),t.removeStorageSync("jobMatchResult")}else this.fetchMatchedJobs()},methods:{getUserLocation:function(){var e=this;l.getLocation((function(t){console.log("获取位置成功:",t),e.latitude=t.latitude,e.longitude=t.longitude,e.fetchMatchedJobs()}),(function(e){console.error("获取位置失败:",e),t.showToast({title:"获取位置信息失败，距离排序可能不准确",icon:"none"})}))},loadUserProfile:function(){var e=this;return(0,i.default)(n.default.mark((function r(){return n.default.wrap((function(r){while(1)switch(r.prev=r.next){case 0:return r.prev=0,r.next=3,e.$api.getUserProfile();case 3:r.sent,r.next=9;break;case 6:r.prev=6,r.t0=r["catch"](0),t.showToast({title:"获取简历信息失败",icon:"none"});case 9:case"end":return r.stop()}}),r,null,[[0,6]])})))()},fetchMatchedJobs:function(){var e=this;return(0,i.default)(n.default.mark((function r(){var a,o,i,c;return n.default.wrap((function(r){while(1)switch(r.prev=r.next){case 0:try{e.loading=!0,a=e.filterTags.find((function(t){return t.active}))||e.filterTags[0],o=a.field,i=a.order,c=s({page:e.currentPage,page_size:e.pageSize,sort_field:o,sort_order:i,longitude:e.longitude,latitude:e.latitude},e.filterData||{}),console.log("请求参数:",c),l.get("apiZhaopin/jobMatch",c,(function(r){console.log("匹配结果:",r),1===r.status?e.processMatchResult(r):t.showToast({title:r.msg||"获取数据失败",icon:"none"}),e.loading=!1}))}catch(n){console.error("获取匹配职位失败:",n),t.showToast({title:"获取匹配职位失败",icon:"none"}),e.loading=!1}case 1:case"end":return r.stop()}}),r)})))()},getActiveSortType:function(){var t=this.filterTags.find((function(t){return t.active}));return t?t.field:""},toggleFilter:function(t){this.filterTags.forEach((function(e,r){e.active=r===t})),this.currentPage=1,this.fetchMatchedJobs()},viewJobDetail:function(e){t.navigateTo({url:"/zhaopin/partdetails?id=".concat(e)})},stripHtml:function(t){if(!t)return"";try{return t.replace(/<[^>]+>/g,"").replace(/&[^;]+;/g,"").trim()}catch(e){return console.warn("处理HTML标签失败:",e),String(t)}},processMatchResult:function(e){var r=e.data;if(!r||!r.list)return console.error("匹配结果数据格式错误:",e),void t.showToast({title:"数据格式错误",icon:"none"});try{if(r.statistics){this.matchStats={totalJobs:r.statistics.match_count||0,highMatch:(r.statistics.match_degree||0).toFixed(1),salary:r.statistics.average_salary||"0"};var a=r.statistics.distribution||{},n=(a["90up"]||0)+(a["80_89"]||0)+(a["60_79"]||0)+(a["below60"]||0),o=n||1,i=[{type:"very-high",label:"90%+",count:a["90up"]||0,percentage:Math.round((a["90up"]||0)/o*100)},{type:"high",label:"80-89%",count:a["80_89"]||0,percentage:Math.round((a["80_89"]||0)/o*100)},{type:"medium",label:"60-79%",count:a["60_79"]||0,percentage:Math.round((a["60_79"]||0)/o*100)},{type:"low",label:"60%以下",count:a["below60"]||0,percentage:Math.round((a["below60"]||0)/o*100)}];this.matchDistribution=i}this.matchedJobs=(r.list||[]).map((function(t){try{var e,r=t.work_time_type||"",a=t.work_mode||"",n=t.work_intensity||"",o=[];return r&&o.push(r),a&&o.push(a),n&&o.push(n),t.rest_time&&o.push(t.rest_time),t.payment&&o.push(t.payment),t.education&&o.push(t.education),t.experience&&o.push(t.experience),{id:t.id||"",title:t.title||"职位名称未知",company:(null===(e=t.company)||void 0===e?void 0:e.name)||"未知公司",tags:o,salary:t.salary||"薪资面议",location:t.work_address||t.district||t.city||"地点未知",workTime:t.work_time_start&&t.work_time_end?"".concat(t.work_time_start.substring(0,5),"-").concat(t.work_time_end.substring(0,5)):"",match_degree:t.match_degree||0,distance:t.distance_text||null,latitude:t.latitude||null,longitude:t.longitude||null}}catch(i){return console.error("处理职位数据时出错:",i,t),{id:t.id||"",title:"数据解析错误",company:"未知公司",tags:[],salary:"薪资面议",location:"地点未知",workTime:"",match_degree:0,distance:null}}})),console.log("处理后的职位数据:",this.matchedJobs)}catch(c){console.error("处理匹配结果时出错:",c),t.showToast({title:"数据处理失败",icon:"none"})}}}};e.default=u}).call(this,r("df3c")["default"])},bf9a:function(t,e,r){"use strict";(function(t,e){var a=r("47a9");r("06e9");a(r("3240"));var n=a(r("8526"));t.__webpack_require_UNI_MP_PLUGIN__=r,e(n.default)}).call(this,r("3223")["default"],r("df3c")["createPage"])}},[["bf9a","common/runtime","common/vendor"]]]);