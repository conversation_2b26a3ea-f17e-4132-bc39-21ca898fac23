(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["yuyue/cycle/buy"],{"08df":function(t,e,o){"use strict";o.d(e,"b",(function(){return a})),o.d(e,"c",(function(){return i})),o.d(e,"a",(function(){return n}));var n={couponlist:function(){return o.e("components/couponlist/couponlist").then(o.bind(null,"c5a13"))},loading:function(){return o.e("components/loading/loading").then(o.bind(null,"ceaa"))},dpTabbar:function(){return o.e("components/dp-tabbar/dp-tabbar").then(o.bind(null,"b875"))},popmsg:function(){return o.e("components/popmsg/popmsg").then(o.bind(null,"2bf2"))}},a=function(){var t=this,e=t.$createElement,o=(t._self._c,t.isload?t.t("优惠券"):null),n=t.isload?t.couponList.length:null,a=t.isload&&n>0?t.t("color1"):null,i=t.isload&&n>0&&!t.couponSelected.id?t.couponList.length:null,c=!t.isload||n>0?null:t.t("优惠券"),r=t.isload?t.t("color1"):null,s=t.isload?t.t("color1rgb"):null,d=t.isload&&t.couponVisible?t.t("优惠券"):null;t.$mp.data=Object.assign({},{$root:{m0:o,g0:n,m1:a,g1:i,m2:c,m3:r,m4:s,m5:d}})},i=[]},"1d7e":function(t,e,o){"use strict";o.r(e);var n=o("08df"),a=o("2b06");for(var i in a)["default"].indexOf(i)<0&&function(t){o.d(e,t,(function(){return a[t]}))}(i);o("6eb9");var c=o("828b"),r=Object(c["a"])(a["default"],n["b"],n["c"],!1,null,null,null,!1,n["a"],void 0);e["default"]=r.exports},"2b06":function(t,e,o){"use strict";o.r(e);var n=o("84cf"),a=o.n(n);for(var i in n)["default"].indexOf(i)<0&&function(t){o.d(e,t,(function(){return n[t]}))}(i);e["default"]=a.a},"308a":function(t,e,o){},"6eb9":function(t,e,o){"use strict";var n=o("308a"),a=o.n(n);a.a},"84cf":function(t,e,o){"use strict";(function(t){Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n=getApp(),a={components:{couponlist:function(){o.e("components/couponlist/couponlist").then(function(){return resolve(o("c5a13"))}.bind(null,o)).catch(o.oe)}},data:function(){return{opt:{},loading:!0,isload:!1,menuindex:-1,submitDisabled:!1,productInfo:{},address:{},priceInfo:{product_total_price:"0.00",total_price:"0.00",coupon_money:"0.00"},memberInfo:{},couponList:[],couponSelected:{},couponDiscount:"0.00",totalPrice:"0.00",startDate:"",minStartDate:"",couponVisible:!1,selectedPaytype:1}},onLoad:function(t){this.opt=n.getopts(t),this.getdata()},onShow:function(){var e=t.getStorageSync("choosedAddress");e&&(this.address=e,t.removeStorageSync("choosedAddress"),this.calculatePrice())},methods:{getdata:function(){var t=this,e=t.opt.product_id;e?(t.loading=!0,n.get("ApiPeriodicService/buy",{product_id:e},(function(e){if(t.loading=!1,0!=e.status){t.productInfo=e.data.product,t.address=e.data.address||{},t.priceInfo=e.data.price_info,t.memberInfo=e.data.member_info,t.couponList=e.data.coupon_list||[];var o=new Date,a=new Date(o);a.setDate(a.getDate()+1),t.minStartDate=t.formatDate(a),t.startDate=t.minStartDate,t.calculatePrice(),t.isload=!0}else n.alert(e.msg,(function(){n.goback()}))}))):n.alert("缺少服务ID",(function(){n.goback()}))},calculatePrice:function(){var t=parseFloat(this.priceInfo.product_total_price)||0,e=0;this.couponSelected.id;var o=parseFloat(this.priceInfo.total_price)||t;this.couponSelected.id?(e=parseFloat(this.couponSelected.money)||0,o=t-e,this.couponDiscount=e.toFixed(2)):this.couponDiscount="0.00",o<0&&(o=0),this.totalPrice=o.toFixed(2)},bindStartDateChange:function(t){this.startDate=t.detail.value},formatDate:function(t){var e=t.getFullYear(),o=String(t.getMonth()+1).padStart(2,"0"),n=String(t.getDate()).padStart(2,"0");return"".concat(e,"-").concat(o,"-").concat(n)},showCouponList:function(){this.couponList.length>0&&(this.couponVisible=!0)},hideCouponList:function(){this.couponVisible=!1},chooseCoupon:function(t){this.couponSelected.id===t.rid?this.couponSelected={}:this.couponSelected=this.couponList[t.key],this.hideCouponList(),this.calculatePrice()},paytypeChange:function(t){this.selectedPaytype=parseInt(t.detail.value)},submitOrder:function(t){var e=this,o=t.detail.value;if(e.address&&e.address.id)if(e.startDate){var a={product_id:e.productInfo.id,address_id:e.address.id,linkman:e.address.name,tel:e.address.tel,address:e.address.province+e.address.city+e.address.area+e.address.address,longitude:e.address.longitude||"",latitude:e.address.latitude||"",start_date:e.startDate,remark:o.remark||"",coupon_id:e.couponSelected.id||0,platform:n.globalData.platform,paytype:e.selectedPaytype};e.submitDisabled=!0,n.showLoading("提交中..."),n.post("ApiPeriodicService/createOrder",a,(function(t){n.showLoading(!1),e.submitDisabled=!1,0!=t.status?n.goto("/pages/pay/pay?id="+t.payorderid):n.error(t.msg)}))}else n.error("请选择期望开始日期");else n.error("请选择服务地址")}}};e.default=a}).call(this,o("df3c")["default"])},"8f62":function(t,e,o){"use strict";(function(t,e){var n=o("47a9");o("06e9");n(o("3240"));var a=n(o("1d7e"));t.__webpack_require_UNI_MP_PLUGIN__=o,e(a.default)}).call(this,o("3223")["default"],o("df3c")["createPage"])}},[["8f62","common/runtime","common/vendor"]]]);