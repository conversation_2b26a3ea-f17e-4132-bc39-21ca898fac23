<view class="container"><block wx:if="{{isload}}"><block><view class="topbg"></view><view class="order-content"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><view class="order-box" data-url="{{'orderlog?id='+item.$orig.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="pic"><image class="img" src="{{item.$orig.pic}}"></image></view><view class="detail"><text class="t1">{{item.$orig.name}}</text><text class="t2">{{"数量："+item.$orig.num}}</text><text class="t2">{{"存入时间："+item.m0}}</text></view><block wx:if="{{item.$orig.status==0}}"><view class="takeout st0">审核中</view></block><block wx:if="{{item.$orig.status==1}}"><view class="takeout" data-orderid="{{item.$orig.id}}" data-num="{{item.$orig.num}}" data-event-opts="{{[['tap',[['takeout',['$event']]]]]}}" catchtap="__e"><image class="img" src="/static/img/deposit_takeout.png"></image>取出</view></block><block wx:if="{{item.$orig.status==2}}"><view class="takeout st2" data-orderid="{{item.$orig.id}}">已取走</view></block><block wx:if="{{item.$orig.status==3}}"><view class="takeout st3" data-orderid="{{item.$orig.id}}">未通过</view></block><block wx:if="{{item.$orig.status==4}}"><view class="takeout st3">已过期</view></block></view></block></block></view><block wx:if="{{nomore}}"><nomore vue-id="263fd1dc-1" bind:__l="__l"></nomore></block><block wx:if="{{nodata}}"><nodata vue-id="263fd1dc-2" bind:__l="__l"></nodata></block><view class="bottom"><view class="btn2" data-orderid="0" data-event-opts="{{[['tap',[['takeouts',['$event']]]]]}}" bindtap="__e">一键取出</view><view class="btn1" style="{{'background:'+($root.m1)+';'}}" data-url="{{'add?bid='+bid}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">我要寄存</view></view></block></block><block wx:if="{{loading}}"><loading vue-id="263fd1dc-3" bind:__l="__l"></loading></block><dp-tabbar vue-id="263fd1dc-4" opt="{{opt}}" bind:__l="__l"></dp-tabbar><block wx:if="{{boxShow}}"><view data-event-opts="{{[['touchmove',[['disabledScroll',['$event']]]]]}}" catchtouchmove="__e"><view data-event-opts="{{[['tap',[['handleClickMask',['$event']]]]]}}" class="popup__overlay" catchtap="__e"></view><view class="popup__modal"><view class="popup__title"><text class="popup__title-text">请输入取出数量</text><image class="popup__close" style="width:36rpx;height:36rpx;" src="/static/img/close.png" data-event-opts="{{[['tap',[['handleClickMask',['$event']]]]]}}" catchtap="__e"></image></view><view class="popup__content takeoutBox"><form report-submit="true" data-event-opts="{{[['submit',[['formSubmit',['$event']]]],['reset',[['formReset',['$event']]]]]}}" bindsubmit="__e" bindreset="__e"><view class="orderinfo"><view class="item"><text class="t1">取出数量</text><input class="t2" type="text" placeholder="请输入要取出的数量" placeholder-style="font-size:28rpx;color:#BBBBBB" name="numbers" value="{{num}}"/></view></view><button class="btn" style="{{'background:'+($root.m2)+';'}}" form-type="submit">确定</button></form></view></view></view></block></view>