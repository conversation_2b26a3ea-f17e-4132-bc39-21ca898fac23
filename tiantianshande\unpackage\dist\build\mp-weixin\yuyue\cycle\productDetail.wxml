<view class="container"><block wx:if="{{isload}}"><block><view class="swiper-container"><swiper class="swiper" indicator-dots="{{false}}" autoplay="{{true}}" interval="{{5000}}" data-event-opts="{{[['change',[['swiperChange',['$event']]]]]}}" bindchange="__e"><block wx:for="{{product.pics_list}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><swiper-item class="swiper-item"><view class="swiper-item-view"><image class="img" src="{{item}}" mode="widthFix" data-url="{{item}}" data-urls="{{product.pics_list}}" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image></view></swiper-item></block></block></swiper><block wx:if="{{$root.g0>1}}"><view class="imageCount">{{current+1+"/"+$root.g1}}</view></block></view><view class="collage_title flex-bt"><text><text class="price">{{"￥"+product.sell_price}}<text style="font-size:24rpx;color:#999;">/期</text></text><block wx:if="{{product.market_price>0}}"><text class="m-price">{{"￥"+product.market_price}}</text></block></text><view class="ps_title flex-y-center" style="{{'background:'+('rgba('+$root.m0+',0.12)')+';'+('color:'+($root.m1)+';')}}">{{"共"+product.total_period+"期"}}</view></view><view class="header"><view class="title"><view class="lef"><text>{{product.name}}</text></view><view data-event-opts="{{[['tap',[['shareClick',['$event']]]]]}}" class="share" bindtap="__e"><image src="/static/img/share.png"></image><text>分享</text></view></view><view class="sales_stock"><view class="f2">{{"已售:"+product.sales}}</view></view></view><block wx:if="{{business&&business.id}}"><view class="shop"><image class="p1" src="{{business.logo}}"></image><view class="p2 flex1"><view class="t1">{{business.name}}</view><view class="t2">{{business.desc}}</view></view><button class="p4" style="{{'background:'+('linear-gradient(90deg,'+$root.m2+' 0%,rgba('+$root.m3+',0.8) 100%)')+';'}}" data-url="{{'/pages/business/index?id='+business.id}}" data-opentype="reLaunch" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">进入店铺</button></view></block><view class="detail_title"><view class="t1"></view><view class="t2"></view><view class="t0">服务描述</view><view class="t2"></view><view class="t1"></view></view><view class="detail"><dp vue-id="8fe2bfa2-1" pagecontent="{{pagecontent}}" bind:__l="__l"></dp></view><view style="width:100%;height:120rpx;"></view><block wx:if="{{product.status==1}}"><view class="{{['bottombar','flex-row','flex-xy-center',menuindex>-1?'tabbarbot':'notabbarbot']}}"><block wx:if="{{kfurl!='contact::'}}"><view class="cart flex-col flex-x-center flex-y-center" data-url="{{kfurl}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image class="img" src="/static/img/kefu.png"></image><view class="t1">客服</view></view></block><block wx:else><button class="cart flex-col flex-x-center flex-y-center" open-type="contact"><image class="img" src="/static/img/kefu.png"></image><view class="t1">客服</view></button></block><view data-event-opts="{{[['tap',[['addfavorite',['$event']]]]]}}" class="favorite flex-col flex-x-center flex-y-center" bindtap="__e"><image class="img" src="{{'/static/img/shoucang'+(isfavorite?'2':'')+'.png'}}"></image><view class="t1">{{isfavorite?'已收藏':'收藏'}}</view></view><view data-event-opts="{{[['tap',[['tobuy',['$event']]]]]}}" class="tobuy flex1" style="{{'background:'+($root.m4)+';'}}" bindtap="__e"><text>立即购买</text></view></view></block><block wx:if="{{sharetypevisible}}"><view class="popup__container"><view data-event-opts="{{[['tap',[['handleClickMask',['$event']]]]]}}" class="popup__overlay" catchtap="__e"></view><view class="popup__modal" style="height:320rpx;min-height:320rpx;"><view class="popup__content"><view class="sharetypecontent"><block wx:if="{{$root.m5=='app'}}"><view data-event-opts="{{[['tap',[['shareapp',['$event']]]]]}}" class="f1" bindtap="__e"><image class="img" src="/static/img/weixin.png"></image><text class="t1">分享给好友</text></view></block><block wx:else><block wx:if="{{$root.m6=='mp'}}"><view data-event-opts="{{[['tap',[['sharemp',['$event']]]]]}}" class="f1" bindtap="__e"><image class="img" src="/static/img/weixin.png"></image><text class="t1">分享给好友</text></view></block><block wx:else><block wx:if="{{$root.m7=='h5'}}"><view data-event-opts="{{[['tap',[['sharemp',['$event']]]]]}}" class="f1" bindtap="__e"><image class="img" src="/static/img/weixin.png"></image><text class="t1">分享给好友</text></view></block><block wx:else><button class="f1" open-type="share"><image class="img" src="/static/img/weixin.png"></image><text class="t1">分享给好友</text></button></block></block></block><view data-event-opts="{{[['tap',[['showPoster',['$event']]]]]}}" class="f2" bindtap="__e"><image class="img" src="/static/img/sharepic.png"></image><text class="t1">生成分享图片</text></view></view></view></view></view></block><block wx:if="{{showposter}}"><view class="posterDialog"><view class="main"><view data-event-opts="{{[['tap',[['posterDialogClose',['$event']]]]]}}" class="close" bindtap="__e"><image class="img" src="/static/img/close.png"></image></view><view class="content"><image class="img" src="{{posterpic}}" mode="widthFix" data-url="{{posterpic}}" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image></view></view></view></block></block></block><block wx:if="{{loading}}"><loading vue-id="8fe2bfa2-2" bind:__l="__l"></loading></block><dp-tabbar vue-id="8fe2bfa2-3" opt="{{opt}}" data-event-opts="{{[['^getmenuindex',[['getmenuindex']]]]}}" bind:getmenuindex="__e" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="8fe2bfa2-4" data-ref="popmsg" bind:__l="__l"></popmsg></view>