<view class="container"><dd-tab vue-id="026e1387-1" itemdata="{{['全部('+countall+')','已上架('+count1+')','未上架('+count0+')']}}" itemst="{{['all','1','0']}}" st="{{st}}" isfixed="{{true}}" data-event-opts="{{[['^changetab',[['changetab']]]]}}" bind:changetab="__e" bind:__l="__l"></dd-tab><view style="width:100%;height:100rpx;"></view><view class="topsearch flex-y-center"><view class="f1 flex-y-center"><image class="img" src="{{pre_url+'/static/img/search_ico.png'}}"></image><input placeholder="输入关键字搜索" placeholder-style="font-size:24rpx;color:#C2C2C2" data-event-opts="{{[['confirm',[['searchConfirm',['$event']]]]]}}" value="{{keyword}}" bindconfirm="__e"/></view></view><view class="order-content" id="datalist"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><view class="order-box"><view class="content" style="border-bottom:none;"><view><image src="{{item.$orig.pic}}"></image></view><view class="detail"><text class="t1">{{item.$orig.name}}</text><view class="t2">{{"剩余："+item.$orig.stock}}<text style="color:#a88;padding-left:20rpx;">{{"已售："+item.$orig.sales}}</text></view><view class="t3"><text class="x1">{{"￥"+item.$orig.sell_price}}</text><text class="x2">{{"￥"+item.$orig.market_price}}</text></view></view></view><view class="op"><block wx:if="{{!item.$orig.status||item.$orig.status==0}}"><text class="flex1" style="color:red;">未上架</text></block><block wx:else><text class="flex1" style="color:green;">已上架</text></block><block wx:if="{{!item.$orig.ischecked}}"><text class="flex1" style="color:orange;">待审核</text></block><block wx:if="{{bottomButShow}}"><block><block wx:if="{{!item.$orig.status||item.$orig.status==0}}"><view class="btn1" style="{{'background:'+(item.m0)+';'}}" data-st="1" data-id="{{item.$orig.id}}" data-event-opts="{{[['tap',[['setst',['$event']]]]]}}" bindtap="__e">上架</view></block><block wx:else><view class="btn1" style="{{'background:'+(item.m1)+';'}}" data-st="0" data-id="{{item.$orig.id}}" data-event-opts="{{[['tap',[['setst',['$event']]]]]}}" bindtap="__e">下架</view></block><view class="btn2" data-url="{{'edit?id='+item.$orig.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">编辑</view><view class="btn2" data-id="{{item.$orig.id}}" data-event-opts="{{[['tap',[['todel',['$event']]]]]}}" bindtap="__e">删除</view></block></block><block wx:else><block><view data-event-opts="{{[['tap',[['couponAddChange',['$0'],[[['datalist','',index]]]]]]]}}" class="btn1" style="{{'background:'+($root.m2)+';'}}" bindtap="__e">添加</view></block></block></view></view></block></block></view><block wx:if="{{nomore}}"><nomore vue-id="026e1387-2" bind:__l="__l"></nomore></block><block wx:if="{{nodata}}"><nodata vue-id="026e1387-3" bind:__l="__l"></nodata></block></view>