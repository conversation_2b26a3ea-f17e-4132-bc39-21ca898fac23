<view class="container"><block wx:if="{{isload}}"><block><view class="ordertop" style="{{('background:url('+pre_url+'/static/img/ordertop_refund.jpg);background-size:100%')}}"><block wx:if="{{detail.refund_status==0}}"><view class="f1"><view class="t1">已取消</view></view></block><block wx:if="{{detail.refund_status==1}}"><view class="f1"><view class="t1">待审核</view></view></block><block wx:if="{{detail.refund_status==2}}"><view class="f1"><view class="t1">审核通过，已退款</view></view></block><block wx:if="{{detail.refund_status==3}}"><view class="f1"><view class="t1">驳回</view></view></block><block wx:if="{{detail.refund_status==4}}"><view class="f1"><view class="t1">审核通过，待退货</view><view class="t2">联系买家进行退货</view></view></block></view><view class="product"><block wx:for="{{prolist}}" wx:for-item="item" wx:for-index="idx" wx:key="idx"><view class="content"><view data-url="{{'/shopPackage/shop/product?id='+item.proid}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image src="{{item.pic}}"></image></view><view class="detail"><text class="t1">{{item.name}}</text><text class="t2">{{item.ggname}}</text><view class="t3"><text class="x1 flex1">{{"￥"+item.sell_price}}</text><text class="x2">{{"×"+item.refund_num}}</text></view></view></view></block></view><view class="orderinfo"><view class="item"><text class="t1">下单人</text><text class="flex1"></text><image style="width:80rpx;height:80rpx;margin-right:8rpx;" src="{{detail.headimg}}"></image><text style="height:80rpx;line-height:80rpx;">{{detail.nickname}}</text></view><view class="item"><text class="t1">{{$root.m0+"ID"}}</text><text class="t2">{{detail.mid}}</text></view></view><view class="orderinfo"><view class="item"><text class="t1">类型</text><text class="t2 red">{{detail.refund_type_label}}</text></view><view class="item"><text class="t1">退货单编号</text><text class="t2" user-select="true" selectable="true">{{detail.refund_ordernum}}</text></view><view class="item"><text class="t1">申请退款金额</text><text class="t2 red">{{"¥"+detail.refund_money}}</text></view><view class="item"><text class="t1">本单已退款金额</text><text class="t2 red">{{"¥"+detail.refundMoneyTotal}}</text></view><view class="item"><text class="t1">申请时间</text><text class="t2">{{detail.createtime}}</text></view><block wx:if="{{detail.refund_status>0}}"><view class="item"><text class="t1">退款状态</text><block wx:if="{{detail.refund_status==0}}"><text class="t2 grey">已取消</text></block><block wx:if="{{detail.refund_status==1}}"><text class="t2 red">待审核</text></block><block wx:if="{{detail.refund_status==2}}"><text class="t2 red">已退款</text></block><block wx:if="{{detail.refund_status==3}}"><text class="t2 red">已驳回</text></block><block wx:if="{{detail.refund_status==4}}"><text class="t2 red">审核通过，待退货</text></block></view></block><block wx:if="{{detail.refund_status>0}}"><view class="item"><text class="t1">退款原因</text><text class="t2 red">{{detail.refund_reason}}</text></view></block><view class="item"><text class="t1">图片</text><block wx:if="{{$root.g0}}"><text class="t2"><block wx:for="{{detail.refund_pics}}" wx:for-item="item" wx:for-index="__i0__"><block><image class="imageMin" src="{{item}}" mode="widthFix" data-url="{{item}}" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image></block></block></text></block><block wx:else><text class="t2">无</text></block></view><block wx:if="{{detail.refund_checkremark}}"><view class="item"><text class="t1">审核备注</text><text class="t2 red">{{detail.refund_checkremark}}</text></view></block></view><view class="orderinfo"><view class="item"><text class="t1">订单编号</text><text class="t2" user-select="true" selectable="true">{{order.ordernum}}</text></view><view class="item"><text class="t1">下单时间</text><text class="t2">{{order.createtime}}</text></view><block wx:if="{{order.status>0&&order.paytypeid!='4'&&order.paytime}}"><view class="item"><text class="t1">支付时间</text><text class="t2">{{order.paytime}}</text></view></block><block wx:if="{{order.status>0&&order.paytime}}"><view class="item"><text class="t1">支付方式</text><text class="t2">{{order.paytype}}</text></view></block><block wx:if="{{order.status>1&&order.send_time}}"><view class="item"><text class="t1">发货时间</text><text class="t2">{{order.send_time}}</text></view></block><block wx:if="{{order.status==3&&order.collect_time}}"><view class="item"><text class="t1">收货时间</text><text class="t2">{{order.collect_time}}</text></view></block></view><view class="orderinfo"><view class="item"><text class="t1">商品金额</text><text class="t2 red">{{"¥"+order.product_price}}</text></view><block wx:if="{{order.disprice>0}}"><view class="item"><text class="t1">{{$root.m1+"折扣"}}</text><text class="t2 red">{{"-¥"+order.leveldk_money}}</text></view></block><block wx:if="{{order.jianmoney>0}}"><view class="item"><text class="t1">满减活动</text><text class="t2 red">{{"-¥"+order.manjian_money}}</text></view></block><view class="item"><text class="t1">配送方式</text><text class="t2">{{order.freight_text}}</text></view><block wx:if="{{order.freight_type==1&&order.freightprice>0}}"><view class="item"><text class="t1">服务费</text><text class="t2 red">{{"+¥"+order.freight_price}}</text></view></block><block wx:if="{{order.freight_time}}"><view class="item"><text class="t1">{{(order.freight_type!=1?'配送':'提货')+"时间"}}</text><text class="t2">{{order.freight_time}}</text></view></block><block wx:if="{{order.coupon_money>0}}"><view class="item"><text class="t1">{{$root.m2+"抵扣"}}</text><text class="t2 red">{{"-¥"+order.coupon_money}}</text></view></block><block wx:if="{{order.scoredk>0}}"><view class="item"><text class="t1">{{$root.m3+"抵扣"}}</text><text class="t2 red">{{"-¥"+order.scoredk_money}}</text></view></block><view class="item"><text class="t1">实付款</text><text class="t2 red">{{"¥"+order.totalprice}}</text></view><view class="item"><text class="t1">订单状态</text><block wx:if="{{order.status==0}}"><text class="t2">未付款</text></block><block wx:if="{{order.status==1}}"><text class="t2">已付款</text></block><block wx:if="{{order.status==2}}"><text class="t2">已发货</text></block><block wx:if="{{order.status==3}}"><text class="t2">已收货</text></block><block wx:if="{{order.status==4}}"><text class="t2">已关闭</text></block></view></view><view style="width:100%;height:120rpx;"></view><view class="bottom"><block wx:if="{{detail.refund_status==1}}"><block><view class="btn2" data-id="{{detail.id}}" data-event-opts="{{[['tap',[['refundnopassShow',['$event']]]]]}}" bindtap="__e">驳回</view><block wx:if="{{detail.refund_type=='refund'}}"><view class="btn2" data-id="{{detail.id}}" data-event-opts="{{[['tap',[['refundpass',['$event']]]]]}}" bindtap="__e">退款通过</view></block><block wx:else><block wx:if="{{detail.refund_type=='return'}}"><view class="btn2" data-id="{{detail.id}}" data-event-opts="{{[['tap',[['returnpass',['$event']]]]]}}" bindtap="__e">退货退款通过</view></block></block></block></block><block wx:if="{{detail.refund_type=='return'&&detail.refund_status==4}}"><view class="btn2" data-id="{{detail.id}}" data-title="确定要收到退货并退款吗？" data-event-opts="{{[['tap',[['refundpass',['$event']]]]]}}" bindtap="__e">收货并退款</view></block></view><uni-popup class="vue-ref" vue-id="bcac2958-1" id="dialogSetremark" type="dialog" data-ref="dialogSetremark" bind:__l="__l" vue-slots="{{['default']}}"><uni-popup-dialog vue-id="{{('bcac2958-2')+','+('bcac2958-1')}}" mode="input" title="确定要驳回申请吗？" value="{{detail.remark}}" placeholder="请输入备注" data-event-opts="{{[['^confirm',[['refundnopass']]]]}}" bind:confirm="__e" bind:__l="__l"></uni-popup-dialog></uni-popup></block></block><block wx:if="{{loading}}"><loading vue-id="bcac2958-3" bind:__l="__l"></loading></block><dp-tabbar vue-id="bcac2958-4" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="bcac2958-5" data-ref="popmsg" bind:__l="__l"></popmsg></view>