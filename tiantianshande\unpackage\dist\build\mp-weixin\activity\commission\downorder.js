(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["activity/commission/downorder"],{"00fa":function(t,n,e){"use strict";e.r(n);var o=e("fb46"),a=e.n(o);for(var i in o)["default"].indexOf(i)<0&&function(t){e.d(n,t,(function(){return o[t]}))}(i);n["default"]=a.a},4886:function(t,n,e){"use strict";var o=e("e423"),a=e.n(o);a.a},"4e2f":function(t,n,e){"use strict";e.d(n,"b",(function(){return a})),e.d(n,"c",(function(){return i})),e.d(n,"a",(function(){return o}));var o={ddTab:function(){return e.e("components/dd-tab/dd-tab").then(e.bind(null,"caa1"))},uniPopup:function(){return Promise.all([e.e("common/vendor"),e.e("components/uni-popup/uni-popup")]).then(e.bind(null,"ca44a"))},nodata:function(){return e.e("components/nodata/nodata").then(e.bind(null,"101c"))},nomore:function(){return e.e("components/nomore/nomore").then(e.bind(null,"3892"))},loading:function(){return e.e("components/loading/loading").then(e.bind(null,"ceaa"))},dpTabbar:function(){return e.e("components/dp-tabbar/dp-tabbar").then(e.bind(null,"b875"))},popmsg:function(){return e.e("components/popmsg/popmsg").then(e.bind(null,"2bf2"))}},a=function(){var t=this.$createElement,n=(this._self._c,this.isload?this.t("分销订单"):null),e=this.isload?this.datalist&&this.datalist.length>0:null;this.$mp.data=Object.assign({},{$root:{m0:n,g0:e}})},i=[]},"5da5":function(t,n,e){"use strict";(function(t,n){var o=e("47a9");e("06e9");o(e("3240"));var a=o(e("cf11"));t.__webpack_require_UNI_MP_PLUGIN__=e,n(a.default)}).call(this,e("3223")["default"],e("df3c")["createPage"])},cf11:function(t,n,e){"use strict";e.r(n);var o=e("4e2f"),a=e("00fa");for(var i in a)["default"].indexOf(i)<0&&function(t){e.d(n,t,(function(){return a[t]}))}(i);e("4886");var s=e("828b"),u=Object(s["a"])(a["default"],o["b"],o["c"],!1,null,null,null,!1,o["a"],void 0);n["default"]=u.exports},e423:function(t,n,e){},fb46:function(t,n,e){"use strict";(function(t){Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;var e=getApp(),o={data:function(){return{opt:{},loading:!1,isload:!1,menuindex:-1,st:0,count:0,commissionyj:0,pagenum:1,datalist:[],express_content:"",nodata:!1,nomore:!1}},onLoad:function(t){this.opt=e.getopts(t),this.getdata()},onPullDownRefresh:function(){this.pagenum=1,this.datalist=[],this.getdata()},onReachBottom:function(){this.nodata||this.nomore||(this.pagenum=this.pagenum+1,this.getdata())},methods:{getdata:function(){var n=this,o=n.pagenum,a=n.st;n.loading=!0,n.nodata=!1,n.nomore=!1,e.get("ApiAgent/agorder",{st:a,pagenum:o},(function(e){n.loading=!1;var a=e.datalist;if(1==o)n.commissionyj=e.commissionyj,n.count=e.count,n.datalist=a,0==a.length&&(n.nodata=!0),t.setNavigationBarTitle({title:n.t("分销订单")}),n.loaded();else if(0==a.length)n.nomore=!0;else{var i=n.datalist,s=i.concat(a);n.datalist=s}}))},changetab:function(n){this.pagenum=1,this.st=n,this.datalist=[],t.pageScrollTo({scrollTop:0,duration:0}),this.getdata()},logistics:function(t){console.log(t);var n=t.express_com,o=t.express_no,a=t.express_content,i=t.express_type;console.log(a),a?(this.express_content=JSON.parse(a),console.log(a),this.$refs.dialogSelectExpress.open()):e.goto("/pagesExt/order/logistics?express_com="+n+"&express_no="+o+"&type="+i)},contactCustomer:function(n){n.tel?t.makePhoneCall({phoneNumber:n.tel,fail:function(t){console.log("拨打电话失败",t)}}):this.$refs.popmsg.show({msg:"暂无联系方式"})}}};n.default=o}).call(this,e("df3c")["default"])}},[["5da5","common/runtime","common/vendor"]]]);