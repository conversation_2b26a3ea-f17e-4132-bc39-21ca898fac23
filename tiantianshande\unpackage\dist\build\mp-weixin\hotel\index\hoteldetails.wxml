<view><block wx:if="{{isload}}"><block><view class="navigation"><view class="navcontent" style="{{'margin-top:'+(navigationMenu.top+'px')+';'+('width:'+(navigationMenu.right+'px')+';')}}"><view class="header-location-top" style="{{'height:'+(navigationMenu.height+'px')+';'}}"><view data-event-opts="{{[['tap',[['goback',['$event']]]]]}}" class="header-back-but" bindtap="__e"><image src="{{pre_url+'/static/img/hotel/fanhui.png'}}"></image></view></view></view></view><view class="dp-banner"><swiper class="dp-banner-swiper" autoplay="{{true}}" indicator-dots="{{false}}" current="{{0}}" circular="{{true}}" interval="{{3000}}"><block wx:for="{{photolist}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><swiper-item><view data-event-opts="{{[['tap',[['viewPicture',['$0'],[[['photolist','',index]]]]]]]}}" bindtap="__e"><image class="dp-banner-swiper-img" src="{{item}}" mode="widthFix"></image></view></swiper-item></block></block></swiper></view><view class="position-view"><view class="banner-tab-view"><scroll-view style="width:auto;white-space:nowrap;margin-top:7.5rpx;" scroll-x="{{true}}"><block wx:for="{{photos}}" wx:for-item="item" wx:for-index="index"><block><view class="{{['tab-options-banner '+(index==pindex?'tab-options-banner-active':'')]}}" data-index="{{index}}" data-event-opts="{{[['tap',[['chanagePhoto',['$event']]]]]}}" bindtap="__e">{{item.name}}</view></block></block></scroll-view></view><view class="content-view" style="{{'background:'+('linear-gradient(180deg, '+$root.m0+' 0%, #FFFFFF 45px, #FFFFFF 63%, #FFFFFF 100%)')+';'}}"><view class="title-view">{{hotel.name}}</view><view class="hotel-nature"><block wx:if="{{hotel.hotellevel>0}}"><view class="star-view"><block wx:for="{{hotel.hotellevel+2}}" wx:for-item="item" wx:for-index="index"><image class="start" src="{{pre_url+'/static/img/star2native.png'}}"></image></block></view></block><block wx:if="{{nature!=null}}"><view class="hotspot-nature-text">{{nature}}</view></block></view><block wx:if="{{$root.g0}}"><view class="hotspot-view"><view class="hotspot-view-left"><block wx:for="{{hotel.tag}}" wx:for-item="item" wx:for-index="index"><view class="hotspot-options">{{''+item+''}}</view></block></view></view></block><view class="address-view"><view style="flex:1;"><view class="address-text">{{hotel.address}}</view><view class="address-traffic"><image src="{{pre_url+'/static/img/hotel/address.png'}}"></image>{{"距您"+hotel.juli+''}}</view></view><view class="fangshi-view"><view class="fagnshi-options" data-latitude="{{hotel.latitude}}" data-longitude="{{hotel.longitude}}" data-company="{{hotel.name}}" data-address="{{hotel.address}}" data-event-opts="{{[['tap',[['openLocation',['$event']]]]]}}" bindtap="__e"><image src="{{pre_url+'/static/img/hotel/dingwei.png'}}"></image><view>导航</view></view></view></view><view data-event-opts="{{[['tap',[['selectDate',['$event']]]]]}}" class="time-view flex flex-y-center flex-bt" bindtap="__e"><view class="time-options flex flex-y-center flex-bt"><view class="month-tetx">{{startDate}}</view><view class="day-tetx">{{startWeek+"入住"}}</view></view><view class="content-text"><view class="content-decorate left-c-d"></view>{{'共'+dayCount+'晚'}}<view class="content-decorate right-c-d"></view></view><view class="time-options flex flex-y-center flex-bt"><view class="month-tetx">{{endDate}}</view><view class="day-tetx">{{endWeek+"离店"}}</view></view></view></view><view class="screen-view"><view class="screen-view-left"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index"><block><view class="{{['screen-options ']}}" style="{{(item.m1?'background:rgba('+item.m2+',0.05);color:'+item.m3:'')}}" data-id="{{item.$orig.id}}" data-event-opts="{{[['tap',[['groupChange',['$event']]]]]}}" bindtap="__e">{{item.$orig.name}}</view></block></block></view></view><view class="hotels-list"><block wx:for="{{$root.l2}}" wx:for-item="item" wx:for-index="index"><block><view class="hotels-options" data-id="{{item.$orig.id}}" data-event-opts="{{[['tap',[['hotelDetail',['$event']]]]]}}" catchtap="__e"><view class="hotel-img"><block wx:if="{{roomstyle==1}}"><image src="{{item.$orig.pic}}"></image></block><block wx:else><image class="fangxing" src="{{item.$orig.pic}}"></image></block></view><view class="hotel-info"><view class="hotel-title">{{item.$orig.name}}</view><block wx:if="{{roomstyle==1}}"><view class="hotel-characteristic"><block wx:for="{{item.l1}}" wx:for-item="items" wx:for-index="indexs"><block><view class="characteristic-options" style="{{('background:rgba('+items.m4+',0.05);color:'+items.m5)}}">{{items.$orig}}</view></block></block></view></block><block wx:else><view class="hotel-characteristic"><block wx:if="{{item.$orig.bedxing!='不显示'}}"><view class="under_title"><view class="options-title">{{item.$orig.bedxing}}</view></view></block><view class="under_title"><view class="options-title">{{item.$orig.square+"m²"}}</view></view><block wx:if="{{item.$orig.ischuanghu!='不显示'}}"><view class="under_title"><view class="options-title">{{item.$orig.ischuanghu}}</view></view></block><block wx:if="{{item.$orig.breakfast!='不显示'}}"><view class="under_title"><view class="options-title">{{item.$orig.breakfast+"早餐"}}</view></view></block></view></block><view class="hotel-but-view"><view class="make-info"><block wx:if="{{item.$orig.daymoney}}"><view class="hotel-price" style="{{'color:'+(item.m6)+';'}}"><view class="hotel-price-num">{{item.$orig.daymoney+moneyunit}}</view><view>/晚起</view></view></block><block wx:else><view class="hotel-price" style="{{'color:'+(item.m7)+';'}}"><view>￥</view><view class="hotel-price-num">{{item.$orig.sell_price}}</view><view>起</view></view></block><block wx:if="{{roomstyle==1}}"><view class="hotel-text">{{item.$orig.sales+"人已预定 | 剩"+item.$orig.stock+text['间']+"可订"}}</view></block><block wx:else><view class="hotel-text">{{item.$orig.sales+"人已预定"}}</view></block></view><block wx:if="{{btnstyle==1}}"><block><block wx:if="{{item.$orig.stock>0&&!item.$orig.isbooking}}"><view class="hotel-make" style="{{('background:rgba('+item.m8+',0.8);color:#FFF')}}">预约</view></block><block wx:else><block wx:if="{{item.$orig.isbooking}}"><view class="hotel-make" style="background:#999999;color:#fff;">不可订</view></block><block wx:else><view class="hotel-make" style="background:#999999;color:#fff;">已满</view></block></block></block></block></view><block wx:if="{{btnstyle==2}}"><view><block wx:if="{{item.$orig.stock>0&&!item.$orig.isbooking}}"><view class="hotel-make-new" style="{{'border-color:'+(item.m9)+';'}}"><view class="make-new-view" style="{{('background:'+item.m10+'')}}">订</view><view class="make-new-view2" style="{{'color:'+(item.m11)+';'+('border-color:'+(item.m12)+';')}}">{{"剩"+item.$orig.stock+text['间']}}</view></view></block><block wx:else><block wx:if="{{item.$orig.isbooking}}"><view class="hotel-make-new" style="background:#999999;border:1px #999999 solid;color:#fff;"><view class="make-new-view" style="background:#999999;color:#fff;">不可订</view><view class="make-new-view2" style="background:#999999;border-bottom:1px #999999 solid;color:#fff;">{{"剩"+item.$orig.stock+text['间']}}</view></view></block><block wx:else><view class="hotel-make-new" style="background:#999999;border:1px #999999 solid;color:#fff;"><view class="make-new-view" style="background:#999999;color:#fff;">已满</view><view class="make-new-view2" style="background:#999999;border-bottom:1px #999999 solid;color:#fff;">{{"剩"+item.$orig.stock+text['间']}}</view></view></block></block></view></block></view></view></block></block></view><block wx:if="{{nomore}}"><nomore vue-id="26415892-1" text="没有更多了" bind:__l="__l"></nomore></block><block wx:if="{{nodata}}"><nodata vue-id="26415892-2" text="没有查找到相关房型" bind:__l="__l"></nodata></block><block wx:if="{{totalpagenum>pagenum}}"><view data-event-opts="{{[['tap',[['showmore',['$event']]]]]}}" class="hotel-more" style="{{'color:'+($root.m13)+';'}}" bindtap="__e">查看剩余房型<image src="{{pre_url+'/static/img/hotel/mroe-hotel.png'}}"></image></view></block><view class="work-order-view flex flex-col"><view class="work-order-title"><view class="work-order-title-left">配套设施</view></view><view class="facilities-view flex"><block wx:for="{{hotel.ptsheshi}}" wx:for-item="item" wx:for-index="index"><block><view class="options-facilities flex flex-col"><image src="{{item.icon}}"></image><view class="facilities-text">{{item.text}}</view></view></block></block></view></view><view class="work-order-view flex flex-col"><view class="work-order-title"><view class="work-order-title-left">订房须知</view></view><view class="required-reading-view"><parse vue-id="26415892-3" content="{{hotel.content}}" data-event-opts="{{[['^navigate',[['navigate']]]]}}" bind:navigate="__e" bind:__l="__l"></parse></view></view><block wx:if="{{hotel.comment==1}}"><view class="work-order-view flex flex-col"><view class="work-order-title"><view class="work-order-title-left">住客评价</view><view class="work-order-title-right" data-url="{{'/hotel/order/commentlist?hotelid='+hotel.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image src="{{pre_url+'/static/img/hotel/rightjiantou.png'}}"></image></view></view><block wx:if="{{$root.g1>0}}"><view class="score-view"><view class="score-content flex flex-y-center flex-bt"><view class="total-score-view flex flex-col"><view class="total-score-num flex flex-y-center" style="{{'color:'+($root.m14)+';'}}"><view class="total-num">{{hotel.comment_score}}</view><view class="tatal-describe">{{haoping}}</view></view></view></view><view class="evaluate-list-view flex flex-col"><block wx:for="{{$root.l3}}" wx:for-item="item" wx:for-index="index"><block><view class="text-options flex flex-col"><view class="user-info-view flex flex-y-center flex-bt"><view class="info-view flex flex-y-center"><image class="avater-view" src="{{item.$orig.headimg}}"></image><view class="name-view flex flex-col"><view class="name-text">{{item.$orig.nickname}}</view><view class="time-view">{{item.$orig.createtime}}</view></view></view><view class="scoring-view flex flex-xy-center" style="{{('background:rgba('+item.m15+',0.08);color:'+item.m16)}}"><view>{{item.$orig.score}}</view><view style="font-size:20rpx;margin-left:5rpx;">分</view></view></view><view class="evaluate-imgList flex flex-y-center"><block wx:for="{{item.$orig.content_pic}}" wx:for-item="pic" wx:for-index="index"><block><view class="img-options"><image src="{{pic}}"></image></view></block></block></view><view class="evaluate-value">{{''+item.$orig.content+''}}</view></view></block></block></view></view></block><block wx:else><view><nodata vue-id="26415892-4" text="没有查找到记录" bind:__l="__l"></nodata></view></block></view></block></view><uni-popup class="vue-ref" vue-id="26415892-5" id="popup" type="bottom" data-ref="popup" bind:__l="__l" vue-slots="{{['default']}}"><view class="popup__content" style="bottom:0;padding-top:0;padding-bottom:0;max-height:86vh;"><view data-event-opts="{{[['tap',[['popupdetailClose',['$event']]]]]}}" class="popup-close" bindtap="__e"><image src="{{pre_url+'/static/img/hotel/popupClose.png'}}"></image></view><scroll-view style="height:auto;max-height:90vh;" scroll-y="{{true}}"><view class="popup-banner-view" style="height:450rpx;"><swiper class="dp-banner-swiper" autoplay="{{true}}" indicator-dots="{{false}}" current="{{0}}" circular="{{true}}" interval="{{3000}}" data-event-opts="{{[['change',[['swiperChange',['$event']]]]]}}" bindchange="__e"><block wx:for="{{room.pics}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><swiper-item><view data-event-opts="{{[['tap',[['viewPicture',['$0'],[[['room.pics','',index]]]]]]]}}" bindtap="__e"><image class="dp-banner-swiper-img" src="{{item}}" mode="widthFix"></image></view></swiper-item></block></block></swiper><block wx:if="{{$root.g2}}"><view class="popup-numstatistics flex flex-xy-center">{{''+bannerindex+" / "+$root.g3+''}}</view></block></view><view class="hotel-details-view flex flex-col"><view class="hotel-title">{{room.name}}</view><view class="introduce-view flex"><block wx:if="{{room.bedxing!='不显示'}}"><view class="options-intro flex flex-y-center"><image src="{{pre_url+'/static/img/hotel/dachuang.png'}}"></image><view class="options-title">{{room.bedxing}}</view></view></block><view class="options-intro flex flex-y-center"><image src="{{pre_url+'/static/img/hotel/pingfang.png'}}"></image><view class="options-title">{{room.square+"m²"}}</view></view><view class="options-intro flex flex-y-center"><image src="{{pre_url+'/static/img/hotel/dachuang.png'}}"></image><view class="options-title">{{room.bedwidth+"米"}}</view></view><block wx:if="{{room.ischuanghu!='不显示'}}"><view class="options-intro flex flex-y-center"><image src="{{pre_url+'/static/img/hotel/youchuang.png'}}"></image><view class="options-title">{{room.ischuanghu}}</view></view></block><block wx:if="{{room.breakfast!='不显示'}}"><view class="options-intro flex flex-y-center"><image src="{{pre_url+'/static/img/hotel/zaocan.png'}}"></image><view class="options-title">{{room.breakfast+"早餐"}}</view></view></block></view><view class="other-view flex flex-y-center"><view class="other-title">特色</view><view class="other-text" style="white-space:pre-line;">{{room.tese}}</view></view><view class="other-view flex flex-y-center"><view class="other-title">房型详情</view><dp vue-id="{{('26415892-6')+','+('26415892-5')}}" pagecontent="{{pagecontent}}" bind:__l="__l"></dp></view></view><block wx:if="{{qystatus==1}}"><view class="hotel-equity-view flex flex-col"><view class="equity-title-view flex"><view class="equity-title">{{qyname}}</view></view><view class="equity-options flex flex-col"><parse vue-id="{{('26415892-7')+','+('26415892-5')}}" content="{{hotel.hotelquanyi}}" data-event-opts="{{[['^navigate',[['navigate']]]]}}" bind:navigate="__e" bind:__l="__l"></parse></view></view></block><block wx:if="{{fwstatus==1}}"><view class="hotel-equity-view flex flex-col"><view class="equity-title-view flex"><view class="equity-title">{{fwname}}</view></view><view class="equity-options flex flex-col"><parse vue-id="{{('26415892-8')+','+('26415892-5')}}" content="{{hotel.hotelfuwu}}" data-event-opts="{{[['^navigate',[['navigate']]]]}}" bind:navigate="__e" bind:__l="__l"></parse></view></view></block><view class="hotel-equity-view flex flex-col"><view class="equity-title-view flex"><view class="equity-title">费用明细</view></view><view class="cost-details flex flex-col"><view class="price-view flex flex-bt flex-y-center"><view class="price-text">押金（可退）</view><view class="price-num">{{"￥"+yajin}}</view></view><view class="price-view flex flex-bt flex-y-center"><view class="price-text">{{text['服务费']}}</view><view class="price-num">{{"￥"+service_money+"/天"}}</view></view><block wx:if="{{room.isdaymoney==1}}"><view class="price-view flex flex-bt flex-y-center"><view class="price-text">房费</view><view class="price-num">{{room.daymoney+moneyunit+"/晚"}}</view></view></block><block wx:else><view class="price-view flex flex-bt flex-y-center"><view class="price-text">房费</view><view class="price-num">{{"￥"+room.price+"/晚"}}</view></view></block><view class="price-view flex flex-y-center" style="justify-content:flex-end;margin-top:30rpx;margin-bottom:0rpx;align-items:center;padding-bottom:0rpx;"><view class="price-text" style="font-size:28rpx;margin-right:15rpx;">每日金额</view><block wx:if="{{room.isdaymoney==1}}"><view class="price-num flex flex-y-center"><view style="font-size:24rpx;font-weight:none;margin-top:5rpx;">￥</view><view style="font-size:44rpx;">{{totalprice+"+"+room.daymoney+moneyunit}}</view></view></block><block wx:else><view class="price-num flex flex-y-center"><view style="font-size:24rpx;font-weight:none;margin-top:5rpx;">￥</view><view style="font-size:44rpx;">{{totalprice}}</view></view></block></view><block wx:if="{{room.isdaymoney==1}}"><view class="tips">{{"未有旅居"+$root.m17+",支付"+room.price+"/晚或去获取旅居"+$root.m18}}</view></block></view></view><view style="height:260rpx;"></view></scroll-view><view class="popup-but-view flex flex-col" style="bottom:0;"><view class="price-statistics flex flex-y-center" style="{{'color:'+($root.m19)+';'}}"><view class="title-text">每日金额：</view><block wx:if="{{room.isdaymoney==1}}"><view class="price-text flex"><view style="font-size:22rpx;margin-top:8rpx;">￥</view><view style="font-weight:bold;font-size:36rpx;">{{totalprice+"+"+room.daymoney+moneyunit}}</view></view></block><block wx:else><view class="price-text flex"><view style="font-size:22rpx;margin-top:8rpx;">￥</view><view style="font-weight:bold;font-size:36rpx;">{{totalprice}}</view></view></block></view><block wx:if="{{minstock>0&&!room.isbooking}}"><view data-event-opts="{{[['tap',[['tobuy',['$event']]]]]}}" class="detail_but-class" style="{{('background: linear-gradient(90deg,rgba('+$root.m20+',1) 0%,rgba('+$root.m21+',1) 100%)')}}" bindtap="__e">预定</view></block><block wx:else><block wx:if="{{room.isbooking&&minstock>0}}"><view class="but-class" style="{{('background: #999999;color:#fff')}}">不可订</view></block><block wx:else><view class="but-class" style="{{('background: #999999;color:#fff')}}">已订满</view></block></block></view></view></uni-popup><block wx:if="{{calendarvisible}}"><view class="popup__container"><view data-event-opts="{{[['tap',[['handleClickMask',['$event']]]]]}}" class="popup__overlay" catchtap="__e"></view><view class="popup__modal"><view class="popup__title"><text class="popup__title-text">选择日期</text><image class="popup__close" style="width:56rpx;height:56rpx;top:20rpx;right:20rpx;" src="{{pre_url+'/static/img/hotel/popupClose2.png'}}" data-event-opts="{{[['tap',[['handleClickMask',['$event']]]]]}}" catchtap="__e"></image></view><view class="popup__content"><view class="reserve-time-view"><view class="time-view"><view class="time-title">入住</view><view class="flex flex-y-center" style="margin-top:15rpx;align-items:flex-end;"><view class="date-time">{{startDate}}</view><view class="time-title">{{startWeek}}</view></view></view><view class="statistics-view"><view class="statistics-date"><view class="content-decorate left-c-d"></view>{{'共'+dayCount+'晚'}}<view class="content-decorate right-c-d"></view></view><view class="color-line"></view></view><view class="time-view"><view class="time-title">离店</view><view class="flex flex-y-center" style="margin-top:15rpx;align-items:flex-end;"><view class="date-time">{{endDate}}</view><view class="time-title">{{endWeek}}</view></view></view></view><view class="calendar-view"><calendar vue-id="26415892-9" is-show="{{true}}" isFixed="{{false}}" showstock="1" text="{{text}}" dayroomprice="{{dayroomprice}}" start-date="{{starttime}}" end-date="{{endtime}}" mode="2" maxdays="{{maxdays}}" themeColor="{{$root.m22}}" between-end="{{maxenddate}}" data-event-opts="{{[['^callback',[['getDate']]]]}}" bind:callback="__e" bind:__l="__l"></calendar></view><view data-event-opts="{{[['tap',[['popupClose',['$event']]]]]}}" class="choose-but-class" style="{{('background: linear-gradient(90deg,rgba('+$root.m23+',1) 0%,rgba('+$root.m24+',1) 100%)')}}" bindtap="__e">{{'确认'+dayCount+"晚 可订"+minday+text['间']+''}}</view></view></view></view></block></block></block></view>