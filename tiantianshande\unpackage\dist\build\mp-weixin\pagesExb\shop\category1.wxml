<view><view class="navbox flex flex-wp"><block wx:for="{{data}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="nav_li" data-url="{{'/shopPackage/shop/prolist?'+(bid>0?'bid='+bid+'&cid2':'cid')+'='+item.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image src="{{item.pic}}" mode="aspectFill"></image><view class="title">{{item.name}}</view></view></block></view><block wx:if="{{loading}}"><loading vue-id="d2e5e5b4-1" bind:__l="__l"></loading></block><dp-tabbar vue-id="d2e5e5b4-2" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="d2e5e5b4-3" data-ref="popmsg" bind:__l="__l"></popmsg></view>