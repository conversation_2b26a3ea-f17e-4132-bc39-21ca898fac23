<view class="container"><block wx:if="{{isload}}"><block><view class="topsearch flex-y-center"><view class="f1 flex-y-center"><image class="img" src="{{pre_url+'/static/img/search_ico.png'}}"></image><input placeholder="输入姓名/手机号搜索" placeholder-style="font-size:24rpx;color:#C2C2C2" data-event-opts="{{[['confirm',[['searchConfirm',['$event']]]],['input',[['searchChange',['$event']]]]]}}" value="{{keyword}}" bindconfirm="__e" bindinput="__e"/></view></view><block wx:for="{{datalist}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="content" data-id="{{item.id}}" data-event-opts="{{[['tap',[['setdefault',['$0'],[[['datalist','',index]]]]]]]}}" catchtap="__e"><view class="f1"><text class="t1">{{item.name}}</text><text class="t2">{{item.tel}}</text><block wx:if="{{item.company}}"><text class="t2">{{item.company}}</text></block><text class="flex1"></text></view><view class="f2">{{item.area+" "+item.address}}</view></view></block><block wx:if="{{nodata}}"><nodata vue-id="08a40e49-1" bind:__l="__l"></nodata></block><view style="height:140rpx;"></view></block></block><block wx:if="{{loading}}"><loading vue-id="08a40e49-2" bind:__l="__l"></loading></block><dp-tabbar vue-id="08a40e49-3" opt="{{opt}}" data-event-opts="{{[['^getmenuindex',[['getmenuindex']]]]}}" bind:getmenuindex="__e" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="08a40e49-4" data-ref="popmsg" bind:__l="__l"></popmsg></view>