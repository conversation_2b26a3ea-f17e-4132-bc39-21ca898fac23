<view class="container"><block wx:if="{{isload}}"><block><view class="header-container"><view class="activity-info"><view class="activity-name">{{config.name}}</view><view class="activity-desc">{{config.copy_mode+"复制模式 · 财富奖励"+config.wealth_reward_amount+"元"}}</view></view></view><view class="stats-container"><view class="stats-item"><view class="stats-value" style="color:#007aff;">{{stats.total_positions||0}}</view><view class="stats-label">总点位数</view></view><view class="stats-item"><view class="stats-value" style="color:#007aff;">{{stats.active_positions||0}}</view><view class="stats-label">活跃点位</view></view><view class="stats-item"><view class="stats-value" style="color:#007aff;">{{"¥"+(stats.total_rewards||0)}}</view><view class="stats-label">累计奖励</view></view></view><block wx:if="{{config.id}}"><view class="test-actions"><view class="test-title">测试功能</view><view class="test-buttons"><view data-event-opts="{{[['tap',[['showAddPositionModal',['$event']]]]]}}" class="test-btn" style="background:linear-gradient(90deg,#007aff 0%,rgba(0,122,255,0.8) 100%);" bindtap="__e"><text>手动增加点位</text></view><view data-event-opts="{{[['tap',[['toPositionTree',['$event']]]]]}}" class="test-btn outline" bindtap="__e"><text>查看排单树</text></view></view></view></block><view class="content-container"><view class="tab-container"><view data-event-opts="{{[['tap',[['switchTab',['positions']]]]]}}" class="{{['tab-item',(currentTab=='positions')?'active':'']}}" bindtap="__e"><text>我的点位</text></view><view data-event-opts="{{[['tap',[['switchTab',['rewards']]]]]}}" class="{{['tab-item',(currentTab=='rewards')?'active':'']}}" bindtap="__e"><text>奖励记录</text></view></view><scroll-view class="list-box" scroll-y="true" data-event-opts="{{[['scrolltolower',[['scrolltolower',['$event']]]]]}}" bindscrolltolower="__e"><block wx:if="{{currentTab=='positions'}}"><view class="position-list"><block wx:for="{{positionList}}" wx:for-item="item" wx:for-index="index" wx:key="id"><view class="position-item"><view class="position-info"><view class="position-title"><text class="position-name">{{"点位 #"+item.position_number}}</text><text class="{{['position-status',item.status]}}">{{item.status_text}}</text></view><view class="position-details"><view class="detail-item"><text class="label">购买商品：</text><text class="value">{{item.product_name}}</text></view><view class="detail-item"><text class="label">购买金额：</text><text class="value" style="color:#007aff;">{{"¥"+item.amount}}</text></view><view class="detail-item"><text class="label">创建时间：</text><text class="value">{{item.create_time}}</text></view></view></view><block wx:if="{{item.can_claim&&config.wealth_reward_enabled}}"><view class="position-action"><view class="claim-btn" style="background:linear-gradient(90deg,#007aff 0%,rgba(0,122,255,0.8) 100%);" data-id="{{item.id}}" data-event-opts="{{[['tap',[['claimReward',['$event']]]]]}}" bindtap="__e">领取奖励</view></view></block></view></block></view></block><block wx:if="{{currentTab=='rewards'}}"><view class="reward-list"><block wx:for="{{rewardList}}" wx:for-item="item" wx:for-index="index" wx:key="id"><view class="reward-item"><view class="reward-info"><view class="reward-title"><text class="reward-type">{{item.reward_type_text}}</text><text class="reward-amount" style="color:#007aff;">{{"+¥"+item.amount}}</text></view><view class="reward-details"><view class="detail-item"><text class="label">奖励说明：</text><text class="value">{{item.remark}}</text></view><view class="detail-item"><text class="label">获得时间：</text><text class="value">{{item.create_time}}</text></view></view></view></view></block></view></block><block wx:if="{{nomore}}"><nomore vue-id="2f3f863f-1" text="没有更多数据了" bind:__l="__l"></nomore></block><block wx:if="{{nodata}}"><nodata vue-id="2f3f863f-2" text="暂无数据" bind:__l="__l"></nodata></block><view style="width:100%;height:100rpx;"></view></scroll-view></view><block wx:if="{{showAddModal}}"><view data-event-opts="{{[['tap',[['hideAddPositionModal',['$event']]]]]}}" class="modal-overlay" bindtap="__e"><view data-event-opts="{{[['tap',[['',['$event']]]]]}}" class="modal-content" catchtap="__e"><view class="modal-header"><text class="modal-title">手动增加点位</text><text data-event-opts="{{[['tap',[['hideAddPositionModal',['$event']]]]]}}" class="modal-close" bindtap="__e">×</text></view><view class="modal-body"><view class="form-item"><text class="form-label">父点位ID（可选）：</text><input class="form-input" type="number" placeholder="不填则创建根点位" data-event-opts="{{[['input',[['__set_model',['$0','parent_id','$event',[]],['addForm']]]]]}}" value="{{addForm.parent_id}}" bindinput="__e"/></view><view class="form-item"><text class="form-label">订单ID（可选）：</text><input class="form-input" type="number" placeholder="关联的订单ID" data-event-opts="{{[['input',[['__set_model',['$0','order_id','$event',[]],['addForm']]]]]}}" value="{{addForm.order_id}}" bindinput="__e"/></view><view class="form-tips"><text>• 不填父点位ID将创建根点位</text><text>• 系统会自动计算层级和位置</text><text>• 财富点位根据配置自动判断</text></view></view><view class="modal-footer"><view data-event-opts="{{[['tap',[['hideAddPositionModal',['$event']]]]]}}" class="modal-btn cancel" bindtap="__e">取消</view><view data-event-opts="{{[['tap',[['confirmAddPosition',['$event']]]]]}}" class="modal-btn confirm" style="background:#007aff;" bindtap="__e">确认添加</view></view></view></view></block></block></block><block wx:if="{{loading}}"><loading vue-id="2f3f863f-3" loadstyle="left:62.5%" bind:__l="__l"></loading></block><dp-tabbar vue-id="2f3f863f-4" opt="{{opt}}" data-event-opts="{{[['^getmenuindex',[['getmenuindex']]]]}}" bind:getmenuindex="__e" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="2f3f863f-5" data-ref="popmsg" bind:__l="__l"></popmsg></view>