<view class="dp-search" style="{{(containerStyle)}}"><block wx:if="{{params.position>0}}"><view data-event-opts="{{[['tap',[['selectCity']]]]}}" class="city-selector" bindtap="__e"><view class="city-text" style="{{'color:'+(params.area_color||'#333333')+';'}}">{{city}}</view><image class="city-arrow" src="../../static/img/arrowdown.png"></image></view></block><view class="search-container"><block wx:if="{{!params.openmore||params.openmore<=0}}"><block><view class="dp-search-search" style="{{'border-color:'+(params.bordercolor)+';'+('border-radius:'+(params.borderradius+'px')+';')+('width:'+(isSearchBtnOutside?'calc(100% - '+searchOffset+'rpx)':'100%')+';')+('background-color:'+(searchBoxBgColor)+';')}}"><view class="dp-search-search-f1"></view><view class="dp-search-search-f2"><input class="dp-search-search-input" style="{{(params.color?'color:'+params.color:'')}}" data-url="{{params.hrefurl}}" name="keyword" placeholder="{{params.placeholder||'输入关键字在店铺内搜索'}}" placeholder-style="color:#aaa;font-size:28rpx" data-event-opts="{{[['confirm',[['searchgoto',['$event']]]],['input',[['inputKeyword',['$event']]]]]}}" bindconfirm="__e" bindinput="__e"/></view><block wx:if="{{params.search_btn=='1'&&params.btn_position=='inside'}}"><view class="dp-search-btn-inside" style="{{'background-color:'+(params.btn_color||'#f0f0f0')+';'+('color:'+(params.btn_text_color||'#333333')+';')+('border-radius:'+((params.btn_radius||0)+'px')+';')+('border:'+('1px solid '+(params.btn_border_color||'#e6e6e6'))+';')}}" data-url="{{params.hrefurl}}" data-event-opts="{{[['tap',[['searchgoto',['$event']]]]]}}" bindtap="__e"><block wx:if="{{params.btn_type!='icon'}}"><text>{{params.btn_text||'搜索'}}</text></block><block wx:else><image class="dp-search-btn-icon-inside" src="{{btnIcon}}" mode="aspectFit"></image></block></view></block><block wx:if="{{params.image_search==1}}"><view class="dp-search-search-f3" style="{{('background-image:url('+pre_url+'/static/img/camera.png)')}}" data-url="/pagesExt/shop/imgsearch" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"></view></block></view><block wx:if="{{params.search_btn=='1'&&params.btn_position!='inside'}}"><view class="dp-search-btn-wrap"><view class="dp-search-btn" style="{{'background-color:'+(params.btn_color||'#f0f0f0')+';'+('color:'+(params.btn_text_color||'#333333')+';')+('border-radius:'+((params.btn_radius||0)+'px')+';')+('border:'+('1px solid '+(params.btn_border_color||'#e6e6e6'))+';')+('width:'+(btnSizeWidth+'rpx')+';')}}" data-url="{{params.hrefurl}}" data-event-opts="{{[['tap',[['searchgoto',['$event']]]]]}}" bindtap="__e"><block wx:if="{{params.btn_type!='icon'}}"><text>{{params.btn_text||'搜索'}}</text></block><block wx:else><image class="dp-search-btn-icon" src="{{btnIcon}}" mode="aspectFit"></image></block></view></view></block></block></block><block wx:else><block><view style="display:flex;width:100%;"><view style="width:140rpx;overflow:hidden;"><picker style="{{('line-height: 72rpx;background-color:'+searchBoxBgColor+';padding-left: 10rpx;overflow: hidden;border: 0;')}}" value="{{data_index}}" range="{{data}}" range-key="title1" data-event-opts="{{[['change',[['dataChange',['$event']]]]]}}" bindchange="__e"><view style="width:80rpx;white-space:nowrap;overflow:hidden;float:left;">{{data_name}}</view><image style="width:26rpx;height:26rpx;float:right;margin-top:26rpx;" src="/static/img/hdsanjiao.png"></image></picker></view><view class="dp-search-search" style="{{'border-color:'+(params.bordercolor)+';'+('border-radius:'+(params.borderradius+'px')+';')+('width:'+(isSearchBtnOutside?'calc(100% - 140rpx - '+searchOffset+'rpx)':'calc(100% - 140rpx)')+';')+('background-color:'+(searchBoxBgColor)+';')}}"><view class="dp-search-search-f1"></view><view class="dp-search-search-f2"><input class="dp-search-search-input" style="{{(params.color?'color:'+params.color:'')}}" data-url="{{data_hrefurl}}" name="keyword" placeholder="{{data_placeholder?data_placeholder:params.placeholder||'输入关键字在店铺内搜索'}}" placeholder-style="color:#aaa;font-size:28rpx" data-event-opts="{{[['confirm',[['searchgoto',['$event']]]],['input',[['inputKeyword',['$event']]]]]}}" bindconfirm="__e" bindinput="__e"/></view><block wx:if="{{params.search_btn=='1'&&params.btn_position=='inside'}}"><view class="dp-search-btn-inside" style="{{'background-color:'+(params.btn_color||'#f0f0f0')+';'+('color:'+(params.btn_text_color||'#333333')+';')+('border-radius:'+((params.btn_radius||0)+'px')+';')+('border:'+('1px solid '+(params.btn_border_color||'#e6e6e6'))+';')}}" data-url="{{data_hrefurl}}" data-event-opts="{{[['tap',[['searchgoto',['$event']]]]]}}" bindtap="__e"><block wx:if="{{params.btn_type!='icon'}}"><text>{{params.btn_text||'搜索'}}</text></block><block wx:else><image class="dp-search-btn-icon-inside" src="{{btnIcon}}" mode="aspectFit"></image></block></view></block><block wx:if="{{params.image_search==1}}"><view class="dp-search-search-f3" style="{{('background-image:url('+pre_url+'/static/img/camera.png)')}}" data-url="{{data_hrefurl}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"></view></block></view><block wx:if="{{params.search_btn=='1'&&params.btn_position!='inside'}}"><view class="dp-search-btn-wrap"><view class="dp-search-btn" style="{{'background-color:'+(params.btn_color||'#f0f0f0')+';'+('color:'+(params.btn_text_color||'#333333')+';')+('border-radius:'+((params.btn_radius||0)+'px')+';')+('border:'+('1px solid '+(params.btn_border_color||'#e6e6e6'))+';')+('width:'+(btnSizeWidth+'rpx')+';')}}" data-url="{{data_hrefurl}}" data-event-opts="{{[['tap',[['searchgoto',['$event']]]]]}}" bindtap="__e"><block wx:if="{{params.btn_type!='icon'}}"><text>{{params.btn_text||'搜索'}}</text></block><block wx:else><image class="dp-search-btn-icon" src="{{btnIcon}}" mode="aspectFit"></image></block></view></view></block></view></block></block></view></view>