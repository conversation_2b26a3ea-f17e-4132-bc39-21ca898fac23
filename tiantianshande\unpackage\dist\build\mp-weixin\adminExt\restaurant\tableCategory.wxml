<view><view class="container" id="datalist"><view><block wx:for="{{datalist}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="info-item"><view class="t1"><view class="info-title">{{item.name}}<block wx:if="{{item.status==0}}"><text style="color:#DBAA83;">(隐藏)</text></block></view><view class="info-item-child"><view>{{"预定费："+item.booking_fee}}</view><view>{{"最低消费："+item.limit_fee}}</view><view>{{"座位数："+item.seat}}</view></view></view><image class="t3" data-url="{{'tableCategoryEdit?id='+item.id}}" src="{{pre_url+'/static/img/arrowright.png'}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"></image></view></block></view><view class="bottom-view"><view class="button" data-url="tableCategoryEdit" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">添加分类</view></view><block wx:if="{{nomore}}"><nomore vue-id="03b336e6-1" bind:__l="__l"></nomore></block><block wx:if="{{nodata}}"><nodata vue-id="03b336e6-2" bind:__l="__l"></nodata></block></view></view>