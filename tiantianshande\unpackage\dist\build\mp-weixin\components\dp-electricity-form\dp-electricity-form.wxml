<view class="dp-form" style="{{'color:'+(params.color)+';'+('background-color:'+(params.bgcolor)+';')+('margin:'+(params.margin_y*2.2+'rpx '+params.margin_x*2.2+'rpx')+';')+('padding:'+(params.padding_y*2.2+'rpx '+params.padding_x*2.2+'rpx')+';')+('font-size:'+(params.fontsize*2+'rpx')+';')}}"><form data-formcontent="{{data.content}}" data-tourl="{{params.hrefurl}}" data-formid="{{data.id}}" data-event-opts="{{[['submit',[['editorFormSubmit',['$event']]]]]}}" bindsubmit="__e"><view style="display:none;">{{test}}</view><block wx:for="{{$root.l1}}" wx:for-item="item" wx:for-index="idx" wx:key="id"><view class="{{[params.style==1?'dp-form-item':'dp-form-item2']}}" style="{{'border-color:'+(params.linecolor)+';'}}"><block wx:if="{{item.$orig.key=='separate'}}"><block><view class="dp-form-separate">{{item.$orig.val1}}</view></block></block><block wx:if="{{item.$orig.key!='separate'}}"><view class="label">{{item.$orig.val1}}<block wx:if="{{item.$orig.val3==1&&params.showmuststar}}"><text style="color:red;">*</text></block></view></block><block wx:if="{{item.$orig.key=='input'}}"><block><block wx:if="{{params.style==1}}"><block><block wx:if="{{item.$orig.val5}}"><text style="margin-right:10rpx;">{{item.$orig.val5}}</text></block><block wx:if="{{item.$orig.val4==2&&item.$orig.val6==1&&(platform=='mp'||platform=='wx')}}"><block><input class="input disabled" style="{{'border-color:'+(params.inputbordercolor)+';'+('background-color:'+('#efefef')+';')}}" type="{{item.$orig.val4==1||item.$orig.val4==2?'digit':'text'}}" disabled="true" name="{{'form'+idx}}" placeholder="{{item.$orig.val2}}" placeholder-style="font-size:28rpx" data-formidx="{{'form'+idx}}" data-event-opts="{{[['input',[['setfield',['$event']]]]]}}" value="{{formdata['form'+idx]}}" bindinput="__e"/><button class="authtel" style="{{'background-color:'+(params.btnbgcolor)+';'+('color:'+(params.btncolor)+';')}}" open-type="getPhoneNumber" type="primary" data-idx="{{idx}}" data-event-opts="{{[['getphonenumber',[['getPhoneNumber',['$event']]]]]}}" bindgetphonenumber="__e">获取手机号码</button></block></block><block wx:else><block><input class="input" style="{{'border-color:'+(params.inputbordercolor)+';'+('background-color:'+(params.inputbgcolor)+';')}}" type="{{item.$orig.val4==1||item.$orig.val4==2?'digit':'text'}}" readonly="{{true}}" name="{{'form'+idx}}" placeholder="{{item.$orig.val2}}" placeholder-style="font-size:28rpx" data-formidx="{{'form'+idx}}" data-event-opts="{{[['input',[['setfield',['$event']]]]]}}" value="{{formdata['form'+idx]}}" bindinput="__e"/></block></block></block></block><block wx:if="{{params.style==2}}"><view class="value"><block wx:if="{{item.$orig.val5}}"><text style="margin-right:10rpx;">{{item.$orig.val5}}</text></block><block wx:if="{{item.$orig.val4==2&&item.$orig.val6==1&&(platform=='mp'||platform=='wx')}}"><block><input class="input disabled" style="{{'border-color:'+(params.inputbordercolor)+';'+('background-color:'+('#efefef')+';')}}" type="{{item.$orig.val4==1||item.$orig.val4==2?'digit':'text'}}" disabled="true" name="{{'form'+idx}}" placeholder="{{item.$orig.val2}}" placeholder-style="font-size:28rpx" data-formidx="{{'form'+idx}}" data-event-opts="{{[['input',[['setfield',['$event']]]]]}}" value="{{formdata['form'+idx]}}" bindinput="__e"/><button class="authtel" style="{{'background-color:'+(params.btnbgcolor)+';'+('color:'+(params.btncolor)+';')}}" open-type="getPhoneNumber" type="primary" data-idx="{{idx}}" data-event-opts="{{[['getphonenumber',[['getPhoneNumber',['$event']]]]]}}" bindgetphonenumber="__e">获取手机号码</button></block></block><block wx:else><block><input class="input" style="{{'border-color:'+(params.inputbordercolor)+';'+('background-color:'+(params.inputbgcolor)+';')}}" type="{{item.$orig.val4==1||item.$orig.val4==2?'digit':'text'}}" readonly="{{true}}" name="{{'form'+idx}}" placeholder="{{item.$orig.val2}}" placeholder-style="font-size:28rpx" data-formidx="{{'form'+idx}}" data-event-opts="{{[['input',[['setfield',['$event']]]]]}}" value="{{formdata['form'+idx]}}" bindinput="__e"/></block></block></view></block></block></block><block wx:if="{{item.$orig.key=='textarea'}}"><block><textarea class="textarea" style="{{'border-color:'+(params.inputbordercolor)+';'+('background-color:'+(params.inputbgcolor)+';')}}" name="{{'form'+idx}}" placeholder="{{item.$orig.val2}}" placeholder-style="font-size:28rpx" data-formidx="{{'form'+idx}}" data-event-opts="{{[['input',[['setfield',['$event']]]]]}}" value="{{formdata['form'+idx]}}" bindinput="__e"></textarea></block></block><block wx:if="{{item.$orig.key=='radio'}}"><block><radio-group class="flex" style="flex-wrap:wrap;" name="{{'form'+idx}}" data-formidx="{{'form'+idx}}" data-event-opts="{{[['change',[['setfield',['$event']]]]]}}" bindchange="__e"><block wx:for="{{item.$orig.val2}}" wx:for-item="item1" wx:for-index="idx1" wx:key="id"><label class="flex-y-center"><radio class="radio" value="{{item1}}" checked="{{formdata['form'+idx]&&formdata['form'+idx]==item1?true:false}}"></radio>{{item1+''}}</label></block></radio-group></block></block><block wx:if="{{item.$orig.key=='checkbox'}}"><block><checkbox-group class="flex" style="flex-wrap:wrap;" name="{{'form'+idx}}" data-formidx="{{'form'+idx}}" data-event-opts="{{[['change',[['setfield',['$event']]]]]}}" bindchange="__e"><block wx:for="{{item.l0}}" wx:for-item="item1" wx:for-index="idx1" wx:key="id"><label class="flex-y-center"><checkbox class="checkbox" value="{{item1.$orig}}" checked="{{item1.m0?true:false}}"></checkbox>{{item1.$orig+''}}</label></block></checkbox-group></block></block><block wx:if="{{item.$orig.key=='selector'}}"><block><picker class="picker" mode="selector" name="{{'form'+idx}}" value="{{editorFormdata[idx]}}" range="{{item.$orig.val2}}" data-idx="{{idx}}" data-formidx="{{'form'+idx}}" data-event-opts="{{[['change',[['editorBindPickerChange',['$event']]]]]}}" bindchange="__e"><block wx:if="{{editorFormdata[idx]||editorFormdata[idx]===0}}"><view>{{''+item.$orig.val2[editorFormdata[idx]]}}</view></block><block wx:else><view>请选择</view></block></picker></block></block><block wx:if="{{item.$orig.key=='time'}}"><block><picker class="picker" mode="time" name="{{'form'+idx}}" value="{{formdata['form'+idx]}}" start="{{item.$orig.val2[0]}}" end="{{item.$orig.val2[1]}}" range="{{item.$orig.val2}}" data-idx="{{idx}}" data-formidx="{{'form'+idx}}" data-event-opts="{{[['change',[['editorBindPickerChange',['$event']]]]]}}" bindchange="__e"><block wx:if="{{editorFormdata[idx]}}"><view>{{editorFormdata[idx]}}</view></block><block wx:else><view>请选择</view></block></picker></block></block><block wx:if="{{item.$orig.key=='date'}}"><block><picker class="picker" mode="date" name="{{'form'+idx}}" value="{{formdata['form'+idx]}}" start="{{item.$orig.val2[0]}}" end="{{item.$orig.val2[1]}}" range="{{item.$orig.val2}}" data-idx="{{idx}}" data-formidx="{{'form'+idx}}" data-event-opts="{{[['change',[['editorBindPickerChange',['$event']]]]]}}" bindchange="__e"><block wx:if="{{editorFormdata[idx]}}"><view>{{editorFormdata[idx]}}</view></block><block wx:else><view>请选择</view></block></picker></block></block><block wx:if="{{item.$orig.key=='region'}}"><block><uni-data-picker vue-id="{{'53687a7e-1-'+idx}}" localdata="{{items}}" popup-title="请选择省市区" placeholder="{{formdata['form'+idx]||'请选择省市区'}}" data-formidx="{{'form'+idx}}" data-event-opts="{{[['^change',[['onchange']]]]}}" bind:change="__e" bind:__l="__l"></uni-data-picker><input style="display:none;" type="text" name="{{'form'+idx}}" value="{{regiondata?regiondata:formdata['form'+idx]}}"/></block></block><block wx:if="{{item.$orig.key=='upload'}}"><block><input style="display:none;" type="text" name="{{'form'+idx}}" value="{{editorFormdata[idx]}}"/><view class="flex" style="flex-wrap:wrap;padding-top:20rpx;"><block wx:if="{{editorFormdata[idx]}}"><view class="dp-form-imgbox"><view class="dp-form-imgbox-close" data-idx="{{idx}}" data-formidx="{{'form'+idx}}" data-event-opts="{{[['tap',[['removeimg',['$event']]]]]}}" bindtap="__e"><image class="image" src="/static/img/ico-del.png"></image></view><view class="dp-form-imgbox-img"><image class="image" src="{{editorFormdata[idx]}}" data-url="{{editorFormdata[idx]}}" mode="widthFix" data-idx="{{idx}}" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image></view></view></block><block wx:else><view class="dp-form-uploadbtn" style="{{'background:'+('url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 60rpx')+';'+('background-size:'+('80rpx 80rpx')+';')+('background-color:'+('#F3F3F3')+';')}}" data-idx="{{idx}}" data-formidx="{{'form'+idx}}" data-event-opts="{{[['tap',[['editorChooseImage',['$event']]]]]}}" bindtap="__e"></view></block></view></block></block><block wx:if="{{item.$orig.key=='upload_file'}}"><block><input style="display:none;" type="text" name="{{'form'+idx}}" value="{{editorFormdata[idx]}}"/><view class="flex" style="flex-wrap:wrap;padding-top:20rpx;"><block wx:if="{{editorFormdata[idx]}}"><view class="dp-form-imgbox"><view class="dp-form-imgbox-close" data-idx="{{idx}}" data-formidx="{{'form'+idx}}" data-event-opts="{{[['tap',[['removeimg',['$event']]]]]}}" bindtap="__e"><image class="image" src="/static/img/ico-del.png"></image></view><view style="overflow:hidden;white-space:pre-wrap;word-wrap:break-word;color:#4786BC;width:530rpx;" data-file="{{editorFormdata[idx]}}" data-event-opts="{{[['tap',[['download',['$event']]]]]}}" bindtap="__e">{{''+editorFormdata[idx]+''}}</view></view></block><block wx:else><view class="dp-form-uploadbtn" style="{{'background:'+('url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 60rpx')+';'+('background-size:'+('80rpx 80rpx')+';')+('background-color:'+('#F3F3F3')+';')}}" data-idx="{{idx}}" data-formidx="{{'form'+idx}}" data-event-opts="{{[['tap',[['chooseFile',['$event']]]]]}}" bindtap="__e"></view></block></view></block></block></view></block><block wx:if="{{data.payset==1}}"><block><block wx:if="{{!data.is_other_fee||data.is_other_fee==0}}"><view class="dp-form-item"><text class="label">支付金额：</text><block wx:if="{{data.priceedit==1}}"><input class="input" type="text" name="price" data-formidx="price" data-event-opts="{{[['input',[['setfield',['$event']]]]]}}" value="{{data.price}}" bindinput="__e"/></block><block wx:if="{{data.priceedit==0}}"><text>{{data.price}}</text></block><text style="padding-left:10rpx;">元</text></view></block><block wx:if="{{data.is_other_fee==1}}"><block><view class="dp-form-item" style="border:none;"><view class="dp-form-label">费用明细</view><view class="dp-form-feelist"><checkbox-group name="feelist"><block wx:for="{{$root.l2}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="dp-fee-item"><view class="dp-fee-name">{{item.$orig.name}}</view><view class="dp-fee-money">{{"￥"+item.$orig.money}}</view><view class="dp-fee-check"><checkbox style="transform:scale(0.7);" data-index="{{index}}" value="{{''+index}}" checked="{{item.$orig.checked?true:false}}" color="{{item.m1}}" data-event-opts="{{[['tap',[['feeChange',['$event']]]]]}}" bindtap="__e"></checkbox></view></view></block></checkbox-group><view class="dp-fee-item sum"><view class="dp-fee-name">合计</view><view class="dp-fee-money">￥<text>{{feetotal}}</text></view><view class="dp-fee-check"></view></view></view></view></block></block></block></block><block wx:if="{{data!=''}}"><button class="dp-form-btn" style="{{'background-color:'+(params.btnbgcolor)+';'+('border:'+('1px solid '+params.btnbordercolor)+';')+('font-size:'+(params.btnfontsize*2+'rpx')+';')+('color:'+(params.btncolor)+';')+('width:'+(params.btnwidth*2.2+'rpx')+';')+('height:'+(params.btnheight*2.2+'rpx')+';')+('line-height:'+(params.btnheight*2.2+'rpx')+';')+('border-radius:'+(params.btnradius*2.2+'rpx')+';')}}" data-formcontent="{{data.content}}" data-tourl="{{params.hrefurl}}" data-formid="{{data.id}}" data-event-opts="{{[['tap',[['editorFormSubmit',['$event']]]]]}}" bindtap="__e">{{params.btntext}}</button></block></form></view>