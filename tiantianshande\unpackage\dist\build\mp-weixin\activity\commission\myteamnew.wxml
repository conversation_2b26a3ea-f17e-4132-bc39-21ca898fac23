<view class="container"><block wx:if="{{isload}}"><block><block wx:if="{{userlevel&&userlevel.can_agent==2}}"><dd-tab vue-id="7220a41e-1" itemdata="{{[$root.m0,$root.m1]}}" itemst="{{['1','2','3']}}" st="{{st}}" isfixed="{{false}}" data-event-opts="{{[['^changetab',[['changetab']]]]}}" bind:changetab="__e" bind:__l="__l"></dd-tab></block><block wx:if="{{userlevel&&userlevel.can_agent==3}}"><dd-tab vue-id="7220a41e-2" itemdata="{{[$root.m2,$root.m3,$root.m4]}}" itemst="{{['1','2','3']}}" st="{{st}}" isfixed="{{false}}" data-event-opts="{{[['^changetab',[['changetab']]]]}}" bind:changetab="__e" bind:__l="__l"></dd-tab></block><block wx:if="{{team_settings&&team_settings.show_statistics==1}}"><view class="time-filter"><view class="filter-item"><text class="filter-label">开始：</text><picker mode="date" value="{{start_time}}" data-event-opts="{{[['change',[['onStartDateChange',['$event']]]]]}}" bindchange="__e"><view class="date-picker">{{start_time||'请选择'}}</view></picker></view><view class="filter-item"><text class="filter-label">结束：</text><picker mode="date" value="{{end_time}}" data-event-opts="{{[['change',[['onEndDateChange',['$event']]]]]}}" bindchange="__e"><view class="date-picker">{{end_time||'请选择'}}</view></picker></view><view data-event-opts="{{[['tap',[['applyTimeFilter',['$event']]]]]}}" class="filter-btn" style="{{'background:'+($root.m5)+';'+('color:'+('#fff')+';')}}" bindtap="__e">筛选</view><view data-event-opts="{{[['tap',[['resetTimeFilter',['$event']]]]]}}" class="filter-btn reset-btn" bindtap="__e">重置</view></view></block><block wx:if="{{team_settings&&team_settings.show_statistics==1&&statistics}}"><view class="statistics-bar" style="{{'background:'+('linear-gradient(45deg,'+$root.m6+' 0%,rgba('+$root.m7+',0.8) 100%)')+';'}}"><view class="stat-item"><text class="stat-value">{{statistics.total_count||0}}</text><text class="stat-label">总人数</text></view><view class="stat-item"><text class="stat-value">{{"¥"+(statistics.total_amount||'0.00')}}</text><text class="stat-label">总下单金额</text></view></view></block><view class="topsearch flex-y-center"><view class="f1 flex-y-center"><image class="img" src="/static/img/search_ico.png"></image><input placeholder="输入昵称/姓名/手机号搜索" placeholder-style="font-size:24rpx;color:#C2C2C2" data-event-opts="{{[['confirm',[['searchConfirm',['$event']]]],['input',[['searchChange',['$event']]]]]}}" value="{{keyword}}" bindconfirm="__e" bindinput="__e"/></view></view><block wx:if="{{loading&&pagenum===1}}"><view class="content"><view class="member-list"><block wx:for="{{3}}" wx:for-item="i" wx:for-index="__i0__" wx:key="*this"><view class="skeleton-item"><view class="skeleton-header"><view class="skeleton-avatar"></view><view class="skeleton-info"><view class="skeleton-line" style="width:40%;"></view><view class="skeleton-line" style="width:60%;margin-top:12rpx;"></view></view></view><view class="skeleton-body"><view class="skeleton-line" style="width:90%;"></view><view class="skeleton-line" style="width:70%;"></view></view></view></block></view></view></block><block wx:if="{{$root.g0}}"><view class="content"><view class="member-list"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['toggleExpand',[index]]]]]}}" class="member-item" style="{{'animation-delay:'+(index*0.05+'s')+';'}}" bindtap="__e"><view class="item-header" style="{{'background:'+('linear-gradient(135deg, '+item.m8+' 0%, '+item.m9+' 100%)')+';'}}"><view class="user-info"><image class="avatar" src="{{item.$orig.headimg}}"></image><view class="info-text"><view class="nickname-line"><text class="nickname">{{item.$orig.nickname}}</text><block wx:if="{{team_settings&&team_settings.show_relation_chart==1}}"><view class="relation-chart-btn" data-id="{{item.$orig.id}}" data-event-opts="{{[['tap',[['goToRelationChart',['$event']]]]]}}" catchtap="__e">关系图</view></block></view><view class="join-time">{{item.$orig.createtime+" 加入"}}</view></view></view><view class="right-info"><view class="user-level-badge">{{item.$orig.levelname}}</view><image class="{{['arrow-icon',(expandedIndex===index)?'expanded':'']}}" src="/static/img/arrow-down.png"></image></view></view><view class="{{['expandable-content',(expandedIndex===index)?'expanded':'']}}"><view class="item-body"><view class="info-grid"><block wx:if="{{team_settings&&team_settings.show_other_commission==1}}"><view class="info-cell"><text class="info-label">{{"贡献"+item.m10}}</text><text class="info-value highlight" style="{{'color:'+(item.m11)+';'}}">{{"+"+item.$orig.commission}}</text></view></block><block wx:if="{{team_settings&&team_settings.show_consume_amount==1}}"><view class="info-cell"><text class="info-label">消费金额</text><text class="info-value highlight" style="{{'color:'+(item.m12)+';'}}">{{"¥"+(item.$orig.consume_amount||'0.00')}}</text></view></block><block wx:if="{{team_settings&&team_settings.show_team_performance==1}}"><view class="info-cell"><text class="info-label">团队业绩</text><text class="info-value">{{item.$orig.tuanduiyeji}}</text></view></block><block wx:if="{{team_settings&&team_settings.show_direct_count==1}}"><view class="info-cell"><text class="info-label">直推人数</text><text class="info-value">{{item.$orig.direct_count||item.$orig.downcount}}</text></view></block><block wx:if="{{team_settings&&team_settings.show_member_count==1}}"><view class="info-cell"><text class="info-label">团队人数</text><text class="info-value">{{item.$orig.downcount}}</text></view></block><block wx:if="{{item.$orig.tel}}"><view class="info-cell"><text class="info-label">手机号</text><text class="info-value">{{item.$orig.tel}}</text></view></block><block wx:if="{{item.$orig.has_original_recommender==1&&item.$orig.original_recommender}}"><view class="info-cell"><text class="info-label">原推荐人</text><text class="info-value">{{item.$orig.original_recommender.nickname}}</text></view></block></view></view><block wx:if="{{userlevel&&(userlevel.team_givemoney==1||userlevel.team_givescore==1||userlevel.team_levelup==1||userlevel.team_daikexiadan==1)}}"><view class="item-footer"><block wx:if="{{userlevel&&userlevel.team_givemoney==1}}"><view class="action-btn" style="{{'color:'+(item.m13)+';'+('border-color:'+(item.m14)+';')}}" data-id="{{item.$orig.id}}" data-event-opts="{{[['tap',[['givemoneyshow',['$event']]]]]}}" catchtap="__e">{{"转"+item.m15}}</view></block><block wx:if="{{userlevel&&userlevel.team_givescore==1}}"><view class="action-btn" style="{{'color:'+(item.m16)+';'+('border-color:'+(item.m17)+';')}}" data-id="{{item.$orig.id}}" data-event-opts="{{[['tap',[['givescoreshow',['$event']]]]]}}" catchtap="__e">{{"转"+item.m18}}</view></block><block wx:if="{{userlevel&&userlevel.team_levelup==1}}"><view class="action-btn" style="{{'color:'+(item.m19)+';'+('border-color:'+(item.m20)+';')}}" data-id="{{item.$orig.id}}" data-levelid="{{item.$orig.levelid}}" data-levelsort="{{item.$orig.levelsort}}" data-event-opts="{{[['tap',[['showDialog',['$event']]]]]}}" catchtap="__e">升级</view></block><block wx:if="{{userlevel&&userlevel.team_daikexiadan==1}}"><view class="action-btn" style="{{'color:'+(item.m21)+';'+('border-color:'+(item.m22)+';')}}" data-id="{{item.$orig.id}}" data-event-opts="{{[['tap',[['goDaigoupick',['$event']]]]]}}" catchtap="__e">帮他下单</view></block></view></block></view></view></block></view></view></block><block wx:if="{{nodata}}"><view class="empty-state"><image class="empty-icon" src="/static/img/no-data.png"></image><text class="empty-text">{{keyword?'未找到相关成员':'暂无团队成员'}}</text></view></block><uni-popup class="vue-ref" vue-id="7220a41e-3" id="dialogmoneyInput" type="dialog" data-ref="dialogmoneyInput" bind:__l="__l" vue-slots="{{['default']}}"><uni-popup-dialog vue-id="{{('7220a41e-4')+','+('7220a41e-3')}}" mode="input" title="转账金额" value="" placeholder="请输入转账金额" data-event-opts="{{[['^confirm',[['givemoney']]]]}}" bind:confirm="__e" bind:__l="__l"></uni-popup-dialog></uni-popup><uni-popup class="vue-ref" vue-id="7220a41e-5" id="dialogscoreInput" type="dialog" data-ref="dialogscoreInput" bind:__l="__l" vue-slots="{{['default']}}"><uni-popup-dialog vue-id="{{('7220a41e-6')+','+('7220a41e-5')}}" mode="input" title="转账数量" value="" placeholder="请输入转账数量" data-event-opts="{{[['^confirm',[['givescore']]]]}}" bind:confirm="__e" bind:__l="__l"></uni-popup-dialog></uni-popup><block wx:if="{{dialogShow}}"><view class="popup__container"><view data-event-opts="{{[['tap',[['showDialog',['$event']]]]]}}" class="popup__overlay" catchtap="__e"></view><view class="popup__modal"><view class="popup__title"><text class="popup__title-text">升级</text><image class="popup__close" style="width:36rpx;height:36rpx;" src="/static/img/close.png" data-event-opts="{{[['tap',[['showDialog',['$event']]]]]}}" catchtap="__e"></image></view><view class="popup__content"><block wx:for="{{$root.l1}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="sheet-item"><text class="item-text flex-item">{{item.$orig.name}}</text><view class="flex1"></view><block wx:if="{{item.$orig.id!=tempLevelid&&item.$orig.sort>tempLevelsort}}"><view style="{{'color:'+(item.m23)+';'}}" data-id="{{item.$orig.id}}" data-name="{{item.$orig.name}}" data-event-opts="{{[['tap',[['changeLevel',['$event']]]]]}}" bindtap="__e">选择</view></block><block wx:else><view style="color:#ccc;">选择</view></block></view></block></view></view></view></block></block></block><block wx:if="{{nomore}}"><nomore vue-id="7220a41e-7" bind:__l="__l"></nomore></block><block wx:if="{{loading&&pagenum>1}}"><loading vue-id="7220a41e-8" bind:__l="__l"></loading></block><dp-tabbar vue-id="7220a41e-9" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="7220a41e-10" data-ref="popmsg" bind:__l="__l"></popmsg></view>