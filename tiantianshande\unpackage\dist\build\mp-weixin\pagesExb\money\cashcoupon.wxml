<view class="container"><block wx:if="{{isload}}"><block><view class="header-stats"><view class="stats-container"><view class="stats-item"><text class="stats-amount">{{activeTab==='income'?$root.m0:$root.m1}}</text><text class="stats-label">{{"可用"+$root.m2}}</text></view><view class="stats-item"><text class="stats-amount">{{activeTab==='income'?$root.m3:$root.m4}}</text><block wx:if="{{activeTab==='income'}}"><text class="stats-label">累计补贴</text></block><block wx:else><text class="stats-label">累计收入</text></block></view></view></view><view class="tab-section"><view data-event-opts="{{[['tap',[['switchTab',['income']]]]]}}" class="{{['tab-item',activeTab==='income'?'active':'']}}" bindtap="__e"><text class="tab-text">收入明细</text><block wx:if="{{activeTab==='income'}}"><view class="tab-underline"></view></block></view><view data-event-opts="{{[['tap',[['switchTab',['change']]]]]}}" class="{{['tab-item',activeTab==='change'?'active':'']}}" bindtap="__e"><text class="tab-text">变动明细</text><block wx:if="{{activeTab==='change'}}"><view class="tab-underline"></view></block></view></view><view data-event-opts="{{[['tap',[['showDatePicker',['$event']]]]]}}" class="date-selector" bindtap="__e"><text class="date-text">{{currentDate}}</text><image class="calendar-icon" src="/static/img/calendar.png" mode="aspectFit"></image></view><block wx:if="{{activeTab==='income'}}"><view class="table-header"><text class="header-date">结算日期</text><text class="header-count">参与订单数</text><text class="header-amount">{{"收入"+$root.m5}}</text></view></block><block wx:if="{{activeTab==='income'}}"><view class="table-content"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="table-row"><text class="row-date">{{item.$orig.settle_date}}</text><text class="row-count">{{item.$orig.order_count}}</text><text class="row-amount">{{item.m6}}</text></view></block><block wx:if="{{$root.g0===0}}"><view class="empty-state"><text class="empty-text">没有更多了</text></view></block></view></block><block wx:if="{{activeTab==='change'}}"><view class="change-list"><block wx:for="{{$root.l1}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="change-item"><view class="change-info"><text class="change-title">{{item.$orig.remark}}</text><text class="change-time">{{item.$orig.createtime}}</text><text class="change-balance">{{"变更后"+item.m7+": "+item.m8}}</text></view><view class="{{['change-amount',item.$orig.money>0?'amount-positive':'amount-negative']}}"><text class="amount-symbol">{{item.$orig.money>0?'+':''}}</text><text class="amount-value">{{item.m9}}</text></view></view></block><block wx:if="{{$root.g1===0}}"><view class="empty-state"><text class="empty-text">没有更多了</text></view></block></view></block><block wx:if="{{nomore}}"><nomore vue-id="36cb5ab8-1" bind:__l="__l"></nomore></block><block wx:if="{{nodata}}"><nodata vue-id="36cb5ab8-2" bind:__l="__l"></nodata></block></block></block><block wx:if="{{showDatePickerModal}}"><view data-event-opts="{{[['tap',[['hideDatePicker',['$event']]]]]}}" class="date-picker-mask" bindtap="__e"><view data-event-opts="{{[['tap',[['',['$event']]]]]}}" class="date-picker-content" catchtap="__e"><view class="picker-header"><text data-event-opts="{{[['tap',[['hideDatePicker',['$event']]]]]}}" class="picker-cancel" bindtap="__e">取消</text><text class="picker-title">选择月份</text><text data-event-opts="{{[['tap',[['confirmDatePicker',['$event']]]]]}}" class="picker-confirm" bindtap="__e">确定</text></view><picker-view class="picker-view" value="{{pickerValue}}" data-event-opts="{{[['change',[['onPickerChange',['$event']]]]]}}" bindchange="__e"><picker-view-column><block wx:for="{{years}}" wx:for-item="year" wx:for-index="index" wx:key="index"><view class="picker-item">{{year}}</view></block></picker-view-column><picker-view-column><block wx:for="{{months}}" wx:for-item="month" wx:for-index="index" wx:key="index"><view class="picker-item">{{month}}</view></block></picker-view-column></picker-view></view></view></block><block wx:if="{{loading}}"><loading vue-id="36cb5ab8-3" bind:__l="__l"></loading></block><dp-tabbar vue-id="36cb5ab8-4" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="36cb5ab8-5" data-ref="popmsg" bind:__l="__l"></popmsg></view>