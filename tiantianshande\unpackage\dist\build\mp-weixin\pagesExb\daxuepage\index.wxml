<view><block wx:if="{{isdiy}}"><block><view style="{{('display:flex;min-height: 100vh;flex-direction: column;background-color:'+pageinfo.bgcolor)}}"><view class="container"></view></view><dp-guanggao vue-id="b73fff82-1" guanggaopic="{{guanggaopic}}" guanggaourl="{{guanggaourl}}" bind:__l="__l"></dp-guanggao></block></block><block wx:else><block><block wx:if="{{switchIndex==0}}"><view><view class="flex" style="padding:30rpx;padding-right:0;padding-bottom:0;"><block wx:if="{{business}}"><view style="margin-right:10rpx;"><image style="width:130rpx;height:130rpx;" src="{{business.logo}}" alt></image></view></block><block wx:if="{{business}}"><view style="flex:1;"><view class="text-h2 text-bold" style="margin-bottom:10rpx;">{{''+business.name+''}}</view><view class="flex"><block wx:for="{{business.biaoqian_names}}" wx:for-item="item" wx:for-index="__i0__"><view class="tag-item">{{''+item+''}}</view></block></view></view></block><view><view data-event-opts="{{[['tap',[['addfavorite',['$event']]]]]}}" class="bg-blue flex align-center" style="margin-bottom:10rpx;" bindtap="__e"><view style="margin-right:10rpx;"><block wx:if="{{isfavorite}}"><image style="width:40rpx;height:40rpx;" src="{{pre_url+'/static/icon/collect.png'}}" mode></image></block><block wx:else><image style="width:40rpx;height:40rpx;" src="{{pre_url+'/static/icon/notCollect.png'}}" mode></image></block></view><view>收藏</view></view><button class="bg-blue flex align-center" style="height:28.86px;" open-type="share"><view style="margin-right:10rpx;line-height:0px;"><image style="width:40rpx;height:40rpx;" src="{{pre_url+'/static/icon/share2.png'}}" mode></image></view><view>分享</view></button></view></view><view class="box"><view class="mb30"><block wx:for="{{articles}}" wx:for-item="item" wx:for-index="__i1__"><view class="article-item" data-url="{{'/pagesExa/daxuepage/articledetail?id='+item.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="flex align-center"><view class="text-bold" style="margin-right:10rpx;">{{''+item.name+''}}</view><view><image style="width:40rpx;height:40rpx;" src="{{pre_url+'/static/icon/fire.png'}}" mode></image></view></view><view><image style="width:20rpx;height:20rpx;" src="{{pre_url+'/static/icon/right.png'}}" mode></image></view></view></block></view><view class="mb30"><view class="text-h2 text-bold mb30">学校简介</view><view class="bg-grey flex align-center" data-url="{{'/pagesExa/daxuepage/schoolBlurb?id='+opt.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="text-overflow">{{''+business.desc+''}}</view><view><image style="width:20rpx;height:20rpx;" src="{{pre_url+'/static/icon/right.png'}}" mode></image></view></view></view><view class="mb30"><view class="text-h2 text-bold mb30">基本信息</view><view class="flex align-center" style="margin-bottom:40rpx;"><view class="flex align-center" style="flex:1;"><view class="icon"><image style="width:50rpx;height:50rpx;" src="{{pre_url+'/static/icon/time.png'}}" mode></image></view><view><view class="text-h3 text-bold">{{''+business.established_year+' 年'}}</view><view style="font-size:20rpx;color:#dddddd;">建校时间</view></view></view><view class="flex align-center" style="flex:1;"><view class="icon"><image style="width:50rpx;height:50rpx;" src="{{pre_url+'/static/icon/area.png'}}" mode></image></view><view><view class="text-h3 text-bold">{{''+business.campus_area+' 亩'}}</view><view style="font-size:20rpx;color:#dddddd;">占地面积</view></view></view></view><view class="flex align-center"><view class="flex align-center" style="flex:1;"><view class="icon"><image style="width:50rpx;height:50rpx;" src="{{pre_url+'/static/icon/building.png'}}" mode></image></view><view><view class="text-h3 text-bold">{{''+business.department_count+' 个'}}</view><view style="font-size:20rpx;color:#dddddd;">院系数量</view></view></view><view class="flex align-center" style="flex:1;"><view class="icon"><image style="width:50rpx;height:50rpx;" src="{{pre_url+'/static/icon/speciality.png'}}" mode></image></view><view><view class="text-h3 text-bold">{{''+business.major_count+' 个'}}</view><view style="font-size:20rpx;color:#dddddd;">专业数量</view></view></view></view></view><block wx:if="{{$root.g0}}"><view class="mb30"><view class="text-h2 text-bold mb30">院校环境</view><scroll-view style="white-space:nowrap;width:100%;" scroll-x="true"><block wx:for="{{pics}}" wx:for-item="item" wx:for-index="index"><view data-event-opts="{{[['tap',[['fnPreviewImage',[index]]]]]}}" style="border-radius:30rpx;overflow:hidden;margin-right:20rpx;width:340rpx;height:200rpx;display:inline-block;" bindtap="__e"><image style="width:100%;height:100%;" src="{{item}}" alt></image></view></block></scroll-view></view></block><view class="mb30"><view class="text-h2 text-bold mb30">校区地址</view><view style="position:relative;"><map style="width:100%;height:300rpx;" latitude="{{business.latitude}}" longitude="{{business.longitude}}" scale="{{12}}"></map><view class="map-address">{{'校区地址：'+business.city+business.district+" "+business.address+''}}</view></view></view></view></view></block><block wx:if="{{switchIndex==1}}"><view><speciality vue-id="b73fff82-2" daxueid="{{opt.id}}" bind:__l="__l"></speciality></view></block><block wx:if="{{switchIndex==2}}"><view><view class="mb30"><block wx:for="{{articles}}" wx:for-item="item" wx:for-index="__i2__"><view class="article-item" data-url="{{'/pagesExa/daxuepage/articledetail?id='+item.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="flex align-center"><view class="text-bold" style="margin-right:10rpx;">{{''+item.name+''}}</view><view><image style="width:40rpx;height:40rpx;" src="{{pre_url+'/static/icon/fire.png'}}" mode></image></view></view><view><image style="width:20rpx;height:20rpx;" src="{{pre_url+'/static/icon/right.png'}}" mode></image></view></view></block><block wx:if="{{$root.g1==0}}"><nodata vue-id="b73fff82-3" bind:__l="__l"></nodata></block></view></view></block><block wx:if="{{switchIndex==3}}"><view><fractional-line vue-id="b73fff82-4" daxueid="{{opt.id}}" bind:__l="__l"></fractional-line></view></block><view class="tabbar"><block wx:for="{{tabbarList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['switchTab',[index]]]]]}}" class="tabbar-item" bindtap="__e"><view><image style="width:40rpx;height:40rpx;" src="{{item.icon}}" mode></image></view><view><text>{{item.text}}</text></view></view></block></view></block></block><block wx:if="{{loading}}"><loading vue-id="b73fff82-5" bind:__l="__l"></loading></block><popmsg class="vue-ref" vue-id="b73fff82-6" data-ref="popmsg" bind:__l="__l"></popmsg></view>