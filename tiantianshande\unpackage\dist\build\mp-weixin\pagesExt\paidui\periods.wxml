<view class="container"><block wx:if="{{isload}}"><block><view class="banner" style="{{'background:'+($root.m0)+';'}}"><image src="{{userinfo.headimg||'/static/img/default-avatar.png'}}"></image><view class="info"><text class="nickname">{{userinfo.nickname||'用户'}}</text><text class="subtitle">我的分期排队</text></view></view><view class="stats-container"><view class="stats-card"><view class="stats-row"><view class="stat-item"><text class="stat-value">{{user_summary.total_periods||0}}</text><text class="stat-label">总分期数</text></view><view class="stat-item"><text class="stat-value">{{user_summary.completed_periods||0}}</text><text class="stat-label">已完成</text></view><view class="stat-item"><text class="stat-value">{{user_summary.waiting_periods||0}}</text><text class="stat-label">待分红</text></view></view><view class="stats-row"><view class="stat-item"><text class="stat-value">{{(user_summary.completion_rate||0)+"%"}}</text><text class="stat-label">完成率</text></view><view class="stat-item"><text class="stat-value">{{"￥"+(user_summary.total_distributed||0)}}</text><text class="stat-label">已分红金额</text></view><view class="stat-item"><text class="stat-value">{{"￥"+(user_summary.total_waiting||0)}}</text><text class="stat-label">待分红金额</text></view></view></view></view><dd-tab vue-id="49fb06cf-1" itemdata="{{['全部','待分红','已分红']}}" itemst="{{['all','waiting','completed']}}" st="{{currentStatus}}" isfixed="{{false}}" data-event-opts="{{[['^changetab',[['changeStatus']]]]}}" bind:changetab="__e" bind:__l="__l"></dd-tab><view class="periods-content"><block wx:if="{{$root.g0}}"><block><block wx:for="{{periodsList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="period-card"><view class="period-header"><view class="period-title"><text class="period-id">{{"分期 #"+item.id}}</text><text class="period-num">{{"第"+item.period_num+"期/共"+item.total_periods+"期"}}</text></view><view class="period-status"><block wx:if="{{item.status==0}}"><block><text class="status-waiting">待分红</text></block></block><block wx:else><block><text class="status-completed">已分红</text></block></block></view></view><view class="period-info"><view class="info-row"><view class="info-item"><text class="info-label">排队ID:</text><text class="info-value">{{item.paidui_id}}</text></view><view class="info-item"><text class="info-label">分红金额:</text><text class="info-value amount">{{"￥"+item.period_amount}}</text></view></view><view class="info-row"><view class="info-item"><text class="info-label">创建时间:</text><text class="info-value">{{item.createtime_text}}</text></view><block wx:if="{{item.status==1}}"><view class="info-item"><text class="info-label">分红时间:</text><text class="info-value">{{item.distributed_time_text}}</text></view></block></view></view><block wx:if="{{item.paidui_summary}}"><view class="period-progress"><view class="progress-info"><text class="progress-text">{{"排队进度: "+item.paidui_summary.distributed_periods+"/"+item.paidui_summary.total_periods+" ("+item.paidui_summary.completion_rate+"%)"}}</text></view><view class="progress-bar"><view class="progress-fill" style="{{'width:'+(item.paidui_summary.completion_rate+'%')+';'}}"></view></view></view></block><view class="period-actions"><view data-event-opts="{{[['tap',[['showPeriodDetail',['$0'],[[['periodsList','',index]]]]]]]}}" class="action-btn" bindtap="__e"><text>查看详情</text></view><view data-event-opts="{{[['tap',[['viewPaiduiDetail',['$0'],[[['periodsList','',index,'paidui_id']]]]]]]}}" class="action-btn" bindtap="__e"><text>查看排队</text></view></view></view></block></block></block></view><block wx:if="{{showDetailModal}}"><view data-event-opts="{{[['tap',[['closeDetailModal',['$event']]]]]}}" class="modal-overlay" bindtap="__e"><view data-event-opts="{{[['tap',[['',['$event']]]]]}}" class="modal-content" catchtap="__e"><view class="modal-header"><text class="modal-title">分期详情</text><text data-event-opts="{{[['tap',[['closeDetailModal',['$event']]]]]}}" class="modal-close" bindtap="__e">×</text></view><view class="modal-body"><view class="detail-item"><text class="detail-label">分期ID:</text><text class="detail-value">{{currentPeriod.id}}</text></view><view class="detail-item"><text class="detail-label">排队ID:</text><text class="detail-value">{{currentPeriod.paidui_id}}</text></view><view class="detail-item"><text class="detail-label">期数:</text><text class="detail-value">{{"第"+currentPeriod.period_num+"期 / 共"+currentPeriod.total_periods+"期"}}</text></view><view class="detail-item"><text class="detail-label">分红金额:</text><text class="detail-value amount">{{"￥"+currentPeriod.period_amount}}</text></view><view class="detail-item"><text class="detail-label">总金额:</text><text class="detail-value">{{"￥"+currentPeriod.total_amount}}</text></view><view class="detail-item"><text class="detail-label">状态:</text><text class="detail-value">{{currentPeriod.status_text}}</text></view><view class="detail-item"><text class="detail-label">创建时间:</text><text class="detail-value">{{currentPeriod.createtime_text}}</text></view><block wx:if="{{currentPeriod.status==1}}"><view class="detail-item"><text class="detail-label">分红时间:</text><text class="detail-value">{{currentPeriod.distributed_time_text}}</text></view></block><block wx:if="{{currentPeriod.ordernum}}"><view class="detail-item"><text class="detail-label">订单号:</text><text class="detail-value">{{currentPeriod.ordernum}}</text></view></block><block wx:if="{{currentPeriod.nickname}}"><view class="detail-item"><text class="detail-label">会员昵称:</text><text class="detail-value">{{currentPeriod.nickname}}</text></view></block></view></view></view></block></block></block><block wx:if="{{nodata}}"><nodata vue-id="49fb06cf-2" bind:__l="__l"></nodata></block><block wx:if="{{nomore}}"><nomore vue-id="49fb06cf-3" bind:__l="__l"></nomore></block><block wx:if="{{loading}}"><loading vue-id="49fb06cf-4" bind:__l="__l"></loading></block><dp-tabbar vue-id="49fb06cf-5" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="49fb06cf-6" data-ref="popmsg" bind:__l="__l"></popmsg></view>