<view class="container"><block wx:if="{{isload}}"><block><view class="ordertop" style="{{('background:url('+pre_url+'/static/img/ordertop.png);background-size:100%')}}"><block wx:if="{{detail.status==0}}"><view class="f1"><view class="t1">等待买家付款</view><block wx:if="{{djs}}"><view class="t2">{{"剩余时间："+djs}}</view></block></view></block><block wx:if="{{detail.status==1}}"><view class="f1"><view class="t1">'已成功付款'</view><view class="t2">请尽快确认</view></view></block><block wx:if="{{detail.status==2}}"><view class="f1"><view class="t1">已确认</view><view class="t2">待入住</view></view></block><block wx:if="{{detail.status==3}}"><view class="f1"><view class="t1">已到店</view><view class="t2">{{"入住信息："+detail.linkname+" "+detail.tel}}</view></view></block><block wx:if="{{detail.status==4}}"><view class="f1"><view class="t1">已离店</view></view></block><block wx:if="{{detail.status==5}}"><view class="f1"><view class="t1">已完成</view></view></block><block wx:if="{{detail.status==-1}}"><view class="f1"><view class="t1">订单已取消</view></view></block></view><view class="product"><view class="content"><view><image src="{{detail.pic}}"></image></view><view class="detail"><text class="t1">{{hotel.name}}</text><text class="t2">{{detail.title}}</text><view class="t3"><text class="x1 flex1">{{"￥"+detail.sell_price}}</text></view></view></view></view><view class="orderinfo"><view class="item"><text class="t1">下单人</text><text class="flex1"></text><image style="width:80rpx;height:80rpx;margin-right:8rpx;" src="{{detail.headimg}}"></image><text style="height:80rpx;line-height:80rpx;">{{detail.nickname}}</text></view><view class="item"><text class="t1">{{$root.m0+"ID"}}</text><text class="t2">{{detail.mid}}</text></view><view class="item"><text class="t1">入住姓名</text><text class="t2">{{detail.linkman}}</text></view><view class="item"><text class="t1">联系电话</text><text class="t2">{{detail.tel}}</text></view><view class="item"><text class="t1">入住日期</text><text class="t2">{{detail.in_date}}</text></view><view class="item"><text class="t1">离店日期</text><text class="t2">{{detail.leave_date}}</text></view><view class="item"><text class="t1">入住人数</text><text class="t2">{{detail.totalnum}}</text></view></view><block wx:if="{{detail.remark}}"><view class="orderinfo"><view class="item"><text class="t1">备注</text><text class="t2">{{detail.remark}}</text></view></view></block><view class="orderinfo"><view class="item"><text class="t1">订单编号</text><text class="t2" user-select="true" selectable="true">{{detail.ordernum}}</text></view><view class="item"><text class="t1">下单时间</text><text class="t2">{{detail.createtime}}</text></view><block wx:if="{{detail.status>0&&detail.paytypeid!='4'&&detail.paytime}}"><view class="item"><text class="t1">支付时间</text><text class="t2">{{detail.paytime}}</text></view></block><block wx:if="{{detail.status>0&&detail.paytime}}"><view class="item"><text class="t1">支付方式</text><text class="t2">{{detail.paytype}}</text></view></block><block wx:if="{{detail.status>1&&detail.daodian_time}}"><view class="item"><text class="t1">到店时间</text><text class="t2">{{detail.daodian_time}}</text></view></block><block wx:if="{{detail.status==4&&detail.collect_time}}"><view class="item"><text class="t1">实际离店日期</text><text class="t2">{{detail.real_leavedate}}</text></view></block></view><block wx:if="{{detail.isbefore==1}}"><view class="orderinfo"><block wx:if="{{detail.fuwu_refund_money>0}}"><view class="item"><text class="t1">{{text['服务费']+"退款"}}</text><text class="t2 red">{{"-¥"+detail.fuwu_refund_money}}</text></view></block><view class="item"><text class="t1">实际支付房费</text><block wx:if="{{detail.real_usemoney>0&&detail.real_roomprice>0}}"><block><text class="t2 red">{{"¥"+detail.real_roomprice+" + "+detail.real_usemoney+$root.m1}}</text></block></block><block wx:if="{{detail.real_usemoney>0&&detail.real_roomprice==0}}"><block><text class="t2 red">{{''+detail.real_usemoney+$root.m2}}</text></block></block><block wx:if="{{detail.real_usemoney==0&&d.order.real_roomprice>0}}"><block><text class="t2 red">{{"¥"+detail.real_roomprice}}</text></block></block></view></view></block><block wx:if="{{detail.yajin_money>0&&detail.status>2}}"><view class="orderinfo"><view class="item flex-bt"><text class="t1">押金状态</text><view class="ordernum-info flex-bt"><block wx:if="{{detail.yajin_refund_status==0}}"><text class="t2">待申请</text></block><block wx:if="{{detail.yajin_refund_status==1}}"><text class="t2">审核中</text></block><block wx:if="{{detail.yajin_refund_status==2}}"><text class="t2">已退款</text></block><block wx:if="{{detail.yajin_refund_status==3}}"><text class="t2">已驳回</text></block></view></view><block wx:if="{{detail.yajin_refund_status==3}}"><view class="item flex-bt"><text class="t1">驳回原因</text><view class="ordernum-info flex-bt"><text class="t2">{{detail.yajin_refund_reason?detail.yajin_refund_reason:'无'}}</text></view></view></block></view></block><view class="orderinfo"><view class="item"><text class="t1">房费金额</text><block wx:if="{{detail.use_money>0&&detail.leftmoney>0}}"><text class="t2 red">{{detail.use_money+$root.m3+" + ￥"+detail.leftmoney}}</text></block><block wx:else><block wx:if="{{detail.use_money>0&&detail.leftmoney==0}}"><text class="t2 red">{{detail.use_money+$root.m4}}</text></block><block wx:else><text class="t2 red">{{"￥"+detail.sell_price}}</text></block></block></view><block wx:if="{{detail.fuwu_money>0}}"><view class="item"><text class="t1">{{text['服务费']}}</text><text class="t2 red">{{"+¥"+detail.fuwu_money}}</text></view></block><block wx:if="{{detail.yajin_money>0}}"><view class="item"><text class="t1">押金</text><text class="t2 red">{{"+¥"+detail.yajin_money+''}}</text></view></block><block wx:else><view class="item"><text class="t1">押金 (免押)</text><text class="t2 red">{{"+¥"+detail.yajin_money+''}}</text></view></block><block wx:if="{{detail.leveldk_money>0}}"><view class="item"><text class="t1">{{$root.m5+"折扣"}}</text><text class="t2 red">{{"-¥"+detail.leveldk_money}}</text></view></block><block wx:if="{{detail.jianmoney>0}}"><view class="item"><text class="t1">满减活动</text><text class="t2 red">{{"-¥"+detail.manjian_money}}</text></view></block><block wx:if="{{detail.couponmoney>0}}"><view class="item"><text class="t1">{{$root.m6+"抵扣"}}</text><text class="t2 red">{{"-¥"+detail.coupon_money}}</text></view></block><block wx:if="{{detail.scoredk_money>0}}"><view class="item"><text class="t1">{{$root.m7+"抵扣"}}</text><text class="t2 red">{{"-¥"+detail.scoredk_money}}</text></view></block><block wx:if="{{detail.use_money>0}}"><view class="item"><text class="t1">{{$root.m8+"抵扣"}}</text><text class="t2 red">{{"-"+detail.use_money+$root.m9}}</text></view></block><view class="item"><text class="t1">支付金额</text><text class="t2 red">{{"¥"+detail.totalprice}}</text></view><view class="item"><text class="t1">订单状态</text><block wx:if="{{detail.status==0}}"><text class="t2">未付款</text></block><block wx:if="{{detail.status==1}}"><text class="t2">待确认</text></block><block wx:if="{{detail.status==2}}"><text class="t2">待入住</text></block><block wx:if="{{detail.status==3}}"><text class="t2">已到店</text></block><block wx:if="{{detail.status==4}}"><text class="t2">已离店</text></block><block wx:if="{{detail.status==5}}"><text class="t2">已完成</text></block><block wx:if="{{detail.status==-1}}"><text class="t2">已关闭</text></block></view><block wx:if="{{detail.refund_status>0}}"><view class="item"><text class="t1">退款状态</text><block wx:if="{{detail.refund_status==1}}"><text class="t2 red">{{"审核中,¥"+detail.refund_money}}</text></block><block wx:if="{{detail.refund_status==2}}"><text class="t2 red">{{"已退款,¥"+detail.refund_money}}</text></block><block wx:if="{{detail.refund_status==3}}"><text class="t2 red">{{"已驳回,¥"+detail.refund_money}}</text></block></view></block><block wx:if="{{detail.refund_status>0}}"><view class="item"><text class="t1">退款原因</text><text class="t2 red">{{detail.refund_reason}}</text></view></block><block wx:if="{{detail.refund_checkremark}}"><view class="item"><text class="t1">审核备注</text><text class="t2 red">{{detail.refund_checkremark}}</text></view></block><view class="item"><text class="t1">下单备注</text><text class="t2 red">{{detail.message?detail.message:'无'}}</text></view><block wx:if="{{detail.status==1||detail.status==2}}"><view class="item flex-col"><text class="t1">核销码</text><view class="flex-x-center"><image style="width:400rpx;height:400rpx;" src="{{detail.hexiao_qr}}" data-url="{{detail.hexiao_qr}}" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image></view></view></block></view><view style="width:100%;height:calc(160rpx + env(safe-area-inset-bottom));"></view><view class="bottom notabbarbot"><block wx:if="{{detail.refund_status==1}}"><view class="btn2" data-id="{{detail.id}}" data-event-opts="{{[['tap',[['refundnopass',['$event']]]]]}}" bindtap="__e">退款驳回</view></block><block wx:if="{{detail.refund_status==1}}"><view class="btn2" data-id="{{detail.id}}" data-event-opts="{{[['tap',[['refundpass',['$event']]]]]}}" bindtap="__e">退款通过</view></block><block wx:if="{{detail.status==0}}"><view class="btn2" data-id="{{detail.id}}" data-event-opts="{{[['tap',[['closeOrder',['$event']]]]]}}" bindtap="__e">关闭订单</view></block><block wx:if="{{detail.status==1}}"><view class="btn2" data-id="{{detail.id}}" data-event-opts="{{[['tap',[['confirmorder',['$event']]]]]}}" bindtap="__e">确认订单</view></block><block wx:if="{{detail.status==2}}"><view class="btn2" data-id="{{detail.id}}" data-event-opts="{{[['tap',[['qrdaodian',['$event']]]]]}}" bindtap="__e">确认到店</view></block><block wx:if="{{detail.status==3}}"><view class="btn2" data-id="{{detail.id}}" data-event-opts="{{[['tap',[['qrlidian',['$event']]]]]}}" bindtap="__e">确认离店</view></block><block wx:if="{{detail.status==4&&!detail.yajin_refund_status&&detail.yajin_money>0}}"><view class="btn2" data-id="{{detail.id}}" data-event-opts="{{[['tap',[['refundYajin',['$event']]]]]}}" bindtap="__e">退押金</view></block><view class="btn2" data-id="{{detail.id}}" data-event-opts="{{[['tap',[['setremark',['$event']]]]]}}" bindtap="__e">设置备注</view></view><uni-popup class="vue-ref" vue-id="454b1b5e-1" id="dialogSetremark" type="dialog" data-ref="dialogSetremark" bind:__l="__l" vue-slots="{{['default']}}"><uni-popup-dialog vue-id="{{('454b1b5e-2')+','+('454b1b5e-1')}}" mode="input" title="设置备注" value="{{detail.remark}}" placeholder="请输入备注" data-event-opts="{{[['^confirm',[['setremarkconfirm']]]]}}" bind:confirm="__e" bind:__l="__l"></uni-popup-dialog></uni-popup><uni-popup class="vue-ref" vue-id="454b1b5e-3" id="dialogLeave" type="dialog" data-ref="dialogLeave" bind:__l="__l" vue-slots="{{['default']}}"><view class="uni-popup-dialog"><view class="uni-dialog-title"><text class="uni-dialog-title-text">确认离店</text></view><view class="uni-dialog-content"><view class="uni-list-cell-db"><label>离店日期</label><picker mode="date" value="{{date}}" start="{{startDate}}" end="{{endDate}}" data-event-opts="{{[['change',[['bindDateChange',['$event']]]]]}}" bindchange="__e"><view class="uni-input">{{date}}</view></picker></view></view><view class="uni-dialog-button-group"><view data-event-opts="{{[['tap',[['dialogLeaveClose',['$event']]]]]}}" class="uni-dialog-button" bindtap="__e"><text class="uni-dialog-button-text">取消</text></view><view data-event-opts="{{[['tap',[['confirmleave',['$event']]]]]}}" class="uni-dialog-button uni-border-left" bindtap="__e"><text class="uni-dialog-button-text uni-button-color">确定</text></view></view></view></uni-popup></block></block><block wx:if="{{loading}}"><loading vue-id="454b1b5e-4" bind:__l="__l"></loading></block><dp-tabbar vue-id="454b1b5e-5" opt="{{opt}}" bind:__l="__l"></dp-tabbar></view>