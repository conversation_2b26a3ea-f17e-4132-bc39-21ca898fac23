<view style="width:100%;"><view class="dp-product-item"><block wx:for="{{data}}" wx:for-item="item" wx:for-index="index" wx:key="id"><view class="item" style="{{('width:49%;margin-right:'+(index%2==0?'2%':0))}}" data-url="{{'/zhaopin/qiuzhi/detail?id='+item[idfield]}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="product-pic"><text class="{{['tag',item.has_job==1?'st1':'st2']}}">{{item.has_job==1?'在职':'离职'}}</text><image class="image" style="{{('filter: blur('+item.mohu+'px);-webkit-filter: blur('+item.mohu+'px);-moz-filter: blur('+item.mohu+'px)')}}" src="{{item.thumb}}" mode="aspectFill"></image><block wx:if="{{item.qianyue_id>0}}"><text class="qianyue">签约保障中</text></block><block wx:else><text class="qianyue">认证保障中</text></block></view><view class="product-info"><view class="p1">{{item.title}}</view><view class="p3">{{item.cnames}}</view><view class="p3"><text class="t2">{{item.name+"/"+(item.sex==1?'男':'女')+"/"+item.salary}}</text></view></view></view></block></view></view>