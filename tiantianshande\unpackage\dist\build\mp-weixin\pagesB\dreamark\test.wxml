<view class="test-page"><view class="test-header"><text class="test-title">按钮测试页面</text></view><view class="test-content"><button class="test-btn" type="default" data-event-opts="{{[['tap',[['testBasicClick',['$event']]]]]}}" bindtap="__e">基础按钮测试</button><view data-event-opts="{{[['tap',[['testViewClick',['$event']]]]]}}" class="test-view-btn" bindtap="__e">View按钮测试</view><button class="start-btn" type="default" data-event-opts="{{[['tap',[['testStartDialogue',['$event']]]]]}}" bindtap="__e">启动时空对话测试</button><button class="clear-btn" type="default" data-event-opts="{{[['tap',[['testClearData',['$event']]]]]}}" bindtap="__e">清空数据测试</button><button class="nav-btn" type="default" data-event-opts="{{[['tap',[['testNavigation',['$event']]]]]}}" bindtap="__e">跳转测试</button></view><view class="test-log"><text class="log-title">测试日志:</text><block wx:for="{{testLogs}}" wx:for-item="log" wx:for-index="index" wx:key="index"><view class="log-item"><text class="log-text">{{log}}</text></view></block></view></view>