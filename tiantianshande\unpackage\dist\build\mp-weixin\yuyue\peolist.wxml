<view data-event-opts="{{[['tap',[['checkTapOutside',['$event']]]]]}}" class="container" bindtap="__e"><block wx:if="{{isload}}"><block><view class="topsearch flex-y-center"><view data-event-opts="{{[['tap',[['gotoCity',['$event']]]]]}}" class="{{['city-select',(currentCity)?'active':'']}}" bindtap="__e"><text>{{currentCity||'选择城市'}}</text><text class="city-icon">▼</text></view><view class="f1 flex-y-center"><image class="img" src="/static/img/search_ico.png"></image><input placeholder="输入姓名/手机号搜索" placeholder-style="font-size:24rpx;color:#C2C2C2" data-event-opts="{{[['confirm',[['searchConfirm',['$event']]]],['input',[['searchChange',['$event']]]]]}}" value="{{keyword}}" bindconfirm="__e" bindinput="__e"/></view><view data-event-opts="{{[['tap',[['toggleFilterOptions',['$event']]]]]}}" class="filter-btn" catchtap="__e"><text class="filter-text">筛选</text><text class="filter-icon">▼</text></view></view><block wx:if="{{showFilterOptions}}"><view data-event-opts="{{[['tap',[['',['$event']]]]]}}" class="filter-options" catchtap="__e"><view data-event-opts="{{[['tap',[['toggleFilterOptions',['$event']]]]]}}" class="filter-overlay" bindtap="__e"></view><view data-event-opts="{{[['tap',[['',['$event']]]]]}}" class="filter-panel" catchtap="__e"><view class="filter-title">排序方式</view><view class="filter-list"><view class="{{['filter-item '+(field=='juli'?'active':'')]}}" style="{{(field=='juli'?'color:'+$root.m0+';border-color:'+$root.m1:'')}}" data-field="juli" data-order="asc" data-event-opts="{{[['tap',[['sortClick',['$event']]]]]}}" bindtap="__e"><text>按距离排序</text><block wx:if="{{field=='juli'}}"><text class="check-icon">✓</text></block></view><view class="{{['filter-item '+(field=='comment_score'?'active':'')]}}" style="{{(field=='comment_score'?'color:'+$root.m2+';border-color:'+$root.m3:'')}}" data-field="comment_score" data-order="desc" data-event-opts="{{[['tap',[['sortClick',['$event']]]]]}}" bindtap="__e"><text>按评分排序</text><block wx:if="{{field=='comment_score'}}"><text class="check-icon">✓</text></block></view></view><view class="filter-btn-group"><view data-event-opts="{{[['tap',[['resetFilter',['$event']]]]]}}" class="filter-reset" bindtap="__e">重置</view><view data-event-opts="{{[['tap',[['confirmFilter',['$event']]]]]}}" class="filter-confirm" style="{{'background:'+($root.m4)+';'}}" bindtap="__e">确定</view></view></view></view></block><view class="order-tab"><view class="order-tab2"><view class="{{['item '+(curTopIndex==-1?'on':'')]}}" data-index="{{-1}}" data-id="{{0}}" data-event-opts="{{[['tap',[['switchTopTab',['$event']]]]]}}" bindtap="__e">全部<view class="after" style="{{'background:'+($root.m5)+';'}}"></view></view><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><view class="{{['item '+(curTopIndex==index?'on':'')]}}" data-index="{{index}}" data-id="{{item.$orig.id}}" data-event-opts="{{[['tap',[['switchTopTab',['$event']]]]]}}" bindtap="__e">{{item.$orig.name}}<view class="after" style="{{'background:'+(item.m6)+';'}}"></view></view></block></block></view></view><block wx:if="{{locationFailed}}"><view class="location-failed flex-center"><view data-event-opts="{{[['tap',[['retryLocation',['$event']]]]]}}" class="retry-btn" style="{{'border-color:'+($root.m7)+';'+('color:'+($root.m8)+';')}}" bindtap="__e">重新获取位置</view></view></block><view class="content-list"><block wx:for="{{$root.l2}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="content flex" data-id="{{item.$orig.id}}"><view class="f1" data-url="{{'peodetail?id='+item.$orig.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="headimg"><image src="{{item.$orig.headimg}}" mode="aspectFill"></image></view><view class="text1"><view class="name-type"><text class="t1">{{item.$orig.realname+''}}</text><block wx:if="{{item.$orig.typename}}"><text class="t2">{{item.$orig.typename+''}}</text></block></view><view class="text2">{{item.$orig.jineng||'暂无技能描述'}}</view><block wx:if="{{item.g0}}"><view class="tech-tags"><block wx:for="{{item.l1}}" wx:for-item="tag" wx:for-index="tagIndex" wx:key="tagIndex"><text class="tech-tag">{{tag}}</text></block></view></block><view class="text3"><text class="t4">服务<text>{{''+item.$orig.totalnum}}</text>次</text><text class="t5">评分<text>{{item.$orig.comment_score}}</text></text><block wx:if="{{item.$orig.distance&&item.$orig.distance!='未知'}}"><text class="t6">距离<text>{{item.$orig.distance}}</text></text></block></view></view></view><view><view class="yuyue" style="{{'background:'+(item.m9)+';'}}" data-url="{{'/yuyue/peodetail?id='+item.$orig.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">预约</view></view></view></block><block wx:if="{{$root.g1}}"><view class="no-data-placeholder"><image class="empty-img" src="/static/img/empty.png" mode="aspectFit"></image><view class="empty-text">暂无相关技师</view></view></block></view><block wx:if="{{$root.g2}}"><nodata vue-id="c9136664-1" bind:__l="__l"></nodata></block><block wx:if="{{nomore}}"><nomore vue-id="c9136664-2" bind:__l="__l"></nomore></block></block></block><block wx:if="{{loading}}"><loading vue-id="c9136664-3" bind:__l="__l"></loading></block><block wx:if="{{$root.g3}}"><view class="content-list skeleton-list"><block wx:for="{{3}}" wx:for-item="i" wx:for-index="__i0__" wx:key="*this"><view class="content skeleton-card"><view class="f1"><view class="skeleton headimg-skeleton"></view><view class="text1"><view class="skeleton title-skeleton"></view><view class="skeleton text-skeleton"></view><view class="skeleton text-skeleton short"></view><view class="skeleton-tags"><block wx:for="{{2}}" wx:for-item="j" wx:for-index="__i1__" wx:key="*this"><view class="skeleton tag-skeleton"></view></block></view></view></view><view class="skeleton btn-skeleton"></view></view></block></view></block><dp-tabbar vue-id="c9136664-4" opt="{{opt}}" data-event-opts="{{[['^getmenuindex',[['getmenuindex']]]]}}" bind:getmenuindex="__e" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="c9136664-5" data-ref="popmsg" bind:__l="__l"></popmsg></view>