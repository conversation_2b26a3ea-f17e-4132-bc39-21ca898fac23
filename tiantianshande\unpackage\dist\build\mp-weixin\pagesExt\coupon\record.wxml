<view class="container"><block wx:if="{{isload}}"><block><view class="content" id="datalist"><block wx:for="{{datalist}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><view class="item"><view class="f1"><text class="t1">{{item.remark}}</text><text class="t2">{{item.createtime}}</text></view></view></block></block><block wx:if="{{nodata}}"><nodata vue-id="339c1130-1" bind:__l="__l"></nodata></block></view><block wx:if="{{nomore}}"><nomore vue-id="339c1130-2" bind:__l="__l"></nomore></block></block></block><block wx:if="{{loading}}"><loading vue-id="339c1130-3" bind:__l="__l"></loading></block><dp-tabbar vue-id="339c1130-4" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="339c1130-5" data-ref="popmsg" bind:__l="__l"></popmsg></view>