<view><block wx:if="{{isload}}"><block><view class="topsearch flex-y-center"><view class="f1 flex-y-center"><image class="img" src="/static/img/search_ico.png"></image><input placeholder="输入用户昵称或订单号搜索" placeholder-style="font-size:24rpx;color:#C2C2C2" data-event-opts="{{[['confirm',[['searchConfirm',['$event']]]]]}}" value="{{keyword}}" bindconfirm="__e"/></view></view><block wx:if="{{$root.g0}}"><view class="content"><view class="label"><text class="t1">{{"买单记录（共"+count+"条）"}}</text></view><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="item" data-url="{{'maidandetail?id='+item.$orig.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="f1"><image class="t1" src="{{item.$orig.headimg}}"></image><text class="t2">{{item.$orig.nickname}}</text></view><view class="f2"><text class="t1">{{item.$orig.money+"元"}}</text><text class="t2">{{"支付时间："+item.m0}}</text><text class="t2">{{"支付方式："+item.$orig.paytype}}</text><text class="t2">{{"订单编号："+item.$orig.ordernum}}</text></view></view></block></view></block><block wx:if="{{nodata}}"><nodata vue-id="490a8edd-1" bind:__l="__l"></nodata></block><block wx:if="{{nomore}}"><nomore vue-id="490a8edd-2" bind:__l="__l"></nomore></block></block></block><block wx:if="{{loading}}"><loading vue-id="490a8edd-3" bind:__l="__l"></loading></block><popmsg class="vue-ref" vue-id="490a8edd-4" data-ref="popmsg" bind:__l="__l"></popmsg></view>