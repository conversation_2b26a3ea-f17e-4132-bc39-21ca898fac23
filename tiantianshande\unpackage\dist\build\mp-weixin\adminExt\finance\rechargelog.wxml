<view><block wx:if="{{isload}}"><block><view class="topsearch flex-y-center"><view class="f1 flex-y-center"><image class="img" src="{{pre_url+'/static/img/search_ico.png'}}"></image><input placeholder="{{'输入'+$root.m0+'昵称搜索'}}" placeholder-style="font-size:24rpx;color:#C2C2C2" data-event-opts="{{[['confirm',[['searchConfirm',['$event']]]],['input',[['searchChange',['$event']]]]]}}" value="{{keyword}}" bindconfirm="__e" bindinput="__e"/></view></view><block wx:if="{{$root.g0}}"><view class="content"><view class="label"><text class="t1">{{"充值记录（共"+count+"条）"}}</text></view><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="item"><view class="f1"><image class="t1" src="{{item.$orig.headimg}}"></image><text class="t2">{{item.$orig.nickname}}</text></view><view class="f2" style="flex:none;width:300rpx;"><text class="t1">{{"充值金额："+item.$orig.money+"元"}}</text><text class="t2">{{item.m1}}</text><text class="t3">{{"支付方式："+item.$orig.paytype}}</text><text class="t3">{{"单号："+item.$orig.ordernum}}</text><text class="t3" style="{{(item.$orig.status==1?'color:#03bc01':'color:red')}}">{{"状态："+item.$orig.status_name}}</text></view><block wx:if="{{item.$orig.money_recharge_transfer&&item.$orig.paytypeid==5&&item.$orig.payorder_check_status>=0&&item.$orig.paytype!='随行付支付'}}"><view class="f3"><block wx:if="{{item.$orig.transfer_check==1}}"><view><text class="btn1" style="{{'background:'+(item.m2)+';'}}" data-orderid="{{item.$orig.id}}" data-event-opts="{{[['tap',[['payCheck',['$event']]]]]}}" catchtap="__e">付款凭证</text></view></block><block wx:if="{{item.$orig.transfer_check==0}}"><view><text class="btn1" style="{{'background:'+(item.m3)+';'}}" data-orderid="{{item.$orig.id}}" data-event-opts="{{[['tap',[['transferCheck',['$event']]]]]}}" catchtap="__e">转账审核</text></view></block><block wx:if="{{item.$orig.transfer_check==-1}}"><view><text>转账已驳回</text></view></block></view></block></view></block></view></block><uni-popup class="vue-ref" vue-id="1e78b066-1" id="transferCheck" type="dialog" data-ref="transferCheck" bind:__l="__l" vue-slots="{{['default']}}"><view class="uni-popup-dialog"><view class="uni-dialog-title"><text class="uni-dialog-title-text">转账审核</text></view><view class="uni-dialog-button-group"><view class="uni-dialog-button" data-st="1" data-event-opts="{{[['tap',[['dotransferCheck',['$event']]]]]}}" bindtap="__e"><text class="uni-dialog-button-text uni-button-color">同意可转账</text></view><view class="uni-dialog-button uni-border-left" data-orderid data-st="-1" data-event-opts="{{[['tap',[['dotransferCheck',['$event']]]]]}}" bindtap="__e"><text class="uni-dialog-button-text">驳回可转账</text></view></view></view></uni-popup><uni-popup class="vue-ref" vue-id="1e78b066-2" id="payCheck" type="dialog" data-ref="payCheck" bind:__l="__l" vue-slots="{{['default']}}"><view class="uni-popup-dialog"><view class="uni-dialog-title" style="margin-bottom:10rpx;"><text class="uni-dialog-title-text">付款凭证</text></view><block wx:if="{{rechargeorder.paypics}}"><view class="uni-dialog-content flex" style="padding:0;"><block wx:for="{{rechargeorder.paypics}}" wx:for-item="item1" wx:for-index="index1" wx:key="index1"><image class="img" style="width:200rpx;height:200rpx;" src="{{item1}}" data-url="{{item1}}" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image></block></view></block><view class="flex-y-center flex-x-lift" style="margin:20rpx 20rpx;height:80rpx;"><view style="font-size:28rpx;color:#555;">{{"审核状态："+(rechargeorder.check_status_label?rechargeorder.check_status_label:'')}}</view></view><view class="flex-y-center flex-x-center" style="margin:20rpx 20rpx;height:80rpx;"><view style="font-size:28rpx;color:#555;">审核备注：</view><input style="border:1px #eee solid;height:70rpx;border-radius:4px;flex:1;font-size:28rpx;padding:0 10rpx;" type="text" data-event-opts="{{[['input',[['check_remark_input',['$event']]]]]}}" value="{{rechargeorder.check_remark?rechargeorder.check_remark:''}}" bindinput="__e"/></view><view class="uni-dialog-button-group"><view class="uni-dialog-button" data-orderid="{{rechargeorder.orderid}}" data-st="1" data-event-opts="{{[['tap',[['dopayCheck',['$event']]]]]}}" bindtap="__e"><text class="uni-dialog-button-text uni-button-color">确认已支付</text></view><view class="uni-dialog-button uni-border-left" data-orderid="{{rechargeorder.orderid}}" data-st="2" data-event-opts="{{[['tap',[['dopayCheck',['$event']]]]]}}" bindtap="__e"><text class="uni-dialog-button-text">驳回</text></view></view></view></uni-popup><block wx:if="{{nodata}}"><nodata vue-id="1e78b066-3" bind:__l="__l"></nodata></block><block wx:if="{{nomore}}"><nomore vue-id="1e78b066-4" bind:__l="__l"></nomore></block></block></block><block wx:if="{{loading}}"><loading vue-id="1e78b066-5" bind:__l="__l"></loading></block><popmsg class="vue-ref" vue-id="1e78b066-6" data-ref="popmsg" bind:__l="__l"></popmsg></view>