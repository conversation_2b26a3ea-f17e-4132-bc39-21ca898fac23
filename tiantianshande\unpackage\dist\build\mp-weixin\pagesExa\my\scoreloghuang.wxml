<view class="container"><block wx:if="{{isload}}"><block><view class="myscore" style="{{'background:'+($root.m0)+';'}}"><view class="f1">我的V资产</view><view class="f2">{{myscore}}<block wx:if="{{scoreTransfer}}"><view class="btn-mini" data-url="/pagesExa/my/scoreTransfer" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">转赠</view></block></view></view><view class="content" id="datalist"><block wx:for="{{datalist}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><view class="item"><view class="f1"><text class="t1">{{item.remark}}</text><text class="t2">{{item.createtime}}</text></view><view class="f2"><block wx:if="{{item.score>0}}"><block><text class="t1">{{"+"+item.score}}</text></block></block><block wx:else><block><text class="t2">{{item.score}}</text></block></block></view></view></block></block><block wx:if="{{nodata}}"><nodata vue-id="5bf676e7-1" bind:__l="__l"></nodata></block></view><block wx:if="{{nomore}}"><nomore vue-id="5bf676e7-2" bind:__l="__l"></nomore></block></block></block><block wx:if="{{loading}}"><loading vue-id="5bf676e7-3" bind:__l="__l"></loading></block><dp-tabbar vue-id="5bf676e7-4" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="5bf676e7-5" data-ref="popmsg" bind:__l="__l"></popmsg></view>