<view class="container data-v-98a6dfa2"><block wx:if="{{isload}}"><block class="data-v-98a6dfa2"><view class="ordertop data-v-98a6dfa2" style="{{('background:url('+(shopset.order_detail_toppic?shopset.order_detail_toppic:pre_url+'/static/img/ordertop.png')+');background-size:100%')}}"><block wx:if="{{detail.status==0}}"><view class="f1 data-v-98a6dfa2"><view class="t1 data-v-98a6dfa2">等待买家付款</view><block wx:if="{{djs}}"><view class="t2 data-v-98a6dfa2">{{"剩余时间："+djs}}</view></block></view></block><block wx:if="{{detail.status==1}}"><view class="f1 data-v-98a6dfa2"><view class="t1 data-v-98a6dfa2">{{detail.paytypeid==4?'已选择'+detail.paytype:'已成功付款'}}</view><block wx:if="{{detail.freight_type!=1}}"><view class="t2 data-v-98a6dfa2">请尽快发货</view></block><block wx:if="{{detail.freight_type==1}}"><view class="t2 data-v-98a6dfa2">待提货</view></block></view></block><block wx:if="{{detail.status==2}}"><view class="f1 data-v-98a6dfa2"><view class="t1 data-v-98a6dfa2">订单已发货</view><block wx:if="{{detail.freight_type!=3}}"><view class="t2 data-v-98a6dfa2">{{"发货信息："+detail.express_com+" "+detail.express_no}}</view></block></view></block><block wx:if="{{detail.status==3}}"><view class="f1 data-v-98a6dfa2"><view class="t1 data-v-98a6dfa2">订单已完成</view></view></block><block wx:if="{{detail.status==4}}"><view class="f1 data-v-98a6dfa2"><view class="t1 data-v-98a6dfa2">订单已取消</view></view></block></view><view class="address data-v-98a6dfa2"><view class="img data-v-98a6dfa2"><image src="/static/img/address3.png" class="data-v-98a6dfa2"></image></view><view class="info data-v-98a6dfa2"><text class="t1 data-v-98a6dfa2" user-select="true" selectable="true">{{detail.linkman+''}}<block wx:if="{{detail.tel}}"><text style="margin-left:20rpx;" data-url="{{'tel:'+detail.tel}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e" class="data-v-98a6dfa2">{{detail.tel}}</text></block></text><block wx:if="{{detail.freight_type!=1&&detail.freight_type!=3}}"><text class="t2 data-v-98a6dfa2" user-select="true" selectable="true">{{"地址："+detail.area+detail.address}}</text></block><block wx:if="{{detail.freight_type==1}}"><text class="t2 data-v-98a6dfa2" data-address="{{storeinfo.address}}" data-latitude="{{storeinfo.latitude}}" data-longitude="{{storeinfo.longitude}}" user-select="true" selectable="true" data-event-opts="{{[['tap',[['openLocation',['$event']]]]]}}" bindtap="__e">{{"取货地点："+storeinfo.name+" - "+storeinfo.address}}</text></block></view></view><view class="product data-v-98a6dfa2"><block wx:for="{{prolist}}" wx:for-item="item" wx:for-index="idx" wx:key="idx"><view class="content data-v-98a6dfa2"><view data-url="{{'/shopPackage/shop/product?id='+item.proid}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e" class="data-v-98a6dfa2"><image src="{{item.pic}}" class="data-v-98a6dfa2"></image></view><view class="detail data-v-98a6dfa2"><text class="t1 data-v-98a6dfa2">{{item.name}}</text><text class="t2 data-v-98a6dfa2">{{item.ggname}}</text><block wx:if="{{item.is_yh==0}}"><view class="t3 data-v-98a6dfa2"><text class="x1 flex1 data-v-98a6dfa2">{{"￥"+item.sell_price}}</text><text class="x2 data-v-98a6dfa2">{{"×"+item.num}}</text></view></block><block wx:if="{{item.is_yh==1}}"><view class="t3 data-v-98a6dfa2"><text class="x1 flex1 data-v-98a6dfa2">{{"￥"+item.yh_prices+"(优惠金额)"}}</text><text class="x2 data-v-98a6dfa2">{{"×"+item.yh_nums}}</text></view></block><block wx:if="{{detail.total_frame_price}}"><view class="t3 frame-price data-v-98a6dfa2"><text class="frame-label data-v-98a6dfa2">框价格:</text><text class="frame-value data-v-98a6dfa2">{{"￥"+item.frame_price}}</text><text class="frame-multiply data-v-98a6dfa2">×</text><text class="frame-num data-v-98a6dfa2">{{item.num}}</text><text class="frame-equal data-v-98a6dfa2">=</text><text class="frame-total data-v-98a6dfa2">{{"￥"+item.frame_total}}</text></view></block></view></view></block></view><block wx:if="{{(detail.status==3||detail.status==2)&&(detail.freight_type==3||detail.freight_type==4)}}"><view class="orderinfo data-v-98a6dfa2"><view class="item flex-col data-v-98a6dfa2"><text class="t1 data-v-98a6dfa2" style="color:#111;">发货信息</text><text class="t2 data-v-98a6dfa2" style="text-align:left;margin-top:10rpx;padding:0 10rpx;" user-select="true" selectable="true">{{detail.freight_content}}</text></view></view></block><view class="orderinfo data-v-98a6dfa2"><view class="item data-v-98a6dfa2"><text class="t1 data-v-98a6dfa2">下单人</text><text class="flex1 data-v-98a6dfa2"></text><image style="width:80rpx;height:80rpx;margin-right:8rpx;" src="{{detail.headimg}}" class="data-v-98a6dfa2"></image><text style="height:80rpx;line-height:80rpx;" class="data-v-98a6dfa2">{{detail.nickname}}</text></view><view class="item data-v-98a6dfa2"><text class="t1 data-v-98a6dfa2">{{$root.m0+"ID"}}</text><text class="t2 data-v-98a6dfa2">{{detail.mid}}</text></view></view><block wx:if="{{detail.remark}}"><view class="orderinfo data-v-98a6dfa2"><view class="item data-v-98a6dfa2"><text class="t1 data-v-98a6dfa2">备注</text><text class="t2 data-v-98a6dfa2" user-select="true" selectable="true">{{detail.remark}}</text></view></view></block><view class="orderinfo data-v-98a6dfa2"><view class="item data-v-98a6dfa2"><text class="t1 data-v-98a6dfa2">订单编号</text><text class="t2 data-v-98a6dfa2" user-select="true" selectable="true">{{detail.ordernum}}</text></view><view class="item data-v-98a6dfa2"><text class="t1 data-v-98a6dfa2">下单时间</text><text class="t2 data-v-98a6dfa2">{{detail.createtime}}</text></view><block wx:if="{{detail.status>0&&detail.paytypeid!='4'&&detail.paytime}}"><view class="item data-v-98a6dfa2"><text class="t1 data-v-98a6dfa2">支付时间</text><text class="t2 data-v-98a6dfa2">{{detail.paytime}}</text></view></block><block wx:if="{{detail.status>0&&detail.paytime}}"><view class="item data-v-98a6dfa2"><text class="t1 data-v-98a6dfa2">支付方式</text><text class="t2 data-v-98a6dfa2">{{detail.paytype}}</text></view></block><block wx:if="{{detail.status>1&&detail.send_time}}"><view class="item data-v-98a6dfa2"><text class="t1 data-v-98a6dfa2">发货时间</text><text class="t2 data-v-98a6dfa2">{{detail.send_time}}</text></view></block><block wx:if="{{detail.status==3&&detail.collect_time}}"><view class="item data-v-98a6dfa2"><text class="t1 data-v-98a6dfa2">收货时间</text><text class="t2 data-v-98a6dfa2">{{detail.collect_time}}</text></view></block></view><view class="orderinfo data-v-98a6dfa2"><view class="item data-v-98a6dfa2"><text class="t1 data-v-98a6dfa2">商品金额</text><text class="t2 red data-v-98a6dfa2">{{"¥"+detail.product_price}}</text></view><block wx:if="{{detail.total_frame_price}}"><view class="item data-v-98a6dfa2"><text class="t1 data-v-98a6dfa2">框总价</text><text class="t2 red data-v-98a6dfa2">{{"¥"+detail.total_frame_price}}</text></view></block><block wx:if="{{detail.disprice>0}}"><view class="item data-v-98a6dfa2"><text class="t1 data-v-98a6dfa2">{{$root.m1+"折扣"}}</text><text class="t2 red data-v-98a6dfa2">{{"-¥"+detail.leveldk_money}}</text></view></block><block wx:if="{{detail.jianmoney>0}}"><view class="item data-v-98a6dfa2"><text class="t1 data-v-98a6dfa2">满减活动</text><text class="t2 red data-v-98a6dfa2">{{"-¥"+detail.manjian_money}}</text></view></block><block wx:if="{{detail.invoice_money>0}}"><view class="item data-v-98a6dfa2"><text class="t1 data-v-98a6dfa2">发票费用</text><text class="t2 red data-v-98a6dfa2">{{"+¥"+detail.invoice_money}}</text></view></block><view class="item data-v-98a6dfa2"><text class="t1 data-v-98a6dfa2">配送方式</text><text class="t2 data-v-98a6dfa2">{{detail.freight_text}}</text></view><block wx:if="{{detail.freight_type==1&&detail.freightprice>0}}"><view class="item data-v-98a6dfa2"><text class="t1 data-v-98a6dfa2">服务费</text><text class="t2 red data-v-98a6dfa2">{{"+¥"+detail.freight_price}}</text></view></block><block wx:if="{{detail.freight_time}}"><view class="item data-v-98a6dfa2"><text class="t1 data-v-98a6dfa2">{{(detail.freight_type!=1?'配送':'提货')+"时间"}}</text><text class="t2 data-v-98a6dfa2">{{detail.freight_time}}</text></view></block><block wx:if="{{detail.coupon_money>0}}"><view class="item data-v-98a6dfa2"><text class="t1 data-v-98a6dfa2">{{$root.m2+"抵扣"}}</text><text class="t2 red data-v-98a6dfa2">{{"-¥"+detail.coupon_money}}</text></view></block><block wx:if="{{detail.scoredk>0}}"><view class="item data-v-98a6dfa2"><text class="t1 data-v-98a6dfa2">{{$root.m3+"抵扣"}}</text><text class="t2 red data-v-98a6dfa2">{{"-¥"+detail.scoredk_money}}</text></view></block><view class="item data-v-98a6dfa2"><text class="t1 data-v-98a6dfa2">实付款</text><text class="t2 red data-v-98a6dfa2">{{"¥"+detail.totalprice}}</text></view><view class="item data-v-98a6dfa2"><text class="t1 data-v-98a6dfa2">订单状态</text><block wx:if="{{detail.status==0}}"><text class="t2 data-v-98a6dfa2">未付款</text></block><block wx:if="{{detail.status==1}}"><text class="t2 data-v-98a6dfa2">已付款</text></block><block wx:if="{{detail.status==2}}"><text class="t2 data-v-98a6dfa2">已发货</text></block><block wx:if="{{detail.status==3}}"><text class="t2 data-v-98a6dfa2">已收货</text></block><block wx:if="{{detail.status==4}}"><text class="t2 data-v-98a6dfa2">已关闭</text></block></view><block wx:if="{{detail.refund_status>0}}"><view class="item data-v-98a6dfa2"><text class="t1 data-v-98a6dfa2">退款状态</text><block wx:if="{{detail.refund_status==1}}"><text class="t2 red data-v-98a6dfa2">{{"审核中,¥"+detail.refund_money}}</text></block><block wx:if="{{detail.refund_status==2}}"><text class="t2 red data-v-98a6dfa2">{{"已退款,¥"+detail.refund_money}}</text></block><block wx:if="{{detail.refund_status==3}}"><text class="t2 red data-v-98a6dfa2">{{"已驳回,¥"+detail.refund_money}}</text></block></view></block><block wx:if="{{detail.refund_status>0}}"><view class="item data-v-98a6dfa2"><text class="t1 data-v-98a6dfa2">退款原因</text><text class="t2 red data-v-98a6dfa2">{{detail.refund_reason}}</text></view></block><block wx:if="{{detail.refund_checkremark}}"><view class="item data-v-98a6dfa2"><text class="t1 data-v-98a6dfa2">审核备注</text><text class="t2 red data-v-98a6dfa2">{{detail.refund_checkremark}}</text></view></block><block wx:if="{{(detail.status==1||detail.status==2)&&detail.freight_type==1}}"><view class="item flex-col data-v-98a6dfa2"><text class="t1 data-v-98a6dfa2">核销码</text><view class="flex-x-center data-v-98a6dfa2"><image style="width:400rpx;height:400rpx;" src="{{detail.hexiao_qr}}" data-url="{{detail.hexiao_qr}}" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e" class="data-v-98a6dfa2"></image></view></view></block><block wx:if="{{detail.balance_price>0}}"><view class="item data-v-98a6dfa2"><text class="t1 data-v-98a6dfa2">尾款</text><text class="t2 red data-v-98a6dfa2">{{"¥"+detail.balance_price}}</text></view></block><block wx:if="{{detail.balance_price>0}}"><view class="item data-v-98a6dfa2"><text class="t1 data-v-98a6dfa2">尾款状态</text><block wx:if="{{detail.balance_pay_status==1}}"><text class="t2 data-v-98a6dfa2">已支付</text></block><block wx:if="{{detail.balance_pay_status==0}}"><text class="t2 data-v-98a6dfa2">未支付</text></block></view></block></view><block wx:if="{{detail.checkmemid}}"><view class="orderinfo data-v-98a6dfa2"><view class="item data-v-98a6dfa2"><text class="t1 data-v-98a6dfa2">所选会员</text><text class="flex1 data-v-98a6dfa2"></text><image style="width:80rpx;height:80rpx;margin-right:8rpx;" src="{{detail.checkmember.headimg}}" class="data-v-98a6dfa2"></image><text style="height:80rpx;line-height:80rpx;" class="data-v-98a6dfa2">{{detail.checkmember.nickname}}</text></view></view></block><block wx:if="{{$root.g0>0}}"><view class="orderinfo data-v-98a6dfa2"><block wx:for="{{detail.formdata}}" wx:for-item="item" wx:for-index="__i0__" wx:key="*this"><view class="item data-v-98a6dfa2"><text class="t1 data-v-98a6dfa2">{{item[0]}}</text><block wx:if="{{item[2]=='upload'}}"><view class="t2 data-v-98a6dfa2"><image style="width:400rpx;height:auto;" src="{{item[1]}}" mode="widthFix" data-url="{{item[1]}}" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e" class="data-v-98a6dfa2"></image></view></block><block wx:else><text class="t2 data-v-98a6dfa2" user-select="true" selectable="true">{{item[1]}}</text></block></view></block></view></block><view style="width:100%;height:160rpx;" class="data-v-98a6dfa2"></view><view class="bottom notabbarbot data-v-98a6dfa2"><block wx:if="{{detail.bid==0}}"><view class="btn2 data-v-98a6dfa2" data-url="{{'/admin/member/historys?id='+detail.mid}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">历史订单</view></block><block wx:if="{{detail.refund_status==1}}"><view class="btn2 data-v-98a6dfa2" data-id="{{detail.id}}" data-event-opts="{{[['tap',[['refundnopass',['$event']]]]]}}" bindtap="__e">退款驳回</view></block><block wx:if="{{detail.refund_status==1}}"><view class="btn2 data-v-98a6dfa2" data-id="{{detail.id}}" data-event-opts="{{[['tap',[['refundpass',['$event']]]]]}}" bindtap="__e">退款通过</view></block><block wx:if="{{detail.status==0}}"><view class="btn2 data-v-98a6dfa2" data-id="{{detail.id}}" data-event-opts="{{[['tap',[['closeOrder',['$event']]]]]}}" bindtap="__e">关闭订单</view></block><block wx:if="{{detail.status==0&&detail.bid==0}}"><view class="btn2 data-v-98a6dfa2" data-id="{{detail.id}}" data-event-opts="{{[['tap',[['ispay',['$event']]]]]}}" bindtap="__e">改为已支付</view></block><block wx:if="{{detail.status==1}}"><view class="btn2 data-v-98a6dfa2" data-id="{{detail.id}}" data-event-opts="{{[['tap',[['fahuo',['$event']]]]]}}" bindtap="__e">发货</view></block><block wx:if="{{(detail.status==1||detail.status==2)&&detail.freight_type==1}}"><view class="btn2 data-v-98a6dfa2" data-id="{{detail.id}}" data-event-opts="{{[['tap',[['hexiao',['$event']]]]]}}" bindtap="__e">核销</view></block><block wx:if="{{detail.status==1&&detail.canpeisong}}"><block class="data-v-98a6dfa2"><block wx:if="{{detail.express_wx_status}}"><view class="btn2 data-v-98a6dfa2" data-id="{{detail.id}}" data-event-opts="{{[['tap',[['peisongWx',['$event']]]]]}}" bindtap="__e">即时配送</view></block><block wx:else><view class="btn2 data-v-98a6dfa2" data-id="{{detail.id}}" data-event-opts="{{[['tap',[['peisong',['$event']]]]]}}" bindtap="__e">配送</view></block></block></block><block wx:if="{{(detail.status==2||detail.status==3)&&detail.express_com}}"><block class="data-v-98a6dfa2"><block wx:if="{{detail.express_type=='express_wx'}}"><view class="btn2 data-v-98a6dfa2" data-url="{{'/pagesExt/order/logistics?express_com='+detail.express_com+'&express_no='+detail.express_no+'&type=express_wx'}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">订单跟踪</view></block><block wx:else><view class="btn2 data-v-98a6dfa2" data-index="{{index}}" data-event-opts="{{[['tap',[['logistics',['$0'],['detail']]]]]}}" catchtap="__e">查物流</view></block></block></block><block wx:if="{{(detail.status==2||detail.status==3)&&detail.express_com}}"><block class="data-v-98a6dfa2"><view class="btn2 data-v-98a6dfa2" data-index="{{index}}" data-event-opts="{{[['tap',[['fahuoedit',['$event']]]]]}}" catchtap="__e">改物流</view></block></block><block wx:if="{{detail.status==2&&detail.freight_type==10}}"><view class="btn2 data-v-98a6dfa2" data-id="{{detail.id}}" data-event-opts="{{[['tap',[['fahuo',['$event']]]]]}}" bindtap="__e">修改物流</view></block><block wx:if="{{detail.status==4}}"><view class="btn2 data-v-98a6dfa2" data-id="{{detail.id}}" data-event-opts="{{[['tap',[['delOrder',['$event']]]]]}}" bindtap="__e">删除</view></block><view class="btn2 data-v-98a6dfa2" data-id="{{detail.id}}" data-event-opts="{{[['tap',[['setremark',['$event']]]]]}}" bindtap="__e">设置备注</view><view data-event-opts="{{[['tap',[['openCopyDialog',['$event']]]]]}}" class="btn2 data-v-98a6dfa2" bindtap="__e">复制信息</view><block wx:if="{{detail.status==1}}"><view class="btn2 data-v-98a6dfa2" data-id="{{detail.id}}" data-event-opts="{{[['tap',[['tuikuan',['$event']]]]]}}" bindtap="__e">退款</view></block></view><uni-popup vue-id="f86e9d08-1" id="dialogSetremark" type="dialog" data-ref="dialogSetremark" class="data-v-98a6dfa2 vue-ref" bind:__l="__l" vue-slots="{{['default']}}"><uni-popup-dialog vue-id="{{('f86e9d08-2')+','+('f86e9d08-1')}}" mode="input" title="设置备注" value="{{detail.remark}}" placeholder="请输入备注" data-event-opts="{{[['^confirm',[['setremarkconfirm']]]]}}" bind:confirm="__e" class="data-v-98a6dfa2" bind:__l="__l"></uni-popup-dialog></uni-popup><uni-popup vue-id="f86e9d08-3" id="copyDialog" type="dialog" data-ref="copyDialog" class="data-v-98a6dfa2 vue-ref" bind:__l="__l" vue-slots="{{['default']}}"><view class="copy-dialog data-v-98a6dfa2"><view class="copy-dialog-header data-v-98a6dfa2"><text class="copy-dialog-title data-v-98a6dfa2">选择要复制的信息</text><text data-event-opts="{{[['tap',[['closeCopyDialog',['$event']]]]]}}" class="copy-dialog-close data-v-98a6dfa2" bindtap="__e">×</text></view><view class="copy-dialog-content data-v-98a6dfa2"><checkbox-group data-event-opts="{{[['change',[['onFieldChange',['$event']]]]]}}" bindchange="__e" class="data-v-98a6dfa2"><block wx:for="{{copyFields}}" wx:for-item="field" wx:for-index="__i1__" wx:key="value"><label class="copy-field-item data-v-98a6dfa2"><checkbox value="{{field.value}}" checked="{{field.checked}}" color="#007AFF" class="data-v-98a6dfa2"></checkbox><text class="copy-field-label data-v-98a6dfa2">{{field.label}}</text></label></block></checkbox-group></view><view class="copy-dialog-footer data-v-98a6dfa2"><view data-event-opts="{{[['tap',[['closeCopyDialog',['$event']]]]]}}" class="copy-dialog-btn cancel data-v-98a6dfa2" bindtap="__e">取消</view><view data-event-opts="{{[['tap',[['confirmCopy',['$event']]]]]}}" class="copy-dialog-btn confirm data-v-98a6dfa2" bindtap="__e">确定</view></view></view></uni-popup><uni-popup vue-id="f86e9d08-4" id="dialogExpress" type="dialog" data-ref="dialogExpress" class="data-v-98a6dfa2 vue-ref" bind:__l="__l" vue-slots="{{['default']}}"><view class="express-dialog data-v-98a6dfa2"><view class="express-dialog-header data-v-98a6dfa2"><text class="express-dialog-title data-v-98a6dfa2">发货</text></view><scroll-view class="express-dialog-content data-v-98a6dfa2" scroll-y="{{true}}"><view class="express-item data-v-98a6dfa2"><view class="express-item-header data-v-98a6dfa2"><text class="express-item-title data-v-98a6dfa2">物流信息 1</text><block wx:if="{{$root.g1>1}}"><view data-event-opts="{{[['tap',[['deletefahuo',[0]]]]]}}" class="express-item-delete data-v-98a6dfa2" catchtap="__e">删除</view></block></view><view class="express-item-body data-v-98a6dfa2"><view class="form-item data-v-98a6dfa2"><text class="form-label data-v-98a6dfa2">快递公司：</text><picker class="form-picker data-v-98a6dfa2" value="{{express_index}}" range="{{expressdata}}" data-event-opts="{{[['change',[['expresschange',['$event']]]]]}}" bindchange="__e"><view class="picker data-v-98a6dfa2">{{expressdata[express_index]}}</view></picker></view><view class="form-item data-v-98a6dfa2"><text class="form-label data-v-98a6dfa2">快递单号：</text><input class="form-input data-v-98a6dfa2" type="text" placeholder="请输入快递单号" data-event-opts="{{[['input',[['setexpressno',['$event']]]]]}}" value="{{express_no}}" bindinput="__e"/></view><block wx:if="{{$root.g2>=2}}"><view class="form-item data-v-98a6dfa2"><text class="form-label data-v-98a6dfa2">选择商品：</text><checkbox-group data-event-opts="{{[['change',[['checkboxChange_fahuo',['$event',0]]]]]}}" class="checkbox-group data-v-98a6dfa2" bindchange="__e"><block wx:for="{{$root.l0}}" wx:for-item="pi" wx:for-index="px" wx:key="px"><label class="checkbox-item data-v-98a6dfa2"><checkbox value="{{pi.g3}}" class="data-v-98a6dfa2"></checkbox><text class="checkbox-label data-v-98a6dfa2">{{pi.$orig.name}}</text></label></block></checkbox-group></view></block></view></view><block wx:for="{{$root.l2}}" wx:for-item="item" wx:for-index="index"><view class="express-item data-v-98a6dfa2"><view class="express-item-header data-v-98a6dfa2"><text class="express-item-title data-v-98a6dfa2">{{"物流信息 "+(index+2)}}</text><view data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" data-event-params="{{({index})}}" class="express-item-delete data-v-98a6dfa2" catchtap="__e">删除</view></view><view class="express-item-body data-v-98a6dfa2"><view class="form-item data-v-98a6dfa2"><text class="form-label data-v-98a6dfa2">快递公司：</text><picker class="form-picker data-v-98a6dfa2" value="{{item.$orig.express_index}}" range="{{expressdata}}" data-event-opts="{{[['change',[['e1',['$event']]]]]}}" data-event-params="{{({index})}}" bindchange="__e"><view class="picker data-v-98a6dfa2">{{expressdata[item.$orig.express_index]}}</view></picker></view><view class="form-item data-v-98a6dfa2"><text class="form-label data-v-98a6dfa2">快递单号：</text><input class="form-input data-v-98a6dfa2" type="text" placeholder="请输入快递单号" data-event-opts="{{[['input',[['e2',['$event']]]]]}}" data-event-params="{{({index})}}" value="{{item.$orig.express_no}}" bindinput="__e"/></view><block wx:if="{{item.g4>=2}}"><view class="form-item data-v-98a6dfa2"><text class="form-label data-v-98a6dfa2">选择商品：</text><checkbox-group data-event-opts="{{[['change',[['e3',['$event']]]]]}}" data-event-params="{{({index})}}" class="checkbox-group data-v-98a6dfa2" bindchange="__e"><block wx:for="{{item.l1}}" wx:for-item="pi" wx:for-index="px" wx:key="px"><label class="checkbox-item data-v-98a6dfa2"><checkbox value="{{pi.g5}}" class="data-v-98a6dfa2"></checkbox><text class="checkbox-label data-v-98a6dfa2">{{pi.$orig.name}}</text></label></block></checkbox-group></view></block></view></view></block></scroll-view><view class="express-dialog-footer data-v-98a6dfa2"><view data-event-opts="{{[['tap',[['dialogExpressClose',['$event']]]]]}}" class="dialog-btn cancel data-v-98a6dfa2" bindtap="__e">取消</view><view data-event-opts="{{[['tap',[['addfahuo',['$event']]]]]}}" class="dialog-btn normal data-v-98a6dfa2" bindtap="__e">新增发货</view><view data-event-opts="{{[['tap',[['confirmfahuo_news',['$event']]]]]}}" class="dialog-btn confirm data-v-98a6dfa2" bindtap="__e">确定</view></view></view></uni-popup><uni-popup vue-id="f86e9d08-5" id="dialogExpress_edit" type="dialog" data-ref="dialogExpress_edit" class="data-v-98a6dfa2 vue-ref" bind:__l="__l" vue-slots="{{['default']}}"><view class="express-dialog data-v-98a6dfa2"><view class="express-dialog-header data-v-98a6dfa2"><text class="express-dialog-title data-v-98a6dfa2">修改物流</text></view><scroll-view class="express-dialog-content data-v-98a6dfa2" scroll-y="{{true}}"><block wx:for="{{$root.l4}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="express-item data-v-98a6dfa2"><view class="express-item-header data-v-98a6dfa2"><text class="express-item-title data-v-98a6dfa2">{{"物流信息 "+(index+1)}}</text><block wx:if="{{item.g6>1}}"><view data-event-opts="{{[['tap',[['deletefahuo',[index]]]]]}}" class="express-item-delete data-v-98a6dfa2" catchtap="__e">删除</view></block></view><view class="express-item-body data-v-98a6dfa2"><view class="form-item data-v-98a6dfa2"><text class="form-label data-v-98a6dfa2">快递公司：</text><picker class="form-picker data-v-98a6dfa2" value="{{item.$orig.express_index}}" range="{{expressdata}}" data-event-opts="{{[['change',[['expresschange_fahuo',['$event',index]]]]]}}" bindchange="__e"><view class="picker data-v-98a6dfa2">{{expressdata[item.$orig.express_index]}}</view></picker></view><view class="form-item data-v-98a6dfa2"><text class="form-label data-v-98a6dfa2">快递单号：</text><input class="form-input data-v-98a6dfa2" type="text" placeholder="请输入快递单号" data-event-opts="{{[['input',[['setexpressno_fahuo',['$event',index]]]]]}}" value="{{item.$orig.express_no}}" bindinput="__e"/></view><block wx:if="{{item.g7>=2}}"><view class="form-item data-v-98a6dfa2"><text class="form-label data-v-98a6dfa2">选择商品：</text><checkbox-group data-event-opts="{{[['change',[['checkboxChange_fahuo',['$event',index]]]]]}}" class="checkbox-group data-v-98a6dfa2" bindchange="__e"><block wx:for="{{item.l3}}" wx:for-item="pi" wx:for-index="px" wx:key="px"><label class="checkbox-item data-v-98a6dfa2"><checkbox value="{{pi.g8}}" checked="{{pi.g9}}" class="data-v-98a6dfa2"></checkbox><text class="checkbox-label data-v-98a6dfa2">{{pi.$orig.name}}</text></label></block></checkbox-group></view></block></view></view></block></scroll-view><view class="express-dialog-footer data-v-98a6dfa2"><view data-event-opts="{{[['tap',[['dialogExpressClose',['$event']]]]]}}" class="dialog-btn cancel data-v-98a6dfa2" bindtap="__e">取消</view><view data-event-opts="{{[['tap',[['addfahuo',['$event']]]]]}}" class="dialog-btn normal data-v-98a6dfa2" bindtap="__e">新增发货</view><view data-event-opts="{{[['tap',[['confirmfahuo_news_edit',['$event']]]]]}}" class="dialog-btn confirm data-v-98a6dfa2" bindtap="__e">确定</view></view></view></uni-popup><uni-popup vue-id="f86e9d08-6" id="dialogPeisong" type="dialog" data-ref="dialogPeisong" class="data-v-98a6dfa2 vue-ref" bind:__l="__l" vue-slots="{{['default']}}"><view class="uni-popup-dialog data-v-98a6dfa2"><view class="uni-dialog-title data-v-98a6dfa2"><text class="uni-dialog-title-text data-v-98a6dfa2">请选择配送员</text></view><view class="uni-dialog-content data-v-98a6dfa2"><view class="data-v-98a6dfa2"><picker style="font-size:28rpx;border:1px #eee solid;padding:10rpx;height:70rpx;border-radius:4px;flex:1;" value="{{index2}}" range="{{peisonguser2}}" data-event-opts="{{[['change',[['peisongChange',['$event']]]]]}}" bindchange="__e" class="data-v-98a6dfa2"><view class="picker data-v-98a6dfa2">{{peisonguser2[index2]}}</view></picker></view></view><view class="uni-dialog-button-group data-v-98a6dfa2"><view data-event-opts="{{[['tap',[['dialogPeisongClose',['$event']]]]]}}" class="uni-dialog-button data-v-98a6dfa2" bindtap="__e"><text class="uni-dialog-button-text data-v-98a6dfa2">取消</text></view><view data-event-opts="{{[['tap',[['confirmPeisong',['$event']]]]]}}" class="uni-dialog-button uni-border-left data-v-98a6dfa2" bindtap="__e"><text class="uni-dialog-button-text uni-button-color data-v-98a6dfa2">确定</text></view></view></view></uni-popup><uni-popup vue-id="f86e9d08-7" id="dialogExpress10" type="dialog" data-ref="dialogExpress10" class="data-v-98a6dfa2 vue-ref" bind:__l="__l" vue-slots="{{['default']}}"><view class="uni-popup-dialog data-v-98a6dfa2"><view class="uni-dialog-title data-v-98a6dfa2"><text class="uni-dialog-title-text data-v-98a6dfa2">发货信息</text></view><view class="uni-dialog-content data-v-98a6dfa2"><view class="data-v-98a6dfa2"><view class="form-item flex data-v-98a6dfa2" style="border-bottom:0;"><view class="f1 data-v-98a6dfa2" style="margin-right:20rpx;">物流单照片</view><view class="f2 data-v-98a6dfa2"><block wx:if="{{express_pic}}"><view class="layui-imgbox data-v-98a6dfa2"><view class="layui-imgbox-close data-v-98a6dfa2" data-index="{{0}}" data-field="express_pic" data-event-opts="{{[['tap',[['removeimg',['$event']]]]]}}" bindtap="__e"><image style="display:block;" src="/static/img/ico-del.png" class="data-v-98a6dfa2"></image></view><view class="layui-imgbox-img data-v-98a6dfa2"><image src="{{express_pic}}" data-url="{{express_pic}}" mode="widthFix" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e" class="data-v-98a6dfa2"></image></view></view></block><block wx:else><view class="uploadbtn data-v-98a6dfa2" style="{{('background:url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 60rpx;background-size:80rpx 80rpx;background-color:#F3F3F3;')}}" data-field="express_pic" data-pernum="1" data-event-opts="{{[['tap',[['uploadimg',['$event']]]]]}}" bindtap="__e"></view></block></view><input type="text" hidden="true" name="express_pic" maxlength="-1" value="{{express_pic}}" class="data-v-98a6dfa2"/></view><view class="flex-y-center flex-x-center data-v-98a6dfa2" style="margin:20rpx 20rpx;"><view style="font-size:28rpx;color:#555;" class="data-v-98a6dfa2">发货人：</view><input style="border:1px #eee solid;padding:10rpx;height:70rpx;border-radius:4px;flex:1;font-size:28rpx;" type="text" placeholder="请输入发货人信息" data-event-opts="{{[['input',[['setexpress_fhname',['$event']]]]]}}" bindinput="__e" class="data-v-98a6dfa2"/></view><view class="flex-y-center flex-x-center data-v-98a6dfa2" style="margin:20rpx 20rpx;"><view style="font-size:28rpx;color:#555;" class="data-v-98a6dfa2">发货地址：</view><input style="border:1px #eee solid;padding:10rpx;height:70rpx;border-radius:4px;flex:1;font-size:28rpx;" type="text" placeholder="请输入发货地址" data-event-opts="{{[['input',[['setexpress_fhaddress',['$event']]]]]}}" bindinput="__e" class="data-v-98a6dfa2"/></view><view class="flex-y-center flex-x-center data-v-98a6dfa2" style="margin:20rpx 20rpx;"><view style="font-size:28rpx;color:#555;" class="data-v-98a6dfa2">收货人：</view><input style="border:1px #eee solid;padding:10rpx;height:70rpx;border-radius:4px;flex:1;font-size:28rpx;" type="text" placeholder="请输入发货人信息" data-event-opts="{{[['input',[['setexpress_shname',['$event']]]]]}}" bindinput="__e" class="data-v-98a6dfa2"/></view><view class="flex-y-center flex-x-center data-v-98a6dfa2" style="margin:20rpx 20rpx;"><view style="font-size:28rpx;color:#555;" class="data-v-98a6dfa2">收货地址：</view><input style="border:1px #eee solid;padding:10rpx;height:70rpx;border-radius:4px;flex:1;font-size:28rpx;" type="text" placeholder="请输入发货地址" data-event-opts="{{[['input',[['setexpress_shaddress',['$event']]]]]}}" bindinput="__e" class="data-v-98a6dfa2"/></view><view class="flex-y-center flex-x-center data-v-98a6dfa2" style="margin:20rpx 20rpx;"><view style="font-size:28rpx;color:#555;" class="data-v-98a6dfa2">备注：</view><input style="border:1px #eee solid;padding:10rpx;height:70rpx;border-radius:4px;flex:1;font-size:28rpx;" type="text" placeholder="请输入备注" data-event-opts="{{[['input',[['setexpress_remark',['$event']]]]]}}" bindinput="__e" class="data-v-98a6dfa2"/></view></view></view><view class="uni-dialog-button-group data-v-98a6dfa2"><view data-event-opts="{{[['tap',[['dialogExpress10Close',['$event']]]]]}}" class="uni-dialog-button data-v-98a6dfa2" bindtap="__e"><text class="uni-dialog-button-text data-v-98a6dfa2">取消</text></view><view data-event-opts="{{[['tap',[['confirmfahuo10',['$event']]]]]}}" class="uni-dialog-button uni-border-left data-v-98a6dfa2" bindtap="__e"><text class="uni-dialog-button-text uni-button-color data-v-98a6dfa2">确定</text></view></view></view></uni-popup><uni-popup vue-id="f86e9d08-8" id="dialogSelectExpress" type="dialog" data-ref="dialogSelectExpress" class="data-v-98a6dfa2 vue-ref" bind:__l="__l" vue-slots="{{['default']}}"><block wx:if="{{express_content}}"><view style="background:#fff;padding:20rpx 30rpx;border-radius:10rpx;width:600rpx;" class="data-v-98a6dfa2"><block wx:for="{{express_content}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="sendexpress data-v-98a6dfa2" style="border-bottom:1px solid #f5f5f5;padding:20rpx 0;"><view class="sendexpress-item data-v-98a6dfa2" style="display:flex;" data-url="{{'/pagesExt/order/logistics?express_com='+item.express_com+'&express_no='+item.express_no}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="flex1 data-v-98a6dfa2" style="color:#121212;">{{item.express_com+" - "+item.express_no}}</view><image style="width:30rpx;height:30rpx;" src="/static/img/arrowright.png" class="data-v-98a6dfa2"></image></view><block wx:if="{{item.express_oglist}}"><view style="margin-top:20rpx;" class="data-v-98a6dfa2"><block wx:for="{{item.express_oglist}}" wx:for-item="item2" wx:for-index="index2" wx:key="index2"><view class="oginfo-item data-v-98a6dfa2" style="display:flex;align-items:center;margin-bottom:10rpx;"><image style="width:50rpx;height:50rpx;margin-right:10rpx;flex-shrink:0;" src="{{item2.pic}}" class="data-v-98a6dfa2"></image><view class="flex1 data-v-98a6dfa2" style="color:#555;">{{item2.name+"("+item2.ggname+")"}}</view></view></block></view></block></view></block></view></block></uni-popup><uni-popup vue-id="f86e9d08-9" id="tuikuandis" type="dialog" data-ref="tuikuandis" class="data-v-98a6dfa2 vue-ref" bind:__l="__l" vue-slots="{{['default']}}"><view class="refund-dialog data-v-98a6dfa2"><view class="uni-dialog-title data-v-98a6dfa2"><text class="uni-dialog-title-text data-v-98a6dfa2">退款</text></view><view class="refund-content data-v-98a6dfa2"><view class="refund-goods-list data-v-98a6dfa2"><view class="section-title _h5 data-v-98a6dfa2">选择退款商品</view><block wx:for="{{$root.l5}}" wx:for-item="item" wx:for-index="idx" wx:key="idx"><view class="refund-goods-item data-v-98a6dfa2"><view class="refund-goods-row data-v-98a6dfa2"><checkbox-group data-event-opts="{{[['change',[['toggleRefundItem',[idx]]]]]}}" class="checkbox-group data-v-98a6dfa2" bindchange="__e"><checkbox value="{{item.g10}}" checked="{{refund.refundNum[idx].selected}}" class="data-v-98a6dfa2"></checkbox></checkbox-group><view class="refund-goods-info data-v-98a6dfa2"><image class="refund-goods-img data-v-98a6dfa2" src="{{item.$orig.pic}}" mode="aspectFill"></image><view class="refund-goods-detail data-v-98a6dfa2"><view class="refund-goods-name data-v-98a6dfa2">{{item.$orig.name}}</view><view class="refund-goods-spec data-v-98a6dfa2">{{item.$orig.ggname}}</view><view class="refund-goods-price data-v-98a6dfa2">{{"￥"+item.$orig.sell_price}}</view></view></view></view><block wx:if="{{refund.refundNum[idx].selected}}"><view class="refund-goods-num data-v-98a6dfa2"><text class="num-title data-v-98a6dfa2">退款数量:</text><view class="num-control data-v-98a6dfa2"><input class="num-input data-v-98a6dfa2" type="number" placeholder="{{'最多'+item.$orig.num+'件'}}" data-event-opts="{{[['input',[['setrefundNum',['$event',idx]]]]]}}" value="{{refund.refundNum[idx].num}}" bindinput="__e"/><text class="num-total data-v-98a6dfa2">{{"共"+item.$orig.num+"件"}}</text></view></view></block></view></block></view><view class="refund-form data-v-98a6dfa2"><view class="section-title _h5 data-v-98a6dfa2">退款说明</view><view class="form-item data-v-98a6dfa2"><text class="form-label data-v-98a6dfa2">退款原因:</text><input class="form-input data-v-98a6dfa2" type="text" placeholder="请输入退款原因" data-event-opts="{{[['input',[['setrefundreason',['$event']]]]]}}" value="{{refund.reason}}" bindinput="__e"/></view><view class="form-item data-v-98a6dfa2"><text class="form-label data-v-98a6dfa2">退款金额:</text><input class="form-input data-v-98a6dfa2" type="digit" placeholder="请输入退款金额" data-event-opts="{{[['input',[['setrefundmoney',['$event']]]]]}}" value="{{refund.money}}" bindinput="__e"/></view></view></view><view class="dialog-footer data-v-98a6dfa2"><button data-event-opts="{{[['tap',[['closeTuikuan',['$event']]]]]}}" class="btn cancel data-v-98a6dfa2" bindtap="__e">取消</button><button data-event-opts="{{[['tap',[['getRefundSubmit',['$event']]]]]}}" class="btn confirm data-v-98a6dfa2" bindtap="__e">确定</button></view></view></uni-popup></block></block><block wx:if="{{loading}}"><loading vue-id="f86e9d08-10" class="data-v-98a6dfa2" bind:__l="__l"></loading></block><dp-tabbar vue-id="f86e9d08-11" opt="{{opt}}" class="data-v-98a6dfa2" bind:__l="__l"></dp-tabbar><popmsg vue-id="f86e9d08-12" data-ref="popmsg" class="data-v-98a6dfa2 vue-ref" bind:__l="__l"></popmsg></view>