<view class="container"><block wx:if="{{purchaseOrder.order_no}}"><view class="order-info"><view class="section-title">进货单信息</view><view class="order-header"><text class="order-no">{{"订单号: "+purchaseOrder.order_no}}</text><text class="order-status" style="{{'color:'+($root.m0)+';'}}">{{$root.m1}}</text></view><view class="order-detail"><view class="detail-row"><text class="detail-label">总金额:</text><text class="detail-value">{{"￥"+purchaseOrder.total_price}}</text></view><view class="detail-row"><text class="detail-label">下单时间:</text><text class="detail-value">{{purchaseOrder.create_time}}</text></view></view></view></block><view class="section-title">可退商品</view><block wx:if="{{$root.g0>0}}"><view class="product-list"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="product-item"><view class="product-checkbox"><checkbox checked="{{item.m2}}" data-event-opts="{{[['tap',[['toggleSelectProduct',['$0'],[[['productList','',index]]]]]]]}}" bindtap="__e"></checkbox></view><image class="product-img" src="{{item.$orig.product_pic||(item.$orig.product_detail?item.$orig.product_detail.pic:item.$orig.pic)}}" mode="aspectFill"></image><view class="product-info"><text class="product-name">{{item.$orig.product_name||(item.$orig.product_detail?item.$orig.product_detail.name:item.$orig.name)}}</text><view class="product-price">{{"￥"+(item.$orig.price||(item.$orig.product_detail?item.$orig.product_detail.sell_price||item.$orig.product_detail.cost_price:item.$orig.sell_price||item.$orig.cost_price||0))}}</view><view class="product-quantity">{{"可退数量: "+(item.$orig.available_for_return!==undefined?item.$orig.available_for_return:item.$orig.quantity)}}</view></view><block wx:if="{{item.m3}}"><view class="product-action"><view class="quantity-control"><view data-event-opts="{{[['tap',[['updateQuantity',['$0',-1],[[['productList','',index]]]]]]]}}" class="minus" bindtap="__e">-</view><input class="quantity-input" type="number" data-event-opts="{{[['input',[['onQuantityInput',['$event','$0'],[[['productList','',index]]]]]]]}}" value="{{item.m4}}" bindinput="__e"/><view data-event-opts="{{[['tap',[['updateQuantity',['$0',1],[[['productList','',index]]]]]]]}}" class="plus" bindtap="__e">+</view></view></view></block></view></block></view></block><block wx:if="{{$root.g1}}"><nodata vue-id="1b42d049-1" bind:__l="__l"></nodata></block><block wx:if="{{loading}}"><loading vue-id="1b42d049-2" bind:__l="__l"></loading></block><view class="remark-section"><view class="section-title">退货原因</view><textarea class="remark-input" placeholder="请输入退货原因（必填）" data-event-opts="{{[['input',[['__set_model',['','remark','$event',[]]]]]]}}" value="{{remark}}" bindinput="__e"></textarea></view><block wx:if="{{$root.g2>0}}"><view class="bottom-bar"><view class="order-summary"><text class="summary-text">{{"已选择 "+$root.m5+" 种商品"}}</text><text class="summary-price">{{"退款: ￥"+$root.m6}}</text></view><view class="order-actions"><view data-event-opts="{{[['tap',[['submitOrder',['$event']]]]]}}" class="action-btn submit" bindtap="__e">提交申请</view></view></view></block><popmsg class="vue-ref" vue-id="1b42d049-3" data-ref="popmsg" bind:__l="__l"></popmsg></view>