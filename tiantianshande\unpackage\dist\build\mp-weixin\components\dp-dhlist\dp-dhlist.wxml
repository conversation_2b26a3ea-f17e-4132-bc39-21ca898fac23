<view class="dp-dhlist" style="{{'color:'+(params.color)+';'+('background-color:'+(params.bgcolor)+';')+('margin:'+(params.margin_y*2.2+'rpx '+params.margin_x*2.2+'rpx 0')+';')}}"><block wx:for="{{data}}" wx:for-item="item" wx:for-index="__i0__" wx:key="id"><block><view class="dp-dhlist-item" style="{{'padding:'+(params.padding_y*2.2+'rpx '+params.padding_x*2.2+'rpx')+';'}}" data-url="{{item.hrefurl}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="dp-dhlist-text1" style="{{'font-size:'+(params.fontsize1*2+'rpx')+';'+('color:'+(params.color1)+';')}}"><block wx:if="{{item.showicon==1}}"><image class="image" style="{{'width:'+(item.iconsize*2+'rpx')+';'}}" src="{{item.imgurl}}" mode="widthFix"></image></block><block wx:if="{{item.title1!=''}}"><text class="dp-dhlist-title1" style="{{'margin-left:'+(params.titlemarginleft*2+'rpx')+';'}}">{{item.title1}}</text></block></view><view class="dp-dhlist-text2" style="{{'font-size:'+(params.fontsize2*2+'rpx')+';'+('color:'+(params.color2)+';')}}">{{item.title2}}</view><block wx:if="{{params.arrowshow==1}}"><image style="width:32rpx;height:32rpx;" src="/static/img/arrowright.png"></image></block></view></block></block></view>