<view class="container"><block wx:if="{{isload}}"><block><dd-tab vue-id="04103307-1" itemdata="{{['全部','待处理','处理中','已处理','待支付']}}" itemst="{{['all','0','1','2','10']}}" st="{{st}}" isfixed="{{true}}" data-event-opts="{{[['^changetab',[['changetab']]]]}}" bind:changetab="__e" bind:__l="__l"></dd-tab><view style="width:100%;height:90rpx;"></view><view class="content" id="datalist"><block wx:for="{{datalist}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="item"><view class="f1"><view class="flex" style="justify-content:space-between;"><text class="t1" data-url="{{'myformdetail?id='+item.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">{{"工单类型："+item.title}}</text><view class="f2"><block wx:if="{{item.status==0&&(!item.payorderid||item.paystatus==1)}}"><text class="t1" style="color:#88e;">待处理</text></block><block wx:if="{{item.status==0&&item.payorderid&&item.paystatus==0}}"><text class="t1" style="color:red;">待支付</text></block><block wx:if="{{item.status==1}}"><text class="t1" style="color:green;">处理中</text></block><block wx:if="{{item.status==2}}"><text class="t1" style="color:red;">已处理</text></block><block wx:if="{{item.status==-1}}"><text class="t1" style="color:red;">已驳回</text></block></view></view><view class="flex" style="justify-content:space-between;margin-top:15rpx;"><text class="t2" data-url="{{'myformdetail?id='+item.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">{{"提交时间："+item.createtime}}</text><block wx:if="{{!item.payorderid||item.payorderid&&item.paystatus==1}}"><view class="jindu" data-id="{{item.id}}" data-event-opts="{{[['tap',[['jindu',['$event']]]]]}}" bindtap="__e">查看进度</view></block></view></view></view></block></view></block></block><block wx:if="{{ishowjindu}}"><view class="modal"><view class="modal_jindu"><view data-event-opts="{{[['tap',[['close',['$event']]]]]}}" class="close" bindtap="__e"><image src="{{pre_url+'/static/img/close.png'}}"></image></view><block wx:if="{{$root.g0>0}}"><block><block wx:for="{{jdlist}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="item"><view class="f1"><image src="{{'/static/img/jindu'+(index==0?'2':'1')+'.png'}}"></image></view><view class="f2"><text class="t2">{{'时间：'+item.time}}</text><text class="t1">{{item.desc+"("+item.remark+')'}}</text></view></view></block></block></block><block wx:else><block><view style="font-size:14px;color:#f05555;padding:10px;">等待处理</view></block></block></view></view></block><block wx:if="{{nodata}}"><nodata vue-id="04103307-2" bind:__l="__l"></nodata></block><block wx:if="{{nomore}}"><nomore vue-id="04103307-3" bind:__l="__l"></nomore></block><block wx:if="{{loading}}"><loading vue-id="04103307-4" bind:__l="__l"></loading></block><dp-tabbar vue-id="04103307-5" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="04103307-6" data-ref="popmsg" bind:__l="__l"></popmsg></view>