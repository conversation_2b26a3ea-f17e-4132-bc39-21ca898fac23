<view class="container"><block wx:if="{{isload}}"><block><view class="head-bg"><block wx:if="{{detail.check_status==0&&detail.status==0}}"><view class="title">预定成功，请支付</view></block><block wx:if="{{detail.check_status==0&&detail.status>0}}"><view class="title">预定成功，等待审核</view></block><block wx:if="{{detail.check_status==1&&detail.status>0}}"><view class="title">预定成功</view></block><block wx:if="{{detail.check_status==1&&detail.status==0}}"><view class="title">预定成功，请支付</view></block><block wx:if="{{detail.check_status==-1&&detail.status>0}}"><view class="title">预定失败，商家驳回</view></block><block wx:if="{{detail.check_status!=-1}}"><view class="text-center mt20">请在预定时间20分钟内到店。</view></block><block wx:if="{{detail.check_status==-1&&detail.status==0}}"><view class="text-center mt20">请重新预定。</view></block></view><view class="card-view"><view class="card-wrap"><view class="card-title">{{business.name}}</view><view class="mt20">{{business.address}}</view></view><view class="card-wrap"><view class="card-title">预定信息</view><view class="info-item mt"><view class="t1">预定人</view><view class="t2">{{detail.linkman}}</view></view><view class="info-item"><view class="t1">手机号</view><view class="t2">{{detail.tel}}</view></view><view class="info-item"><view class="t1">预定时间</view><view class="t2">{{detail.booking_time}}</view></view><view class="info-item"><view class="t1">用餐人数</view><view class="t2">{{detail.seat}}</view></view><view class="info-item"><view class="t1">预定桌台</view><view class="t2">{{detail.tableName}}</view></view><view class="info-item info-textarea"><view class="t1">备注信息</view><view class="t2">{{detail.message}}</view></view></view></view><view class="btn-view button-sp-area"><button class="btn-default" type="default" data-event-opts="{{[['tap',[['cancel',['$event']]]]]}}" bindtap="__e">取消</button><block wx:if="{{detail.status==0}}"><button type="primary" data-url="{{'/pages/pay/pay?id='+detail.payorderid}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">去支付</button></block></view></block></block><block wx:if="{{loading}}"><loading vue-id="11d89aab-1" bind:__l="__l"></loading></block><dp-tabbar vue-id="11d89aab-2" opt="{{opt}}" bind:__l="__l"></dp-tabbar></view>