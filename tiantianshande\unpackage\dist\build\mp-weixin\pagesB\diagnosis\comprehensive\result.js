require('../../common/vendor.js');(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pagesB/diagnosis/comprehensive/result"],{"5dbc":function(e,t,s){"use strict";s.r(t);var i=s("f164"),a=s("7cba");for(var n in a)["default"].indexOf(n)<0&&function(e){s.d(t,e,(function(){return a[e]}))}(n);s("7f32");var r=s("828b"),o=Object(r["a"])(a["default"],i["b"],i["c"],!1,null,null,null,!1,i["a"],void 0);t["default"]=o.exports},"5efb":function(e,t,s){"use strict";(function(e,t){var i=s("47a9");s("06e9");i(s("3240"));var a=i(s("5dbc"));e.__webpack_require_UNI_MP_PLUGIN__=s,t(a.default)}).call(this,s("3223")["default"],s("df3c")["createPage"])},7135:function(e,t,s){},"7cba":function(e,t,s){"use strict";s.r(t);var i=s("f7b8"),a=s.n(i);for(var n in i)["default"].indexOf(n)<0&&function(e){s.d(t,e,(function(){return i[e]}))}(n);t["default"]=a.a},"7f32":function(e,t,s){"use strict";var i=s("7135"),a=s.n(i);a.a},f164:function(e,t,s){"use strict";s.d(t,"b",(function(){return i})),s.d(t,"c",(function(){return a})),s.d(t,"a",(function(){}));var i=function(){var e=this.$createElement,t=(this._self._c,this.recommendVideos.length),s=this.t("color1"),i=this.t("color1rgb");this.$mp.data=Object.assign({},{$root:{g0:t,m0:s,m1:i}})},a=[]},f7b8:function(e,t,s){"use strict";(function(e){var i=s("47a9");Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var a=i(s("3b2d")),n={data:function(){return{recordId:"",resultData:{},activeTab:"comprehensive",tongueResult:{},faceResult:{},sublingualResult:{},comprehensiveAnalysis:{},tongueAnalysis:{},faceAnalysis:{},sublingualAnalysis:{},careAdvice:{},recommendVideos:[]}},computed:{currentDate:function(){var e=new Date;return e.getFullYear()+"-"+String(e.getMonth()+1).padStart(2,"0")+"-"+String(e.getDate()).padStart(2,"0")},scoreLevelClass:function(){if(!this.resultData.comprehensive_score)return"normal";var e=parseInt(this.resultData.comprehensive_score);return e>=80?"excellent":e>=60?"good":e>=40?"normal":"poor"},scoreLevelText:function(){if(!this.resultData.comprehensive_score)return"正常";var e=parseInt(this.resultData.comprehensive_score);return e>=80?"优秀":e>=60?"良好":e>=40?"正常":"需要关注"}},onLoad:function(e){e.recordId&&(this.recordId=e.recordId,this.getAnalysisRecord())},methods:{getAnalysisRecord:function(){var t=this;console.log("2025-07-17 INFO-[comprehensive-result][getAnalysisRecord_001] 开始获取综合诊疗分析记录");var s=getApp();e.showLoading({title:"加载中..."}),s.post("ApiComprehensiveAnalysis/getRecord",{id:this.recordId},(function(s){e.hideLoading(),console.log("2025-07-17 INFO-[comprehensive-result][getAnalysisRecord_002] 获取综合诊疗记录结果:",s),s&&1===s.code?(console.log("2025-07-17 INFO-[comprehensive-result][getAnalysisRecord_003] 获取记录成功，开始解析数据"),t.resultData=s.data,t.parseNewAnalysisResult()):(console.error("2025-07-17 ERROR-[comprehensive-result][getAnalysisRecord_004] 获取记录失败:",null===s||void 0===s?void 0:s.msg),e.showToast({title:(null===s||void 0===s?void 0:s.msg)||"获取记录失败",icon:"none"}),t.loadMockData())}),(function(s){e.hideLoading(),console.error("2025-07-17 ERROR-[comprehensive-result][getAnalysisRecord_005] 获取记录接口调用失败:",s),e.showToast({title:"网络错误，请重试",icon:"none"}),t.loadMockData()}))},parseNewAnalysisResult:function(){try{console.log("2025-07-17 INFO-[comprehensive-result][parseNewAnalysisResult_001] 开始解析新综合诊疗分析结果"),console.log("2025-07-17 INFO-[comprehensive-result][parseNewAnalysisResult_002] 原始数据:",this.resultData);var e=this.resultData.analysis_result||{},t={};if("string"===typeof e?t=JSON.parse(e):"object"===(0,a.default)(e)&&(t=e),console.log("2025-07-17 INFO-[comprehensive-result][parseNewAnalysisResult_003] 解析后的分析数据:",t),t.raw_report_data)console.log("2025-07-17 INFO-[comprehensive-result][parseNewAnalysisResult_004] 发现新接口数据结构"),this.parseNewApiData(t.raw_report_data);else{if(!t.physique_name&&!t.score)return console.log("2025-07-17 INFO-[comprehensive-result][parseNewAnalysisResult_006] 使用旧版解析方法"),void this.parseAnalysisResult();console.log("2025-07-17 INFO-[comprehensive-result][parseNewAnalysisResult_005] 发现API数据结构"),this.parseNewApiData(t)}}catch(s){console.error("2025-07-17 ERROR-[comprehensive-result][parseNewAnalysisResult_007] 解析新综合诊疗结果失败:",s),this.parseAnalysisResult()}},parseNewApiData:function(e){console.log("2025-07-17 INFO-[comprehensive-result][parseNewApiData_001] 开始解析新API数据:",e),e.score&&(this.resultData.comprehensive_score=e.score),e.features&&Array.isArray(e.features)?this.parseNewComprehensiveFeatures(e.features):this.parseDefaultFeatures(),this.parseNewComprehensiveAnalysis(e),e.advices?this.parseNewComprehensiveCareAdvice(e.advices):this.parseDefaultCareAdvice()},parseNewComprehensiveFeatures:function(e){console.log("2025-07-17 INFO-[comprehensive-result][parseNewComprehensiveFeatures_001] 解析综合特征:",e);var t=e.filter((function(e){return"舌部"===e.feature_category})),s=e.filter((function(e){return"面部"===e.feature_category})),i=e.filter((function(e){return"舌下"===e.feature_category}));if(this.resultData.tongue_image&&t.length>0){var a=this.calculateFeatureScore(t);this.tongueResult={constitution_type:this.getConstitutionFromFeatures(t),constitution_score:a},this.tongueAnalysis={detailed_analysis:this.generateFeatureAnalysis(t,"舌诊")}}if(this.resultData.face_image&&s.length>0){var n=this.calculateFeatureScore(s);this.faceResult={face_status:this.getFaceStatusFromFeatures(s),face_score:n},this.faceAnalysis={detailed_analysis:this.generateFeatureAnalysis(s,"面诊")}}if(this.resultData.sublingual_image&&i.length>0){var r=this.calculateFeatureScore(i);this.sublingualResult={vein_status:this.getVeinStatusFromFeatures(i),vein_score:r},this.sublingualAnalysis={detailed_analysis:this.generateFeatureAnalysis(i,"舌下脉络")}}this.getRecommendVideos()},parseNewComprehensiveAnalysis:function(e){e.score;var t=e.physique_name||"未知体质",s=e.physique_analysis||"体质分析中...",i=e.risk_warning||"请注意保持健康的生活方式";this.comprehensiveAnalysis={overall_health:s,constitution_type:t,constitution_desc:this.getConstitutionDescription(t),health_advice:i}},parseNewComprehensiveCareAdvice:function(e){console.log("2025-07-17 INFO-[comprehensive-result][parseNewComprehensiveCareAdvice_001] 解析综合调理建议:",e);var t="保持均衡饮食，多吃新鲜蔬果";e.food&&Array.isArray(e.food)&&(t=e.food.map((function(e){return e.advice||e.title})).join("；"));var s="适当进行有氧运动，增强体质";e.sport&&Array.isArray(e.sport)&&(s=e.sport.map((function(e){return e.advice||e.title})).join("；"));var i="保持规律作息，早睡早起";e.sleep&&Array.isArray(e.sleep)&&(i=e.sleep.map((function(e){return e.advice||e.title})).join("；")),this.careAdvice={diet:t,exercise:s,sleep:i}},calculateFeatureScore:function(e){if(!e||0===e.length)return 85;var t=0;e.forEach((function(e){"正常"===e.feature_situation&&t++}));var s=t/e.length;return Math.round(60+40*s)},getConstitutionFromFeatures:function(e){var t=e.filter((function(e){return"异常"===e.feature_situation}));return 0===t.length?"平和质":t.length<=2?"偏颇体质":"需要调理"},getFaceStatusFromFeatures:function(e){var t=e.filter((function(e){return"异常"===e.feature_situation}));return 0===t.length?"气血充足":t.length<=1?"气血较好":"需要调理"},getVeinStatusFromFeatures:function(e){var t=e.filter((function(e){return"异常"===e.feature_situation}));return 0===t.length?"血液循环良好":t.length<=1?"血液循环较好":"血液循环需要改善"},generateFeatureAnalysis:function(e,t){var s=e.filter((function(e){return"正常"===e.feature_situation})),i=e.filter((function(e){return"异常"===e.feature_situation})),a="".concat(t,"显示：");if(s.length>0){var n=s.map((function(e){return e.feature_name})).join("、");a+="".concat(n,"正常；")}if(i.length>0){var r=i.map((function(e){return e.feature_name})).join("、");a+="".concat(r,"需要关注。")}else a+="整体状况良好。";return a},getConstitutionDescription:function(e){return{"平和质":"体质平和，阴阳气血调和，脏腑功能正常，适应能力强。","气虚质":"元气不足，以疲乏、气短、自汗等气虚表现为主要特征。","阳虚质":"阳气不足，以畏寒怕冷、手足不温等虚寒表现为主要特征。","阴虚质":"阴液亏少，以口燥咽干、手足心热等虚热表现为主要特征。","痰湿质":"痰湿凝聚，以形体肥胖、腹部肥满、口黏苔腻等痰湿表现为主要特征。","湿热质":"湿热内蕴，以面垢油腻、口苦、苔黄腻等湿热表现为主要特征。","血瘀质":"血行不畅，以肤色晦黯、舌质紫黯等血瘀表现为主要特征。","气郁质":"气机郁滞，以神情抑郁、忧虑脆弱等气郁表现为主要特征。","特禀质":"先天失常，以生理缺陷、过敏反应等为主要特征。"}[e]||"体质特征分析中，请咨询专业医师。"},loadMockData:function(){var e,t,s;this.resultData={comprehensive_score:85,tongue_image:(null===(e=this.resultData)||void 0===e?void 0:e.tongue_image)||"",face_image:(null===(t=this.resultData)||void 0===t?void 0:t.face_image)||"",sublingual_image:(null===(s=this.resultData)||void 0===s?void 0:s.sublingual_image)||""},this.parseDefaultFeatures(),this.parseDefaultCareAdvice()},parseDefaultFeatures:function(){this.resultData.tongue_image&&(this.tongueResult={constitution_type:"平和质",constitution_score:85},this.tongueAnalysis={detailed_analysis:"舌质淡红，舌苔薄白，舌体大小适中，说明脾胃功能正常，气血充足。"}),this.resultData.face_image&&(this.faceResult={face_status:"气血充足",face_score:82},this.faceAnalysis={detailed_analysis:"面色红润有光泽，眼神明亮有神，唇色淡红，说明气血运行良好，精神状态佳。"}),this.resultData.sublingual_image&&(this.sublingualResult={vein_status:"血液循环良好",vein_score:88},this.sublingualAnalysis={detailed_analysis:"舌下脉络清晰可见，颜色正常，粗细适中，说明血液循环良好，无明显瘀血现象。"}),this.comprehensiveAnalysis={overall_health:"根据综合分析，您的整体健康状况良好，气血运行正常，脏腑功能协调。",constitution_type:"平和质",constitution_desc:"体质平和，阴阳气血调和，脏腑功能正常，适应能力强。",health_advice:"保持现有的良好生活习惯，注意饮食均衡，适量运动，保持心情愉悦。"}},parseDefaultCareAdvice:function(){this.careAdvice={diet:"保持均衡饮食，多吃新鲜蔬果，适量摄入优质蛋白质，少食辛辣刺激食物。",sleep:"保持规律作息，早睡早起，保证充足睡眠，避免熬夜。",exercise:"适当进行有氧运动，如散步、慢跑、太极拳等，增强体质。"}},parseAnalysisResult:function(){try{var e=JSON.parse(this.resultData.analysis_result||"{}");this.resultData.tongue_image&&(this.tongueResult={constitution_type:e.constitution_type||"平和质",constitution_score:e.constitution_score||85},this.tongueAnalysis={detailed_analysis:e.tongue_detailed||"舌质淡红，舌苔薄白，舌体大小适中，说明脾胃功能正常，气血充足。"}),this.resultData.face_image&&(this.faceResult={face_status:e.face_status||"气血充足",face_score:e.face_score||82},this.faceAnalysis={detailed_analysis:e.face_detailed||"面色红润有光泽，眼神明亮有神，唇色淡红，说明气血运行良好，精神状态佳。"}),this.resultData.sublingual_image&&(this.sublingualResult={vein_status:e.vein_status||"血液循环良好",vein_score:e.vein_score||88},this.sublingualAnalysis={detailed_analysis:e.sublingual_detailed||"舌下脉络清晰可见，颜色正常，粗细适中，说明血液循环良好，无明显瘀血现象。"}),this.comprehensiveAnalysis={overall_health:e.overall_health||"根据舌诊、面诊和舌下脉络综合分析，您的整体健康状况良好，气血运行正常，脏腑功能协调。",constitution_type:e.comprehensive_constitution||"平和质",constitution_desc:e.comprehensive_desc||"体质平和，阴阳气血调和，脏腑功能正常，适应能力强。",health_advice:e.comprehensive_advice||"保持现有的良好生活习惯，注意饮食均衡，适量运动，保持心情愉悦。"},this.careAdvice={diet:e.diet_advice||"保持均衡饮食，多吃新鲜蔬果，适量摄入优质蛋白质，少食辛辣刺激食物。",sleep:e.sleep_advice||"保持规律作息，早睡早起，保证充足睡眠，避免熬夜。",exercise:e.exercise_advice||"适当进行有氧运动，如散步、慢跑、太极拳等，增强体质。"}}catch(t){console.error("解析分析结果失败:",t)}},switchTab:function(e){this.activeTab=e},goBack:function(){e.navigateBack()},shareResult:function(){e.showToast({title:"分享功能开发中",icon:"none"})},getRecommendVideos:function(){var e=this;console.log("2025-01-31 INFO-[comprehensive-result][getRecommendVideos_001] 开始获取推荐视频");var t="",s=0;if(this.tongueResult&&this.tongueResult.constitution_type&&(t=this.tongueResult.constitution_type),this.tongueResult&&this.tongueResult.constitution_score&&(s=parseInt(this.tongueResult.constitution_score)),t||s){var i=getApp();i.post("ApiSheZhen/getRecommendProducts",{constitution_type:t,constitution_score:s},(function(t){if(console.log("2025-01-31 INFO-[comprehensive-result][getRecommendVideos_003] 获取推荐视频结果:",t),t&&1===t.code&&t.data&&t.data.products){var s=t.data.products.filter((function(e){return"video"===e.type}));e.recommendVideos=s,console.log("2025-01-31 INFO-[comprehensive-result][getRecommendVideos_004] 获取到推荐视频数量:",s.length)}else console.log("2025-01-31 INFO-[comprehensive-result][getRecommendVideos_005] 无推荐视频数据")}),(function(e){console.error("2025-01-31 ERROR-[comprehensive-result][getRecommendVideos_006] 获取推荐视频失败:",e)}))}else console.log("2025-01-31 INFO-[comprehensive-result][getRecommendVideos_002] 无体质信息，跳过视频推荐")},playVideo:function(t){console.log("2025-01-31 INFO-[comprehensive-result][playVideo_001] 播放视频:",t),t.url?e.navigateTo({url:"/pagesExt/shortvideo/detail?id=".concat(t.id)}):e.showToast({title:"视频地址无效",icon:"none"})}}};t.default=n}).call(this,s("df3c")["default"])}},[["5efb","common/runtime","common/vendor"]]]);