<view class="container"><block wx:if="{{isload}}"><block><view class="search-container" style="{{(history_show?'height:100%;':'')}}"><view class="topsearch flex-y-center"><view class="f1 flex-y-center"><image class="img" src="{{pre_url+'/static/img/search_ico.png'}}"></image><input placeholder="搜索感兴趣的商品" placeholder-style="font-size:24rpx;color:#C2C2C2" data-event-opts="{{[['confirm',[['searchConfirm',['$event']]]],['input',[['searchChange',['$event']]]],['focus',[['searchFocus',['$event']]]]]}}" value="{{keyword}}" bindconfirm="__e" bindinput="__e" bindfocus="__e"/></view></view><view hidden="{{!(history_show)}}" class="search-history"><view><text class="search-history-title">最近搜索</text><view data-event-opts="{{[['tap',[['deleteSearchHistory',['$event']]]]]}}" class="delete-search-history" bindtap="__e"><image style="width:36rpx;height:36rpx;" src="{{pre_url+'/static/img/del.png'}}"></image></view></view><view class="search-history-list"><block wx:for="{{history_list}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="search-history-item" data-value="{{item}}" data-event-opts="{{[['tap',[['historyClick',['$event']]]]]}}" bindtap="__e">{{item+''}}</view></block><block wx:if="{{$root.g0}}"><view class="flex-y-center"><image style="width:36rpx;height:36rpx;margin-right:10rpx;" src="{{pre_url+'/static/img/tanhao.png'}}"></image>暂无记录</view></block></view></view><view hidden="{{!(!history_show)}}" class="search-navbar"><view class="search-navbar-item" style="{{(!field||field=='sort'?'color:'+$root.m0:'')}}" data-field="sort" data-order="desc" data-event-opts="{{[['tap',[['sortClick',['$event']]]]]}}" catchtap="__e">综合</view><view class="search-navbar-item" style="{{(field=='sales'?'color:'+$root.m1:'')}}" data-field="sales" data-order="desc" data-event-opts="{{[['tap',[['sortClick',['$event']]]]]}}" catchtap="__e">销量</view><view class="search-navbar-item" data-field="sell_price" data-order="{{order=='asc'?'desc':'asc'}}" data-event-opts="{{[['tap',[['sortClick',['$event']]]]]}}" catchtap="__e"><text style="{{(field=='sell_price'?'color:'+$root.m2:'')}}">价格</text><text class="iconfont iconshangla" style="{{(field=='sell_price'&&order=='asc'?'color:'+$root.m3:'')}}"></text><text class="iconfont icondaoxu" style="{{(field=='sell_price'&&order=='desc'?'color:'+$root.m4:'')}}"></text></view><view data-event-opts="{{[['tap',[['showDrawer',['showRight']]]]]}}" class="search-navbar-item flex-x-center flex-y-center" catchtap="__e">筛选<text class="{{['iconfont iconshaixuan '+(showfilter?'active':'')]}}"></text></view></view><uni-drawer class="vue-ref" vue-id="b98fe2d2-1" mode="right" width="{{280}}" data-ref="showRight" data-event-opts="{{[['^change',[['change',['$event','showRight']]]]]}}" bind:change="__e" bind:__l="__l" vue-slots="{{['default']}}"><scroll-view class="filter-scroll-view filter-page" scroll-y="true"><view class="filter-scroll-view-box"><view class="search-filter"><view class="filter-title">筛选</view><view class="filter-content-title">商品分组</view><view class="search-filter-content"><view class="filter-item" style="{{(catchegid==''?'color:'+$root.m5+';background:rgba('+$root.m6+',0.1)':'')}}" data-gid data-event-opts="{{[['tap',[['groupClick',['$event']]]]]}}" catchtap="__e">全部</view><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><view class="filter-item" style="{{(catchegid==item.$orig.id?'color:'+item.m7+';background:rgba('+item.m8+',0.1)':'')}}" data-gid="{{item.$orig.id}}" data-event-opts="{{[['tap',[['groupClick',['$event']]]]]}}" catchtap="__e">{{item.$orig.name}}</view></block></block></view><block wx:if="{{!bid||bid<=0}}"><block><view class="filter-content-title">商品分类</view><view class="search-filter-content"><view class="filter-item" style="{{(catchecid==oldcid?'color:'+$root.m9+';background:rgba('+$root.m10+',0.1)':'')}}" data-cid="{{oldcid}}" data-event-opts="{{[['tap',[['cateClick',['$event']]]]]}}" catchtap="__e">全部</view><block wx:for="{{$root.l1}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><view class="filter-item" style="{{(catchecid==item.$orig.id?'color:'+item.m11+';background:rgba('+item.m12+',0.1)':'')}}" data-cid="{{item.$orig.id}}" data-event-opts="{{[['tap',[['cateClick',['$event']]]]]}}" catchtap="__e">{{item.$orig.name}}</view></block></block></view></block></block><block wx:else><block><view class="filter-content-title">商品分类</view><view class="search-filter-content"><view class="filter-item" style="{{(catchecid2==oldcid2?'color:'+$root.m13+';background:rgba('+$root.m14+',0.1)':'')}}" data-cid2="{{oldcid2}}" data-event-opts="{{[['tap',[['cate2Click',['$event']]]]]}}" catchtap="__e">全部</view><block wx:for="{{$root.l2}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><view class="filter-item" style="{{(catchecid2==item.$orig.id?'color:'+item.m15+';background:rgba('+item.m16+',0.1)':'')}}" data-cid2="{{item.$orig.id}}" data-event-opts="{{[['tap',[['cate2Click',['$event']]]]]}}" catchtap="__e">{{item.$orig.name}}</view></block></block></view></block></block><view class="search-filter-btn"><view data-event-opts="{{[['tap',[['filterReset',['$event']]]]]}}" class="btn" bindtap="__e">重置</view><view data-event-opts="{{[['tap',[['filterConfirm',['$event']]]]]}}" class="btn2" style="{{'background:'+($root.m17)+';'}}" bindtap="__e">确定</view></view></view></view></scroll-view></uni-drawer></view><view class="product-container"><block wx:if="{{$root.g1}}"><block><view style="width:100%;"><view class="dp-product-normal-item"><block wx:for="{{$root.l3}}" wx:for-item="item" wx:for-index="index" wx:key="id"><view class="item" style="{{('background:'+probgcolor+';'+(showstyle==2?'width:49%;margin-right:'+(index%2==0?'2%':0):showstyle==3?'width:32%;margin-right:'+(index%3!=2?'2%':0):'width:100%'))}}"><view class="product-pic"><image class="image" src="{{item.$orig.pic}}" mode="widthFix"></image><block wx:if="{{saleimg!=''}}"><image class="saleimg" src="{{saleimg}}" mode="widthFix"></image></block></view><view class="product-info"><block wx:if="{{item.$orig.name}}"><view class="p1">{{item.$orig.name}}</view></block><block wx:if="{{(showbname=='1'||showbdistance=='1')&&item.$orig.binfo}}"><view class="binfo flex-bt"><view class="flex-y-center b1"><block wx:if="{{showbname=='1'}}"><block><image class="t1" src="{{item.$orig.binfo.logo}}"></image><text class="t2">{{item.$orig.binfo.name}}</text></block></block></view><block wx:if="{{showbdistance=='1'&&item.$orig.binfo.distance}}"><view class="b2 t2">{{item.$orig.binfo.distance}}</view></block></view></block><block wx:if="{{showcommission==1&&item.$orig.commission_price>0}}"><view class="couponitem"><view class="f1"><view class="t" style="{{'background:'+('rgba('+item.m18+',0.1)')+';'+('color:'+(item.m19)+';')}}"><text>{{item.m20+item.$orig.commission_price+item.$orig.commission_desc}}</text></view></view></view></block><block wx:if="{{showstyle==2}}"><view><block wx:if="{{params.brand==1&&item.$orig.brand}}"><view class="field_buy"><label style="width:80rpx;" class="_span">品牌：</label><label class="_span">{{item.$orig.brand}}</label></view></block><block wx:if="{{params.barcode==1&&item.$orig.barcode}}"><view class="field_buy"><label style="width:80rpx;" class="_span">货号：</label><label class="_span">{{item.$orig.barcode}}</label></view></block><block wx:if="{{params.guige==1&&item.$orig.ggname}}"><view class="field_buy"><label style="width:80rpx;" class="_span">规格：</label><label class="_span">{{item.$orig.ggname}}</label></view></block><block wx:if="{{params.unit==1&&item.$orig.unit}}"><view class="field_buy"><label style="width:80rpx;" class="_span">单位：</label><label class="_span">{{item.$orig.unit}}</label></view></block><block wx:if="{{params.ggstock==1}}"><view class="field_buy"><label style="width:80rpx;" class="_span">库存：</label><label class="_span">{{item.$orig.ggstock}}</label></view></block><block wx:if="{{params.valid_time==1&&item.$orig.valid_time}}"><view class="field_buy"><label style="width:80rpx;" class="_span">有效期：</label><label class="_span">{{item.$orig.valid_time}}</label></view></block><block wx:if="{{params.remark==1&&item.$orig.remark}}"><view class="field_buy"><label style="width:80rpx;" class="_span">备注：</label><label class="_span">{{item.$orig.remark}}</label></view></block></view></block><block wx:if="{{(showstyle=='2'||showstyle=='3')&&item.$orig.price_type!=1&&item.$orig.show_cost=='1'}}"><view style="{{'color:'+(item.$orig.cost_color?item.$orig.cost_color:'#999')+';'+('font-size:'+('36rpx')+';')}}"><text style="font-size:24rpx;">{{item.$orig.cost_tag}}</text>{{item.$orig.cost_price}}</view></block><view class="p2"><block wx:if="{{showprice!='0'&&(item.$orig.price_type!=1||item.$orig.sell_price>0)}}"><view class="{{['p2-1',params.style=='1'?'flex-bt flex-y-center':'']}}"><block wx:if="{{showstyle=='1'&&item.$orig.price_type!=1&&item.$orig.show_cost=='1'}}"><view style="{{'color:'+(item.$orig.cost_color?item.$orig.cost_color:'#999')+';'+('font-size:'+('36rpx')+';')}}"><text style="font-size:24rpx;">{{item.$orig.cost_tag}}</text>{{item.$orig.cost_price}}</view></block><block wx:if="{{!item.$orig.show_sellprice||item.$orig.show_sellprice&&item.$orig.show_sellprice==true||item.$orig.usd_sellprice}}"><view class="flex-y-center"><view class="t1" style="{{'color:'+(item.$orig.price_color?item.$orig.price_color:item.m21)+';'}}"><block wx:if="{{item.$orig.usd_sellprice}}"><block><text style="font-size:24rpx;">$</text>{{item.$orig.usd_sellprice+''}}<text style="font-size:28rpx;"><text style="font-size:24rpx;">￥</text>{{item.$orig.sell_price}}</text><block wx:if="{{item.$orig.product_unit}}"><text style="font-size:24rpx;">{{"/"+item.$orig.product_unit}}</text></block></block></block><block wx:else><block><text style="font-size:24rpx;">{{item.$orig.price_tag?item.$orig.price_tag:'￥'}}</text>{{item.$orig.sell_price}}<block wx:if="{{item.$orig.product_unit}}"><text style="font-size:24rpx;">{{"/"+item.$orig.product_unit}}</text></block></block></block></view><block wx:if="{{(!item.$orig.show_sellprice||item.$orig.show_sellprice&&item.$orig.show_sellprice==true)&&item.$orig.market_price*1>item.$orig.sell_price*1&&showprice=='1'}}"><text class="t2">{{"￥"+item.$orig.market_price}}</text></block><block wx:if="{{item.$orig.juli}}"><text class="t3" style="color:#888;">{{item.$orig.juli}}</text></block></view></block></view></block><block wx:if="{{item.$orig.xunjia_text&&item.$orig.price_type==1&&item.$orig.sell_price<=0}}"><view class="p2-1" style="height:50rpx;line-height:44rpx;"><block wx:if="{{showstyle!=1}}"><text class="t1" style="{{'color:'+(item.m22)+';'+('font-size:'+('30rpx')+';')}}">询价</text></block><block wx:if="{{showstyle==1}}"><text class="t1" style="{{'color:'+(item.m23)+';'}}">询价</text></block><block wx:if="{{item.$orig.xunjia_text&&item.$orig.price_type==1}}"><block><view class="lianxi" style="{{'background:'+(item.m24)+';'}}" data-lx_name="{{item.$orig.lx_name}}" data-lx_bid="{{item.$orig.lx_bid}}" data-lx_bname="{{item.$orig.lx_bname}}" data-lx_tel="{{item.$orig.lx_tel}}" data-btntype="2" data-event-opts="{{[['tap',[['showLinkChange',['$event']]]]]}}" catchtap="__e">{{item.$orig.xunjia_text?item.$orig.xunjia_text:'联系TA'}}</view></block></block></view></block></view><block wx:if="{{item.$orig.merchant_name}}"><view class="p1" style="color:#666;font-size:24rpx;white-space:nowrap;text-overflow:ellipsis;margin-top:6rpx;height:30rpx;line-height:30rpx;font-weight:normal;"><text>{{item.$orig.merchant_name}}</text></view></block><block wx:if="{{item.$orig.main_business}}"><view class="p1" style="color:#666;font-size:24rpx;margin-top:4rpx;font-weight:normal;"><text>{{item.$orig.main_business}}</text></view></block><block wx:if="{{item.$orig.product_type==3}}"><text class="p3">{{"手工费: ￥"+(item.$orig.hand_fee?item.$orig.hand_fee:0)}}</text></block><block wx:if="{{showsales=='1'&&item.$orig.sales>0}}"><view class="p3">{{"已售"+item.$orig.sales+"件"}}</view></block><block wx:if="{{(showsales!='1'||item.$orig.sales<=0)&&item.$orig.main_business}}"><view style="height:44rpx;"></view></block><block wx:if="{{params.style=='2'&&params.nowbuy==1}}"><view class="nowbuy" style="{{'background:'+('rgba('+item.m25+',0.1)')+';'+('color:'+(item.m26)+';')}}" data-btntype="2" data-proid="{{item.$orig[idfield]}}" data-event-opts="{{[['tap',[['buydialogChange',['$event']]]]]}}" catchtap="__e">立即购买</view></block><block wx:if="{{showcart==1&&!item.$orig.price_type&&item.$orig.hide_cart!=true&&!setCoupon}}"><view class="p4" style="{{(params.style=='2'&&params.nowbuy==1?'bottom:24rpx;background:rgba('+item.m27+',0.1);color:'+item.m28:'background:rgba('+item.m29+',0.1);color:'+item.m30)}}" data-btntype="1" data-proid="{{item.$orig.id}}" data-event-opts="{{[['tap',[['buydialogChange',['$event']]]]]}}" catchtap="__e"><text class="iconfont icon_gouwuche"></text></view></block><block wx:else><view data-event-opts="{{[['tap',[['couponAddChange',['$0'],[[['datalist','id',item.$orig.id]]]]]]]}}" class="addbut" style="{{'background:'+('linear-gradient(270deg,'+item.m31+' 0%,rgba('+item.m32+',0.8) 100%)')+';'}}" bindtap="__e">添加</view></block></view><block wx:if="{{item.$orig.hongbaoEdu>0}}"><view class="bg-desc" style="{{'background:'+('linear-gradient(90deg,'+item.m33+' 0%,rgba('+item.m34+',0.8) 100%)')+';'}}">{{"可获额度 +"+item.$orig.hongbaoEdu}}</view></block></view></block></view><block wx:if="{{buydialogShow}}"><buydialog vue-id="b98fe2d2-2" proid="{{proid}}" menuindex="{{menuindex}}" btntype="1" needaddcart="{{false}}" data-event-opts="{{[['^buydialogChange',[['buydialogChange']]],['^addcart',[['afteraddcart']]]]}}" bind:buydialogChange="__e" bind:addcart="__e" bind:__l="__l"></buydialog></block><block wx:if="{{showLinkStatus}}"><view class="posterDialog linkDialog"><view class="main"><view data-event-opts="{{[['tap',[['showLinkChange',['$event']]]]]}}" class="close" bindtap="__e"><image class="img" src="{{pre_url+'/static/img/close.png'}}"></image></view><view class="content"><view class="title">{{lx_name}}</view><block wx:if="{{lx_bid>0}}"><view class="row"><view class="f1" style="width:150rpx;">店铺名称</view><view class="f2" style="width:100%;max-width:470rpx;display:flex;" data-url="{{'/pagesExt/business/index?id='+lx_bid}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view style="width:100%;white-space:nowrap;text-overflow:ellipsis;overflow:hidden;">{{lx_bname}}</view><view style="flex:1;"></view><image class="image" src="{{pre_url+'/static/img/arrowright.png'}}"></image></view></view></block><block wx:if="{{lx_tel}}"><view class="row"><view class="f1" style="width:150rpx;">联系电话</view><view class="f2" style="{{'width:100%;max-width:470rpx;'+('color:'+($root.m35)+';')}}" data-url="{{'tel::'+lx_tel}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">{{lx_tel}}<image class="copyicon" src="{{pre_url+'/static/img/copy.png'}}" data-text="{{lx_tel}}" data-event-opts="{{[['tap',[['copy',['$event']]]]]}}" catchtap="__e"></image></view></view></block></view></view></view></block></view></block></block><block wx:if="{{nomore}}"><nomore vue-id="b98fe2d2-3" text="没有更多商品了" bind:__l="__l"></nomore></block><block wx:if="{{nodata}}"><nodata vue-id="b98fe2d2-4" text="没有查找到相关商品" bind:__l="__l"></nodata></block><block wx:if="{{loading}}"><loading vue-id="b98fe2d2-5" bind:__l="__l"></loading></block></view></block></block><block wx:if="{{loading}}"><loading vue-id="b98fe2d2-6" bind:__l="__l"></loading></block><dp-tabbar vue-id="b98fe2d2-7" opt="{{opt}}" data-event-opts="{{[['^getmenuindex',[['getmenuindex']]]]}}" bind:getmenuindex="__e" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="b98fe2d2-8" data-ref="popmsg" bind:__l="__l"></popmsg><wxxieyi vue-id="b98fe2d2-9" bind:__l="__l"></wxxieyi></view>