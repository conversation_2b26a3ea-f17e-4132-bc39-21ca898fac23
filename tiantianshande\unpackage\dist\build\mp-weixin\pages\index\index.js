(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages/index/index"],{"0081":function(e,t,n){"use strict";n.r(t);var a=n("9dfb"),o=n("78bd");for(var i in o)["default"].indexOf(i)<0&&function(e){n.d(t,e,(function(){return o[e]}))}(i);n("a758");var s=n("828b"),l=Object(s["a"])(o["default"],a["b"],a["c"],!1,null,null,null,!1,a["a"],void 0);t["default"]=l.exports},"69ee":function(e,t,n){"use strict";(function(e,t){var a=n("47a9");n("06e9");a(n("3240"));var o=a(n("0081"));e.__webpack_require_UNI_MP_PLUGIN__=n,t(o.default)}).call(this,n("3223")["default"],n("df3c")["createPage"])},"78bd":function(e,t,n){"use strict";n.r(t);var a=n("919a"),o=n.n(a);for(var i in a)["default"].indexOf(i)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(i);t["default"]=o.a},"919a":function(e,t,n){"use strict";(function(e,a){var o=n("47a9");Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i=o(n("af34")),s=o(n("3b2d")),l=getApp(),r={data:function(){return{opt:{},loading:!1,isload:!1,menuindex:-1,pre_url:l.globalData.pre_url,platform:l.globalData.platform,homeNavigationCustom:l.globalData.homeNavigationCustom,navigationBarBackgroundColor:l.globalData.navigationBarBackgroundColor,navigationBarTextStyle:l.globalData.navigationBarTextStyle,id:0,pageinfo:{menuStyle:"default",title:"",menuAlign:"center",bgType:"solid",navBgColor:"#ffffff",topGradient:"0"},pagecontent:[],sysset:{},title:"",oglist:[],guanggaopic:"",guanggaourl:"",copyright:"",latitude:"",longitude:"",area:"",locationInfo:null,statusBarHeight:20,screenWidth:375,business:[],xixie:!1,xdata:"",display_buy:"",cartnum:0,cartprice:0,code:"",area_id:"",area_name:"",bannerList:[{image:"/static/images/banner1.png"},{image:"/static/images/banner2.png"},{image:"/static/images/banner3.png"}],scrollTop:0,areaEnabled:!1,currentAreaId:"",currentAreaName:"",areaList:{},isLocating:!1,locationFailed:!1,menuTop:0,showSplash:!1,splashConfig:{},splashStartTime:0,splashDuration:0,skipCountdown:0,progressPercent:0,splashTimer:null,splashVideo:null,splashImage:null}},onLoad:function(t){var n=this,a=this;a.opt=l.getopts(t);var o=e.getSystemInfoSync();a.statusBarHeight=o.statusBarHeight,a.screenWidth=o.screenWidth,e.setNavigationBarTitle({title:a.pageinfo.title||a.title||""});var i=e.getMenuButtonBoundingClientRect();a.menuTop=i.top,console.log(i),a.initAreaData(),e.$on("city",(function(t){a.currentAreaId=t.id,a.currentAreaName=t.name,e.setStorageSync("area_id",t.id),e.setStorageSync("area_name",t.name),a.getdata()})),e.$on("location_selected",(function(t){a.latitude=t.latitude,a.longitude=t.longitude,a.area=t.display_name||t.district+t.street,a.locationInfo=t,e.setStorageSync("location_info",t),a.getdata(1)})),e.$on("updateAfterSwitch",(function(t){if(console.log("Received update event with data:",t),t&&t.select_bid){l.setCache("select_bid",t.select_bid);var a="ios"===e.getSystemInfoSync().platform;if(console.log("当前设备平台:",a?"iOS":"非iOS"),a)console.log("iOS设备：使用优化的页面切换方式"),n.opt.select_bid=t.select_bid,e.showLoading({title:"加载中...",mask:!0}),n.getdata(),setTimeout((function(){e.hideLoading()}),1e3);else{var o="/pages/index/index?select_bid=".concat(t.select_bid);console.log("非iOS设备：使用reLaunch刷新页面，URL:",o),e.reLaunch({url:o,success:function(){console.log("Page refreshed with new URL:",o)},fail:function(e){console.error("Failed to refresh page:",e),n.opt.select_bid=t.select_bid,n.getdata()}})}}}));var s=e.getStorageSync("location_info");s&&(this.latitude=s.latitude,this.longitude=s.longitude,this.area=s.district+s.street,this.locationInfo=s),a.getdata()},onPullDownRefresh:function(e){this.getdata()},onPageScroll:function(t){e.$emit("onPageScroll",t)},onUnload:function(){e.$off("city"),e.$off("location_selected"),e.$off("updateAfterSwitch"),this.splashTimer&&(clearInterval(this.splashTimer),this.splashTimer=null)},methods:{showSplashScreen:function(){console.log("显示开屏"),this.showSplash=!0,e.$emit("hideTabbar",!0),this.startSplashCountdown(),this.recordSplashShow()},hideSplashScreen:function(){console.log("隐藏开屏"),this.showSplash=!1,e.$emit("hideTabbar",!1),this.splashTimer&&(clearInterval(this.splashTimer),this.splashTimer=null)},startSplashCountdown:function(){var e=this;this.splashConfig&&(this.splashStartTime=Date.now(),this.splashDuration=1e3*parseInt(this.splashConfig.display_time)||3e3,this.skipCountdown=parseInt(this.splashConfig.skip_time)||2,this.progressPercent=0,this.splashTimer&&clearInterval(this.splashTimer),this.splashTimer=setInterval((function(){var t=Date.now(),n=t-e.splashStartTime;e.progressPercent=Math.min(n/e.splashDuration*100,100);var a=1e3*parseInt(e.splashConfig.skip_time),o=Math.max(0,Math.ceil((a-n)/1e3));e.skipCountdown=o,n>=e.splashDuration&&e.hideSplashScreen()}),100))},skipSplash:function(){1===this.splashConfig.skip_enabled?this.skipCountdown>0?console.log("还在跳过等待期内，剩余",this.skipCountdown,"秒"):(console.log("用户跳过开屏"),this.hideSplashScreen()):console.log("跳过功能未启用")},handleSplashClick:function(){this.splashConfig&&this.splashConfig.link_url?(console.log("点击开屏跳转:",this.splashConfig.link_url),this.hideSplashScreen(),l.gotopage(this.splashConfig.link_url)):this.hideSplashScreen()},recordSplashShow:function(){if(this.splashConfig&&this.splashConfig.id){var e={splash_id:this.splashConfig.id,platform:l.globalData.platform||"unknown",action:"show",timestamp:Date.now()};l.get("ApiIndex/recordsplashshow",e,(function(e){1==e.status?console.log("开屏统计记录成功"):console.log("开屏统计记录失败:",e.msg)}))}},checkAndInitSplash:function(e){var t=this;e&&1==e.is_enabled?"image"!==e.display_type||e.image_url?"video"!==e.display_type||e.video_url?(console.log("开屏配置有效，准备显示:",e),this.splashConfig=e,setTimeout((function(){t.showSplashScreen()}),100)):console.log("开屏视频URL为空"):console.log("开屏图片URL为空"):console.log("开屏未启用")},getdata:function(t){var n=this,o=this.opt,r=!1,c="",u="";if(t&&"object"===(0,s.default)(t)){c=t.componentId||t.id||"",u=t.style||"";var d=t.forceRefresh||!1;if(c&&(r=!0,console.log("首页收到特定组件刷新请求:",{"组件ID":c,"样式":u,"强制刷新":d,"时间戳":t._timestamp||(new Date).getTime()}),d)){console.log("检测到强制刷新标记，将模拟前端刷新");try{var g=this.findComponentById(c);if(g&&(console.log("找到目标组件:",g.temp,g.id),g.data&&g.data.length>0)){var p=(0,i.default)(g.data).sort((function(){return.5-Math.random()}));return g.data=p,console.log("前端数据刷新成功!"),void e.showToast({title:"换一换成功",icon:"none",duration:1500})}}catch(b){console.error("前端刷新数据失败:",b)}}}var f=getCurrentPages(),h=f[f.length-1],m=h.options||{},y=m.select_bid||o.select_bid||l.getCache("select_bid");console.log("Loading data with select_bid:",y),console.log("Current URL:",h.route,m);var _={id:0,select_bid:y,pid:l.globalData.pid,area:n.area};r&&(_.componentId=c,_.componentStyle=u),console.log("Request params:",_),"boolean"===typeof t&&t?(_.latitude=n.latitude,_.longitude=n.longitude):_.area_id=n.area_id,n.loading=!0,l.get("ApiIndex/index",_,(function(t){if(console.log("API response:",t),n.loading=!1,2!=t.status)if(1==t.status){if(r&&t.componentData){console.log("接收到特定组件数据:",t.componentData);for(var o=0;o<n.pagecontent.length;o++)if(n.pagecontent[o].id===c){n.pagecontent[o].data=t.componentData,console.log("已更新组件数据:",c,n.pagecontent[o].temp);break}return}t.pagecontent;if(n.title=t.pageinfo.title||"",t.oglist&&(n.oglist=t.oglist),n.guanggaopic=t.guanggaopic,n.guanggaourl=t.guanggaourl,t.splash_config&&(console.log("接收到开屏配置:",t.splash_config),n.checkAndInitSplash(t.splash_config)),t.pageinfo&&0!==Object.keys(t.pageinfo).length?(n.pageinfo=Object.assign({},n.pageinfo,t.pageinfo),n.pageinfo.title=t.pageinfo.title||n.title||n.sysset.name||""):console.log("未接收到后端菜单配置，使用默认居中标题样式"),n.copyright=t.copyright,n.sysset=t.sysset,1==t.sysset.mode&&(console.log("门店模式：主动请求定位"),n.latitude&&n.longitude||n.getCurrentLocation(),t.business&&(n.business=t.business,""==y&&l.setCache("select_bid",t.business.id))),2==t.sysset.mode&&t.sysset.outernet_url){console.log("外网模式：准备跳转到外部URL",t.sysset.outernet_url);var i=setTimeout((function(){console.log("跳转超时，显示手动跳转按钮"),n.loading=!1}),3e3);try{console.log("小程序环境：准备跳转到WebView页面");var s="/pages/index/webView3?url="+encodeURIComponent(t.sysset.outernet_url);console.log("跳转到的WebView路径：",s),setTimeout((function(){e.reLaunch({url:s,success:function(){console.log("WebView跳转成功"),clearTimeout(i)},fail:function(e){console.error("WebView跳转失败",e),clearTimeout(i)}})}),500)}catch(b){console.error("外网跳转发生错误：",b),clearTimeout(i),e.showModal({title:"跳转错误",content:"跳转外部链接时发生错误："+b.message,showCancel:!1})}return}var u=n.pageinfo.title||n.title||n.sysset.name||"";if(e.setNavigationBarTitle({title:u}),n.loaded(),n.initAreaData(),1!=t.sysset.mode){var d=JSON.parse(t.sysset.area_set||"{}"),g="1"===d.auto_location||"1"===n.pageinfo.auto_location,p="nearby"===n.pageinfo.locationType;console.log("定位设置检查：",{"area_set.auto_location":d.auto_location,"pageinfo.auto_location":n.pageinfo.auto_location,"pageinfo.locationType":n.pageinfo.locationType,"最终自动定位设置":g,"是否为附近模式":p,"当前纬度":n.latitude,"当前经度":n.longitude}),""!==n.latitude&&""!==n.longitude&&n.latitude&&n.longitude||!g||!p?n.pagecontent=t.pagecontent:(console.log("开始自动请求定位..."),n.getCurrentLocation())}else n.pagecontent=t.pagecontent;if(t.xixie){a.login({success:function(e){n.code=e.code}}),n.xixie=t.xixie;var f=t.xdata;n.xdata=f,n.display_buy=!!f.display_buy&&f.display_buy,n.cartnum=f.cartnum?f.cartnum:0,n.cartprice=f.cartprice?f.cartprice:0,f.cart_data&&setTimeout((function(){n.xcart_data=f.cart_data}),200),f.popup_address&&setTimeout((function(){n.popup_address=t.popup_address}),200)}}else t.msg?l.alert(t.msg,(function(){t.url&&l.goto(t.url)})):t.url?l.goto(t.url):l.alert("您无查看权限");else l.goto("/pages/pay/pay?fromPage=index&id="+t.payorderid+"&pageid="+n.id,"redirect")}))},showsubqrcode:function(){this.$refs.qrcodeDialog.open()},closesubqrcode:function(){this.$refs.qrcodeDialog.close()},changePopupAddress:function(e){this.xdata.popup_address=e},setMendianData:function(e){this.mendian_data=e},handleBannerClick:function(t){t.url&&e.navigateTo({url:t.url})},getTextColor:function(){return this.isValidPageInfo()?("float"===this.pageinfo.menuStyle||"fixed"===this.pageinfo.menuStyle)&&"1"===this.pageinfo.isOpacity&&this.pageinfo.opacity<.5?"#fff":this.pageinfo.menuTextColor||"#000000":"#000000"},getSearchBoxBackground:function(){return"#ffffff"},getSearchTextColor:function(){return this.isValidPageInfo()?this.pageinfo.searchTextColor?this.pageinfo.searchTextColor:("float"===this.pageinfo.menuStyle||"fixed"===this.pageinfo.menuStyle)&&"1"===this.pageinfo.isOpacity&&this.pageinfo.opacity<.5?"#fff":"#666":"#666"},handleSearchTap:function(){var e=this.pageinfo.hrefurl||"/shopPackage/shop/search";l.goto(e)},initAreaData:function(){var t=e.getStorageSync("area_id"),n=e.getStorageSync("area_name");if(this.sysset&&this.sysset.area_on){if(this.areaEnabled=!0,t&&n)this.currentAreaId=t,this.currentAreaName=n;else if(this.sysset.current_area_id&&this.sysset.current_area_name)this.currentAreaId=this.sysset.current_area_id,this.currentAreaName=this.sysset.current_area_name,e.setStorageSync("area_id",this.currentAreaId),e.setStorageSync("area_name",this.currentAreaName);else{var a=this.sysset.area_list||{};if(Object.keys(a).length>0){var o=Object.values(a)[0];this.currentAreaId=o.id,this.currentAreaName=o.name,e.setStorageSync("area_id",this.currentAreaId),e.setStorageSync("area_name",this.currentAreaName)}}this.areaList=this.sysset.area_list||{},console.log("当前区域信息：",{id:this.currentAreaId,name:this.currentAreaName})}},getCurrentLocation:function(){var t=this;this.isLocating||(this.isLocating=!0,this.locationFailed=!1,console.log("正在获取位置..."),l.getLocation((function(n){console.log("位置获取成功：",n),t.latitude=n.latitude,t.longitude=n.longitude,l.get("apiIndex/getLocation",{latitude:n.latitude,longitude:n.longitude,is_store_mode:1==t.sysset.mode?1:0},(function(n){console.log("地址信息获取结果：",n),1===n.status?(t.area=n.data.district+n.data.street,t.locationInfo=n.data,e.setStorageSync("location_info",n.data),1==t.sysset.mode?(console.log("门店模式：使用位置信息重新获取数据"),t.getdata(1)):"nearby"===t.pageinfo.locationType?t.getdata(1):t.pagecontent=t.pagecontent):(t.locationFailed=!0,console.error("获取地址信息失败：",n.msg||"未知错误"),1==t.sysset.mode&&l.alert("获取位置信息失败，这可能会影响到您查看附近门店的功能")),t.isLocating=!1}))}),(function(n){console.error("获取位置失败：",n),t.locationFailed=!0,t.isLocating=!1;var a=e.getStorageSync("location_info");a?(t.area=a.district+a.street,t.locationInfo=a,1==t.sysset.mode&&(t.latitude=a.latitude,t.longitude=a.longitude,console.log("门店模式：使用上次保存的位置信息"),t.getdata(1))):1==t.sysset.mode&&l.alert("获取位置信息失败，这可能会影响到您查看附近门店的功能")})))},handleLocationTap:function(){if("nearby"===this.pageinfo.locationType){if(!this.latitude||!this.longitude)return void this.getCurrentLocation();var e={latitude:this.latitude,longitude:this.longitude,current_address:this.area,location_info:this.locationInfo};l.goto("/pages/index/location?data="+encodeURIComponent(JSON.stringify(e)))}else{var t=JSON.parse(this.sysset.area_set||"{}");if(0==t.switcharea){var n={area_list:this.sysset.area_list,current_area_id:this.currentAreaId,current_area_name:this.currentAreaName,area_config:t};l.goto("/pages/index/city?data="+encodeURIComponent(JSON.stringify(n)))}}},isValidPageInfo:function(){return this.pageinfo&&Object.keys(this.pageinfo).length>0&&void 0!==this.pageinfo.menuStyle},manualJump:function(){if(this.sysset&&this.sysset.outernet_url){console.log("手动跳转到外部URL：",this.sysset.outernet_url);try{var t="/pages/index/webView3?url="+encodeURIComponent(this.sysset.outernet_url);e.reLaunch({url:t,fail:function(t){console.error("手动跳转失败",t),e.showModal({title:"跳转提示",content:"跳转失败，请稍后再试或联系管理员",showCancel:!1})}})}catch(n){console.error("手动跳转发生错误：",n),e.showModal({title:"跳转错误",content:"跳转外部链接时发生错误："+n.message,showCancel:!1})}}else e.showModal({title:"跳转提示",content:"无效的外部链接",showCancel:!1})},findComponentById:function(e){if(!this.pagecontent||!this.pagecontent.length)return null;for(var t=0;t<this.pagecontent.length;t++)if(this.pagecontent[t].id===e)return this.pagecontent[t];return null}}};t.default=r}).call(this,n("df3c")["default"],n("3223")["default"])},"9dfb":function(e,t,n){"use strict";n.d(t,"b",(function(){return o})),n.d(t,"c",(function(){return i})),n.d(t,"a",(function(){return a}));var a={dpXixieMendian:function(){return n.e("components/dp-xixie-mendian/dp-xixie-mendian").then(n.bind(null,"2c46"))},uniPopup:function(){return Promise.all([n.e("common/vendor"),n.e("components/uni-popup/uni-popup")]).then(n.bind(null,"ca44a"))},dp:function(){return n.e("components/dp/dp").then(n.bind(null,"1506"))},dpXixiePopupAddress:function(){return n.e("components/dp-xixie-popup-address/dp-xixie-popup-address").then(n.bind(null,"1f5d"))},dpXixieBuycart:function(){return n.e("components/dp-xixie-buycart/dp-xixie-buycart").then(n.bind(null,"019e"))},loading:function(){return n.e("components/loading/loading").then(n.bind(null,"ceaa"))},dpTabbar:function(){return n.e("components/dp-tabbar/dp-tabbar").then(n.bind(null,"b875"))},dpGuanggao:function(){return n.e("components/dp-guanggao/dp-guanggao").then(n.bind(null,"1574"))},popmsg:function(){return n.e("components/popmsg/popmsg").then(n.bind(null,"2bf2"))}},o=function(){var e=this,t=e.$createElement,n=(e._self._c,e.isValidPageInfo()),a=e.isValidPageInfo()&&"solid"===e.pageinfo.bgType,o=!e.isValidPageInfo()||"default"===e.pageinfo.menuStyle,i=e.isValidPageInfo()&&"gradient"===e.pageinfo.bgType&&"1"!==e.pageinfo.topGradient,s=i?null:e.isValidPageInfo()&&e.pageinfo.bgImage,l=e.isValidPageInfo()&&"hidden"===e.pageinfo.menuStyle,r=!e.isValidPageInfo()||"default"===e.pageinfo.menuStyle,c=e.isValidPageInfo(),u=!e.isValidPageInfo()||"default"===e.pageinfo.menuStyle,d=!e.isValidPageInfo()||"default"===e.pageinfo.menuStyle,g=!e.isValidPageInfo()||"default"===e.pageinfo.menuStyle,p=!e.isValidPageInfo()||"default"===e.pageinfo.menuStyle,f=p||"location"!==e.pageinfo.leftMode?null:e.getTextColor(),h=p||"1"!==e.pageinfo.showSearch?null:e.getSearchBoxBackground(),m=p||"1"!==e.pageinfo.showSearch?null:e.getSearchTextColor(),y=!e.isValidPageInfo()||"fixed"===e.pageinfo.menuStyle||"default"===e.pageinfo.menuStyle,_=e.isValidPageInfo()&&"gradient"===e.pageinfo.bgType&&"1"===e.pageinfo.topGradient,b=1==e.sysset.mode?e.t("color1"):null,S=1==e.sysset.mode?e.t("color1rgb"):null,v=1==e.sysset.mode?e.t("color1"):null,x=1==e.sysset.agent_card&&e.sysset.agent_card_info?e.t("color2"):null,I=e.sysset.showgzts?e.t("color1"):null,w=e.sysset.showgzts?e.t("color1"):null,C=e.oglist&&e.oglist.length>0,T=e.xixie&&e.display_buy?e.t("color1"):null,P=e.xixie&&e.display_buy?e.t("color1rgb"):null;e.$mp.data=Object.assign({},{$root:{m0:n,m1:a,m2:o,m3:i,m4:s,m5:l,m6:r,m7:c,m8:u,m9:d,m10:g,m11:p,m12:f,m13:h,m14:m,m15:y,m16:_,m17:b,m18:S,m19:v,m20:x,m21:I,m22:w,g0:C,m23:T,m24:P}})},i=[]},a758:function(e,t,n){"use strict";var a=n("b3cb"),o=n.n(a);o.a},b3cb:function(e,t,n){}},[["69ee","common/runtime","common/vendor"]]]);