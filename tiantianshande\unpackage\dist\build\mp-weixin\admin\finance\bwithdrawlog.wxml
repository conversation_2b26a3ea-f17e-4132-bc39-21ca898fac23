<view class="container"><block wx:if="{{isload}}"><block><view class="summary-box"><view class="filter-bar"><text data-event-opts="{{[['tap',[['toggleDateFilter',[false]]]]]}}" class="{{['filter-item',!isDateFilter?'active':'']}}" bindtap="__e">全部</text><text data-event-opts="{{[['tap',[['toggleDateFilter',[true]]]]]}}" class="{{['filter-item',isDateFilter?'active':'']}}" bindtap="__e">按月份</text><block wx:if="{{isDateFilter}}"><picker class="date-picker" mode="date" fields="month" value="{{currentDate}}" data-event-opts="{{[['change',[['dateChange',['$event']]]]]}}" bindchange="__e"><text class="picker-text">{{currentDate||'选择月份'}}</text></picker></block></view><view class="summary-list"><view class="summary-item"><text class="label">已提现总额</text><text class="value">{{(summary.withdrawed||0)+"元"}}</text></view><view class="summary-item"><text class="label">待提现(审核中)</text><text class="value">{{(summary.pending||0)+"元"}}</text></view><view class="summary-item"><text class="label">待提现(已审核)</text><text class="value">{{(summary.approved||0)+"元"}}</text></view><view class="summary-item"><text class="label">冻结金额</text><text class="value">{{(summary.frozen||0)+"元"}}</text></view></view></view><view class="content"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="item"><view class="f1"><text class="t1">{{"提现金额："+item.$orig.money+"元"}}</text><text class="t2">{{item.m0}}</text></view><view class="f3"><block wx:if="{{item.$orig.status==0}}"><text class="t1">审核中</text></block><block wx:if="{{item.$orig.status==1}}"><text class="t1">已审核</text></block><block wx:if="{{item.$orig.status==2}}"><text class="t2">已驳回</text></block><block wx:if="{{item.$orig.status==3}}"><text class="t1">已打款</text></block></view></view></block></view><block wx:if="{{nomore}}"><nomore vue-id="13b9bdab-1" bind:__l="__l"></nomore></block><block wx:if="{{nodata}}"><nodata vue-id="13b9bdab-2" bind:__l="__l"></nodata></block></block></block><block wx:if="{{loading}}"><loading vue-id="13b9bdab-3" bind:__l="__l"></loading></block><dp-tabbar vue-id="13b9bdab-4" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="13b9bdab-5" data-ref="popmsg" bind:__l="__l"></popmsg></view>