<view class="container" style="{{'background:'+(info.color1)+';'}}"><block wx:if="{{isload}}"><block><view class="banner"><image src="{{info.banner}}" mode="widthFix"></image></view><view class="box1"><view class="item"><view class="t1" style="{{'color:'+(info.color2)+';'}}">{{info.joinnum}}</view><view class="t2">参与人数</view></view><view class="item"><view class="t1" style="{{'color:'+(info.color2)+';'}}">{{info.helpnum}}</view><view class="t2">累计投票</view></view><view class="item"><view class="t1" style="{{'color:'+(info.color2)+';'}}">{{info.readcount}}</view><view class="t2">访问次数</view></view></view><view class="box2"><view class="f1"><image style="width:32rpx;height:32rpx;margin-right:6rpx;" src="/static/img/clock.png"></image><text>{{info.starttime>nowtime?'距活动开始还有':'距活动结束还剩'}}</text></view><view class="f2" style="{{'color:'+(info.color2)+';'}}"><text class="t1">{{djsday}}</text><text class="t2">天</text><text class="t1">{{djshour}}</text><text class="t2">小时</text><text class="t1">{{djsmin}}</text><text class="t2">分钟</text><text class="t1">{{djssec}}</text><text class="t2">秒</text></view><view class="topsearch flex-y-center"><view class="topsearch-f1 flex-y-center"><image class="img" src="/static/img/search_ico.png"></image><input placeholder="搜索编号或选手名称" placeholder-style="font-size:24rpx;color:#778899" data-event-opts="{{[['confirm',[['searchConfirm',['$event']]]]]}}" value="{{keyword}}" bindconfirm="__e"/></view></view></view><view class="box3"><block wx:for="{{datalist}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><block wx:if="{{info.listtype==0}}"><view class="item" data-url="{{'detail?id='+item.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="pic"><image class="img" src="{{item.pic}}" mode="widthFix"></image></view><view class="name">{{item.name}}</view><view class="no">{{"NO: "+item.number}}</view><view class="helpnum">{{item.helpnum+"票"}}</view><view class="tou" style="{{'background:'+('linear-gradient(90deg,'+info.color2+' 0%,rgba('+info.color2rgb+',0.7) 100%)')+';'}}" data-id="{{item.id}}" data-type="0" data-event-opts="{{[['tap',[['toupiao',['$event']]]]]}}" catchtap="__e">{{info.helptext}}</view></view></block><block wx:if="{{info.listtype==1}}"><view class="itemlist" data-url="{{'detail?id='+item.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="pic"><image class="img" src="{{item.pic}}" mode="widthFix"></image></view><view class="right"><view class="name">{{item.name}}</view><view class="no">{{"NO: "+item.number}}</view><view class="helpnum">{{item.helpnum+"票"}}</view><view class="tou" style="{{'background:'+('linear-gradient(90deg,'+info.color2+' 0%,rgba('+info.color2rgb+',0.7) 100%)')+';'}}" data-id="{{item.id}}" data-type="0" data-event-opts="{{[['tap',[['toupiao',['$event']]]]]}}" catchtap="__e">{{info.helptext}}</view></view></view></block></block></block></view><block wx:if="{{nodata}}"><view style="background:#fff;width:94%;margin:0 3%;border-radius:12rpx;"><nodata vue-id="9f0decd0-1" bind:__l="__l"></nodata></view></block><view style="width:100%;height:60rpx;"></view><block wx:if="{{info.help_check==1}}"><uni-popup class="vue-ref" vue-id="9f0decd0-2" id="dialogCaptcha" type="dialog" data-ref="dialogCaptcha" bind:__l="__l" vue-slots="{{['default']}}"><view class="hxqrbox"><view class="hxqrbox1"><input type="text" placeholder="请输入验证码" data-event-opts="{{[['input',[['setcaptcha',['$event']]]]]}}" bindinput="__e"/><image src="{{pre_url+'/?s=/ApiIndex/captcha&aid='+aid+'&session_id='+session_id+'&t='+randt}}" data-event-opts="{{[['tap',[['regetcaptcha',['$event']]]]]}}" bindtap="__e"></image></view><view class="tou2" style="{{'background:'+('linear-gradient(90deg,'+info.color2+' 0%,rgba('+info.color2rgb+',0.7) 100%)')+';'}}" data-id="{{nowjoinid}}" data-type="1" data-event-opts="{{[['tap',[['toupiao',['$event']]]]]}}" bindtap="__e">确 定</view><view data-event-opts="{{[['tap',[['closeCaptcha',['$event']]]]]}}" class="close" bindtap="__e"><image style="width:100%;height:100%;" src="/static/img/close2.png"></image></view></view></uni-popup></block><block wx:if="{{info.help_check==2}}"><uni-popup class="vue-ref" vue-id="9f0decd0-3" id="dialogSmscode" type="dialog" data-ref="dialogSmscode" bind:__l="__l" vue-slots="{{['default']}}"><view class="hxqrbox"><view class="hxqrbox1"><image style="width:44rpx;height:44rpx;margin-right:30rpx;" src="/static/img/reg-tel.png"></image><input type="text" placeholder="请输入手机号" data-event-opts="{{[['input',[['setsmstel',['$event']]]]]}}" bindinput="__e"/><view data-event-opts="{{[['tap',[['sendsmscode',['$event']]]]]}}" class="code" style="{{'font-size:30rpx;width:160rpx;text-align:center;'+('color:'+($root.m0)+';')}}" bindtap="__e">{{smsdjs||'获取验证码'}}</view></view><view class="hxqrbox1" style="margin-top:40rpx;"><image style="width:44rpx;height:44rpx;margin-right:30rpx;" src="/static/img/reg-code.png"></image><input type="text" placeholder="请输入短信验证码" data-event-opts="{{[['input',[['setsmscode',['$event']]]]]}}" bindinput="__e"/></view><view class="tou2" style="{{'background:'+('linear-gradient(90deg,'+info.color2+' 0%,rgba('+info.color2rgb+',0.7) 100%)')+';'}}" data-id="{{nowjoinid}}" data-type="1" data-event-opts="{{[['tap',[['toupiao',['$event']]]]]}}" bindtap="__e">确 定</view><view data-event-opts="{{[['tap',[['closeSmscode',['$event']]]]]}}" class="close" bindtap="__e"><image style="width:100%;height:100%;" src="/static/img/close2.png"></image></view></view></uni-popup></block></block></block><block wx:if="{{loading}}"><loading vue-id="9f0decd0-4" bind:__l="__l"></loading></block><dp-tabbar vue-id="9f0decd0-5" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="9f0decd0-6" data-ref="popmsg" bind:__l="__l"></popmsg></view>