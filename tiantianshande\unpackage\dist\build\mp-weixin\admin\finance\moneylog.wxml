<view><block wx:if="{{isload}}"><block><view class="topsearch flex-y-center"><view class="f1 flex-y-center"><image class="img" src="/static/img/search_ico.png"></image><input placeholder="{{'输入'+$root.m0+'昵称搜索'}}" placeholder-style="font-size:24rpx;color:#C2C2C2" data-event-opts="{{[['confirm',[['searchConfirm',['$event']]]],['input',[['searchChange',['$event']]]]]}}" value="{{keyword}}" bindconfirm="__e" bindinput="__e"/></view></view><block wx:if="{{$root.g0}}"><view class="content"><view class="label"><text class="t1">{{$root.m1+"明细（共"+count+"条）"}}</text></view><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="item"><view class="f1"><image class="t1" src="{{item.$orig.headimg}}"></image><text class="t2">{{item.$orig.nickname}}</text></view><view class="f2"><block wx:if="{{item.$orig.money>0}}"><text class="t1" style="color:#000;">{{"+"+item.$orig.money+"元"}}</text></block><block wx:else><text class="t1">{{item.$orig.money+"元"}}</text></block><text class="t2">{{item.m2}}</text><text class="t3">{{"备注："+item.$orig.remark}}</text></view></view></block></view></block><block wx:if="{{nodata}}"><nodata vue-id="3760d37f-1" bind:__l="__l"></nodata></block><block wx:if="{{nomore}}"><nomore vue-id="3760d37f-2" bind:__l="__l"></nomore></block></block></block><block wx:if="{{loading}}"><loading vue-id="3760d37f-3" bind:__l="__l"></loading></block><popmsg class="vue-ref" vue-id="3760d37f-4" data-ref="popmsg" bind:__l="__l"></popmsg></view>