<view style="{{'font-size:'+(fontSize)+';'}}"><block wx:if="{{isload}}"><block><view class="progress">当前：<text>{{question_index+1+"/"+$root.g0}}</text></view><view class="container"><view class="question"><view class="title"><rich-text nodes="{{question.name}}"></rich-text></view><view class="option_group"><block wx:for="{{question.optionlist}}" wx:for-item="item" wx:for-index="index"><block><view class="option flex" style="{{(item.checked==1?'background:'+themeColor+';color:#ffffff':'')}}" data-index="{{index}}" data-event-opts="{{[['tap',[['selectOption',['$event']]]]]}}" bindtap="__e">{{''+item.option+''}}</view></block></block></view></view></view><block wx:if="{{custom.PSQI}}"><view class="bottom"><block><block wx:if="{{question_index>0}}"><view data-event-opts="{{[['tap',[['goprev',['$event']]]]]}}" class="btn btn1" bindtap="__e">上一题</view></block><block wx:else><view class="btn btn0"></view></block></block><block wx:if="{{question_index<$root.g1-1}}"><view data-event-opts="{{[['tap',[['gonext',['$event']]]]]}}" class="btn btn2" bindtap="__e">下一题</view></block><block wx:if="{{question_index==$root.g2-1}}"><view data-event-opts="{{[['tap',[['goback',['$event']]]]]}}" class="btn btn2" bindtap="__e">返 回</view></block></view></block><block wx:else><view class="bottom"><block><block wx:if="{{question_index>0}}"><view data-event-opts="{{[['tap',[['goprev',['$event']]]]]}}" class="btn btn1" style="{{('background:rgba('+$root.m0+',0.16);color:'+$root.m1)}}" bindtap="__e">上一题</view></block><block wx:else><view class="btn btn0"></view></block></block><block wx:if="{{question_index<$root.g3-1}}"><view data-event-opts="{{[['tap',[['gonext',['$event']]]]]}}" class="btn" style="{{'background:'+(themeColor)+';'}}" bindtap="__e">下一题</view></block><block wx:if="{{question_index==$root.g4-1}}"><view data-event-opts="{{[['tap',[['goback',['$event']]]]]}}" class="btn" style="{{'background:'+(themeColor)+';'}}" bindtap="__e">返 回</view></block></view></block></block></block><block wx:if="{{loading}}"><loading vue-id="e962c8f2-1" bind:__l="__l"></loading></block><popmsg class="vue-ref" vue-id="e962c8f2-2" data-ref="popmsg" bind:__l="__l"></popmsg></view>