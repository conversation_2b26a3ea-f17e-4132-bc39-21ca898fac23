<view class="container"><block wx:if="{{isload}}"><block><form data-event-opts="{{[['submit',[['formSubmit',['$event']]]],['reset',[['formReset',['$event']]]]]}}" bindsubmit="__e" bindreset="__e"><view class="form"><view class="form-item"><input class="input" type="text" placeholder="请输入微信号" placeholder-style="color:#BBBBBB;font-size:28rpx" name="weixin" data-event-opts="{{[['input',[['__set_model',['','weixin','$event',[]]]]]]}}" value="{{weixin}}" bindinput="__e"/></view></view><button class="set-btn" style="{{'background:'+('linear-gradient(90deg,'+$root.m0+' 0%,rgba('+$root.m1+',0.8) 100%)')+';'}}" form-type="submit">保 存</button></form></block></block><block wx:if="{{loading}}"><loading vue-id="11379a84-1" bind:__l="__l"></loading></block><dp-tabbar vue-id="11379a84-2" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="11379a84-3" data-ref="popmsg" bind:__l="__l"></popmsg></view>