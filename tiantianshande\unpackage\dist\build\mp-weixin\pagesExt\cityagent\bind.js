require('../common/vendor.js');(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pagesExt/cityagent/bind"],{"2e44":function(t,e,n){},"45a7":function(t,e,n){"use strict";(function(t){Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n=getApp(),a={data:function(){return{apply_status:"none",agentInfo:null,submitting:!1,formData:{name:"",tel:"",agent_level:1,remark:""},agentLevels:[{id:1,name:"市级代理"},{id:2,name:"省级代理"},{id:3,name:"区县代理"},{id:4,name:"街道代理"}],selectedLevel:{id:1,name:"市级代理"}}},computed:{statusIcon:function(){return{0:"/static/img/status-pending.png",1:"/static/img/status-approved.png",2:"/static/img/status-rejected.png"}[this.apply_status]||"/static/img/status-pending.png"},statusText:function(){return{0:"审核中",1:"申请通过",2:"申请被拒绝"}[this.apply_status]||"未知状态"},statusDesc:function(){return{0:"您的代理申请正在审核中，请耐心等待...",1:"恭喜您！代理申请已通过，您可以开始享受代理权益",2:"很抱歉，您的代理申请被拒绝，请联系客服了解详情"}[this.apply_status]||""}},onLoad:function(){this.checkApplyStatus()},methods:{getColor:function(t){try{return"function"===typeof this.t?this.t(t):null}catch(e){return console.log("获取颜色失败:",e),null}},getColorRgb:function(t){try{return"function"===typeof this.t?this.t(t):null}catch(e){return console.log("获取RGB颜色失败:",e),null}},checkApplyStatus:function(){var e=this;t.getStorageSync("userTel")?n.get("ApiCityAgentPublic/getApplyStatusByTel",{tel:t.getStorageSync("userTel")},(function(t){1===t.status?(e.apply_status=t.apply_status,e.agentInfo=t.agent):e.apply_status="none"})):n.get("ApiCityAgent/getApplyStatus",{},(function(t){1===t.status?(e.apply_status=t.apply_status,e.agentInfo=t.agent):e.apply_status="none"}),(function(t){e.apply_status="none"}))},onLevelChange:function(t){var e=t.detail.value;this.selectedLevel=this.agentLevels[e],this.formData.agent_level=this.selectedLevel.id},submitApply:function(){var e=this;if(this.formData.name)if(this.formData.tel){/^1[3-9]\d{9}$/.test(this.formData.tel)?(this.submitting=!0,n.post("ApiCityAgentPublic/applyAgent",this.formData,(function(a){e.submitting=!1,1===a.status?(n.success(a.msg),t.setStorageSync("userTel",e.formData.tel),setTimeout((function(){e.checkApplyStatus()}),1e3)):n.error(a.msg)}))):n.alert("请输入正确的手机号")}else n.alert("请输入联系电话");else n.alert("请输入代理商名称")},goBack:function(){1===this.apply_status?n.goto("/pagesExt/cityagent/index"):t.navigateBack()},getAgentLevelName:function(t){return{1:"市级代理",2:"省级代理",3:"区县代理",4:"街道代理"}[t]||"未知"},formatTime:function(t){var e=new Date(1e3*t);return e.toLocaleString()}}};e.default=a}).call(this,n("df3c")["default"])},"52cc":function(t,e,n){"use strict";var a=n("2e44"),s=n.n(a);s.a},"6dbe":function(t,e,n){"use strict";(function(t,e){var a=n("47a9");n("06e9");a(n("3240"));var s=a(n("deb2"));t.__webpack_require_UNI_MP_PLUGIN__=n,e(s.default)}).call(this,n("3223")["default"],n("df3c")["createPage"])},"6eb4":function(t,e,n){"use strict";n.r(e);var a=n("45a7"),s=n.n(a);for(var u in a)["default"].indexOf(u)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(u);e["default"]=s.a},"7d24":function(t,e,n){"use strict";n.d(e,"b",(function(){return a})),n.d(e,"c",(function(){return s})),n.d(e,"a",(function(){}));var a=function(){var t=this,e=t.$createElement,n=(t._self._c,"none"!==t.apply_status&&t.agentInfo?t.getAgentLevelName(t.agentInfo.agent_level):null),a="none"!==t.apply_status&&t.agentInfo?t.formatTime(t.agentInfo.createtime):null;t.$mp.data=Object.assign({},{$root:{m0:n,m1:a}})},s=[]},deb2:function(t,e,n){"use strict";n.r(e);var a=n("7d24"),s=n("6eb4");for(var u in s)["default"].indexOf(u)<0&&function(t){n.d(e,t,(function(){return s[t]}))}(u);n("52cc");var i=n("828b"),o=Object(i["a"])(s["default"],a["b"],a["c"],!1,null,null,null,!1,a["a"],void 0);e["default"]=o.exports}},[["6dbe","common/runtime","common/vendor"]]]);