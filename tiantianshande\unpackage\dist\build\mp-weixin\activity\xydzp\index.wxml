<view class="container"><block wx:if="{{isload}}"><block><view class="pageback" style="{{'background:'+(backcolor==''?'#f58d40':backcolor)+';'}}"></view><view class="wrap" style="{{('background-image:url('+info.bgpic+');background-size:100% 100%;')}}"><view class="header clearfix"><view data-event-opts="{{[['tap',[['changemaskrule',['$event']]]]]}}" class="rule" bindtap="__e">活动规则</view><view class="my" data-url="{{'myprize?hid='+info.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">我的奖品</view></view><view class="title" style="{{('background-image:url('+info.banner+');background-size:100% 100%;')}}"></view><view class="canvas" hidden="{{showmaskrule||jxshow}}"><canvas style="width:650rpx;height:650rpx;" canvas-id="roulette"></canvas><cover-image class="start" src="{{pre_url+'/static/img/xydzp_start.png'}}" data-event-opts="{{[['tap',[['rollStart',['$event']]]]]}}" bindtap="__e"></cover-image></view><block wx:if="{{info.use_type!=2}}"><view class="border">您今日还有<text id="change">{{remaindaytimes}}</text>次抽奖机会</view></block><block wx:if="{{info.use_type==1&&info.usescore>0}}"><view class="border2"><block wx:if="{{!info.is_tr}}"><text>每次</text></block><block wx:else><text>本次</text></block>抽奖将消耗<text>{{info.usescore}}</text>{{''+$root.m0+'，您共有'}}<text id="myscore">{{member.score}}</text>{{''+$root.m1}}</view></block><block wx:if="{{info.use_type==3&&info.usecontribution>0}}"><view class="border2"><block wx:if="{{!info.is_tr}}"><text>每次</text></block><block wx:else><text>本次</text></block>抽奖将消耗<text>{{info.usecontribution}}</text>{{''+$root.m2+'，您共有'}}<text id="myscore">{{member.contribution_num}}</text>{{''+$root.m3}}</view></block><block wx:if="{{info.use_type==2&&info.usemoney>0}}"><view class="border2">每次抽奖将消耗<text>{{$root.m4}}</text>{{info.usemoney+'元 ，您共有'}}<text id="mymoney">{{member.money}}</text>元</view></block><view class="scroll"><view class="p" style="{{('background-image: url('+pre_url+'/static/img/dzp/list.png);background-size:100% 100%;')}}"></view><view class="sideBox"><swiper class="bd" autoplay="true" indicator-dots="{{false}}" current="0" vertical="{{true}}" circular="true"><block wx:for="{{zjlist}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block wx:if="{{index%2==0}}"><swiper-item class="sitem"><view>{{"恭喜"+item.nickname+" 获得"}}<text class="info">{{item.jxmc}}</text></view><block wx:if="{{zjlist[index+1]}}"><view>{{"恭喜"+zjlist[index+1].nickname+" 获得"}}<text class="info">{{zjlist[index+1].jxmc}}</text></view></block></swiper-item></block></block></swiper></view></view><block wx:if="{{showmaskrule}}"><view id="mask-rule"><view class="box-rule"><view class="h2">活动规则说明</view><view style="{{('background-image:url('+pre_url+'/static/img/dzp/close.png);background-size:100%')}}" id="close-rule" data-event-opts="{{[['tap',[['changemaskrule',['$event']]]]]}}" bindtap="__e"></view><view class="con"><view class="text"><text decode="true" space="true">{{info.guize}}</text></view></view></view></view></block><block wx:if="{{jxshow&&jx>0}}"><view id="mask"><view class="blin"></view><view class="caidai" style="{{('background-image: url('+pre_url+'/static/img/dzp/dianzhui.png);')}}"></view><view class="winning reback" style="{{('background:url('+pre_url+'/static/img/dzp/bg2.png) no-repeat;background-size:100% 100%;')}}"><view class="p"><view>恭喜您抽中了</view><view class="b" id="text1">{{jxmc}}</view></view><view data-event-opts="{{[['tap',[['changemask',['$event']]]]]}}" class="btn" bindtap="__e">确定</view></view></view></block><block wx:if="{{jxshow&&jx==0}}"><view id="mask2"><view class="blin"></view><view class="caidai" style="{{('background-image: url('+pre_url+'/static/img/dzp/dianzhui.png);')}}"></view><view class="winning reback" style="{{('background:url('+pre_url+'/static/img/dzp/bg3.png) no-repeat;background-size:100% 100%;')}}"><view class="p"><view class="b" id="text2">{{jxmc}}</view></view><view data-event-opts="{{[['tap',[['changemask',['$event']]]]]}}" class="btn" bindtap="__e">确定</view></view></view></block></view></block></block><block wx:if="{{loading}}"><loading vue-id="593c0f7c-1" bind:__l="__l"></loading></block><dp-tabbar vue-id="593c0f7c-2" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="593c0f7c-3" data-ref="popmsg" bind:__l="__l"></popmsg></view>