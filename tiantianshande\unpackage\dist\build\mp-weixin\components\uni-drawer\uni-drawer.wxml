<block wx:if="{{visibleSync}}"><view data-event-opts="{{[['touchmove',[['clear',['$event']]]]]}}" class="{{['uni-drawer','data-v-b8d52f08',(showDrawer)?'uni-drawer--visible':'']}}" catchtouchmove="__e"><view data-event-opts="{{[['tap',[['close',['mask']]]]]}}" class="{{['uni-drawer__mask','data-v-b8d52f08',(showDrawer&&mask)?'uni-drawer__mask--visible':'']}}" bindtap="__e"></view><view class="{{['uni-drawer__content','data-v-b8d52f08',(rightMode)?'uni-drawer--right':'',(!rightMode)?'uni-drawer--left':'',(showDrawer)?'uni-drawer__content--visible':'']}}" style="{{'width:'+(drawerWidth+'px')+';'}}"><slot></slot></view></view></block>