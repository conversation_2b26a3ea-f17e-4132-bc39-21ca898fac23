(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["zhaopin/myFavorites"],{"0003":function(t,e,o){"use strict";o.r(e);var a=o("4bbe"),n=o("109d");for(var i in n)["default"].indexOf(i)<0&&function(t){o.d(e,t,(function(){return n[t]}))}(i);o("3c10");var r=o("828b"),l=Object(r["a"])(n["default"],a["b"],a["c"],!1,null,null,null,!1,a["a"],void 0);e["default"]=l.exports},"109d":function(t,e,o){"use strict";o.r(e);var a=o("f9c7"),n=o.n(a);for(var i in a)["default"].indexOf(i)<0&&function(t){o.d(e,t,(function(){return a[t]}))}(i);e["default"]=n.a},"3c10":function(t,e,o){"use strict";var a=o("a4c7c"),n=o.n(a);n.a},"4bbe":function(t,e,o){"use strict";o.d(e,"b",(function(){return n})),o.d(e,"c",(function(){return i})),o.d(e,"a",(function(){return a}));var a={loading:function(){return o.e("components/loading/loading").then(o.bind(null,"ceaa"))},dpTabbar:function(){return o.e("components/dp-tabbar/dp-tabbar").then(o.bind(null,"b875"))},popmsg:function(){return o.e("components/popmsg/popmsg").then(o.bind(null,"2bf2"))}},n=function(){var t=this,e=t.$createElement,o=(t._self._c,t.isload&&"all"===t.currentTab?t.t("color1rgb"):null),a=t.isload&&"all"===t.currentTab?t.t("color1"):null,n=t.isload&&"all"===t.currentTab?t.t("color1rgb"):null,i=t.isload&&"fulltime"===t.currentTab?t.t("color1rgb"):null,r=t.isload&&"fulltime"===t.currentTab?t.t("color1"):null,l=t.isload&&"fulltime"===t.currentTab?t.t("color1rgb"):null,s=t.isload&&"parttime"===t.currentTab?t.t("color1rgb"):null,u=t.isload&&"parttime"===t.currentTab?t.t("color1"):null,c=t.isload&&"parttime"===t.currentTab?t.t("color1rgb"):null,f=t.isload?t.favoriteJobs.length:null,d=!t.isload||f>0?null:t.t("color1"),p=!t.isload||f>0?null:t.t("color1"),b=!t.isload||f>0?null:t.t("color1rgb"),g=t.isload?t.isLoading&&t.favoriteJobs.length>0:null,h=t.isload?t.noMoreData&&t.favoriteJobs.length>0:null;t.$mp.data=Object.assign({},{$root:{m0:o,m1:a,m2:n,m3:i,m4:r,m5:l,m6:s,m7:u,m8:c,g0:f,m9:d,m10:p,m11:b,g1:g,g2:h}})},i=[]},"5e45":function(t,e,o){"use strict";(function(t,e){var a=o("47a9");o("06e9");a(o("3240"));var n=a(o("0003"));t.__webpack_require_UNI_MP_PLUGIN__=o,e(n.default)}).call(this,o("3223")["default"],o("df3c")["createPage"])},a4c7c:function(t,e,o){},f9c7:function(t,e,o){"use strict";(function(t){var a=o("47a9");Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n=a(o("7eb4")),i=a(o("af34")),r=a(o("ee10")),l=getApp(),s={data:function(){return{opt:{},loading:!1,isload:!1,currentTab:"all",favoriteJobs:[],isLoading:!1,isRefreshing:!1,noMoreData:!1,page:1,pageSize:10,pre_url:l.globalData.pre_url}},onLoad:function(t){this.opt=l.getopts(t),this.loadFavoriteJobs()},onPullDownRefresh:function(){this.onRefresh()},onShareAppMessage:function(){return this._sharewx({title:"我的收藏",desc:"查看收藏的职位",pic:""})},onShareTimeline:function(){var t=this._sharewx({title:"我的收藏",desc:"查看收藏的职位",pic:""}),e=t.path.split("?")[1];return{title:t.title,imageUrl:t.imageUrl,query:e}},onReachBottom:function(){this.noMoreData||this.isLoading||this.loadMore()},methods:{switchTab:function(t){this.currentTab!==t&&(this.currentTab=t,this.page=1,this.noMoreData=!1,this.favoriteJobs=[],this.loadFavoriteJobs())},loadFavoriteJobs:function(){var e=this;return(0,r.default)(n.default.mark((function o(){var a;return n.default.wrap((function(o){while(1)switch(o.prev=o.next){case 0:if(!e.isLoading&&!e.noMoreData){o.next=2;break}return o.abrupt("return");case 2:e.isLoading=!0,e.loading=!0,a={page:e.page,limit:e.pageSize},"all"!==e.currentTab&&(a.type="fulltime"===e.currentTab?1:2),l.get("/apiZhaopin/getFavoriteList",a,(function(o){if(e.loading=!1,e.isLoading=!1,e.isRefreshing=!1,t.stopPullDownRefresh(),1===o.status){var a=o.data.list.map((function(t){return{id:t.id,title:t.title,salary:t.salary,companyName:t.company_name,companyLogo:t.company_logo,tags:[t.education,t.experience,t.work_address].filter(Boolean),collectTime:t.create_time}}));1===e.page?e.favoriteJobs=a:e.favoriteJobs=[].concat((0,i.default)(e.favoriteJobs),(0,i.default)(a)),e.noMoreData=a.length<e.pageSize||e.favoriteJobs.length>=o.data.total,e.page++,e.isload=!0}else t.showToast({title:o.msg||"加载失败，请重试",icon:"none"})}));case 7:case"end":return o.stop()}}),o)})))()},onRefresh:function(){var t=this;return(0,r.default)(n.default.mark((function e(){return n.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t.isRefreshing=!0,t.page=1,t.noMoreData=!1,e.next=5,t.loadFavoriteJobs();case 5:case"end":return e.stop()}}),e)})))()},loadMore:function(){this.loadFavoriteJobs()},viewJobDetail:function(e){t.navigateTo({url:"/zhaopin/partdetails?id=".concat(e)})},removeFromFavorites:function(e){var o=this;t.showModal({title:"提示",content:"确定要取消收藏该职位吗？",success:function(t){t.confirm&&o.toggleFavorite(e)}})},toggleFavorite:function(e){var o=this;l.post("/apiZhaopin/favoritePosition",{position_id:e},(function(a){1===a.status?(0===a.data.is_favorite&&(o.favoriteJobs=o.favoriteJobs.filter((function(t){return t.id!==e}))),t.showToast({title:a.msg,icon:"success"})):t.showToast({title:a.msg||"操作失败",icon:"none"})}))},goToJobList:function(){t.navigateTo({url:"/zhaopin/index"})}}};e.default=s}).call(this,o("df3c")["default"])}},[["5e45","common/runtime","common/vendor"]]]);