<view class="dp-notice" style="{{'color:'+(params.color)+';'+('background-color:'+(params.bgcolor)+';')+('margin:'+(params.margin_y*2.2+'rpx '+params.margin_x*2.2+'rpx')+';')+('padding:'+(params.padding_y*2.2+'rpx '+params.padding_x*2.2+'rpx')+';')}}"><block wx:if="{{params.showimg==1}}"><view class="left"><image class="image" src="{{params.img}}" mode="heightFix"></image></view></block><view class="right"><block wx:if="{{params.showicon==1}}"><image class="ico" src="{{params.icon}}"></image></block><block wx:if="{{params.scrollType==='vertical'}}"><swiper class="itemlist" style="position:relative;height:40rpx;" autoplay="{{true}}" interval="{{params.scroll*1000}}" vertical="{{true}}"><block wx:for="{{data}}" wx:for-item="item" wx:for-index="__i0__" wx:key="id"><swiper-item class="item" style="{{'font-size:'+(params.fontsize*2.2+'rpx')+';'}}" data-url="{{item.hrefurl}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">{{item.title}}</swiper-item></block></swiper></block><block wx:else><block wx:if="{{params.scrollType==='horizontal'}}"><swiper class="itemlist" style="position:relative;height:40rpx;" autoplay="{{true}}" interval="{{params.scroll*1000}}" vertical="{{false}}"><block wx:for="{{data}}" wx:for-item="item" wx:for-index="__i1__" wx:key="id"><swiper-item class="item" style="{{'font-size:'+(params.fontsize*2.2+'rpx')+';'}}" data-url="{{item.hrefurl}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">{{item.title}}</swiper-item></block></swiper></block><block wx:else><block wx:if="{{params.scrollType==='marquee'}}"><view class="marquee-container"><view class="marquee-text" style="{{'font-size:'+(params.fontsize*2.2+'rpx')+';'+('animation-duration:'+(marqueeTime+'s')+';')}}"><text class="marquee-content"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="id"><text class="marquee-item" data-url="{{item.$orig.hrefurl}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">{{''+item.$orig.title+''}}<block wx:if="{{index<item.g0-1}}"><text>|</text></block></text></block></text><text class="marquee-content"><block wx:for="{{$root.l1}}" wx:for-item="item" wx:for-index="index"><text class="marquee-item" data-url="{{item.$orig.hrefurl}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">{{''+item.$orig.title+''}}<block wx:if="{{index<item.g1-1}}"><text>|</text></block></text></block></text></view></view></block></block></block></view></view>