<view class="uni-popup-dialog data-v-bda7c408"><view class="uni-dialog-title data-v-bda7c408"><text class="{{['uni-dialog-title-text','data-v-bda7c408','uni-popup__'+dialogType]}}">{{title}}</text></view><view class="uni-dialog-content data-v-bda7c408"><block wx:if="{{mode==='base'}}"><text class="uni-dialog-content-text data-v-bda7c408">{{content}}</text></block><block wx:else><input class="uni-dialog-input data-v-bda7c408" style="height:60rpx;font-size:25rpx;" type="{{valueType}}" placeholder="{{placeholder}}" focus="{{focus}}" data-event-opts="{{[['input',[['__set_model',['','val','$event',[]]]]]]}}" value="{{val}}" bindinput="__e"/></block></view><view class="uni-dialog-button-group data-v-bda7c408"><view data-event-opts="{{[['tap',[['close',['$event']]]]]}}" class="uni-dialog-button data-v-bda7c408" bindtap="__e"><text class="uni-dialog-button-text data-v-bda7c408">取消</text></view><view data-event-opts="{{[['tap',[['onOk',['$event']]]]]}}" class="uni-dialog-button uni-border-left data-v-bda7c408" bindtap="__e"><text class="uni-dialog-button-text uni-button-color data-v-bda7c408">确定</text></view></view><block wx:if="{{popup.isDesktop}}"><view data-event-opts="{{[['tap',[['close',['$event']]]]]}}" class="uni-popup-dialog__close data-v-bda7c408" bindtap="__e"><label class="uni-popup-dialog__close-icon _span data-v-bda7c408"></label></view></block></view>