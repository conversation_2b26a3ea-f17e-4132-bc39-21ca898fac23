<view class="container"><block wx:if="{{isload}}"><block><form data-event-opts="{{[['submit',[['formSubmit',['$event']]]],['reset',[['formReset',['$event']]]]]}}" bindsubmit="__e" bindreset="__e"><view class="form"><view class="form-item"><textarea class="input" placeholder="请输入用户简介" placeholder-style="color:#BBBBBB;font-size:28rpx" name="yonghujianjie" rows="5" data-event-opts="{{[['input',[['__set_model',['','yonghujianjie','$event',[]]]]]]}}" value="{{yonghujianjie}}" bindinput="__e"></textarea></view></view><button class="set-btn" style="{{'background:'+('linear-gradient(90deg,'+$root.m0+' 0%,rgba('+$root.m1+',0.8) 100%)')+';'}}" form-type="submit">保 存</button></form></block></block><block wx:if="{{loading}}"><loading vue-id="6455c3be-1" bind:__l="__l"></loading></block><dp-tabbar vue-id="6455c3be-2" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="6455c3be-3" data-ref="popmsg" bind:__l="__l"></popmsg></view>