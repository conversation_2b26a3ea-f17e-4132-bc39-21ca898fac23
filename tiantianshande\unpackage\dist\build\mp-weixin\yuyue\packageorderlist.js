(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["yuyue/packageorderlist"],{"086f":function(t,e,a){},"169e":function(t,e,a){"use strict";var r=a("086f"),s=a.n(r);s.a},"20d5":function(t,e,a){"use strict";a.r(e);var r=a("52d8"),s=a.n(r);for(var i in r)["default"].indexOf(i)<0&&function(t){a.d(e,t,(function(){return r[t]}))}(i);e["default"]=s.a},"3cee":function(t,e,a){"use strict";(function(t,e){var r=a("47a9");a("06e9");r(a("3240"));var s=r(a("9f66"));t.__webpack_require_UNI_MP_PLUGIN__=a,e(s.default)}).call(this,a("3223")["default"],a("df3c")["createPage"])},"52d8":function(t,e,a){"use strict";(function(t){Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var a={data:function(){return{themeColor:"",tabs:[{name:"全部",status:-1},{name:"待支付",status:0},{name:"可使用",status:1},{name:"已用完",status:100},{name:"已失效",status:2}],currentTab:0,status:-1,orderList:[],page:1,limit:10,total:0,loading:!1,nodata:!1,triggered:!1,$baseUrl:""}},onLoad:function(e){var a=getApp();a&&a.globalData&&a.globalData.config&&a.globalData.config.t?this.themeColor=a.globalData.config.t("color1"):(this.themeColor="#2979ff",console.warn("2025-01-03 22:55:53,565-INFO-[packageorderlist][onLoad_001] 无法获取主题色配置，使用默认值"));var r=-1;e&&void 0!==e.status&&(r=parseInt(e.status));var s=this.tabs.findIndex((function(t){return t.status===r}));this.currentTab=s>=0?s:0,this.status=this.tabs[this.currentTab].status,this.getOrderList(!0),t.setNavigationBarTitle({title:"我的套餐订单"}),console.log("2025-01-03 22:55:53,565-INFO-[packageorderlist][onLoad_002] 组件初始化完成，当前状态:",this.status)},onReachBottom:function(){this.loading||this.nodata||(this.page++,this.getOrderList())},onPullDownRefresh:function(){var e=this;this.triggered||(this.triggered=!0,this.page=1,this.nodata=!1,this.getOrderList(!0,(function(){t.stopPullDownRefresh(),e.triggered=!1})))},methods:{switchTab:function(t){this.currentTab!==t&&(this.currentTab=t,this.status=this.tabs[t].status,this.page=1,this.nodata=!1,this.orderList=[],this.getOrderList(!0))},getOrderList:function(){var t=arguments.length>0&&void 0!==arguments[0]&&arguments[0],e=arguments.length>1?arguments[1]:void 0,a=this;if(a.loading||a.nodata&&!t)e&&e();else{a.loading=!0,t&&(a.page=1,a.orderList=[],a.nodata=!1);var r=getApp();if(!r||!r.post)return console.error("2025-01-03 22:55:53,565-ERROR-[packageorderlist][getOrderList_001] app对象未初始化或post方法不存在"),a.loading=!1,void(e&&e());var s=100===a.status?1:a.status;r.post("ApiYuyuePackage/getUserPackages",{page:a.page,limit:a.limit,status:s},(function(t){if(a.loading=!1,1==t.status){var s=t.data||[],i=s.map((function(t){if(1===t.status){var e=Math.floor(Date.now()/1e3),a=0===t.expires_time||t.expires_time<e&&t.expires_time<1672531200;if(a&&t.pay_time){console.log("2025-01-03 22:55:53,565-INFO-[packageorderlist][getOrderList_004] 订单"+t.id+"检测到无效的过期时间:",t.expires_time);var r=new Date(t.pay_time.replace(/-/g,"/")).getTime(),s=t.valid_days||365;t.expires_time=Math.floor(r/1e3)+86400*s,console.log("2025-01-03 22:55:53,565-INFO-[packageorderlist][getOrderList_005] 订单"+t.id+"自动计算新的过期时间:",{payTime:t.pay_time,payTimeStamp:Math.floor(r/1e3),validDays:s,newExpiresTime:t.expires_time,currentTime:e})}if(t.expires_time>0){var i=new Date(1e3*t.expires_time);t.expires_time_format=i.getFullYear()+"-"+String(i.getMonth()+1).padStart(2,"0")+"-"+String(i.getDate()).padStart(2,"0")}t.expires_time>0&&e>t.expires_time?(t.status_text="已过期",console.log("2025-01-03 22:55:53,565-INFO-[packageorderlist][getOrderList_006] 订单"+t.id+"已过期，当前时间:",e,"过期时间:",t.expires_time)):t.remain_services<=0?t.status_text="已用完":t.status_text="可使用"}return t.package_pic&&0===t.package_pic.indexOf("https://localhost")&&(t.package_pic=t.package_pic.replace("https://localhost","")),t.package_pic&&0===t.package_pic.indexOf("https://localhosthttps://")&&(t.package_pic=t.package_pic.replace("https://localhost","")),t})),o=i;100===a.status?(o=i.filter((function(t){return 1===t.status&&(t.remain_services<=0||"已用完"===t.status_text)})),console.log("2025-01-03 22:55:53,565-INFO-[packageorderlist][getOrderList_007] 过滤已用完套餐，原数量:",i.length,"过滤后数量:",o.length)):1===a.status&&(o=i.filter((function(t){return 1===t.status&&t.remain_services>0&&"可使用"===t.status_text})),console.log("2025-01-03 22:55:53,565-INFO-[packageorderlist][getOrderList_008] 过滤真正可使用套餐，原数量:",i.length,"过滤后数量:",o.length)),1===a.page?a.orderList=o:a.orderList=a.orderList.concat(o),100===a.status||1===a.status?(a.total=o.length,o.length<a.limit&&(a.nodata=!0)):(a.total=t.total||0,(s.length<a.limit||a.orderList.length>=a.total)&&(a.nodata=!0)),console.log("2025-01-03 22:55:53,565-INFO-[packageorderlist][getOrderList_003] 订单列表数据处理完成",i)}else r.error(t.msg||"获取订单列表失败"),a.page>1&&a.page--;e&&e()}),(function(){a.loading=!1,a.page>1&&a.page--,e&&e(),r.error("请求失败")}))}},gotoDetail:function(t){if(t){var e=getApp();e.goto("/yuyue/packageorderdetail?order_id="+t)}},gotoPay:function(t,e){var a=this;if(t){var r=getApp();r.payorder({payorderid:t,orderid:e,type:"yuyue_package",success:function(){r.success("支付成功"),a.getOrderList(!0)},fail:function(){r.error("支付失败或取消")}})}else{var s=getApp();s.error("支付单号不存在")}},cancelOrder:function(t,e){var a=this,r=getApp();r.confirm("确定要取消该订单吗？",(function(){r.showLoading("处理中..."),r.post("ApiYuyuePackage/cancelOrder",{order_id:t},(function(t){r.showLoading(!1),1==t.status?(r.success("取消成功"),-1===a.status||0===a.status?a.getOrderList(!0):a.orderList.splice(e,1)):r.error(t.msg||"取消失败")}))}))},deleteOrder:function(t,e){var a=this,r=getApp();r.confirm("确定要删除该订单吗？删除后不可恢复。",(function(){r.showLoading("处理中..."),r.post("ApiYuyuePackage/deleteOrder",{order_id:t},(function(t){r.showLoading(!1),1==t.status?(r.success("删除成功"),a.orderList.splice(e,1),0===a.orderList.length&&a.page>1&&a.page--):r.error(t.msg||"删除失败")}))}))},applyRefund:function(t){this.gotoDetail(t);var e=getApp();e.toast("请在订单详情页申请退款")},canUse:function(t){return 1===t.status&&t.remain_services>0&&"已用完"!==t.status_text&&"已过期"!==t.status_text},canRefund:function(t){return 1===t.status&&"已用完"!==t.status_text&&"已过期"!==t.status_text&&(!t.refund_status||0===t.refund_status)},getStatusColor:function(t){switch(t){case 0:return"#FF9900";case 1:return this.themeColor||"#2979ff";case 2:return"#999999";case 3:return"#4CAF50";case 4:return"#FF9900";case 5:return"#999999";default:return"#666666"}},formatTime:function(t){if(!t)return"";var e=new Date(1e3*t),a=e.getFullYear(),r=(e.getMonth()+1).toString().padStart(2,"0"),s=e.getDate().toString().padStart(2,"0"),i=e.getHours().toString().padStart(2,"0"),o=e.getMinutes().toString().padStart(2,"0");return"".concat(a,"-").concat(r,"-").concat(s," ").concat(i,":").concat(o)}}};e.default=a}).call(this,a("df3c")["default"])},8275:function(t,e,a){"use strict";a.d(e,"b",(function(){return r})),a.d(e,"c",(function(){return s})),a.d(e,"a",(function(){}));var r=function(){var t=this,e=t.$createElement,a=(t._self._c,t.orderList.length),r=a>0?t.__map(t.orderList,(function(e,a){var r=t.__get_orig(e),s=t.getStatusColor(e.status),i=t.canUse(e),o=t.canRefund(e);return{$orig:r,m0:s,m1:i,m2:o}})):null;t.$mp.data=Object.assign({},{$root:{g0:a,l0:r}})},s=[]},"9f66":function(t,e,a){"use strict";a.r(e);var r=a("8275"),s=a("20d5");for(var i in s)["default"].indexOf(i)<0&&function(t){a.d(e,t,(function(){return s[t]}))}(i);a("169e");var o=a("828b"),n=Object(o["a"])(s["default"],r["b"],r["c"],!1,null,null,null,!1,r["a"],void 0);e["default"]=n.exports}},[["3cee","common/runtime","common/vendor"]]]);