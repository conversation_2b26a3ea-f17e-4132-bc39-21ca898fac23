require('../common/vendor.js');(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pagesExt/business/maps"],{"1a4d":function(t,n,e){"use strict";var i=e("2c54"),a=e.n(i);a.a},"2c54":function(t,n,e){},3732:function(t,n,e){"use strict";var i=e("4d03"),a=e.n(i);a.a},"4d03":function(t,n,e){},"572d":function(t,n,e){"use strict";(function(t,n){var i=e("47a9");e("06e9");i(e("3240"));var a=i(e("b61e"));t.__webpack_require_UNI_MP_PLUGIN__=e,n(a.default)}).call(this,e("3223")["default"],e("df3c")["createPage"])},7356:function(t,n,e){"use strict";(function(t){Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;var e=getApp(),i={data:function(){return{latitude:23.106574,longitude:113.324587,scale:12,bottomData:!1,marker:[],currentMerchant:null,merchantList:[]}},onLoad:function(){this.getLocation()},computed:{coverbottom:function(){return"100rpx"}},onPullDownRefresh:function(){this.getLocation()},methods:{getNearbyBusiness:function(){this.marker=[];var t=this;e.post("ApiBusiness/blist",{pagenum:1,field:"juli",order:"asc",longitude:this.longitude,latitude:this.latitude,keyword:""},(function(n){var e;null!==(e=n.data)&&void 0!==e&&e.length&&(t.merchantList=n.data,n.data.forEach((function(n){t.marker.push({id:n.id,latitude:n.latitude,longitude:n.longitude,iconPath:"/static/img/address.png",rotate:0,width:20,height:30,alpha:.5,callout:{content:n.name,color:"#ffffff",fontSize:14,borderRadius:15,borderWidth:"10",bgColor:"#e51860",display:"ALWAYS"}})})))}))},setDistance:function(t){return t.includes("km")?t.slice(0,-2):t},getLocation:function(){var n=this;t.getLocation({type:"gcj02",complete:function(t){t.longitude&&t.latitude&&(n.longitude=t.longitude,n.latitude=t.latitude),n.getNearbyBusiness()}})},callouttap:function(t){this.currentMerchant=this.merchantList.find((function(n){return n.id===t.detail.markerId})),this.$refs.popup.open("top")},navigateToMerchantPage:function(){console.log("Navigating to merchant page",this.currentMerchant),this.currentMerchant&&this.currentMerchant.id?t.navigateTo({url:"/pagesExt/zuji/prolistzuji?bid=".concat(this.currentMerchant.id)}):console.log("Current merchant is null or has no id")},onControltap:function(){this.getLocation()}}};n.default=i}).call(this,e("df3c")["default"])},"81ab":function(t,n,e){"use strict";e.r(n);var i=e("7356"),a=e.n(i);for(var o in i)["default"].indexOf(o)<0&&function(t){e.d(n,t,(function(){return i[t]}))}(o);n["default"]=a.a},"8f95":function(t,n,e){"use strict";e.d(n,"b",(function(){return a})),e.d(n,"c",(function(){return o})),e.d(n,"a",(function(){return i}));var i={uniPopup:function(){return Promise.all([e.e("common/vendor"),e.e("components/uni-popup/uni-popup")]).then(e.bind(null,"ca44a"))}},a=function(){var t=this.$createElement,n=(this._self._c,this.currentMerchant&&this.currentMerchant&&this.currentMerchant.juli?this.setDistance(this.currentMerchant.juli):null);this.$mp.data=Object.assign({},{$root:{m0:n}})},o=[]},b61e:function(t,n,e){"use strict";e.r(n);var i=e("8f95"),a=e("81ab");for(var o in a)["default"].indexOf(o)<0&&function(t){e.d(n,t,(function(){return a[t]}))}(o);e("3732"),e("1a4d");var r=e("828b"),u=Object(r["a"])(a["default"],i["b"],i["c"],!1,null,"cd1cdc4a",null,!1,i["a"],void 0);n["default"]=u.exports}},[["572d","common/runtime","common/vendor"]]]);