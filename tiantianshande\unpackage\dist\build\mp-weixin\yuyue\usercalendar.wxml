<view class="container"><block wx:if="{{isload}}"><block><view class="page-header"><view class="title"><text class="title-text">服务日程</text></view></view><view class="calendar-header"><view class="month-nav"><view data-event-opts="{{[['tap',[['prevMonth',['$event']]]]]}}" class="prev-month ripple" bindtap="__e"><text class="iconfont iconleft"></text><text>上个月</text></view><view class="current-month"><text class="month-text">{{year+"年"+month+"月"}}</text></view><view data-event-opts="{{[['tap',[['nextMonth',['$event']]]]]}}" class="next-month ripple" bindtap="__e"><text>下个月</text><text class="iconfont iconright"></text></view></view><view class="week-header"><block wx:for="{{weekDays}}" wx:for-item="day" wx:for-index="index" wx:key="index"><view class="week-day"><text class="{{[(index===0||index===6)?'weekend':'']}}">{{day}}</text></view></block></view></view><view class="calendar-body"><block wx:for="{{calendarDays}}" wx:for-item="day" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['selectDate',['$0'],[[['calendarDays','',index]]]]]]]}}" class="{{['calendar-day',(!day.day)?'empty':'',(day.disabled)?'disabled':'',(day.available&&!day.disabled)?'available':'',(selectedDate===day.date)?'selected':'',(day.isToday)?'today':'']}}" bindtap="__e"><text class="day-number">{{day.day||''}}</text><block wx:if="{{day.available}}"><view class="day-marker"><block wx:if="{{day.specialPrice}}"><text class="special-price"></text></block><block wx:if="{{day.available_worker_count>0}}"><text class="has-service"></text></block></view></block></view></block></view><view class="calendar-legend"><view class="legend-item"><view class="legend-marker special-price-marker"></view><text>特价</text></view><view class="legend-item"><view class="legend-marker has-service-marker"></view><text>有服务</text></view></view><block wx:if="{{$root.g0>0}}"><view class="order-section"><view class="section-title">可预约订单</view><view class="order-list"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['selectAndShowCalendar',['$0'],[[['orderList','',index]]]]]]]}}" class="{{['order-item',(selectedOrderId===item.$orig.id)?'selected-order':'']}}" bindtap="__e"><view class="order-left"><image src="{{item.$orig.propic}}" mode="aspectFill"></image><block wx:if="{{item.$orig.status==5||item.$orig.status==6}}"><view class="order-badge">待预约</view></block></view><view class="order-center"><text class="order-name">{{item.$orig.proname}}</text><view class="order-info"><text class="order-number">{{"订单号："+item.$orig.ordernum}}</text><text class="{{['order-status-label','status-'+item.$orig.status]}}">{{item.m0}}</text></view><view class="order-bottom"><text class="order-price">{{"¥"+item.$orig.totalprice}}</text></view></view><view class="order-right"><view class="select-btn-wrap" style="{{(selectedOrderId===item.$orig.id?'background: #56aaff':'background: #f0f7ff')}}"><text class="select-btn" style="{{(selectedOrderId===item.$orig.id?'color: #fff':'color: #56aaff')}}">选择</text><text class="iconfont iconright" style="{{(selectedOrderId===item.$orig.id?'color: #fff':'color: #56aaff')}}"></text></view></view></view></block></view></view></block><block wx:else><view class="no-order-container"><view class="no-order-tip"><text>您暂时没有可预约的服务项目~</text></view><button data-event-opts="{{[['tap',[['goToPurchase',['$event']]]]]}}" class="purchase-btn" style="{{'background:'+('#56aaff')+';'}}" bindtap="__e">购买服务</button></view></block><block wx:if="{{showOrderPanel}}"><view class="order-panel"><view class="order-panel-header"><text>选择订单进行预约</text><text data-event-opts="{{[['tap',[['closeOrderPanel',['$event']]]]]}}" class="close-btn" bindtap="__e">×</text></view><scroll-view class="order-list" scroll-y="true"><block wx:for="{{$root.l1}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['selectOrder',['$0'],[[['orderList','',index]]]]]]]}}" class="order-item" bindtap="__e"><view class="order-left"><image src="{{item.$orig.propic}}" mode="aspectFill"></image></view><view class="order-center"><text class="order-name">{{item.$orig.proname}}</text><text class="order-number">{{"订单号："+item.$orig.ordernum}}</text><text class="order-price">{{"¥"+item.$orig.totalprice}}</text></view><view class="order-right"><text class="{{['order-status','status-'+item.$orig.status]}}">{{item.m1}}</text></view></view></block></scroll-view><block wx:if="{{$root.g1===0}}"><view class="no-data"><text>暂无可预约订单</text></view></block></view></block><block wx:if="{{showDatePanel}}"><view class="date-panel"><view class="date-panel-header"><text>选择预约日期</text><text data-event-opts="{{[['tap',[['closeDatePanel',['$event']]]]]}}" class="close-btn" bindtap="__e">×</text></view><view class="date-content"><view class="selected-date-info"><text>{{"已选择："+selectedDate+" ("+selectedDayText+")"}}</text><text>{{"可用服务人员："+selectedDayWorkers+"人"}}</text></view><button data-event-opts="{{[['tap',[['confirmDate',['$event']]]]]}}" class="confirm-btn" style="{{'background:'+($root.m2)+';'}}" bindtap="__e">确认预约</button></view></view></block><block wx:if="{{showOrderPanel||showDatePanel}}"><view data-event-opts="{{[['tap',[['closeAllPanels',['$event']]]]]}}" class="mask" bindtap="__e"></view></block><block wx:if="{{nomore}}"><nomore vue-id="5d5ef626-1" bind:__l="__l"></nomore></block><block wx:if="{{nodata}}"><nodata vue-id="5d5ef626-2" bind:__l="__l"></nodata></block></block></block><block wx:if="{{loading}}"><loading vue-id="5d5ef626-3" bind:__l="__l"></loading></block><dp-tabbar vue-id="5d5ef626-4" opt="{{opt}}" active="{{3}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="5d5ef626-5" data-ref="popmsg" bind:__l="__l"></popmsg></view>