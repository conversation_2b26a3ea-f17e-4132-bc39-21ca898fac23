<view class="container"><block wx:if="{{isload}}"><block><block wx:if="{{data&&data.url_h5}}"><view class="row"><view class="title">链接</view><view>{{data.url_h5}}</view><view class="btn" style="{{'background:'+($root.m0)+';'}}" data-url="{{'copy::'+data.url_h5}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">复制</view></view></block><block wx:if="{{data&&data.qrcode_h5}}"><view class="row"><view class="title">二维码</view><image style="width:60%;" src="{{data.qrcode_h5}}" data-url="{{poster}}" mode="widthFix" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image><view class="btn" style="{{'background:'+($root.m1)+';'}}" data-pic="{{data.qrcode_h5}}" data-event-opts="{{[['tap',[['savpic',['$event']]]]]}}" bindtap="__e">下载</view></view></block><block wx:if="{{data&&data.qrcode_wx}}"><view class="row"><view class="title">微信小程序码</view><image style="width:60%;" src="{{data.qrcode_wx}}" data-url="{{poster}}" mode="widthFix" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image><view class="btn" style="{{'background:'+($root.m2)+';'}}" data-pic="{{data.qrcode_wx}}" data-event-opts="{{[['tap',[['savpic',['$event']]]]]}}" bindtap="__e">下载</view></view></block><block wx:if="{{data&&data.qrcode_alipay}}"><view class="row"><view class="title">支付宝小程序码</view><image style="width:60%;" src="{{data.qrcode_alipay}}" data-url="{{poster}}" mode="widthFix" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image><view class="btn" style="{{'background:'+($root.m3)+';'}}" data-pic="{{data.qrcode_alipay}}" data-event-opts="{{[['tap',[['savpic',['$event']]]]]}}" bindtap="__e">下载</view></view></block></block></block><block wx:if="{{loading}}"><loading vue-id="27715354-1" bind:__l="__l"></loading></block><popmsg class="vue-ref" vue-id="27715354-2" data-ref="popmsg" bind:__l="__l"></popmsg></view>