<view class="container"><block wx:if="{{isload}}"><block><view class="search-container" style="{{(history_show?'height:100%;':'')}}"><view class="topsearch flex-y-center"><view class="f1 flex-y-center"><image class="img" src="/static/img/search_ico.png"></image><input placeholder="搜索菜品" placeholder-style="font-size:24rpx;color:#C2C2C2" data-event-opts="{{[['confirm',[['searchConfirm',['$event']]]],['input',[['searchChange',['$event']]]],['focus',[['searchFocus',['$event']]]]]}}" value="{{keyword}}" bindconfirm="__e" bindinput="__e" bindfocus="__e"/></view></view><view hidden="{{!(history_show)}}" class="search-history"><view><text class="search-history-title">最近搜索</text><view data-event-opts="{{[['tap',[['deleteSearchHistory',['$event']]]]]}}" class="delete-search-history" bindtap="__e"><image style="width:36rpx;height:36rpx;" src="/static/img/del.png"></image></view></view><view class="search-history-list"><block wx:for="{{history_list}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="search-history-item" data-value="{{item}}" data-event-opts="{{[['tap',[['historyClick',['$event']]]]]}}" bindtap="__e">{{item+''}}</view></block><block wx:if="{{$root.g0}}"><view class="flex-y-center"><image style="width:36rpx;height:36rpx;margin-right:10rpx;" src="/static/img/tanhao.png"></image>暂无记录</view></block></view></view><view hidden="{{!(!history_show)}}" class="search-navbar"><view class="search-navbar-item" style="{{(!field||field=='sort'?'color:'+$root.m0:'')}}" data-field="sort" data-order="desc" data-event-opts="{{[['tap',[['sortClick',['$event']]]]]}}" catchtap="__e">综合</view><view class="search-navbar-item" style="{{(field=='sales'?'color:'+$root.m1:'')}}" data-field="sales" data-order="desc" data-event-opts="{{[['tap',[['sortClick',['$event']]]]]}}" catchtap="__e">销量</view><view class="search-navbar-item" data-field="sell_price" data-order="{{order=='asc'?'desc':'asc'}}" data-event-opts="{{[['tap',[['sortClick',['$event']]]]]}}" catchtap="__e"><text style="{{(field=='sell_price'?'color:'+$root.m2:'')}}">价格</text><text class="iconfont iconshangla" style="{{(field=='sell_price'&&order=='asc'?'color:'+$root.m3:'')}}"></text><text class="iconfont icondaoxu" style="{{(field=='sell_price'&&order=='desc'?'color:'+$root.m4:'')}}"></text></view></view></view><view class="product-container"><block wx:if="{{$root.g1}}"><view class="product-itemlist"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="id"><view class="{{['item',item.$orig.stock<=0||item.$orig.stock_daily<=item.$orig.sales_daily?'soldout':'']}}"><view class="product-pic" data-url="{{'product?id='+item.$orig.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image class="image" src="{{item.$orig.pic}}" mode="widthFix"></image><view class="overlay"><view class="text">售罄</view></view></view><view class="product-info"><view class="p1"><text>{{item.$orig.name}}</text></view><view class="p2"><text class="t1" style="{{'color:'+(item.m5)+';'}}"><text style="font-size:20rpx;padding-right:1px;">￥</text>{{item.$orig.sell_price}}</text><block wx:if="{{item.$orig.market_price*1>item.$orig.sell_price*1}}"><text class="t2">{{"￥"+item.$orig.market_price}}</text></block></view><view class="p3"><block wx:if="{{item.$orig.sales>0}}"><view class="p3-1"><text style="overflow:hidden;">{{"已售"+item.$orig.sales+"件"}}</text></view></block><block wx:if="{{item.$orig.limit_start>0}}"><view class="p3-1"><text style="overflow:hidden;">{{item.$orig.limit_start+"件起售"}}</text></view></block></view><block wx:if="{{item.$orig.stock>0&&item.$orig.stock_daily>item.$orig.sales_daily}}"><view class="addnum"><block wx:if="{{item.$orig.ggcount>1}}"><view class="plus" data-proid="{{item.$orig.id}}" data-stock="{{item.$orig.stock}}" data-event-opts="{{[['tap',[['buydialogChange',['$event']]]]]}}" catchtap="__e">+</view></block><block wx:else><view class="plus" data-num="1" data-proid="{{item.$orig.id}}" data-ggid="{{item.$orig.gglist[0].id}}" data-stock="{{item.$orig.stock}}" data-event-opts="{{[['tap',[['addcart',['$event']]]]]}}" catchtap="__e">+</view></block></view></block></view></view></block></view></block><block wx:if="{{buydialogShow}}"><buydialog vue-id="f50243ee-1" proid="{{proid}}" menuindex="{{menuindex}}" btntype="1" needaddcart="{{false}}" controller="ApiRestaurantShop" data-event-opts="{{[['^buydialogChange',[['buydialogChange']]],['^addcart',[['afteraddcart']]]]}}" bind:buydialogChange="__e" bind:addcart="__e" bind:__l="__l"></buydialog></block><block wx:if="{{nomore}}"><nomore vue-id="f50243ee-2" text="没有更多菜品了" bind:__l="__l"></nomore></block><block wx:if="{{nodata}}"><nodata vue-id="f50243ee-3" text="没有查找到相关菜品" bind:__l="__l"></nodata></block><block wx:if="{{loading}}"><loading vue-id="f50243ee-4" bind:__l="__l"></loading></block></view></block></block><block wx:if="{{loading}}"><loading vue-id="f50243ee-5" bind:__l="__l"></loading></block><dp-tabbar vue-id="f50243ee-6" opt="{{opt}}" data-event-opts="{{[['^getmenuindex',[['getmenuindex']]]]}}" bind:getmenuindex="__e" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="f50243ee-7" data-ref="popmsg" bind:__l="__l"></popmsg></view>