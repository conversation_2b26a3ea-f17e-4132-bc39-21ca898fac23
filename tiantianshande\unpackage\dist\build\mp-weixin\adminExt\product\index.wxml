<view class="container"><dd-tab vue-id="33bbc423-1" itemdata="{{['全部('+countall+')','已上架('+count1+')','未上架('+count0+')']}}" itemst="{{['all','1','0']}}" st="{{st}}" isfixed="{{true}}" data-event-opts="{{[['^changetab',[['changetab']]]]}}" bind:changetab="__e" bind:__l="__l"></dd-tab><view style="width:100%;height:100rpx;"></view><view class="topsearch flex-y-center"><view class="f1 flex-y-center"><image class="img" src="{{pre_url+'/static/img/search_ico.png'}}"></image><input placeholder="输入关键字搜索" placeholder-style="font-size:24rpx;color:#C2C2C2" data-event-opts="{{[['confirm',[['searchConfirm',['$event']]]]]}}" value="{{keyword}}" bindconfirm="__e"/></view><view data-event-opts="{{[['tap',[['changeClistDialog',['$event']]]]]}}" class="screen-view flex flex-y-center" bindtap="__e"><view class="screen-view-text" style="{{'color:'+($root.g0>0?$root.m0:'')+';'}}">分类筛选</view><text class="iconfont iconshaixuan" style="{{'color:'+($root.g1>0?$root.m1:'')+';'}}"></text></view></view><view class="order-content"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><view class="order-box"><view class="content" style="border-bottom:none;"><view data-url="{{'/pages/shop/product?id='+item.$orig.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image src="{{item.$orig.pic}}"></image></view><view class="detail"><view class="t1">{{item.$orig.name+''}}<block wx:if="{{item.$orig.isstock_warning==1}}"><view class="stockwarning"><image style="width:30rpx;height:30rpx;" src="{{pre_url+'/static/img/workorder/ts.png'}}"></image>库存不足</view></block></view><view class="t2">{{"剩余："+item.$orig.stock+''}}<text style="color:#a88;padding-left:20rpx;">{{"已售："+item.$orig.sales}}</text></view><view class="t3"><text class="x1">{{"￥"+item.$orig.sell_price}}</text><block wx:if="{{item.$orig.market_price!=null}}"><text class="x2">{{"￥"+item.$orig.market_price}}</text></block></view></view></view><view class="op"><block wx:if="{{!item.$orig.status||item.$orig.status==0}}"><text class="flex1" style="color:red;">未上架</text></block><block wx:else><text class="flex1" style="color:green;">已上架</text></block><block wx:if="{{!item.$orig.ischecked}}"><text class="flex1" style="color:orange;">待审核</text></block><block wx:if="{{item.$orig.plate_id!=0}}"><block><block wx:if="{{manage_set&&manage_set.status_product==1}}"><view><block wx:if="{{!item.$orig.status||item.$orig.status==0}}"><view class="btn1" style="{{'background:'+(item.m2)+';'}}" data-st="1" data-id="{{item.$orig.id}}" data-event-opts="{{[['tap',[['setst',['$event']]]]]}}" bindtap="__e">上架</view></block><block wx:else><view class="btn1" style="{{'background:'+(item.m3)+';'}}" data-st="0" data-id="{{item.$orig.id}}" data-event-opts="{{[['tap',[['setst',['$event']]]]]}}" bindtap="__e">下架</view></block></view></block><block wx:if="{{manage_set&&manage_set.stock_product==1}}"><view><view class="btn2" data-url="{{'editstock?id='+item.$orig.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">修改价格</view></view></block></block></block><block wx:else><block><block wx:if="{{!item.$orig.status||item.$orig.status==0}}"><view class="btn1" style="{{'background:'+(item.m4)+';'}}" data-st="1" data-id="{{item.$orig.id}}" data-event-opts="{{[['tap',[['setst',['$event']]]]]}}" bindtap="__e">上架</view></block><block wx:else><view class="btn1" style="{{'background:'+(item.m5)+';'}}" data-st="0" data-id="{{item.$orig.id}}" data-event-opts="{{[['tap',[['setst',['$event']]]]]}}" bindtap="__e">下架</view></block><view class="btn2" data-url="{{'edit?id='+item.$orig.id+'&cids='+cids}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">编辑</view><view class="btn2" data-id="{{item.$orig.id}}" data-event-opts="{{[['tap',[['todel',['$event']]]]]}}" bindtap="__e">删除</view></block></block></view></view></block></block></view><block wx:if="{{clistshow}}"><view class="popup__container"><view data-event-opts="{{[['tap',[['changeClistDialog',['$event']]]]]}}" class="popup__overlay" catchtap="__e"></view><view class="popup__modal" style="max-height:1400rpx;"><view class="popup__title"><text class="popup__title-text">请选择分类</text><image class="popup__close" style="width:36rpx;height:36rpx;" src="{{pre_url+'/static/img/close.png'}}" data-event-opts="{{[['tap',[['changeClistDialog',['$event']]]]]}}" catchtap="__e"></image></view><view class="popup__content"><block wx:for="{{$root.l3}}" wx:for-item="item" wx:for-index="index" wx:key="id"><block><view class="clist-item" data-id="{{item.$orig.id}}" data-event-opts="{{[['tap',[['cidsChange',['$event']]]]]}}" bindtap="__e"><view class="flex1">{{item.$orig.name}}</view><view class="radio" style="{{(item.m6?'background:'+item.m7+';border:0':'')}}"><image class="radio-img" src="{{pre_url+'/static/img/checkd.png'}}"></image></view></view><block wx:for="{{item.l2}}" wx:for-item="item2" wx:for-index="index2" wx:key="id"><block><view class="clist-item" style="padding-left:80rpx;" data-id="{{item2.$orig.id}}" data-event-opts="{{[['tap',[['cidsChange',['$event']]]]]}}" bindtap="__e"><block wx:if="{{item.g2-1==index2}}"><view class="flex1">{{"└ "+item2.$orig.name}}</view></block><block wx:else><view class="flex1">{{"├ "+item2.$orig.name}}</view></block><view class="radio" style="{{(item2.m8?'background:'+item2.m9+';border:0':'')}}"><image class="radio-img" src="{{pre_url+'/static/img/checkd.png'}}"></image></view></view><block wx:for="{{item2.l1}}" wx:for-item="item3" wx:for-index="index3" wx:key="id"><block><view class="clist-item" style="padding-left:160rpx;" data-id="{{item3.$orig.id}}" data-event-opts="{{[['tap',[['cidsChange',['$event']]]]]}}" bindtap="__e"><block wx:if="{{item2.g3-1==index3}}"><view class="flex1">{{"└ "+item3.$orig.name}}</view></block><block wx:else><view class="flex1">{{"├ "+item3.$orig.name}}</view></block><view class="radio" style="{{(item3.m10?'background:'+item3.m11+';border:0':'')}}"><image class="radio-img" src="{{pre_url+'/static/img/checkd.png'}}"></image></view></view></block></block></block></block></block></block></view><view class="popup_but_view flex"><view data-event-opts="{{[['tap',[['changeClistDialogReset',['$event']]]]]}}" class="popup_but" style="{{'border-color:'+($root.m12)+';'+('color:'+($root.m13)+';')}}" catchtap="__e">重置</view><view data-event-opts="{{[['tap',[['changeClistDialogsearch',['$event']]]]]}}" class="popup_but" style="{{'background:'+($root.m14)+';'}}" catchtap="__e">确定</view></view></view></view></block><block wx:if="{{loading}}"><loading vue-id="33bbc423-2" bind:__l="__l"></loading></block><block wx:if="{{nomore}}"><nomore vue-id="33bbc423-3" bind:__l="__l"></nomore></block><block wx:if="{{nodata}}"><nodata vue-id="33bbc423-4" bind:__l="__l"></nodata></block><popmsg class="vue-ref" vue-id="33bbc423-5" data-ref="popmsg" bind:__l="__l"></popmsg></view>