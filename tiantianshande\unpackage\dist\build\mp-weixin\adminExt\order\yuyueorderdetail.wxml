<view class="container"><block wx:if="{{isload}}"><block><view class="ordertop flex" style="{{('background:url('+pre_url+'/static/img/orderbg.png);background-size:100%')}}"><block wx:if="{{detail.status==0}}"><view class="f1"><view class="t1">等待买家付款</view><block wx:if="{{djs}}"><view class="t2">{{"剩余时间："+djs}}</view></block></view></block><block wx:if="{{detail.status==1&&detail.refund_status==0}}"><view class="f1"><block wx:if="{{!detail.worker_id}}"><block><view class="t2">等待派单</view></block></block><block wx:else><block><view class="t2">订单已接单</view></block></block></view></block><block wx:if="{{detail.status==2}}"><view class="f1"><view class="t1">订单服务中</view></view></block><block wx:if="{{detail.status==3}}"><view class="f1"><view class="t1">订单已完成</view></view></block><block wx:if="{{detail.status==4}}"><view class="f1"><view class="t1">订单已取消</view></view></block><view class="orderx"><image src="{{pre_url+'/static/img/orderx.png'}}"></image></view></view><view class="orderinfo orderinfotop"><view class="title">订单信息</view><view class="item"><text class="t1">订单编号</text><text class="t2" user-select="true" selectable="true">{{detail.ordernum}}</text></view><view class="item"><text class="t1">下单时间</text><text class="t2">{{detail.createtime}}</text></view><view class="item"><text class="t1">预约时间</text><text class="t2">{{detail.yy_time}}</text></view></view><block wx:if="{{$root.g0>0}}"><view class="orderinfo"><block wx:for="{{detail.formdata}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="item"><text class="t1">{{item[0]}}</text><block wx:if="{{item[2]=='upload'}}"><view class="t2"><image style="width:400rpx;height:auto;" src="{{item[1]}}" mode="widthFix" data-url="{{item[1]}}" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image></view></block><block wx:else><block wx:if="{{item[2]=='upload_video'}}"><view class="t2"><video style="width:100%;" src="{{item[1]}}"></video></view></block><block wx:else><block wx:if="{{item[2]=='upload_pics'}}"><view class="t2"><block wx:for="{{item[1]}}" wx:for-item="vv" wx:for-index="__i0__" wx:key="*this"><block><image style="width:200rpx;height:auto;margin-right:10rpx;" src="{{vv}}" mode="widthFix" data-url="{{vv}}" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image></block></block></view></block><block wx:else><text class="t2" user-select="true" selectable="true">{{item[1]}}</text></block></block></block></view></block></view></block><view class="product"><view class="title">服务信息</view><view class="content"><view data-url="{{'product?id='+prolist.proid}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image src="{{prolist.propic}}"></image></view><view class="detail"><text class="t1">{{prolist.proname}}</text><text class="t2">{{prolist.ggname}}</text><view class="t3"><text class="x1 flex1">{{"￥"+prolist.product_price}}</text><text style="color:#939393;">{{"×"+prolist.num}}</text></view></view></view></view><block wx:if="{{(detail.status==3||detail.status==2)&&(detail.freight_type==3||detail.freight_type==4)}}"><view class="orderinfo"><view class="item flex-col"><text class="t1" style="color:#111;">发货信息</text><text class="t2" style="text-align:left;margin-top:10rpx;padding:0 10rpx;" user-select="true" selectable="true">{{detail.freight_content}}</text></view></view></block><view class="orderinfo"><view class="item"><text class="t1">下单人</text><text class="flex1"></text><image style="width:80rpx;height:80rpx;margin-right:8rpx;" src="{{detail.headimg}}"></image><text style="height:80rpx;line-height:80rpx;">{{detail.nickname}}</text></view><view class="item"><text class="t1">{{$root.m0+"ID"}}</text><text class="t2">{{detail.mid}}</text></view></view><view class="orderinfo"><view class="item"><text class="t1">应付金额</text><text class="t2 red">{{"¥"+detail.product_price}}</text></view><block wx:if="{{detail.leveldk_money>0}}"><view class="item"><text class="t1">{{$root.m1+"折扣"}}</text><text class="t2 red">{{"-¥"+detail.leveldk_money}}</text></view></block><block wx:if="{{detail.manjian_money>0}}"><view class="item"><text class="t1">满减活动</text><text class="t2 red">{{"-¥"+detail.manjian_money}}</text></view></block><block wx:if="{{detail.freight_type==1&&detail.freightprice>0}}"><view class="item"><text class="t1">服务费</text><text class="t2 red">{{"+¥"+detail.freight_price}}</text></view></block><block wx:if="{{detail.freight_time}}"><view class="item"><text class="t1">{{(detail.freight_type!=1?'配送':'提货')+"时间"}}</text><text class="t2">{{detail.freight_time}}</text></view></block><block wx:if="{{detail.coupon_money>0}}"><view class="item"><text class="t1">{{$root.m2+"抵扣"}}</text><text class="t2 red">{{"-¥"+detail.coupon_money}}</text></view></block><block wx:if="{{detail.scoredk_money>0}}"><view class="item"><text class="t1">{{$root.m3+"抵扣"}}</text><text class="t2 red">{{"-¥"+detail.scoredk_money}}</text></view></block><view class="item"><text class="t1">实付款</text><text class="t2 red">{{"¥"+detail.totalprice}}</text></view><block wx:if="{{detail.showfeedetail&&(detail.status==1||detail.status==2||detail.status==3)}}"><block><block wx:if="{{detail.worker_id}}"><view class="item"><text class="t1">服务提成</text><text class="t2 red">{{"¥"+detail.ticheng}}</text></view></block><block wx:if="{{detail.bid>0}}"><block><block wx:if="{{detail.plateform_fee>0}}"><view class="item"><text class="t1">平台抽成</text><text class="t2 red">{{"¥"+detail.plateform_fee}}</text></view></block><block wx:if="{{detail.js_totalprice>0&&detail.totalprice!=detail.js_totalprice}}"><view class="item"><text class="t1">预估结算</text><text class="t2 red">{{"¥"+detail.js_totalprice}}</text></view></block></block></block></block></block><view class="item"><text class="t1">订单状态</text><block wx:if="{{detail.status==0}}"><text class="t2">未付款</text></block><block wx:if="{{detail.status==1}}"><text class="t2">已付款</text></block><block wx:if="{{detail.status==2}}"><text class="t2">服务中</text></block><block wx:if="{{detail.status==3}}"><text class="t2">已完成</text></block><block wx:if="{{detail.status==4}}"><text class="t2">已关闭</text></block><block wx:if="{{detail.refundCount}}"><text style="margin-left:8rpx;">{{"有退款("+detail.refundCount+")"}}</text></block></view><block wx:if="{{detail.refundingMoneyTotal>0}}"><view class="item"><text class="t1">退款中</text><text class="t2 red" data-url="{{'refundlist?orderid='+detail.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">{{"¥"+detail.refundingMoneyTotal}}</text><text class="t3 iconfont iconjiantou" style="color:#999;font-weight:normal;"></text></view></block><block wx:if="{{detail.refundedMoneyTotal>0}}"><view class="item"><text class="t1">已退款</text><text class="t2 red" data-url="{{'refundlist?orderid='+detail.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">{{"¥"+detail.refundedMoneyTotal}}</text><text class="t3 iconfont iconjiantou" style="color:#999;font-weight:normal;"></text></view></block><block wx:if="{{detail.refund_status>0}}"><view class="item"><text class="t1">退款状态</text><block wx:if="{{detail.refund_status==1}}"><text class="t2 red">审核中</text></block><block wx:if="{{detail.refund_status==2}}"><text class="t2 red">已退款</text></block><block wx:if="{{detail.refund_status==3}}"><text class="t2 red">已驳回</text></block></view></block><block wx:if="{{detail.balance_price>0}}"><view class="item"><text class="t1">尾款</text><text class="t2 red">{{"¥"+detail.balance_price}}</text></view></block><block wx:if="{{detail.balance_price>0}}"><view class="item"><text class="t1">尾款状态</text><block wx:if="{{detail.balance_pay_status==1}}"><text class="t2">已支付</text></block><block wx:if="{{detail.balance_pay_status==0}}"><text class="t2">未支付</text></block></view></block><block wx:if="{{detail.status>0&&detail.paytypeid!='4'&&detail.paytime}}"><view class="item"><text class="t1">支付时间</text><text class="t2">{{detail.paytime}}</text></view></block><block wx:if="{{detail.paytypeid}}"><view class="item"><text class="t1">支付方式</text><text class="t2">{{detail.paytype}}</text></view></block><block wx:if="{{detail.status>1&&detail.send_time}}"><view class="item"><text class="t1">派单时间</text><text class="t2">{{detail.send_time}}</text></view></block><block wx:if="{{detail.status==3&&detail.collect_time}}"><view class="item"><text class="t1">完成时间</text><text class="t2">{{detail.collect_time}}</text></view></block><block wx:if="{{detail.addmoney>0}}"><view class="item"><text class="t1">补差价</text><text class="t2 red">{{"¥"+detail.addmoney}}</text></view></block><view class="item"><text class="t1">服务方式</text><text class="t2"><block wx:if="{{detail.fwtype==2}}"><block>{{''+text['上门服务']+''}}</block></block><block wx:if="{{detail.fwtype==3}}"><block>到商家服务</block></block><block wx:if="{{detail.fwtype==1}}"><block>{{''+text['到店服务']+''}}</block></block></text></view><block wx:if="{{detail.fwbid&&detail.fwbinfo}}"><block><view class="item"><text class="t1">商家名称</text><text class="t2">{{detail.fwbinfo.name}}</text></view><block wx:if="{{detail.fwbinfo.address}}"><view class="item"><view class="t1">商家地址</view><block wx:if="{{!detail.fwbinfo.latitude||!detail.fwbinfo.longitude}}"><view class="t2">{{''+detail.fwbinfo.address+''}}</view></block><block wx:else><view class="t2" data-latitude="{{detail.fwbinfo.latitude}}" data-longitude="{{detail.fwbinfo.longitude}}" data-event-opts="{{[['tap',[['openLocation',['$event']]]]]}}" bindtap="__e">{{''+detail.fwbinfo.address+''}}</view></block></view></block></block></block></view><view class="orderinfo"><view class="title">顾客信息</view><view class="item"><text class="t1">姓名</text><text class="t2">{{detail.linkman}}</text></view><view class="item"><text class="t1">手机号</text><text class="t2" data-url="{{'tel:'+detail.tel}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">{{detail.tel}}</text></view><block wx:if="{{detail.fwtype==2}}"><view class="item"><text class="t1">上门地址</text><text class="t2" data-text="{{detail.area2+detail.address}}" data-event-opts="{{[['tap',[['copy',['$event']]]]]}}" bindtap="__e">{{detail.area2+detail.address}}</text></view></block></view><view style="width:100%;height:120rpx;"></view><view class="bottom"><block wx:if="{{detail.refund_status==1}}"><view class="btn2" data-id="{{detail.id}}" data-event-opts="{{[['tap',[['refundnopass',['$event']]]]]}}" bindtap="__e">退款驳回</view></block><block wx:if="{{detail.refund_status==1}}"><view class="btn2" data-id="{{detail.id}}" data-event-opts="{{[['tap',[['refundpass',['$event']]]]]}}" bindtap="__e">退款通过</view></block><block wx:if="{{detail.status==0}}"><view class="btn2" data-id="{{detail.id}}" data-event-opts="{{[['tap',[['closeOrder',['$event']]]]]}}" bindtap="__e">关闭订单</view></block><block wx:if="{{detail.status==0&&detail.bid==0}}"><view class="btn2" data-id="{{detail.id}}" data-event-opts="{{[['tap',[['ispay',['$event']]]]]}}" bindtap="__e">改为已支付</view></block><block wx:if="{{detail.status==1&&detail.can_paidan&&!detail.worker_id&&showlist}}"><view class="btn2" data-url="{{'/adminExt/yuyue/selectworker?id='+detail.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">派单</view></block><block wx:if="{{detail.status==1&&detail.worker_id&&showlist}}"><view class="btn2" data-url="{{'/adminExt/yuyue/selectworker?id='+detail.id+'&type=update'}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">改派</view></block><block wx:else><block wx:if="{{detail.status==1&&!detail.worker_id&&!showlist}}"><view class="btn2" data-id="{{detail.id}}" data-event-opts="{{[['tap',[['peisong',['$event']]]]]}}" bindtap="__e">派单</view></block></block><block wx:if="{{(detail.status==2||detail.status==3||detail.worker_id>0)&&detail.status!=4}}"><block><view class="btn2" data-express_com="{{detail.express_com}}" data-express_no="{{detail.worker_orderid}}" data-event-opts="{{[['tap',[['logistics',['$event']]]]]}}" bindtap="__e">查进度</view></block></block><view class="btn2" data-id="{{detail.id}}" data-event-opts="{{[['tap',[['setremark',['$event']]]]]}}" bindtap="__e">设置备注</view><block wx:if="{{detail.status==3||detail.status==4}}"><block><view class="btn2" data-id="{{detail.id}}" data-event-opts="{{[['tap',[['todel',['$event']]]]]}}" bindtap="__e">删除订单</view></block></block></view><uni-popup class="vue-ref" vue-id="312742d8-1" id="dialogSetremark" type="dialog" data-ref="dialogSetremark" bind:__l="__l" vue-slots="{{['default']}}"><uni-popup-dialog vue-id="{{('312742d8-2')+','+('312742d8-1')}}" mode="input" title="设置备注" value="{{detail.remark}}" placeholder="请输入备注" data-event-opts="{{[['^confirm',[['setremarkconfirm']]]]}}" bind:confirm="__e" bind:__l="__l"></uni-popup-dialog></uni-popup><uni-popup class="vue-ref" vue-id="312742d8-3" id="dialogPeisong" type="dialog" data-ref="dialogPeisong" bind:__l="__l" vue-slots="{{['default']}}"><view class="uni-popup-dialog"><view class="uni-dialog-title"><text class="uni-dialog-title-text">请选择配送员</text></view><view class="uni-dialog-content"><view><picker style="font-size:24rpx;border:1px #eee solid;padding:10rpx;height:70rpx;border-radius:4px;flex:1;" value="{{index2}}" range="{{peisonguser2}}" data-event-opts="{{[['change',[['peisongChange',['$event']]]]]}}" bindchange="__e"><view class="picker">{{peisonguser2[index2]}}</view></picker></view></view><view class="uni-dialog-button-group"><view data-event-opts="{{[['tap',[['dialogPeisongClose',['$event']]]]]}}" class="uni-dialog-button" bindtap="__e"><text class="uni-dialog-button-text">取消</text></view><view data-event-opts="{{[['tap',[['confirmPeisong',['$event']]]]]}}" class="uni-dialog-button uni-border-left" bindtap="__e"><text class="uni-dialog-button-text uni-button-color">确定</text></view></view></view></uni-popup></block></block><block wx:if="{{loading}}"><loading vue-id="312742d8-4" bind:__l="__l"></loading></block><dp-tabbar vue-id="312742d8-5" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="312742d8-6" data-ref="popmsg" bind:__l="__l"></popmsg></view>