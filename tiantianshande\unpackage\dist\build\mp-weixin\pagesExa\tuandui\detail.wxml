<view><block wx:if="{{isload}}"><block><view class="container"><view class="header"><block wx:if="{{activityInfo.title}}"><text class="title">{{activityInfo.title}}</text></block><view class="activity-meta"><text class="meta-item">{{activityInfo.performance_name||(activityInfo.performance_type==1?'团队金额':'团队数量')}}</text><text class="meta-item">{{activityInfo.reward_name||(activityInfo.reward_type==1?'比例奖励':'固定金额')}}</text><text class="{{['meta-item','algorithm-tag',activityInfo.algorithm_type==='layered_reduction'?'algorithm-layered':'algorithm-standard']}}">{{''+(activityInfo.algorithm_name||(activityInfo.algorithm_type==='layered_reduction'?'分层递减算法':'传统算法'))+''}}</text></view><view class="activity-description"><block wx:if="{{activityInfo.time_range}}"><view class="desc-item"><text class="desc-label">活动时间：</text><text class="desc-value">{{activityInfo.time_range.start_date+" ~ "+activityInfo.time_range.end_date}}</text></view></block><block wx:if="{{activityInfo.min_amount}}"><view class="desc-item"><text class="desc-label">起始业绩：</text><text class="desc-value">{{activityInfo.min_amount}}</text></view></block><block wx:if="{{activityInfo.deduct_contribution_text}}"><view class="desc-item"><text class="desc-label">贡献值扣除：</text><text class="desc-value">{{activityInfo.deduct_contribution_text}}</text></view></block><block wx:if="{{activityInfo.description}}"><view class="desc-item"><text class="desc-label">活动说明：</text><text class="desc-value">{{activityInfo.description}}</text></view></block><view class="desc-item"><text class="desc-label">创建时间：</text><text class="desc-value">{{activityInfo.createtime}}</text></view></view></view><view class="rules-box"><view class="rules-title">奖励规则</view><view class="rules-content"><block wx:for="{{rewardRules}}" wx:for-item="rule" wx:for-index="index" wx:key="index"><block><view class="rule-item"><view class="rule-level">{{"等级 "+(index+1)}}</view><view class="rule-details"><view class="rule-achievement"><text class="rule-label">目标业绩：</text><text class="rule-value">{{rule.achievement}}</text></view><view class="rule-reward"><text class="rule-label">奖励：</text><text class="rule-value reward-highlight">{{''+(activityInfo.reward_type==1?rule.reward_value+'%':'¥'+rule.reward_value)+''}}</text></view></view><block wx:if="{{activityInfo.reward_type==1}}"><view class="rule-calculation">{{'按业绩的 '+rule.reward_value+'% 发放奖励'}}</view></block><block wx:else><view class="rule-calculation">{{'固定奖励 ¥'+rule.reward_value+''}}</view></block><block wx:if="{{activityInfo.algorithm_type==='layered_reduction'}}"><view class="algorithm-note"><text class="note-text">注：使用分层递减算法，实际奖励 = 理论奖励 - 直接下级理论奖励</text></view></block></view></block></block></view></view><view class="conditions-box"><view class="conditions-title">适用条件</view><view class="conditions-content"><block wx:if="{{activityInfo.level_names}}"><view class="condition-item"><view class="condition-label">适用等级：</view><view class="condition-value">{{activityInfo.level_names}}</view></view></block><block wx:if="{{activityInfo.product_names}}"><view class="condition-item"><view class="condition-label">适用商品：</view><view class="condition-value">{{activityInfo.product_names}}</view></view></block><view class="condition-item"><view class="condition-label">统计类型：</view><view class="condition-value">{{activityInfo.statistics_type==1?'已付款订单':activityInfo.statistics_type==2?'已收货订单':'全部订单'}}</view></view></view></view><block wx:if="{{userRewardInfo&&userRewardInfo.user_performance}}"><view class="user-status-box"><view class="status-title">我的状态</view><view class="status-content"><view class="current-performance"><view class="perf-label">当前团队业绩</view><view class="perf-value">{{userRewardInfo.user_performance.team_performance+(activityInfo.performance_type==1?'元':'件')}}</view></view><block wx:if="{{userRewardInfo.team_structure}}"><view class="team-info"><view class="team-count">{{"团队成员："+userRewardInfo.team_structure.total_members+"人"}}</view><view class="team-levels">{{"团队层级："+userRewardInfo.team_structure.max_level+"级"}}</view></view></block><view class="reward-progress"><view class="progress-label">奖励进度</view><view class="progress-details"><view class="progress-item"><text class="progress-name">理论奖励</text><text class="progress-amount">{{"¥"+(userRewardInfo.user_performance.theoretical_reward||0)}}</text></view><block wx:if="{{activityInfo.algorithm_type==='layered_reduction'}}"><view class="progress-item"><text class="progress-name">实际奖励</text><text class="progress-amount success">{{"¥"+(userRewardInfo.user_performance.actual_reward||0)}}</text></view></block></view><block wx:if="{{userRewardInfo.user_performance.calculation_detail}}"><view class="calculation-explanation"><text class="explanation-text">{{userRewardInfo.user_performance.calculation_detail}}</text></view></block></view></view></view></block><block wx:if="{{userRewardInfo&&userRewardInfo.reward_details}}"><view class="reward-details-box"><view class="details-title">奖励详情</view><view class="details-content"><block wx:for="{{userRewardInfo.reward_details}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><view class="detail-item"><view class="detail-header"><view class="achievement-info"><text class="achievement-label">{{"等级 "+item.achievement_level}}</text><text class="achievement-target">{{"目标："+item.achievement_target}}</text></view><view class="reward-info"><text class="reward-value">{{item.reward_value+(activityInfo.reward_type==1?'%':'元')}}</text></view></view><view class="detail-amounts"><view class="amount-item"><text class="amount-label">理论奖励：</text><text class="amount-value theoretical">{{"¥"+item.theoretical_reward_amount}}</text></view><block wx:if="{{activityInfo.algorithm_type==='layered_reduction'&&item.actual_reward_amount!==item.theoretical_reward_amount}}"><view class="amount-item"><text class="amount-label">实际奖励：</text><text class="amount-value actual">{{"¥"+item.actual_reward_amount}}</text></view></block></view><view class="detail-status"><view class="{{['status-badge',item.is_achieved?item.is_paid?'status-paid':item.is_claimed?'status-claimed':'status-achieved':'status-not-achieved']}}">{{''+(item.is_achieved?item.is_paid?'已发放':item.is_claimed?'已领取':'已达成':'未达成')+''}}</view><block wx:if="{{item.is_achieved&&!item.is_claimed&&!item.is_paid}}"><view class="claim-action"><text class="claim-btn" data-activity="{{item.activity_id}}" data-level="{{item.achievement_level}}" data-event-opts="{{[['tap',[['claimReward',['$event']]]]]}}" bindtap="__e">立即领取</text></view></block></view><block wx:if="{{item.calculation}}"><view class="calculation-desc">{{''+item.calculation+''}}</view></block></view></block></block></view></view></block><view class="action-box"><view data-event-opts="{{[['tap',[['refreshData',['$event']]]]]}}" class="action-btn primary" bindtap="__e">刷新数据</view><view data-event-opts="{{[['tap',[['gotoRecords',['$event']]]]]}}" class="action-btn secondary" bindtap="__e">查看记录</view></view></view></block></block><block wx:if="{{loading}}"><loading vue-id="c7ec91ee-1" bind:__l="__l"></loading></block><dp-tabbar vue-id="c7ec91ee-2" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="c7ec91ee-3" data-ref="popmsg" bind:__l="__l"></popmsg></view>