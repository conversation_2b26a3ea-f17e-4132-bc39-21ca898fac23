<view style="width:100%;"><view class="dp-product-item"><block wx:for="{{data}}" wx:for-item="item" wx:for-index="index" wx:key="id"><view class="item" style="{{(showstyle==2?'width:49%;margin-right:'+(index%2==0?'2%':0):showstyle==3?'width:32%;margin-right:'+(index%3!=2?'2%':0):'width:100%')}}" data-url="{{'/yuyue/product?id='+item[idfield]}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="product-pic"><image class="image" src="{{item.pic}}" mode="widthFix"></image><block wx:if="{{saleimg!=''}}"><image class="saleimg" src="{{saleimg}}" mode="widthFix"></image></block></view><view class="product-info"><block wx:if="{{showname==1}}"><view class="p1">{{item.name}}</view></block><view class="p2"><block wx:if="{{showprice!='0'}}"><view class="p2-1"><text class="t1" style="{{'color:'+($root.m0)+';'}}">{{item.sell_price}}<text style="font-size:24rpx;padding-left:4rpx;">{{"元/"+item.danwei}}</text></text></view></block></view><block wx:if="{{showsales=='1'&&item.sales>0}}"><view class="p3">{{"已售"+item.sales}}</view></block></view></view></block></view><block wx:if="{{buydialogShow}}"><buydialog vue-id="1e9990d8-1" proid="{{proid}}" menuindex="{{menuindex}}" data-event-opts="{{[['^addcart',[['addcart']]],['^buydialogChange',[['buydialogChange']]]]}}" bind:addcart="__e" bind:buydialogChange="__e" bind:__l="__l"></buydialog></block></view>