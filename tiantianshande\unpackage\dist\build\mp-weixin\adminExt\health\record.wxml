<view class="container"><block wx:if="{{isload}}"><block><view class="top"><view class="top-picker"><picker mode="selector" range="{{healthlist}}" range-key="name" data-field="health" data-event-opts="{{[['change',[['pickerChange',['$event']]]]]}}" bindchange="__e"><view class="picker"><view class="picker-txt">{{health_index>-1?healthlist[health_index].name:'全部量表'}}</view><image class="down" src="{{pre_url+'/static/img/location/down-black.png'}}"></image></view></picker><picker mode="selector" range="{{bidlist}}" range-key="name" data-field="bid" data-event-opts="{{[['change',[['pickerChange',['$event']]]]]}}" bindchange="__e"><view class="picker"><view class="picker-txt">{{bid_index>-1?bidlist[bid_index].name:'全部门店'}}</view><image class="down" src="{{pre_url+'/static/img/location/down-black.png'}}"></image></view></picker><block><block wx:if="{{startDate}}"><view data-event-opts="{{[['tap',[['toCheckDate',['$event']]]]]}}" class="picker pickerD" bindtap="__e"><view class="picker-date"><block wx:if="{{startDate}}"><view class="picker-row">{{startDate}}</view></block><view class="picker-row">{{endDate?endDate:'至今'}}</view></view><view data-event-opts="{{[['tap',[['clearDate',['$event']]]]]}}" class="picker-clear" catchtap="__e">清除</view></view></block><block wx:else><view data-event-opts="{{[['tap',[['toCheckDate',['$event']]]]]}}" class="picker pickerD" bindtap="__e"><view>不限日期</view><image class="down" src="{{pre_url+'/static/img/location/down-black.png'}}"></image></view></block></block></view><view class="top-search"><image class="search-icon" src="{{pre_url+'/static/img/search_ico.png'}}"></image><input class="input" type="text" placeholder="输入姓名|手机号检索" placeholder-style="font-size:26rpx;color:#999" data-event-opts="{{[['confirm',[['searchConfirm',['$event']]]],['input',[['__set_model',['','keyword','$event',[]]]]]]}}" value="{{keyword}}" bindconfirm="__e" bindinput="__e"/></view></view><view class="main"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><view class="item" data-url="{{'result?id='+item.$orig.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e"><view class="header"><view class="flex-sb"><view class="col">{{"姓名："+item.$orig.name}}</view><view class="col">{{"电话："+item.$orig.tel}}</view></view><view class="flex-sb"><view class="col">{{"年龄："+item.$orig.age+"岁"}}</view><view class="col">{{"性别："+(item.$orig.sex==2?'女':'男')}}</view></view><view class="flex-sb">{{'家庭地址：'+item.$orig.address+''}}</view></view><view class="info"><view><view>{{"评测量表："+item.$orig.ha_name}}</view><block wx:if="{{item.$orig.score>0}}"><view>{{"评测结果："+item.$orig.score+'分'}}<text class="scoretag">{{item.$orig.score_tag}}</text></view></block><view>{{"评测时间："+item.$orig.createtime}}</view><view>{{"选择门店："+item.$orig.bname}}</view></view><view class="btn" style="{{'background:'+(item.m0)+';'+('color:'+('#FFFFFF')+';')}}">查看详情</view></view></view></block></block></view><block wx:if="{{nomore}}"><nomore vue-id="2182b89e-1" bind:__l="__l"></nomore></block><block wx:if="{{nodata}}"><nodata vue-id="2182b89e-2" bind:__l="__l"></nodata></block></block></block><block wx:if="{{loading}}"><loading vue-id="2182b89e-3" bind:__l="__l"></loading></block><popmsg class="vue-ref" vue-id="2182b89e-4" data-ref="popmsg" bind:__l="__l"></popmsg></view>