<view class="daihuobiji-detail-page" style="{{('padding-top:'+totalBarHeight+'px;')}}"><block wx:if="{{loading}}"><view class="skeleton"><view class="skeleton-swiper"></view><view class="skeleton-info"><view class="skeleton-title"></view><view class="skeleton-content"></view><view class="skeleton-time"></view></view></view></block><block wx:else><block wx:if="{{detailInfo}}"><detail-nav vue-id="765232bd-1" userInfo="{{detailInfo}}" noteId="{{$root.m0}}" bind:__l="__l"></detail-nav></block><view class="cover-swipper"><swiper class="swiper" style="{{'height:'+(swiperHeight+'px')+';'}}" circular="{{true}}" indicator-dots="#D9D9D9" autoplay="{{false}}" interval="{{interval}}" indicator-active-color="#E3582F" duration="{{duration}}" data-event-opts="{{[['change',[['onSwiperChange',['$event']]]]]}}" bindchange="__e"><block wx:for="{{detailInfo.videoAndImg}}" wx:for-item="item" wx:for-index="index" wx:key="index"><swiper-item><view class="swiper-item"><block wx:if="{{item.type=='img'}}"><image class="cover-img" src="{{item.url}}" mode="widthFix" data-event-opts="{{[['load',[['onImageLoad',['$event',index]]]],['tap',[['openImagePreview',[index]]]]]}}" bindload="__e" bindtap="__e"></image></block><block wx:if="{{item.type=='video'}}"><video src="{{item.url}}"></video></block></view></swiper-item></block></swiper></view><view class="info-box"><text class="title-text">{{detailInfo.title||''}}</text><text class="content-text">{{detailInfo.content}}</text><view class="create-time">{{detailInfo.showtime}}</view><block wx:if="{{$root.m1>0}}"><view class="commission-box"><view class="commission-content"><image class="commission-icon" src="{{pre_url+'/static/img/shortvideo_cart.png'}}" mode="aspectFit"></image><view class="commission-info"><text class="commission-text">预计佣金</text><text class="commission-amount">{{"￥"+totalCommission}}</text><text class="commission-unit">{{commission_desc}}</text></view><text class="commission-tips">分享商品后用户下单可获得</text></view></view></block><block wx:if="{{detailInfo.shop_id>0||detailInfo.leader_id>0}}"><view class="action-buttons"><block wx:if="{{detailInfo.shop_id>0}}"><view class="action-btn shop" style="{{('background: '+$root.m2)}}" data-url="{{'/pagesExt/business/index?id='+detailInfo.shop_id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image src="{{pre_url+'/static/img/set.png'}}" mode="widthFix"></image><text>TA的店铺</text></view></block><block wx:if="{{detailInfo.leader_id>0}}"><view class="action-btn team" style="{{('background: '+$root.m3)}}" data-url="{{'/daihuobiji/kuaituan/tuanzhangtuanlist?bid='+detailInfo.leader_id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image src="{{pre_url+'/static/img/add.png'}}" mode="widthFix"></image><text>带货团</text></view></block></view></block><block wx:if="{{detailInfo.mid==mid}}"><view class="action-buttons"><view data-event-opts="{{[['tap',[['editNote',['$event']]]]]}}" class="action-btn edit" bindtap="__e"><image src="{{pre_url+'/static/img/set.png'}}" mode="widthFix"></image><text>编辑</text></view><view data-event-opts="{{[['tap',[['deleteNote',['$event']]]]]}}" class="action-btn delete" bindtap="__e"><image src="{{pre_url+'/static/img/close2.png'}}" mode="widthFix"></image><text>删除</text></view></view></block></view><view class="comment-box"><view class="top-tips">{{"共"+$root.g0+"条评论"}}</view><view class="comment-input-box" style="margin:15px 0;" data-url="{{'pinglun?type=0&id='+detailInfo.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><input class="comment-input" placeholder="请在此评论"/></view><view class="plbox_content" style="padding-bottom:50px;" id="datalist"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="idx" wx:key="idx"><block><view class="item1 flex"><view class="f1 flex0"><image src="{{item.$orig.headimg}}"></image></view><view class="f2 flex-col"><text class="t1">{{item.$orig.nickname}}</text><text class="t11">{{item.$orig.createtime}}</text><view class="t2 plcontent"><parse vue-id="{{'765232bd-2-'+idx}}" content="{{item.$orig.content}}" bind:__l="__l"></parse></view><block wx:if="{{item.g1>0}}"><block><view class="relist"><block wx:for="{{item.$orig.replylist}}" wx:for-item="hfitem" wx:for-index="index" wx:key="index"><block><view class="item2"><view class="f1">{{''+hfitem.nickname+''}}<text class="t1">{{hfitem.createtime}}</text><block wx:if="{{hfitem.mid==mid}}"><text class="phuifu" style="font-size:20rpx;margin-left:20rpx;font-weight:normal;" data-id="{{hfitem.id}}" data-event-opts="{{[['tap',[['delplreply',['$event']]]]]}}" bindtap="__e">删除</text></block></view><view class="f2 plcontent"><parse vue-id="{{'765232bd-3-'+idx+'-'+index}}" content="{{hfitem.content}}" bind:__l="__l"></parse></view></view></block></block></view></block></block><view class="t3 flex"><view class="flex1"><text class="phuifu" style="cursor:pointer;" data-url="{{'pinglun?type=1&id='+detailInfo.id+'&hfid='+item.$orig.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">回复</text><block wx:if="{{item.$orig.mid==mid}}"><text class="phuifu" style="cursor:pointer;margin-left:20rpx;" data-id="{{item.$orig.id}}" data-event-opts="{{[['tap',[['delpinglun',['$event']]]]]}}" bindtap="__e">删除</text></block></view><view class="flex-y-center pzan" data-id="{{detailInfo.id}}" data-index="{{idx}}" data-event-opts="{{[['tap',[['pzan',['$event']]]]]}}" bindtap="__e"><image src="{{pre_url+'/static/img/lt_like'+(item.$orig.iszan==1?'2':'')+'.png'}}"></image>{{''+item.$orig.zan+''}}</view></view></view></view></block></block></view></view><view class="footer-box"><view class="buy"><view class="good-img" style="margin-top:0px;"><image style="width:100%;height:100%;border-radius:50px;" src="{{yindaologo}}"></image></view><view data-event-opts="{{[['tap',[['openShop',['$event']]]]]}}" class="buy-text" bindtap="__e"><text class="text-tips">{{linkname}}</text></view></view><view class="footer-actions"><view class="action-item" data-url="{{'pinglun?type=0&id='+detailInfo.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image class="action-item-img" src="{{pre_url+'/static/img/daihuobiji_comment.png'}}"></image><text class="action-item-label">评论</text></view><view class="action-item" data-id="{{detailInfo.id}}" data-event-opts="{{[['tap',[['zan',['$event']]]]]}}" bindtap="__e"><image class="action-item-img" src="{{pre_url+'/static/img/lt_like'+(iszan?'2':'')+'.png'}}"></image><text class="action-item-label">{{''+detailInfo.zan+''}}</text></view></view></view><uni-popup class="vue-ref" vue-id="765232bd-4" type="bottom" data-ref="shopShow" bind:__l="__l" vue-slots="{{['default']}}"><view class="viewShop"><view style="padding:20rpx;position:sticky;top:0;background:#fff;z-index:1;"><view style="text-align:center;font-weight:bold;margin-bottom:10rpx;">{{'文中提到的商品（'+$root.g2+'）'}}</view><view style="text-align:center;font-size:24rpx;color:#999;">预估佣金合计:<text style="color:#E54D42;font-weight:bold;">{{"￥"+totalCommission}}</text></view></view><view class="cart-container"><scroll-view style="height:50vh;" scroll-top="{{0}}" scroll-y="true"><block wx:for="{{matchedData}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="cart-item"><view data-event-opts="{{[['tap',[['toggleSelection',['$0'],[[['matchedData','',index]]]]]]]}}" class="item-selector" bindtap="__e"><checkbox style="transform:scale(0.8);" checked="{{item.selected}}"></checkbox></view><view class="image-box" data-url="{{'/shopPackage/shop/product?id='+item.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image class="item-image" src="{{item.pic}}" mode="heightFix"></image></view><view class="item-info"><text class="item-name" data-url="{{'/shopPackage/shop/product?id='+item.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">{{item.name}}</text><view style="display:flex;justify-content:space-between;width:100%;"><view><text class="item-price"><label class="_span">￥</label><label style="font-size:34rpx;font-weight:bold;" class="_span">{{item.sell_price}}</label></text><block wx:if="{{item.commission>0}}"><text style="font-size:20rpx;color:#999;margin-left:8px;">{{'预估佣金 ￥'+item.commission+item.commission_desc+''}}</text></block></view><view data-event-opts="{{[['tap',[['addCartOne',['$0'],[[['matchedData','',index]]]]]]]}}" style="background:#E54D42;color:#fff;border-radius:25px;padding:3px 12px;font-size:11px;line-height:1.2;white-space:nowrap;height:20px;display:flex;align-items:center;" catchtap="__e">加入购物车</view></view></view></view></block></scroll-view><view style="position:sticky;bottom:0;height:100rpx;display:flex;justify-content:space-between;align-items:center;padding:0 20rpx;background:#fff;"><view data-event-opts="{{[['tap',[['shopAllSelectedClick',['$event']]]]]}}" bindtap="__e"><checkbox style="transform:scale(0.8);" checked="{{shopAllSelected}}"></checkbox>全选</view><view style="text-align:center;margin-left:100px;position:relative;" data-url="/shopPackage/shop/cart" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view><image style="width:25px;height:25px;" src="{{pre_url+'/static/icon/buy.png'}}"></image></view><block wx:if="{{cart_num>0}}"><view style="position:absolute;top:-5px;right:0;background:red;color:#fff;border-radius:50px;padding:0 5px;">{{cart_num}}</view></block></view><view data-event-opts="{{[['tap',[['shop',['$event']]]]]}}" class="{{[isAnyItemSelected?'shopButtonActive':'shopButton']}}" bindtap="__e">加入购物车</view></view></view></view></uni-popup></block><uni-popup class="vue-ref" vue-id="765232bd-5" type="bottom" maskClick="{{false}}" animation="{{false}}" data-ref="specPopup" bind:__l="__l" vue-slots="{{['default']}}"><view class="spec-dialog"><block wx:if="{{buydialogShow}}"><buydialog vue-id="{{('765232bd-6')+','+('765232bd-5')}}" proid="{{product_id}}" tid="{{tid}}" btntype="{{btntype}}" data-event-opts="{{[['^buydialogChange',[['buydialogChange']]],['^showLinkChange',[['showLinkChange']]],['^addcart',[['addcart']]]]}}" bind:buydialogChange="__e" bind:showLinkChange="__e" bind:addcart="__e" bind:__l="__l"></buydialog></block></view></uni-popup></view>