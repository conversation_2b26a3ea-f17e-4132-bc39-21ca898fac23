<view class="container"><block wx:if="{{isload}}"><block><view class="orderinfo"><view class="item"><text class="t1">社区名称</text><text class="t2">{{detail.xqname}}</text></view><view class="item"><text class="t1">姓名</text><text class="t2" user-select="true" selectable="true">{{detail.name}}</text></view><view class="item"><text class="t1">电话</text><text class="t2">{{detail.tel}}</text></view><view class="item"><text class="t1">提现方式</text><text class="t2">{{detail.paytype}}</text></view><view class="item"><text class="t1">提现金额</text><text class="t2">{{"￥"+detail.txmoney}}</text></view><view class="item"><text class="t1">打款金额</text><text class="t2">{{"￥"+detail.money}}</text></view><view class="item"><text class="t1">申请时间</text><text class="t2">{{detail.createtime}}</text></view><view class="item"><text class="t1">状态</text><block wx:if="{{detail.status==0}}"><text class="t2 st0">待审核</text></block><block wx:if="{{detail.status==1}}"><text class="t2 st1">已审核</text></block><block wx:if="{{detail.status==2}}"><text class="t2 st2">已驳回</text></block><block wx:if="{{detail.status==3}}"><text class="t2 st3">已打款</text></block></view></view><block><view class="orderinfo"><view class="title">收款账号</view><block wx:if="{{detail.paytype=='微信钱包'}}"><block><view class="item"><text class="t1">微信号</text><text class="t2" user-select="true" selectable="true">{{detail.weixin}}</text><text class="copy" style="color:#B0543D;margin-left:20rpx;font-weight:bold;" data-text="{{detail.weixin}}" data-event-opts="{{[['tap',[['copy',['$event']]]]]}}" catchtap="__e">复制</text></view></block></block><block wx:if="{{detail.paytype=='支付宝'}}"><block><view class="item"><text class="t1">姓名</text><text class="t2" user-select="true" selectable="true">{{detail.aliacountname}}</text><text class="copy" style="color:#B0543D;margin-left:20rpx;font-weight:bold;" data-text="{{detail.aliacountname}}" data-event-opts="{{[['tap',[['copy',['$event']]]]]}}" catchtap="__e">复制</text></view><view class="item"><text class="t1">支付宝账号</text><text class="t2">{{detail.aliacount}}</text><text class="copy" style="color:#B0543D;margin-left:20rpx;font-weight:bold;" data-text="{{detail.aliacount}}" data-event-opts="{{[['tap',[['copy',['$event']]]]]}}" catchtap="__e">复制</text></view></block></block><block wx:if="{{detail.paytype=='银行卡'}}"><block><view class="item"><text class="t1">开户银行</text><text class="t2">{{detail.bankname}}</text><text class="copy" style="color:#B0543D;margin-left:20rpx;font-weight:bold;" data-text="{{detail.bankname}}" data-event-opts="{{[['tap',[['copy',['$event']]]]]}}" catchtap="__e">复制</text></view><block wx:if="{{detail.bankcarduser}}"><view class="item"><text class="t1">银行卡姓名</text><text class="t2">{{detail.bankcarduser}}</text><text class="copy" style="color:#B0543D;margin-left:20rpx;font-weight:bold;" data-text="{{detail.bankcarduser}}" data-event-opts="{{[['tap',[['copy',['$event']]]]]}}" catchtap="__e">复制</text></view></block><block wx:if="{{detail.bankcardnum}}"><view class="item"><text class="t1">银行卡号</text><text class="t2">{{detail.bankcardnum}}</text><text class="copy" style="color:#B0543D;margin-left:20rpx;font-weight:bold;" data-text="{{detail.bankcardnum}}" data-event-opts="{{[['tap',[['copy',['$event']]]]]}}" catchtap="__e">复制</text></view></block></block></block></view></block><view style="width:100%;height:160rpx;"></view><view class="bottom notabbarbot"><block wx:if="{{detail.status==0}}"><block><view class="btn2" data-id="{{detail.id}}" data-st="1" data-event-opts="{{[['tap',[['confirm',['$event']]]]]}}" bindtap="__e">通过</view><view class="btn2" data-id="{{detail.id}}" data-st="2" data-event-opts="{{[['tap',[['confirm',['$event']]]]]}}" bindtap="__e">驳回</view></block></block><block wx:if="{{detail.status==1}}"><block><view class="btn2" data-id="{{detail.id}}" data-st="3" data-event-opts="{{[['tap',[['confirm',['$event']]]]]}}" bindtap="__e">确认打款</view><block wx:if="{{detail.paytype=='微信钱包'}}"><block><view class="btn2" data-st="10" data-event-opts="{{[['tap',[['confirm',['$event']]]]]}}" bindtap="__e">微信打款</view></block></block></block></block></view><uni-popup class="vue-ref" vue-id="24ef6e36-1" id="dialogSetremark" type="dialog" data-ref="dialogSetremark" bind:__l="__l" vue-slots="{{['default']}}"><uni-popup-dialog vue-id="{{('24ef6e36-2')+','+('24ef6e36-1')}}" mode="input" title="驳回原因" value="{{detail.remark}}" placeholder="请输入驳回原因" data-event-opts="{{[['^confirm',[['setremarkconfirm']]]]}}" bind:confirm="__e" bind:__l="__l"></uni-popup-dialog></uni-popup></block></block><block wx:if="{{loading}}"><loading vue-id="24ef6e36-3" bind:__l="__l"></loading></block><dp-tabbar vue-id="24ef6e36-4" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="24ef6e36-5" data-ref="popmsg" bind:__l="__l"></popmsg></view>