<view><view data-event-opts="{{[['tap',[['close_address',['$event']]]]]}}" style="width:100%;height:100%;background-color:#000;position:fixed;top:0;opacity:0.45;z-index:9999999;" bindtap="__e"></view><view class="pa_view"><view data-event-opts="{{[['tap',[['close_address',['$event']]]]]}}" class="pa_close" bindtap="__e">x</view><view style="text-align:center;font-size:30rpx;font-weight:bold;">请完善收货信息</view><view style="display:flex;border-bottom:2rpx solid #F6F7F9;"><view style="width:140rpx;">所在地区</view><view style="width:410rpx;overflow:hidden;white-space:nowrap;"><block wx:if="{{area}}"><text>{{area}}</text></block><block wx:else><text style="color:#808080;">所在地区</text></block></view><view data-event-opts="{{[['tap',[['selzb',['$event']]]]]}}" style="width:90rpx;overflow:hidden;" bindtap="__e"><text style="{{('color:'+$root.m0)}}">切换</text><image style="width:30rpx;height:30rpx;float:right;margin-top:24rpx;" src="/static/img/arrowright.png"></image></view></view><view style="display:flex;border-bottom:2rpx solid #F6F7F9;"><view style="width:140rpx;">详细地址</view><view style="width:500rpx;overflow:hidden;white-space:nowrap;"><input style="height:80rpx;line-height:80rpx;" data-name="address" placeholder="街道门牌,楼层等信息" placeholder-style="height:80rpx;line-height:80rpx;color:#808080" data-event-opts="{{[['input',[['inputVal',['$event']]]]]}}" value="{{address}}" bindinput="__e"/></view></view><view style="display:flex;border-bottom:2rpx solid #F6F7F9;"><view style="width:140rpx;">姓名</view><view style="width:500rpx;overflow:hidden;white-space:nowrap;"><input style="height:80rpx;line-height:80rpx;" data-name="name" placeholder="收货人姓名" placeholder-style="height:80rpx;line-height:80rpx;color:#808080" data-event-opts="{{[['input',[['inputVal',['$event']]]]]}}" value="{{name}}" bindinput="__e"/></view></view><view style="display:flex;border-bottom:2rpx solid #F6F7F9;"><view style="width:140rpx;">电话</view><view style="width:360rpx;overflow:hidden;white-space:nowrap;"><block wx:if="{{tel}}"><text>{{tel}}</text></block><block wx:else><text style="color:#808080;">收货人手机号</text></block></view><button class="pa_wx" style="{{('color:'+$root.m1+';border:2rpx solid '+$root.m2)}}" open-type="getPhoneNumber" data-event-opts="{{[['getphonenumber',[['getPhoneNumber',['$event']]]]]}}" bindgetphonenumber="__e">微信授权</button></view><view data-event-opts="{{[['tap',[['saveAddress',['$event']]]]]}}" class="pa_save" style="{{'background:'+($root.m3)+';'}}" bindtap="__e">保存并使用</view></view></view>