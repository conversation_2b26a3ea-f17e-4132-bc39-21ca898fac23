require('../common/vendor.js');(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["admin/order/shoporderdetail"],{"4ca1":function(e,s,t){"use strict";t.r(s);var r=t("7e9e"),n=t("ce32");for(var i in n)["default"].indexOf(i)<0&&function(e){t.d(s,e,(function(){return n[e]}))}(i);t("a17f");var o=t("828b"),a=Object(o["a"])(n["default"],r["b"],r["c"],!1,null,"98a6dfa2",null,!1,r["a"],void 0);s["default"]=a.exports},"4e2ff":function(e,s,t){"use strict";(function(e,s){var r=t("47a9");t("06e9");r(t("3240"));var n=r(t("4ca1"));e.__webpack_require_UNI_MP_PLUGIN__=t,s(n.default)}).call(this,t("3223")["default"],t("df3c")["createPage"])},"7e9e":function(e,s,t){"use strict";t.d(s,"b",(function(){return n})),t.d(s,"c",(function(){return i})),t.d(s,"a",(function(){return r}));var r={uniPopup:function(){return Promise.all([t.e("common/vendor"),t.e("components/uni-popup/uni-popup")]).then(t.bind(null,"ca44a"))},uniPopupDialog:function(){return t.e("components/uni-popup-dialog/uni-popup-dialog").then(t.bind(null,"267c"))},loading:function(){return t.e("components/loading/loading").then(t.bind(null,"ceaa"))},dpTabbar:function(){return t.e("components/dp-tabbar/dp-tabbar").then(t.bind(null,"b875"))},popmsg:function(){return t.e("components/popmsg/popmsg").then(t.bind(null,"2bf2"))}},n=function(){var e=this,s=e.$createElement,t=(e._self._c,e.isload?e.t("会员"):null),r=e.isload&&e.detail.disprice>0?e.t("会员"):null,n=e.isload&&e.detail.coupon_money>0?e.t("优惠券"):null,i=e.isload&&e.detail.scoredk>0?e.t("积分"):null,o=e.isload?e.detail.formdata.length:null,a=e.isload?e.fahuodatas.length:null,d=e.isload?e.prolist.length:null,u=e.isload&&d>=2?e.__map(e.prolist,(function(s,t){var r=e.__get_orig(s),n=s.id.toString();return{$orig:r,g3:n}})):null,c=e.isload?e.__map(e.fahuodatas.slice(1),(function(s,t){var r=e.__get_orig(s),n=e.prolist.length,i=n>=2?e.__map(e.prolist,(function(s,t){var r=e.__get_orig(s),n=s.id.toString();return{$orig:r,g5:n}})):null;return{$orig:r,g4:n,l1:i}})):null,p=e.isload?e.__map(e.fahuodatas,(function(s,t){var r=e.__get_orig(s),n=e.fahuodatas.length,i=e.prolist.length,o=i>=2?e.__map(e.prolist,(function(t,r){var n=e.__get_orig(t),i=t.id.toString(),o=s.express_ogids.includes(t.id.toString());return{$orig:n,g8:i,g9:o}})):null;return{$orig:r,g6:n,g7:i,l3:o}})):null,f=e.isload?e.__map(e.prolist,(function(s,t){var r=e.__get_orig(s),n=s.id.toString();return{$orig:r,g10:n}})):null;e._isMounted||(e.e0=function(s,t){var r=arguments[arguments.length-1].currentTarget.dataset,n=r.eventParams||r["event-params"];t=n.index;return s.stopPropagation(),e.deletefahuo(t+1)},e.e1=function(s,t){var r=arguments[arguments.length-1].currentTarget.dataset,n=r.eventParams||r["event-params"];t=n.index;return e.expresschange_fahuo(s,t+1)},e.e2=function(s,t){var r=arguments[arguments.length-1].currentTarget.dataset,n=r.eventParams||r["event-params"];t=n.index;return e.setexpressno_fahuo(s,t+1)},e.e3=function(s,t){var r=arguments[arguments.length-1].currentTarget.dataset,n=r.eventParams||r["event-params"];t=n.index;return e.checkboxChange_fahuo(s,t+1)}),e.$mp.data=Object.assign({},{$root:{m0:t,m1:r,m2:n,m3:i,g0:o,g1:a,g2:d,l0:u,l2:c,l4:p,l5:f}})},i=[]},"8fc2":function(e,s,t){"use strict";(function(e){var r=t("47a9");Object.defineProperty(s,"__esModule",{value:!0}),s.default=void 0;var n,i=r(t("7ca3"));function o(e,s){var t="undefined"!==typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!t){if(Array.isArray(e)||(t=function(e,s){if(!e)return;if("string"===typeof e)return a(e,s);var t=Object.prototype.toString.call(e).slice(8,-1);"Object"===t&&e.constructor&&(t=e.constructor.name);if("Map"===t||"Set"===t)return Array.from(e);if("Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return a(e,s)}(e))||s&&e&&"number"===typeof e.length){t&&(e=t);var r=0,n=function(){};return{s:n,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:n}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var i,o=!0,d=!1;return{s:function(){t=t.call(e)},n:function(){var e=t.next();return o=e.done,e},e:function(e){d=!0,i=e},f:function(){try{o||null==t.return||t.return()}finally{if(d)throw i}}}}function a(e,s){(null==s||s>e.length)&&(s=e.length);for(var t=0,r=new Array(s);t<s;t++)r[t]=e[t];return r}var d=getApp(),u=null,c={data:function(){return{opt:{},loading:!1,isload:!1,menuindex:-1,dddd:"1111",express_content:"",pre_url:d.globalData.pre_url,expressdata:[],express_index:0,express_no:"",fahuodatas:[{express_index:0,express_no:"",express_ogids:[]}],refund:{reason:"",money:"",refundNum:[]},prodata:"",djs:"",detail:"",prolist:"",shopset:"",storeinfo:"",lefttime:"",codtxt:"",peisonguser:[],peisonguser2:[],index2:0,express_pic:"",express_fhname:"",express_fhaddress:"",express_shname:"",express_shaddress:"",express_remark:"",copyFields:[{label:"收货信息",value:"address",checked:!1},{label:"商品信息",value:"product",checked:!1},{label:"商城订单号",value:"shopordernum",checked:!1},{label:"订单ID",value:"orderid",checked:!1},{label:"快递单号",value:"express",checked:!1},{label:"订单状态",value:"status",checked:!1},{label:"价格信息",value:"price",checked:!1},{label:"运费",value:"freight",checked:!1},{label:"实付款",value:"totalprice",checked:!1},{label:"下单人",value:"buyer",checked:!1}]}},onLoad:function(e){this.opt=d.getopts(e),this.getdata()},onPullDownRefresh:function(){this.getdata()},onUnload:function(){clearInterval(u)},methods:(n={getdata:function(){var e=this;e.loading=!0,d.get("ApiAdminOrder/shoporderdetail",{id:e.opt.id},(function(s){if(e.loading=!1,e.expressdata=s.expressdata,e.detail=s.detail,e.prolist=s.prolist,e.shopset=s.shopset,e.storeinfo=s.storeinfo,e.lefttime=s.lefttime,e.codtxt=s.codtxt,e.fahuodatas=[],s.detail.express_content){var t=JSON.parse(s.detail.express_content);t.forEach((function(t){e.fahuodatas.push({express_index:s.expressdata.indexOf(t.express_com),express_no:t.express_no,express_ogids:t.express_ogids})}))}e.refund.refundNum=[],e.prolist.forEach((function(s){e.refund.refundNum.push({ogid:s.id,num:s.num,selected:!1})})),console.log(e.refund,985654),s.lefttime>0&&(u=setInterval((function(){e.lefttime=e.lefttime-1,e.getdjs()}),1e3)),e.loaded()}))},getdjs:function(){var e=this.lefttime;if(e<=0)this.djs="00时00分00秒";else{var s=Math.floor(e/3600),t=Math.floor((e-3600*s)/60),r=e-3600*s-60*t,n=(s<10?"0":"")+s+"时"+(t<10?"0":"")+t+"分"+(r<10?"0":"")+r+"秒";this.djs=n}},setremark:function(){this.$refs.dialogSetremark.open()},setremarkconfirm:function(e,s){this.$refs.dialogSetremark.close();var t=this;d.post("ApiAdminOrder/setremark",{type:"shop",orderid:t.detail.id,content:s},(function(e){d.success(e.msg),setTimeout((function(){t.getdata()}),1e3)}))},tuikuan:function(){var e=this;this.refund.money=parseFloat(this.detail.totalprice||0).toFixed(2),this.refund.refundNum.forEach((function(s,t){e.$set(e.refund.refundNum[t],"selected",!0)})),this.$refs.tuikuandis.open()},getRefundSubmit:function(){this.$refs.dialogSetremark.close();var e=this;d.post("ApiAdminOrder/setrefund",{type:"shop",orderid:e.detail.id,money:e.refund.money,reason:e.refund.reason,refundNum:e.refund.refundNum},(function(s){d.success(s.msg),e.$refs.tuikuandis.close(),setTimeout((function(){e.getdata()}),1e3)}))},setrefundreason:function(e){this.refund.reason=e.detail.value},setrefundmoney:function(e){this.refund.money=e.detail.value},setrefundNum:function(e,s){var t=this,r=e.detail.value;if(r>this.refund.refundNum[s].num)d.error("退款数量不能超过购买数量");else{this.$set(this.refund.refundNum[s],"num",r);var n=0;this.refund.refundNum.forEach((function(e,s){if(e.selected){var r=t.prolist[s],i=r.sell_price*e.num,o=r.frame_price?r.frame_price*e.num:0;n+=i+o}})),this.refund.money=parseFloat(n).toFixed(2),this.dddd=Math.random().toString(36).substring(7)}},fahuo:function(){10==this.detail.freight_type?this.$refs.dialogExpress10.open():(this.fahuodatas=[{express_index:this.express_index,express_no:this.express_no,express_ogids:[]}],this.$refs.dialogExpress.open())},fahuoedit:function(){var e=this;if(this.fahuodatas=[],this.detail.express_content){var s=JSON.parse(this.detail.express_content);s.forEach((function(s){e.fahuodatas.push({express_index:e.expressdata.indexOf(s.express_com),express_no:s.express_no,express_ogids:s.express_ogids||[]})}))}else this.fahuodatas=[{express_index:this.expressdata.indexOf(this.detail.express_com),express_no:this.detail.express_no,express_ogids:1==this.prolist.length?[this.prolist[0].id.toString()]:[]}];this.$refs.dialogExpress_edit.open()},dialogExpressClose:function(){this.$refs.dialogExpress.close(),this.$refs.dialogExpress_edit.close()},dialogExpress10Close:function(){this.$refs.dialogExpress10.close()},expresschange:function(e){this.express_index=e.detail.value},expresschange_fahuo:function(e,s){this.fahuodatas[s].express_index=e.detail.value},setexpressno:function(e){this.express_no=e.detail.value},setexpressno_fahuo:function(e,s){this.fahuodatas[s].express_no=e.detail.value},checkboxChange_fahuo:function(e,s){this.fahuodatas[s].express_ogids=e.detail.value},addfahuo:function(){var e,s=o(this.fahuodatas);try{for(s.s();!(e=s.n()).done;){var t=e.value;if(!t.express_no.trim())return void d.error("请先填写已有物流单号")}}catch(r){s.e(r)}finally{s.f()}this.fahuodatas.push({express_index:0,express_no:"",express_ogids:[]})},deletefahuo:function(e){this.fahuodatas.length<=1?d.error("至少需要保留一个物流信息"):0===e?(this.express_index=this.fahuodatas[1].express_index,this.express_no=this.fahuodatas[1].express_no,this.fahuodatas.splice(1,1)):this.fahuodatas.splice(e,1)},logistics:function(e){var s=e.express_com,t=e.express_no,r=e.express_content,n=e.express_type,i=this.prolist;if(r){for(var o in r=JSON.parse(r),r)if(r[o].express_ogids){var a=r[o].express_ogids;console.log(a);var u=[];for(var c in i)d.inArray(i[c].id+"",a)&&u.push(i[c]);r[o].express_oglist=u}this.express_content=r,console.log(r),this.$refs.dialogSelectExpress.open()}else d.goto("/pagesExt/order/logistics?express_com="+s+"&express_no="+t+"&type="+n)},confirmfahuo:function(){this.$refs.dialogExpress.close();var e=this,s=this.expressdata[this.express_index];d.post("ApiAdminOrder/sendExpress",{type:"shop",orderid:e.detail.id,express_no:e.express_no,express_com:s},(function(s){0!=s.status?(d.success(s.msg),setTimeout((function(){e.getdata()}),1e3)):d.error(s.msg)}))},confirmfahuo_news:function(){var e=this;if(1!=this.prolist.length){var s,t=o(this.fahuodatas);try{for(t.s();!(s=t.n()).done;){var r=s.value;if(!r.express_no.trim())return void d.error("请填写所有物流单号")}}catch(c){t.e(c)}finally{t.f()}this.$refs.dialogExpress.close();u=this;var n=[],i=[],a=[];this.fahuodatas.forEach((function(s){n.push(s.express_no.trim()),i.push(e.expressdata[s.express_index]),a.push(s.express_ogids)})),d.post("ApiAdminOrder/sendExpress",{type:"shop",orderid:u.detail.id,express_no:n,express_com:i,express_ogids:a},(function(e){0!=e.status?(d.success(e.msg),setTimeout((function(){u.getdata()}),1e3)):d.error(e.msg)}))}else{if(!this.express_no.trim())return void d.error("请填写快递单号");this.$refs.dialogExpress.close();var u=this;d.post("ApiAdminOrder/sendExpress",{type:"shop",orderid:u.detail.id,express_no:u.express_no,express_com:u.expressdata[u.express_index]},(function(e){0!=e.status?(d.success(e.msg),setTimeout((function(){u.getdata()}),1e3)):d.error(e.msg)}))}},confirmfahuo_news_edit:function(){var e=this;this.$refs.dialogExpress_edit.close();var s=this;if(1!=this.prolist.length||this.detail.express_content){var t=[],r=[],n=[];this.fahuodatas.forEach((function(s){t.push(s.express_no),r.push(e.expressdata[s.express_index]),n.push(s.express_ogids)})),d.post("ApiAdminOrder/sendExpressEdit",{type:"shop",orderid:s.detail.id,express_no:t,express_com:r,express_ogids:n},(function(e){0!=e.status?(d.success(e.msg),setTimeout((function(){s.getdata()}),1e3)):d.error(e.msg)}))}else d.post("ApiAdminOrder/sendExpressEdit",{type:"shop",orderid:s.detail.id,express_no:s.fahuodatas[0].express_no,express_com:s.expressdata[s.fahuodatas[0].express_index]},(function(e){0!=e.status?(d.success(e.msg),setTimeout((function(){s.getdata()}),1e3)):d.error(e.msg)}))},setexpress_pic:function(e){this.express_pic=e.detail.value},setexpress_fhname:function(e){this.express_fhname=e.detail.value},setexpress_fhaddress:function(e){this.express_fhaddress=e.detail.value},setexpress_shname:function(e){this.express_shname=e.detail.value},setexpress_shaddress:function(e){this.express_shaddress=e.detail.value},setexpress_remark:function(e){this.express_remark=e.detail.value},confirmfahuo10:function(){this.$refs.dialogExpress10.close();var e=this;this.expressdata[this.express_index];d.post("ApiAdminOrder/sendExpress",{type:"shop",orderid:e.detail.id,pic:e.express_pic,fhname:e.express_fhname,fhaddress:e.express_fhaddress,shname:e.express_shname,shaddress:e.express_shaddress,remark:e.express_remark},(function(s){d.success(s.msg),setTimeout((function(){e.getdata()}),1e3)}))},ispay:function(e){var s=this,t=e.currentTarget.dataset.id;d.confirm("确定要改为已支付吗?",(function(){d.showLoading("提交中"),d.post("ApiAdminOrder/ispay",{type:"shop",orderid:t},(function(e){d.showLoading(!1),d.success(e.msg),setTimeout((function(){s.getdata()}),1e3)}))}))},hexiao:function(e){var s=this,t=e.currentTarget.dataset.id;d.confirm("确定要核销并改为已完成状态吗?",(function(){d.showLoading("提交中"),d.post("ApiAdminOrder/hexiao",{type:"shop",orderid:t},(function(e){d.showLoading(!1),d.success(e.msg),setTimeout((function(){s.getdata()}),1e3)}))}))},peisong:function(){var e=this;e.loading=!0,d.post("ApiAdminOrder/getpeisonguser",{type:"shop_order",orderid:e.detail.id},(function(s){e.loading=!1;var t=s.peisonguser,r=s.paidantype,n=s.psfee,i=(s.ticheng,[]);for(var o in t)i.push(t[o].title);if(e.peisonguser=s.peisonguser,e.peisonguser2=i,1==r)e.$refs.dialogPeisong.open();else{var a="选择配送员配送，订单将发布到抢单大厅由配送员抢单，需扣除配送费￥"+n+"，确定要配送员配送吗？";if(2==r)var u="-1";else u="0";d.confirm(a,(function(){d.post("ApiAdminOrder/peisong",{type:"shop_order",orderid:e.detail.id,psid:u},(function(s){d.success(s.msg),setTimeout((function(){e.getdata()}),1e3)}))}))}}))},dialogPeisongClose:function(){this.$refs.dialogPeisong.close()},peisongChange:function(e){this.index2=e.detail.value},confirmPeisong:function(){var e=this,s=this.peisonguser[this.index2].id;d.post("ApiAdminOrder/peisong",{type:"shop_order",orderid:e.detail.id,psid:s},(function(s){d.success(s.msg),e.$refs.dialogPeisong.close(),setTimeout((function(){e.getdata()}),1e3)}))},peisongWx:function(){var e=this,s=e.detail.freight_price;if(0==e.detail.bid)var t="选择即时配送，订单将派单到第三方配送平台，并扣除相应费用，确定要派单吗？";else t="选择即时配送，订单将派单到第三方配送平台，需扣除配送费￥"+s+"，确定要派单吗？";d.confirm(t,(function(){e.loading=!0,d.post("ApiAdminOrder/peisongWx",{type:"shop_order",orderid:e.detail.id},(function(s){e.loading=!1,d.success(s.msg),setTimeout((function(){e.getdata()}),1e3)}))}))},uploadimg:function(e){var s=this,t=parseInt(e.currentTarget.dataset.pernum);t||(t=1);var r=e.currentTarget.dataset.field,n=s[r];n||(n=[]),d.chooseImage((function(e){for(var t=0;t<e.length;t++)n.push(e[t]);"express_pic"==r&&(s.express_pic=n[0])}),t)},removeimg:function(e){e.currentTarget.dataset.index;var s=e.currentTarget.dataset.field;"express_pic"==s&&(this.express_pic="")},openCopyDialog:function(){this.$refs.copyDialog.open()},closeCopyDialog:function(){this.$refs.copyDialog.close()},onFieldChange:function(e){var s=e.detail.value;this.copyFields.forEach((function(e){e.checked=s.includes(e.value)}))},confirmCopy:function(){var s=this,t=this.copyFields.filter((function(e){return e.checked})).map((function(e){return e.value})),r="",n=this.detail,i=this.prolist;t.forEach((function(e){switch(e){case"address":r+="收货信息："+n.linkman+" "+n.tel+" "+n.area+n.address+"\n";break;case"product":r+="商品信息：\n",i.forEach((function(e){r+="名称："+e.name+"\n",r+="规格："+e.ggname+"\n",r+="数量："+e.num+"\n"}));break;case"shopordernum":r+="商城订单号："+n.ordernum+"\n";break;case"express":if(n.express_content){var s=JSON.parse(n.express_content);r+="快递单号：\n",s.forEach((function(e,s){if(e.express_ogids&&e.express_ogids.length>0){var t=i.filter((function(s){return e.express_ogids.includes(s.id.toString())})),n=t.map((function(e){return e.name})).join("、");r+="".concat(n,"：\n").concat(e.express_com," - ").concat(e.express_no,"\n")}else r+="".concat(e.express_com," - ").concat(e.express_no,"\n")}))}else n.express_no?r+="快递单号：".concat(n.express_com," - ").concat(n.express_no,"\n"):r+="快递单号：暂无\n";break;case"status":var t="";0==n.status?t="未付款":1==n.status?t="已付款":2==n.status?t="已发货":3==n.status?t="已收货":4==n.status&&(t="已关闭"),r+="订单状态："+t+"\n";break;case"price":r+="价格信息：\n",i.forEach((function(e){r+=e.name+"："+e.sell_price+"×"+e.num+"="+(e.sell_price*e.num).toFixed(2)+"\n"}));break;case"freight":r+="运费：¥"+n.freight_price+"\n";break;case"totalprice":r+="实付款：¥"+n.totalprice+" (="+n.product_price+"+"+n.freight_price+")\n";break;case"buyer":r+="下单人："+n.nickname+"\n";break;case"orderid":r+="订单ID："+n.id+"\n";break}})),e.setClipboardData({data:r,success:function(){s.closeCopyDialog(),d.success("复制成功")}})},closeTuikuan:function(){this.$refs.tuikuandis.close()}},(0,i.default)(n,"tuikuan",(function(){this.$refs.tuikuandis.open()})),(0,i.default)(n,"toggleRefundItem",(function(e){var s=this;this.$set(this.refund.refundNum[e],"selected",!this.refund.refundNum[e].selected),this.refund.refundNum[e].selected?this.$set(this.refund.refundNum[e],"num",this.prolist[e].num):this.$set(this.refund.refundNum[e],"num",0);var t=0;this.refund.refundNum.forEach((function(e,r){if(e.selected){var n=s.prolist[r],i=n.sell_price*e.num,o=n.frame_price?n.frame_price*e.num:0;t+=i+o}})),this.refund.money=parseFloat(t).toFixed(2)})),(0,i.default)(n,"closeOrder",(function(e){var s=this,t=e.currentTarget.dataset.id;d.confirm("确定要关闭订单吗?",(function(){d.showLoading("提交中"),d.post("ApiAdminOrder/closeOrder",{type:"shop",orderid:t},(function(e){d.showLoading(!1),d.success(e.msg),setTimeout((function(){s.getdata()}),1e3)}))}))})),(0,i.default)(n,"delOrder",(function(s){var t=s.currentTarget.dataset.id;d.confirm("确定要删除此订单吗?",(function(){d.showLoading("提交中"),d.post("ApiAdminOrder/delOrder",{type:"shop",orderid:t},(function(s){d.showLoading(!1),d.success(s.msg),setTimeout((function(){e.navigateBack()}),1e3)}))}))})),n)};s.default=c}).call(this,t("df3c")["default"])},a17f:function(e,s,t){"use strict";var r=t("ba88"),n=t.n(r);n.a},ba88:function(e,s,t){},ce32:function(e,s,t){"use strict";t.r(s);var r=t("8fc2"),n=t.n(r);for(var i in r)["default"].indexOf(i)<0&&function(e){t.d(s,e,(function(){return r[e]}))}(i);s["default"]=n.a}},[["4e2ff","common/runtime","common/vendor"]]]);