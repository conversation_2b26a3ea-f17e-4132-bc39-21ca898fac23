<view class="page flex-col"><view class="flex-col"><view class="text-wrapper_5 flex-row justify-between"><text data-event-opts="{{[['tap',[['switchTab',['notes']]]]]}}" class="{{['text_10',(activeTab==='notes')?'active':'']}}" style="{{('color: '+(activeTab==='notes'?$root.m0:'#999999'))}}" bindtap="__e">我的笔记</text><text data-event-opts="{{[['tap',[['switchTab',['favorites']]]]]}}" class="{{['text_11',(activeTab==='favorites')?'active':'']}}" style="{{('color: '+(activeTab==='favorites'?$root.m1:'#999999'))}}" bindtap="__e">我的评论</text></view><view style="padding:24rpx 24rpx 60rpx 24rpx;margin-top:15rpx;"><view class="list"><block wx:for="{{currentList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="pbl"><view data-event-opts="{{[['tap',[['goItem',['$0'],[[['currentList','',index,'id']]]]]]]}}" class="image" bindtap="__e"><image fade-show="{{true}}" lazy-load="{{true}}" lazy-load-margin="{{0}}" mode="widthFix" src="{{item.coverimg}}"></image></view><view data-event-opts="{{[['tap',[['goItem',['$0'],[[['currentList','',index,'id']]]]]]]}}" class="title" bindtap="__e"><rich-text nodes="{{item.title}}"></rich-text></view><view data-event-opts="{{[['tap',[['goItem',['$0'],[[['currentList','',index,'id']]]]]]]}}" style="display:flex;align-items:center;justify-content:space-between;padding:10px;color:#aaa;" bindtap="__e"><view style="display:flex;align-items:center;width:60%;"><image style="width:20px;height:20px;border-radius:50px;" src="{{item.headimg}}" class="_img"></image><view style="font-size:10px;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;margin-left:5px;">{{''+item.nickname+''}}</view></view><view style="display:flex;align-items:center;"><image style="width:12px;height:12px;" src="../../static/restaurant/like1.png"></image><view style="font-size:10px;margin-left:5px;">{{item.zan}}</view></view></view><block wx:if="{{activeTab==='notes'}}"><view class="action-buttons"><button data-event-opts="{{[['tap',[['editItem',['$0'],[[['currentList','',index,'id']]]]]]]}}" class="edit-button" catchtap="__e">编辑</button><button data-event-opts="{{[['tap',[['confirmDelete',['$0'],[[['currentList','',index,'id']]]]]]]}}" class="delete-button" catchtap="__e">删除</button></view></block></view></block></view><block wx:if="{{!loading}}"><uni-load-more vue-id="77305b1e-1" status="{{loadStatus}}" bind:__l="__l"></uni-load-more></block></view></view><block wx:if="{{loading}}"><loading vue-id="77305b1e-2" bind:__l="__l"></loading></block><dp-tabbar vue-id="77305b1e-3" opt="{{opt}}" data-event-opts="{{[['^getmenuindex',[['getmenuindex']]]]}}" bind:getmenuindex="__e" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="77305b1e-4" data-ref="popmsg" bind:__l="__l"></popmsg></view>