<view class="container"><view class="header"><text class="title">区域管理</text><text class="subtitle">管理您的代理覆盖区域</text></view><view class="content"><view class="stats-container"><view class="stats-item"><text class="stats-number">{{coverage_stats.province_count}}</text><text class="stats-label">覆盖省份</text></view><view class="stats-item"><text class="stats-number">{{coverage_stats.city_count}}</text><text class="stats-label">覆盖城市</text></view><view class="stats-item"><text class="stats-number">{{coverage_stats.district_count}}</text><text class="stats-label">覆盖区县</text></view><view class="stats-item"><text class="stats-number">{{coverage_stats.orders_count}}</text><text class="stats-label">区域订单</text></view></view><block wx:if="{{$root.g0>0}}"><view class="region-container"><view class="region-header"><image class="region-icon" src="/static/img/icon-province.png"></image><text class="region-title">覆盖省份</text></view><view class="province-list"><block wx:for="{{provinces}}" wx:for-item="province" wx:for-index="index" wx:key="id"><view data-event-opts="{{[['tap',[['toggleProvince',[index]]]]]}}" class="province-item" bindtap="__e"><view class="province-info"><text class="province-name">{{province.name}}</text><text class="province-stats">{{province.city_count+"个城市 | "+province.orders+"笔订单"}}</text></view><image class="arrow-icon" src="{{province.expanded?'/static/img/arrow-up.png':'/static/img/arrow-down.png'}}"></image></view></block><block wx:if="{{provinces[expandedProvinceIndex]&&provinces[expandedProvinceIndex].expanded}}"><view class="city-container"><view class="city-list"><block wx:for="{{provinces[expandedProvinceIndex].cities}}" wx:for-item="city" wx:for-index="__i0__" wx:key="id"><view data-event-opts="{{[['tap',[['viewCityDetail',['$0'],[[['provinces.'+expandedProvinceIndex+'.cities','id',city.id]]]]]]]}}" class="city-item" bindtap="__e"><view class="city-info"><text class="city-name">{{city.name}}</text><text class="city-stats">{{city.district_count+"个区县 | "+city.orders+"笔订单"}}</text></view><view class="city-revenue"><text class="revenue-amount">{{"￥"+city.revenue}}</text><text class="revenue-label">收益</text></view></view></block></view></view></block></view></view></block><block wx:if="{{$root.g1>0}}"><view class="region-container"><view class="region-header"><image class="region-icon" src="/static/img/icon-district.png"></image><text class="region-title">直辖区县</text></view><view class="district-list"><block wx:for="{{districts}}" wx:for-item="district" wx:for-index="__i1__" wx:key="id"><view data-event-opts="{{[['tap',[['viewDistrictDetail',['$0'],[[['districts','id',district.id]]]]]]]}}" class="district-item" bindtap="__e"><view class="district-info"><text class="district-name">{{district.name}}</text><text class="district-stats">{{district.streets+"个街道 | "+district.orders+"笔订单"}}</text></view><view class="district-revenue"><text class="revenue-amount">{{"￥"+district.revenue}}</text><text class="revenue-label">收益</text></view></view></block></view></view></block><view class="analysis-container"><view class="analysis-header"><image class="analysis-icon" src="/static/img/icon-analysis.png"></image><text class="analysis-title">区域业绩分析</text></view><view class="analysis-chart"><block wx:for="{{performance_data}}" wx:for-item="item" wx:for-index="__i2__" wx:key="name"><view class="chart-item"><view class="chart-bar"><view class="bar-fill" style="{{'width:'+(item.percentage+'%')+';'+('background:'+($root.m0)+';')}}"></view></view><view class="chart-info"><text class="chart-name">{{item.name}}</text><text class="chart-value">{{"￥"+item.value}}</text></view></view></block></view></view><block wx:if="{{$root.g2}}"><view class="empty-state"><image class="empty-icon" src="/static/img/empty-coverage.png"></image><text class="empty-title">暂无覆盖区域</text><text class="empty-desc">您还没有分配到任何代理区域</text><button data-event-opts="{{[['tap',[['contactAdmin',['$event']]]]]}}" class="empty-btn" bindtap="__e">联系管理员</button></view></block></view><block wx:if="{{loading}}"><loading vue-id="61abbb55-1" bind:__l="__l"></loading></block><popmsg class="vue-ref" vue-id="61abbb55-2" data-ref="popmsg" bind:__l="__l"></popmsg></view>