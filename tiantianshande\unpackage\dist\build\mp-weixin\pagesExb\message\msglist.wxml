<view class="container"><view class="nav-header"><view class="nav-title">消息通知</view><view class="nav-actions"><block wx:if="{{unread_count>0}}"><view class="unread-count">{{unread_count}}</view></block><block wx:if="{{unread_count>0}}"><view data-event-opts="{{[['tap',[['markAllAsRead',['$event']]]]]}}" class="action-btn" bindtap="__e">全部已读</view></block></view></view><view class="search-container"><view class="search-box"><image class="search-icon" src="/static/icon/search.png"></image><input type="text" placeholder="搜索消息" data-event-opts="{{[['confirm',[['searchMessages',['$event']]]],['input',[['__set_model',['','keyword','$event',[]]]]]]}}" value="{{keyword}}" bindconfirm="__e" bindinput="__e"/></view></view><view class="filter-tabs"><view data-event-opts="{{[['tap',[['filterByReadStatus',['all']]]]]}}" class="{{['tab-item',(read_status==='all')?'active':'']}}" bindtap="__e">全部</view><view data-event-opts="{{[['tap',[['filterByReadStatus',['unread']]]]]}}" class="{{['tab-item',(read_status==='unread')?'active':'']}}" bindtap="__e">未读<block wx:if="{{unread_count>0}}"><text class="tab-count">{{"("+unread_count+")"}}</text></block></view><view data-event-opts="{{[['tap',[['filterByReadStatus',['read']]]]]}}" class="{{['tab-item',(read_status==='read')?'active':'']}}" bindtap="__e">已读<block wx:if="{{read_count>0}}"><text class="tab-count">{{"("+read_count+")"}}</text></block></view></view><view class="message-list"><block wx:for="{{list}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['goDetail',['$0'],[[['list','',index]]]]]]]}}" class="chat-item" bindtap="__e"><view class="avatar-container"><image class="message-avatar" src="{{item.pic||'/static/img/default-message.png'}}" mode="aspectFill"></image><block wx:if="{{item.is_read==0}}"><view class="red-dot"></view></block></view><view class="content-container"><view class="content-header"><view class="message-title">{{item.title}}</view><view class="message-time">{{item.createtime_text}}</view></view><view class="content-body"><view class="message-preview">{{item.subtitle||item.content}}</view><view class="message-status"><block wx:if="{{item.is_read==0}}"><text class="unread-indicator">●</text></block><text class="read-count">{{item.readcount+"人已读"}}</text></view></view></view></view></block></view><block wx:if="{{$root.g0}}"><view class="empty-state"><image class="empty-icon" src="/static/img/empty-message.png"></image><text class="empty-text">暂无消息通知</text></view></block><block wx:if="{{$root.g1}}"><view class="loading-state"><text>加载中...</text></view></block><block wx:if="{{$root.g2}}"><view class="load-more"><text>上拉加载更多</text></view></block><block wx:if="{{$root.g3}}"><view class="no-more"><text>没有更多消息了</text></view></block><dp-tabbar vue-id="5c91dc36-1" current="{{0}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="5c91dc36-2" data-ref="popmsg" bind:__l="__l"></popmsg></view>