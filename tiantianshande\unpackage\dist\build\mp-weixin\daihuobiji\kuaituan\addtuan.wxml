<view class="container"><view style="background:linear-gradient(45deg, rgb(253, 74, 70) 0%, rgba(253, 74, 70, 0.8) 100%);"><view class="dp-userinfo2"><view class="flex" style="margin-top:80px;"><view class="flex"><view style="width:60px;height:60px;background:#FFF8AF;border-radius:5px;position:relative;"><image style="width:30px;height:30px;position:absolute;left:0;right:0;top:0;bottom:0;margin:auto;" src="{{pre_url+'/static/icon/photo.png'}}"></image></view><view style="font-size:14px;margin-left:10px;color:#fff;">快团用户</view></view><view class="btn-b">已开启智能背景</view></view></view></view><view style="position:fixed;z-index:1000;top:10px;left:10px;"><image style="width:30px;height:30px;" src="{{pre_url+'/static/img/goback.png'}}" data-event-opts="{{[['tap',[['goBack',['$event']]]]]}}" bindtap="__e"></image></view><block wx:if="{{showTab}}"><view class="load-list" style="height:100px;position:fixed;top:0;width:100%;background:#fff;z-index:100;"><view style="display:flex;justify-content:space-between;padding:70px 50px 0 50px;"><view data-event-opts="{{[['tap',[['scrollToElement',['tgjs']]]]]}}" class="{{[toView==='tgjs'?'on_tab':'']}}" bindtap="__e">介绍</view><view data-event-opts="{{[['tap',[['scrollToElement',['tgsp']]]]]}}" class="{{[toView==='tgsp'?'on_tab':'']}}" bindtap="__e">商品</view><view data-event-opts="{{[['tap',[['scrollToElement',['tgsz']]]]]}}" class="{{[toView==='tgsz'?'on_tab':'']}}" bindtap="__e">设置</view></view></view></block><view class="flex2" style="background:#FFF9E3;border-radius:8px;margin:-15px 11px 11px;padding:8px;overflow:hidden;"><marquee-component vue-id="5bf62a51-1" duration="{{10}}" bind:__l="__l" vue-slots="{{['default']}}"><view style="width:300px;" class="_p">{{''+notice+''}}</view></marquee-component><image style="width:15px;height:15px;" src="{{pre_url+'/static/icon/close.png'}}" class="_img"></image></view><view class="dp-userinfo-order" style="margin:0px 11px 11px;padding:10px 0;" id="tgjs"><view style="display:flex;justify-content:space-between;padding-bottom:15px;align-items:center;border-bottom:1px solid #eee;padding:10px;"><view class="tit-m">团购介绍</view><view class="btn-m">复制已有团购</view></view><view style="padding:0 10px;"><view style="padding:12px 0;border-bottom:1px solid #eee;"><input style="font-size:14px;" placeholder="请输入团购活动标题" data-event-opts="{{[['input',[['__set_model',['','title','$event',[]]]]]]}}" value="{{title}}" bindinput="__e"/></view><block wx:if="{{showTxt}}"><view style="padding:12px 0;"><input style="font-size:14px;" placeholder="请输入团购活动内容" data-event-opts="{{[['blur',[['editTxt',['$event']]]],['input',[['__set_model',['','content','$event',[]]]]]]}}" value="{{content}}" bindblur="__e" bindinput="__e"/></view></block></view><block wx:if="{{!showTxt}}"><view style="padding:0 10px;"><block wx:for="{{$root.l0}}" wx:for-item="setData" wx:for-index="index" wx:key="index"><view style="padding-top:10px;padding-bottom:40px;"><view class="flex"><view><block wx:if="{{setData.$orig.temp=='text'}}"><text>文字</text></block><block wx:if="{{setData.$orig.temp=='pictures'}}"><text>小图</text></block><block wx:if="{{setData.$orig.temp=='picture'}}"><text>大图</text></block><block wx:if="{{setData.$orig.temp=='video'}}"><text>视频</text></block></view><view class="flex"><view data-event-opts="{{[['tap',[['toTop',[index]]]]]}}" class="mz" style="{{(index==0?'color:#ccc;':'')}}" bindtap="__e">上移</view><view data-event-opts="{{[['tap',[['toBottom',[index]]]]]}}" class="mz" style="{{(setData.g0==index+1?'color:#ccc;':'')}}" bindtap="__e">下移</view><view data-event-opts="{{[['tap',[['toOne',[index]]]]]}}" class="mz" style="{{(index==0?'color:#ccc;':'')}}" bindtap="__e">置顶</view><view data-event-opts="{{[['tap',[['toAdd',[index]]]]]}}" class="mz" bindtap="__e">添加</view><view data-event-opts="{{[['tap',[['delItem',[index]]]]]}}" class="mz" bindtap="__e">删除</view></view></view><view style="padding-top:10px;"><block wx:if="{{setData.$orig.temp=='notice'}}"><block><dp-notice vue-id="{{'5bf62a51-2-'+index}}" params="{{setData.$orig.params}}" data="{{setData.$orig.data}}" bind:__l="__l"></dp-notice></block></block><block wx:if="{{setData.$orig.temp=='banner'}}"><block><dp-banner vue-id="{{'5bf62a51-3-'+index}}" params="{{setData.$orig.params}}" data="{{setData.$orig.data}}" bind:__l="__l"></dp-banner></block></block><block wx:if="{{setData.$orig.temp=='search'}}"><block><dp-search vue-id="{{'5bf62a51-4-'+index}}" params="{{setData.$orig.params}}" data="{{setData.$orig.data}}" bind:__l="__l"></dp-search></block></block><block wx:if="{{setData.$orig.temp=='text'}}"><block><input style="font-size:14px;" placeholder="请输入内容" data-event-opts="{{[['input',[['iptItem',['$event',index]]]]]}}" value="{{setData.$orig.params.content}}" bindinput="__e"/></block></block><block wx:if="{{setData.$orig.temp=='title'}}"><block><dp-title vue-id="{{'5bf62a51-5-'+index}}" params="{{setData.$orig.params}}" data="{{setData.$orig.data}}" bind:__l="__l"></dp-title></block></block><block wx:if="{{setData.$orig.temp=='dhlist'}}"><block><dp-dhlist vue-id="{{'5bf62a51-6-'+index}}" params="{{setData.$orig.params}}" data="{{setData.$orig.data}}" bind:__l="__l"></dp-dhlist></block></block><block wx:if="{{setData.$orig.temp=='line'}}"><block><dp-line vue-id="{{'5bf62a51-7-'+index}}" params="{{setData.$orig.params}}" data="{{setData.$orig.data}}" bind:__l="__l"></dp-line></block></block><block wx:if="{{setData.$orig.temp=='blank'}}"><block><dp-blank vue-id="{{'5bf62a51-8-'+index}}" params="{{setData.$orig.params}}" data="{{setData.$orig.data}}" bind:__l="__l"></dp-blank></block></block><block wx:if="{{setData.$orig.temp=='menu'}}"><block><dp-menu vue-id="{{'5bf62a51-9-'+index}}" params="{{setData.$orig.params}}" data="{{setData.$orig.data}}" bind:__l="__l"></dp-menu></block></block><block wx:if="{{setData.$orig.temp=='map'}}"><block><dp-map vue-id="{{'5bf62a51-10-'+index}}" params="{{setData.$orig.params}}" data="{{setData.$orig.data}}" bind:__l="__l"></dp-map></block></block><block wx:if="{{setData.$orig.temp=='cube'}}"><block><dp-cube vue-id="{{'5bf62a51-11-'+index}}" params="{{setData.$orig.params}}" data="{{setData.$orig.data}}" bind:__l="__l"></dp-cube></block></block><block wx:if="{{setData.$orig.temp=='picture'}}"><block><dp-picture vue-id="{{'5bf62a51-12-'+index}}" params="{{setData.$orig.params}}" data="{{setData.$orig.data}}" bind:__l="__l"></dp-picture></block></block><block wx:if="{{setData.$orig.temp=='pictures'}}"><block><view style="display:flex;flex-wrap:wrap;padding-top:20rpx;"><block wx:for="{{setData.$orig.data}}" wx:for-item="it" wx:for-index="idx" wx:key="idx"><view class="layui-imgbox"><view data-event-opts="{{[['tap',[['removeimg',[idx,index]]]]]}}" class="layui-imgbox-close" bindtap="__e"><image src="{{pre_url+'/static/img/ico-del.png'}}"></image></view><view class="layui-imgbox-img"><image src="{{it.imgurl}}" data-url="{{it.imgurl}}" mode="widthFix" data-event-opts="{{[['tap',[['previewImage',['$0'],[[['items','',index],['data','',idx,'imgurl']]]]]]]}}" bindtap="__e"></image></view></view></block><block wx:if="{{setData.g1<9}}"><view data-event-opts="{{[['tap',[['uploadSmallimg',[index]]]]]}}" class="uploadbtn" style="{{('background:url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 60rpx;background-size:80rpx 80rpx;background-color:#F3F3F3;')}}" bindtap="__e"></view></block></view></block></block><block wx:if="{{setData.$orig.temp=='video'}}"><block><dp-video vue-id="{{'5bf62a51-13-'+index}}" params="{{setData.$orig.params}}" data="{{setData.$orig.data}}" bind:__l="__l"></dp-video></block></block><block wx:if="{{setData.$orig.temp=='shop'}}"><block><dp-shop vue-id="{{'5bf62a51-14-'+index}}" params="{{setData.$orig.params}}" data="{{setData.$orig.data}}" shopinfo="{{setData.$orig.shopinfo}}" bind:__l="__l"></dp-shop></block></block><block wx:if="{{setData.$orig.temp=='product'}}"><block><dp-product vue-id="{{'5bf62a51-15-'+index}}" params="{{setData.$orig.params}}" data="{{setData.$orig.data}}" menuindex="{{menuindex}}" bind:__l="__l"></dp-product></block></block><block wx:if="{{setData.$orig.temp=='collage'}}"><block><dp-collage vue-id="{{'5bf62a51-16-'+index}}" params="{{setData.$orig.params}}" data="{{setData.$orig.data}}" bind:__l="__l"></dp-collage></block></block><block wx:if="{{setData.$orig.temp=='kanjia'}}"><block><dp-kanjia vue-id="{{'5bf62a51-17-'+index}}" params="{{setData.$orig.params}}" data="{{setData.$orig.data}}" bind:__l="__l"></dp-kanjia></block></block><block wx:if="{{setData.$orig.temp=='seckill'}}"><block><dp-seckill vue-id="{{'5bf62a51-18-'+index}}" params="{{setData.$orig.params}}" data="{{setData.$orig.data}}" bind:__l="__l"></dp-seckill></block></block><block wx:if="{{setData.$orig.temp=='scoreshop'}}"><block><dp-scoreshop vue-id="{{'5bf62a51-19-'+index}}" params="{{setData.$orig.params}}" data="{{setData.$orig.data}}" bind:__l="__l"></dp-scoreshop></block></block><block wx:if="{{setData.$orig.temp=='coupon'}}"><block><dp-coupon vue-id="{{'5bf62a51-20-'+index}}" params="{{setData.$orig.params}}" data="{{setData.$orig.data}}" bind:__l="__l"></dp-coupon></block></block><block wx:if="{{setData.$orig.temp=='article'}}"><block><dp-article vue-id="{{'5bf62a51-21-'+index}}" params="{{setData.$orig.params}}" data="{{setData.$orig.data}}" bind:__l="__l"></dp-article></block></block><block wx:if="{{setData.$orig.temp=='business'}}"><block><dp-business vue-id="{{'5bf62a51-22-'+index}}" params="{{setData.$orig.params}}" data="{{setData.$orig.data}}" bind:__l="__l"></dp-business></block></block><block wx:if="{{setData.$orig.temp=='liveroom'}}"><block><dp-liveroom vue-id="{{'5bf62a51-23-'+index}}" params="{{setData.$orig.params}}" data="{{setData.$orig.data}}" bind:__l="__l"></dp-liveroom></block></block><block wx:if="{{setData.$orig.temp=='button'}}"><block><dp-button vue-id="{{'5bf62a51-24-'+index}}" params="{{setData.$orig.params}}" data="{{setData.$orig.data}}" bind:__l="__l"></dp-button></block></block><block wx:if="{{setData.$orig.temp=='hotspot'}}"><block><dp-hotspot vue-id="{{'5bf62a51-25-'+index}}" params="{{setData.$orig.params}}" data="{{setData.$orig.data}}" bind:__l="__l"></dp-hotspot></block></block><block wx:if="{{setData.$orig.temp=='cover'}}"><block><dp-cover vue-id="{{'5bf62a51-26-'+index}}" params="{{setData.$orig.params}}" data="{{setData.$orig.data}}" bind:__l="__l"></dp-cover></block></block><block wx:if="{{setData.$orig.temp=='richtext'}}"><block><dp-richtext vue-id="{{'5bf62a51-27-'+index}}" params="{{setData.$orig.params}}" data="{{setData.$orig.data}}" content="{{setData.$orig.content}}" bind:__l="__l"></dp-richtext></block></block><block wx:if="{{setData.$orig.temp=='form'}}"><block><dp-form vue-id="{{'5bf62a51-28-'+index}}" params="{{setData.$orig.params}}" data="{{setData.$orig.data}}" content="{{setData.$orig.content}}" bind:__l="__l"></dp-form></block></block><block wx:if="{{setData.$orig.temp=='userinfo'}}"><block><dp-userinfo vue-id="{{'5bf62a51-29-'+index}}" params="{{setData.$orig.params}}" data="{{setData.$orig.data}}" content="{{setData.$orig.content}}" bind:__l="__l"></dp-userinfo></block></block></view></view></block></view></block><view style="display:flex;margin-top:40px;flex-wrap:wrap;"><block wx:for="{{tabs}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['goCom',['$0'],[[['tabs','',index,'temp']]]]]]]}}" style="text-align:center;width:20%;margin-top:10px;" bindtap="__e"><image style="width:20px;" src="{{pre_url+item.img}}" mode="widthFix"></image><view>{{item.txt}}</view></view></block></view></view><view class="dp-userinfo-order" style="margin:0px 11px 11px;" id="tgsp"><view style="display:flex;justify-content:space-between;margin-bottom:15px;align-items:center;"><view class="tit-m">团购商品</view><view data-event-opts="{{[['tap',[['navigateToSelectGoods',['$event']]]]]}}" class="btn-m" bindtap="__e">从商品库导入</view></view><block wx:if="{{$root.g2===0}}"><view><view style="text-align:center;padding:20px;">尚未选择商品</view></view></block><block wx:else><view><block wx:for="{{$root.l1}}" wx:for-item="item" wx:for-index="index" wx:key="id"><view style="padding:10px;background:#F6F6F6;margin-top:10px;border-radius:10rpx;position:relative;"><view class="ltn">{{index+1}}</view><view class="flex"><view></view><view class="flex"><view data-event-opts="{{[['tap',[['goodTop',[index]]]]]}}" class="mz" style="{{(index==0?'color:#ccc;':'')}}" bindtap="__e">上移</view><view data-event-opts="{{[['tap',[['goodBottom',[index]]]]]}}" class="mz" style="{{(item.g3==index+1?'color:#ccc;':'')}}" bindtap="__e">下移</view><view data-event-opts="{{[['tap',[['goodOne',[index]]]]]}}" class="mz" style="{{(index==0?'color:#ccc;':'')}}" bindtap="__e">置顶</view><view data-event-opts="{{[['tap',[['removeSelectedGood',[index]]]]]}}" class="mz" bindtap="__e">删除</view></view></view><view class="ml"><view style="font-size:12px;width:60px;">名称</view><input style="font-size:12px;" placeholder="商品名称" data-event-opts="{{[['input',[['__set_model',['$0','name','$event',[]],[[['selectedGoods','id',item.$orig.id]]]]]]]}}" value="{{item.$orig.name}}" bindinput="__e"/></view><view class="ml"><view style="font-size:12px;width:60px;">描述</view><input style="font-size:12px;" placeholder="商品描述" data-event-opts="{{[['input',[['__set_model',['$0','sellpoint','$event',[]],[[['selectedGoods','id',item.$orig.id]]]]]]]}}" value="{{item.$orig.sellpoint}}" bindinput="__e"/></view><view class="ml"><view style="font-size:12px;width:60px;">价格(￥)</view><input style="font-size:12px;" type="number" placeholder="价格" data-event-opts="{{[['input',[['__set_model',['$0','sell_price','$event',[]],[[['selectedGoods','id',item.$orig.id]]]]]]]}}" value="{{item.$orig.sell_price}}" bindinput="__e"/></view><view class="ml"><view style="font-size:12px;width:60px;">库存</view><input style="font-size:12px;" type="number" placeholder="库存" data-event-opts="{{[['input',[['__set_model',['$0','stock','$event',[]],[[['selectedGoods','id',item.$orig.id]]]]]]]}}" value="{{item.$orig.stock}}" bindinput="__e"/></view></view></block></view></block><view data-event-opts="{{[['tap',[['navigateToSelectGoods',['$event']]]]]}}" class="abtn" bindtap="__e">+ 添加商品</view></view><view class="dp-userinfo-order" style="margin:0px 11px 11px;padding:10px 0;" id="tgsz"><view style="display:flex;justify-content:space-between;padding-bottom:15px;align-items:center;border-bottom:1px solid #eee;padding:10px;"><view class="tit-m">团购设置</view></view><view class="flex" style="padding:10px 0;border-bottom:1px solid #eee;margin:0 10px;"><view style="font-size:13px;">物流方式</view><view class="flex"><view>未设置</view><image style="width:12px;" src="{{pre_url+'/static/icon/right.png'}}" mode="widthFix"></image></view></view><view class="flex" style="padding:10px 0;border-bottom:1px solid #eee;margin:0 10px;"><view style="font-size:13px;">团购时间</view><view class="flex"><view>发团即开始，7天后结束</view><image style="width:12px;" src="{{pre_url+'/static/icon/right.png'}}" mode="widthFix"></image></view></view><view class="flex" style="padding:10px 0;border-bottom:1px solid #eee;margin:0 10px;"><view style="font-size:13px;">开团通知推送</view><view class="flex"><view>全部订阅成员</view><image style="width:12px;" src="{{pre_url+'/static/icon/right.png'}}" mode="widthFix"></image></view></view><view class="flex" style="padding:10px 0;margin:0 10px;"><view><view style="font-size:13px;">更多团购设置</view><view style="color:#aaa;font-size:10px;">优惠设置、帮卖设置、隐私设置</view></view><image style="width:12px;" src="{{pre_url+'/static/img/arrowdown.png'}}" mode="widthFix"></image></view></view><view class="bll"><button class="btn1" type="button" data-event-opts="{{[['tap',[['formsubmit',['$event']]]]]}}" bindtap="__e">保存并预览</button><button class="btn2" type="button" data-event-opts="{{[['tap',[['formsubmit',['$event']]]]]}}" bindtap="__e">发布团购</button></view></view>