<view class="container"><view class="search-box"><view class="search-form"><view class="search-item"><text class="icon-search">🔍</text><input class="input" type="text" placeholder="搜索订单号/买家信息" confirm-type="search" data-event-opts="{{[['confirm',[['doSearch',['$event']]]],['input',[['__set_model',['','keyword','$event',[]]]]]]}}" value="{{keyword}}" bindconfirm="__e" bindinput="__e"/></view><view class="date-range"><picker mode="date" value="{{start_time}}" data-event-opts="{{[['change',[['bindStartChange',['$event']]]]]}}" bindchange="__e"><view class="picker-item"><text class="icon-calendar">📅</text><text class="picker-text">{{start_time||'开始时间'}}</text></view></picker><text class="split-line">-</text><picker mode="date" value="{{end_time}}" data-event-opts="{{[['change',[['bindEndChange',['$event']]]]]}}" bindchange="__e"><view class="picker-item"><text class="icon-calendar">📅</text><text class="picker-text">{{end_time||'结束时间'}}</text></view></picker></view><view data-event-opts="{{[['tap',[['doSearch',['$event']]]]]}}" class="search-btn" bindtap="__e">搜索</view></view></view><view class="total-card"><view class="total-item"><text class="label">订单总数</text><text class="num">{{count}}</text><text class="unit">笔</text></view><view class="divider"></view><view class="total-item"><text class="label">待收总额</text><text class="num highlight">{{"￥"+total_money}}</text></view></view><view class="list-content"><block wx:for="{{list}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="order-card"><view class="order-header"><view class="left"><text class="order-label">订单号</text><text class="order-num">{{item.ordernum}}</text></view><text class="order-status">{{item.status_text}}</text></view><view class="buyer-info"><image class="avatar" src="{{item.headimg}}" mode="aspectFill"></image><view class="buyer-detail"><view class="nickname-box"><text class="nickname">{{item.nickname}}</text><text class="tel">{{item.member_tel}}</text></view></view></view><view class="goods-list"><block wx:for="{{item.goods}}" wx:for-item="goods" wx:for-index="idx" wx:key="idx"><view class="goods-item"><image class="goods-img" src="{{goods.pic}}" mode="aspectFill"></image><view class="goods-info"><text class="goods-name text-cut-2">{{goods.name}}</text><text class="goods-spec">{{goods.ggname}}</text><view class="goods-price"><text class="price">{{"￥"+goods.sell_price}}</text><text class="num">{{"x"+goods.num}}</text></view></view></view></block></view><view class="order-footer"><view class="pay-info"><view class="pay-item"><text class="label">支付方式：</text><text class="value">{{item.paytype}}</text></view><view class="pay-item"><text class="label">支付时间：</text><text class="value">{{item.paytime_text}}</text></view></view><view class="total-price"><text class="label">待收金额</text><text class="amount">{{"￥"+item.totalprice}}</text></view></view></view></block></view><block wx:if="{{$root.g0==0}}"><view class="nodata"><image class="nodata-img" src="/static/img/nodata.png" mode="aspectFit"></image><text class="nodata-text">暂无待收款订单</text></view></block><block wx:if="{{loadmore}}"><view class="loadmore"><view class="loading-icon"></view><text class="loading-text">加载中...</text></view></block></view>