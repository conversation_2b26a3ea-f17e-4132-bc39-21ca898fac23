<view class="container"><block wx:if="{{isload}}"><block><view class="orderinfo"><view class="item"><text class="t1">提交人</text><text class="flex1"></text><image style="width:80rpx;height:80rpx;margin-right:8rpx;" src="{{detail.headimg}}"></image><text style="height:80rpx;line-height:80rpx;">{{detail.nickname}}</text></view><view class="item"><text class="t1">{{$root.m0+"ID"}}</text><text class="t2">{{detail.mid}}</text></view><view class="item"><text class="t1">工单分类</text><view class="t2" data-url="{{'updatecate?id='+detail.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">{{detail.cname+''}}<text class="iconfont iconjiantou" style="color:#999;font-weight:normal;"></text></view></view><view class="item"><text class="t1">工单名称</text><text class="t2">{{detail.title}}</text></view><block wx:for="{{formcontent}}" wx:for-item="fitem" wx:for-index="findex"><block class="item"><view class="dp-form-separate" data-index="{{findex}}" data-event-opts="{{[['tap',[['formChange',['$event']]]]]}}" catchtap="__e">{{fitem.name}}<image src="{{pre_url+'/static/img/workorder/'+(currentindex==findex?'down':'up')+'.png'}}"></image></view><view class="parentitem" style="{{(currentindex!=findex?'display:none':'')}}"><block wx:for="{{fitem.list}}" wx:for-item="item" wx:for-index="idx"><view class="item"><text class="t1">{{item.val1}}</text><block wx:if="{{item.key!='upload'&&item.key!='upload_file'&&item.key!='upload_video'}}"><text class="t2">{{detail['form'+idx]}}</text></block><block wx:if="{{item.key=='upload'}}"><view class="t2" style="display:flex;justify-content:flex-end;"><block wx:for="{{detail['form'+idx]}}" wx:for-item="sub" wx:for-index="indx" wx:key="indx"><view><image style="width:50px;margin-left:10rpx;" src="{{sub}}" mode="widthFix" data-url="{{sub}}" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image></view></block></view></block><block wx:if="{{item.key=='upload_file'}}"><view class="t2" style="overflow:hidden;white-space:pre-wrap;word-wrap:break-word;color:#4786BC;" data-file="{{detail['form'+idx]}}" data-event-opts="{{[['tap',[['download',['$event']]]]]}}" bindtap="__e">点击下载查看</view></block><block wx:if="{{item.key=='upload_video'}}"><view class="t2"><video style="width:100%;" src="{{detail['form'+idx]}}"></video></view></block></view></block></view></block></block><view class="item"><text class="t1">提交时间</text><text class="t2">{{detail.createtime}}</text></view><view class="item"><text class="t1">审核状态</text><block wx:if="{{detail.status==0&&(!detail.payorderid||detail.paystatus==1)}}"><text class="t2" style="color:#88e;">待处理</text></block><block wx:if="{{detail.status==0&&detail.payorderid&&detail.paystatus==0}}"><text class="t2" style="color:red;">待支付</text></block><block wx:if="{{detail.status==1}}"><text class="t2" style="color:green;">处理中</text></block><block wx:if="{{detail.status==2}}"><text class="t2" style="color:green;">已完成</text></block><block wx:if="{{detail.status==-1}}"><text class="t2" style="color:red;">已驳回</text></block></view><block wx:if="{{detail.status==-1}}"><view class="item"><text class="t1">驳回原因</text><text class="t2" style="color:red;">{{detail.reason}}</text></view></block><block wx:if="{{form.payset==1}}"><block><view class="item"><text class="t1">付款金额</text><text class="t2" style="font-size:32rpx;color:#e94745;">{{"￥"+detail.money}}</text></view><view class="item"><text class="t1">付款方式</text><text class="t2">{{detail.paytype}}</text></view><view class="item"><text class="t1">付款状态</text><block wx:if="{{detail.paystatus==1&&detail.isrefund==0}}"><text class="t2" style="color:green;">已付款</text></block><block wx:if="{{detail.paystatus==1&&detail.isrefund==1}}"><text class="t2" style="color:red;">已退款</text></block><block wx:if="{{detail.paystatus==0}}"><text class="t2" style="color:red;">未付款</text></block></view><block wx:if="{{detail.paystatus>0&&detail.paytime}}"><view class="item"><text class="t1">付款时间</text><text class="t2">{{detail.paytime}}</text></view></block></block></block><block wx:if="{{detail.iscomment==1}}"><block><view class="item"><text class="t1">满意度</text><block wx:if="{{detail.comment_status==1}}"><text class="t2" style="color:red;">不满意</text></block><block wx:if="{{detail.comment_status==2}}"><text class="t2" style="color:#88e;">一般</text></block><block wx:if="{{detail.comment_status==3}}"><text class="t2" style="color:green;">满意</text></block></view></block></block></view><view style="width:100%;height:160rpx;"></view><view class="bottom notabbarbot"><block wx:if="{{detail.status>0}}"><view class="btn2" data-url="{{'jindu?id='+detail.id+'&cid='+detail.cid}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">查看进度</view></block><block wx:if="{{detail.status==0}}"><view class="btn2" data-st="{{-1}}" data-id="{{detail.id}}" data-event-opts="{{[['tap',[['setst2',['$event']]]]]}}" bindtap="__e">驳回</view></block><view class="btn2" data-id="{{detail.id}}" data-event-opts="{{[['tap',[['del',['$event']]]]]}}" bindtap="__e">删除</view></view><uni-popup class="vue-ref" vue-id="381d5f80-1" id="dialogSetst2" type="dialog" data-ref="dialogSetst2" bind:__l="__l" vue-slots="{{['default']}}"><uni-popup-dialog vue-id="{{('381d5f80-2')+','+('381d5f80-1')}}" mode="input" title="驳回原因" value="{{detail.reason}}" placeholder="请输入驳回原因" data-event-opts="{{[['^confirm',[['setst2confirm']]]]}}" bind:confirm="__e" bind:__l="__l"></uni-popup-dialog></uni-popup></block></block><block wx:if="{{ishowjindu}}"><view class="modaljd"><view class="modal_jindu2"><view data-event-opts="{{[['tap',[['closejd',['$event']]]]]}}" class="close" bindtap="__e"><image src="{{pre_url+'/static/img/close.png'}}"></image></view><block wx:if="{{$root.g0>0}}"><block><block wx:for="{{$root.l1}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="item" style="display:flex;"><view class="f1"><image src="{{'/static/img/jindu'+(index==0?'2':'1')+'.png'}}"></image></view><view class="f2"><text class="t2">{{'时间：'+item.$orig.time}}</text><text class="t1">{{item.$orig.desc+"("+item.$orig.remark+')'}}</text><block wx:for="{{item.$orig.content_pic}}" wx:for-item="pic" wx:for-index="ind"><block wx:if="{{item.g1>0}}"><view><view class="layui-imgbox-img"><image src="{{pic}}" data-url="{{pic}}" mode="widthFix" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image></view></view></block></block><block wx:for="{{item.l0}}" wx:for-item="hf" wx:for-index="hfindex" wx:key="hfindex"><view><block wx:if="{{hf.$orig.hfremark}}"><view class="t3">{{"用户回复："+hf.$orig.hfremark+''}}</view></block><block wx:if="{{hf.$orig.hftime}}"><view class="t4">{{"回复时间："+hf.$orig.hftime+''}}</view></block><block wx:for="{{hf.$orig.hfcontent_pic}}" wx:for-item="pic2" wx:for-index="ind2"><block wx:if="{{hf.g2>0}}"><view><view class="layui-imgbox-img"><image src="{{pic2}}" data-url="{{pic2}}" mode="widthFix" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image></view></view></block></block></view></block></view></view></block></block></block><block wx:else><block><view style="font-size:14px;color:#f05555;padding:10px;">等待处理</view></block></block></view></view></block><block wx:if="{{showstatus}}"><view class="modal"><view class="modal_jindu"><form data-event-opts="{{[['submit',[['formsubmit',['$event']]]]]}}" bindsubmit="__e"><view data-event-opts="{{[['tap',[['close',['$event']]]]]}}" class="close" bindtap="__e"><image src="{{pre_url+'/static/img/close.png'}}"></image></view><view class="title">选择处理流程</view><view class="uni-list"><radio-group name="liucheng"><block wx:for="{{lclist}}" wx:for-item="item" wx:for-index="index" wx:key="index"><label class="uni-list-cell uni-list-cell-pd"><view><radio style="transform:scale(0.7);" value="{{''+item.id}}"></radio></view><view>{{item.name}}</view></label></block></radio-group><view class="beizhu flex"><label>备注1:</label><textarea style="height:100rpx;" placeholder="输入内容" name="content" maxlength="-1"></textarea></view></view><button class="btn" form-type="submit">提交</button></form></view></view></block><block wx:if="{{loading}}"><loading vue-id="381d5f80-3" bind:__l="__l"></loading></block><dp-tabbar vue-id="381d5f80-4" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="381d5f80-5" data-ref="popmsg" bind:__l="__l"></popmsg></view>