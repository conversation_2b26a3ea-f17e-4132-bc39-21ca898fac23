require('../../../../../common/vendor.js');(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["daihuobiji/detail/components/jack-filepicker/components/jack-fileupload/jack-fileupload"],{2949:function(t,e,n){"use strict";n.r(e);var i=n("e72f"),r=n.n(i);for(var a in i)["default"].indexOf(a)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(a);e["default"]=r.a},"4e04":function(t,e,n){"use strict";n.r(e);var i=n("90cc"),r=n("2949");for(var a in r)["default"].indexOf(a)<0&&function(t){n.d(e,t,(function(){return r[t]}))}(a);n("8576"),n("56e8");var s=n("828b"),o=Object(s["a"])(r["default"],i["b"],i["c"],!1,null,null,null,!1,i["a"],void 0);e["default"]=o.exports},5354:function(t,e,n){},"56ba":function(t,e,n){},"56e8":function(t,e,n){"use strict";var i=n("56ba"),r=n.n(i);r.a},8576:function(t,e,n){"use strict";var i=n("5354"),r=n.n(i);r.a},"90cc":function(t,e,n){"use strict";n.d(e,"b",(function(){return r})),n.d(e,"c",(function(){return a})),n.d(e,"a",(function(){return i}));var i={uniPopup:function(){return Promise.all([n.e("common/vendor"),n.e("components/uni-popup/uni-popup")]).then(n.bind(null,"ca44a"))}},r=function(){var t=this,e=t.$createElement,n=(t._self._c,t.images.length);t._isMounted||(t.e0=function(e){t.btnCanotUse||t.addMark("edit")},t.e1=function(e){t.btnCanotUse||t.addMark("edit")},t.e2=function(e){t.btnCanotUse||t.addMark("time")},t.e3=function(e){t.btnCanotUse||t.addMark("text")},t.e4=function(e){t.btnCanotUse||t.addMark("rotate")},t.e5=function(e){t.canDraw||t.addMark("clip")},t.e6=function(e,n){var i=arguments[arguments.length-1].currentTarget.dataset,r=i.eventParams||i["event-params"];n=r.item;t.strokeInfo.color=n},t.e7=function(e){t.isClip||t.addMark("draw")},t.e8=function(e){t.isClip||t.addMark("draw")},t.e9=function(e){t.btnCanotUse||t.addMark("edit")},t.e10=function(e){t.btnCanotUse||t.exit()},t.e11=function(e,n){var i=arguments[arguments.length-1].currentTarget.dataset,r=i.eventParams||i["event-params"];n=r.item;t.textInfo.color=n}),t.$mp.data=Object.assign({},{$root:{g0:n}})},a=[]},e72f:function(t,e,n){"use strict";(function(t,i){var r=n("47a9");Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var a=r(n("7eb4")),s=r(n("7ca3")),o=r(n("ee10")),c=r(n("af34")),h=n("4d67");function u(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);e&&(i=i.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,i)}return n}function f(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?u(Object(n),!0).forEach((function(e){(0,s.default)(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):u(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}var d=null,x=[],l=[],I=[],g=[],m=[],v=[],w=[],p={name:"JackFileupload",components:{},props:{},data:function(){return{currentIndex:0,images:[],isClip:!1,canDraw:!1,dateTimeInfo:{left:0,top:0,hash:!1,nowTime:""},colorList:["red","orange","black","white","green","clear"],strokeInfo:{weight:2,color:"red"},textInfo:{left:0,top:0,text:"",color:"red"},currentImage:{},currentImgInfo:[],canvasHeight:[],screenInfo:{width:0,height:0},itemCanvasInfo:{width:0,height:0},textOffset:{left:0,top:0},timeOffset:{left:0,top:0},fourPointRange:[],rotateDeg:0,touches:[],currentTouchPoint:{},radius:10,isMovePoint:!1,startX:"",startY:"",draggingCorner:[],isEdit:!1,testImg:""}},computed:{notMoveCanvas:function(){return this.moveTime||this.canDraw||this.moveText},btnCanotUse:function(){return this.isClip||this.canDraw}},watch:{isClip:function(e){var n=this;e||(this.itemCanvasInfo.width=this.fourPointRange[1].x2-this.fourPointRange[0].x1,this.itemCanvasInfo.height=this.fourPointRange[2].y2-this.fourPointRange[0].y1,t.showModal({cancelText:"舍弃",confirmText:"保留",title:"是否保留裁剪区域",success:function(t){t.confirm&&n.saveClip(),t.cancel&&(v[n.currentIndex].clearRect(0,0,n.screenInfo.width,n.canvasHeight[n.currentIndex]),v[n.currentIndex].draw(),n.itemCanvasInfo.width=0,n.itemCanvasInfo.height=0)}}))}},methods:(0,s.default)({onChange:function(t){this.currentIndex=t.detail.current,this.currentImage=this.images[this.currentIndex]},switchImg:function(e){this.isEdit?t.showToast({title:"请先保存图片",duration:2e3,icon:"none"}):(this.currentIndex=e,this.currentImage=this.images[this.currentIndex])},sliderChange:function(t){this.strokeInfo.weight=t.detail.value},loadImgToCanvas:function(){var e=this;this.canvasHeight=[],this.currentImgInfo.forEach((function(t){var n=t.width,i=t.height,r=n/i,a=.73*e.screenInfo.height;e.screenInfo.width/r>a?e.canvasHeight.push(a):e.canvasHeight.push(e.screenInfo.width/r)})),l=[],I=[],g=[],m=[],v=[],w=[],this.images.forEach((function(n,i){x.push(t.createCanvasContext("newCanvas-"+i,e)),l.push(t.createCanvasContext("imgCanvas-"+i,e)),I.push(t.createCanvasContext("timeCanvas-"+i,e)),g.push(t.createCanvasContext("textCanvas-"+i,e)),m.push(t.createCanvasContext("drawCanvas-"+i,e)),v.push(t.createCanvasContext("clipCanvas-"+i,e)),w.push(t.createCanvasContext("itemCanvas-"+i,e))})),setTimeout((function(){var t=.73*e.screenInfo.height;l.forEach((function(n,i){var r=e.currentImgInfo[i].width,a=e.currentImgInfo[i].height,s=r/a,o=t*s;e.screenInfo.width/s>t?n.drawImage(e.images[i].url,(e.screenInfo.width-o)/2,0,o,t):n.drawImage(e.images[i].url,0,0,e.screenInfo.width,e.screenInfo.width/s),n.draw()}))}),100)},chooseFile:function(){var e=this;t.chooseImage({extension:[".png",".jpg",".jpeg"],sourceType:["album"],sizeType:["original"],complete:function(n){var i;(console.log(n),"chooseImage:fail cancel"==n.errMsg)||(e.currentImage={url:n.tempFilePaths[0]},(i=e.images).push.apply(i,(0,c.default)(n.tempFilePaths.map((function(t){return{url:t}})))),console.log("this.images.",e.images),e.currentImgInfo=[],e.images.forEach(function(){var n=(0,o.default)(a.default.mark((function n(i){var r;return a.default.wrap((function(n){while(1)switch(n.prev=n.next){case 0:return n.next=2,t.getImageInfo({src:i.url});case 2:r=n.sent,i.width=r.width,i.height=r.height,Array.isArray(r)?e.currentImgInfo.push(r[1]):e.currentImgInfo.push(r),e.$nextTick((function(){e.loadImgToCanvas()}));case 7:case"end":return n.stop()}}),n)})));return function(t){return n.apply(this,arguments)}}()),console.log("this.currentImgInfo",e.currentImgInfo))}})},addMark:function(e){var n=this;return(0,o.default)(a.default.mark((function r(){var s,c,u,f,d,l,g,m;return a.default.wrap((function(r){while(1)switch(r.prev=r.next){case 0:if("time"!=e&&"rotate"!=e&&"draw"!=e&&"clip"!=e&&"edit"!=e&&"text"!=e){r.next=4;break}if(0!=n.images.length){r.next=4;break}return t.showToast({title:"请先选择图片",duration:2e3,icon:"none"}),r.abrupt("return");case 4:if("time"!=e){r.next=9;break}if(n.isEdit){r.next=8;break}return t.showToast({title:"请进入编辑模式",duration:2e3,icon:"none"}),r.abrupt("return");case 8:n.dateTimeInfo.hash?(I[n.currentIndex].clearRect(0,0,n.screenInfo.width,n.canvasHeight[n.currentIndex]),I[n.currentIndex].draw(!0),n.dateTimeInfo.hash=!1):(I[n.currentIndex].setFontSize(14),I[n.currentIndex].setLineWidth(4),I[n.currentIndex].setFillStyle("rgb(83, 83, 83)"),I[n.currentIndex].setStrokeStyle("white"),s=(0,h.getNowDateTime)(),n.dateTimeInfo.nowTime=s,n.dateTimeInfo.left=5,n.dateTimeInfo.top=n.canvasHeight[n.currentIndex]-10,I[n.currentIndex].strokeText(s,n.dateTimeInfo.left,n.dateTimeInfo.top,120),I[n.currentIndex].fillText(s,n.dateTimeInfo.left,n.dateTimeInfo.top,120),n.dateTimeInfo.hash=!0,I[n.currentIndex].draw(!0));case 9:if("text"!=e){r.next=14;break}if(n.isEdit){r.next=13;break}return t.showToast({title:"请进入编辑模式",duration:2e3,icon:"none"}),r.abrupt("return");case 13:n.$refs.popup.open("top");case 14:if("rotate"!=e){r.next=31;break}if(n.isEdit){r.next=18;break}return t.showToast({title:"请进入编辑模式",duration:2e3,icon:"none"}),r.abrupt("return");case 18:return c={canvasId:"timeCanvas-"+n.currentIndex,x:0,y:0,width:n.screenInfo.width,height:n.canvasHeight[n.currentIndex]},u={canvasId:"textCanvas-"+n.currentIndex,x:0,y:0,width:n.screenInfo.width,height:n.canvasHeight[n.currentIndex]},f={canvasId:"drawCanvas-"+n.currentIndex,x:0,y:0,width:n.screenInfo.width,height:n.canvasHeight[n.currentIndex]},r.next=23,(0,h.canvasHasContent)(c,n);case 23:return d=r.sent,r.next=26,(0,h.canvasHasContent)(f,n);case 26:return l=r.sent,r.next=29,(0,h.canvasHasContent)(u,n);case 29:g=r.sent,d||l||g?t.showModal({cancelText:"旋转",confirmText:"取消旋转",title:"旋转将清图片上的编辑内容",content:"是否继续？",success:function(t){t.cancel&&n.rotateClearOther()}}):n.rotateClearOther();case 31:if("draw"!=e){r.next=37;break}if(n.isEdit){r.next=35;break}return t.showToast({title:"请进入编辑模式",duration:2e3,icon:"none"}),r.abrupt("return");case 35:n.canDraw=!n.canDraw,n.isClip=!1;case 37:if("clip"!=e){r.next=43;break}if(n.isEdit){r.next=41;break}return t.showToast({title:"请进入编辑模式",duration:2e3,icon:"none"}),r.abrupt("return");case 41:n.isClip=!n.isClip,n.isClip&&(n.fourPointRange=[],40,n.fourPointRange.push({x1:40,y1:40,x2:2*n.radius+40,y2:2*n.radius+40}),n.fourPointRange.push({x1:n.screenInfo.width-2*n.radius-40,y1:40,x2:n.screenInfo.width-40,y2:2*n.radius+40}),n.fourPointRange.push({x1:40,y1:n.canvasHeight[n.currentIndex]-2*n.radius-40,x2:2*n.radius+40,y2:n.canvasHeight[n.currentIndex]-40}),n.fourPointRange.push({x1:n.screenInfo.width-2*n.radius-40,y1:n.canvasHeight[n.currentIndex]-2*n.radius-40,x2:n.screenInfo.width-40,y2:n.canvasHeight[n.currentIndex]-40}),n.drawFourPoint());case 43:if("edit"!=e){r.next=52;break}if(n.isEdit=!n.isEdit,!n.isEdit){r.next=47;break}return r.abrupt("return");case 47:n.itemCanvasInfo.width=n.screenInfo.width,n.itemCanvasInfo.height=n.canvasHeight[n.currentIndex],t.showLoading({mask:!0}),m=["imgCanvas-"+n.currentIndex,"drawCanvas-"+n.currentIndex,"timeCanvas-"+n.currentIndex,"textCanvas-"+n.currentIndex],m.forEach(function(){var e=(0,o.default)(a.default.mark((function e(r,s){var o,c,h,u;return a.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,i.canvasToTempFilePath({canvasId:r},n);case 2:if(o=e.sent,c=o.tempFilePath,console.log("this.itemCanvasInfo",n.itemCanvasInfo),x[n.currentIndex].drawImage(c,0,0,n.itemCanvasInfo.width,n.itemCanvasInfo.height),x[n.currentIndex].draw(!0),console.log("newCanvas[this.currentIndex]",x[n.currentIndex]),s!=m.length-1){e.next=17;break}return e.next=11,i.canvasToTempFilePath({canvasId:"newCanvas-"+n.currentIndex},n);case 11:h=e.sent,u=h.tempFilePath,n.images[n.currentIndex].url=u,n.clearCanvasContent(!0),n.clearCanvas(),t.hideLoading();case 17:case"end":return e.stop()}}),e)})));return function(t,n){return e.apply(this,arguments)}}());case 52:case"end":return r.stop()}}),r)})))()},confirmText:function(){g[this.currentIndex].clearRect(0,0,this.screenInfo.width,this.canvasHeight[this.currentIndex]),g[this.currentIndex].draw(!0),g[this.currentIndex].setFontSize(20),g[this.currentIndex].setLineWidth(4),g[this.currentIndex].setFillStyle(this.textInfo.color),g[this.currentIndex].setStrokeStyle("white"),this.textInfo.left=30,this.textInfo.top=50,g[this.currentIndex].strokeText(this.textInfo.text,this.textInfo.left,this.textInfo.top,120),g[this.currentIndex].fillText(this.textInfo.text,this.textInfo.left,this.textInfo.top,120),g[this.currentIndex].draw(!0),this.$refs.popup.close()},drawFourPoint:function(){var t=this;v[this.currentIndex].setFillStyle("rgba(0,0,0,0.6)"),v[this.currentIndex].fillRect(0,0,this.screenInfo.width,this.canvasHeight[this.currentIndex]),v[this.currentIndex].clearRect(this.fourPointRange[0].x1,this.fourPointRange[0].y1,this.fourPointRange[1].x2-this.fourPointRange[0].x1,this.fourPointRange[2].y2-this.fourPointRange[0].y1),v[this.currentIndex].setStrokeStyle("gray"),v[this.currentIndex].setFillStyle("white"),this.fourPointRange.forEach((function(e,n){v[t.currentIndex].beginPath(),v[t.currentIndex].arc(e.x1+(e.x2-e.x1)/2,e.y1+(e.y2-e.y1)/2,t.radius,0,360),v[t.currentIndex].fill(),v[t.currentIndex].beginPath(),v[t.currentIndex].arc(e.x1+(e.x2-e.x1)/2,e.y1+(e.y2-e.y1)/2,t.radius,0,360),v[t.currentIndex].stroke()})),v[this.currentIndex].draw(),this.isClip=!0,this.canDraw=!1;this.fourPointRange[1].x2,this.fourPointRange[0].x1,this.fourPointRange[2].y2,this.fourPointRange[0].y1;this.itemCanvasInfo.width=200,this.itemCanvasInfo.height=200},rotateClearOther:function(){var e=this;return(0,o.default)(a.default.mark((function n(){return a.default.wrap((function(n){while(1)switch(n.prev=n.next){case 0:t.showLoading({mask:!0}),e.rotateDeg+=90,e.clearCanvasContent(),e.canvasHeight[e.currentIndex]=e.screenInfo.width/e.canvasHeight[e.currentIndex]*e.screenInfo.width,setTimeout((0,o.default)(a.default.mark((function n(){var i;return a.default.wrap((function(n){while(1)switch(n.prev=n.next){case 0:return n.next=2,(0,h.urlImgRotate)(e.currentImage.url,e.rotateDeg,e);case 2:i=n.sent,console.log("imgurl",i),t.getImageInfo({src:i}).then((function(t){console.log("===========返回图片宽高"),console.log(t)})),l[e.currentIndex].drawImage(i,0,0,e.screenInfo.width,e.canvasHeight[e.currentIndex]),l[e.currentIndex].draw(!1,(0,o.default)(a.default.mark((function n(){return a.default.wrap((function(n){while(1)switch(n.prev=n.next){case 0:e.dateTimeInfo.hash=!1,t.hideLoading();case 2:case"end":return n.stop()}}),n)}))));case 7:case"end":return n.stop()}}),n)}))));case 5:case"end":return n.stop()}}),n)})))()},clearCanvasContent:function(t){t||(x[this.currentIndex].clearRect(0,0,this.screenInfo.width,this.canvasHeight[this.currentIndex]),x[this.currentIndex].draw()),l[this.currentIndex].clearRect(0,0,this.screenInfo.width,this.canvasHeight[this.currentIndex]),I[this.currentIndex].clearRect(0,0,this.screenInfo.width,this.canvasHeight[this.currentIndex]),g[this.currentIndex].clearRect(0,0,this.screenInfo.width,this.canvasHeight[this.currentIndex]),m[this.currentIndex].clearRect(0,0,this.screenInfo.width,this.canvasHeight[this.currentIndex]),v[this.currentIndex].clearRect(0,0,this.screenInfo.width,this.canvasHeight[this.currentIndex]),w[this.currentIndex].clearRect(0,0,this.screenInfo.width,this.canvasHeight[this.currentIndex]),l[this.currentIndex].draw(),I[this.currentIndex].draw(),g[this.currentIndex].draw(),m[this.currentIndex].draw(),v[this.currentIndex].draw(),w[this.currentIndex].draw()},clearCanvas:function(){this.canDraw=!1,this.dateTimeInfo.hash=!1,this.textInfo.text="",this.rotateDeg=0},exit:function(){var e=this;t.showModal({content:"是否删除图片？",confirmText:"删除",title:"操作将删除此图片",cancelText:"关闭",success:function(t){t.confirm&&(e.clearCanvas(),e.clearCanvasContent(),e.images.splice(e.currentIndex,1),e.currentIndex=0)}})},touchstart:function(t){t.preventDefault&&t.preventDefault(),t.stopPropagation&&t.stopPropagation(),this.currentTouchPoint=t.touches[0],this.timeOffset.left=t.touches[0].x-this.dateTimeInfo.left,this.timeOffset.top=t.touches[0].y-this.dateTimeInfo.top,this.textOffset.left=t.touches[0].x-this.textInfo.left,this.textOffset.top=t.touches[0].y-this.textInfo.top,this.canDraw&&m[this.currentIndex].moveTo(t.touches[0].x,t.touches[0].y),this.draggingCorner=this.getCornerByTouch(this.currentTouchPoint,this.fourPointRange),this.draggingCorner&&(this.isMovePoint=!0,this.startX=this.currentTouchPoint.x,this.startY=this.currentTouchPoint.y)},touchend:function(t){this.textOffset.left=0,this.textOffset.top=0,this.timeOffset.left=0,this.timeOffset.top=0,this.oOffset=0,this.isMovePoint=!1,this.draggingCorner=null},isMoveIn:function(t,e){return t.x>e.x1&&t.x<e.x2&&t.y>e.y1&&t.y<e.y2},touchmove:function(t){var e=this;if(this.touches=t.touches,1==this.touches.length){if(!this.canDraw&&this.dateTimeInfo.hash&&this.isMoveIn({x:this.touches[0].x,y:this.touches[0].y},{x1:this.dateTimeInfo.left,y1:this.dateTimeInfo.top-15,x2:this.dateTimeInfo.left+120,y2:this.dateTimeInfo.top+5})){this.moveTime=!0,d&&clearTimeout(d),d=setTimeout((function(){e.moveTime=!1,clearTimeout(d),d=null}),200);var n=this.touches[0].x-this.timeOffset.left;n<0?this.dateTimeInfo.left=0:n+120>this.screenInfo.width?this.dateTimeInfo.left=this.screenInfo.width-120:this.dateTimeInfo.left=n;var i=this.touches[0].y-this.timeOffset.top;i<10?this.dateTimeInfo.top=10:i>this.canvasHeight[this.currentIndex]-10?this.dateTimeInfo.top=this.canvasHeight[this.currentIndex]-10:this.dateTimeInfo.top=i,this.reDrawTime()}if(!this.canDraw&&this.isMoveIn({x:this.touches[0].x,y:this.touches[0].y},{x1:this.textInfo.left,y1:this.textInfo.top-15,x2:this.textInfo.left+120,y2:this.textInfo.top+5})){this.moveText=!0,d&&clearTimeout(d),d=setTimeout((function(){e.moveText=!1,clearTimeout(d),d=null}),200);var r=this.touches[0].x-this.textOffset.left;r<0?this.textInfo.left=0:r+120>this.screenInfo.width?this.textInfo.left=this.screenInfo.width-120:this.textInfo.left=r;var a=this.touches[0].y-this.textOffset.top;a<10?this.textInfo.top=10:a>this.canvasHeight[this.currentIndex]-10?this.textInfo.top=this.canvasHeight[this.currentIndex]-10:this.textInfo.top=a,this.reDrawText()}if(this.canDraw&&("clear"!=this.strokeInfo.color?(m[this.currentIndex].setStrokeStyle(this.strokeInfo.color),m[this.currentIndex].setLineCap("round"),m[this.currentIndex].setLineWidth(this.strokeInfo.weight),m[this.currentIndex].lineTo(this.touches[0].x,this.touches[0].y),m[this.currentIndex].stroke(),m[this.currentIndex].draw(!0),m[this.currentIndex].moveTo(this.touches[0].x,this.touches[0].y)):(m[this.currentIndex].clearRect(this.touches[0].x-this.strokeInfo.weight/2,this.touches[0].y-this.strokeInfo.weight/2,this.strokeInfo.weight,this.strokeInfo.weight),m[this.currentIndex].draw(!0))),this.isClip&&(this.isMovePoint&&this.moveCorner(t.touches),!this.isMovePoint&&this.isMoveIn({x:this.touches[0].x,y:this.touches[0].y},{x1:this.fourPointRange[0].x1,y1:this.fourPointRange[0].y1,x2:this.fourPointRange[3].x2,y2:this.fourPointRange[3].y2}))){this.moveTime=!0,this.moveText=!0,d&&clearTimeout(d),d=setTimeout((function(){e.moveTime=!1,e.moveText=!1,clearTimeout(d),d=null}),200);var s=this.touches[0].x-this.currentTouchPoint.x,o=this.touches[0].y-this.currentTouchPoint.y;s<0&&this.currentTouchPoint.x-this.fourPointRange[0].x1>=this.currentTouchPoint.x&&(s=0),s>0&&this.fourPointRange[1].x2-this.currentTouchPoint.x>=this.screenInfo.width-this.currentTouchPoint.x&&(s=0),o<0&&this.currentTouchPoint.y-this.fourPointRange[0].y1>=this.currentTouchPoint.y&&(o=0),o>0&&this.fourPointRange[2].y2-this.currentTouchPoint.y>=this.canvasHeight[this.currentIndex]-this.currentTouchPoint.y&&(o=0),this.fourPointRange.forEach((function(t){t.x1+=s,t.x2+=s,t.y1+=o,t.y2+=o})),this.currentTouchPoint.x=this.touches[0].x,this.currentTouchPoint.y=this.touches[0].y,this.drawFourPoint()}}},getCornerByTouch:function(t,e){for(var n=t.x,i=t.y,r=0;r<e.length;r++){var a=e[r],s=a.x1+(a.x2-a.x1)/2,o=a.y1+(a.y2-a.y1)/2;if(Math.abs(n-s)<10&&Math.abs(i-o)<10)return f(f({},a),{},{index:r})}return null},moveCorner:function(t){if(this.isMovePoint){var e=t[0],n=e.x-this.startX,i=e.y-this.startY,r=this.draggingCorner.x1+n,a=this.draggingCorner.y1+i;r=Math.max(this.radius,Math.min(this.screenInfo.width-this.radius,r)),a=Math.max(this.radius,Math.min(this.canvasHeight[this.currentIndex]-this.radius,a)),this.reComPointPos(r,a,this.draggingCorner.index),this.drawFourPoint()}},reComPointPos:function(t,e,n){0==n&&(t>=this.fourPointRange[1].x1-2*this.radius&&(t=this.fourPointRange[1].x1-2*this.radius),e>=this.fourPointRange[2].y1-2*this.radius&&(e=this.fourPointRange[2].y1-2*this.radius)),1==n&&(t<=this.fourPointRange[0].x2+2*this.radius&&(t=this.fourPointRange[0].x2+2*this.radius),e>=this.fourPointRange[2].y1-2*this.radius&&(e=this.fourPointRange[2].y1-2*this.radius)),2==n&&(t>=this.fourPointRange[1].x1-2*this.radius&&(t=this.fourPointRange[1].x1-2*this.radius),e<=this.fourPointRange[0].y2+2*this.radius&&(e=this.fourPointRange[0].y2+2*this.radius)),3==n&&(t<=this.fourPointRange[0].x2+2*this.radius&&(t=this.fourPointRange[0].x2+2*this.radius),e<=this.fourPointRange[0].y2+2*this.radius&&(e=this.fourPointRange[0].y2+2*this.radius)),this.fourPointRange[n]={x1:t-this.radius,y1:e-this.radius,x2:t+this.radius,y2:e+this.radius},0==n&&(this.fourPointRange[1].y1=this.fourPointRange[n].y1,this.fourPointRange[1].y2=this.fourPointRange[n].y2,this.fourPointRange[2].x1=this.fourPointRange[n].x1,this.fourPointRange[2].x2=this.fourPointRange[n].x2),1==n&&(this.fourPointRange[0].y1=this.fourPointRange[n].y1,this.fourPointRange[0].y2=this.fourPointRange[n].y2,this.fourPointRange[3].x1=this.fourPointRange[n].x1,this.fourPointRange[3].x2=this.fourPointRange[n].x2),2==n&&(this.fourPointRange[3].y1=this.fourPointRange[n].y1,this.fourPointRange[3].y2=this.fourPointRange[n].y2,this.fourPointRange[0].x1=this.fourPointRange[n].x1,this.fourPointRange[0].x2=this.fourPointRange[n].x2),3==n&&(this.fourPointRange[2].y1=this.fourPointRange[n].y1,this.fourPointRange[2].y2=this.fourPointRange[n].y2,this.fourPointRange[1].x1=this.fourPointRange[n].x1,this.fourPointRange[1].x2=this.fourPointRange[n].x2)},reDrawTime:function(){I[this.currentIndex].clearRect(0,0,this.screenInfo.width,this.canvasHeight[this.currentIndex]),I[this.currentIndex].strokeText(this.dateTimeInfo.nowTime,this.dateTimeInfo.left,this.dateTimeInfo.top,120),I[this.currentIndex].fillText(this.dateTimeInfo.nowTime,this.dateTimeInfo.left,this.dateTimeInfo.top,120),I[this.currentIndex].draw(!0)},reDrawText:function(){g[this.currentIndex].clearRect(0,0,this.screenInfo.width,this.canvasHeight[this.currentIndex]),g[this.currentIndex].setFontSize(20),g[this.currentIndex].setLineWidth(4),g[this.currentIndex].setFillStyle(this.textInfo.color),g[this.currentIndex].setStrokeStyle("white"),g[this.currentIndex].strokeText(this.textInfo.text,this.textInfo.left,this.textInfo.top,120),g[this.currentIndex].fillText(this.textInfo.text,this.textInfo.left,this.textInfo.top,120),g[this.currentIndex].draw(!0)},afterClipDraw:function(e){var n=this;return(0,o.default)(a.default.mark((function i(){var r,s,o,c,h;return a.default.wrap((function(i){while(1)switch(i.prev=i.next){case 0:if(e){i.next=4;break}return console.error("Invalid image URL"),t.showToast({title:"图片处理失败",icon:"none"}),i.abrupt("return");case 4:return t.showLoading({title:"处理中...",mask:!0}),i.prev=5,i.next=8,new Promise((function(n,i){t.getImageInfo({src:e,success:function(t){return n(t)},fail:function(t){return i(t)}})}));case 8:return r=i.sent,s=r.width,o=r.height,c=o/s*n.screenInfo.width,n.canvasHeight[n.currentIndex]=c,n.currentImage.url=e,n.images[n.currentIndex].url=e,i.next=17,n.$nextTick();case 17:return h=t.createCanvasContext("imgCanvas-"+n.currentIndex,n),h.clearRect(0,0,n.screenInfo.width,c),h.fillStyle="#FFFFFF",h.fillRect(0,0,n.screenInfo.width,c),h.drawImage(e,0,0,n.screenInfo.width,c),i.next=24,new Promise((function(t){h.draw(!1,(function(){setTimeout(t,100)}))}));case 24:n.dateTimeInfo.hash=!1,n.itemCanvasInfo.width=0,n.itemCanvasInfo.height=0,i.next=33;break;case 29:i.prev=29,i.t0=i["catch"](5),console.error("图片处理失败：",i.t0),t.showToast({title:"图片处理失败",icon:"none"});case 33:return i.prev=33,t.hideLoading(),i.finish(33);case 36:case"end":return i.stop()}}),i,null,[[5,29,33,36]])})))()},itemsToNewH5:function(e,n,i){var r=this,a=this.$refs["itemCanvas-".concat(this.currentIndex)][0].$el,s=a.children[0];this.currentImage.url=s,t.showLoading({mask:!0}),setTimeout((function(){if(t.hideLoading(),e.drawImage(s,0,0,n.width,n.height),2==i){var a=n.toDataURL(),o=(0,h.blobToUrl)((0,h.base64toBlob)(a));r.images[r.currentIndex].url=o,r.afterClipDraw(o)}}),100)},itemsToNewWX:function(e){var n=this;t.canvasToTempFilePath({canvasId:"itemCanvas-"+this.currentIndex,success:function(i){x[n.currentIndex].drawImage(i.tempFilePath,0,0,n.itemCanvasInfo.width,n.itemCanvasInfo.height),x[n.currentIndex].draw(!0),2==e&&t.canvasToTempFilePath({canvasId:"newCanvas-"+n.currentIndex},n).then((function(t){t=Array.isArray(t)?t[1]:t,n.clearCanvasContent(),n.afterClipDraw(t.tempFilePath)}))},fail:function(t){console.log(t)}},this)},saveClip:function(){var e=this;return(0,o.default)(a.default.mark((function n(){return a.default.wrap((function(n){while(1)switch(n.prev=n.next){case 0:return n.prev=0,n.delegateYield(a.default.mark((function n(){var i,r,s,o,c,h,u;return a.default.wrap((function(n){while(1)switch(n.prev=n.next){case 0:i=e.fourPointRange[1].x2-e.fourPointRange[0].x1,r=e.fourPointRange[2].y2-e.fourPointRange[0].y1,s=["imgCanvas","drawCanvas","timeCanvas","textCanvas"],o=t.createCanvasContext("itemCanvas-"+e.currentIndex,e),o.clearRect(0,0,i,r),o.draw(!0),c=a.default.mark((function n(o){var c;return a.default.wrap((function(n){while(1)switch(n.prev=n.next){case 0:return c=s[o]+"-"+e.currentIndex,n.next=3,new Promise((function(n){t.canvasToTempFilePath({canvasId:c,x:e.fourPointRange[0].x1,y:e.fourPointRange[0].y1,width:i,height:r,success:function(a){var s=t.createCanvasContext("itemCanvas-"+e.currentIndex,e);s.drawImage(a.tempFilePath,0,0,i,r),s.draw(!0,n)},fail:function(t){console.error("Failed to get layer ".concat(c,":"),t),n()}},e)}));case 3:case"end":return n.stop()}}),n)})),h=0;case 8:if(!(h<s.length)){n.next=13;break}return n.delegateYield(c(h),"t0",10);case 10:h++,n.next=8;break;case 13:return n.next=15,new Promise((function(n,a){t.canvasToTempFilePath({canvasId:"itemCanvas-"+e.currentIndex,width:i,height:r,destWidth:i,destHeight:r,success:function(t){return n(t)},fail:function(t){return a(t)}},e)}));case 15:if(u=n.sent,u&&u.tempFilePath){n.next=18;break}throw new Error("Failed to get temp file path");case 18:return e.isClip=!1,v[e.currentIndex].clearRect(0,0,e.screenInfo.width,e.canvasHeight[e.currentIndex]),v[e.currentIndex].draw(),n.next=23,e.afterClipDraw(u.tempFilePath);case 23:case"end":return n.stop()}}),n)}))(),"t0",2);case 2:n.next=8;break;case 4:n.prev=4,n.t1=n["catch"](0),console.error("裁剪保存失败：",n.t1),t.showToast({title:"裁剪失败，请重试",icon:"none"});case 8:case"end":return n.stop()}}),n,null,[[0,4]])})))()}},"clearCanvasContent",(function(){var t=this,e=arguments.length>0&&void 0!==arguments[0]&&arguments[0],n=["timeCanvas","textCanvas","drawCanvas","clipCanvas"];n.forEach((function(n){if(!e||"imgCanvas"!==n){var i=t[n][t.currentIndex];i&&(i.clearRect(0,0,t.screenInfo.width,t.canvasHeight[t.currentIndex]),i.draw())}}))})),mounted:function(){var e=this;t.getSystemInfo().then((function(t){Array.isArray(t)?(e.screenInfo.width=t[1].windowWidth,e.screenInfo.height=t[1].windowHeight):(e.screenInfo.width=t.windowWidth,e.screenInfo.height=t.windowHeight)}))},created:function(){x=[],l=[],I=[],g=[],m=[],v=[],w=[]}};e.default=p}).call(this,n("df3c")["default"],n("3223")["default"])}}]);
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'daihuobiji/detail/components/jack-filepicker/components/jack-fileupload/jack-fileupload-create-component',
    {
        'daihuobiji/detail/components/jack-filepicker/components/jack-fileupload/jack-fileupload-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('df3c')['createComponent'](__webpack_require__("4e04"))
        })
    },
    [['daihuobiji/detail/components/jack-filepicker/components/jack-fileupload/jack-fileupload-create-component']]
]);
