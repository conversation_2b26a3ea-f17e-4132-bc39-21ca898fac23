(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["yuyue/jdorderlist"],{"1d71":function(t,o,n){"use strict";(function(t){Object.defineProperty(o,"__esModule",{value:!0}),o.default=void 0;var n=getApp(),s={data:function(){return{opt:{},loading:!1,isload:!1,menuindex:-1,pre_url:n.globalData.pre_url,st:"11",datalist:[],pagenum:1,nomore:!1,nodata:!1,keyword:"",interval1:null,timestamp:"",showform:0,showtabbar:!1,showaddmoney:!1,showmodal:!1,addprice:0,showpaycodes:!1,paycode:"",addmoneystatus:0,showPunchModal:!1,punchId:null,punchSt:null,punchTitle:"",punchPhotoType:"",punchLocationInfo:null,punchPhotos:[],isLocating:!1,statusType:"waiting"}},computed:{canSubmitPunch:function(){return this.punchLocationInfo&&this.punchPhotos.length>0}},onLoad:function(t){this.opt=n.getopts(t),this.st=this.opt.st||"11",this.statusType=this.opt.statusType||"waiting",this.opt.mid?this.showtabbar=!1:this.showtabbar=!0,this.getdata()},onUnload:function(){clearInterval(this.interval1)},onPullDownRefresh:function(){this.getdata()},onReachBottom:function(){this.nodata||this.nomore||(this.pagenum=this.pagenum+1,this.getdata(!0))},methods:{changetab:function(o){this.st=o,t.pageScrollTo({scrollTop:0,duration:0}),this.getdata()},getdata:function(t){console.log("2025-01-03 22:55:53,565-INFO-[jdorderlist][getdata_001]开始获取数据，loadmore:",t,"当前状态类型:",this.statusType),t||(this.pagenum=1,this.datalist=[]);var o=this,s=o.st,e=o.pagenum,a=o.keyword;o.nodata=!1,o.nomore=!1;var l={st:s,pagenum:e,keyword:a,mid:this.opt.mid,statusType:this.statusType};console.log("2025-01-03 22:55:53,565-INFO-[jdorderlist][getdata_002]请求参数:",JSON.stringify(l)),n.post("ApiYuyueWorker/orderlist",l,(function(t){if(console.log("2025-01-03 22:55:53,565-INFO-[jdorderlist][getdata_003]接口响应状态:",t.status,"数据长度:",t.datalist?t.datalist.length:0),0==t.status)return console.log("2025-01-03 22:55:53,565-ERROR-[jdorderlist][getdata_004]接口返回错误:",t.msg),void n.alert(t.msg);var s=t.datalist;if(1==e)console.log("2025-01-03 22:55:53,565-INFO-[jdorderlist][getdata_005]首页数据加载，数据条数:",s.length),o.datalist=s,o.nowtime=t.nowtime,o.showform=t.showform,o.showaddmoney=t.addmoney,0==s.length&&(o.nodata=!0,console.log("2025-01-03 22:55:53,565-INFO-[jdorderlist][getdata_006]无数据显示")),o.loaded(),o.updatemylocation(),clearInterval(o.interval1),o.interval1=setInterval((function(){o.updatemylocation(!0),o.nowtime=o.nowtime+10}),1e4);else if(console.log("2025-01-03 22:55:53,565-INFO-[jdorderlist][getdata_007]分页数据加载，数据条数:",s.length),0==s.length)o.nomore=!0,console.log("2025-01-03 22:55:53,565-INFO-[jdorderlist][getdata_008]没有更多数据");else{var a=o.datalist,l=a.concat(s);o.datalist=l,console.log("2025-01-03 22:55:53,565-INFO-[jdorderlist][getdata_009]数据合并完成，总条数:",l.length)}}))},updatemylocation:function(){var t=this;n.getLocation((function(o){var s=o.longitude,e=o.latitude,a=t.datalist;for(var l in console.log(a),a){var i=a[l],u=t.getdistance(i.longitude2,i.latitude2,s,e,1);i.juli2=u.juli,i.juli2_unit=u.unit,i.leftminute=parseInt((i.yujitime-t.nowtime)/60),a[l]=i}t.datalist=a,t.timestamp=parseInt((new Date).getTime()/1e3),n.get("ApiYuyueWorker/updatemylocation",{longitude:s,latitude:e,t:t.timestamp},(function(){}))}))},getdistance:function(t,o,n,s){if(!o||!t||!s||!n)return"";var e=o*Math.PI/180,a=s*Math.PI/180,l=e-a,i=t*Math.PI/180-n*Math.PI/180,u=12756274*Math.asin(Math.sqrt(Math.pow(Math.sin(l/2),2)+Math.cos(e)*Math.cos(a)*Math.pow(Math.sin(i/2),2))),d="m";return u>1e3&&(u/=1e3,d="km"),u=u.toFixed(1),{juli:u,unit:d}},setst:function(t){t.currentTarget.dataset.id,t.currentTarget.dataset.st;this.openPunchModal(t)},openPunchModal:function(t){var o=t.currentTarget.dataset.id,n=t.currentTarget.dataset.st;console.log("2025-01-03 22:55:53,565-INFO-[jdorderlist][openPunchModal_001]打开打卡弹窗，id:",o,"状态:",n);var s="",e="";2==n?this.showaddmoney?(s="出发打卡",e="出发前照片"):(s="到达打卡",e="到达现场照片"):3!=n&&5!=n||(s="完成打卡",e="服务完成照片"),this.punchId=o,this.punchSt=n,this.punchTitle=s,this.punchPhotoType=e,this.punchLocationInfo=null,this.punchPhotos=[],this.showPunchModal=!0,this.getPunchLocation()},closePunchModal:function(){this.showPunchModal=!1},getPunchLocation:function(){var o=this;o.isLocating=!0,console.log("2025-01-03 22:55:53,565-INFO-[jdorderlist][getPunchLocation_001]开始获取位置"),n.getLocation((function(n){console.log("2025-01-03 22:55:53,565-INFO-[jdorderlist][getPunchLocation_002]位置获取成功:",JSON.stringify(n)),o.isLocating=!1,o.punchLocationInfo={longitude:n.longitude,latitude:n.latitude},t.showToast({title:"位置获取成功",icon:"success",duration:1500})}),(function(n){console.log("2025-01-03 22:55:53,565-ERROR-[jdorderlist][getPunchLocation_003]位置获取失败:",JSON.stringify(n)),o.isLocating=!1,t.showModal({title:"位置获取失败",content:"请检查是否授予定位权限，并重试",confirmText:"重试",cancelText:"取消",success:function(t){t.confirm&&o.getPunchLocation()}})}))},selectPunchPhoto:function(){var o=this;console.log("2025-01-03 22:55:53,565-INFO-[jdorderlist][selectPunchPhoto_001]开始选择照片"),n.chooseImage((function(n){console.log("2025-01-03 22:55:53,565-INFO-[jdorderlist][selectPunchPhoto_002]照片上传成功:",n),o.punchPhotos=o.punchPhotos.concat(n),t.showToast({title:"照片上传成功",icon:"success",duration:1500})}),9-o.punchPhotos.length)},reselectPunchPhoto:function(t){var o=t.currentTarget.dataset.index;this.punchPhotos.splice(o,1)},removePunchPhoto:function(t){var o=t.currentTarget.dataset.index;console.log("2025-01-03 22:55:53,565-INFO-[jdorderlist][removePunchPhoto_001]移除照片，索引:",o),o>=0&&o<this.punchPhotos.length&&(this.punchPhotos.splice(o,1),console.log("2025-01-03 22:55:53,565-INFO-[jdorderlist][removePunchPhoto_002]照片移除成功，剩余数量:",this.punchPhotos.length))},previewPunchPhoto:function(o){var n=o.currentTarget.dataset.url;this.punchPhotos&&this.punchPhotos.length>0&&t.previewImage({urls:this.punchPhotos,current:n})},submitPunchData:function(){var o=this;if(o.canSubmitPunch){console.log("2025-01-03 22:55:53,565-INFO-[jdorderlist][submitPunchData_001]准备提交打卡数据");var s={id:o.punchId,st:o.punchSt,longitude:o.punchLocationInfo.longitude,latitude:o.punchLocationInfo.latitude};2==o.punchSt?s.arrival_photo=o.punchPhotos.join(","):s.complete_photo=o.punchPhotos.join(","),console.log("2025-01-03 22:55:53,565-INFO-[jdorderlist][submitPunchData_002]提交参数:",JSON.stringify(s)),t.showLoading({title:"提交中...",mask:!0}),n.post("ApiYuyueWorker/setst",s,(function(s){if(t.hideLoading(),console.log("2025-01-03 22:55:53,565-INFO-[jdorderlist][submitPunchData_003]提交响应:",JSON.stringify(s)),1===s.status){o.closePunchModal(),n.success(s.msg);var e="",a="";2==o.punchSt?o.showaddmoney?(e="出发打卡成功",a="您已成功打卡出发，请尽快前往客户位置"):(e="到达打卡成功",a="您已成功打卡到达，请开始服务"):(e="完成打卡成功",a="服务已完成，感谢您的工作"),s.distance&&2==o.punchSt&&(a="您距离客户位置: "+o.formatDistance(s.distance)+"\n"+a),t.showModal({title:e,content:a,showCancel:!1,success:function(){setTimeout((function(){o.getdata()}),500)}})}else n.alert(s.msg||"提交失败")}))}else t.showToast({title:"请先获取位置并上传照片",icon:"none",duration:2e3})},formatDistance:function(t){return t||0===t?(t=parseFloat(t),isNaN(t)?"未知":t>=1e3?(t/1e3).toFixed(2)+"公里":parseInt(t)+"米"):"未知"},searchConfirm:function(t){var o=t.detail.value;this.keyword=o,this.getdata()},addmoney:function(t){this.showmodal=!0,this.id=t.currentTarget.dataset.id},cancel:function(t){this.showmodal=!1,this.showpaycodes=!1},bindMoney:function(t){this.addprice=t.detail.value},addconfirm:function(t){var o=this;o.addprice?n.post("ApiYuyueWorker/addmoney",{id:o.id,price:o.addprice,addmoneyPayorderid:o.addmoneyPayorderid},(function(t){n.showLoading(!1),n.success(t.msg),t.payorderid&&(o.showmodal=!1,o.getdata())})):n.error("请输入金额")},showpaycode:function(t){this.showpaycodes=!0;var o=t.currentTarget.dataset.key;this.index=o,this.addprice=this.datalist[o].addprice,this.paycode=this.datalist[o].paycode,this.addmoneystatus=this.datalist[o].addmoneystatus,this.addmoneyPayorderid=this.datalist[o].addmoneyPayorderid,this.id=t.currentTarget.dataset.id},update:function(t){this.showmodal=!0,this.showpaycodes=!1;var o=t.currentTarget.dataset.key;this.addprice=this.datalist[o].addprice},daohang:function(o){var n=o.currentTarget.dataset.index,s=this.datalist[n];t.showActionSheet({itemList:["导航到商家","导航到用户"],success:function(o){if(o.tapIndex>=0){if(0==o.tapIndex)var n=s.longitude,e=s.latitude,a=s.binfo.name,l=s.binfo.address;else n=s.longitude2,e=s.latitude2,a=s.orderinfo.address,l=s.orderinfo.address;t.openLocation({latitude:parseFloat(e),longitude:parseFloat(n),name:a,address:l,scale:13,success:function(){console.log("success")},fail:function(t){console.log(t)}})}}})},switchStatusType:function(o){if(console.log("2025-01-03 22:55:53,565-INFO-[jdorderlist][switchStatusType_001]状态切换，原状态:",this.statusType,"新状态:",o),this.statusType!==o){this.statusType=o,console.log("2025-01-03 22:55:53,565-INFO-[jdorderlist][switchStatusType_003]开始切换到状态:",o);var n="jdorderlist?st="+this.st;"all"!==o&&(n+="&statusType="+o),this.opt.mid&&(n+="&mid="+this.opt.mid),console.log("2025-01-03 22:55:53,565-INFO-[jdorderlist][switchStatusType_004]构建URL:",n);var s=getCurrentPages(),e=s[s.length-1];e&&e.route&&e.route.indexOf("jdorderlist")>-1&&t.redirectTo({url:n}),t.pageScrollTo({scrollTop:0,duration:0}),console.log("2025-01-03 22:55:53,565-INFO-[jdorderlist][switchStatusType_005]开始获取数据"),this.getdata()}else console.log("2025-01-03 22:55:53,565-INFO-[jdorderlist][switchStatusType_002]状态相同，跳过切换")}}};o.default=s}).call(this,n("df3c")["default"])},6568:function(t,o,n){"use strict";n.r(o);var s=n("b528"),e=n("fcf8");for(var a in e)["default"].indexOf(a)<0&&function(t){n.d(o,t,(function(){return e[t]}))}(a);n("aa7e");var l=n("828b"),i=Object(l["a"])(e["default"],s["b"],s["c"],!1,null,null,null,!1,s["a"],void 0);o["default"]=i.exports},"6cc9":function(t,o,n){"use strict";(function(t,o){var s=n("47a9");n("06e9");s(n("3240"));var e=s(n("6568"));t.__webpack_require_UNI_MP_PLUGIN__=n,o(e.default)}).call(this,n("3223")["default"],n("df3c")["createPage"])},a8ba:function(t,o,n){},aa7e:function(t,o,n){"use strict";var s=n("a8ba"),e=n.n(s);e.a},b528:function(t,o,n){"use strict";n.d(o,"b",(function(){return e})),n.d(o,"c",(function(){return a})),n.d(o,"a",(function(){return s}));var s={nomore:function(){return n.e("components/nomore/nomore").then(n.bind(null,"3892"))},nodata:function(){return n.e("components/nodata/nodata").then(n.bind(null,"101c"))},loading:function(){return n.e("components/loading/loading").then(n.bind(null,"ceaa"))},dpTabbar:function(){return n.e("components/dp-tabbar/dp-tabbar").then(n.bind(null,"b875"))},popmsg:function(){return n.e("components/popmsg/popmsg").then(n.bind(null,"2bf2"))}},e=function(){var t=this,o=t.$createElement,n=(t._self._c,t.isload&&"waiting"===t.statusType?t.t("color1"):null),s=t.isload&&"waiting"===t.statusType?t.t("color1"):null,e=t.isload&&"serving"===t.statusType?t.t("color1"):null,a=t.isload&&"serving"===t.statusType?t.t("color1"):null,l=t.isload&&"completed"===t.statusType?t.t("color1"):null,i=t.isload&&"completed"===t.statusType?t.t("color1"):null,u=t.isload&&"transferred"===t.statusType?t.t("color1"):null,d=t.isload&&"transferred"===t.statusType?t.t("color1"):null,c=t.isload&&"all"===t.statusType?t.t("color1"):null,r=t.isload&&"all"===t.statusType?t.t("color1"):null,h=t.isload?t.__map(t.datalist,(function(o,n){var s=t.__get_orig(o),e=1==o.fwtype&&3==o.status?t.t("color1"):null,a=1==o.fwtype&&1==o.status?t.t("color1"):null,l=1==o.fwtype&&2==o.status?t.t("color1"):null,i=1!=o.fwtype&&2==o.fwtype&&3==o.status?t.t("color1"):null,u=1!=o.fwtype&&2==o.fwtype&&3!=o.status&&1==o.status?t.t("color1"):null,d=1!=o.fwtype&&2==o.fwtype&&3!=o.status&&1!=o.status&&2==o.status&&t.showaddmoney&&!o.sign_status?t.t("color1"):null,c=1!=o.fwtype&&2==o.fwtype&&3!=o.status&&1!=o.status&&2==o.status&&t.showaddmoney&&o.sign_status?t.t("color1"):null,r=1==o.fwtype||2!=o.fwtype||3==o.status||1==o.status||2!=o.status||t.showaddmoney?null:t.t("color1"),h=1==o.fwtype&&1==o.status?t.t("color1"):null,p=1==o.fwtype&&2==o.status?t.t("color1"):null,m=1==o.fwtype&&3==o.status?t.t("color1"):null,f=1==o.fwtype&&1==o.status?t.t("color1"):null,g=1==o.fwtype&&1==o.status?t.t("color1rgb"):null,y=1==o.fwtype&&2==o.status?t.t("color1"):null,w=1==o.fwtype&&2==o.status?t.t("color1rgb"):null,P=1!=o.fwtype&&2==o.fwtype&&1==o.status?t.t("color1"):null,b=1!=o.fwtype&&2==o.fwtype&&2==o.status?t.t("color1"):null,v=1!=o.fwtype&&2==o.fwtype&&3==o.status?t.t("color1"):null,_=1!=o.fwtype&&2==o.fwtype&&t.showaddmoney&&1==o.sign_status&&2==o.status&&o.addprice<=0?t.t("color1rgb"):null,T=1!=o.fwtype&&2==o.fwtype&&t.showaddmoney&&1==o.sign_status&&2==o.status&&o.addprice<=0?t.t("color1"):null,I=1!=o.fwtype&&2==o.fwtype&&t.showaddmoney&&1==o.sign_status&&2==o.status&&o.addprice<=0?t.t("color1"):null,j=1!=o.fwtype&&2==o.fwtype&&t.showaddmoney&&1==o.status?t.t("color1"):null,O=1!=o.fwtype&&2==o.fwtype&&t.showaddmoney&&1==o.status?t.t("color1rgb"):null,M=1!=o.fwtype&&2==o.fwtype&&t.showaddmoney&&o.addprice>0?t.t("color1rgb"):null,L=1!=o.fwtype&&2==o.fwtype&&t.showaddmoney&&o.addprice>0?t.t("color1"):null,N=1!=o.fwtype&&2==o.fwtype&&t.showaddmoney&&o.addprice>0?t.t("color1"):null,F=1!=o.fwtype&&2==o.fwtype&&t.showaddmoney&&!o.sign_status&&2==o.status?t.t("color1"):null,S=1!=o.fwtype&&2==o.fwtype&&t.showaddmoney&&!o.sign_status&&2==o.status?t.t("color1rgb"):null,k=1!=o.fwtype&&2==o.fwtype&&t.showaddmoney&&1==o.sign_status&&2==o.status?t.t("color1"):null,x=1!=o.fwtype&&2==o.fwtype&&t.showaddmoney&&1==o.sign_status&&2==o.status?t.t("color1rgb"):null,D=1==o.fwtype||2!=o.fwtype||t.showaddmoney||1!=o.status?null:t.t("color1"),R=1==o.fwtype||2!=o.fwtype||t.showaddmoney||1!=o.status?null:t.t("color1rgb"),J=1==o.fwtype||2!=o.fwtype||t.showaddmoney||2!=o.status?null:t.t("color1"),A=1==o.fwtype||2!=o.fwtype||t.showaddmoney||2!=o.status?null:t.t("color1rgb");return{$orig:s,m10:e,m11:a,m12:l,m13:i,m14:u,m15:d,m16:c,m17:r,m18:h,m19:p,m20:m,m21:f,m22:g,m23:y,m24:w,m25:P,m26:b,m27:v,m28:_,m29:T,m30:I,m31:j,m32:O,m33:M,m34:L,m35:N,m36:F,m37:S,m38:k,m39:x,m40:D,m41:R,m42:J,m43:A}})):null,p=t.isload&&t.showtabbar&&3!=t.st?t.t("color1"):null,m=t.isload&&t.showtabbar&&3==t.st?t.t("color1"):null,f=t.isload&&t.showmodal?t.t("color1"):null,g=t.isload&&t.showmodal?t.t("color1rgb"):null,y=t.isload&&t.showpaycodes&&1==t.addmoneystatus?t.t("color1"):null,w=t.isload&&t.showpaycodes&&0==t.addmoneystatus?t.t("color1"):null,P=t.isload&&t.showpaycodes&&0==t.addmoneystatus?t.t("color1rgb"):null,b=t.isload&&t.showPunchModal?t.t("color1"):null,v=t.isload&&t.showPunchModal&&t.punchLocationInfo?t.t("color1"):null,_=t.isload&&t.showPunchModal&&t.punchLocationInfo?t.t("color1"):null,T=!t.isload||!t.showPunchModal||t.punchLocationInfo||t.isLocating||t.punchLocationInfo?null:t.t("color1"),I=!t.isload||!t.showPunchModal||t.punchLocationInfo||t.isLocating||t.punchLocationInfo?null:t.t("color1rgb"),j=t.isload&&t.showPunchModal?t.punchPhotos.length:null,O=t.isload&&t.showPunchModal?t.punchPhotos.length:null,M=t.isload&&t.showPunchModal?t.punchPhotos.length:null,L=t.isload&&t.showPunchModal&&t.canSubmitPunch?t.t("color1"):null,N=t.isload&&t.showPunchModal&&t.canSubmitPunch?t.t("color1rgb"):null;t.$mp.data=Object.assign({},{$root:{m0:n,m1:s,m2:e,m3:a,m4:l,m5:i,m6:u,m7:d,m8:c,m9:r,l0:h,m44:p,m45:m,m46:f,m47:g,m48:y,m49:w,m50:P,m51:b,m52:v,m53:_,m54:T,m55:I,g0:j,g1:O,g2:M,m56:L,m57:N}})},a=[]},fcf8:function(t,o,n){"use strict";n.r(o);var s=n("1d71"),e=n.n(s);for(var a in s)["default"].indexOf(a)<0&&function(t){n.d(o,t,(function(){return s[t]}))}(a);o["default"]=e.a}},[["6cc9","common/runtime","common/vendor"]]]);