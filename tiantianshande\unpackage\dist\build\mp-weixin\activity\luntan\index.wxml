<view><block wx:if="{{isload}}"><block><view class="container2"><block wx:if="{{sysset.pic}}"><image style="width:100%;height:auto;" src="{{sysset.pic}}" mode="widthFix" data-url="{{sysset.picurl}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"></image></block><block wx:if="{{$root.g0>0}}"><view class="navbox"><block wx:for="{{clist}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><view class="nav_li" style="cursor:pointer;" data-url="{{'ltlist?cid='+item.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image src="{{item.pic}}"></image><view>{{item.name}}</view></view></block></block></view></block></view><view style="display:flex;width:100%;height:16rpx;background:#F7F7F7;"></view><view class="container2"><view class="listtitle">最新动态</view><view class="datalist"><block wx:for="{{datalist}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><view class="item" data-url="{{'detail?id='+item.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="top"><image class="f1" src="{{item.headimg}}"></image><view class="f2"><view class="t1">{{item.nickname}}</view><view class="t2">{{item.showtime}}</view></view></view><view class="con"><view class="f1"><text style="white-space:pre-wrap;">{{item.content}}</text></view><block wx:if="{{item.pics}}"><view class="f2"><block wx:for="{{item.pics}}" wx:for-item="pic" wx:for-index="idx" wx:key="idx"><image style="height:auto;" src="{{pic}}" mode="widthFix"></image></block></view></block><block wx:if="{{item.video}}"><video class="video" id="video" src="{{item.video}}" data-event-opts="{{[['tap',[['playvideo',['$event']]]]]}}" catchtap="__e"></video></block></view><view class="bot"><view class="f1"><image style="margin-top:0;" src="/static/img/lt_read.png"></image>{{item.readcount}}</view><view class="f1" style="margin-left:60rpx;"><image src="/static/img/lt_pinglun.png"></image>{{item.plcount}}</view><block wx:if="{{need_call&&item.mobile}}"><view class="f1" style="margin-left:60rpx;" data-phone="{{item.mobile}}" data-event-opts="{{[['tap',[['callphone',['$event']]]]]}}" catchtap="__e"><image style="width:30rpx;height:30rpx;margin-top:0;" src="/static/img/mobile.png"></image></view></block><view class="f2"></view><block wx:if="{{sysset.cansave}}"><view class="f4" data-id="{{item.id}}" data-index="{{index}}" data-event-opts="{{[['tap',[['savecontent',['$event']]]]]}}" catchtap="__e"><image src="/static/img/lt_save.png"></image>保存</view></block><view class="f3" data-id="{{item.id}}" data-index="{{index}}" data-event-opts="{{[['tap',[['zan',['$event']]]]]}}" catchtap="__e"><image src="{{'/static/img/lt_like'+(item['iszan']==0?'':'2')+'.png'}}"></image>{{item.zan}}</view></view></view></block></block><block wx:if="{{nomore}}"><nomore vue-id="11286bc5-1" bind:__l="__l"></nomore></block><block wx:if="{{nodata}}"><nodata vue-id="11286bc5-2" bind:__l="__l"></nodata></block></view></view><view class="{{['covermy',menuindex>-1?'tabbarbot':'notabbarbot']}}" data-url="fatie" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image src="{{pre_url+'/static/img/lt_fatie.png'}}"></image></view></block></block><block wx:if="{{loading}}"><loading vue-id="11286bc5-3" bind:__l="__l"></loading></block><dp-tabbar vue-id="11286bc5-4" opt="{{opt}}" data-event-opts="{{[['^getmenuindex',[['getmenuindex']]]]}}" bind:getmenuindex="__e" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="11286bc5-5" data-ref="popmsg" bind:__l="__l"></popmsg></view>