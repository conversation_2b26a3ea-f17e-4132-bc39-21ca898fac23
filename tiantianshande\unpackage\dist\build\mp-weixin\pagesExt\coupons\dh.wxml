<view class="container"><block wx:if="{{isload}}"><block><form style="width:100%;" report-submit="true" data-event-opts="{{[['submit',[['subconfirm',['$event']]]]]}}" bindsubmit="__e"><view class="title">{{"请输入兑换码进行兑换:\""+coupon.name+'"'}}</view><block><view class="inputdiv"><input id="dhcode" type="text" name="dhcode" placeholder-style="color:#666;" placeholder="请输入您的兑换码" value="{{dhcode}}"/><block wx:if="{{platform!='h5'&&lipinset.scanshow==1}}"><view data-event-opts="{{[['tap',[['saoyisao',['$event']]]]]}}" class="scanicon" bindtap="__e"><image src="{{pre_url+'/static/img/scan-icon2.png'}}"></image></view></block></view></block><button class="btn" form-type="submit">立即兑换</button><view class="f0" data-url="record" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><text>查看兑换记录</text></view><view class="f0" style="margin-top:40rpx;line-height:60rpx;" data-url="{{'coupondetail?id='+coupon.id+'&rid='+record.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><text>返回</text></view></form></block></block><block wx:if="{{loading}}"><loading vue-id="d05547e8-1" bind:__l="__l"></loading></block><dp-tabbar vue-id="d05547e8-2" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="d05547e8-3" data-ref="popmsg" bind:__l="__l"></popmsg></view>