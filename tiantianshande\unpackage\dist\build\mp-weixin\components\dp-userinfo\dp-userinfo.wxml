<view><block wx:if="{{params.style==1}}"><view style="{{('background: linear-gradient(180deg, '+$root.m0+' 0%, rgba('+$root.m1+',0) 100%);padding-bottom:'+(params.cardUpgradeShow==1?'0rpx':'0')+';')}}"><view class="dp-userinfo" style="{{'background:'+('url('+params.bgimg+') no-repeat')+';'+('backgroundsize:'+('100% auto')+';')+('margin:'+(params.margin_y*2.2+'rpx '+params.margin_x*2.2+'rpx')+';')+('padding:'+(params.padding_y*2.2+'rpx '+params.padding_x*2.2+'rpx')+';')}}"><view class="banner"><view class="info"><view class="f1"><image class="headimg" src="{{data.userinfo.headimg}}" background-size="cover" data-event-opts="{{[['tap',[['weixinlogin',['$event']]]]]}}" bindtap="__e"></image><view class="flex-y-center"><view class="nickname">{{data.userinfo.nickname}}</view><block wx:if="{{params.midshow=='1'&&(data.userinfo.show_user_id===undefined||data.userinfo.show_user_id==1)}}"><text style="font-size:26rpx;padding-left:10rpx;">{{"(ID:"+data.userinfo.id+")"}}</text></block><block wx:if="{{params.levelshow==1}}"><view class="user-level" data-levelid data-event-opts="{{[['tap',[['openLevelup',['$event']]]]]}}" bindtap="__e"><block wx:if="{{data.userlevel.icon}}"><image class="level-img" src="{{data.userlevel.icon}}"></image></block><view class="level-name">{{data.userlevel.name}}</view></view></block><block wx:for="{{data.userlevelList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block wx:if="{{params.levelshow==1}}"><view class="user-level" data-levelid="{{item.id}}" data-event-opts="{{[['tap',[['openLevelup',['$event']]]]]}}" bindtap="__e"><block wx:if="{{item.icon}}"><image class="level-img" src="{{item.icon}}"></image></block><view class="level-name">{{item.name}}</view></view></block></block><block wx:if="{{data.userlevel.can_agent>0&&data.sysset.reg_invite_code!='0'&&data.sysset.reg_invite_code_type==1}}"><view class="usermid" style="margin-left:10rpx;font-size:24rpx;color:#999;">邀请码：<text user-select="true" selectable="true">{{data.userinfo.yqcode}}</text></view></block><block wx:if="{{params.parent_show==1&&data.userinfo.referral_detail&&data.userinfo.referral_detail.nickname}}"><view class="parent-simple" style="margin-left:10rpx;margin-top:8rpx;"><text class="parent-label" style="font-size:22rpx;color:#999;margin-right:8rpx;">上级：</text><text class="parent-name" style="font-size:24rpx;color:#333;font-weight:500;">{{data.userinfo.referral_detail.nickname}}</text></view></block></view><block wx:if="{{data.zhaopin&&data.zhaopin.show_zhaopin}}"><view class="flex"><block wx:if="{{data.zhaopin.is_qiuzhi_renzheng&&!data.zhaopin.is_qiuzhi_renzheng}}"><text class="zhaopin-renzheng">认证保障中</text></block><block wx:if="{{data.zhaopin.is_qiuzhi_qianyue}}"><text class="zhaopin-renzheng">签约保障中</text></block><block wx:if="{{data.zhaopin.is_zhaopin_renzheng}}"><text class="zhaopin-renzheng">认证企业</text></block></view></block></view><block wx:if="{{platform=='wx'}}"><block><block wx:if="{{params.cardshow==1&&data.userinfo.card_code}}"><view class="usercard" data-card_id="{{data.userinfo.card_id}}" data-card_code="{{data.userinfo.card_code}}" data-event-opts="{{[['tap',[['opencard',['$event']]]]]}}" bindtap="__e"><image class="img" src="/static/img/ico-card2.png"></image><text class="txt">会员卡</text></view></block><block wx:if="{{params.cardshow==1&&!data.userinfo.card_code}}"><view class="usercard" data-card_id="{{data.card_id}}" data-event-opts="{{[['tap',[['addmembercard',['$event']]]]]}}" bindtap="__e"><image class="img" src="/static/img/ico-card2.png"></image><text class="txt">会员卡</text></view></block></block></block><block wx:if="{{platform=='mp'}}"><block><block wx:if="{{params.cardshow==1&&data.userinfo.card_code}}"><view class="usercard" data-card_id="{{data.userinfo.card_id}}" data-card_code="{{data.userinfo.card_code}}" data-event-opts="{{[['tap',[['opencard',['$event']]]]]}}" bindtap="__e"><image class="img" src="/static/img/ico-card2.png"></image><text class="txt">会员卡</text></view></block><block wx:if="{{params.cardshow==1&&!data.userinfo.card_code}}"><view class="usercard" data-url="{{data.card_returl}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image class="img" src="/static/img/ico-card2.png"></image><text class="txt">会员卡</text></view></block></block></block></view><view class="custom_field"><block wx:if="{{params.creditshow==1}}"><view class="item" data-money-type="credit" data-default-url="/pagesExa/my/creditlog" data-event-opts="{{[['tap',[['gotoMoneyItem',['$event']]]]]}}" bindtap="__e"><text class="t2">{{data.userinfo.credit_score||0}}</text><text class="t1">{{$root.m2}}</text></view></block><block wx:if="{{params.workshow==1}}"><view class="item" data-money-type="work" data-default-url="/zhaopin/myApply" data-event-opts="{{[['tap',[['gotoMoneyItem',['$event']]]]]}}" bindtap="__e"><text class="t2">{{data.userinfo.job_count||0}}</text><text class="t1">{{$root.m3}}</text></view></block><block wx:if="{{params.inviteshow==1}}"><view class="item" data-money-type="invite" data-default-url="/activity/commission/myteam" data-event-opts="{{[['tap',[['gotoMoneyItem',['$event']]]]]}}" bindtap="__e"><text class="t2">{{data.userinfo.team_count||0}}</text><text class="t1">{{$root.m4}}</text></view></block></view><block wx:if="{{params.moneyshow==1||params.scoreshow==1||params.couponshow==1}}"><view class="custom_field"><block wx:if="{{params.moneyshow==1}}"><view class="item" data-money-type="money" data-default-url="/pagesExb/money/recharge" data-event-opts="{{[['tap',[['gotoMoneyItem',['$event']]]]]}}" bindtap="__e"><text class="t2">{{$root.m5}}</text><text class="t1">{{$root.m6}}</text></view></block><block wx:if="{{params.commissionshow==1&&data.userlevel&&data.userlevel.can_agent>0}}"><view class="item" data-money-type="commission" data-default-url="/activity/commission/index" data-event-opts="{{[['tap',[['gotoMoneyItem',['$event']]]]]}}" bindtap="__e"><text class="t2">{{$root.m7}}</text><text class="t1">{{$root.m8}}</text></view></block><block wx:if="{{params.scoreshow==1}}"><view class="item" data-money-type="score" data-default-url="/pagesExa/my/scorelog" data-event-opts="{{[['tap',[['gotoMoneyItem',['$event']]]]]}}" bindtap="__e"><text class="t2">{{$root.m9}}</text><text class="t1">{{$root.m10}}</text></view></block><block wx:if="{{params.couponshow==1}}"><view class="item" data-money-type="coupon" data-default-url="/pagesExb/coupon/mycoupon" data-event-opts="{{[['tap',[['gotoMoneyItem',['$event']]]]]}}" bindtap="__e"><text class="t2">{{data.userinfo.couponcount}}</text><text class="t1">{{$root.m11}}</text></view></block><block wx:if="{{params.tuikuangshow==1}}"><view class="item"><text class="t2">{{data.userinfo.kuang_num}}</text><text class="t1">{{$root.m12}}</text></view></block></view></block><view class="custom_field"><block wx:if="{{params.xianjinquanshow==1}}"><view class="custom_field"><view class="item" data-money-type="xianjinquan" data-default-url="/pagesExb/money/moneylog?st=8" data-event-opts="{{[['tap',[['gotoMoneyItem',['$event']]]]]}}" bindtap="__e"><text class="t2">{{$root.m13}}</text><text class="t1">{{$root.m14}}</text></view></view></block><block wx:if="{{params.huangjifenshow==1}}"><view class="custom_field"><view class="item" data-money-type="huangjifen" data-default-url="/pagesExb/money/moneylog?st=9" data-event-opts="{{[['tap',[['gotoMoneyItem',['$event']]]]]}}" bindtap="__e"><text class="t2">{{$root.m15}}</text><text class="t1">{{$root.m16}}</text></view></view></block><block wx:if="{{params.gongxianzhishow==1}}"><view class="custom_field"><view class="item" data-money-type="gongxianzhi" data-default-url="/pagesExb/money/moneylog?st=10" data-event-opts="{{[['tap',[['gotoMoneyItem',['$event']]]]]}}" bindtap="__e"><text class="t2">{{$root.m17}}</text><text class="t1">{{$root.m18}}</text></view></view></block><block wx:if="{{params.shouyichishow==1}}"><view class="custom_field"><view class="item" data-money-type="shouyichi" data-default-url="/pagesExb/money/moneylog?st=15" data-event-opts="{{[['tap',[['gotoMoneyItem',['$event']]]]]}}" bindtap="__e"><text class="t2">{{$root.m19}}</text><text class="t1">{{$root.m20}}</text></view></view></block><block wx:if="{{params.chuangyezhishow==1}}"><view class="custom_field"><block wx:if="{{params.scoreshow==1}}"><view class="item" data-url="/pagesExa/my/scoreloghuang" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><text class="t2">{{$root.m21}}</text><text class="t1">{{$root.m22}}</text></view></block></view></block><block wx:if="{{params.suishijineshow==1}}"><view class="custom_field"><view class="item" data-url="/pagesExt/othermoney/withdraw?type=money2" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><text class="t2" style="color:#dd0000;">{{$root.m23}}</text><text class="t1" style="color:#dd0000;">{{$root.m24}}</text></view></view></block></view><block wx:if="{{data.userinfo.othermoney_status==1}}"><view class="custom_field"><view class="item" data-url="/pagesExt/othermoney/withdraw?type=money2" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><text class="t2">{{$root.m25}}</text><text class="t1">{{$root.m26}}</text></view><view class="item" data-url="/pagesExt/othermoney/withdraw?type=money3" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><text class="t2">{{$root.m27}}</text><text class="t1">{{$root.m28}}</text></view><view class="item" data-url="/pagesExt/othermoney/withdraw?type=money4" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><text class="t2">{{$root.m29}}</text><text class="t1">{{$root.m30}}</text></view><view class="item" data-url="/pagesExt/othermoney/withdraw?type=money5" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><text class="t2">{{$root.m31}}</text><text class="t1">{{$root.m32}}</text></view><view class="item" data-url="/pagesExt/othermoney/frozen_moneylog" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><text class="t2">{{$root.m33}}</text><text class="t1">{{$root.m34}}</text></view></view></block></view><block wx:if="{{params.seticonshow!=='0'}}"><view class="userset" data-url="/pagesExa/my/set" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image class="img" src="/static/img/set.png"></image></view></block></view></view></block><block wx:if="{{params.style==2}}"><view style="{{('background: linear-gradient(45deg,'+$root.m35+' 0%, rgba('+$root.m36+',0.8) 100%)')}}"><block wx:if="{{params.style==2}}"><view class="dp-userinfo2" style="{{'background:'+('url('+params.bgimg+') no-repeat')+';'+('background-size:'+('100% auto')+';')+('margin:'+(params.margin_y*2.2+'rpx '+params.margin_x*2.2+'rpx')+';')+('padding:'+(params.padding_y*2.2+'rpx '+params.padding_x*2.2+'rpx')+';')+('height:'+(400+(data.userinfo.othermoney_status==1?110:0)+(params.cardUpgradeShow==1?150:0)+(params.ordershow==1?100:0)+'rpx')+';')}}"><view class="info"><image class="headimg" src="{{data.userinfo.headimg}}" data-event-opts="{{[['tap',[['weixinlogin',['$event']]]]]}}" bindtap="__e"></image><view class="nickname"><view class="nick">{{data.userinfo.nickname+''}}</view><block wx:if="{{data.zhaopin&&data.zhaopin.show_zhaopin}}"><view class="flex"><block wx:if="{{data.zhaopin.is_qiuzhi_renzheng&&!data.zhaopin.is_qiuzhi_qianyue}}"><text class="qiuzhi-renzheng">认证保障中</text></block><block wx:if="{{data.zhaopin.is_qiuzhi_qianyue}}"><text class="qiuzhi-qianyue">签约保障中</text></block><block wx:if="{{data.zhaopin.is_zhaopin_renzheng}}"><text class="zhaopin-renzheng">认证企业</text></block></view></block><block wx:if="{{params.levelshow==1}}"><view style="display:flex;"><view class="user-level" data-levelid data-event-opts="{{[['tap',[['openLevelup',['$event']]]]]}}" bindtap="__e"><block wx:if="{{data.userlevel.icon}}"><image class="level-img" src="{{data.userlevel.icon}}"></image></block><view class="level-name">{{data.userlevel.name}}</view></view><block wx:for="{{data.userlevelList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="user-level" data-levelid="{{item.id}}" data-event-opts="{{[['tap',[['openLevelup',['$event']]]]]}}" bindtap="__e"><block wx:if="{{item.icon}}"><image class="level-img" src="{{item.icon}}"></image></block><view class="level-name">{{item.name}}</view></view></block></view></block><block wx:if="{{params.midshow=='1'&&(data.userinfo.show_user_id===undefined||data.userinfo.show_user_id==1)}}"><view class="usermid">用户ID：<text user-select="true" selectable="true">{{data.userinfo.id}}</text></view></block><block wx:if="{{data.userlevel.can_agent>0&&data.sysset.reg_invite_code!='0'&&data.sysset.reg_invite_code_type==1}}"><view class="usermid">邀请码：<text user-select="true" selectable="true">{{data.userinfo.yqcode}}</text></view></block><block wx:if="{{params.parent_show==1&&data.userinfo.referral_detail&&data.userinfo.referral_detail.nickname}}"><view class="parent-simple-style2" style="margin-top:8rpx;"><text class="parent-label" style="font-size:22rpx;color:rgba(255,255,255,0.7);margin-right:8rpx;">上级：</text><text class="parent-name" style="font-size:24rpx;color:rgba(255,255,255,0.9);font-weight:500;">{{data.userinfo.referral_detail.nickname}}</text></view></block></view><block wx:if="{{params.toggleidentity==1}}"><view class="qhsf" data-url="{{params.hrefurl}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">切换身份</view></block><block wx:if="{{params.ktnumshow=='1'}}"><view class="ktnum" style="display:flex;position:absolute;right:30rpx;top:30%;color:#ffff;">开团次数：<text style="font-size:24rpx;line-height:40rpx;">{{data.userinfo.ktnum}}</text></view></block></view><block wx:if="{{params.cardUpgradeShow==1&&data.userlevel&&data.userlevel.name}}"><view class="dp-userinfo-vipcard"><view class="vipcard-container" style="{{'background:'+($root.m37)+';'+('border-radius:'+('16rpx')+';')+('position:'+('relative')+';')+('min-height:'+('180rpx')+';')+('overflow:'+('hidden')+';')}}"><block wx:if="{{data.userlevel.card_main_cover}}"><image class="vipcard-main-cover" src="{{data.userlevel.card_main_cover}}" mode="aspectFill"></image></block><block wx:if="{{data.userlevel.card_sub_cover}}"><image class="vipcard-sub-cover" src="{{data.userlevel.card_sub_cover}}" mode="aspectFit"></image></block></view></view></block><block wx:if="{{params.moneyshow==1||params.scoreshow==1||params.couponshow==1}}"><view class="custom_field"><block wx:if="{{params.moneyshow==1}}"><view class="item" data-money-type="money" data-default-url="/pagesExb/money/recharge" data-event-opts="{{[['tap',[['gotoMoneyItem',['$event']]]]]}}" bindtap="__e"><text class="t2">{{$root.m38}}</text><text class="t1">{{$root.m39}}</text></view></block><block wx:if="{{params.commissionshow==1&&data.userlevel&&data.userlevel.can_agent>0}}"><view class="item" data-money-type="commission" data-default-url="/pages/commission/index" data-event-opts="{{[['tap',[['gotoMoneyItem',['$event']]]]]}}" bindtap="__e"><text class="t2">{{$root.m40}}</text><text class="t1">{{$root.m41}}</text></view></block><block wx:if="{{params.creditshow==1}}"><view class="item" data-money-type="credit" data-default-url="/pagesExa/my/creditlog" data-event-opts="{{[['tap',[['gotoMoneyItem',['$event']]]]]}}" bindtap="__e"><text class="t2">{{data.userinfo.credit||0}}</text><text class="t1">{{$root.m42}}</text></view></block><block wx:if="{{params.workshow==1}}"><view class="item" data-money-type="work" data-default-url="/pagesExt/work/mywork" data-event-opts="{{[['tap',[['gotoMoneyItem',['$event']]]]]}}" bindtap="__e"><text class="t2">{{data.userinfo.work_count||0}}</text><text class="t1">{{$root.m43}}</text></view></block><block wx:if="{{params.inviteshow==1}}"><view class="item" data-money-type="invite" data-default-url="/pagesExt/team/index" data-event-opts="{{[['tap',[['gotoMoneyItem',['$event']]]]]}}" bindtap="__e"><text class="t2">{{data.userinfo.team_count||0}}</text><text class="t1">{{$root.m44}}</text></view></block><block wx:if="{{params.scoreshow==1}}"><view class="item" data-money-type="score" data-default-url="/pagesExa/my/scorelog" data-event-opts="{{[['tap',[['gotoMoneyItem',['$event']]]]]}}" bindtap="__e"><text class="t2">{{$root.m45}}</text><text class="t1">{{$root.m46}}</text></view></block><block wx:if="{{params.couponshow==1}}"><view class="item" data-money-type="coupon" data-default-url="/pagesExb/coupon/mycoupon" data-event-opts="{{[['tap',[['gotoMoneyItem',['$event']]]]]}}" bindtap="__e"><text class="t2">{{data.userinfo.couponcount}}</text><text class="t1">{{$root.m47}}</text></view></block><block wx:if="{{params.formshow==1}}"><view class="item" data-url="/pages/form/formlog?st=1" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><text class="t2">{{data.userinfo.formcount}}</text><text class="t1">{{params.formtext}}</text></view></block></view></block><block wx:if="{{data.userinfo.othermoney_status==1}}"><view class="custom_field"><view class="item" data-url="/pagesExt/othermoney/withdraw?type=money2" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><text class="t2">{{$root.m48}}</text><text class="t1">{{$root.m49}}</text></view><view class="item" data-url="/pagesExt/othermoney/withdraw?type=money3" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><text class="t2">{{$root.m50}}</text><text class="t1">{{$root.m51}}</text></view><view class="item" data-url="/pagesExt/othermoney/withdraw?type=money4" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><text class="t2">{{$root.m52}}</text><text class="t1">{{$root.m53}}</text></view><view class="item" data-url="/pagesExt/othermoney/withdraw?type=money5" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><text class="t2">{{$root.m54}}</text><text class="t1">{{$root.m55}}</text></view><view class="item" data-url="/pagesExt/othermoney/frozen_moneylog" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><text class="t2">{{$root.m56}}</text><text class="t1">{{$root.m57}}</text></view></view></block><block wx:if="{{params.seticonshow!=='0'}}"><view class="userset" data-url="/pagesExa/my/set" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image class="img" src="/static/img/set.png"></image></view></block><block wx:if="{{platform=='wx'}}"><block><block wx:if="{{params.cardshow==1&&data.userinfo.card_code}}"><view class="usercard" data-card_id="{{data.userinfo.card_id}}" data-card_code="{{data.userinfo.card_code}}" data-event-opts="{{[['tap',[['opencard',['$event']]]]]}}" bindtap="__e"><image class="img" src="/static/img/ico-card2.png"></image><text class="txt">会员卡</text></view></block><block wx:if="{{params.cardshow==1&&!data.userinfo.card_code}}"><view class="usercard" data-card_id="{{data.card_id}}" data-event-opts="{{[['tap',[['addmembercard',['$event']]]]]}}" bindtap="__e"><image class="img" src="/static/img/ico-card2.png"></image><text class="txt">会员卡</text></view></block></block></block><block wx:if="{{platform=='mp'}}"><block><block wx:if="{{params.cardshow==1&&data.userinfo.card_code}}"><view class="usercard" data-card_id="{{data.userinfo.card_id}}" data-card_code="{{data.userinfo.card_code}}" data-event-opts="{{[['tap',[['opencard',['$event']]]]]}}" bindtap="__e"><image class="img" src="/static/img/ico-card2.png"></image><text class="txt">会员卡</text></view></block><block wx:if="{{params.cardshow==1&&!data.userinfo.card_code}}"><view class="usercard" data-url="{{data.card_returl}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image class="img" src="/static/img/ico-card2.png"></image><text class="txt">会员卡</text></view></block></block></block></view></block></view></block><block wx:if="{{params.style==3}}"><view style="{{('background: linear-gradient(to bottom, '+$root.m58+' 0%, rgba('+$root.m59+',0.1) 100%);padding-bottom:'+(params.cardUpgradeShow==1?'0rpx':'1rpx')+';')}}"><view class="dp-userinfo3" style="{{'background:'+(params.bgimg?'url('+params.bgimg+') no-repeat':'#fff')+';'+('background-size:'+(params.bgimg?'100% auto':'auto')+';')+('margin:'+(params.margin_y*2.2+'rpx '+params.margin_x*2.2+'rpx')+';')+('padding:'+(params.padding_y*2.2+'rpx '+params.padding_x*2.2+'rpx')+';')}}"><block wx:if="{{params.seticonshow!=='0'}}"><view class="userset" data-url="/pagesExa/my/set" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image class="img" src="/static/img/set.png"></image></view></block><block wx:if="{{platform=='wx'}}"><block><block wx:if="{{params.cardshow==1&&data.userinfo.card_code}}"><view class="usercard style3card" data-card_id="{{data.userinfo.card_id}}" data-card_code="{{data.userinfo.card_code}}" data-event-opts="{{[['tap',[['opencard',['$event']]]]]}}" bindtap="__e"><image class="img" src="/static/img/ico-card2.png"></image><text class="txt">会员卡</text></view></block><block wx:if="{{params.cardshow==1&&!data.userinfo.card_code}}"><view class="usercard style3card" data-card_id="{{data.card_id}}" data-event-opts="{{[['tap',[['addmembercard',['$event']]]]]}}" bindtap="__e"><image class="img" src="/static/img/ico-card2.png"></image><text class="txt">会员卡</text></view></block></block></block><block wx:if="{{platform=='mp'}}"><block><block wx:if="{{params.cardshow==1&&data.userinfo.card_code}}"><view class="usercard style3card" data-card_id="{{data.userinfo.card_id}}" data-card_code="{{data.userinfo.card_code}}" data-event-opts="{{[['tap',[['opencard',['$event']]]]]}}" bindtap="__e"><image class="img" src="/static/img/ico-card2.png"></image><text class="txt">会员卡</text></view></block><block wx:if="{{params.cardshow==1&&!data.userinfo.card_code}}"><view class="usercard style3card" data-url="{{data.card_returl}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image class="img" src="/static/img/ico-card2.png"></image><text class="txt">会员卡</text></view></block></block></block><view class="info-main"><image class="headimg" src="{{data.userinfo.headimg}}" data-event-opts="{{[['tap',[['weixinlogin',['$event']]]]]}}" bindtap="__e"></image><view class="nickname">{{data.userinfo.nickname}}</view><block wx:if="{{params.midshow=='1'&&(data.userinfo.show_user_id===undefined||data.userinfo.show_user_id==1)}}"><text class="userid">{{"(ID:"+data.userinfo.id+")"}}</text></block><view class="level-container"><block wx:if="{{params.levelshow==1&&data.userlevel&&data.userlevel.name}}"><view class="user-level" data-levelid data-event-opts="{{[['tap',[['openLevelup',['$event']]]]]}}" bindtap="__e"><block wx:if="{{data.userlevel.icon}}"><image class="level-img" src="{{data.userlevel.icon}}"></image></block><view class="level-name">{{data.userlevel.name}}</view></view></block><block wx:for="{{data.userlevelList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block wx:if="{{params.levelshow==1}}"><view class="user-level" data-levelid="{{item.id}}" data-event-opts="{{[['tap',[['openLevelup',['$event']]]]]}}" bindtap="__e"><block wx:if="{{item.icon}}"><image class="level-img" src="{{item.icon}}"></image></block><view class="level-name">{{item.name}}</view></view></block></block></view><block wx:if="{{data.userlevel&&data.userlevel.can_agent>0&&data.sysset.reg_invite_code!='0'&&data.sysset.reg_invite_code_type==1}}"><view class="invite-code">邀请码：<text user-select="true" selectable="true">{{data.userinfo.yqcode}}</text></view></block><block wx:if="{{params.parent_show==1&&data.userinfo.referral_detail&&data.userinfo.referral_detail.nickname}}"><view class="parent-simple-style3" style="margin-top:8rpx;font-size:22rpx;color:#666;"><text class="parent-label" style="font-size:22rpx;color:#999;margin-right:8rpx;">上级：</text><text class="parent-name" style="font-size:24rpx;color:#333;font-weight:500;">{{data.userinfo.referral_detail.nickname}}</text></view></block><block wx:if="{{data.zhaopin&&data.zhaopin.show_zhaopin}}"><view class="zhaopin-tags"><block wx:if="{{data.zhaopin.is_qiuzhi_renzheng&&!data.zhaopin.is_qiuzhi_qianyue}}"><text class="zhaopin-tag">认证保障中</text></block><block wx:if="{{data.zhaopin.is_qiuzhi_qianyue}}"><text class="zhaopin-tag">签约保障中</text></block><block wx:if="{{data.zhaopin.is_zhaopin_renzheng}}"><text class="zhaopin-tag">认证企业</text></block></view></block></view><view class="custom_field_grid"><block wx:if="{{params.moneyshow==1}}"><view class="item" data-money-type="money" data-default-url="/pagesExb/money/recharge" data-event-opts="{{[['tap',[['gotoMoneyItem',['$event']]]]]}}" bindtap="__e"><text class="t2">{{$root.m60}}</text><text class="t1">{{$root.m61}}</text></view></block><block wx:if="{{params.commissionshow==1&&data.userlevel&&data.userlevel.can_agent>0}}"><view class="item" data-money-type="commission" data-default-url="/activity/commission/index" data-event-opts="{{[['tap',[['gotoMoneyItem',['$event']]]]]}}" bindtap="__e"><text class="t2">{{$root.m62}}</text><text class="t1">{{$root.m63}}</text></view></block><block wx:if="{{params.scoreshow==1}}"><view class="item" data-money-type="score" data-default-url="/pagesExa/my/scorelog" data-event-opts="{{[['tap',[['gotoMoneyItem',['$event']]]]]}}" bindtap="__e"><text class="t2">{{$root.m64}}</text><text class="t1">{{$root.m65}}</text></view></block><block wx:if="{{params.couponshow==1}}"><view class="item" data-money-type="coupon" data-default-url="/pagesExb/coupon/mycoupon" data-event-opts="{{[['tap',[['gotoMoneyItem',['$event']]]]]}}" bindtap="__e"><text class="t2">{{data.userinfo.couponcount}}</text><text class="t1">{{$root.m66}}</text></view></block><block wx:if="{{params.creditshow==1}}"><view class="item" data-money-type="credit" data-default-url="/pagesExa/my/creditlog" data-event-opts="{{[['tap',[['gotoMoneyItem',['$event']]]]]}}" bindtap="__e"><text class="t2">{{data.userinfo.credit_score||0}}</text><text class="t1">{{$root.m67}}</text></view></block><block wx:if="{{params.workshow==1}}"><view class="item" data-money-type="work" data-default-url="/zhaopin/myApply" data-event-opts="{{[['tap',[['gotoMoneyItem',['$event']]]]]}}" bindtap="__e"><text class="t2">{{data.userinfo.job_count||0}}</text><text class="t1">{{$root.m68}}</text></view></block><block wx:if="{{params.inviteshow==1}}"><view class="item" data-money-type="invite" data-default-url="/activity/commission/myteam" data-event-opts="{{[['tap',[['gotoMoneyItem',['$event']]]]]}}" bindtap="__e"><text class="t2">{{data.userinfo.team_count||0}}</text><text class="t1">{{$root.m69}}</text></view></block><block wx:if="{{params.tuikuangshow==1}}"><view class="item"><text class="t2">{{data.userinfo.kuang_num}}</text><text class="t1">{{$root.m70}}</text></view></block><block wx:if="{{params.xianjinquanshow==1}}"><view class="item" data-money-type="xianjinquan" data-default-url="/pagesExb/money/moneylog?st=8" data-event-opts="{{[['tap',[['gotoMoneyItem',['$event']]]]]}}" bindtap="__e"><text class="t2">{{$root.m71}}</text><text class="t1">{{$root.m72}}</text></view></block><block wx:if="{{params.huangjifenshow==1}}"><view class="item" data-money-type="huangjifen" data-default-url="/pagesExb/money/moneylog?st=9" data-event-opts="{{[['tap',[['gotoMoneyItem',['$event']]]]]}}" bindtap="__e"><text class="t2">{{$root.m73}}</text><text class="t1">{{$root.m74}}</text></view></block><block wx:if="{{params.gongxianzhishow==1}}"><view class="item" data-money-type="gongxianzhi" data-default-url="/pagesExb/money/moneylog?st=10" data-event-opts="{{[['tap',[['gotoMoneyItem',['$event']]]]]}}" bindtap="__e"><text class="t2">{{$root.m75}}</text><text class="t1">{{$root.m76}}</text></view></block><block wx:if="{{params.shouyichishow==1}}"><view class="item" data-money-type="shouyichi" data-default-url="/pagesExb/money/moneylog?st=15" data-event-opts="{{[['tap',[['gotoMoneyItem',['$event']]]]]}}" bindtap="__e"><text class="t2">{{$root.m77}}</text><text class="t1">{{$root.m78}}</text></view></block><block wx:if="{{params.chuangyezhishow==1&&params.scoreshow==1}}"><view class="item" data-url="/pagesExa/my/scoreloghuang" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><text class="t2">{{$root.m79}}</text><text class="t1">{{$root.m80}}</text></view></block><block wx:if="{{params.suishijineshow==1}}"><view class="item" data-url="/pagesExt/othermoney/withdraw?type=money2" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><text class="t2" style="color:#dd0000;">{{$root.m81}}</text><text class="t1" style="color:#dd0000;">{{$root.m82}}</text></view></block><block wx:if="{{params.formshow==1}}"><view class="item" data-url="/pages/form/formlog?st=1" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><text class="t2">{{data.userinfo.formcount}}</text><text class="t1">{{params.formtext}}</text></view></block></view><block wx:if="{{data.userinfo.othermoney_status==1}}"><view class="custom_field_othermoney"><view class="item" data-url="/pagesExt/othermoney/withdraw?type=money2" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><text class="t2">{{$root.m83}}</text><text class="t1">{{$root.m84}}</text></view><view class="item" data-url="/pagesExt/othermoney/withdraw?type=money3" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><text class="t2">{{$root.m85}}</text><text class="t1">{{$root.m86}}</text></view><view class="item" data-url="/pagesExt/othermoney/withdraw?type=money4" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><text class="t2">{{$root.m87}}</text><text class="t1">{{$root.m88}}</text></view><view class="item" data-url="/pagesExt/othermoney/withdraw?type=money5" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><text class="t2">{{$root.m89}}</text><text class="t1">{{$root.m90}}</text></view><view class="item" data-url="/pagesExt/othermoney/frozen_moneylog" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><text class="t2">{{$root.m91}}</text><text class="t1">{{$root.m92}}</text></view></view></block></view></view></block><block wx:if="{{params.style==4}}"><view style="{{('background: linear-gradient(180deg, '+$root.m93+' 0%, rgba('+$root.m94+',0) 100%);padding-bottom:'+(params.cardUpgradeShow==1?'10rpx':'1rpx')+';')}}"><view class="dp-userinfo4" style="{{'background:'+(params.bgimg?'url('+params.bgimg+') no-repeat':'#fff')+';'+('background-size:'+(params.bgimg?'100% auto':'auto')+';')+('margin:'+(params.margin_y*2.2+'rpx '+params.margin_x*2.2+'rpx')+';')+('padding:'+(params.padding_y*2.2+'rpx '+params.padding_x*2.2+'rpx')+';')}}"><view class="top-bar-style4"><block wx:if="{{params.seticonshow!=='0'}}"><view class="userset" data-url="/pagesExa/my/set" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image class="img" src="/static/img/set.png"></image></view></block><block wx:if="{{platform=='wx'}}"><block><block wx:if="{{params.cardshow==1&&data.userinfo.card_code}}"><view class="usercard style4card" data-card_id="{{data.userinfo.card_id}}" data-card_code="{{data.userinfo.card_code}}" data-event-opts="{{[['tap',[['opencard',['$event']]]]]}}" bindtap="__e"><image class="img" src="/static/img/ico-card2.png"></image><text class="txt">会员卡</text></view></block><block wx:if="{{params.cardshow==1&&!data.userinfo.card_code}}"><view class="usercard style4card" data-card_id="{{data.card_id}}" data-event-opts="{{[['tap',[['addmembercard',['$event']]]]]}}" bindtap="__e"><image class="img" src="/static/img/ico-card2.png"></image><text class="txt">会员卡</text></view></block></block></block><block wx:if="{{platform=='mp'}}"><block><block wx:if="{{params.cardshow==1&&data.userinfo.card_code}}"><view class="usercard style4card" data-card_id="{{data.userinfo.card_id}}" data-card_code="{{data.userinfo.card_code}}" data-event-opts="{{[['tap',[['opencard',['$event']]]]]}}" bindtap="__e"><image class="img" src="/static/img/ico-card2.png"></image><text class="txt">会员卡</text></view></block><block wx:if="{{params.cardshow==1&&!data.userinfo.card_code}}"><view class="usercard style4card" data-url="{{data.card_returl}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image class="img" src="/static/img/ico-card2.png"></image><text class="txt">会员卡</text></view></block></block></block></view><view class="user-details-style4"><image class="headimg-style4" src="{{data.userinfo.headimg}}" data-event-opts="{{[['tap',[['weixinlogin',['$event']]]]]}}" bindtap="__e"></image><view class="text-info-style4"><view class="nickname-style4">{{data.userinfo.nickname+''}}<block wx:if="{{params.midshow=='1'&&(data.userinfo.show_user_id===undefined||data.userinfo.show_user_id==1)}}"><text class="id-style4">{{'(ID:'+data.userinfo.id+")"}}</text></block></view><view class="level-section-style4"><block wx:if="{{params.levelshow==1&&data.userlevel&&data.userlevel.name}}"><view class="user-level-style4" data-levelid data-event-opts="{{[['tap',[['openLevelup',['$event']]]]]}}" bindtap="__e"><block wx:if="{{data.userlevel.icon}}"><image class="level-img" src="{{data.userlevel.icon}}"></image></block><text class="level-name">{{data.userlevel.name}}</text></view></block><block wx:for="{{data.userlevelList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block wx:if="{{params.levelshow==1}}"><view class="user-level-style4" data-levelid="{{item.id}}" data-event-opts="{{[['tap',[['openLevelup',['$event']]]]]}}" bindtap="__e"><block wx:if="{{item.icon}}"><image class="level-img" src="{{item.icon}}"></image></block><text class="level-name">{{item.name}}</text></view></block></block></view><block wx:if="{{data.userlevel&&data.userlevel.can_agent>0&&data.sysset.reg_invite_code!='0'&&data.sysset.reg_invite_code_type==1}}"><view class="invite-code-style4">邀请码：<text user-select="true" selectable="true">{{data.userinfo.yqcode}}</text></view></block><block wx:if="{{params.parent_show==1&&data.userinfo.referral_detail&&data.userinfo.referral_detail.nickname}}"><view class="parent-simple-style4" style="margin-top:8rpx;font-size:22rpx;color:#666;"><text class="parent-label" style="font-size:22rpx;color:#999;margin-right:8rpx;">上级：</text><text class="parent-name" style="font-size:24rpx;color:#333;font-weight:500;">{{data.userinfo.referral_detail.nickname}}</text></view></block><block wx:if="{{data.zhaopin&&data.zhaopin.show_zhaopin}}"><view class="zhaopin-tags-style4"><block wx:if="{{data.zhaopin.is_qiuzhi_renzheng&&!data.zhaopin.is_qiuzhi_qianyue}}"><text class="zhaopin-tag">认证保障中</text></block><block wx:if="{{data.zhaopin.is_qiuzhi_qianyue}}"><text class="zhaopin-tag">签约保障中</text></block><block wx:if="{{data.zhaopin.is_zhaopin_renzheng}}"><text class="zhaopin-tag">认证企业</text></block></view></block></view></view><block wx:if="{{params.cardUpgradeShow==1&&data.userlevel&&data.userlevel.name}}"><view class="dp-userinfo-vipcard"><view class="vipcard-container" style="{{'background:'+($root.m95)+';'+('border-radius:'+('16rpx')+';')+('position:'+('relative')+';')+('min-height:'+('180rpx')+';')+('overflow:'+('hidden')+';')}}"><block wx:if="{{data.userlevel.card_main_cover}}"><image class="vipcard-main-cover" src="{{data.userlevel.card_main_cover}}" mode="aspectFill"></image></block><block wx:if="{{data.userlevel.card_sub_cover}}"><image class="vipcard-sub-cover" src="{{data.userlevel.card_sub_cover}}" mode="aspectFit"></image></block></view></view></block><view class="stats-grid-style4"><block wx:if="{{params.moneyshow==1}}"><view class="item" data-money-type="money" data-default-url="/pagesExb/money/recharge" data-event-opts="{{[['tap',[['gotoMoneyItem',['$event']]]]]}}" bindtap="__e"><text class="t2">{{$root.m96}}</text><text class="t1">{{$root.m97}}</text></view></block><block wx:if="{{params.scoreshow==1}}"><view class="item" data-money-type="score" data-default-url="/pagesExa/my/scorelog" data-event-opts="{{[['tap',[['gotoMoneyItem',['$event']]]]]}}" bindtap="__e"><text class="t2">{{$root.m98}}</text><text class="t1">{{$root.m99}}</text></view></block><block wx:if="{{params.couponshow==1}}"><view class="item" data-money-type="coupon" data-default-url="/pagesExb/coupon/mycoupon" data-event-opts="{{[['tap',[['gotoMoneyItem',['$event']]]]]}}" bindtap="__e"><text class="t2">{{data.userinfo.couponcount}}</text><text class="t1">{{$root.m100}}</text></view></block><block wx:if="{{params.commissionshow==1&&data.userlevel&&data.userlevel.can_agent>0}}"><view class="item" data-money-type="commission" data-default-url="/activity/commission/index" data-event-opts="{{[['tap',[['gotoMoneyItem',['$event']]]]]}}" bindtap="__e"><text class="t2">{{$root.m101}}</text><text class="t1">{{$root.m102}}</text></view></block><block wx:if="{{params.creditshow==1}}"><view class="item" data-money-type="credit" data-default-url="/pagesExa/my/creditlog" data-event-opts="{{[['tap',[['gotoMoneyItem',['$event']]]]]}}" bindtap="__e"><text class="t2">{{data.userinfo.credit_score||0}}</text><text class="t1">{{$root.m103}}</text></view></block><block wx:if="{{params.workshow==1}}"><view class="item" data-money-type="work" data-default-url="/zhaopin/myApply" data-event-opts="{{[['tap',[['gotoMoneyItem',['$event']]]]]}}" bindtap="__e"><text class="t2">{{data.userinfo.job_count||0}}</text><text class="t1">{{$root.m104}}</text></view></block><block wx:if="{{params.inviteshow==1}}"><view class="item" data-money-type="invite" data-default-url="/activity/commission/myteam" data-event-opts="{{[['tap',[['gotoMoneyItem',['$event']]]]]}}" bindtap="__e"><text class="t2">{{data.userinfo.team_count||0}}</text><text class="t1">{{$root.m105}}</text></view></block><block wx:if="{{params.tuikuangshow==1}}"><view class="item"><text class="t2">{{data.userinfo.kuang_num}}</text><text class="t1">{{$root.m106}}</text></view></block><block wx:if="{{params.xianjinquanshow==1}}"><view class="item" data-money-type="xianjinquan" data-default-url="/pagesExb/money/moneylog?st=8" data-event-opts="{{[['tap',[['gotoMoneyItem',['$event']]]]]}}" bindtap="__e"><text class="t2">{{$root.m107}}</text><text class="t1">{{$root.m108}}</text></view></block><block wx:if="{{params.huangjifenshow==1}}"><view class="item" data-money-type="huangjifen" data-default-url="/pagesExb/money/moneylog?st=9" data-event-opts="{{[['tap',[['gotoMoneyItem',['$event']]]]]}}" bindtap="__e"><text class="t2">{{$root.m109}}</text><text class="t1">{{$root.m110}}</text></view></block><block wx:if="{{params.gongxianzhishow==1}}"><view class="item" data-money-type="gongxianzhi" data-default-url="/pagesExb/money/moneylog?st=10" data-event-opts="{{[['tap',[['gotoMoneyItem',['$event']]]]]}}" bindtap="__e"><text class="t2">{{$root.m111}}</text><text class="t1">{{$root.m112}}</text></view></block><block wx:if="{{params.shouyichishow==1}}"><view class="item" data-money-type="shouyichi" data-default-url="/pagesExb/money/moneylog?st=15" data-event-opts="{{[['tap',[['gotoMoneyItem',['$event']]]]]}}" bindtap="__e"><text class="t2">{{$root.m113}}</text><text class="t1">{{$root.m114}}</text></view></block><block wx:if="{{params.chuangyezhishow==1&&params.scoreshow==1}}"><view class="item" data-url="/pagesExa/my/scoreloghuang" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><text class="t2">{{$root.m115}}</text><text class="t1">{{$root.m116}}</text></view></block><block wx:if="{{params.suishijineshow==1}}"><view class="item" data-url="/pagesExt/othermoney/withdraw?type=money2" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><text class="t2" style="color:#dd0000;">{{$root.m117}}</text><text class="t1" style="color:#dd0000;">{{$root.m118}}</text></view></block><block wx:if="{{params.formshow==1}}"><view class="item" data-url="/pages/form/formlog?st=1" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><text class="t2">{{data.userinfo.formcount}}</text><text class="t1">{{params.formtext}}</text></view></block></view><block wx:if="{{data.userinfo.othermoney_status==1}}"><view class="othermoney-section-style4"><view class="item" data-url="/pagesExt/othermoney/withdraw?type=money2" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><text class="t2">{{$root.m119}}</text><text class="t1">{{$root.m120}}</text></view><view class="item" data-url="/pagesExt/othermoney/withdraw?type=money3" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><text class="t2">{{$root.m121}}</text><text class="t1">{{$root.m122}}</text></view><view class="item" data-url="/pagesExt/othermoney/withdraw?type=money4" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><text class="t2">{{$root.m123}}</text><text class="t1">{{$root.m124}}</text></view><view class="item" data-url="/pagesExt/othermoney/withdraw?type=money5" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><text class="t2">{{$root.m125}}</text><text class="t1">{{$root.m126}}</text></view><view class="item" data-url="/pagesExt/othermoney/frozen_moneylog" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><text class="t2">{{$root.m127}}</text><text class="t1">{{$root.m128}}</text></view></view></block></view></view></block><block wx:if="{{params.style==5}}"><view style="{{('background: linear-gradient(to bottom, '+$root.m129+' 0%, rgba('+$root.m130+',0.05) 100%);padding-bottom:'+(params.cardUpgradeShow==1?'0rpx':'1rpx')+';')}}"><view class="dp-userinfo5" style="{{'background:'+(params.bgimg?'url('+params.bgimg+') no-repeat':'#fff')+';'+('background-size:'+(params.bgimg?'100% auto':'cover')+';')+('margin:'+(params.margin_y*2.2+'rpx '+params.margin_x*2.2+'rpx')+';')+('padding:'+(params.padding_y*2.2+'rpx '+params.padding_x*2.2+'rpx '+30+'rpx '+params.padding_y*2.2+'rpx ')+';')}}"><view class="top-actions-style5"><block wx:if="{{params.seticonshow!=='0'}}"><view class="userset" data-url="/pagesExa/my/set" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image class="img" src="/static/img/set.png"></image></view></block><block wx:if="{{platform=='wx'}}"><block><block wx:if="{{params.cardshow==1&&data.userinfo.card_code}}"><view class="usercard style5card" data-card_id="{{data.userinfo.card_id}}" data-card_code="{{data.userinfo.card_code}}" data-event-opts="{{[['tap',[['opencard',['$event']]]]]}}" bindtap="__e"><image class="img" src="/static/img/ico-card2.png"></image><text class="txt">会员卡</text></view></block><block wx:if="{{params.cardshow==1&&!data.userinfo.card_code}}"><view class="usercard style5card" data-card_id="{{data.card_id}}" data-event-opts="{{[['tap',[['addmembercard',['$event']]]]]}}" bindtap="__e"><image class="img" src="/static/img/ico-card2.png"></image><text class="txt">会员卡</text></view></block></block></block><block wx:if="{{platform=='mp'}}"><block><block wx:if="{{params.cardshow==1&&data.userinfo.card_code}}"><view class="usercard style5card" data-card_id="{{data.userinfo.card_id}}" data-card_code="{{data.userinfo.card_code}}" data-event-opts="{{[['tap',[['opencard',['$event']]]]]}}" bindtap="__e"><image class="img" src="/static/img/ico-card2.png"></image><text class="txt">会员卡</text></view></block><block wx:if="{{params.cardshow==1&&!data.userinfo.card_code}}"><view class="usercard style5card" data-url="{{data.card_returl}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image class="img" src="/static/img/ico-card2.png"></image><text class="txt">会员卡</text></view></block></block></block></view><view class="profile-main-style5"><image class="avatar-style5" src="{{data.userinfo.headimg}}" data-event-opts="{{[['tap',[['weixinlogin',['$event']]]]]}}" bindtap="__e"></image><view class="profile-content-style5"><view class="name-container-style5"><view class="name-id-wrapper"><text class="name-style5">{{data.userinfo.nickname}}</text><block wx:if="{{params.midshow=='1'&&(data.userinfo.show_user_id===undefined||data.userinfo.show_user_id==1)}}"><text class="id-style5">{{"（ID: "+data.userinfo.id+"）"}}</text></block></view></view><view class="badges-container-style5"><block wx:for="{{data.userlevelList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block wx:if="{{params.levelshow==1}}"><view class="user-level-style5" data-levelid="{{item.id}}" data-event-opts="{{[['tap',[['openLevelup',['$event']]]]]}}" bindtap="__e"><block wx:if="{{item.icon}}"><image class="level-img" src="{{item.icon}}"></image></block><text class="level-name">{{item.name}}</text></view></block></block><block wx:if="{{data.zhaopin&&data.zhaopin.show_zhaopin}}"><view class="cert-badges-style5"><block wx:if="{{data.zhaopin.is_qiuzhi_renzheng&&!data.zhaopin.is_qiuzhi_qianyue}}"><text class="cert-badge">认证保障中</text></block><block wx:if="{{data.zhaopin.is_qiuzhi_qianyue}}"><text class="cert-badge">签约保障中</text></block><block wx:if="{{data.zhaopin.is_zhaopin_renzheng}}"><text class="cert-badge">认证企业</text></block></view></block></view><block wx:if="{{data.userlevel&&data.userlevel.can_agent>0&&data.sysset.reg_invite_code!='0'&&data.sysset.reg_invite_code_type==1}}"><view class="invite-code-style5"><text class="invite-label">邀请码：</text><text class="invite-value" user-select="true" selectable="true">{{data.userinfo.yqcode}}</text></view></block><block wx:if="{{params.parent_show==1&&data.userinfo.referral_detail&&data.userinfo.referral_detail.nickname}}"><view class="parent-simple-style5" style="margin-top:8rpx;font-size:22rpx;color:#666;"><text class="parent-label" style="font-size:22rpx;color:#999;margin-right:8rpx;">上级：</text><text class="parent-name" style="font-size:24rpx;color:#333;font-weight:500;">{{data.userinfo.referral_detail.nickname}}</text></view></block></view></view><block wx:if="{{params.cardUpgradeShow==1&&data.userlevel&&data.userlevel.name}}"><view class="dp-userinfo-vipcard"><view class="vipcard-container" style="{{'background:'+($root.m131)+';'+('border-radius:'+('16rpx')+';')+('position:'+('relative')+';')+('min-height:'+('180rpx')+';')+('overflow:'+('hidden')+';')}}"><block wx:if="{{data.userlevel.card_main_cover}}"><image class="vipcard-main-cover" src="{{data.userlevel.card_main_cover}}" mode="aspectFill"></image></block><block wx:if="{{data.userlevel.card_sub_cover}}"><image class="vipcard-sub-cover" src="{{data.userlevel.card_sub_cover}}" mode="aspectFit"></image></block></view></view></block></view></view></block><block wx:if="{{$root.m132}}"><view class="dp-userinfo-referral" style="{{'margin:'+(params.padding_y*2.2+'rpx '+params.padding_x*2.2+'rpx')+';'+('margin-top:'+(params.style==2?'-100rpx':'0')+';')+('background:'+('#fff')+';')+('border-radius:'+('16rpx')+';')+('margin-bottom:'+('20rpx')+';')}}"><view class="referral-info"><view class="referral-avatar"><image class="avatar-img" src="{{data.userinfo.referral_avatar||data.sysset.logo}}"></image><view class="referral-tag" style="{{('background: rgba('+$root.m133+',100%);')}}">{{$root.m134}}</view></view><view class="referral-content flex1"><view class="referral-name">{{$root.m135}}</view></view><view data-event-opts="{{[['tap',[['viewReferralInfo',['$event']]]]]}}" class="referral-action" bindtap="__e"><text class="action-text">{{$root.m136}}</text><image class="action-icon" src="/static/img/arrowright.png"></image></view></view><block wx:if="{{params.referral_team_show==1||params.referral_support_show==1}}"><view class="referral-internal-actions" style="{{'background-color:'+(params.referral_actions_bgcolor||'#f9f9f9')+';'+('border-bottom-left-radius:'+(params.referral_actions_radius+'px')+';')+('border-bottom-right-radius:'+(params.referral_actions_radius+'px')+';')}}"><block wx:if="{{params.referral_team_show==1}}"><view data-event-opts="{{[['tap',[['showTeamInfoPopup',['$event']]]]]}}" class="action-item" bindtap="__e"><image class="action-item-icon" src="{{params.referral_team_icon||'/static/img/dsn_team_icon.png'}}"></image><text class="action-item-text">{{$root.m137}}</text></view></block><block wx:if="{{params.referral_support_show==1}}"><view data-event-opts="{{[['tap',[['handleLink',['$0','$1'],['params.referral_support_hrefurl','params.referral_support_link_name']]]]]}}" class="action-item" bindtap="__e"><image class="action-item-icon" src="{{params.referral_support_icon||'/static/img/dsn_support_icon.png'}}"></image><text class="action-item-text">{{$root.m138}}</text></view></block></view></block></view></block><block wx:if="{{params.ordershow==1}}"><view class="dp-userinfo-order" style="{{'margin:'+(params.padding_y*2.2+'rpx '+params.padding_x*2.2+'rpx')+';'+('margin-top:'+(params.style==2?'-100rpx':'0')+';')}}"><view class="head"><text class="f1">我的订单</text><view class="f2" data-url="/pagesExt/order/orderlist" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><text>查看全部订单</text><image class="image" src="/static/img/arrowright.png"></image></view></view><view class="content"><view class="item" data-url="/pagesExt/order/orderlist?st=0" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><text class="iconfont icondaifukuan" style="{{'color:'+($root.m139)+';'}}"></text><block wx:if="{{data.orderinfo.count0>0}}"><view class="t2">{{data.orderinfo.count0}}</view></block><text class="t3">待付款</text></view><view class="item" data-url="/pagesExt/order/orderlist?st=1" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><text class="iconfont icondaifahuo" style="{{'color:'+($root.m140)+';'}}"></text><block wx:if="{{data.orderinfo.count1>0}}"><view class="t2">{{data.orderinfo.count1}}</view></block><text class="t3">待发货</text></view><view class="item" data-url="/pagesExt/order/orderlist?st=2" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><text class="iconfont icondaishouhuo" style="{{'color:'+($root.m141)+';'}}"></text><block wx:if="{{data.orderinfo.count2>0}}"><view class="t2">{{data.orderinfo.count2}}</view></block><text class="t3">待收货</text></view><view class="item" data-url="/pagesExt/order/orderlist?st=3" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><text class="iconfont iconyiwancheng" style="{{'color:'+($root.m142)+';'}}"></text><block wx:if="{{data.orderinfo.count3>0}}"><view class="t2">{{data.orderinfo.count3}}</view></block><text class="t3">已完成</text></view><view class="item" data-url="/pagesExt/order/refundlist" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><text class="iconfont icontuikuandingdan" style="{{'color:'+($root.m143)+';'}}"></text><block wx:if="{{data.orderinfo.count4>0}}"><view class="t2">{{data.orderinfo.count4}}</view></block><text class="t3">退款/售后</text></view></view></view></block></view>