<view class="container"><block wx:if="{{isload}}"><block><view class="total-profit"><text>{{"总利润：￥"+lirun+" 元"}}</text></view><view class="order-content"><block wx:for="{{datalist}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><view class="order-box"><view class="order-head"><view class="profit-info"><text class="profit-text">{{"商品名称："+item.name}}</text></view></view><view class="order-content-details"><image class="product-image" src="{{item.pic}}"></image><view class="detail-info"><text class="detail-name">{{item.name}}</text><text class="detail-profit">{{"订单利润：￥"+item.lirun}}</text></view></view><view class="order-operation"><block wx:if="{{item.status2==0}}"><view class="btn-entrust" data-url="{{'buyweituo?id='+item.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e">委托上架</view></block></view></view></block></block></view><block wx:if="{{nomore}}"><nomore vue-id="5b4fed9c-1" bind:__l="__l"></nomore></block><block wx:if="{{nodata}}"><nodata vue-id="5b4fed9c-2" bind:__l="__l"></nodata></block></block></block><block wx:if="{{loading}}"><loading vue-id="5b4fed9c-3" bind:__l="__l"></loading></block><dp-tabbar vue-id="5b4fed9c-4" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="5b4fed9c-5" data-ref="popmsg" bind:__l="__l"></popmsg></view>