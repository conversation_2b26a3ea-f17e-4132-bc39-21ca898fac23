<view><block wx:if="{{isload}}"><view><view data-event-opts="{{[['tap',[['buydialogChange',['$event']]]]]}}" class="buydialog-mask" bindtap="__e"></view><view class="{{['buydialog',menuindex>-1?'tabbarbot':'notabbarbot']}}"><view data-event-opts="{{[['tap',[['buydialogChange',['$event']]]]]}}" class="close" bindtap="__e"><image class="image" src="/static/img/close.png"></image></view><view class="title"><image class="img" src="{{product.pic}}" data-url="{{product.pic}}" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image><view class="price" style="{{'color:'+($root.m0)+';'}}">{{"￥"+product.sell_price+''}}<block wx:if="{{product.market_price*1>product.sell_price*1}}"><text class="t2">{{"￥"+product.market_price}}</text></block></view><view class="stock">{{"库存："+product.stock}}</view></view><view class="buynum flex flex-y-center"><view class="flex1">购买数量：</view><view class="addnum"><view data-event-opts="{{[['tap',[['gwcminus',['$event']]]]]}}" class="minus" bindtap="__e"><image class="img" src="/static/img/cart-minus.png"></image></view><input class="input" type="number" data-event-opts="{{[['input',[['gwcinput',['$event']]]]]}}" value="{{gwcnum}}" bindinput="__e"/><view data-event-opts="{{[['tap',[['gwcplus',['$event']]]]]}}" class="plus" bindtap="__e"><image class="img" src="/static/img/cart-plus.png"></image></view></view></view><view class="op"><block wx:if="{{product.stock<=0}}"><block><button class="nostock">库存不足</button></block></block><block wx:else><block><block wx:if="{{btntype==0}}"><button data-event-opts="{{[['tap',[['tobuy',['$event']]]]]}}" class="tobuy" style="{{'background-color:'+($root.m1)+';'}}" bindtap="__e">立即购买</button></block><block wx:if="{{btntype==2}}"><button data-event-opts="{{[['tap',[['tobuy',['$event']]]]]}}" class="tobuy" style="{{'background-color:'+($root.m2)+';'}}" bindtap="__e">确 定</button></block></block></block></view></view></view></block><block wx:if="{{loading}}"><loading vue-id="5c3783c0-1" bind:__l="__l"></loading></block></view>