<view class="container"><block wx:if="{{isload}}"><block><form data-event-opts="{{[['submit',[['formSubmit',['$event']]]]]}}" bindsubmit="__e"><view class="mymoney" style="{{'background:'+($root.m0)+';'}}"><view class="f1">可用余额</view><view class="f2"><text style="font-size:26rpx;"></text>{{userinfo.money}}</view><view class="f3" data-url="/pages/money/moneylog" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><text>余额明细</text><text class="iconfont iconjiantou" style="font-size:20rpx;"></text></view></view><button data-event-opts="{{[['tap',[['formSubmit1',['$event']]]]]}}" class="btn" style="{{'background:'+($root.m1)+';'}}" bindtap="__e">兌換主賬號</button><view class="content"><view class="info-item"><view>兌換賬號信息</view></view><view class="info-item"><view class="t1">賬號id</view><view class="t2">帳號</view><view class="t2">佣金</view><view class="t2">余额</view></view><block wx:for="{{datalist}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="info-item"><view class="t1">{{item.id}}</view><block wx:if="{{item.zi==1}}"><view class="t2">{{item.tel}}</view></block><block wx:if="{{item.zi==2}}"><view class="t2">{{item.tel}}</view></block><view class="t2">{{item.yongjin}}</view><view class="t2">{{item.jine}}</view></view></block></view></form></block></block><block wx:if="{{loading}}"><loading vue-id="931fde84-1" bind:__l="__l"></loading></block><dp-tabbar vue-id="931fde84-2" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="931fde84-3" data-ref="popmsg" bind:__l="__l"></popmsg></view>