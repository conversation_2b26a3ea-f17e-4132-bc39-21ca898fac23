(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["activity/kecheng/category3"],{1123:function(t,e,n){"use strict";n.r(e);var i=n("4eaa"),a=n("9f8e");for(var o in a)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(o);n("2ee5");var d=n("828b"),c=Object(d["a"])(a["default"],i["b"],i["c"],!1,null,null,null,!1,i["a"],void 0);e["default"]=c.exports},"2ee5":function(t,e,n){"use strict";var i=n("fb2b"),a=n.n(i);a.a},3412:function(t,e,n){"use strict";(function(t,e){var i=n("47a9");n("06e9");i(n("3240"));var a=i(n("1123"));t.__webpack_require_UNI_MP_PLUGIN__=n,e(a.default)}).call(this,n("3223")["default"],n("df3c")["createPage"])},"3f42":function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var i=getApp(),a={data:function(){return{opt:{},loading:!1,isload:!1,menuindex:-1,data:[],currentActiveIndex:0,animation:!0,level2List:[],currentLevel2Index:0,clist:"",bid:"",test:""}},onLoad:function(t){this.opt=i.getopts(t),this.bid=this.opt.bid?this.opt.bid:"",this.getdata()},onPullDownRefresh:function(){this.getdata()},methods:{getdata:function(){var t=this;t.loading=!0,i.get("ApiKecheng/category3",{bid:t.bid},(function(e){t.loading=!1,t.data=e.data,t.loaded(),e.data.length>0&&t.getdownclist3(e.data[0].id)}))},getdownclist3:function(t){var e=this;e.loading=!0,e.nodata=!1,i.post("ApiKecheng/getdownclist3",{id:t,bid:e.bid},(function(t){e.loading=!1,e.level2List=t.data,0==e.level2List.length?e.nodata=!0:(e.currentLevel2Index=0,e.level2List.length>0&&e.getLevel3List(e.level2List[0].id))}))},clickRootItem:function(t){var e=t.currentTarget.dataset;this.currentActiveIndex=e.rootItemIndex;var n=e.rootItemId;this.getdownclist3(n)},getLevel3List:function(t){var e=this;e.loading=!0,e.nodata=!1,i.post("ApiKecheng/getdownclist3",{id:t,bid:e.bid},(function(t){e.loading=!1,e.clist=t.data,0==e.clist.length&&(e.nodata=!0)}))},clickLevel2Item:function(t){var e=t.currentTarget.dataset;this.currentLevel2Index=e.itemIndex;var n=e.itemId;this.getLevel3List(n)},gotoCatproductPage:function(t){var e=t.currentTarget.dataset;i.goto("/activity/kecheng/list?cid="+e.id)},hideshow:function(t){var e=t.currentTarget.dataset.key;1==this.clist[e].ishide?this.clist[e].ishide=0:this.clist[e].ishide=1,this.test=Math.random()}}};e.default=a},"4eaa":function(t,e,n){"use strict";n.d(e,"b",(function(){return a})),n.d(e,"c",(function(){return o})),n.d(e,"a",(function(){return i}));var i={nodata:function(){return n.e("components/nodata/nodata").then(n.bind(null,"101c"))},loading:function(){return n.e("components/loading/loading").then(n.bind(null,"ceaa"))},dpTabbar:function(){return n.e("components/dp-tabbar/dp-tabbar").then(n.bind(null,"b875"))},popmsg:function(){return n.e("components/popmsg/popmsg").then(n.bind(null,"2bf2"))}},a=function(){var t=this,e=t.$createElement,n=(t._self._c,t.isload?t.__map(t.level2List,(function(e,n){var i=t.__get_orig(e),a=t.t("color1");return{$orig:i,m0:a}})):null);t.$mp.data=Object.assign({},{$root:{l0:n}})},o=[]},"9f8e":function(t,e,n){"use strict";n.r(e);var i=n("3f42"),a=n.n(i);for(var o in i)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(o);e["default"]=a.a},fb2b:function(t,e,n){}},[["3412","common/runtime","common/vendor"]]]);