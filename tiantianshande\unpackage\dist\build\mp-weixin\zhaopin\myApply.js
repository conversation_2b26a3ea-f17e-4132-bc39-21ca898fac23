(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["zhaopin/myApply"],{"0810":function(t,e,a){"use strict";(function(t){var n=a("47a9");Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var o=n(a("7eb4")),i=n(a("ee10")),r=n(a("af34")),s=getApp(),l={data:function(){return{opt:{},loading:!1,isload:!1,currentTab:"all",swiperCurrent:0,applyJobs:[],isLoading:!1,isRefreshing:!1,noMoreData:!1,page:1,pageSize:10,pre_url:s.globalData.pre_url,statusList:["all","1","2","3","4","5","6","7"]}},onLoad:function(t){this.opt=s.getopts(t),this.loadApplyJobs()},onPullDownRefresh:function(){this.onRefresh()},onShareAppMessage:function(){return this._sharewx({title:"我投递的工作",desc:"查看投递记录",pic:""})},onShareTimeline:function(){var t=this._sharewx({title:"我投递的工作",desc:"查看投递记录",pic:""}),e=t.path.split("?")[1];return{title:t.title,imageUrl:t.imageUrl,query:e}},onReachBottom:function(){this.noMoreData||this.isLoading||this.loadMore()},methods:{switchTab:function(t){this.currentTab!==t&&(this.currentTab=t,this.swiperCurrent=this.statusList.indexOf(t),this.page=1,this.noMoreData=!1,this.applyJobs=[],this.loadApplyJobs())},swiperChange:function(t){var e=t.detail.current,a=this.statusList[e];this.currentTab!==a&&(this.currentTab=a,this.page=1,this.noMoreData=!1,this.applyJobs=[],this.loadApplyJobs())},getStatusText:function(t,e){return e||"已投递"},loadApplyJobs:function(){var e=this;if(!this.isLoading&&!this.noMoreData){this.isLoading=!0,this.loading=!0;var a={page:this.page,limit:this.pageSize};"all"!==this.currentTab&&(a.status=parseInt(this.currentTab)),s.get("apiZhaopin/getApplyList",a,(function(a){if(e.loading=!1,e.isLoading=!1,e.isRefreshing=!1,t.stopPullDownRefresh(),0===a.code){var n=a.data.map((function(t){var e=[];if(t.options)try{var a=JSON.parse(t.options);Object.values(a).forEach((function(t){Array.isArray(t)&&e.push.apply(e,(0,r.default)(t))}))}catch(n){console.error("解析options失败:",n)}return t.education&&e.push(t.education),t.experience&&e.push(t.experience),t.address&&e.push(t.address),{id:t.position_id,title:t.position_name||t.title,salary:t.salary,companyName:t.company_name,companyLogo:t.company_logo,tags:e,applyTime:t.create_time,status:t.status,statusText:t.status_text}}));1===e.page?e.applyJobs=n:e.applyJobs=[].concat((0,r.default)(e.applyJobs),(0,r.default)(n)),e.noMoreData=n.length<e.pageSize||e.applyJobs.length>=a.count,e.page++,e.isload=!0}else t.showToast({title:a.msg||"加载失败，请重试",icon:"none"})}))}},onRefresh:function(){var t=this;return(0,i.default)(o.default.mark((function e(){return o.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t.isRefreshing=!0,t.page=1,t.noMoreData=!1,e.next=5,t.loadApplyJobs();case 5:case"end":return e.stop()}}),e)})))()},loadMore:function(){this.loadApplyJobs()},viewJobDetail:function(e){t.navigateTo({url:"/zhaopin/partdetails?id=".concat(e)})},goToJobList:function(){t.switchTab({url:"/zhaopin/index"})}}};e.default=l}).call(this,a("df3c")["default"])},"18ea":function(t,e,a){},"594c1":function(t,e,a){"use strict";a.r(e);var n=a("0810"),o=a.n(n);for(var i in n)["default"].indexOf(i)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(i);e["default"]=o.a},"5f6b":function(t,e,a){"use strict";a.r(e);var n=a("656c"),o=a("594c1");for(var i in o)["default"].indexOf(i)<0&&function(t){a.d(e,t,(function(){return o[t]}))}(i);a("b83a");var r=a("828b"),s=Object(r["a"])(o["default"],n["b"],n["c"],!1,null,null,null,!1,n["a"],void 0);e["default"]=s.exports},"656c":function(t,e,a){"use strict";a.d(e,"b",(function(){return o})),a.d(e,"c",(function(){return i})),a.d(e,"a",(function(){return n}));var n={loading:function(){return a.e("components/loading/loading").then(a.bind(null,"ceaa"))},dpTabbar:function(){return a.e("components/dp-tabbar/dp-tabbar").then(a.bind(null,"b875"))},popmsg:function(){return a.e("components/popmsg/popmsg").then(a.bind(null,"2bf2"))}},o=function(){var t=this,e=t.$createElement,a=(t._self._c,t.isload?t.__map(t.statusList,(function(e,a){var n=t.__get_orig(e),o=t.currentTab===e?t.t("color1"):null,i=t.currentTab===e?t.t("color1"):null,r=t.currentTab===e?t.t("color1rgb"):null,s=t.currentTab===e?t.t("color1rgb"):null;return{$orig:n,m0:o,m1:i,m2:r,m3:s}})):null),n=t.isload?t.__map(["all","1","2","3","4","5","6","7"],(function(e,a){var n=t.__get_orig(e),o=t.applyJobs.length,i=o>0?t.__map(t.applyJobs,(function(e,a){var n=t.__get_orig(e),o=t.getStatusText(e.status,e.statusText);return{$orig:n,m4:o}})):null,r=o>0?null:t.t("color1"),s=o>0?null:t.t("color1"),l=o>0?null:t.t("color1rgb"),u=t.isLoading&&t.applyJobs.length>0,c=t.noMoreData&&t.applyJobs.length>0;return{$orig:n,g0:o,l1:i,m5:r,m6:s,m7:l,g1:u,g2:c}})):null;t._isMounted||(t.e0=function(e,a){var n=arguments[arguments.length-1].currentTarget.dataset,o=n.eventParams||n["event-params"];a=o.job;return t.viewJobDetail(a.id)}),t.$mp.data=Object.assign({},{$root:{l0:a,l2:n}})},i=[]},b83a:function(t,e,a){"use strict";var n=a("18ea"),o=a.n(n);o.a},e822:function(t,e,a){"use strict";(function(t,e){var n=a("47a9");a("06e9");n(a("3240"));var o=n(a("5f6b"));t.__webpack_require_UNI_MP_PLUGIN__=a,e(o.default)}).call(this,a("3223")["default"],a("df3c")["createPage"])}},[["e822","common/runtime","common/vendor"]]]);