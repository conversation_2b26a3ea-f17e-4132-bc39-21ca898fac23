<view class="waterfalls-box" style="{{'height:'+(height+'px')+';'}}"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="m0"><view class="waterfalls-list vue-ref-in-for" style="{{'--offset:'+(offset+'px')+';'+('--cols:'+(cols)+';')+('background:'+(probgcolor)+';')+('top:'+(allPositionArr[index]?allPositionArr[index].top:0)+';')+('left:'+(allPositionArr[index]?allPositionArr[index].left:0)+';')}}" id="{{'waterfalls-list-id-'+item.m2}}" data-ref="{{'waterfalls-list-id-'+item.m1}}" data-event-opts="{{[['tap',[['gotoDetail',['$0'],[[['list','getItemKey(item)',item.m3]]]]]]]}}" bindtap="__e"><block wx:if="{{!loadedItems[index]}}"><view class="loading-mask"><view class="loading-spinner"></view><text class="loading-text">加载中...</text></view></block><block wx:if="{{shiningItems[index]}}"><view class="shine-effect"></view></block><block wx:if="{{!item.$orig.content_type}}"><image class="waterfalls-list-image" style="{{(imageStyle)}}" mode="widthFix" src="{{item.$orig.pic||' '}}" data-event-opts="{{[['load',[['imageLoadHandle',[index]]]],['error',[['imageLoadHandle',[index]]]]]}}" bindload="__e" binderror="__e"></image><block wx:if="{{saleimg!=''}}"><image class="saleimg" src="{{saleimg}}" mode="widthFix"></image></block><view class="product-info"><block wx:if="{{showname==1}}"><view class="p1">{{item.$orig.name}}</view></block><block wx:if="{{showbname&&item.$orig.binfo}}"><view class="binfo flex-y-center"><image class="t1" src="{{item.$orig.binfo.logo}}"></image><text class="t2">{{item.$orig.binfo.name}}</text></view></block><view class="p2"><block wx:if="{{showprice!='0'&&(item.$orig.price_type!=1||item.$orig.sell_price>0)}}"><view class="p2-1"><text class="t1" style="color:#f5222d;"><text style="font-size:24rpx;">￥</text>{{item.$orig.sell_price}}</text><block wx:if="{{showprice=='1'&&item.$orig.market_price*1>item.$orig.sell_price*1}}"><text class="t2">{{"￥"+item.$orig.market_price}}</text></block><block wx:if="{{item.$orig.juli}}"><text class="t3" style="color:#888;">{{item.$orig.juli}}</text></block></view></block><block wx:if="{{item.$orig.xunjia_text&&item.$orig.price_type==1&&item.$orig.sell_price<=0}}"><view class="p2-1" style="height:50rpx;line-height:44rpx;"><text class="t1" style="color:#f5222d;">询价</text><block wx:if="{{item.$orig.xunjia_text&&item.$orig.price_type==1}}"><block><view class="lianxi" style="background:#f5222d;" data-lx_name="{{item.$orig.lx_name}}" data-lx_bid="{{item.$orig.lx_bid}}" data-lx_bname="{{item.$orig.lx_bname}}" data-lx_tel="{{item.$orig.lx_tel}}" data-btntype="2" data-event-opts="{{[['tap',[['showLinkChange',['$event']]]]]}}" catchtap="__e">{{''+(item.$orig.xunjia_text?item.$orig.xunjia_text:'联系TA')}}</view></block></block></view></block></view><block wx:if="{{item.$orig.merchant_name}}"><view class="p1" style="color:#666;font-size:24rpx;white-space:nowrap;text-overflow:ellipsis;margin-top:6rpx;height:30rpx;line-height:30rpx;font-weight:normal;"><text>{{item.$orig.merchant_name}}</text></view></block><block wx:if="{{item.$orig.main_business}}"><view class="p1" style="color:#666;font-size:24rpx;margin-top:4rpx;font-weight:normal;"><text>{{item.$orig.main_business}}</text></view></block><block wx:if="{{showsales=='1'&&item.$orig.sales>0||showstock=='1'}}"><view class="p3"><block wx:if="{{showsales=='1'&&item.$orig.sales>0}}"><text>{{"已售"+item.$orig.sales}}</text></block><block wx:if="{{showsales=='1'&&item.$orig.sales>0&&showstock=='1'}}"><text style="padding:0 4px;font-size:22rpx;">|</text></block><block wx:if="{{showstock=='1'}}"><text>{{"仅剩"+item.$orig.stock}}</text></block></view></block><block wx:if="{{(showsales!='1'||item.$orig.sales<=0)&&item.$orig.main_business}}"><view style="height:44rpx;"></view></block><block wx:if="{{showcart==1&&!item.$orig.price_type}}"><view class="p4" style="background:rgba(245, 34, 45, 0.1);color:#f5222d;" data-proid="{{item.$orig[idfield]}}" data-event-opts="{{[['tap',[['buydialogChange',['$event']]]]]}}" catchtap="__e"><text class="iconfont icon_gouwuche"></text></view></block><block wx:if="{{showcart==2&&!item.$orig.price_type}}"><view class="p4" style="background:rgba(245, 34, 45, 0.1);color:#f5222d;" data-proid="{{item.$orig[idfield]}}" data-event-opts="{{[['tap',[['buydialogChange',['$event']]]]]}}" catchtap="__e"><image class="img" src="{{cartimg}}"></image></view></block></view><block wx:if="{{item.g0}}"><view class="couponitem"><view class="f1"><block wx:for="{{item.$orig.couponlist}}" wx:for-item="coupon" wx:for-index="index2" wx:key="index2"><view class="t" style="background:rgba(245, 34, 45, 0.1);color:#f5222d;"><block wx:if="{{coupon.minprice>0}}"><text>{{"满"+coupon.minprice+"减"+coupon.money}}</text></block><block wx:if="{{coupon.minprice==0}}"><text>{{coupon.money+"元无门槛"}}</text></block></view></block></view></view></block></block><block wx:else><block wx:if="{{item.$orig.content_type==='article'}}"><view class="article-container"><image class="article-image" mode="widthFix" src="{{item.$orig.cover_img||item.$orig.coverimg||' '}}" data-event-opts="{{[['load',[['imageLoadHandle',[index]]]],['error',[['imageLoadHandle',[index]]]]]}}" bindload="__e" binderror="__e"></image><view class="article-tag">文章</view><view class="article-info"><view class="article-title">{{item.$orig.title}}</view><block wx:if="{{item.$orig.summary}}"><view class="article-desc">{{item.$orig.summary}}</view></block><view class="article-meta"><text class="article-date">{{item.m4}}</text><text class="article-views"><text class="iconfont icon_chakan"></text>{{''+(item.$orig.view_num||0)+''}}</text></view></view></view></block><block wx:else><block wx:if="{{item.$orig.content_type==='video'}}"><view class="video-container"><view class="video-image-wrap"><image class="video-image" mode="widthFix" src="{{item.$orig.coverimg||' '}}" data-event-opts="{{[['load',[['imageLoadHandle',[index]]]],['error',[['imageLoadHandle',[index]]]]]}}" bindload="__e" binderror="__e"></image><view class="video-play-icon"><text class="iconfont icon_bofang"></text></view></view><view class="video-tag">视频</view><view class="video-info"><view class="video-title">{{item.$orig.title||'精彩短视频'}}</view><view class="video-meta"><block wx:if="{{item.$orig.username}}"><view class="video-user"><image class="video-user-avatar" src="{{item.$orig.logo||'/static/img/default-avatar.png'}}"></image><text class="video-user-name">{{item.$orig.username}}</text></view></block><view class="video-counts"><text class="video-view-count"><text class="iconfont icon_bofang1"></text>{{''+(item.$orig.view_num||0)+''}}</text><text class="video-like-count"><text class="iconfont icon_dianzan"></text>{{''+(item.$orig.zan_num||0)+''}}</text></view></view></view></view></block></block></block></view></block><block wx:if="{{buydialogShow}}"><buydialog vue-id="3fcaf062-1" proid="{{proid}}" menuindex="{{menuindex}}" data-event-opts="{{[['^addcart',[['addcart']]],['^buydialogChange',[['buydialogChange']]]]}}" bind:addcart="__e" bind:buydialogChange="__e" bind:__l="__l"></buydialog></block><block wx:if="{{showLinkStatus}}"><view class="posterDialog linkDialog"><view class="main"><view data-event-opts="{{[['tap',[['showLinkChange',['$event']]]]]}}" class="close" bindtap="__e"><image class="img" src="/static/img/close.png"></image></view><view class="content"><view class="title">{{lx_name}}</view><block wx:if="{{lx_bid>0}}"><view class="row"><view class="f1">店铺名称</view><view class="f2" data-url="{{'/pagesExt/business/index?id='+lx_bid}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">{{lx_bname+''}}<image class="image" src="/static/img/arrowright.png"></image></view></view></block><block wx:if="{{lx_tel}}"><view class="row"><view class="f1">联系电话</view><view class="f2" style="color:#f5222d;" data-url="{{'tel::'+lx_tel}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">{{lx_tel+''}}<image class="copyicon" src="/static/img/copy.png" data-text="{{lx_tel}}" data-event-opts="{{[['tap',[['copy',['$event']]]]]}}" catchtap="__e"></image></view></view></block></view></view></view></block></view>