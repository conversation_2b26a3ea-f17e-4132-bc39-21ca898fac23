<view class="container"><block wx:if="{{isload}}"><block><form data-event-opts="{{[['submit',[['formSubmit',['$event']]]],['reset',[['formReset',['$event']]]]]}}" bindsubmit="__e" bindreset="__e"><view class="form"><view class="form-item"><text class="label">微信号</text><input class="input" type="text" placeholder="请输入微信号" placeholder-style="color:#BBBBBB;font-size:28rpx" name="weixin" value="{{info.weixin}}"/></view></view><view class="form"><view class="form-item"><text class="label">支付宝账号</text><input class="input" type="text" placeholder="请输入支付宝账号" placeholder-style="color:#BBBBBB;font-size:28rpx" name="aliaccount" value="{{info.aliaccount}}"/></view></view><view class="form"><view class="form-item"><text class="label">开户行</text><input class="input" type="text" placeholder="请输入持卡人姓名" name="bankname" placeholder-style="color:#BBBBBB;font-size:28rpx" value="{{info.bankname}}"/></view><view class="form-item"><text class="label">持卡人姓名</text><input class="input" type="text" placeholder="请输入持卡人姓名" name="bankcarduser" placeholder-style="color:#BBBBBB;font-size:28rpx" value="{{info.bankcarduser}}"/></view><view class="form-item"><text class="label">银行卡号</text><input class="input" type="text" placeholder="请输入银行卡号" name="bankcardnum" placeholder-style="color:#BBBBBB;font-size:28rpx" value="{{info.bankcardnum}}"/></view></view><button class="set-btn" form-type="submit">保 存</button></form></block></block><block wx:if="{{loading}}"><loading vue-id="b73e3718-1" bind:__l="__l"></loading></block><dp-tabbar vue-id="b73e3718-2" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="b73e3718-3" data-ref="popmsg" bind:__l="__l"></popmsg></view>