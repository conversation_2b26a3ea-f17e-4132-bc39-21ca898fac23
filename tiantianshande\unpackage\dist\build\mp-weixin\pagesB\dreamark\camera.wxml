<view class="container"><canvas class="particles-canvas" style="{{'width:'+(canvasWidth+'px')+';'+('height:'+(canvasHeight+'px')+';')}}" canvas-id="particlesCanvas"></canvas><view class="bg-grid"></view><view class="bg-circles"></view><view class="camera-console"><view class="header-section"><view class="ai-logo"><view class="logo-glow"></view><text class="logo-icon">✨</text></view><view class="title-info"><text class="main-title" style="{{'color:'+($root.m0)+';'}}">AI变脸预测系统</text><text class="subtitle">使用先进的AI技术预测您20年后的样子</text></view><view class="time-display"><text class="year">2049</text><text class="status">{{currentStatus}}</text></view></view><view class="main-content"><block wx:if="{{currentStep==='camera'}}"><view class="camera-section"><view class="camera-container"><block wx:if="{{showCamera&&!showConfig}}"><camera class="camera-preview" device-position="front" flash="off" data-event-opts="{{[['error',[['onCameraError',['$event']]]],['initdone',[['onCameraReady',['$event']]]]]}}" binderror="__e" bindinitdone="__e"></camera></block><block wx:if="{{showConfig}}"><view class="camera-placeholder"><text class="placeholder-text">摄像头已暂停</text><text class="placeholder-desc">完成配置后将自动恢复</text></view></block><view class="photo-frame"><view class="frame-corner top-left"></view><view class="frame-corner top-right"></view><view class="frame-corner bottom-left"></view><view class="frame-corner bottom-right"></view><view class="scan-line"></view></view><view class="camera-status"><view class="status-indicator"><view class="{{['status-dot',(cameraReady)?'active':'']}}"></view><text class="status-text">{{cameraStatusText}}</text></view></view><block wx:if="{{showDebugInfo}}"><view class="debug-controls"><button class="debug-btn" type="default" data-event-opts="{{[['tap',[['forceShowConfig',['$event']]]]]}}" bindtap="__e">强制显示配置</button><button class="debug-btn" type="default" data-event-opts="{{[['tap',[['toggleCamera',['$event']]]]]}}" bindtap="__e">切换摄像头</button><button class="debug-btn" type="default" data-event-opts="{{[['tap',[['testAlert',['$event']]]]]}}" bindtap="__e">测试弹窗</button><view class="debug-info-text"><text>{{"showCamera: "+showCamera}}</text><text>{{"showConfig: "+showConfig}}</text><text>{{"currentStep: "+currentStep}}</text></view></view></block></view><view class="photo-controls"><view data-event-opts="{{[['tap',[['capturePhoto',['$event']]]]]}}" class="{{['capture-btn',(!cameraReady)?'disabled':'']}}" style="{{'background:'+('linear-gradient(45deg,'+$root.m1+' 0%,rgba('+$root.m2+',0.8) 100%)')+';'}}" bindtap="__e"><view class="btn-glow"></view><text class="btn-icon">📷</text><text class="btn-text">拍照</text></view><view data-event-opts="{{[['tap',[['chooseImage',['$event']]]]]}}" class="upload-btn" bindtap="__e"><view class="btn-glow"></view><text class="btn-icon">📁</text><text class="btn-text">选择图片</text></view></view></view></block><block wx:if="{{currentStep==='preview'}}"><view class="preview-section"><view class="preview-container"><view class="photo-preview"><image class="preview-image" src="{{capturedImageUrl}}" mode="aspectFit"></image><view class="preview-overlay"><view class="analysis-grid"></view><view class="analysis-points"></view></view></view><view class="process-controls"><view data-event-opts="{{[['tap',[['startProcessing',['$event']]]]]}}" class="process-btn" bindtap="__e"><view class="btn-glow"></view><text class="btn-icon">🔮</text><text class="btn-text">开始AI预测</text></view><view class="process-info"><text class="info-text">AI将分析您的面部特征，预测20年后的样子</text></view></view></view></view></block><block wx:if="{{currentStep==='processing'}}"><view class="processing-section"><view class="processing-container"><view class="ai-brain"><view class="brain-core"></view><view class="neural-waves"></view><view class="data-streams"></view></view><view class="processing-info"><text class="process-title">AI正在处理中...</text><view class="progress-bar"><view class="progress-fill" style="{{'width:'+(progressPercent+'%')+';'}}"></view></view><view class="processing-steps"><block wx:for="{{processingSteps}}" wx:for-item="step" wx:for-index="index" wx:key="index"><view class="{{['step',[(currentProcessStep===index)?'active':'']]}}"><text class="step-icon">{{step.icon}}</text><text class="step-text">{{step.text}}</text></view></block></view><view data-event-opts="{{[['tap',[['playReminderAudio',['$event']]]]]}}" class="reminder-btn" bindtap="__e"><text class="reminder-icon">🔊</text><text class="reminder-text">播放处理提醒</text></view></view></view></view></block><block wx:if="{{currentStep==='result'}}"><view class="result-section"><view class="result-container"><view class="result-header"><text class="result-title">✨ AI预测结果</text><text class="result-subtitle">这就是您20年后的样子！</text></view><view class="result-comparison"><view class="photo-compare"><view class="photo-item"><view class="photo-wrapper"><image class="compare-image" src="{{capturedImageUrl}}" mode="aspectFit"></image><text class="photo-label">现在的您</text></view></view><view class="transform-arrow"><text class="arrow-icon">→</text><text class="arrow-text">20年后</text></view><view class="photo-item"><view class="photo-wrapper"><image class="compare-image" src="{{predictedImageUrl}}" mode="aspectFit"></image><text class="photo-label">2044年的您</text></view></view></view></view><view class="result-actions"><view data-event-opts="{{[['tap',[['playHandoverAudio',['$event']]]]]}}" class="action-btn" bindtap="__e"><text class="action-icon">▶</text><text class="action-text">播放交接语音</text></view><view data-event-opts="{{[['tap',[['goToVoiceChat',['$event']]]]]}}" class="action-btn primary" bindtap="__e"><text class="action-icon">📞</text><text class="action-text">与未来对话</text></view><view data-event-opts="{{[['tap',[['downloadResult',['$event']]]]]}}" class="action-btn" bindtap="__e"><text class="action-icon">⬇</text><text class="action-text">下载结果</text></view><view data-event-opts="{{[['tap',[['shareResult',['$event']]]]]}}" class="action-btn" bindtap="__e"><text class="action-icon">📤</text><text class="action-text">分享结果</text></view><view data-event-opts="{{[['tap',[['retakePhoto',['$event']]]]]}}" class="action-btn secondary" bindtap="__e"><text class="action-icon">📷</text><text class="action-text">重新拍照</text></view><view data-event-opts="{{[['tap',[['clearAllAndRestart',['$event']]]]]}}" class="action-btn clear-all" bindtap="__e"><text class="action-icon">🔄</text><text class="action-text">清空记录，重新开始</text></view></view></view></view></block></view><view class="bottom-controls"><view data-event-opts="{{[['tap',[['playWelcomeAudio',['$event']]]]]}}" class="control-btn" bindtap="__e"><text class="control-icon">🔊</text><text class="control-text">播放欢迎语音</text></view><view data-event-opts="{{[['tap',[['showConfigModal',['$event']]]]]}}" class="control-btn" bindtap="__e"><text class="control-icon">⚙</text><text class="control-text">用户配置</text></view><view data-event-opts="{{[['tap',[['goBack',['$event']]]]]}}" class="control-btn" bindtap="__e"><text class="control-icon">←</text><text class="control-text">返回对话</text></view><view data-event-opts="{{[['tap',[['goHome',['$event']]]]]}}" class="control-btn" bindtap="__e"><text class="control-icon">🏠</text><text class="control-text">返回方舟</text></view></view></view><block wx:if="{{showConfig}}"><view class="config-modal"><view data-event-opts="{{[['tap',[['hideConfigModal',['$event']]]]]}}" class="config-overlay" bindtap="__e"></view><view class="config-container"><view class="config-header"><text class="config-title">👤 个人信息设置</text><view data-event-opts="{{[['tap',[['hideConfigModal',['$event']]]]]}}" class="config-close" bindtap="__e"><text class="close-text">×</text></view></view><scroll-view class="config-content" scroll-y="true"><block wx:if="{{showConfigTip}}"><view class="config-tip"><text class="tip-icon">ℹ</text><text class="tip-text">首次使用需要设置性别和职业信息，以便AI更好地为您预测未来形象。</text></view></block><view class="config-item"><text class="config-label">性别:<text class="required">*</text></text><view class="gender-buttons"><block wx:for="{{$root.l0}}" wx:for-item="option" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['e0',['$event']]]],['tap',[['e1',['$event']]]],['touchstart',[['onGenderTouchStart',['$event']]]],['touchend',[['onGenderTouchEnd',['$event']]]]]}}" data-event-params="{{({index,index})}}" class="{{['gender-btn',[(genderIndex===index+1)?'active':'']]}}" bindtap="__e" bindtouchstart="__e" bindtouchend="__e"><text class="btn-text">{{option}}</text><block wx:if="{{genderIndex===index+1}}"><text class="btn-check">✓</text></block></view></block></view></view><view class="config-item"><text class="config-label">职业:<text class="required">*</text></text><view class="input-wrapper"><block wx:if="{{!useTextarea}}"><input class="config-input" placeholder="请输入您的职业" placeholder-style="color:#B2B5BE;font-size:28rpx" maxlength="20" type="text" confirm-type="done" cursor-spacing="50" data-event-opts="{{[['input',[['onProfessionInput',['$event']]]],['focus',[['onInputFocus',['$event']]]],['blur',[['onInputBlur',['$event']]]],['confirm',[['onInputConfirm',['$event']]]]]}}" value="{{userProfession}}" bindinput="__e" bindfocus="__e" bindblur="__e" bindconfirm="__e"/></block><block wx:else><textarea class="config-textarea" placeholder="请输入您的职业" placeholder-style="color:#B2B5BE;font-size:28rpx" maxlength="20" auto-height="{{true}}" cursor-spacing="50" data-event-opts="{{[['input',[['onProfessionInput',['$event']]]],['focus',[['onInputFocus',['$event']]]],['blur',[['onInputBlur',['$event']]]]]}}" value="{{userProfession}}" bindinput="__e" bindfocus="__e" bindblur="__e"></textarea></block></view><view class="input-tip"><text class="tip-text">如：医生、教师、工程师、学生等</text></view><block wx:if="{{showDebugInfo}}"><view class="debug-info"><text class="debug-text">{{"当前值: "+userProfession}}</text><view class="debug-buttons"><view data-event-opts="{{[['tap',[['testInput',['$event']]]]]}}" class="debug-btn" bindtap="__e">测试输入</view><view data-event-opts="{{[['tap',[['clearInput',['$event']]]]]}}" class="debug-btn" bindtap="__e">清空</view><view data-event-opts="{{[['tap',[['toggleInputType',['$event']]]]]}}" class="debug-btn" bindtap="__e">{{useTextarea?'input':'textarea'}}</view><view data-event-opts="{{[['tap',[['toggleDebug',['$event']]]]]}}" class="debug-btn" bindtap="__e">{{(showDebugInfo?'隐藏':'显示')+"调试"}}</view></view></view></block></view><view class="config-description"><text class="desc-title">说明：</text><text class="desc-item">• 性别信息用于AI算法选择合适的预测模型</text><text class="desc-item">• 职业信息影响未来形象的气质和风格预测</text><text class="desc-item">• 所有信息仅用于本次预测，不会上传服务器</text></view></scroll-view><view class="config-footer"><view data-event-opts="{{[['tap',[['saveConfig',['$event']]]]]}}" class="config-btn" bindtap="__e"><text class="btn-icon">💾</text><text class="btn-text">保存配置</text></view></view></view></view></block><block wx:if="{{showError}}"><view data-event-opts="{{[['tap',[['hideErrorModal',['$event']]]]]}}" class="modal" bindtap="__e"><view data-event-opts="{{[['tap',[['',['$event']]]]]}}" class="modal-content" catchtap="__e"><view class="modal-header"><text class="modal-title">⚠ 提示</text><view data-event-opts="{{[['tap',[['hideErrorModal',['$event']]]]]}}" class="modal-close" bindtap="__e"><text class="close-icon">×</text></view></view><view class="modal-body"><text class="error-message">{{errorMessage}}</text></view><view class="modal-footer"><view data-event-opts="{{[['tap',[['hideErrorModal',['$event']]]]]}}" class="modal-btn" bindtap="__e"><text class="btn-icon">✓</text><text class="btn-text">确定</text></view></view></view></view></block><view class="footer"><text class="footer-text">AI变脸预测系统 v2049.1 | 基于深度学习技术 | 数据安全加密传输</text></view></view>