<view><block wx:if="{{isload}}"><block><view class="container"><view class="tab-box"><view data-event-opts="{{[['tap',[['changeRankType',[1]]]]]}}" class="{{['tab-item',(rankType===1)?'active':'']}}" bindtap="__e">个人风云榜</view><view data-event-opts="{{[['tap',[['changeRankType',[2]]]]]}}" class="{{['tab-item',(rankType===2)?'active':'']}}" bindtap="__e">商品风云榜</view></view><view class="filter-box"><view class="filter-row"><picker value="{{monthIndex}}" range="{{monthList}}" range-key="name" data-event-opts="{{[['change',[['onMonthChange',['$event']]]]]}}" bindchange="__e"><view class="picker-item"><text>{{getMonthName}}</text><text class="icon-arrow"></text></view></picker></view></view><view class="rank-list"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="id"><block><block wx:if="{{rankType===1}}"><view class="rank-item"><view class="{{['rank-num','rank-'+(index+1)]}}"><block wx:if="{{index<3}}"><image class="medal-icon" src="{{item.m0}}" mode="aspectFit"></image></block><block wx:else><text>{{index+1}}</text></block></view><view class="info"><view class="name">{{item.$orig.nickname||'匿名用户'}}</view></view></view></block><block wx:if="{{rankType===2}}"><view data-event-opts="{{[['tap',[['goToProduct',['$0'],[[['rankList','id',item.$orig.id,'id']]]]]]]}}" class="rank-item" bindtap="__e"><view class="{{['rank-num','rank-'+(index+1)]}}"><block wx:if="{{index<3}}"><image class="medal-icon" src="{{item.m1}}" mode="aspectFit"></image></block><block wx:else><text>{{index+1}}</text></block></view><view class="info"><view class="name product-link">{{item.$orig.name||'-'}}</view><view class="sales-info"><block wx:for="{{item.$orig.tier_sales}}" wx:for-item="tier" wx:for-index="idx" wx:key="idx"><view class="sales-item"><text class="tier-name">{{tier.name}}</text><text class="tier-num">{{tier.num+"盒"}}</text></view></block></view></view><view class="total-sales"><text>总销量</text><text class="num">{{(item.$orig.total_num||0)+"盒"}}</text></view></view></block></block></block></view><block wx:if="{{loading}}"><view class="loading-more">加载中...</view></block><block wx:if="{{nomore}}"><view class="no-more">没有更多数据了</view></block></view></block></block><block wx:if="{{loading}}"><loading vue-id="53747c64-1" bind:__l="__l"></loading></block></view>