require('../common/vendor.js');(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["daihuobiji/kuaituan/tuanlist"],{"094d9":function(t,n,a){"use strict";a.r(n);var i=a("e15cd"),e=a("2272");for(var o in e)["default"].indexOf(o)<0&&function(t){a.d(n,t,(function(){return e[t]}))}(o);a("0e04");var l=a("828b"),s=Object(l["a"])(e["default"],i["b"],i["c"],!1,null,null,null,!1,i["a"],void 0);n["default"]=s.exports},"0e04":function(t,n,a){"use strict";var i=a("1af2"),e=a.n(i);e.a},"1af2":function(t,n,a){},2272:function(t,n,a){"use strict";a.r(n);var i=a("fe5d"),e=a.n(i);for(var o in i)["default"].indexOf(o)<0&&function(t){a.d(n,t,(function(){return i[t]}))}(o);n["default"]=e.a},"23e7":function(t,n,a){"use strict";(function(t,n){var i=a("47a9");a("06e9");i(a("3240"));var e=i(a("094d9"));t.__webpack_require_UNI_MP_PLUGIN__=a,n(e.default)}).call(this,a("3223")["default"],a("df3c")["createPage"])},e15cd:function(t,n,a){"use strict";a.d(n,"b",(function(){return e})),a.d(n,"c",(function(){return o})),a.d(n,"a",(function(){return i}));var i={waterfallArticle:function(){return a.e("components/waterfall-article/waterfall-article").then(a.bind(null,"97070"))},nodata:function(){return a.e("components/nodata/nodata").then(a.bind(null,"101c"))},nomore:function(){return a.e("components/nomore/nomore").then(a.bind(null,"3892"))},loading:function(){return a.e("components/loading/loading").then(a.bind(null,"ceaa"))},dpTabbar:function(){return a.e("components/dp-tabbar/dp-tabbar").then(a.bind(null,"b875"))},popmsg:function(){return a.e("components/popmsg/popmsg").then(a.bind(null,"2bf2"))}},e=function(){var t=this,n=t.$createElement,a=(t._self._c,t.isload?t.t("color1"):null),i=t.isload?t.t("color1"):null,e=t.isload?t.t("color1"):null,o=t.isload?t.t("color1"):null,l=t.isload?t.t("color1"):null,s=t.isload?t.t("color1"):null,c=t.isload?t.t("color1"):null,u=t.isload?t.t("color1"):null;t.$mp.data=Object.assign({},{$root:{m0:a,m1:i,m2:e,m3:o,m4:l,m5:s,m6:c,m7:u}})},o=[]},fe5d:function(t,n,a){"use strict";(function(t){var i=a("47a9");Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;var e=i(a("7ca3")),o=getApp(),l={data:function(){var t;return t={opt:{},loading:!1,isload:!1,menuindex:-1,nodata:!1,nomore:!1,keyword:"",datalist:[],pagenum:1,clist:[],cnamelist:[],cidlist:[]},(0,e.default)(t,"datalist",[]),(0,e.default)(t,"cid",0),(0,e.default)(t,"bid",0),(0,e.default)(t,"listtype",0),(0,e.default)(t,"set",""),(0,e.default)(t,"tuanzhang_status",0),(0,e.default)(t,"look_type",!1),(0,e.default)(t,"pre_url",""),t},onLoad:function(t){this.opt=o.getopts(t),this.cid=this.opt.cid||0,this.bid=this.opt.bid||0,this.look_type=this.opt.look_type||!1,this.pre_url=this.opt.pre_url||"",this.opt.keyword&&(this.keyword=this.opt.keyword),this.getdata()},onPullDownRefresh:function(){this.getdata()},onReachBottom:function(){this.nomore||this.nodata||(this.pagenum=this.pagenum+1,this.getdata(!0))},methods:{getdata:function(n){n||(this.pagenum=1,this.datalist=[]);var a=this,i=a.pagenum,e=a.keyword,l=a.cid;a.loading=!0,a.nodata=!1,a.nomore=!1,o.post("Apidaihuoyiuan/getartlist",{bid:a.bid,cid:l,pagenum:i,keyword:e},(function(n){console.log(n),a.loading=!1;var e=n.data;if(a.tuanzhang_status=n.tuanzhang_status,e&&e.length>0&&e.forEach((function(t){t.pic2?t.pics=t.pic2.split(","):t.pics=[]})),1==i){if(a.listtype=n.listtype||0,a.clist=n.clist,a.set=n.set,n.clist.length>0){var o=[],l=[];for(var s in o.push("全部"),l.push("0"),a.clist)o.push(a.clist[s].name),l.push(a.clist[s].id);a.cnamelist=o,a.cidlist=l}t.setNavigationBarTitle({title:n.title}),a.datalist=e,0==e.length&&(a.nodata=!0),a.loaded()}else if(0==e.length)a.nomore=!0;else{var c=a.datalist,u=c.concat(e);a.datalist=u}}))},gotoDetail:function(n){if(1===this.tuanzhang_status){var a="/daihuobiji/kuaituan/tuanzhangdetail?id=".concat(n.id);console.log("跳转到团长链接: ",a),t.navigateTo({url:a})}else{var i="/daihuobiji/kuaituan/detail?id=".concat(n.id);console.log("跳转到默认链接: ",i),t.navigateTo({url:i})}},searchConfirm:function(t){var n=t.detail.value;this.keyword=n,this.getdata()},changetab:function(n){this.cid=n,t.pageScrollTo({scrollTop:0,duration:0}),2==this.listtype&&this.$refs.waterfall.refresh(),this.getdata()},handleShare:function(n){t.showActionSheet({itemList:["分享到微信","复制链接"],success:function(a){switch(a.tapIndex){case 0:t.share({provider:"weixin",scene:"WXSceneSession",type:0,title:n.name,imageUrl:n.pic,summary:"价格：￥".concat(n.priceRange),success:function(){t.showToast({title:"分享成功",icon:"success"})}});break;case 1:var i="".concat(window.location.origin,"/daihuobiji/kuaituan/detail?id=").concat(n.id);t.setClipboardData({data:i,success:function(){t.showToast({title:"链接已复制",icon:"success"})}});break}}})}}};n.default=l}).call(this,a("df3c")["default"])}},[["23e7","common/runtime","common/vendor"]]]);