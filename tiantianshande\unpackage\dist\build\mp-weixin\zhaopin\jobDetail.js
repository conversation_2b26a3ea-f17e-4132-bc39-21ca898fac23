(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["zhaopin/jobDetail"],{"5c7d":function(t,e,i){"use strict";i.r(e);var n=i("d7dd"),o=i.n(n);for(var a in n)["default"].indexOf(a)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(a);e["default"]=o.a},"7bfd":function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return o})),i.d(e,"a",(function(){}));var n=function(){var t=this,e=t.$createElement,i=(t._self._c,t.loading?null:t.t("color1")),n=t.loading||t.hasApplied?null:t.t("color1"),o=t.loading||t.hasApplied?null:t.t("color1rgb"),a=t.loading||t.hasApplied?null:t.t("color1rgb"),l=t.reportVisible?t.t("color1"):null,s=t.reportVisible?t.t("color1rgb"):null,r=t.agreementVisible?t.t("color1"):null;t.$mp.data=Object.assign({},{$root:{m0:i,m1:n,m2:o,m3:a,m4:l,m5:s,m6:r}})},o=[]},"865e":function(t,e,i){"use strict";i.r(e);var n=i("7bfd"),o=i("5c7d");for(var a in o)["default"].indexOf(a)<0&&function(t){i.d(e,t,(function(){return o[t]}))}(a);i("8fd1");var l=i("828b"),s=Object(l["a"])(o["default"],n["b"],n["c"],!1,null,null,null,!1,n["a"],void 0);e["default"]=s.exports},"8fd1":function(t,e,i){"use strict";var n=i("f155"),o=i.n(n);o.a},b85b:function(t,e,i){"use strict";(function(t,e){var n=i("47a9");i("06e9");n(i("3240"));var o=n(i("865e"));t.__webpack_require_UNI_MP_PLUGIN__=i,e(o.default)}).call(this,i("3223")["default"],i("df3c")["createPage"])},d7dd:function(t,e,i){"use strict";(function(t){Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var i=getApp(),n={data:function(){return{loading:!0,jobInfo:{id:0,title:"",salary:"",work_address:"",education:"",experience:"",company_logo:"",company_name:"",description:"",requirement:"",benefits:"",work_mode:"",work_intensity:"",work_time_type:"",payment:"",numbers:0,nature:1,scale:1,formatted_options:{},is_favorite:0,status:1,hasApplied:!1},isCollected:!1,reportVisible:!1,agreementVisible:!1,agreementContent:"",submitting:!1,hasApplied:!1,formData:{name:"",phone:"",agreement:!1},genderOptions:[{label:"男",value:"1"},{label:"女",value:"2"}],experienceOptions:[{label:"应届生",value:"0"},{label:"1年以下",value:"1"},{label:"1-3年",value:"2"},{label:"3-5年",value:"3"},{label:"5-10年",value:"4"},{label:"10年以上",value:"5"}],educationOptions:[{label:"初中及以下",value:"1"},{label:"高中",value:"2"},{label:"大专",value:"3"},{label:"本科",value:"4"},{label:"硕士",value:"5"},{label:"博士",value:"6"}]}},computed:{getCompanyNature:function(){return{1:"民营企业",2:"国有企业",3:"合资企业",4:"外资企业"}[this.jobInfo.nature]||"其他企业"},getCompanyScale:function(){return{1:"20-99人",2:"100-499人",3:"500-999人",4:"1000-9999人",5:"10000人以上"}[this.jobInfo.scale]||"规模未知"},isFormValid:function(){var t=this.formData,e=t.name,i=t.phone,n=t.agreement;return e&&i&&11===i.length&&n}},onLoad:function(e){e.id?this.getJobDetail(e.id):(this.loading=!1,t.showToast({title:"参数错误",icon:"none"}))},onPullDownRefresh:function(){this.jobInfo.id&&this.getJobDetail(this.jobInfo.id),setTimeout((function(){t.stopPullDownRefresh()}),1e3)},methods:{getJobDetail:function(e){var n=this;this.loading=!0,i.get("ApiZhaopin/getPositionDetail",{id:e},(function(e){if(n.loading=!1,1===e.status&&e.data){var i=e.data;["description","requirement","benefits"].forEach((function(t){i[t]&&(i[t]=i[t].replace(/<img/gi,'<img style="max-width:100%;height:auto;display:block;margin:10rpx 0;"'))})),n.jobInfo=i,n.isCollected=1===i.is_favorite,n.hasApplied=i.hasApplied||!1,t.setNavigationBarTitle({title:i.title||"职位详情"})}else t.showToast({title:e.msg||"获取数据失败",icon:"none"})}),(function(){n.loading=!1,t.showToast({title:"网络错误，请重试",icon:"none"})}))},handleCollect:function(){var e=this;if(this.jobInfo.id){var n=this.isCollected;this.isCollected=!this.isCollected,i.post("apiZhaopin/favoritePosition",{position_id:this.jobInfo.id},(function(i){1===i.status?(e.isCollected=1===i.data.is_favorite,t.showToast({title:e.isCollected?"收藏成功":"已取消收藏",icon:"none"})):(e.isCollected=n,t.showToast({title:i.msg||"操作失败",icon:"none"}))}),(function(){e.isCollected=n,t.showToast({title:"网络错误，请重试",icon:"none"})}))}},handleApply:function(){1===this.jobInfo.status?this.hasApplied?t.showToast({title:"您已经报名过该职位",icon:"none"}):this.reportVisible=!0:t.showToast({title:"该职位已结束招聘",icon:"none"})},closeReportDialog:function(){this.reportVisible=!1,this.resetForm()},resetForm:function(){this.formData={name:"",phone:"",agreement:!1}},handleAgreementChange:function(t){this.formData.agreement=t.detail.value.length>0},showAgreement:function(){var t=this;i.get("ApiZhaopin/getAgreement",{},(function(e){1===e.status&&(t.agreementContent=e.data.content,t.agreementVisible=!0)}))},closeAgreement:function(){this.agreementVisible=!1},submitApply:function(){var e=this;this.isFormValid&&(this.submitting=!0,i.post("ApiZhaopin/submitApply",{position_id:this.jobInfo.id,name:this.formData.name,phone:this.formData.phone},(function(i){e.submitting=!1,1===i.status?(e.hasApplied=!0,e.reportVisible=!1,t.showToast({title:"报名成功",icon:"success"}),e.getJobDetail(e.jobInfo.id)):t.showToast({title:i.msg||"报名失败，请重试",icon:"none"})}),(function(){e.submitting=!1,t.showToast({title:"网络错误，请重试",icon:"none"})})))},goToCompany:function(){this.jobInfo.company_id&&t.navigateTo({url:"/zhaopin/company?id=".concat(this.jobInfo.company_id),fail:function(){t.showToast({title:"页面跳转失败",icon:"none"})}})}}};e.default=n}).call(this,i("df3c")["default"])},f155:function(t,e,i){}},[["b85b","common/runtime","common/vendor"]]]);