(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["components/dp-tabbar/dp-tabbar"],{"2ab1":function(t,e,n){"use strict";n.d(e,"b",(function(){return i})),n.d(e,"c",(function(){return a})),n.d(e,"a",(function(){}));var i=function(){var t=this,e=t.$createElement,n=(t._self._c,t.currentIndex>-1&&!t.isHidden?1==t.menudata.menustyle&&(3==t.menudata.list.length||5==t.menudata.list.length):null),i=t.currentIndex>-1&&!t.isHidden&&n?t.menudata.list.length:null,a=t.currentIndex>-1&&!t.isHidden&&n&&5==i?t.__map(t.menudata.list,(function(e,n){var i=t.__get_orig(e),a=n<2?0!=n&&"baidu"==t.getplatform():null;return{$orig:i,m0:a}})):null,r=t.currentIndex>-1&&!t.isHidden&&n&&5==i?t.__map(t.menudata.list,(function(e,n){var i=t.__get_orig(e),a=2==n?0!=n&&"baidu"==t.getplatform():null;return{$orig:i,m1:a}})):null,l=t.currentIndex>-1&&!t.isHidden&&n&&5==i?t.__map(t.menudata.list,(function(e,n){var i=t.__get_orig(e),a=n>=3?0!=n&&"baidu"==t.getplatform():null;return{$orig:i,m2:a}})):null,d=t.currentIndex>-1&&!t.isHidden&&n?t.menudata.list.length:null,u=t.currentIndex>-1&&!t.isHidden&&n&&3==d?t.__map(t.menudata.list,(function(e,n){var i=t.__get_orig(e),a=n<1?0!=n&&"baidu"==t.getplatform():null;return{$orig:i,m3:a}})):null,o=t.currentIndex>-1&&!t.isHidden&&n&&3==d?t.__map(t.menudata.list,(function(e,n){var i=t.__get_orig(e),a=1==n?0!=n&&"baidu"==t.getplatform():null;return{$orig:i,m4:a}})):null,s=t.currentIndex>-1&&!t.isHidden&&n&&3==d?t.__map(t.menudata.list,(function(e,n){var i=t.__get_orig(e),a=n>=2?0!=n&&"baidu"==t.getplatform():null;return{$orig:i,m5:a}})):null,c=t.currentIndex>-1&&!t.isHidden&&!n?t.__map(t.menudata.list,(function(e,n){var i=t.__get_orig(e),a=0!=n&&"baidu"==t.getplatform();return{$orig:i,m6:a}})):null;t.$mp.data=Object.assign({},{$root:{g0:n,g1:i,l0:a,l1:r,l2:l,g2:d,l3:u,l4:o,l5:s,l6:c}})},a=[]},"2de6":function(t,e,n){"use strict";(function(t){Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n=getApp(),i={data:function(){return{menudata:{},currentIndex:-1,i:0,opentype:"reLaunch",isHidden:!1}},mounted:function(){var e=this;e.settabbar(),"toutiao"==n.globalData.platform&&setTimeout((function(){e.settabbar()}),100),t.$on("hideTabbar",(function(t){e.isHidden=t}))},beforeDestroy:function(){t.$off("hideTabbar")},props:{opt:{}},methods:{settabbar:function(){var t=this;if(!n.globalData.isinit&&this.i<100)setTimeout((function(){t.i++,t.settabbar()}),100);else{var e="reLaunch",i=getCurrentPages(),a=i[i.length-1],r="/"+(a.route?a.route:a.__route__);if("baidu"==n.globalData.platform)var l=a.options;else l=a.$vm.opt;if(l&&l.id?r+="?id="+l.id:l&&l.cid?r+="?cid="+l.cid:l&&l.gid?r+="?gid="+l.gid:l&&l.bid&&(r+="?bid="+l.bid),console.log(r),t.opt.defaultIndex)d=t.opt.defaultIndex;else var d=-1;for(var u=!1,o=JSON.parse(JSON.stringify(n.globalData.initdata.menudata)),s=o["list"],c=0;c<s.length;c++)s[c]["pagePath"]==r?(d=c,u=!0,o["list"][c].color=o["selectedColor"]):o["list"][c].color=o["color"];if(0==u){var f=JSON.parse(JSON.stringify(n.globalData.initdata.menu2data));if(f.length>0)for(var c in f)if(l&&l.bid&&(f[c].indexurl=f[c].indexurl.replace("[bid]",l.bid)),f[c].indexurl==r){for(var g in u=!0,d=10,o=f[c],o.list)l&&l.bid&&(o.list[g].pagePath=o.list[g].pagePath.replace("[bid]",l.bid)),o.list[g].pagePath==r&&o["selectedColor"]?(o["list"][g].color=o["selectedColor"],o["list"][g]["selectedIconPath"]&&(o["list"][g].iconPath=o["list"][g]["selectedIconPath"])):o["color"]&&(o["list"][g].color=o["color"]);e=""}}t.opentype=e,t.currentIndex=d,t.menudata=o,console.log(d),t.$emit("getmenuindex",d)}}}};e.default=i}).call(this,n("df3c")["default"])},"4d5a":function(t,e,n){},"728d":function(t,e,n){"use strict";var i=n("4d5a"),a=n.n(i);a.a},b875:function(t,e,n){"use strict";n.r(e);var i=n("2ab1"),a=n("eb61");for(var r in a)["default"].indexOf(r)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(r);n("728d");var l=n("828b"),d=Object(l["a"])(a["default"],i["b"],i["c"],!1,null,"514cd9e3",null,!1,i["a"],void 0);e["default"]=d.exports},eb61:function(t,e,n){"use strict";n.r(e);var i=n("2de6"),a=n.n(i);for(var r in i)["default"].indexOf(r)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(r);e["default"]=a.a}}]);
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'components/dp-tabbar/dp-tabbar-create-component',
    {
        'components/dp-tabbar/dp-tabbar-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('df3c')['createComponent'](__webpack_require__("b875"))
        })
    },
    [['components/dp-tabbar/dp-tabbar-create-component']]
]);
