(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["zhaopin/resume"],{"06d9":function(t,e,o){"use strict";o.r(e);var r=o("8af9"),n=o.n(r);for(var a in r)["default"].indexOf(a)<0&&function(t){o.d(e,t,(function(){return r[t]}))}(a);e["default"]=n.a},"3ce5":function(t,e,o){"use strict";o.r(e);var r=o("cbc6"),n=o("06d9");for(var a in n)["default"].indexOf(a)<0&&function(t){o.d(e,t,(function(){return n[t]}))}(a);o("b91d");var i=o("828b"),l=Object(i["a"])(n["default"],r["b"],r["c"],!1,null,"145b735b",null,!1,r["a"],void 0);e["default"]=l.exports},"400c":function(t,e,o){"use strict";(function(t,e){var r=o("47a9");o("06e9");r(o("3240"));var n=r(o("3ce5"));t.__webpack_require_UNI_MP_PLUGIN__=o,e(n.default)}).call(this,o("3223")["default"],o("df3c")["createPage"])},"8af9":function(t,e,o){"use strict";(function(t){var r=o("47a9");Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n=r(o("af34")),a=getApp(),i={data:function(){return{currentStep:0,steps:[{title:"基本信息",required:["name","phone","gender","birthday"]},{title:"教育经历",required:[]},{title:"工作经历",required:[]},{title:"求职意向",required:["expectedPosition","expectedSalary","expectedCity"]}],startDate:"1960-01-01",endDate:(new Date).toISOString().split("T")[0],genderOptions:["男","女"],educationLevels:["高中","专科","本科","硕士","博士"],cityList:["北京","上海","广州","深圳","杭州","成都","武汉","南京"],arrivalTimeOptions:["随时到岗","一周内到岗","一个月内到岗","三个月内到岗","待定"],jobTypes:["全职","兼职","实习"],nationOptions:["汉族","壮族","满族","回族","苗族","维吾尔族","土家族","彝族","蒙古族","藏族","布依族","侗族","瑶族","朝鲜族","白族","哈尼族","哈萨克族","黎族","傣族","畲族","傈僳族","仡佬族","东乡族","高山族","拉祜族","水族","佤族","纳西族","羌族","土族","仫佬族","锡伯族","柯尔克孜族","达斡尔族","景颇族","毛南族","撒拉族","布朗族","塔吉克族","阿昌族","普米族","鄂温克族","怒族","京族","基诺族","德昂族","保安族","俄罗斯族","裕固族","乌孜别克族","门巴族","鄂伦春族","独龙族","塔塔尔族","赫哲族","珞巴族"],cultureOptions:["小学","初中","高中","专科","本科","硕士研究生","博士研究生"],formData:{name:"",phone:"",gender:"",birthday:"",education:[],workExperience:[],expectedPosition:"",expectedSalary:"",expectedCity:"",arrivalTime:"",jobType:"全职",additionalInfo:"",avatar:"",email:"",address:"",nation:"",age:"",culture:""},contentHeight:0,isSubmitting:!1,pre_url:"",loading:!1,isload:!1,isFocused:!1}},computed:{canGoNext:function(){var t=this,e=this.steps[this.currentStep];return!e.required.length||e.required.every((function(e){var o=t.formData[e];return o&&""!==o.toString().trim()}))}},onLoad:function(){var e=t.getSystemInfoSync();this.contentHeight=e.windowHeight-t.upx2px(380),this.pre_url=a.globalData.pre_url,this.getResumeData()},methods:{onGenderChange:function(t){this.formData.gender=this.genderOptions[t.detail.value]},onBirthChange:function(t){this.formData.birthday=t.detail.value},addEducation:function(){this.formData.education.push({school:"",major:"",level:"",startDate:"",endDate:""})},deleteEducation:function(t){var e=this,o=this.formData.education[t];o.id?a.post("ApiZhaopin/deleteEducation",{id:o.id},(function(o){1==o.status?(e.formData.education.splice(t,1),a.error("删除成功")):a.error(o.msg)})):e.formData.education.splice(t,1)},onEduLevelChange:function(t,e){this.formData.education[e].level=this.educationLevels[t.detail.value]},onEduStartChange:function(t,e){this.formData.education[e].startDate=t.detail.value},onEduEndChange:function(t,e){this.formData.education[e].endDate=t.detail.value},addWork:function(){this.formData.workExperience.push({company:"",position:"",startDate:"",endDate:"",description:""})},deleteWork:function(t){var e=this,o=this.formData.workExperience[t];o.id?a.post("ApiZhaopin/deleteWork",{id:o.id},(function(o){1==o.status?(e.formData.workExperience.splice(t,1),a.error("删除成功")):a.error(o.msg)})):e.formData.workExperience.splice(t,1)},onWorkStartChange:function(t,e){this.formData.workExperience[e].startDate=t.detail.value},onWorkEndChange:function(t,e){this.formData.workExperience[e].endDate=t.detail.value},onCityChange:function(t){this.formData.expectedCity=this.cityList[t.detail.value]},onArrivalTimeChange:function(t){this.formData.arrivalTime=this.arrivalTimeOptions[t.detail.value]},selectJobType:function(t){this.formData.jobType=t},jumpToStep:function(e){e<this.currentStep||this.canGoNext?this.currentStep=e:t.showToast({title:"请完善当前步骤",icon:"none"})},onSwiperChange:function(t){var e=t.detail.current;this.currentStep=e},prevStep:function(){this.currentStep>0&&this.currentStep--},nextStep:function(){this.currentStep<this.steps.length-1&&this.canGoNext&&this.currentStep++},submitResume:function(){var t=this.formData;if(!t.name)return a.error("请填写姓名"),!1;if(!t.phone)return a.error("请填写手机号码"),!1;if(!t.gender)return a.error("请选择性别"),!1;if(!t.birthday)return a.error("请选择出生日期"),!1;if(!t.avatar)return a.error("请上传头像"),!1;if(!t.nation)return a.error("请选择民族"),!1;if(!t.age)return a.error("请输入年龄"),!1;if(!t.culture)return a.error("请选择文化程度"),!1;if(t.email&&!this.validateEmail(t.email))return a.error("请输入正确的邮箱格式"),!1;var e={name:t.name,avatar:t.avatar,gender:"男"===t.gender?1:2,birthday:t.birthday,work_years:0,education:t.culture,phone:t.phone,email:t.email||"",current_status:0,expect_position:t.expectedPosition,expect_salary:t.expectedSalary,expect_city:t.expectedCity,self_evaluation:t.additionalInfo||"",nation:t.nation,age:t.age,address:t.address,culture:t.culture,arrival_time:t.arrivalTime||""};a.showLoading("提交中"),a.post("ApiZhaopin/saveResumeBasic",e,(function(e){if(1==e.status){e.data.resume_id;var o=t.education.map((function(t){return new Promise((function(e,o){a.post("ApiZhaopin/saveEducation",{id:t.id||0,school_name:t.school,major:t.major,education:t.level,start_time:t.startDate,end_time:t.endDate,description:t.description||""},e)}))})),r=t.workExperience.map((function(t){return new Promise((function(e,o){a.post("ApiZhaopin/saveWork",{id:t.id||0,company_name:t.company,position:t.position,department:t.department||"",start_time:t.startDate,end_time:t.endDate,description:t.description||""},e)}))}));Promise.all([].concat((0,n.default)(o),(0,n.default)(r))).then((function(){a.showLoading(!1),a.error("保存成功"),setTimeout((function(){a.goto(a.globalData.indexurl)}),1e3)}))}else a.showLoading(!1),a.error(e.msg)}))},chooseAvatar:function(){var t=this;a.chooseImage((function(e){e&&e.length>0&&(t.formData.avatar=e[0])}),1)},removeAvatar:function(){this.formData.avatar=""},previewImage:function(e){var o=e.currentTarget.dataset.url;o&&t.previewImage({urls:[o]})},validateEmail:function(t){return/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(t)},getResumeData:function(){var t=this;t.loading=!0,a.get("ApiZhaopin/getResumeDetail",{},(function(e){t.loading=!1,1==e.status&&(e.data&&(t.formData={name:e.data.name||"",phone:e.data.phone||"",gender:1==e.data.gender?"男":"女",birthday:e.data.birthday||"",avatar:e.data.avatar||"",email:e.data.email||"",address:e.data.address||"",nation:e.data.nation||"",age:e.data.age||"",culture:e.data.culture||"",education:[],workExperience:[],expectedPosition:e.data.expect_position||"",expectedSalary:e.data.expect_salary||"",expectedCity:e.data.expect_city||"",additionalInfo:e.data.self_evaluation||"",arrivalTime:e.data.arrival_time||"",jobType:"全职"},t.formData.education=(e.data.education_list||[]).map((function(t){return{id:t.id||0,school:t.school_name,major:t.major,level:t.education,startDate:t.start_time,endDate:t.end_time,description:t.description}})),t.formData.workExperience=(e.data.work_list||[]).map((function(t){return{id:t.id||0,company:t.company_name,position:t.position,department:t.department,startDate:t.start_time,endDate:t.end_time,description:t.description}}))),t.loaded())}))},loaded:function(){this.isload=!0},onNationChange:function(t){this.formData.nation=this.nationOptions[t.detail.value]},onCultureChange:function(t){this.formData.culture=this.cultureOptions[t.detail.value]}},watch:{"formData.email":function(e){e&&!this.validateEmail(e)&&t.showToast({title:"请输入正确的邮箱格式",icon:"none"})}}};e.default=i}).call(this,o("df3c")["default"])},b09b:function(t,e,o){},b91d:function(t,e,o){"use strict";var r=o("b09b"),n=o.n(r);n.a},cbc6:function(t,e,o){"use strict";o.d(e,"b",(function(){return r})),o.d(e,"c",(function(){return n})),o.d(e,"a",(function(){}));var r=function(){var t=this,e=t.$createElement,o=(t._self._c,t.t("color1")),r=t.t("color1rgb"),n=t.__map(t.steps,(function(e,o){var r=t.__get_orig(e),n=t.currentStep===o||o<t.currentStep?t.t("color1"):null;return{$orig:r,m2:n}})),a=t.t("color1"),i=t.isFocused?t.t("color1"):null,l=t.isFocused?t.t("color1rgb"):null,c=t.isFocused?t.t("color1"):null,u=t.isFocused?t.t("color1rgb"):null,s=t.isFocused?t.t("color1"):null,d=t.isFocused?t.t("color1rgb"):null,m=t.isFocused?t.t("color1"):null,p=t.isFocused?t.t("color1rgb"):null,f=t.isFocused?t.t("color1"):null,g=t.isFocused?t.t("color1rgb"):null,h=t.isFocused?t.t("color1"):null,v=t.isFocused?t.t("color1rgb"):null,b=t.isFocused?t.t("color1"):null,_=t.isFocused?t.t("color1rgb"):null,D=t.isFocused?t.t("color1"):null,F=t.isFocused?t.t("color1rgb"):null,x=t.isFocused?t.t("color1"):null,y=t.isFocused?t.t("color1rgb"):null,w=t.t("color1"),S=t.t("color1rgb"),E=t.t("color1"),T=t.t("color1rgb"),k=t.formData.education.length,C=0!==k?t.__map(t.formData.education,(function(e,o){var r=t.__get_orig(e),n=t.t("color1"),a=t.t("color1rgb"),i=t.t("color1rgb"),l=t.isFocused?t.t("color1"):null,c=t.isFocused?t.t("color1rgb"):null,u=t.isFocused?t.t("color1"):null,s=t.isFocused?t.t("color1rgb"):null,d=t.isFocused?t.t("color1"):null,m=t.isFocused?t.t("color1rgb"):null,p=t.isFocused?t.t("color1"):null,f=t.isFocused?t.t("color1rgb"):null,g=t.isFocused?t.t("color1"):null,h=t.isFocused?t.t("color1rgb"):null,v=t.isFocused?t.t("color1"):null,b=t.isFocused?t.t("color1rgb"):null;return{$orig:r,m26:n,m27:a,m28:i,m29:l,m30:c,m31:u,m32:s,m33:d,m34:m,m35:p,m36:f,m37:g,m38:h,m39:v,m40:b}})):null,j=t.t("color1"),P=t.t("color1rgb"),O=t.t("color1"),L=t.t("color1rgb"),A=t.isFocused?t.t("color1"):null,I=t.isFocused?t.t("color1rgb"):null,W=t.formData.workExperience.length,$=0!==W?t.__map(t.formData.workExperience,(function(e,o){var r=t.__get_orig(e),n=t.t("color1"),a=t.t("color1rgb"),i=t.t("color1rgb"),l=t.isFocused?t.t("color1"):null,c=t.isFocused?t.t("color1rgb"):null,u=t.isFocused?t.t("color1"):null,s=t.isFocused?t.t("color1rgb"):null,d=t.isFocused?t.t("color1"):null,m=t.isFocused?t.t("color1rgb"):null,p=t.isFocused?t.t("color1"):null,f=t.isFocused?t.t("color1rgb"):null,g=t.isFocused?t.t("color1"):null,h=t.isFocused?t.t("color1rgb"):null,v=t.isFocused?t.t("color1"):null,b=t.isFocused?t.t("color1rgb"):null;return{$orig:r,m47:n,m48:a,m49:i,m50:l,m51:c,m52:u,m53:s,m54:d,m55:m,m56:p,m57:f,m58:g,m59:h,m60:v,m61:b}})):null,q=t.t("color1"),N=t.t("color1rgb"),Z=t.isFocused?t.t("color1"):null,G=t.isFocused?t.t("color1rgb"):null,R=t.isFocused?t.t("color1"):null,H=t.isFocused?t.t("color1rgb"):null,J=t.isFocused?t.t("color1"):null,M=t.isFocused?t.t("color1rgb"):null,B=t.isFocused?t.t("color1"):null,U=t.isFocused?t.t("color1rgb"):null,z=t.__map(t.jobTypes,(function(e,o){var r=t.__get_orig(e),n=t.formData.jobType===e?t.t("color1rgb"):null,a=t.formData.jobType===e?t.t("color1"):null;return{$orig:r,m72:n,m73:a}})),K=t.isFocused?t.t("color1"):null,Q=t.isFocused?t.t("color1rgb"):null,V=t.steps.length,X=t.currentStep<V-1?t.t("color1"):null,Y=t.currentStep<V-1?t.t("color1rgb"):null,tt=t.steps.length,et=t.currentStep===tt-1?t.t("color1"):null,ot=t.currentStep===tt-1?t.t("color1rgb"):null;t._isMounted||(t.e0=function(e,o){var r=[],n=arguments.length-2;while(n-- >0)r[n]=arguments[n+2];var a=r[r.length-1].currentTarget.dataset,i=a.eventParams||a["event-params"];o=i.index;return t.onEduLevelChange(e,o)},t.e1=function(e,o){var r=[],n=arguments.length-2;while(n-- >0)r[n]=arguments[n+2];var a=r[r.length-1].currentTarget.dataset,i=a.eventParams||a["event-params"];o=i.index;return t.onEduStartChange(e,o)},t.e2=function(e,o){var r=[],n=arguments.length-2;while(n-- >0)r[n]=arguments[n+2];var a=r[r.length-1].currentTarget.dataset,i=a.eventParams||a["event-params"];o=i.index;return t.onEduEndChange(e,o)},t.e3=function(e,o){var r=[],n=arguments.length-2;while(n-- >0)r[n]=arguments[n+2];var a=r[r.length-1].currentTarget.dataset,i=a.eventParams||a["event-params"];o=i.index;return t.onWorkStartChange(e,o)},t.e4=function(e,o){var r=[],n=arguments.length-2;while(n-- >0)r[n]=arguments[n+2];var a=r[r.length-1].currentTarget.dataset,i=a.eventParams||a["event-params"];o=i.index;return t.onWorkEndChange(e,o)}),t.$mp.data=Object.assign({},{$root:{m0:o,m1:r,l0:n,m3:a,m4:i,m5:l,m6:c,m7:u,m8:s,m9:d,m10:m,m11:p,m12:f,m13:g,m14:h,m15:v,m16:b,m17:_,m18:D,m19:F,m20:x,m21:y,m22:w,m23:S,m24:E,m25:T,g0:k,l1:C,m41:j,m42:P,m43:O,m44:L,m45:A,m46:I,g1:W,l2:$,m62:q,m63:N,m64:Z,m65:G,m66:R,m67:H,m68:J,m69:M,m70:B,m71:U,l3:z,m74:K,m75:Q,g2:V,m76:X,m77:Y,g3:tt,m78:et,m79:ot}})},n=[]}},[["400c","common/runtime","common/vendor"]]]);