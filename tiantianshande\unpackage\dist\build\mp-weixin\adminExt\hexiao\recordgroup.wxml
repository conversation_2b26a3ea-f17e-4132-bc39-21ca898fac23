<view><block wx:if="{{isload}}"><block><view class="topsearch flex-y-center"><view class="f1 flex-y-center"><image class="img" src="{{pre_url+'/static/img/search_ico.png'}}"></image><input placeholder="{{'输入'+$root.m0+'昵称搜索'}}" placeholder-style="font-size:24rpx;color:#C2C2C2" data-event-opts="{{[['confirm',[['searchChange',['$event']]]],['input',[['__set_model',['','keyword','$event',[]]]]]]}}" value="{{keyword}}" bindconfirm="__e" bindinput="__e"/></view><block wx:if="{{showsearch}}"><image class="close" src="{{pre_url+'/static/img/close.png'}}" data-event-opts="{{[['tap',[['hideSearch',['$event']]]]]}}" bindtap="__e"></image></block></view><block wx:if="{{$root.g0}}"><view class="content"><block wx:for="{{$root.l2}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="yearitem"><view class="label" data-index="{{index}}" data-event-opts="{{[['tap',[['yearToggle',['$event']]]]]}}" bindtap="__e"><block wx:if="{{item.$orig.isshow}}"><image src="{{pre_url+'/static/img/location/down-dark.png'}}"></image></block><block wx:if="{{!item.$orig.isshow}}"><image src="{{pre_url+'/static/img/location/right-dark.png'}}"></image></block><text class="t1">{{item.$orig.name}}</text></view><block wx:for="{{item.l1}}" wx:for-item="item1" wx:for-index="index1" wx:key="index1"><block wx:if="{{item.$orig.isshow}}"><view class="monthbox"><view class="{{['month-title',item1.$orig.isshow?'on':'']}}" data-yindex="{{index}}" data-index="{{index1}}" data-event-opts="{{[['tap',[['monthToggle',['$event']]]]]}}" bindtap="__e"><block wx:if="{{!item1.$orig.isshow}}"><image src="{{pre_url+'/static/img/arrowright.png'}}"></image></block><block wx:if="{{item1.$orig.isshow}}"><image style="height:24rpx;width:24rpx;margin-right:6rpx;" src="{{pre_url+'/static/img/arrowdown.png'}}"></image></block><text>{{item1.$orig.name}}</text></view><block wx:if="{{item1.$orig.isshow}}"><view class="listitem"><scroll-view class="classify-box" scroll-y="true" data-yindex="{{index}}" data-index="{{index1}}" data-event-opts="{{[['scrolltolower',[['scrolltolower',['$event']]]]]}}" bindscrolltolower="__e"><block wx:for="{{item1.l0}}" wx:for-item="item2" wx:for-index="index2" wx:key="index2"><view class="item"><view class="f1"><image class="t1" src="{{item2.$orig.headimg}}"></image><text class="t2">{{item2.$orig.nickname}}</text></view><view class="f2"><text class="t1" style="color:#000;">{{item2.$orig.title}}</text><text class="t2">{{item2.m1}}</text><text class="t3">{{"备注："+item2.$orig.remark}}</text></view></view></block><block wx:if="{{nomore}}"><nomore vue-id="{{'4b884aec-1-'+index+'-'+index1}}" bind:__l="__l"></nomore></block></scroll-view></view></block></view></block></block></view></block></view></block><block wx:if="{{showsearch}}"><view class="searchbox content"><block wx:for="{{$root.l3}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="item"><view class="f1"><image class="t1" src="{{item.$orig.headimg}}"></image><text class="t2">{{item.$orig.nickname}}</text></view><view class="f2"><text class="t1" style="color:#000;">{{item.$orig.title}}</text><text class="t2">{{item.m2}}</text><text class="t3">{{"备注："+item.$orig.remark}}</text></view></view></block><block wx:if="{{nomores}}"><nomore vue-id="4b884aec-2" bind:__l="__l"></nomore></block><block wx:if="{{nodatas}}"><nomore vue-id="4b884aec-3" bind:__l="__l"></nomore></block></view></block></block></block><block wx:if="{{loading}}"><loading vue-id="4b884aec-4" bind:__l="__l"></loading></block><popmsg class="vue-ref" vue-id="4b884aec-5" data-ref="popmsg" bind:__l="__l"></popmsg></view>