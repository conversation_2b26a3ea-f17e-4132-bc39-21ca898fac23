<view style="width:100%;"><block wx:if="{{params.bgimg||params.main_title}}"><view class="line-container"><block wx:if="{{params.bgimg}}"><view class="line-bg" style="{{'background-image:'+('url('+params.bgimg+')')+';'}}"></view></block><block wx:if="{{params.main_title}}"><view class="header"><view class="title-box"><text class="main-title" style="{{'color:'+(params.main_title_color)+';'+('text-shadow:'+(params.main_title_color?'none':'0 2rpx 4rpx rgba(0, 0, 0, 0.2)')+';')}}">{{params.main_title}}</text><block wx:if="{{params.sub_title}}"><text class="sub-title" style="{{'color:'+(params.sub_title_color)+';'+('text-shadow:'+(params.sub_title_color?'none':'0 2rpx 4rpx rgba(0, 0, 0, 0.2)')+';')}}">{{params.sub_title}}</text></block></view><block wx:if="{{params.hrefurl}}"><view class="more-link" style="{{'color:'+(params.more_text_color||'#fff')+';'}}" data-url="{{params.hrefurl}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">{{''+(params.more_text||'查看更多')+''}}<text class="arrow">></text></view></block></view></block><view class="dp-product-itemline"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="id"><view class="item" style="{{'background-color:'+(probgcolor)+';'}}" data-url="{{'/shopPackage/shop/product?id='+item.$orig[idfield]}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="product-pic"><image class="image" src="{{item.$orig.pic}}" mode="widthFix"></image><block wx:if="{{saleimg!=''}}"><image class="saleimg" src="{{saleimg}}" mode="widthFix"></image></block></view><view class="product-info"><block wx:if="{{showname==1}}"><view class="p1">{{item.$orig.name}}</view></block><view class="p2"><block wx:if="{{showprice!='0'&&(item.$orig.price_type!=1||item.$orig.sell_price>0)}}"><view class="p2-1"><text class="t1" style="{{'color:'+(item.m0)+';'}}"><text style="font-size:24rpx;">￥</text>{{item.$orig.sell_price}}</text><block wx:if="{{showprice=='1'&&item.$orig.market_price*1>item.$orig.sell_price*1}}"><text class="t2">{{"￥"+item.$orig.market_price}}</text></block></view></block><block wx:if="{{item.$orig.xunjia_text&&item.$orig.price_type==1&&item.$orig.sell_price<=0}}"><view class="p2-1" style="height:50rpx;line-height:44rpx;"><text class="t1" style="{{'color:'+(item.m1)+';'+('font-size:'+('30rpx')+';')}}">询价</text><block wx:if="{{item.$orig.xunjia_text&&item.$orig.price_type==1}}"><block><view class="lianxi" style="{{'background:'+(item.m2)+';'}}" data-lx_name="{{item.$orig.lx_name}}" data-lx_bid="{{item.$orig.lx_bid}}" data-lx_bname="{{item.$orig.lx_bname}}" data-lx_tel="{{item.$orig.lx_tel}}" data-btntype="2" data-event-opts="{{[['tap',[['showLinkChange',['$event']]]]]}}" catchtap="__e">{{item.$orig.xunjia_text?item.$orig.xunjia_text:'联系TA'}}</view></block></block></view></block></view><block wx:if="{{item.$orig.merchant_name}}"><view class="p1" style="color:#666;font-size:24rpx;white-space:nowrap;text-overflow:ellipsis;margin-top:6rpx;height:30rpx;line-height:30rpx;font-weight:normal;"><text>{{item.$orig.merchant_name}}</text></view></block><block wx:if="{{item.$orig.main_business}}"><view class="p1" style="color:#666;font-size:24rpx;margin-top:4rpx;font-weight:normal;"><text>{{item.$orig.main_business}}</text></view></block><block wx:if="{{showsales=='1'&&item.$orig.sales>0}}"><view class="p3">{{"已售"+item.$orig.sales+"件"}}</view></block><block wx:if="{{(showsales!='1'||item.$orig.sales<=0)&&item.$orig.main_business}}"><view style="height:44rpx;"></view></block><block wx:if="{{showcart==1&&!item.$orig.price_type}}"><view class="p4" style="{{'background:'+('rgba('+item.m3+',0.1)')+';'+('color:'+(item.m4)+';')}}" data-proid="{{item.$orig[idfield]}}" data-event-opts="{{[['tap',[['buydialogChange',['$event']]]]]}}" catchtap="__e"><text class="iconfont icon_gouwuche"></text></view></block><block wx:if="{{showcart==2&&!item.$orig.price_type}}"><view class="p4" style="{{'background:'+('rgba('+item.m5+',0.1)')+';'+('color:'+(item.m6)+';')}}" data-proid="{{item.$orig[idfield]}}" data-event-opts="{{[['tap',[['buydialogChange',['$event']]]]]}}" catchtap="__e"><image class="img" src="{{cartimg}}"></image></view></block></view></view></block></view></view></block><block wx:else><view class="dp-product-itemline"><block wx:for="{{$root.l1}}" wx:for-item="item" wx:for-index="index" wx:key="id"><view class="item" style="{{'background-color:'+(probgcolor)+';'}}" data-url="{{'/shopPackage/shop/product?id='+item.$orig[idfield]}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="product-pic"><image class="image" src="{{item.$orig.pic}}" mode="widthFix"></image><block wx:if="{{saleimg!=''}}"><image class="saleimg" src="{{saleimg}}" mode="widthFix"></image></block></view><view class="product-info"><block wx:if="{{showname==1}}"><view class="p1">{{item.$orig.name}}</view></block><view class="p2"><block wx:if="{{showprice!='0'&&(item.$orig.price_type!=1||item.$orig.sell_price>0)}}"><view class="p2-1"><text class="t1" style="{{'color:'+(item.m7)+';'}}"><text style="font-size:24rpx;">￥</text>{{item.$orig.sell_price}}</text><block wx:if="{{showprice=='1'&&item.$orig.market_price*1>item.$orig.sell_price*1}}"><text class="t2">{{"￥"+item.$orig.market_price}}</text></block></view></block><block wx:if="{{item.$orig.xunjia_text&&item.$orig.price_type==1&&item.$orig.sell_price<=0}}"><view class="p2-1" style="height:50rpx;line-height:44rpx;"><text class="t1" style="{{'color:'+(item.m8)+';'+('font-size:'+('30rpx')+';')}}">询价</text><block wx:if="{{item.$orig.xunjia_text&&item.$orig.price_type==1}}"><block><view class="lianxi" style="{{'background:'+(item.m9)+';'}}" data-lx_name="{{item.$orig.lx_name}}" data-lx_bid="{{item.$orig.lx_bid}}" data-lx_bname="{{item.$orig.lx_bname}}" data-lx_tel="{{item.$orig.lx_tel}}" data-btntype="2" data-event-opts="{{[['tap',[['showLinkChange',['$event']]]]]}}" catchtap="__e">{{item.$orig.xunjia_text?item.$orig.xunjia_text:'联系TA'}}</view></block></block></view></block></view><block wx:if="{{item.$orig.merchant_name}}"><view class="p1" style="color:#666;font-size:24rpx;white-space:nowrap;text-overflow:ellipsis;margin-top:6rpx;height:30rpx;line-height:30rpx;font-weight:normal;"><text>{{item.$orig.merchant_name}}</text></view></block><block wx:if="{{item.$orig.main_business}}"><view class="p1" style="color:#666;font-size:24rpx;margin-top:4rpx;font-weight:normal;"><text>{{item.$orig.main_business}}</text></view></block><block wx:if="{{showsales=='1'&&item.$orig.sales>0}}"><view class="p3">{{"已售"+item.$orig.sales+"件"}}</view></block><block wx:if="{{(showsales!='1'||item.$orig.sales<=0)&&item.$orig.main_business}}"><view style="height:44rpx;"></view></block><block wx:if="{{showcart==1&&!item.$orig.price_type}}"><view class="p4" style="{{'background:'+('rgba('+item.m10+',0.1)')+';'+('color:'+(item.m11)+';')}}" data-proid="{{item.$orig[idfield]}}" data-event-opts="{{[['tap',[['buydialogChange',['$event']]]]]}}" catchtap="__e"><text class="iconfont icon_gouwuche"></text></view></block><block wx:if="{{showcart==2&&!item.$orig.price_type}}"><view class="p4" style="{{'background:'+('rgba('+item.m12+',0.1)')+';'+('color:'+(item.m13)+';')}}" data-proid="{{item.$orig[idfield]}}" data-event-opts="{{[['tap',[['buydialogChange',['$event']]]]]}}" catchtap="__e"><image class="img" src="{{cartimg}}"></image></view></block></view></view></block></view></block><block wx:if="{{buydialogShow}}"><buydialog vue-id="06bc9134-1" proid="{{proid}}" menuindex="{{menuindex}}" data-event-opts="{{[['^addcart',[['addcart']]],['^buydialogChange',[['buydialogChange']]]]}}" bind:addcart="__e" bind:buydialogChange="__e" bind:__l="__l"></buydialog></block><block wx:if="{{showLinkStatus}}"><view class="posterDialog linkDialog"><view class="main"><view data-event-opts="{{[['tap',[['showLinkChange',['$event']]]]]}}" class="close" bindtap="__e"><image class="img" src="/static/img/close.png"></image></view><view class="content"><view class="title">{{lx_name}}</view><block wx:if="{{lx_bid>0}}"><view class="row"><view class="f1">店铺名称</view><view class="f2" data-url="{{'/pagesExt/business/index?id='+lx_bid}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">{{lx_bname}}<image class="image" src="/static/img/arrowright.png"></image></view></view></block><block wx:if="{{lx_tel}}"><view class="row"><view class="f1">联系电话</view><view class="f2" style="{{'color:'+($root.m14)+';'}}" data-url="{{'tel::'+lx_tel}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">{{lx_tel}}<image class="copyicon" src="/static/img/copy.png" data-text="{{lx_tel}}" data-event-opts="{{[['tap',[['copy',['$event']]]]]}}" catchtap="__e"></image></view></view></block></view></view></view></block></view>