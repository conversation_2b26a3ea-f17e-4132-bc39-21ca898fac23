<view class="container"><block wx:if="{{isload}}"><block><block wx:if="{{style==1}}"><view class="qd_head"><block wx:if="{{signset.bgpic}}"><block><image class="qdbg" src="{{signset.bgpic}}"></image></block></block><block wx:else><block><image class="qdbg" src="{{pre_url+'/static/img/sign-bg.png'}}"></image></block></block><view class="myscore"><view class="f2"><view class="f2">{{"今日签到分红:"+userinfo.qiandaofh}}</view></view><view class="f2"><view class="f2">{{"签到分红总计:"+userinfo.qiandaocount}}</view></view></view><view class="signlog" data-url="signrecord" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">签到记录</view><block wx:if="{{!hassign}}"><view class="signbtn"><button data-event-opts="{{[['tap',[['signin',['$event']]]]]}}" class="btn" style="{{'background:'+($root.m0)+';'}}" bindtap="__e">立即签到</button></view></block><block wx:else><view class="signbtn"><button class="btn2">今日已签到</button><view class="signtip">{{"已连续签到"+userinfo.signtimeslx+"天"}}</view></view></block></view></block><block wx:if="{{style==2}}"><view class="qd_head qd_head2" style="{{('background-image:url('+signset.bgpic+');')}}"><view class="signlog" data-url="signrecord" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">签到记录</view><block wx:if="{{!hassign}}"><view class="signbtn"><button data-event-opts="{{[['tap',[['signin',['$event']]]]]}}" class="btn" style="{{'background:'+($root.m1)+';'}}" bindtap="__e">立即签到</button><view class="signtip">{{"当前共"+userinfo.score+$root.m2}}</view></view></block><block wx:else><view class="signbtn"><button class="btn2">今日已签到</button><view class="signtip">{{"已连续签到"+userinfo.signtimeslx+"天，共"+userinfo.score+$root.m3}}</view></view></block><view class="calendar"><uni-calendar vue-id="84a8c1dc-1" insert="{{true}}" lunar="{{false}}" start-date="{{start_date}}" end-date="{{end_date}}" selected="{{selectedDate}}" showMonth="{{false}}" backColor="{{$root.m4}}" fontColor="#fff" bind:__l="__l"></uni-calendar></view></view></block><view class="qd_guize"><view class="gztitle">— 签到排名 —</view><view class="paiming"><block wx:for="{{list}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="item flex"><view class="f1"><text class="t1">{{item.nickname}}</text></view><view class="f2"><text class="t2">连续签到</text><text class="t1">{{item.signtimeslx}}</text><text class="t2">天</text></view></view></block></view><block wx:if="{{$root.g0}}"><view data-event-opts="{{[['tap',[['getPaiming',['$event']]]]]}}" class="btn-a" style="{{'color:'+($root.m5)+';'}}" bindtap="__e">查看更多</view></block><block wx:if="{{nomore}}"><nomore vue-id="84a8c1dc-2" bind:__l="__l"></nomore></block></view><view class="qd_guize"><view class="gztitle">— 签到规则 —</view><view class="guize_txt"><parse vue-id="84a8c1dc-3" content="{{signset.guize}}" bind:__l="__l"></parse></view></view></block></block><block wx:if="{{loading}}"><loading vue-id="84a8c1dc-4" bind:__l="__l"></loading></block><dp-tabbar vue-id="84a8c1dc-5" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="84a8c1dc-6" data-ref="popmsg" bind:__l="__l"></popmsg></view>