<view><view><block wx:if="{{loading}}"><loading vue-id="52598430-1" bind:__l="__l"></loading></block><page-meta pageStyle="{{'overflow: '+(listFilterVisitor?'hidden':'auto')}}"></page-meta><view class="container"><block wx:if="{{firstLoaded}}"><view class="select-job-area"><view class="{{['gradient-bg fixed white']}}" style="{{('top: 0px; z-index: 999;')}}"><tab class="vue-ref" style="{{('top: 0px; z-index: 999;')}}" vue-id="52598430-2" tabList="{{tabList}}" current="{{jobItemType}}" data-ref="jobTab" data-event-opts="{{[['^tabChange',[['tabChange']]]]}}" bind:tabChange="__e" bind:__l="__l"></tab><block wx:if="{{showFilter}}"><view><list-filter class="filterRef" vue-id="52598430-3" areas="{{areas}}" className="index-filter" clearingList="{{clearingForms}}" controlPop="{{controlPop}}" jobtypeList="{{classifications}}" ptpId="{{tabList[jobItemType].key}}" sortRules="{{sortRules}}" typeIndex="{{jobItemType}}" userSex="{{userSex}}" data-event-opts="{{[['^change',[['filterChange']]],['^visitorChange',[['e0']]]]}}" bind:change="__e" bind:visitorChange="__e" bind:__l="__l"></list-filter></view></block></view><swiper class="swiper-container" style="{{'height:'+('calc(100vh - '+(showFilter?'206rpx':'106rpx')+')')+';'}}" current="{{jobItemType}}" data-event-opts="{{[['change',[['handleSwiperChange',['$event']]]]]}}" bindchange="__e"><block wx:for="{{$root.l0}}" wx:for-item="tab" wx:for-index="index" wx:key="index"><swiper-item><scroll-view class="{{['school-life-job-list '+(isFixed&&showFilter?'padding206':isFixed&&!showFilter?'padding106':tabList[jobItemType].key!=='1'?'padding16':'')+' '+(tabList[jobItemType].key==='4'?'padding0':'')]}}" scroll-y="{{true}}" data-event-opts="{{[['scrolltolower',[['onReachBottom',['$event']]]]]}}" bindscrolltolower="__e"><block wx:if="{{tabList[jobItemType].key==='3'&&showGuide}}"><position-guide bind:handleTapGuide="__e" vue-id="{{'52598430-4-'+index}}" data-event-opts="{{[['^handleTapGuide',[['locationHandle']]]]}}" bind:__l="__l"></position-guide></block><block wx:for="{{homeList}}" wx:for-item="item" wx:for-index="idx" wx:key="idx"><regular-item vue-id="{{'52598430-5-'+index+'-'+idx}}" data="{{item}}" index="{{idx}}" listIndex="{{tab.m0}}" ptpId="{{'jfb2-dn2b-dn1b-718d-'+tabList[jobItemType].key}}" type="{{jobItemType}}" data-event-opts="{{[['^btnFresh',[['btFresh']]]]}}" bind:btnFresh="__e" bind:__l="__l"></regular-item></block><block wx:if="{{tab.g0}}"><view class="noneList"><image lazyLoad="{{true}}" mode="widthFix" src="{{pre_url+'/static/img/wuzhaopin.png'}}"></image><text>此时内心是空荡荡的</text></view></block></scroll-view></swiper-item></block></swiper></view></block></view><dp-tabbar vue-id="52598430-6" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="52598430-7" data-ref="popmsg" bind:__l="__l"></popmsg></view></view>