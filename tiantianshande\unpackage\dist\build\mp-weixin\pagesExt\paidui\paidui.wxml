<view class="container"><block wx:if="{{isload}}"><block><view class="banner" style="{{'background:'+($root.m0)+';'}}"><image src="{{userinfo.headimg}}"></image><view class="info" style="line-height:120rpx;padding-top:0;"><text class="nickname" style="color:azure;">{{userinfo.nickname}}</text></view></view><view class="contentdata"><view class="order"><view class="head"><text class="f1">排队数据看板</text><view data-event-opts="{{[['tap',[['topay',['$event']]]]]}}" class="f2" bindtap="__e"><text>红包转至余额</text><image src="/static/img/arrowright.png"></image></view><view data-event-opts="{{[['tap',[['showDataFixMenu',['$event']]]]]}}" class="f3" bindtap="__e"><text>数据修复</text></view></view><view class="content2"><view class="item" style="border:none;"><text class="t1">{{"￥"+userinfo.yingde}}</text><text class="t3">应补贴红包</text></view><view class="item" style="border:none;"><text class="t1">{{"￥"+userinfo.leiji}}</text><text class="t3">合计补贴红包</text></view></view><view class="content2"><view class="item" style="border:none;"><text class="t1">{{"￥"+userinfo.daizhuan}}</text><text class="t3">可转至余额</text></view><view class="item" style="border:none;"><text class="t1">{{userinfo.zhuanhua}}</text><text class="t3">已转至余额</text></view></view></view></view><block wx:if="{{set.endtime.remark!=0}}"><view class="container3"><view class="card"><view class="content23"><text class="discount-text">活动结束时间:<text class="highlight">{{set.endtime.endtime}}</text><text data-event-opts="{{[['tap',[['showExplanation',['$event']]]]]}}" class="question-icon" bindtap="__e">?</text></text></view></view><block wx:if="{{showModal}}"><view data-event-opts="{{[['tap',[['closeExplanation',['$event']]]]]}}" class="modal" bindtap="__e"><view data-event-opts="{{[['tap',[['',['$event']]]]]}}" class="modal-content" catchtap="__e"><text data-event-opts="{{[['tap',[['closeExplanation',['$event']]]]]}}" class="close" bindtap="__e">×</text><text>{{set.endtime.remark}}</text></view></view></block></view></block><dd-tab vue-id="4e780fbb-1" itemdata="{{[$root.m1,$root.m2]}}" itemst="{{['1','2']}}" st="{{st}}" isfixed="{{false}}" data-event-opts="{{[['^changetab',[['changetab']]]]}}" bind:changetab="__e" bind:__l="__l"></dd-tab><dd-tab vue-id="4e780fbb-2" itemdata="{{[$root.m3,$root.m4,$root.m5,$root.m6]}}" itemst="{{['1','2','3','4']}}" st="{{st1}}" isfixed="{{false}}" data-event-opts="{{[['^changetab',[['changetab2']]]]}}" bind:changetab="__e" bind:__l="__l"></dd-tab><view class="order-content"><block wx:if="{{$root.g0}}"><block><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="content"><view class="order-box"><view class="head"><view class="f1"><image src="/static/img/ico-shop.png"></image>{{item.$orig.bunessname}}</view><view class="flex1"></view><text class="st4"><block wx:if="{{item.$orig.status==0}}"><block><text class="t1" style="{{'color:'+(item.m7)+';'}}">{{item.$orig.statusname}}</text></block></block><block wx:if="{{item.$orig.status==1}}"><block><text class="t1" style="{{'color:'+(item.m8)+';'}}">{{item.$orig.statusname}}</text></block></block><block wx:if="{{item.$orig.status==2}}"><block><text class="t1" style="{{'color:'+(item.m9)+';'}}">{{item.$orig.statusname}}</text></block></block></text></view><block><view class="content"><block wx:if="{{item.$orig.qu==0}}"><view data-url="{{'/shopPackage/shop/product?id='+item.$orig.proid}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e"><image src="{{item.$orig.pic}}"></image></view></block><block wx:if="{{item.$orig.qu==1}}"><view><image src="{{item.$orig.pic}}"></image></view></block><block wx:if="{{item.$orig.name}}"><view class="detail"><text class="t1">{{item.$orig.name}}</text><text class="t2" data-url="{{'/pagesExt/order/detail?id='+item.$orig.orderid}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e">{{"排队时间："+item.m10}}</text><view class="t3"><text class="x1 flex1">{{"￥"+item.$orig.totalprice}}</text><text class="x2">{{"×"+item.$orig.num}}</text></view></view></block></view></block><view class="info-row"><view class="info-item" style="border:none;"><text class="amount">{{item.g1+"%"}}</text><text class="label">补贴比例</text></view><view class="info-item" style="border:none;"><text class="amount">{{"￥"+item.$orig.paidui_jiang}}</text><text class="label">应补贴红包</text></view><view class="info-item" style="border:none;"><text class="amount">{{"￥"+item.$orig.shiji_dedao}}</text><text class="label">已补贴红包</text></view></view><block wx:if="{{item.$orig.status==0}}"><view class="footer"><text class="date pw">{{"目前排位："+item.$orig.qiancount}}</text><block wx:if="{{userinfo.duijifen==1||userinfo.duiyue==1||userinfo.duichoujiang==1}}"><text data-event-opts="{{[['tap',[['showModal2',['$event']]]]]}}" class="action" bindtap="__e">结束排队，领取补贴 ></text></block></view></block><block wx:if="{{modalVisible}}"><view class="modal-overlay"><view class="modal-content"><block wx:if="{{!selectedOption}}"><view><text class="modal-title">选择结束排队的原因</text><block wx:for="{{options}}" wx:for-item="option" wx:for-index="__i0__" wx:key="value"><view data-event-opts="{{[['tap',[['selectOption2',['$0','$1','$2','$3','$4','$5'],[[['options','value',option.value]],[['datalist','',index,'id']],[['options','value',option.value,'value']],[['datalist','',index,'butieyue']],[['datalist','',index,'butiejifen']],[['datalist','',index,'butieduijiang']]]]]]]}}" class="option" bindtap="__e"><text>{{option.text}}</text></view></block></view></block><block wx:if="{{selectedOption}}"><view class="modal-info"><text class="modal-title">结束排队，领取补贴</text><text class="modal-amount">{{selectedOption.amount}}</text><view class="modal-text"><text>{{selectedOption.description}}</text></view><button data-event-opts="{{[['tap',[['confirmEndQueue',['$0'],[[['datalist','',index,'id']]]]]]]}}" class="modal-button confirm" bindtap="__e">确认结束领取补贴</button><button data-event-opts="{{[['tap',[['backToOptions',['$event']]]]]}}" class="modal-button back" bindtap="__e">返回选择</button></view></block><button data-event-opts="{{[['tap',[['hideModal',['$event']]]]]}}" class="modal-button continue" bindtap="__e">继续排队等待补贴 ></button></view></view></block><block wx:if="{{st1==4}}"><view class="op"><text class="t1" style="font-weight:bold;">{{"商家排队队列数量："+item.$orig.duilicount}}</text></view></block></view></view></block></block></block></view></block></block><block wx:if="{{nodata}}"><nodata vue-id="4e780fbb-3" bind:__l="__l"></nodata></block><block wx:if="{{nomore}}"><nomore vue-id="4e780fbb-4" bind:__l="__l"></nomore></block><block wx:if="{{loading}}"><loading vue-id="4e780fbb-5" bind:__l="__l"></loading></block><dp-tabbar vue-id="4e780fbb-6" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="4e780fbb-7" data-ref="popmsg" bind:__l="__l"></popmsg></view>