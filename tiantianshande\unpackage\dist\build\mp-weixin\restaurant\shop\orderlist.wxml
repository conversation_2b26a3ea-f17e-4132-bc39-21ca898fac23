<view class="container"><block wx:if="{{isload}}"><block><dd-tab vue-id="50802042-1" itemdata="{{['全部','待付款','已付款','配送中','已完成','退款']}}" itemst="{{['all','0','1','12','2','3','10']}}" st="{{st}}" isfixed="{{true}}" data-event-opts="{{[['^changetab',[['changetab']]]]}}" bind:changetab="__e" bind:__l="__l"></dd-tab><view class="order-content"><block wx:for="{{$root.l1}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><view class="order-box" data-url="{{'orderdetail?id='+item.$orig.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="head"><block wx:if="{{item.$orig.bid!=0}}"><view class="f1" data-url="{{'/restaurant/shop/index?bid='+item.$orig.bid}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e"><image src="/static/img/ico-shop.png"></image>{{''+item.$orig.binfo.name}}</view></block><block wx:else><view>{{"订单号："+item.$orig.ordernum}}</view></block><view class="flex1"></view><block wx:if="{{item.$orig.status==0}}"><text class="st0">待付款</text></block><block wx:if="{{item.$orig.status==1&&item.$orig.paytypeid!=4}}"><text class="st1">已付款</text></block><block wx:if="{{item.$orig.status==1&&item.$orig.paytypeid==4}}"><text class="st1">线下付款</text></block><block wx:if="{{item.$orig.status==12}}"><text class="st1">已接单</text></block><block wx:if="{{item.$orig.status==2}}"><text class="st2">配送中</text></block><block wx:if="{{item.$orig.status==3}}"><text class="st3">已完成</text></block><block wx:if="{{item.$orig.status==4}}"><text class="st4">已关闭</text></block></view><block wx:for="{{item.l0}}" wx:for-item="item2" wx:for-index="idx" wx:key="idx"><block><view class="content" style="{{(idx+1==item.$orig.procount?'border-bottom:none':'')}}"><view><image src="{{item2.$orig.pic}}"></image></view><view class="detail"><text class="t1">{{item2.$orig.name}}</text><text class="t2">{{item2.$orig.ggname+(item2.$orig.jltitle?item2.$orig.jltitle:'')}}</text><view class="t3"><block wx:if="{{item2.$orig.jlprice}}"><text class="x1 flex1">{{"￥"+item2.g0}}</text></block><block wx:else><text class="x1 flex1">{{"￥"+item2.$orig.sell_price}}</text></block><text class="x2">{{"×"+item2.$orig.num}}</text></view></view></view></block></block><view class="bottom"><text>{{"共计"+item.$orig.procount+"件商品 实付:￥"+item.$orig.totalprice}}</text><block wx:if="{{item.$orig.refund_status==1}}"><text style="color:red;">{{'退款中￥'+item.$orig.refund_money}}</text></block><block wx:if="{{item.$orig.refund_status==2}}"><text style="color:red;">{{'已退款￥'+item.$orig.refund_money}}</text></block><block wx:if="{{item.$orig.refund_status==3}}"><text style="color:red;">退款申请已驳回</text></block></view><view class="op"><view class="btn2" data-url="{{'orderdetail?id='+item.$orig.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e">详情</view><block wx:if="{{item.$orig.status==0}}"><block><view class="btn1" style="{{'background:'+(item.m0)+';'}}" data-url="{{'index?tableId='+item.$orig.tableid+'&bid='+item.$orig.bid}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e">加菜</view><view class="btn1" style="{{'background:'+(item.m1)+';'}}" data-url="{{'/pages/pay/pay?id='+item.$orig.payorderid}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e">去付款</view></block></block><block wx:if="{{item.$orig.status==12||item.$orig.status==2}}"><block><block wx:if="{{item.$orig.refund_status==0||item.$orig.refund_status==3}}"><view class="btn2" data-url="{{'refund?orderid='+item.$orig.id+'&price='+item.$orig.totalprice}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e">申请退款</view></block></block></block><block wx:if="{{item.$orig.status==2}}"><block><block wx:if="{{item.$orig.paytypeid!='4'}}"><view class="btn1" style="{{'background:'+(item.m2)+';'}}" data-id="{{item.$orig.id}}" data-event-opts="{{[['tap',[['orderCollect',['$event']]]]]}}" catchtap="__e">确认收货</view></block></block></block><block wx:if="{{item.$orig.status==3}}"><block><block wx:if="{{item.$orig.iscommentdp==0}}"><view class="btn1" style="{{'background:'+(item.m3)+';'}}" data-url="{{'commentdp?orderid='+item.$orig.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e">评价店铺</view></block></block></block></view></view></block></block></view><block wx:if="{{nomore}}"><nomore vue-id="50802042-2" bind:__l="__l"></nomore></block><block wx:if="{{nodata}}"><nodata vue-id="50802042-3" bind:__l="__l"></nodata></block></block></block><block wx:if="{{loading}}"><loading vue-id="50802042-4" bind:__l="__l"></loading></block><dp-tabbar vue-id="50802042-5" opt="{{opt}}" bind:__l="__l"></dp-tabbar></view>