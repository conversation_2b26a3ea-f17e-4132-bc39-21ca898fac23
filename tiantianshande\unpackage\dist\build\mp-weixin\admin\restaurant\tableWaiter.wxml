<view class="container"><block wx:if="{{isload}}"><block><view class="search-container"><view class="search-box"><image class="img" src="/static/img/search_ico.png"></image><input class="search-text" placeholder="输入关键词或餐桌号码" placeholder-style="color:#aaa;font-size:24rpx" data-event-opts="{{[['confirm',[['searchConfirm',['$event']]]],['input',[['searchChange',['$event']]]]]}}" value="{{keyword}}" bindconfirm="__e" bindinput="__e"/></view></view><view class="tab-box shopping"><view class="page-tab"><view class="page-tab2"><view class="{{['item '+(curTopIndex==-1?'on':'')]}}" data-index="{{-1}}" data-id="{{0}}" data-event-opts="{{[['tap',[['switchTopTab',['$event']]]]]}}" bindtap="__e">全部</view><block wx:for="{{clist}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><view class="{{['item '+(curTopIndex==index?'on':'')]}}" data-index="{{index}}" data-id="{{item.id}}" data-event-opts="{{[['tap',[['switchTopTab',['$event']]]]]}}" bindtap="__e">{{item.name}}</view></block></block></view></view><view class="table-box"><block wx:for="{{contentList}}" wx:for-item="item" wx:for-index="index2" wx:key="index2"><view class="{{['table-item','bg-green',(item.status==2)?'bg-orange':'',(item.status==3)?'bg-blue':'']}}" data-id="{{item.id}}" data-tablename="{{item.name}}" data-event-opts="{{[['tap',[['selectTable',['$event']]]]]}}" bindtap="__e"><view class="shop-name multi-ellipsis-2">{{''+item.name+''}}</view><view class="color-fff line40">{{"座位："+item.seat}}</view><block wx:if="{{item.status==0}}"><view class="color-fff">空闲</view></block><block wx:if="{{item.status==2}}"><view class="color-fff">用餐</view></block><block wx:if="{{item.status==3}}"><view class="color-fff">清台</view></block></view></block></view></view></block></block><block wx:if="{{loading}}"><loading vue-id="1e96648c-1" bind:__l="__l"></loading></block><dp-tabbar vue-id="1e96648c-2" opt="{{opt}}" bind:__l="__l"></dp-tabbar></view>