<view class="container"><block wx:if="{{isload}}"><block><view class="content"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="item"><view class="header"><view class="left"><text class="remark">{{item.$orig.remark}}</text><view class="sub-info"><text>{{"订单号:"+item.$orig.orderid}}</text><text class="dot">·</text><text>{{item.m0}}</text></view></view><view class="right"><text class="{{['money',item.$orig.money>0?'income':'expense']}}">{{''+(item.$orig.money>0?'+':'')+item.$orig.money+''}}</text><text class="balance">{{"余额:"+item.$orig.after}}</text></view></view><block wx:if="{{item.$orig.deduct_money!='0'||item.$orig.afterusertotal!='0'}}"><view class="info"><block wx:if="{{item.$orig.deduct_money!='0'}}"><text>{{'扣除:'+item.$orig.deduct_money+''}}<text class="reason">{{"("+item.$orig.deduct_reason+")"}}</text></text></block><block wx:if="{{item.$orig.afterusertotal!='0'}}"><text>{{"用户总额:"+item.$orig.afterusertotal}}</text></block></view></block></view></block></view><block wx:if="{{nomore}}"><nomore vue-id="b51b9e5a-1" bind:__l="__l"></nomore></block><block wx:if="{{nodata}}"><nodata vue-id="b51b9e5a-2" bind:__l="__l"></nodata></block></block></block><block wx:if="{{loading}}"><loading vue-id="b51b9e5a-3" bind:__l="__l"></loading></block><dp-tabbar vue-id="b51b9e5a-4" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="b51b9e5a-5" data-ref="popmsg" bind:__l="__l"></popmsg></view>