<view class="container" style="{{(form.form_query==1&&form.form_query_bgcolor?'min-height:100vh;background-color:'+form.form_query_bgcolor:'')}}"><block wx:if="{{isload}}"><block><view class="orderinfo" style="{{(form.form_query==1&&form.form_query_bgcolor?'background-color:'+form.form_query_bgcolor+';color:'+form.form_query_txtcolor+'!important':'')}}"><view class="item"><text class="t1">标题</text><text class="t2">{{detail.title}}</text></view><block wx:for="{{formcontent}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="item"><text class="{{['t1',item.key=='separate'?'title':'']}}">{{item.val1}}</text><block wx:if="{{item.key!='upload'&&item.key!='upload_file'}}"><text class="t2">{{detail['form'+index]}}</text></block><block wx:if="{{item.key=='upload'}}"><view class="t2"><image style="width:50px;" src="{{detail['form'+index]}}" mode="widthFix" data-url="{{detail['form'+index]}}" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image></view></block><block wx:if="{{item.key=='upload_file'}}"><view class="t2" style="overflow:hidden;white-space:pre-wrap;word-wrap:break-word;color:#4786BC;" data-file="{{detail['form'+index]}}" data-event-opts="{{[['tap',[['download',['$event']]]]]}}" bindtap="__e">{{''+detail['form'+index]+''}}</view></block></view></block><block wx:if="{{detail.is_other_fee==1}}"><view class="feebox"><text class="title">费用明细</text><view class="feelist"><block wx:for="{{detail.fee_items}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="feeitem"><view>{{item.name}}</view><view class="price">{{"￥"+item.money}}</view></view></block></view></view></block><view class="item"><text class="t1">提交时间</text><text class="t2">{{detail.createtime}}</text></view><view class="item"><text class="t1">审核状态</text><block wx:if="{{detail.status==0&&(!detail.payorderid||detail.paystatus==1)}}"><text class="t2" style="color:#88e;">待确认</text></block><block wx:if="{{detail.status==0&&detail.payorderid&&detail.paystatus==0}}"><text class="t2" style="color:red;">待支付</text></block><block wx:if="{{detail.status==1}}"><text class="t2" style="color:green;">已确认</text></block><block wx:if="{{detail.status==2}}"><text class="t2" style="color:red;">已驳回</text></block></view><block wx:if="{{detail.status==2}}"><view class="item"><text class="t1">驳回原因</text><text class="t2" style="color:red;">{{detail.reason}}</text></view></block><block wx:if="{{detail.payorderid}}"><block><view class="item"><text class="t1">付款金额</text><text class="t2" style="font-size:32rpx;color:#e94745;">{{"￥"+detail.money}}</text></view><view class="item"><text class="t1">付款方式</text><text class="t2">{{detail.paytype||''}}</text></view><view class="item"><text class="t1">付款状态</text><block wx:if="{{detail.paystatus==1&&detail.isrefund==0}}"><text class="t2" style="color:green;">已付款</text></block><block wx:if="{{detail.paystatus==1&&detail.isrefund==1}}"><text class="t2" style="color:red;">已退款</text></block><block wx:if="{{detail.paystatus==0}}"><text class="t2" style="color:red;">未付款</text></block></view><block wx:if="{{detail.paystatus>0&&detail.paytime}}"><view class="item"><text class="t1">付款时间</text><text class="t2">{{detail.paytime}}</text></view></block></block></block></view><view style="width:100%;height:160rpx;"></view><block wx:if="{{opt.op!='view'}}"><view class="bottom notabbarbot"><view class="btn2" data-id="{{detail.id}}" data-event-opts="{{[['tap',[['todel',['$event']]]]]}}" bindtap="__e">删除</view><block wx:if="{{detail.fromurl&&detail.edit_status}}"><view class="btn2" data-url="{{detail.fromurl+'&fromrecord='+detail.id+'&type=edit'}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">{{detail.edit_name?detail.edit_name:'编辑'}}</view></block><block wx:if="{{detail.fromurl}}"><view class="btn2" data-url="{{detail.fromurl+'&fromrecord='+detail.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">{{againname}}</view></block><block wx:if="{{detail.payorderid&&detail.paystatus==0}}"><block><view class="btn1" style="{{'background:'+($root.m0)+';'}}" data-url="{{'/pages/pay/pay?id='+detail.payorderid}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">去付款</view></block></block></view></block></block></block><block wx:if="{{loading}}"><loading vue-id="557c64dc-1" bind:__l="__l"></loading></block><dp-tabbar vue-id="557c64dc-2" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="557c64dc-3" data-ref="popmsg" bind:__l="__l"></popmsg></view>