<view class="container"><block wx:if="{{isload}}"><block><view class="form"><picker value="{{statusIndex}}" range="{{statusList}}" data-event-opts="{{[['change',[['bindPickerChange',['$event']]]]]}}" bindchange="__e"><view class="picker-item"><view class="label">情感状态</view><view class="value">{{statusList[statusIndex]}}</view><image class="arrow" src="/static/img/arrowright.png"></image></view></picker></view><button data-event-opts="{{[['tap',[['saveStatus',['$event']]]]]}}" class="set-btn" style="{{'background:'+('linear-gradient(90deg,'+$root.m0+' 0%,rgba('+$root.m1+',0.8) 100%)')+';'}}" bindtap="__e">保 存</button></block></block><block wx:if="{{loading}}"><loading vue-id="0e22ac96-1" bind:__l="__l"></loading></block><dp-tabbar vue-id="0e22ac96-2" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="0e22ac96-3" data-ref="popmsg" bind:__l="__l"></popmsg></view>