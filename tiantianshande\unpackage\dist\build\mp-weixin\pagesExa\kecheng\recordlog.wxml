<view class="container"><block wx:if="{{isload}}"><block><view class="order-content"><block wx:for="{{datalist}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><view class="order-box" data-url="{{'complete?rid='+item.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e"><view class="content flex"><view class="detail"><text class="t1">{{item.title}}</text><text class="t2">{{item.date}}</text></view><view class="score"><text class="t3">{{item.score}}</text><text class="t4">分</text></view></view></view></block></block></view><block wx:if="{{nomore}}"><nomore vue-id="34fae7cc-1" bind:__l="__l"></nomore></block><block wx:if="{{nodata}}"><nodata vue-id="34fae7cc-2" bind:__l="__l"></nodata></block></block></block><block wx:if="{{loading}}"><loading vue-id="34fae7cc-3" bind:__l="__l"></loading></block><dp-tabbar vue-id="34fae7cc-4" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="34fae7cc-5" data-ref="popmsg" bind:__l="__l"></popmsg></view>