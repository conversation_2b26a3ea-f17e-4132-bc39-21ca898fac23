<view class="container" style="{{'background-color:'+(pageinfo.bgcolor)+';'}}"><block wx:if="{{showSplash}}"><view class="splash-overlay"><view class="splash-container"><block wx:if="{{splashConfig.display_type==='image'}}"><image class="splash-image" src="{{splashConfig.image_url}}" mode="aspectFill" data-event-opts="{{[['tap',[['handleSplashClick',['$event']]]]]}}" bindtap="__e"></image></block><block wx:if="{{splashConfig.display_type==='video'}}"><video class="splash-video" src="{{splashConfig.video_url}}" autoplay="{{true}}" controls="{{false}}" loop="{{false}}" data-event-opts="{{[['tap',[['handleSplashClick',['$event']]]]]}}" muted="{{true}}" bindtap="__e"></video></block><block wx:if="{{splashConfig.skip_enabled===1}}"><view data-event-opts="{{[['tap',[['skipSplash',['$event']]]]]}}" class="splash-skip" bindtap="__e"><block wx:if="{{skipCountdown>0}}"><text>{{skipCountdown+"s"}}</text></block><block wx:else><text>跳过</text></block></view></block><view class="splash-progress"><view class="splash-progress-bar" style="{{'width:'+(progressPercent+'%')+';'}}"></view></view><block wx:if="{{splashConfig.link_url&&splashConfig.link_name}}"><view data-event-opts="{{[['tap',[['handleSplashClick',['$event']]]]]}}" class="splash-link" bindtap="__e">{{''+splashConfig.link_name+''}}</view></block></view></view></block><view class="{{['custom-menu',!$root.m0?'menu-default':'',pageinfo.menuStyle==='fixed'?'menu-fixed':'',pageinfo.menuStyle==='float'&&pageinfo.scrollOpacity==='1'?'menu-float':'',pageinfo.isOpacity==='1'?'menu-opacity':'',pageinfo.bgType==='gradient'?'menu-gradient':'',pageinfo.menuStyle==='default'?'menu-default':'']}}" style="{{'height:'+(menuTop+50+'px')+';'+('--nav-bg-color:'+($root.m1?pageinfo.navBgColor:'')+';')+('background-color:'+($root.m2?'#ffffff':pageinfo.bgType==='gradient'&&pageinfo.topGradient==='1'?'transparent':pageinfo.bgType==='solid'?pageinfo.navBgColor:'transparent')+';')+('background-image:'+($root.m3?'linear-gradient('+(pageinfo.gradientDirection||'to bottom')+', '+(pageinfo.gradientStart||'rgba(0,0,0,0.6)')+', '+(pageinfo.gradientEnd||'rgba(0,0,0,0)')+' 50%, transparent 100%)':$root.m4?'url('+pageinfo.bgImage+')':'none')+';')+('display:'+($root.m5?'none':'block')+';')+('padding-top:'+(menuTop+'px')+';')}}"><view class="{{['menu-content',($root.m6)?'menu-content-default':'']}}" style="{{'text-align:'+(!$root.m7?'center':pageinfo.menuAlign)+';'+('color:'+($root.m8?'#000000':pageinfo.menuTextColor)+';')+('height:'+($root.m9?'100%':pageinfo.menuHeight-pageinfo.searchTopMargin+'px')+';')+('line-height:'+($root.m10?'normal':pageinfo.menuHeight-pageinfo.searchTopMargin+'px')+';')}}"><block wx:if="{{$root.m11}}"><view class="status-bar" style="{{'height:'+(statusBarHeight+'px')+';'}}"></view><view class="nav-content" style="{{'height:'+('44px')+';'}}"><text class="default-title">{{pageinfo.title||title||sysset.name||''}}</text></view></block><block wx:else><view class="menu-left"><block wx:if="{{pageinfo.leftMode!=='location'}}"><image class="menu-logo" src="{{sysset.logo}}" mode="aspectFit"></image></block><block wx:else><view data-event-opts="{{[['tap',[['handleLocationTap',['$event']]]]]}}" class="location-wrapper" bindtap="__e"><view class="location-content"><text class="location-text" style="{{'color:'+($root.m12)+';'}}"><block wx:if="{{pageinfo.locationType==='nearby'}}"><block><block wx:if="{{isLocating}}"><text>定位中...</text></block><block wx:else><block wx:if="{{locationFailed}}"><text>定位失败</text></block><block wx:else><text>{{area||'获取位置'}}</text></block></block></block></block><block wx:else><block><text>{{currentAreaName||'选择区域'}}</text></block></block></text><image class="{{['arrow-icon',(isLocating)?'is-locating':'']}}" src="/static/img/arrowdown.png" mode="aspectFit"></image></view></view></block></view><block wx:if="{{pageinfo.showSearch==='1'}}"><view class="{{['menu-right','search-container',pageinfo.searchPosition==='left'?'search-left':pageinfo.searchPosition==='center'?'search-center':'search-right']}}"><view data-event-opts="{{[['tap',[['handleSearchTap',['$event']]]]]}}" class="search-box" style="{{'width:'+(pageinfo.searchWidth+'px')+';'+('height:'+(pageinfo.searchHeight+'px')+';')+('line-height:'+(pageinfo.searchHeight+'px')+';')+('border-radius:'+(pageinfo.searchRadius+'px')+';')+('background-color:'+($root.m13)+';')}}" bindtap="__e"><image style="{{'width:'+(pageinfo.searchHeight*0.5+'px')+';'+('height:'+(pageinfo.searchHeight*0.5+'px')+';')}}" src="/static/img/search.png" mode="aspectFit"></image><text style="{{'font-size:'+(pageinfo.searchHeight*0.4+'px')+';'+('color:'+($root.m14)+';')}}">{{''+'搜索'+''}}</text></view></view></block></block></view></view><block wx:if="{{$root.m15}}"><view style="{{'height:'+(menuTop+40+'px')+';'}}"></view></block><block wx:if="{{$root.m16}}"><view class="top-gradient-extension" style="{{'background-image:'+('linear-gradient('+(pageinfo.gradientDirection||'to bottom')+', '+(pageinfo.gradientStart||'rgba(0,0,0,0.6)')+', '+(pageinfo.gradientEnd||'rgba(0,0,0,0)')+' 30%, transparent 100%)')+';'+('top:'+('0px')+';')}}"></view></block><block wx:if="{{xixie&&xdata&&xdata.xixie_mendian}}"><block><dp-xixie-mendian vue-id="8dd740cc-1" mendian_data="{{xdata.mendian_data}}" data-event-opts="{{[['^changePopupAddress',[['changePopupAddress']]]]}}" bind:changePopupAddress="__e" bind:__l="__l"></dp-xixie-mendian></block></block><block wx:if="{{platform=='wx'&&xdata.xixie_mendian}}"><block><view class="navigation" style="{{'height:'+(44+statusBarHeight+'px')+';'+('background:'+('transparent')+';')}}"><view style="{{'height:'+(statusBarHeight+'px')+';'}}"></view><view class="navcontent"><view class="topinfo"><image class="topinfoicon" src="{{sysset.logo}}"></image><view class="topinfotxt" style="{{'color:'+('#fff')+';'}}">{{sysset.name}}</view></view><view class="topsearch" style="{{'width:'+(screenWidth-210+'px')+';'+('background:'+('rgba(255,255,255,0.2)')+';')}}" data-url="/shopPackage/shop/search" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image src="/static/img/search.png"></image><text style="font-size:24rpx;color:#fff;">搜索感兴趣的商品</text></view></view></view><view style="{{'width:100%;'+('height:'+(44+statusBarHeight+'px')+';')}}"></view></block></block><block wx:if="{{sysset.mode==1}}"><block><view class="navigation" style="{{'height:'+(44+'px')+';'+('background:'+('rgba(255,255,255,0.95)')+';')+('backdrop-filter:'+('blur(10px)')+';')+('webkit-backdrop-filter:'+('blur(10px)')+';')}}"><view class="navcontent"><view class="topinfo"><image class="topinfoicon" src="{{sysset.logo}}" mode="aspectFill"></image><view class="topinfotxt" style="{{'color:'+('#333')+';'+('font-weight:'+('600')+';')}}">{{sysset.name}}</view></view><view class="topR"><text class="btn-text" style="{{'background:'+('linear-gradient(90deg,'+$root.m17+' 0%,rgba('+$root.m18+',0.8) 100%)')+';'+('box-shadow:'+('0 2px 8px '+$root.m19+'40')+';')}}" data-url="/pagesExt/business/clist2" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e"><image class="switch-icon" style="{{'width:'+('16px')+';'+('height:'+('16px')+';')+('transform:'+('scale(1.2)')+';')+('image-rendering:'+('crisp-edges')+';')}}" src="{{pre_url+'/static/img/switch.png'}}" mode="aspectFit"></image>切换</text><block wx:if="{{sysset.address}}"><block><text class="address-text">{{sysset.address}}</text></block></block></view></view></view><view style="{{'width:100%;'+('height:'+(44+'px')+';')}}"></view></block></block><block wx:if="{{sysset.mode==2}}"><block><view class="outernet-mode"><view class="loading-container"><block wx:if="{{sysset.logo}}"><image class="outernet-logo" src="{{sysset.logo}}" mode="aspectFit"></image></block><block wx:if="{{sysset.name}}"><view class="outernet-title">{{sysset.name}}</view></block><view class="spinner"></view><view class="loading-text">正在加载...</view><block wx:if="{{sysset.outernet_url}}"><view data-event-opts="{{[['tap',[['manualJump',['$event']]]]]}}" class="manual-jump" bindtap="__e"><text>点击进入</text></view></block></view></view></block></block><block wx:if="{{sysset.agent_card==1&&sysset.agent_card_info}}"><block><view style="height:10rpx;"></view><view class="agent-card"><view class="flex-y-center row1"><image class="logo" src="{{sysset.agent_card_info.logo}}"></image><view class="text"><view class="title limitText flex">{{sysset.agent_card_info.shopname}}</view><view class="limitText grey-text">{{sysset.agent_card_info.address}}</view><view class="grey-text flex-y-center"><image class="img" src="{{pre_url+'/static/img/my.png'}}"></image><view>{{sysset.agent_card_info.name}}</view><image class="img" style="margin-left:30rpx;" src="{{pre_url+'/static/img/tel.png'}}"></image><view style="position:relative;" data-url="{{'tel::'+sysset.agent_card_info.tel}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">{{''+sysset.agent_card_info.tel+''}}<view class="btn" data-url="{{'tel::'+sysset.agent_card_info.tel}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">拨打</view></view></view></view><view class="right"><image style="width:180rpx;height:48.5rpx;" src="{{pre_url+'/static/img/shop_vip.png'}}" mode="aspectFit"></image></view></view><view class="flex-y-center flex-x-center agent-card-b" style="{{'background:'+($root.m20)+';'}}"><view data-url="/pagesExt/agent/card" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image class="img" src="{{pre_url+'/static/img/shop.png'}}"></image>店铺信息</view><view data-url="/pages/commission/poster" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image class="img img2" src="{{pre_url+'/static/img/card.png'}}"></image>店铺海报</view></view></view></block></block><block wx:if="{{sysset.showgzts}}"><block><view style="width:100%;height:88rpx;"></view><view class="follow_topbar"><view class="headimg"><image src="{{sysset.logo}}"></image></view><view class="info"><view class="i">欢迎进入<text style="{{'color:'+($root.m21)+';'}}">{{sysset.name}}</text></view><view class="i">关注公众号享更多专属服务</view></view><view data-event-opts="{{[['tap',[['showsubqrcode',['$event']]]]]}}" class="sub" style="{{'background-color:'+($root.m22)+';'}}" bindtap="__e">立即关注</view></view><uni-popup class="vue-ref" vue-id="8dd740cc-2" id="qrcodeDialog" type="dialog" data-ref="qrcodeDialog" bind:__l="__l" vue-slots="{{['default']}}"><view class="qrcodebox"><image class="img" src="{{sysset.qrcode}}" data-url="{{sysset.qrcode}}" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image><view class="txt">长按识别二维码关注</view><view data-event-opts="{{[['tap',[['closesubqrcode',['$event']]]]]}}" class="close" bindtap="__e"><image style="width:100%;height:100%;" src="/static/img/close2.png"></image></view></view></uni-popup></block></block><dp vue-id="8dd740cc-3" pagecontent="{{pagecontent}}" menuindex="{{menuindex}}" data-event-opts="{{[['^getdata',[['getdata']]]]}}" bind:getdata="__e" bind:__l="__l"></dp><block wx:if="{{$root.g0}}"><view class="{{[sysset.ddbb_position=='bottom'?'bobaobox_bottom':'bobaobox']}}"><swiper style="position:relative;height:54rpx;width:450rpx;" autoplay="true" interval="{{5000}}" vertical="true"><block wx:for="{{oglist}}" wx:for-item="item" wx:for-index="index" wx:key="index"><swiper-item class="flex-y-center" data-url="{{item.tourl}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image style="width:40rpx;height:40rpx;border:1px solid rgba(255, 255, 255, 0.7);border-radius:50%;margin-right:4px;" src="{{item.headimg}}"></image><view style="width:400rpx;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;font-size:22rpx;"><text style="padding-right:2px;">{{item.nickname}}</text><text style="padding-right:4px;">{{item.showtime}}</text><block wx:if="{{item.type=='collage'&&item.buytype=='2'}}"><text style="padding-right:2px;">发起拼团</text></block><block wx:else><text>购买了</text></block><text>{{item.name}}</text></view></swiper-item></block></swiper></view></block><block wx:if="{{xixie&&xdata&&xdata.popup_address}}"><block><dp-xixie-popup-address vue-id="8dd740cc-4" xixie_login="{{xdata.xixie_login}}" xixie_location="{{xdata.xixie_location}}" address_latitude="{{latitude}}" address_longitude="{{longitude}}" code="{{code}}" data-event-opts="{{[['^changePopupAddress',[['changePopupAddress']]],['^getdata',[['getdata']]],['^setMendianData',[['setMendianData']]]]}}" bind:changePopupAddress="__e" bind:getdata="__e" bind:setMendianData="__e" bind:__l="__l"></dp-xixie-popup-address></block></block><block wx:if="{{xixie&&display_buy}}"><block><dp-xixie-buycart vue-id="8dd740cc-5" cartnum="{{cartnum}}" cartprice="{{cartprice}}" color="{{$root.m23}}" colorrgb="{{$root.m24}}" bind:__l="__l"></dp-xixie-buycart></block></block><block wx:if="{{copyright!=''}}"><view class="copyright">{{copyright}}</view></block><block wx:if="{{loading}}"><loading vue-id="8dd740cc-6" bind:__l="__l"></loading></block><dp-tabbar vue-id="8dd740cc-7" opt="{{opt}}" data-event-opts="{{[['^getmenuindex',[['getmenuindex']]]]}}" bind:getmenuindex="__e" bind:__l="__l"></dp-tabbar><dp-guanggao vue-id="8dd740cc-8" guanggaopic="{{guanggaopic}}" guanggaourl="{{guanggaourl}}" bind:__l="__l"></dp-guanggao><popmsg class="vue-ref" vue-id="8dd740cc-9" data-ref="popmsg" bind:__l="__l"></popmsg></view>