<view class="container"><block wx:if="{{isload}}"><block><block wx:if="{{$root.g0}}"><view class="content"><view class="label"><text class="t1">报名列表</text></view><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><view class="item"><view class="f1"><image src="{{item.$orig.headimg}}"></image><view class="t2"><text class="x1">{{item.$orig.nickname}}</text><text class="x2">{{item.m0}}</text><block wx:for="{{item.$orig.formdata}}" wx:for-item="items" wx:for-index="__i0__" wx:key="*this"><view><text class="x1" style="margin-right:5px;">{{items[0]+":"}}</text><block wx:if="{{items[2]=='upload'}}"><view class="x1"><image style="width:400rpx;height:auto;" src="{{items[1]}}" mode="widthFix" data-url="{{items[1]}}" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image></view></block><block wx:else><text class="x1" user-select="true" selectable="true">{{items[1]}}</text></block></view></block></view></view></view></block></block></view></block></block></block><block wx:if="{{nodata}}"><nodata vue-id="138e8b8c-1" bind:__l="__l"></nodata></block><block wx:if="{{nomore}}"><nomore vue-id="138e8b8c-2" bind:__l="__l"></nomore></block><block wx:if="{{loading}}"><loading vue-id="138e8b8c-3" bind:__l="__l"></loading></block><dp-tabbar vue-id="138e8b8c-4" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="138e8b8c-5" data-ref="popmsg" bind:__l="__l"></popmsg></view>