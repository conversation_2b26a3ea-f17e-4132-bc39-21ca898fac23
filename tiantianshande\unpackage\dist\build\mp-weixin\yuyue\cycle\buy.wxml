<view class="container"><block wx:if="{{isload}}"><block><form data-event-opts="{{[['submit',[['submitOrder',['$event']]]]]}}" bindsubmit="__e"><view class="address-add flex-y-center" data-url="/pages/address/address?fromPage=buy&type=1" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="f1"><image class="img" src="/static/img/address.png"></image></view><block wx:if="{{address.id}}"><view class="f2 flex1"><view style="font-weight:bold;color:#111111;font-size:30rpx;">{{address.name+" "+address.tel}}</view><view style="font-size:24rpx;">{{address.area+" "+address.address}}</view></view></block><block wx:else><view class="f2 flex1">请选择服务地址</view></block><image class="f3" src="/static/img/arrowright.png"></image></view><view class="buydata"><view class="bcontent"><view class="product"><view class="item flex"><view class="img"><image class="img" src="{{productInfo.pic}}"></image></view><view class="info flex1"><view class="f1">{{productInfo.name}}</view><view class="f2">{{"共"+productInfo.total_period+"期"}}</view><view class="f3">{{"￥"+productInfo.sell_price}}<text style="font-size:24rpx;color:#999;">/期</text></view></view></view></view><view class="price"><text class="f1">商品总价</text><text class="f2">{{"￥"+priceInfo.product_total_price}}</text></view><view class="price"><view class="f1">{{$root.m0}}</view><block wx:if="{{$root.g0>0}}"><view data-event-opts="{{[['tap',[['showCouponList',['$event']]]]]}}" class="f2" bindtap="__e"><text style="{{'color:#fff;padding:4rpx 16rpx;font-weight:normal;border-radius:8rpx;font-size:24rpx;'+('background:'+($root.m1)+';')}}">{{couponSelected.id?couponSelected.name:$root.g1+'张可用'}}</text><text class="iconfont iconjiantou" style="color:#999;font-weight:normal;"></text></view></block><block wx:else><text class="f2" style="color:#999;">{{"无可用"+$root.m2}}</text></block></view><block wx:if="{{couponSelected.id}}"><view class="price"><text class="f1">优惠金额</text><text class="f2" style="color:#ff5043;">{{"-￥"+couponDiscount}}</text></view></block><view class="price"><text class="f1">期望开始日期</text><picker mode="date" value="{{startDate}}" start="{{minStartDate}}" data-event-opts="{{[['change',[['bindStartDateChange',['$event']]]]]}}" bindchange="__e"><view class="f2">{{startDate||'请选择'}}<text class="iconfont iconjiantou" style="color:#999;font-weight:normal;"></text></view></picker></view><view class="remark"><text class="f1">备注</text><input class="f2" name="remark" placeholder="选填, 请输入备注信息" placeholder-style="color:#ccc"/></view></view></view><view style="width:100%;height:120rpx;"></view><view class="{{['footer','flex',menuindex>-1?'tabbarbot':'notabbarbot']}}"><view class="text1 flex1">合计：<text style="font-weight:bold;font-size:36rpx;color:#FF5043;">{{"￥"+totalPrice}}</text></view><button class="op" style="{{'background:'+('linear-gradient(-90deg,'+$root.m3+' 0%,rgba('+$root.m4+',0.8) 100%)')+';'}}" form-type="submit" disabled="{{submitDisabled}}">提交订单</button></view></form><block wx:if="{{couponVisible}}"><view class="popup__container"><view data-event-opts="{{[['tap',[['hideCouponList',['$event']]]]]}}" class="popup__overlay" catchtap="__e"></view><view class="popup__modal"><view class="popup__title"><text class="popup__title-text">{{"请选择"+$root.m5}}</text><image class="popup__close" src="/static/img/close.png" data-event-opts="{{[['tap',[['hideCouponList',['$event']]]]]}}" catchtap="__e"></image></view><view class="popup__content"><couponlist vue-id="e26cee6e-1" couponlist="{{couponList}}" choosecoupon="{{true}}" selectedrid="{{couponSelected.id}}" bid="{{productInfo.bid}}" data-event-opts="{{[['^chooseCoupon',[['chooseCoupon']]]]}}" bind:chooseCoupon="__e" bind:__l="__l"></couponlist></view></view></view></block></block></block><block wx:if="{{loading}}"><loading vue-id="e26cee6e-2" bind:__l="__l"></loading></block><dp-tabbar vue-id="e26cee6e-3" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="e26cee6e-4" data-ref="popmsg" bind:__l="__l"></popmsg></view>