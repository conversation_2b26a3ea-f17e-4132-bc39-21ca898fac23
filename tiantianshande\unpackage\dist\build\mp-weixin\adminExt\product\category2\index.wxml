<view><view class="container" id="datalist"><view><view class="info-item"><view class="t1">分类管理</view><view class="t2" data-url="edit" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">添加分类</view><image class="t3" data-url="edit" src="{{pre_url+'/static/img/arrowright.png'}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"></image></view><view class="info-item"><view class="t1">分类名称</view><view class="t2">图标</view><view class="t2">状态</view><view class="t2">排序</view><view class="t3"></view></view><block wx:for="{{datalist}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="info-item"><view class="t1"><block wx:if="{{item.deep==0}}"><text>{{item.name+''}}</text></block><block wx:if="{{item.deep==1}}"><text class="d1">{{item.name+''}}</text></block><block wx:if="{{item.deep==2}}"><text class="d2">{{item.name+''}}</text></block></view><view class="t2"><image src="{{item.pic}}"></image></view><view class="t2"><block wx:if="{{item.status==1}}"><text>显示</text></block><block wx:else><text>隐藏</text></block></view><view class="t2">{{item.sort}}</view><image class="t3" data-url="{{'edit?id='+item.id}}" src="{{pre_url+'/static/img/arrowright.png'}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"></image></view></block></view></view><block wx:if="{{nomore}}"><nomore vue-id="2863a55e-1" bind:__l="__l"></nomore></block><block wx:if="{{nodata}}"><nodata vue-id="2863a55e-2" bind:__l="__l"></nodata></block></view>