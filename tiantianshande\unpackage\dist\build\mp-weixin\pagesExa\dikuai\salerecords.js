require('../common/vendor.js');(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pagesExa/dikuai/salerecords"],{1604:function(t,n,a){"use strict";a.r(n);var e=a("c94a"),u=a.n(e);for(var o in e)["default"].indexOf(o)<0&&function(t){a.d(n,t,(function(){return e[t]}))}(o);n["default"]=u.a},"55a7":function(t,n,a){"use strict";var e=a("ef62"),u=a.n(e);u.a},"55ed7":function(t,n,a){"use strict";(function(t,n){var e=a("47a9");a("06e9");e(a("3240"));var u=e(a("627a"));t.__webpack_require_UNI_MP_PLUGIN__=a,n(u.default)}).call(this,a("3223")["default"],a("df3c")["createPage"])},"627a":function(t,n,a){"use strict";a.r(n);var e=a("8842"),u=a("1604");for(var o in u)["default"].indexOf(o)<0&&function(t){a.d(n,t,(function(){return u[t]}))}(o);a("55a7");var i=a("828b"),r=Object(i["a"])(u["default"],e["b"],e["c"],!1,null,null,null,!1,e["a"],void 0);n["default"]=r.exports},8842:function(t,n,a){"use strict";a.d(n,"b",(function(){return u})),a.d(n,"c",(function(){return o})),a.d(n,"a",(function(){return e}));var e={nodata:function(){return a.e("components/nodata/nodata").then(a.bind(null,"101c"))}},u=function(){var t=this.$createElement;this._self._c},o=[]},c94a:function(t,n,a){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;var e=getApp(),u={data:function(){return{list:[],nodata:!1}},onLoad:function(t){this.getData()},methods:{getData:function(){var t=this;e.get("ApiPlot/getSales",{},(function(n){t.list=n.data,0==t.list.length&&(t.nodata=!0)}))}}};n.default=u},ef62:function(t,n,a){}},[["55ed7","common/runtime","common/vendor"]]]);