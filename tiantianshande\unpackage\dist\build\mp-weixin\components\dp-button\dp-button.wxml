<view class="dp-button" style="{{'background-color:'+(params.bgcolor)+';'+('margin:'+(params.margin_y*2.2+'rpx '+params.margin_x*2.2+'rpx 0')+';')+('padding:'+(params.padding_y*2.2+'rpx '+params.padding_x*2.2+'rpx')+';')+('font-size:'+(params.fontsize*2+'rpx')+';')}}"><button style="{{'background-color:'+(params.btnbgcolor)+';'+('border:'+('1px solid '+params.btnbordercolor)+';')+('font-size:'+(params.btnfontsize*2+'rpx')+';')+('color:'+(params.btncolor)+';')+('width:'+(params.btnwidth*2.2+'rpx')+';')+('height:'+(params.btnheight*2.2+'rpx')+';')+('line-height:'+(params.btnheight*2.2+'rpx')+';')+('border-radius:'+(params.btnradius*2.2+'rpx')+';')}}" open-type="{{params.hrefurl=='contact::'?'contact':params.hrefurl=='share::'?'share':''}}" data-url="{{params.hrefurl}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">{{params.btntext}}</button></view>