<view><block wx:if="{{isload}}"><block><view class="container"><view class="header"><view class="flex-y-center"><image class="header_icon" src="{{logo}}"></image><view class="flex1"><view class="header_name">{{name}}</view><view class="header_shop"><text>选择门店:</text><text data-event-opts="{{[['tap',[['selectmd',['$event']]]]]}}" bindtap="__e">{{mdlist[mdkey].name}}</text></view></view></view></view><view class="page"><view data-event-opts="{{[['tap',[['handleShowKey',['$event']]]]]}}" class="page_module flex-y-center" bindtap="__e"><text class="page_tag">￥</text><view class="page_price flex-y-center"><block wx:if="{{keyHidden&&!money}}"><text class="page_notice">请输入金额</text></block><block wx:if="{{money}}"><text class="{{[(!keyHidden)?'editable-money':'']}}">{{money}}</text></block><block wx:if="{{!keyHidden}}"><view class="page_cursor"></view></block></view></view><view><view class="info-box"><block wx:if="{{userinfo.discount>0&&userinfo.discount<10}}"><view class="dkdiv-item flex"><text class="f1">{{$root.m0+"折扣("+userinfo.discount*100/100+"折)"}}</text><text class="f2" style="color:#e94745;">{{"-￥"+disprice}}</text></view></block><view class="dkdiv-item flex flex-bt"><text class="t1">实付金额:</text><text class="t2">{{"￥"+paymoney}}</text></view><block wx:if="{{keyHidden}}"><view class="op"><view data-event-opts="{{[['tap',[['topay',['$event']]]]]}}" class="btn" style="{{'background:'+($root.m1)+';'}}" bindtap="__e">去支付</view></view></block></view></view></view></view><block wx:if="{{couponvisible}}"><view class="popup__container"><view data-event-opts="{{[['tap',[['handleClickMask',['$event']]]]]}}" class="popup__overlay" catchtap="__e"></view><view class="popup__modal"><view class="popup__title"><text class="popup__title-text">{{"请选择"+$root.m2}}</text><image class="popup__close" style="width:36rpx;height:36rpx;" src="/static/img/close.png" data-event-opts="{{[['tap',[['handleClickMask',['$event']]]]]}}" catchtap="__e"></image></view><view class="popup__content"><couponlist vue-id="331a4b92-1" couponlist="{{couponList}}" choosecoupon="{{true}}" selectedrid="{{couponrid}}" data-event-opts="{{[['^chooseCoupon',[['chooseCoupon']]]]}}" bind:chooseCoupon="__e" bind:__l="__l"></couponlist></view></view></view></block><block wx:if="{{!keyHidden}}"><view class="keyboard_page"><view data-event-opts="{{[['tap',[['handleHiddenKey',['$event']]]]]}}" class="keyboard_none" bindtap="__e"></view><view class="{{['keyboard_key','hind_box',menuindex>-1?'tabbarbot':'notabbarbot']}}"><image class="key-down" src="{{pre_url+'/static/img/pack_up.png'}}" mode data-event-opts="{{[['tap',[['handleHiddenKey',['$event']]]]]}}" bindtap="__e"></image><view class="key-box"><view class="number-box clearfix"><block wx:for="{{KeyboardKeys}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="{{[index===9?'key key-zero':'key']}}" hover-class="number-box-hover" data-event-opts="{{[['tap',[['handleKey',['$0'],[[['KeyboardKeys','',index]]]]]]]}}" bindtap="__e">{{item}}</view></block></view><view class="btn-box"><view class="key" hover-class="number-box-hover" data-key="X" data-event-opts="{{[['tap',[['handleKey',['X']]]]]}}" bindtap="__e">×</view><view class="{{[money?'key pay_btn':'key pay_btn pay-btn-display']}}" hover-class="pay-btn-hover" data-event-opts="{{[['tap',[['topay',['$event']]]]]}}" bindtap="__e">付款</view></view></view></view></view></block><block wx:if="{{selectmdDialogShow}}"><view class="popup__container"><view data-event-opts="{{[['tap',[['hideSelectmdDialog',['$event']]]]]}}" class="popup__overlay" catchtap="__e"></view><view class="popup__modal"><view class="popup__title"><text class="popup__title-text">请选择门店</text><image class="popup__close" style="width:36rpx;height:36rpx;" src="/static/img/close.png" data-event-opts="{{[['tap',[['hideSelectmdDialog',['$event']]]]]}}" catchtap="__e"></image></view><view class="popup__content"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="pstime-item" data-index="{{index}}" data-event-opts="{{[['tap',[['selectmdRadioChange',['$event']]]]]}}" bindtap="__e"><view class="flex1">{{item.$orig.name}}</view><view style="color:#999;font-size:24rpx;margin-right:10rpx;">{{item.$orig.juli?' 距离:'+item.$orig.juli+'千米':''}}</view><view class="radio" style="{{(index==mdkey?'background:'+item.m3+';border:0':'')}}"><image class="radio-img" src="/static/img/checkd.png"></image></view></view></block></view></view></view></block></block></block><block wx:if="{{loading}}"><loading vue-id="331a4b92-2" bind:__l="__l"></loading></block><dp-tabbar vue-id="331a4b92-3" opt="{{opt}}" data-event-opts="{{[['^getmenuindex',[['getmenuindex']]]]}}" bind:getmenuindex="__e" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="331a4b92-4" data-ref="popmsg" bind:__l="__l"></popmsg></view>