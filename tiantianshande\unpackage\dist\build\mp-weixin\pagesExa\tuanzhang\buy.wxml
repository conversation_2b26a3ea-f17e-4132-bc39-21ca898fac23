<view class="container"><block wx:if="{{isload}}"><block><form data-event-opts="{{[['submit',[['topay',['$event']]]]]}}" bindsubmit="__e"><block wx:if="{{needaddress==0}}"><view class="address-add"><view class="linkitem"><text class="f1">联 系 人：</text><input class="input" type="text" placeholder="请输入您的姓名" placeholder-style="color:#626262;font-size:28rpx" data-event-opts="{{[['input',[['inputLinkman',['$event']]]]]}}" value="{{linkman}}" bindinput="__e"/></view><view class="linkitem"><text class="f1">联系电话：</text><input class="input" type="text" placeholder="请输入您的手机号" placeholder-style="color:#626262;font-size:28rpx" data-event-opts="{{[['input',[['inputTel',['$event']]]]]}}" value="{{tel}}" bindinput="__e"/></view></view></block><block wx:else><view class="address-add flex-y-center" data-url="{{'/pages/address/'+(address.id?'address':'addressadd')+'?fromPage=buy&type='+(havetongcheng==1?'1':'0')}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="f1"><image class="img" src="/static/img/address.png"></image></view><block wx:if="{{address.id}}"><view class="f2 flex1"><view style="font-weight:bold;color:#111111;font-size:30rpx;">{{address.name+" "+address.tel+''}}<block wx:if="{{address.company}}"><text>{{address.company}}</text></block></view><view style="font-size:24rpx;">{{address.area+" "+address.address}}</view></view></block><block wx:else><view class="f2 flex1">请选择收货地址</view></block><image class="f3" src="/static/img/arrowright.png"></image></view></block><block wx:for="{{$root.l5}}" wx:for-item="buydata" wx:for-index="index" wx:key="index"><view class="buydata"><view class="btitle"><image class="img" src="/static/img/ico-shop.png"></image>{{buydata.$orig.business.name+''}}</view><view class="bcontent"><view class="product"><block wx:for="{{buydata.l0}}" wx:for-item="item" wx:for-index="index2" wx:key="index2"><view><block wx:if="{{item.$orig.num>0}}"><view class="item flex"><view class="img" data-url="{{'/shopPackage/shop/product?id='+item.$orig.product.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><block wx:if="{{item.$orig.guige.pic}}"><image src="{{item.$orig.guige.pic}}"></image></block><block wx:else><image src="{{item.$orig.product.pic}}"></image></block></view><view class="info flex1"><view class="f1">{{item.$orig.product.name}}</view><view class="f2">{{"规格："+item.$orig.guige.name}}</view><view class="f3"><block wx:if="{{order_change_price}}"><block><input class="inputPrice" type="number" data-price="{{item.$orig.guige.sell_price}}" data-index="{{index}}" data-index2="{{index2}}" data-event-opts="{{[['input',[['inputPrice',['$event']]]]]}}" value="{{item.$orig.guige.sell_price}}" bindinput="__e"/></block></block><block wx:else><block wx:if="{{item.$orig.guige.is_newcustom==0}}"><block><text style="font-weight:bold;">{{"￥"+item.$orig.guige.yh_price}}</text></block></block><block wx:else><block><text style="font-weight:bold;">{{"￥"+item.$orig.guige.sell_price}}</text></block></block></block><text style="padding-left:20rpx;">{{'× '+item.$orig.num}}</text></view></view></view></block><block wx:if="{{item.$orig.is_yh==1}}"><view class="item flex"><view class="img" data-url="{{'/shopPackage/shop/product?id='+item.$orig.product.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><block wx:if="{{item.$orig.guige.pic}}"><image src="{{item.$orig.guige.pic}}"></image></block><block wx:else><image src="{{item.$orig.product.pic}}"></image></block></view><view class="info flex1"><view class="f1">{{item.$orig.product.name}}</view><view class="f2">{{"规格："+item.$orig.guige.name}}</view><view class="f3"><block wx:if="{{order_change_price}}"><block><input class="inputPrice" type="number" data-price="{{item.$orig.guige.sell_price}}" data-index="{{index}}" data-index2="{{index2}}" data-event-opts="{{[['input',[['inputPrice',['$event']]]]]}}" value="{{item.$orig.guige.sell_price}}" bindinput="__e"/></block></block><block wx:else><block><text style="font-weight:bold;">{{"￥"+item.$orig.youhui.product_price+"(优惠价格)"}}</text></block></block><text style="padding-left:20rpx;">{{'× '+item.$orig.youhui.new_num+"(优惠数量)"}}</text></view></view></view></block><block wx:if="{{item.$orig.product.glassrecord}}"><view class="glassinfo" style="{{('background:rgba('+item.m0+',0.1)')}}" data-index="{{index}}" data-index2="{{index2}}" data-grid="{{item.$orig.product.glassrecord.id}}" data-event-opts="{{[['tap',[['showglass',['$event']]]]]}}" bindtap="__e"><view class="f1">视力档案</view><view class="f2"><text>{{(item.$orig.product.glassrecord.type==1?'近视':'远视')+"，右眼"+item.$orig.product.glassrecord.degress_right+"度，左眼"+item.$orig.product.glassrecord.degress_left+"度"}}</text><image src="../../static/img/arrowright.png"></image></view></view></block></view></block></view><view class="freight"><view class="f1">配送方式</view><view class="freight-ul"><view class="flex" style="width:100%;overflow-y:hidden;overflow-x:scroll;"><block wx:for="{{buydata.l1}}" wx:for-item="item" wx:for-index="idx2" wx:key="idx2"><block><view class="freight-li" style="{{(buydata.$orig.freightkey==idx2?'color:'+item.m1+';background:rgba('+item.m2+',0.2)':'')}}" data-bid="{{buydata.$orig.bid}}" data-index="{{idx2}}" data-event-opts="{{[['tap',[['changeFreight',['$event']]]]]}}" bindtap="__e">{{item.$orig.name+''}}</view></block></block></view></view><block wx:if="{{buydata.$orig.freightList[buydata.$orig.freightkey].minpriceset==1&&buydata.$orig.freightList[buydata.$orig.freightkey].minprice>0&&buydata.$orig.freightList[buydata.$orig.freightkey].minprice*1>buydata.$orig.product_price*1}}"><view class="freighttips">{{'满'+buydata.$orig.freightList[buydata.$orig.freightkey].minprice+"元起送，还差"+buydata.g0+'元'}}</view></block><block wx:if="{{buydata.$orig.freightList[buydata.$orig.freightkey].isoutjuli==1}}"><view class="freighttips">超出配送范围</view></block></view><block wx:if="{{buydata.$orig.freightList[buydata.$orig.freightkey].pstimeset==1}}"><view class="price"><view class="f1">{{(buydata.$orig.freightList[buydata.$orig.freightkey].pstype==1?'取货':'配送')+"时间"}}</view><view class="f2" data-bid="{{buydata.$orig.bid}}" data-event-opts="{{[['tap',[['choosePstime',['$event']]]]]}}" bindtap="__e">{{''+(buydata.$orig.pstimetext==''?'请选择时间':buydata.$orig.pstimetext)}}<text class="iconfont iconjiantou" style="color:#999;font-weight:normal;"></text></view></view></block><block wx:if="{{buydata.$orig.freightList[buydata.$orig.freightkey].pstype==1}}"><view class="storeitem"><block wx:if="{{buydata.g1}}"><view data-event-opts="{{[['tap',[['doStoreShowAll',['$event']]]]]}}" class="storeviewmore" bindtap="__e">- 查看更多 -</view></block></view></block><block wx:if="{{buydata.$orig.freightList[buydata.$orig.freightkey].pstype==5}}"><view class="storeitem"><view class="panel"><view class="f1">配送门店</view><view class="f2" data-bid="{{buydata.$orig.bid}}" data-freightkey="{{buydata.$orig.freightkey}}" data-storekey="{{buydata.$orig.freightList[buydata.$orig.freightkey].storekey}}" data-event-opts="{{[['tap',[['openMendian',['$event']]]]]}}" bindtap="__e"><text class="iconfont icondingwei"></text>{{buydata.$orig.freightList[buydata.$orig.freightkey].storedata[buydata.$orig.freightList[buydata.$orig.freightkey].storekey].name+''}}</view></view><block wx:for="{{buydata.l2}}" wx:for-item="item" wx:for-index="idx" wx:key="idx"><block><block wx:if="{{idx<5||storeshowall==true}}"><view class="radio-item" data-bid="{{buydata.$orig.bid}}" data-index="{{idx}}" data-event-opts="{{[['tap',[['choosestore',['$event']]]]]}}" catchtap="__e"><view class="f1">{{item.$orig.name+''}}</view><text style="color:#f50;">{{item.$orig.juli}}</text><view class="radio" style="{{(buydata.$orig.freightList[buydata.$orig.freightkey].storekey==idx?'background:'+item.m3+';border:0':'')}}"><image class="radio-img" src="/static/img/checkd.png"></image></view></view></block></block></block><block wx:if="{{buydata.g2}}"><view data-event-opts="{{[['tap',[['doStoreShowAll',['$event']]]]]}}" class="storeviewmore" bindtap="__e">- 查看更多 -</view></block></view></block><block wx:if="{{buydata.$orig.freightList[buydata.$orig.freightkey].pstype==11}}"><view class="price"><view class="f1">选择物流</view><view class="f2" data-bid="{{buydata.$orig.bid}}" data-event-opts="{{[['tap',[['showType11List',['$event']]]]]}}" bindtap="__e"><text>{{buydata.$orig.type11key?buydata.$orig.freightList[buydata.$orig.freightkey].type11pricedata[buydata.$orig.type11key-1].name:'请选择'}}</text><text class="iconfont iconjiantou" style="color:#999;font-weight:normal;"></text></view></view></block><view class="price"><text class="f1">订单金额</text><text class="f2">{{"¥"+buydata.$orig.product_price}}</text></view><block wx:if="{{buydata.$orig.leveldk_money>0}}"><view class="price"><text class="f1">{{buydata.m4+"折扣("+userinfo.discount+"折)"}}</text><text class="f2">{{"-¥"+buydata.$orig.leveldk_money}}</text></view></block><block wx:if="{{buydata.$orig.manjian_money>0}}"><view class="price"><text class="f1">满减活动</text><text class="f2">{{"-¥"+buydata.$orig.manjian_money}}</text></view></block><view class="price"><text class="f1">{{buydata.$orig.freightList[buydata.$orig.freightkey].freight_price_txt||'运费'}}<block wx:if="{{buydata.$orig.freightList[buydata.$orig.freightkey].pstype!=1&&buydata.$orig.freightList[buydata.$orig.freightkey].freeset==1}}"><text style="color:#aaa;font-size:24rpx;">{{"（满"+buydata.$orig.freightList[buydata.$orig.freightkey].free_price+"元包邮）"}}</text></block></text><text class="f2">{{"+¥"+buydata.$orig.freightList[buydata.$orig.freightkey].freight_price}}</text></view><view class="price"><view class="f1">{{buydata.m5}}</view><block wx:if="{{buydata.$orig.couponCount>0}}"><view class="f2" data-bid="{{buydata.$orig.bid}}" data-event-opts="{{[['tap',[['showCouponList',['$event']]]]]}}" bindtap="__e"><block wx:if="{{buydata.g3>0}}"><block><block wx:for="{{buydata.l3}}" wx:for-item="item" wx:for-index="index"><text class="couponname" style="{{'background:'+(item.m6)+';'}}">{{item.$orig.couponname}}</text></block></block></block><block wx:else><block><text class="couponname" style="{{'background:'+(buydata.m7)+';'}}">{{buydata.$orig.couponCount+'张可用'}}</text></block></block><text class="iconfont iconjiantou" style="color:#999;font-weight:normal;"></text></view></block><block wx:else><text class="f2" style="color:#999;">{{"无可用"+buydata.m8}}</text></block></view><block wx:if="{{buydata.$orig.cuxiaoCount>0}}"><view class="price"><view class="f1">促销活动</view><view class="f2" data-bid="{{buydata.$orig.bid}}" data-event-opts="{{[['tap',[['showCuxiaoList',['$event']]]]]}}" bindtap="__e"><block wx:if="{{buydata.g4}}"><block><block wx:for="{{buydata.l4}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="redBg" style="{{'background:'+(item.m9)+';'}}">{{item.$orig}}</view></block></block></block><block wx:else><block><text class="redBg" style="{{'background:'+(buydata.m10)+';'}}">{{buydata.$orig.cuxiaoname?buydata.$orig.cuxiaoname:buydata.$orig.cuxiaoCount+'个可用'}}</text></block></block></view><text class="iconfont iconjiantou" style="color:#999;font-weight:normal;"></text></view></block><block wx:if="{{buydata.$orig.business.invoice>0}}"><view class="price"><view class="f1">发票</view><view class="f2" data-url="{{'/shopPackage/shop/invoice?bid='+buydata.$orig.bid+'&prodata='+opt.prodata}}" data-bid="{{buydata.$orig.bid}}" data-index="{{index}}" data-event-opts="{{[['tap',[['showInvoice',['$event']]]]]}}" bindtap="__e"><block wx:if="{{buydata.$orig.tempInvoice&&buydata.$orig.tempInvoice.invoice_name}}"><text style="font-size:24rpx;"><block wx:if="{{buydata.$orig.tempInvoice&&buydata.$orig.tempInvoice.name_type==1}}"><text>个人 -</text></block><block wx:if="{{buydata.$orig.tempInvoice&&buydata.$orig.tempInvoice.name_type==2}}"><text>公司 -</text></block>{{''+buydata.$orig.tempInvoice.invoice_name}}</text></block><text class="iconfont iconjiantou" style="color:#999;font-weight:normal;"></text></view></view></block><block wx:if="{{buydata.$orig.business.invoice>0&&buydata.$orig.business.invoice_rate>0&&buydata.$orig.tempInvoice&&buydata.$orig.tempInvoice.invoice_name}}"><view class="price"><text class="f1">发票费用</text><text class="f2">{{"¥"+buydata.$orig.invoice_price}}</text></view></block><view style="display:none;">{{test}}</view><block wx:for="{{buydata.$orig.freightList[buydata.$orig.freightkey].formdata}}" wx:for-item="item" wx:for-index="idx" wx:key="id"><view class="form-item"><view class="label">{{item.val1}}<block wx:if="{{item.val3==1}}"><text style="color:red;">*</text></block></view><block wx:if="{{item.key=='input'}}"><block><input class="input" type="text" name="{{'form'+buydata.$orig.bid+'_'+idx}}" placeholder="{{item.val2}}" placeholder-style="font-size:28rpx"/></block></block><block wx:if="{{item.key=='textarea'}}"><block><textarea class="textarea" name="{{'form'+buydata.$orig.bid+'_'+idx}}" placeholder="{{item.val2}}" placeholder-style="font-size:28rpx"></textarea></block></block><block wx:if="{{item.key=='radio'}}"><block><radio-group class="radio-group" name="{{'form'+buydata.$orig.bid+'_'+idx}}"><block wx:for="{{item.val2}}" wx:for-item="item1" wx:for-index="idx1" wx:key="id"><label class="flex-y-center"><radio class="radio" value="{{item1}}"></radio>{{item1+''}}</label></block></radio-group></block></block><block wx:if="{{item.key=='checkbox'}}"><block><checkbox-group class="checkbox-group" name="{{'form'+buydata.$orig.bid+'_'+idx}}"><block wx:for="{{item.val2}}" wx:for-item="item1" wx:for-index="idx1" wx:key="id"><label class="flex-y-center"><checkbox class="checkbox" value="{{item1}}"></checkbox>{{item1+''}}</label></block></checkbox-group></block></block><block wx:if="{{item.key=='selector'}}"><block><picker class="picker" mode="selector" name="{{'form'+buydata.$orig.bid+'_'+idx}}" value="" range="{{item.val2}}" data-bid="{{buydata.$orig.bid}}" data-idx="{{idx}}" data-event-opts="{{[['change',[['editorBindPickerChange',['$event']]]]]}}" bindchange="__e"><block wx:if="{{buydata.$orig.editorFormdata[idx]||buydata.$orig.editorFormdata[idx]===0}}"><view>{{''+item.val2[buydata.$orig.editorFormdata[idx]]}}</view></block><block wx:else><view>请选择</view></block></picker><text class="iconfont iconjiantou" style="color:#999;font-weight:normal;"></text></block></block><block wx:if="{{item.key=='time'}}"><block><picker class="picker" mode="time" name="{{'form'+buydata.$orig.bid+'_'+idx}}" value="" start="{{item.val2[0]}}" end="{{item.val2[1]}}" range="{{item.val2}}" data-bid="{{buydata.$orig.bid}}" data-idx="{{idx}}" data-event-opts="{{[['change',[['editorBindPickerChange',['$event']]]]]}}" bindchange="__e"><block wx:if="{{buydata.$orig.editorFormdata[idx]}}"><view>{{buydata.$orig.editorFormdata[idx]}}</view></block><block wx:else><view>请选择</view></block></picker><text class="iconfont iconjiantou" style="color:#999;font-weight:normal;"></text></block></block><block wx:if="{{item.key=='date'}}"><block><picker class="picker" mode="date" name="{{'form'+buydata.$orig.bid+'_'+idx}}" value="" start="{{item.val2[0]}}" end="{{item.val2[1]}}" range="{{item.val2}}" data-bid="{{buydata.$orig.bid}}" data-idx="{{idx}}" data-event-opts="{{[['change',[['editorBindPickerChange',['$event']]]]]}}" bindchange="__e"><block wx:if="{{buydata.$orig.editorFormdata[idx]}}"><view>{{buydata.$orig.editorFormdata[idx]}}</view></block><block wx:else><view>请选择</view></block></picker><text class="iconfont iconjiantou" style="color:#999;font-weight:normal;"></text></block></block><block wx:if="{{item.key=='upload'}}"><block><input style="display:none;" type="text" name="{{'form'+buydata.$orig.bid+'_'+idx}}" value="{{buydata.$orig.editorFormdata[idx]}}"/><view class="flex" style="flex-wrap:wrap;padding-top:20rpx;"><block wx:if="{{buydata.$orig.editorFormdata[idx]}}"><view class="form-imgbox"><view class="form-imgbox-img"><image class="image" src="{{buydata.$orig.editorFormdata[idx]}}" data-url="{{buydata.$orig.editorFormdata[idx]}}" mode="widthFix" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image></view></view></block><view class="form-uploadbtn" style="{{'background:'+('url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 50rpx')+';'+('background-size:'+('80rpx 80rpx')+';')+('background-color:'+('#F3F3F3')+';')}}" data-bid="{{buydata.$orig.bid}}" data-idx="{{idx}}" data-event-opts="{{[['tap',[['editorChooseImage',['$event']]]]]}}" bindtap="__e"></view></view></block></block></view></block><block wx:if="{{buydata.$orig.diy_amount>0}}"><view class="price"><text class="f1">{{"框价格   x "+buydata.$orig.totalnum}}</text><text class="f2">{{"¥"+buydata.$orig.diy_amount}}</text></view></block><block wx:if="{{totalFreight}}"><view class="price"><text class="f1">配送费</text><text class="f2">{{"¥"+totalFreight}}</text></view></block></view></view></block><block wx:if="{{buy_selectmember}}"><view class="scoredk"><view class="price"><view class="f1">选择会员</view><view data-event-opts="{{[['tap',[['showMemberList',['$event']]]]]}}" class="f2" bindtap="__e"><text>{{checkMem.id?checkMem.nickname:'请选择'}}</text><text class="iconfont iconjiantou" style="color:#999;font-weight:normal;"></text></view></view></view></block><block wx:if="{{userinfo.score2money>0&&(userinfo.scoremaxtype==0||userinfo.scoremaxtype==1&&userinfo.scoredkmaxmoney>0)}}"><view class="scoredk"><checkbox-group data-event-opts="{{[['change',[['scoredk',['$event']]]]]}}" class="flex" style="width:100%;" bindchange="__e"><view class="f1"><view>{{userinfo.score*1+" "+$root.m11+'可抵扣'}}<text style="color:#e94745;">{{userinfo.scoredk_money*1}}</text>元</view><block wx:if="{{userinfo.scoremaxtype==0&&userinfo.scoredkmaxpercent>0&&userinfo.scoredkmaxpercent<100}}"><view style="font-size:22rpx;color:#999;">{{'最多可抵扣订单金额的'+userinfo.scoredkmaxpercent+"%"}}</view></block><block wx:else><block wx:if="{{userinfo.scoremaxtype==1}}"><view style="font-size:22rpx;color:#999;">{{'最多可抵扣'+userinfo.scoredkmaxmoney+"元"}}</view></block></block></view><view class="f2">{{"使用"+$root.m12+'抵扣'}}<checkbox style="margin-left:6px;transform:scale(.8);" value="1"></checkbox></view></checkbox-group></view></block><block wx:if="{{userinfo.score2moneyhei>0&&(userinfo.scoremaxtypehei==0||userinfo.scoremaxtypehei==1&&userinfo.scoredkmaxmoneyhei>0)}}"><view class="scoredk"><checkbox-group data-event-opts="{{[['change',[['scoredkhei',['$event']]]]]}}" class="flex" style="width:100%;" bindchange="__e"><view class="f1"><view>{{userinfo.heiscore*1+" "+$root.m13+'可抵扣'}}<text style="color:#e94745;">{{userinfo.scorebdkyfhei*1}}</text>元</view><block wx:if="{{userinfo.scoremaxtypehei==0&&userinfo.scoredkmaxpercenthei>0&&userinfo.scoredkmaxpercenthei<100}}"><view style="font-size:22rpx;color:#999;">{{'最多可抵扣订单金额的'+userinfo.scoredkmaxpercenthei+"%"}}</view></block><block wx:else><block wx:if="{{userinfo.scoremaxtypehei==1}}"><view style="font-size:22rpx;color:#999;">{{'最多可抵扣'+userinfo.scoredkmaxmoneyhei+"元"}}</view></block></block></view><view class="f2">{{"使用"+$root.m14+'抵扣'}}<checkbox style="margin-left:6px;transform:scale(.8);" value="1"></checkbox></view></checkbox-group></view></block><block wx:if="{{userinfo.score2moneyyu>0&&(userinfo.scoremaxtypeyu==0||userinfo.scoremaxtypeyu==1&&userinfo.scoredkmaxmoneyyu>0)}}"><view class="scoredk"><checkbox-group data-event-opts="{{[['change',[['scoredkyu',['$event']]]]]}}" class="flex" style="width:100%;" bindchange="__e"><view class="f1"><view>{{userinfo.yue*1+" "+$root.m15+'可抵扣'}}<text style="color:#e94745;">{{userinfo.scoredkmaxpercentyu+"%"}}</text></view><block wx:if="{{userinfo.scoremaxtypeyu==0&&userinfo.scoredkmaxpercentyu>0&&userinfo.scoredkmaxpercentyu<100}}"><view style="font-size:22rpx;color:#999;">{{'最多可抵扣订单金额的'+userinfo.scoredkmaxpercentyu+"%"}}</view></block><block wx:else><block wx:if="{{userinfo.scoremaxtypeyu==1}}"><view style="font-size:22rpx;color:#999;">{{'最多可抵扣'+userinfo.scoredkmaxmoneyyu+"元"}}</view></block></block></view><view class="f2">{{"使用"+$root.m16+'抵扣'}}<checkbox style="margin-left:6px;transform:scale(.8);" value="1"></checkbox></view></checkbox-group></view></block><block wx:if="{{userinfo.score2moneyhuang>0&&(userinfo.scoremaxtypehuang==0||userinfo.scoremaxtypehuang==1&&userinfo.scoredkmaxmoneyhuang>0)}}"><view class="scoredk"><checkbox-group data-event-opts="{{[['change',[['scoredkhuang',['$event']]]]]}}" class="flex" style="width:100%;" bindchange="__e"><view class="f1"><view>{{userinfo.scorehuang*1+" "+$root.m17+'可抵扣'}}<text style="color:#e94745;">{{userinfo.scorebdkyfhuang*1}}</text>元</view><block wx:if="{{userinfo.scoremaxtypehuang==0&&userinfo.scoredkmaxpercenthuang>0&&userinfo.scoredkmaxpercenthuang<100}}"><view style="font-size:22rpx;color:#999;">{{'最多可抵扣订单金额的'+userinfo.scoredkmaxpercenthuang+"%"}}</view></block><block wx:else><block wx:if="{{userinfo.scoremaxtypehuang==1}}"><view style="font-size:22rpx;color:#999;">{{'最多可抵扣'+userinfo.scoredkmaxmoneyhuang+"元"}}</view></block></block></view><view class="f2">{{"使用"+$root.m18+'抵扣'}}<checkbox style="margin-left:6px;transform:scale(.8);" value="1"></checkbox></view></checkbox-group></view></block><view style="width:100%;height:110rpx;"></view><view class="footer flex notabbarbot"><view class="text1 flex1">总计：<text style="font-weight:bold;font-size:36rpx;">{{"￥"+alltotalprice}}</text></view><button class="op" style="{{'background:'+('linear-gradient(-90deg,'+$root.m19+' 0%,rgba('+$root.m20+',0.8) 100%)')+';'}}" form-type="submit" disabled="{{submitDisabled}}">提交订单</button></view></form><block wx:if="{{invoiceShow}}"><view class="popup__container"><view data-event-opts="{{[['tap',[['handleClickMask',['$event']]]]]}}" class="popup__overlay" catchtap="__e"></view><view class="popup__modal"><view class="popup__title"><text class="popup__title-text">请填写开票信息</text><image class="popup__close" style="width:36rpx;height:36rpx;" src="/static/img/close.png" data-event-opts="{{[['tap',[['handleClickMask',['$event']]]]]}}" catchtap="__e"></image></view><view class="popup__content invoiceBox"><form report-submit="true" data-event-opts="{{[['submit',[['invoiceFormSubmit',['$event']]]],['reset',[['formReset',['$event']]]]]}}" bindsubmit="__e" bindreset="__e"><view class="orderinfo"><view class="item"><text class="t1">发票类型</text><view class="t2"><radio-group class="radio-group" name="invoice_type" data-event-opts="{{[['change',[['changeOrderType',['$event']]]]]}}" bindchange="__e"><block wx:if="{{$root.m21}}"><label class="radio"><radio value="1" checked="{{invoice_type_select==1?true:false}}"></radio>普通发票</label></block><block wx:if="{{$root.m22}}"><label class="radio"><radio value="2" checked="{{invoice_type_select==2?true:false}}"></radio>增值税专用发票</label></block></radio-group></view></view><view class="item"><text class="t1">抬头类型</text><view class="t2"><block wx:if="{{inputDisabled}}"><block><block wx:if="{{invoice&&invoice.name_type==1}}"><text>个人</text></block><block wx:if="{{invoice&&invoice.name_type==2}}"><text>公司</text></block></block></block><block wx:else><block><radio-group class="radio-group" name="name_type" data-event-opts="{{[['change',[['changeNameType',['$event']]]]]}}" bindchange="__e"><label class="radio"><radio value="1" checked="{{name_type_select==1?true:false}}" disabled="{{name_type_personal_disabled?true:false}}"></radio>个人</label><label class="radio"><radio value="2" checked="{{name_type_select==2?true:false}}"></radio>公司</label></radio-group></block></block></view></view><view class="item"><text class="t1">抬头名称</text><input class="t2" type="text" placeholder="抬头名称" placeholder-style="font-size:28rpx;color:#BBBBBB" name="invoice_name" disabled="{{inputDisabled}}" value="{{invoice?invoice.invoice_name:''}}"/></view><block wx:if="{{name_type_select==2}}"><view class="item"><text class="t1">公司税号</text><input class="t2" type="text" placeholder="公司税号" placeholder-style="font-size:28rpx;color:#BBBBBB" name="tax_no" disabled="{{inputDisabled}}" value="{{invoice?invoice.tax_no:''}}"/></view></block><block wx:if="{{invoice_type_select==2}}"><view class="item"><text class="t1">注册地址</text><input class="t2" type="text" placeholder="注册地址" placeholder-style="font-size:28rpx;color:#BBBBBB" name="address" disabled="{{inputDisabled}}" value="{{invoice?invoice.address:''}}"/></view></block><block wx:if="{{invoice_type_select==2}}"><view class="item"><text class="t1">注册电话</text><input class="t2" type="text" placeholder="注册电话" placeholder-style="font-size:28rpx;color:#BBBBBB" name="tel" disabled="{{inputDisabled}}" value="{{invoice?invoice.tel:''}}"/></view></block><block wx:if="{{invoice_type_select==2}}"><view class="item"><text class="t1">开户银行</text><input class="t2" type="text" placeholder="开户银行" placeholder-style="font-size:28rpx;color:#BBBBBB" name="bank_name" disabled="{{inputDisabled}}" value="{{invoice?invoice.bank_name:''}}"/></view></block><block wx:if="{{invoice_type_select==2}}"><view class="item"><text class="t1">银行账号</text><input class="t2" type="text" placeholder="银行账号" placeholder-style="font-size:28rpx;color:#BBBBBB" name="bank_account" disabled="{{inputDisabled}}" value="{{invoice?invoice.bank_account:''}}"/></view></block><view class="item"><text class="t1">手机号</text><input class="t2" type="text" placeholder="接收电子发票手机号" placeholder-style="font-size:28rpx;color:#BBBBBB" name="mobile" disabled="{{inputDisabled}}" value="{{invoice?invoice.mobile:''}}"/></view><view class="item"><text class="t1">邮箱</text><input class="t2" type="text" placeholder="接收电子发票邮箱" placeholder-style="font-size:28rpx;color:#BBBBBB" name="email" disabled="{{inputDisabled}}" value="{{invoice?invoice.email:''}}"/></view></view><button class="btn" style="{{'background:'+($root.m23)+';'}}" form-type="submit">确定</button><view style="padding-top:30rpx;"></view></form></view></view></view></block><block wx:if="{{couponvisible}}"><view class="popup__container"><view data-event-opts="{{[['tap',[['handleClickMask',['$event']]]]]}}" class="popup__overlay" catchtap="__e"></view><view class="popup__modal"><view class="popup__title"><text class="popup__title-text">{{"请选择"+$root.m24}}</text><image class="popup__close" style="width:36rpx;height:36rpx;" src="/static/img/close.png" data-event-opts="{{[['tap',[['handleClickMask',['$event']]]]]}}" catchtap="__e"></image></view><view class="popup__content"><couponlist vue-id="3282c028-1" couponlist="{{allbuydata[bid].couponList}}" choosecoupon="{{true}}" selectedrids="{{allbuydata[bid].couponrids}}" bid="{{bid}}" data-event-opts="{{[['^chooseCoupon',[['chooseCoupon']]]]}}" bind:chooseCoupon="__e" bind:__l="__l"></couponlist></view></view></view></block><block wx:if="{{pstimeDialogShow}}"><view class="popup__container"><view data-event-opts="{{[['tap',[['hidePstimeDialog',['$event']]]]]}}" class="popup__overlay" catchtap="__e"></view><view class="popup__modal"><view class="popup__title"><text class="popup__title-text">{{"请选择"+(allbuydata[nowbid].freightList[allbuydata[nowbid].freightkey].pstype==1?'取货':'配送')+"时间"}}</text><image class="popup__close" style="width:36rpx;height:36rpx;" src="/static/img/close.png" data-event-opts="{{[['tap',[['hidePstimeDialog',['$event']]]]]}}" catchtap="__e"></image></view><view class="popup__content"><block wx:for="{{$root.l6}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="pstime-item" data-index="{{index}}" data-event-opts="{{[['tap',[['pstimeRadioChange',['$event']]]]]}}" bindtap="__e"><view class="flex1">{{item.$orig.title}}</view><view class="radio" style="{{(allbuydata[nowbid].freight_time==item.$orig.value?'background:'+item.m25+';border:0':'')}}"><image class="radio-img" src="/static/img/checkd.png"></image></view></view></block></view></view></view></block><block wx:if="{{cuxiaovisible}}"><view class="popup__container"><view data-event-opts="{{[['tap',[['handleClickMask',['$event']]]]]}}" class="popup__overlay" catchtap="__e"></view><view class="popup__modal"><view class="popup__title"><text class="popup__title-text">优惠促销</text><image class="popup__close" style="width:36rpx;height:36rpx;" src="/static/img/close.png" data-event-opts="{{[['tap',[['handleClickMask',['$event']]]]]}}" catchtap="__e"></image></view><view class="popup__content"><view class="cuxiao-desc"><block wx:if="{{multi_promotion}}"><block><view class="cuxiao-item" data-id="0" data-event-opts="{{[['tap',[['changecxMulti',['$event']]]]]}}" bindtap="__e"><view class="type-name"><text style="color:#333;">不使用促销</text></view><view class="radio" style="{{(cxid===0?'background:'+$root.m26+';border:0':'')}}"><image class="radio-img" src="/static/img/checkd.png"></image></view></view><block wx:for="{{$root.l7}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="cuxiao-item" data-id="{{item.$orig.id}}" data-index="{{index}}" data-event-opts="{{[['tap',[['changecxMulti',['$event']]]]]}}" bindtap="__e"><view class="type-name"><text style="border-radius:4px;border:1px solid #f05423;color:#ff550f;font-size:20rpx;padding:2px 5px;">{{item.$orig.tip}}</text><text style="color:#333;padding-left:20rpx;">{{item.$orig.name}}</text></view><view class="radio" style="{{(item.g5!==-1?'background:'+item.m27+';border:0':'')}}"><image class="radio-img" src="/static/img/checkd.png"></image></view></view></block></block></block><block wx:else><block><view class="cuxiao-item" data-id="0" data-event-opts="{{[['tap',[['changecx',['$event']]]]]}}" bindtap="__e"><view class="type-name"><text style="color:#333;">不使用促销</text></view><view class="radio" style="{{(cxid==0?'background:'+$root.m28+';border:0':'')}}"><image class="radio-img" src="/static/img/checkd.png"></image></view></view><block wx:for="{{$root.l8}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="cuxiao-item" data-id="{{item.$orig.id}}" data-index="{{index}}" data-event-opts="{{[['tap',[['changecx',['$event']]]]]}}" bindtap="__e"><view class="type-name"><text style="border-radius:4px;border:1px solid #f05423;color:#ff550f;font-size:20rpx;padding:2px 5px;">{{item.$orig.tip}}</text><text style="color:#333;padding-left:20rpx;">{{item.$orig.name}}</text></view><view class="radio" style="{{(cxid==item.$orig.id?'background:'+item.m29+';border:0':'')}}"><image class="radio-img" src="/static/img/checkd.png"></image></view></view></block></block></block></view><block wx:if="{{cuxiaoinfo.product}}"><view style="padding:0 40rpx;" id="cxproinfo"><view class="product"><view class="item flex" style="background:#f5f5f5;"><view class="img" data-url="{{'/shopPackage/shop/product?id='+cuxiaoinfo.product.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image src="{{cuxiaoinfo.product.pic}}"></image></view><view class="info flex1"><view class="f1">{{cuxiaoinfo.product.name}}</view><view class="f2">{{"规格："+cuxiaoinfo.guige.name}}</view><view class="f3"><text style="font-weight:bold;">{{"￥"+cuxiaoinfo.guige.sell_price}}</text><text style="padding-left:20rpx;">× 1</text></view></view></view></view></view></block><view style="width:100%;height:120rpx;"></view><view style="width:100%;position:absolute;bottom:0;padding:20rpx 5%;background:#fff;"><view data-event-opts="{{[['tap',[['chooseCuxiao',['$event']]]]]}}" style="{{'width:100%;height:80rpx;line-height:80rpx;border-radius:40rpx;text-align:center;color:#fff;'+('background:'+($root.m30)+';')}}" bindtap="__e">确 定</view></view></view></view></view></block><block wx:if="{{type11visible}}"><view class="popup__container"><view data-event-opts="{{[['tap',[['handleClickMask',['$event']]]]]}}" class="popup__overlay" catchtap="__e"></view><view class="popup__modal"><view class="popup__title"><text class="popup__title-text">选择物流</text><image class="popup__close" style="width:36rpx;height:36rpx;" src="/static/img/close.png" data-event-opts="{{[['tap',[['handleClickMask',['$event']]]]]}}" catchtap="__e"></image></view><view class="popup__content"><view class="cuxiao-desc"><block wx:for="{{$root.l9}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block wx:if="{{address.id&&address.province==item.$orig.province&&address.city==item.$orig.city&&address.district==item.$orig.area}}"><view style="padding:0 30rpx 20rpx 40rpx;" data-index="{{index}}" data-event-opts="{{[['tap',[['changetype11',['$event']]]]]}}" bindtap="__e"><view class="cuxiao-item" style="padding:0;"><view class="type-name"><text style="color:#333;font-weight:bold;">{{item.$orig.name}}</text></view><view class="radio" style="{{(type11key==index?'background:'+item.m31+';border:0':'')}}"><image class="radio-img" src="/static/img/checkd.png"></image></view></view><view style="margin-left:20rpx;">{{"发货: "+item.$orig.send_address+" - "+item.$orig.send_tel}}</view><view style="margin-left:20rpx;">{{"收货: "+item.$orig.receive_address+" - "+item.$orig.receive_tel+''}}</view></view></block></block></view><view style="width:100%;height:120rpx;"></view><view style="width:100%;position:absolute;bottom:0;padding:20rpx 5%;background:#fff;"><view data-event-opts="{{[['tap',[['chooseType11',['$event']]]]]}}" style="{{'width:100%;height:80rpx;line-height:80rpx;border-radius:40rpx;text-align:center;color:#fff;'+('background:'+($root.m32)+';')}}" bindtap="__e">确 定</view></view></view></view></view></block><block wx:if="{{membervisible}}"><view class="popup__container"><view data-event-opts="{{[['tap',[['handleClickMask',['$event']]]]]}}" class="popup__overlay" catchtap="__e"></view><view class="popup__modal" style="height:1100rpx;"><view class="popup__title"><text class="popup__title-text">请选择指定会员</text><image class="popup__close" style="width:36rpx;height:36rpx;" src="/static/img/close.png" data-event-opts="{{[['tap',[['handleClickMask',['$event']]]]]}}" catchtap="__e"></image></view><view class="popup__content"><view class="member_search"><view style="width:130rpx;color:#333;flex-shrink:0;">选择地区</view><uni-data-picker class="flex1" style="overflow:hidden;" vue-id="3282c028-2" localdata="{{items}}" border="{{false}}" placeholder="{{regiondata||'请选择省市区'}}" data-event-opts="{{[['^change',[['regionchange2']]]]}}" bind:change="__e" bind:__l="__l"></uni-data-picker><view data-event-opts="{{[['tap',[['memberSearch',['$event']]]]]}}" class="searchMemberButton" style="flex-shrink:0;" bindtap="__e">检索用户</view></view><view class="memberlist"><block wx:for="{{$root.l10}}" wx:for-item="item2" wx:for-index="i" wx:key="i"><view class="memberitem" data-info="{{item2.$orig}}" data-event-opts="{{[['tap',[['checkMember',['$event']]]]]}}" bindtap="__e"><image src="{{item2.$orig.headimg}}" data-mid="{{item2.$orig.id}}" data-event-opts="{{[['tap',[['showmemberinfo',['$event']]]]]}}" catchtap="__e"></image><view class="flex-col"><view class="t1" data-mid="{{item2.$orig.id}}" data-event-opts="{{[['tap',[['showmemberinfo',['$event']]]]]}}" catchtap="__e">{{item2.$orig.nickname}}</view><view>{{item2.$orig.province+" "+item2.$orig.city+" "+item2.$orig.area}}</view></view><view class="flex1"></view><view class="radio" style="{{(checkMem.id==item2.$orig.id?'background:'+item2.m33+';border:0':'')}}"><image class="radio-img" src="/static/img/checkd.png"></image></view></view></block></view></view></view></view></block><block wx:if="{{memberinfovisible}}"><view class="popup__container"><view data-event-opts="{{[['tap',[['memberinfoClickMask',['$event']]]]]}}" class="popup__overlay" catchtap="__e"></view><view class="popup__modal" style="height:1100rpx;"><view class="popup__title"><text class="popup__title-text">查看资料</text><image class="popup__close" style="width:36rpx;height:36rpx;" src="/static/img/close.png" data-event-opts="{{[['tap',[['memberinfoClickMask',['$event']]]]]}}" catchtap="__e"></image></view><view class="popup__content"><view class="orderinfo"><block wx:for="{{selectmemberinfo}}" wx:for-item="item" wx:for-index="__i0__" wx:key="*this"><view class="item"><text class="t1">{{item[0]}}</text><block wx:if="{{item[2]=='upload'}}"><view class="t2"><image style="width:400rpx;height:auto;" src="{{item[1]}}" mode="widthFix" data-url="{{item[1]}}" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image></view></block><block wx:else><text class="t2">{{item[1]}}</text></block></view></block></view></view></view></view></block><block wx:if="{{isshowglass}}"><view class="popup__container glass_popup"><view data-event-opts="{{[['tap',[['hideglass',['$event']]]]]}}" class="popup__overlay" catchtap="__e"></view><view class="popup__modal" style="height:1100rpx;"><view class="popup__title"><text class="popup__title-text">视力档案</text><image class="popup__close" style="width:36rpx;height:36rpx;" src="/static/img/close.png" data-event-opts="{{[['tap',[['hideglass',['$event']]]]]}}" catchtap="__e"></image></view><view class="popup__content"><radio-group data-event-opts="{{[['change',[['chooseglass',['$event']]]]]}}" bindchange="__e"><block wx:for="{{$root.l11}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><label><view class="{{['glassitem',grid==item.$orig.id?'on':'']}}"><view class="radio"><radio color="{{item.m34}}" checked="{{grid==item.$orig.id?true:false}}" value="{{''+index}}"></radio></view><view class="gcontent"><view class="glassrow"><view class="glasscol"><view class="num">{{item.$orig.type==1?'近视':'远视'}}</view><view class="txt">{{item.$orig.is_ats==1?'散光':''}}</view></view><view class="glasscol"><view class="num">{{item.$orig.degress_right}}</view><view class="txt">右眼</view></view><view class="glasscol"><view class="num">{{item.$orig.degress_left}}</view><view class="txt">左眼</view></view><view class="glasscol"><view class="num">{{item.$orig.ipd+"mm"}}</view><view class="txt">瞳距</view></view></view><block wx:if="{{item.$orig.is_ats==1}}"><block><view class="glassrow bt"><view class="glasscol"><view class="num">{{item.$orig.ats_right}}</view><view class="txt">柱镜右眼</view></view><view class="glasscol"><view class="num">{{item.$orig.ats_left}}</view><view class="txt">柱镜左眼</view></view><view class="glasscol"><view class="num">{{item.$orig.ats_zright}}</view><view class="txt">轴位右眼</view></view><view class="glasscol"><view class="num">{{item.$orig.ats_zleft}}</view><view class="txt">轴位左眼</view></view></view></block></block></view></view></label></block></block></radio-group></view></view></view></block></block></block><block wx:if="{{loading}}"><loading vue-id="3282c028-3" bind:__l="__l"></loading></block><dp-tabbar vue-id="3282c028-4" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="3282c028-5" data-ref="popmsg" bind:__l="__l"></popmsg></view>