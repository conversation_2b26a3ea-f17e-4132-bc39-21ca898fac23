<view class="container"><block wx:if="{{isload}}"><block><view class="navbg"></view><view class="nav"><scroll-view scroll-x="true" scroll-left="{{top_bar_scroll}}"><view class="f1"><block wx:for="{{navlist}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><view class="{{['item '+(selected==index?'active':'')]}}" data-index="{{index}}" data-event-opts="{{[['tap',[['changetab',['$event']]]]]}}" bindtap="__e"><view class="t3">{{item.seckill_date_m}}</view><view class="t1">{{item.showtime}}</view><block wx:if="{{item.active==-1}}"><view class="t2">已结束</view></block><block wx:if="{{item.active==0}}"><view class="t2">已开抢</view></block><block wx:if="{{item.active==1}}"><view class="t2">抢购中</view></block><block wx:if="{{item.active==2}}"><view class="t2">即将开始</view></block></view></block></block></view></scroll-view></view><view class="content"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="item"><image class="f1" mode="widthFix" src="{{item.$orig.pic}}" data-url="{{'product?id='+item.$orig.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"></image><view class="f2"><text class="t1">{{item.$orig.name}}</text><view class="t2" style="{{'color:'+(item.m0)+';'}}"><progress class="progress" percent="{{item.$orig.salepercent}}" backgroundColor="#FFD1C9" activeColor="#FF3143" border-radius="3"></progress><text class="x2">{{item.$orig.salepercent+"%"}}</text></view><view class="t3"><text class="x1" style="{{'color:'+(item.m1)+';'}}"><text style="font-size:24rpx;">￥</text>{{item.$orig.sell_price}}</text><text class="x2">{{"￥"+item.$orig.market_price}}</text><block wx:if="{{item.$orig.starttime<nowtime&&seckill_duration*3600+item.$orig.starttime*1>nowtime}}"><button class="x3" style="{{'background:'+('linear-gradient(90deg,'+item.m2+' 0%,rgba('+item.m3+',0.8) 100%)')+';'}}" data-url="{{'product?id='+item.$orig.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">立即抢购</button></block><block wx:else><block wx:if="{{item.$orig.starttime*1+seckill_duration*3600<nowtime}}"><button class="x3 xx1" data-url="{{'product?id='+item.$orig.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">去看看</button></block><block wx:else><button class="x3" style="{{'background:'+(item.m4)+';'}}" data-url="{{'product?id='+item.$orig.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">抢先看看</button></block></block></view></view></view></block><block wx:if="{{nodata}}"><view class="item" style="display:block;"><nodata vue-id="053c6c06-1" bind:__l="__l"></nodata></view></block><block wx:if="{{nomore}}"><nomore vue-id="053c6c06-2" bind:__l="__l"></nomore></block></view></block></block><block wx:if="{{loading}}"><loading vue-id="053c6c06-3" bind:__l="__l"></loading></block><dp-tabbar vue-id="053c6c06-4" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="053c6c06-5" data-ref="popmsg" bind:__l="__l"></popmsg></view>