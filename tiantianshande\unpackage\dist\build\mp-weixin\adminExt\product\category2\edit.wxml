<view><block wx:if="{{isload}}"><block><form data-event-opts="{{[['submit',[['subform',['$event']]]]]}}" bindsubmit="__e"><view class="form-box"><view class="form-item"><view class="f1">商家商品分类<text style="color:red;">*</text></view><view data-event-opts="{{[['tap',[['changeClist2Dialog',['$event']]]]]}}" class="f2" bindtap="__e"><block wx:if="{{cid>0}}"><text>{{cname}}</text></block><block wx:else><text style="color:#888;">顶级分类</text></block><image style="width:30rpx;height:30rpx;" src="{{pre_url+'/static/img/arrowright.png'}}"></image></view></view><view class="form-item"><view class="f1">分类名称<text style="color:red;">*</text></view><view class="f2"><input type="text" name="name" placeholder="请填写名称" placeholder-style="color:#888" value="{{info.name}}"/></view></view><view class="form-item flex-col" style="border-bottom:0;"><view class="f1">图片</view><view class="f2"><block wx:for="{{pic}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="layui-imgbox"><view class="layui-imgbox-close" data-index="{{index}}" data-field="pic" data-event-opts="{{[['tap',[['removeimg',['$event']]]]]}}" bindtap="__e"><image style="display:block;" src="{{pre_url+'/static/img/ico-del.png'}}"></image></view><view class="layui-imgbox-img"><image src="{{item}}" data-url="{{item}}" mode="widthFix" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image></view></view></block><block wx:if="{{$root.g0==0}}"><view class="uploadbtn" style="{{('background:url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 60rpx;background-size:80rpx 80rpx;background-color:#F3F3F3;')}}" data-field="pic" data-pernum="1" data-event-opts="{{[['tap',[['uploadimg',['$event']]]]]}}" bindtap="__e"></view></block></view><input type="text" hidden="true" name="pic" maxlength="-1" value="{{$root.g1}}"/></view><view class="form-item"><view class="f1">排序</view><view class="f2"><input type="text" name="sort" placeholder="用于排序,越大越靠前" placeholder-style="color:#888" value="{{info.sort}}"/></view></view><view class="form-item"><view>状态<text style="color:red;">*</text></view><view><radio-group class="radio-group" name="status" data-event-opts="{{[['change',[['',['$event']]]]]}}" bindchange="__e"><label><radio value="1" checked="{{info.status==1?true:false}}"></radio>显示</label><label><radio value="0" checked="{{!info||info.status==0?true:false}}"></radio>隐藏</label></radio-group></view></view></view><button class="savebtn" style="{{('background:linear-gradient(90deg,'+$root.m0+' 0%,rgba('+$root.m1+',0.8) 100%)')}}" form-type="submit">提交</button><block wx:if="{{info.id}}"><button data-event-opts="{{[['tap',[['todel',['$event']]]]]}}" class="button text-btn" bindtap="__e">删除</button></block><view style="height:50rpx;"></view></form><block wx:if="{{clist2show}}"><view class="popup__container"><view data-event-opts="{{[['tap',[['changeClist2Dialog',['$event']]]]]}}" class="popup__overlay" catchtap="__e"></view><view class="popup__modal"><view class="popup__title"><text class="popup__title-text">请选择分类</text><image class="popup__close" style="width:36rpx;height:36rpx;" src="{{pre_url+'/static/img/close.png'}}" data-event-opts="{{[['tap',[['changeClist2Dialog',['$event']]]]]}}" catchtap="__e"></image></view><view class="popup__content"><block wx:for="{{$root.l1}}" wx:for-item="item" wx:for-index="index"><block><view class="clist-item" data-id="{{item.$orig.id}}" data-event-opts="{{[['tap',[['cids2Change',['$event']]]]]}}" bindtap="__e"><view class="flex1">{{item.$orig.name}}</view><view class="radio" style="{{(item.$orig.id==cid?'background:'+item.m2+';border:0':'')}}"><image class="radio-img" src="{{pre_url+'/static/img/checkd.png'}}"></image></view></view><block wx:for="{{item.l0}}" wx:for-item="item2" wx:for-index="index2" wx:key="id"><block><view class="clist-item" style="padding-left:80rpx;" data-id="{{item2.$orig.id}}" data-event-opts="{{[['tap',[['cids2Change',['$event']]]]]}}" bindtap="__e"><block wx:if="{{item.g2-1==index2}}"><view class="flex1">{{"└ "+item2.$orig.name}}</view></block><block wx:else><view class="flex1">{{"├ "+item2.$orig.name}}</view></block><view class="radio" style="{{(item2.$orig.id==cid?'background:'+item2.m3+';border:0':'')}}"><image class="radio-img" src="{{pre_url+'/static/img/checkd.png'}}"></image></view></view></block></block></block></block></view></view></view></block></block></block><block wx:if="{{loading}}"><loading vue-id="7543b7e2-1" bind:__l="__l"></loading></block></view>