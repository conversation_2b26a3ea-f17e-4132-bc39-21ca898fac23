<view><view class="common-header education-header pb16"><view class="header-top"><view class="common-header-title">{{partJobVo.title}}</view><block wx:if="{{partJobVo.commission_detail&&partJobVo.commission_detail.calculation}}"><view class="commission-info" style="{{'background:'+($root.m0)+';'+('opacity:'+(0.8)+';')}}"><text class="commission-text" style="{{'color:'+('#fff')+';'}}">{{partJobVo.commission_detail.calculation+''}}</text></view></block></view><view class="rpo-salary"><view class="rpo-salary-num">{{partJobVo.salary}}</view></view><view class="common-list-tag list-tag-box"><block wx:if="{{partJobVo.formatted_options}}"><block wx:for="{{$root.l0}}" wx:for-item="tag" wx:for-index="index"><view class="list-tag"><view class="ellipsis">{{tag}}</view></view></block></block><block wx:if="{{partJobVo.work_mode}}"><view class="list-tag"><view class="ellipsis">{{partJobVo.work_mode}}</view></view></block><block wx:if="{{partJobVo.work_intensity}}"><view class="list-tag"><view class="ellipsis">{{partJobVo.work_intensity}}</view></view></block><block wx:if="{{partJobVo.work_time_type}}"><view class="list-tag"><view class="ellipsis">{{partJobVo.work_time_type}}</view></view></block><block wx:if="{{partJobVo.payment}}"><view class="list-tag"><view class="ellipsis">{{partJobVo.payment}}</view></view></block><block wx:if="{{partJobVo.education}}"><view class="list-tag"><view class="ellipsis">{{partJobVo.education}}</view></view></block><block wx:if="{{partJobVo.experience}}"><view class="list-tag"><view class="ellipsis">{{partJobVo.experience}}</view></view></block></view><view class="common-safe ptp_exposure_static" style="{{'background:'+($root.m1)+';'+('opacity:'+(0.8)+';')}}" data-ptpid="69a4-1137-8c63-fcc7" id="pid=69a4-1137-8c63-fcc7" data-event-opts="{{[['tap',[['skiptoWebview',['$event']]]]]}}" bindtap="__e"><view class="common-safe-left"><view class="iconfont iconsafeguard" style="{{'color:'+($root.m2)+';'}}"></view><view style="{{'color:'+('#fff')+';'+('font-weight:'+('bold')+';')}}">此职位经过官方认证，放心投递+职位无忧</view></view><view class="iconfont iconarrow" style="{{'color:'+($root.m3)+';'}}"></view></view></view><view class="common-title" style="margin-top:48rpx;">基本信息</view><view class="common-box"><block wx:if="{{partJobVo.work_address}}"><view class="info-item"><view class="info-label">工作地点</view><view class="info-content">{{partJobVo.work_address}}</view></view></block><block wx:if="{{partJobVo.education}}"><view class="info-item"><view class="info-label">学历要求</view><view class="info-content">{{partJobVo.education}}</view></view></block><block wx:if="{{partJobVo.experience}}"><view class="info-item"><view class="info-label">经验要求</view><view class="info-content">{{partJobVo.experience}}</view></view></block><block wx:if="{{partJobVo.age_requirement}}"><view class="info-item"><view class="info-label">年龄要求</view><view class="info-content">{{partJobVo.age_requirement}}</view></view></block><block wx:if="{{partJobVo.gender_requirement!==undefined}}"><view class="info-item"><view class="info-label">性别要求</view><view class="info-content">{{$root.m4}}</view></view></block><block wx:if="{{partJobVo.numbers}}"><view class="info-item"><view class="info-label">招聘人数</view><view class="info-content">{{partJobVo.numbers+"人"}}</view></view></block></view><view class="common-title" style="margin-top:48rpx;">工作安排</view><view class="common-box"><block wx:if="{{partJobVo.work_mode}}"><view class="info-item"><view class="info-label">工作形式</view><view class="info-content">{{partJobVo.work_mode}}</view></view></block><block wx:if="{{partJobVo.payment}}"><view class="info-item"><view class="info-label">结算方式</view><view class="info-content">{{partJobVo.payment}}</view></view></block><block wx:if="{{partJobVo.work_time_start||partJobVo.work_time_end}}"><view class="info-item"><view class="info-label">工作时间</view><view class="info-content">{{partJobVo.work_time_start+" - "+partJobVo.work_time_end}}</view></view></block><block wx:if="{{partJobVo.work_time_type}}"><view class="info-item"><view class="info-label">班次类型</view><view class="info-content">{{partJobVo.work_time_type}}</view></view></block><block wx:if="{{partJobVo.rest_time}}"><view class="info-item"><view class="info-label">休息时间</view><view class="info-content">{{partJobVo.rest_time}}</view></view></block></view><view class="common-title" style="margin-top:48rpx;">福利待遇</view><view class="common-box"><block wx:if="{{partJobVo.meal_provided!==undefined}}"><view class="info-item"><view class="info-label">工作餐</view><view class="info-content">{{partJobVo.meal_provided===1?'提供':'不提供'}}</view></view></block><block wx:if="{{partJobVo.meal_provided===0&&partJobVo.meal_allowance}}"><view class="info-item"><view class="info-label">餐费补贴</view><view class="info-content">{{partJobVo.meal_allowance}}</view></view></block><block wx:if="{{partJobVo.housing_provided!==undefined}}"><view class="info-item"><view class="info-label">住宿</view><view class="info-content">{{$root.m5}}</view></view></block><block wx:if="{{partJobVo.housing_provided===2&&partJobVo.housing_fee}}"><view class="info-item"><view class="info-label">住宿费用</view><view class="info-content">{{partJobVo.housing_fee+"元/月"}}</view></view></block></view><view class="common-title" style="margin-top:48rpx;">其他福利</view><view class="common-box"><view class="{{['detail-info-box '+(isBenefitsShowBtn?'':'detail-info-show')]}}"><rich-text class="detail-info-text" nodes="{{$root.m6}}"></rich-text></view><block wx:if="{{isBenefitsShowBtn&&isComputedBenefits}}"><view data-event-opts="{{[['tap',[['benefitsBtnTap',['$event']]]]]}}" class="detail-info-btn" bindtap="__e">查看更多<view class="iconfont iconarrow_down"></view></view></block></view><view class="common-title" style="margin-top:48rpx;">职位描述</view><view class="common-box"><view class="{{['detail-info-box '+(isDescShowBtn?'':'detail-info-show')]}}"><rich-text class="detail-info-text" nodes="{{$root.m7}}"></rich-text></view><block wx:if="{{isDescShowBtn&&isComputedDesc}}"><view data-event-opts="{{[['tap',[['descBtnTap',['$event']]]]]}}" class="detail-info-btn" bindtap="__e">查看更多<view class="iconfont iconarrow_down"></view></view></block></view><block wx:if="{{partJobVo.video_info&&partJobVo.video_info.url}}"><view class="common-title" style="margin-top:48rpx;">视频介绍</view></block><block wx:if="{{partJobVo.video_info&&partJobVo.video_info.url}}"><view class="common-box video-box"><video class="job-video" src="{{partJobVo.video_info.url}}" poster="{{partJobVo.video_info.poster}}" controls="{{true}}" show-center-play-btn="{{true}}" enable-progress-gesture="{{true}}" show-fullscreen-btn="{{true}}" show-play-btn="{{true}}" show-progress="{{true}}" object-fit="contain"></video></view></block><block wx:if="{{partJobVo.requirement}}"><view class="common-title" style="margin-top:48rpx;">任职要求</view></block><block wx:if="{{partJobVo.requirement}}"><view class="common-box"><view class="{{['detail-info-box '+(isReqShowBtn?'':'detail-info-show')]}}"><rich-text class="detail-info-text" nodes="{{$root.m8}}"></rich-text></view><block wx:if="{{isReqShowBtn&&isComputedReq}}"><view data-event-opts="{{[['tap',[['reqBtnTap',['$event']]]]]}}" class="detail-info-btn" bindtap="__e">查看更多<view class="iconfont iconarrow_down"></view></view></block></view></block><block wx:if="{{partJobVo.company_introduction&&partJobVo.company_show===1}}"><view class="common-title" style="margin-top:48rpx;">公司介绍</view></block><block wx:if="{{partJobVo.company_introduction&&partJobVo.company_show===1}}"><view class="common-box"><view class="{{['detail-info-box '+(isCompanyShowBtn?'':'detail-info-show')]}}"><rich-text class="detail-info-text" nodes="{{$root.m9}}"></rich-text></view><block wx:if="{{isCompanyShowBtn&&isComputedCompany}}"><view data-event-opts="{{[['tap',[['companyBtnTap',['$event']]]]]}}" class="detail-info-btn" bindtap="__e">查看更多<view class="iconfont iconarrow_down"></view></view></block></view></block><block wx:if="{{partJobVo.company_show===1}}"><view class="common-title" style="margin-top:48rpx;">{{partJobVo.company.companyType===2?'发布者':'发布企业'}}</view></block><block wx:if="{{partJobVo.company_show===1}}"><view class="{{['common-box',partJobVo.company.isOfficialAccount||partJobVo.company.companyType===2||partJobVo.company.companyType===1&&hasEyeAuth?'pb0':'']}}"><view class="common-company ptp_exposure_static" data-ptpid="c4e6-18fa-96fc-d96d" id="pid=c4e6-18fa-96fc-d96d" data-event-opts="{{[['tap',[['$emit',['clickCompany']]]]]}}" bindtap="__e"><view class="common-company-box"><image class="common-company-logo" lazyLoad="{{true}}" src="{{partJobVo.company.logo||'https://qiniu-image.qtshe.com/company_default_5.4.png'}}"></image><view class="common-company-main"><view class="common-company-name ellipsis">{{partJobVo.company.name}}</view><view class="common-company-auth ignoreT2"><view class="iconfont iconverified company-icon-blue"></view><view>企业认证</view><image class="common-company-auth-icon" mode="scaleToFill" src="https://qiniu-image.qtshe.com/20210106_icon.png"></image></view></view></view><block wx:if="{{hasEyeAuth}}"><view class="iconfont iconarrow"></view></block></view><view class="official-box"><view class="official-item"><view class="iconfont iconconfirm_round"></view>已通过天眼查认证</view></view></view></block></view>