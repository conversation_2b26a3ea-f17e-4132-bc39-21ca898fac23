<view><block wx:for="{{data}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="item-list" data-url="{{'/daihuobiji/kuaituan/detail?id='+item.proid}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="item-header"><image class="item-image" src="{{item.pic}}"></image><view class="item-author">{{item.author}}</view></view><view class="item-name">{{item.name}}</view><view class="item-price"><text class="currency-symbol">￥</text><text class="price-value">{{item.priceRange}}</text></view><view class="item-pics"><block wx:for="{{item.pic2}}" wx:for-item="ite" wx:for-index="ind" wx:key="ind"><block wx:if="{{ind<3}}"><image class="item-pic" src="{{ite}}"></image></block></block></view><view class="item-footer"><view class="viewers"><image class="viewer-icon" src="https://qixian.zhonghengyikang.com.cn/static/img/touxiang.png"></image><image class="viewer-icon" src="https://qixian.zhonghengyikang.com.cn/static/img/touxiang.png"></image><image class="viewer-icon" src="https://qixian.zhonghengyikang.com.cn/static/img/touxiang.png"></image><text class="view-count">{{item.readcount+"人看过"}}</text></view><view class="share"><image class="share-icon" src="../../static/img/share.png"></image><text class="share-text">点击分享</text></view></view></view></block></view>