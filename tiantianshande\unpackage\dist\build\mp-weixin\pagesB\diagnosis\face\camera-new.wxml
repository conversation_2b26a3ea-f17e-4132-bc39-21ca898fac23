<view class="camera-container"><block wx:if="{{!capturedImage}}"><view class="camera-mode"><camera class="camera-preview" device-position="{{devicePosition}}" flash="{{flashMode}}" data-event-opts="{{[['error',[['onCameraError',['$event']]]],['initdone',[['onCameraInit',['$event']]]]]}}" binderror="__e" bindinitdone="__e"></camera><view class="status-bar"><view data-event-opts="{{[['touchstart',[['goBack',['$event']]]],['tap',[['goBack',['$event']]]]]}}" class="back-btn" bindtouchstart="__e" bindtap="__e"><text class="back-icon">←</text></view><text class="page-title">面诊拍摄</text><view data-event-opts="{{[['touchstart',[['toggleFlash',['$event']]]],['tap',[['toggleFlash',['$event']]]]]}}" class="{{['flash-btn',(flashOn)?'active':'']}}" bindtouchstart="__e" bindtap="__e"><text class="flash-icon">{{flashOn?'💡':'🔦'}}</text></view></view><view class="guide-overlay"><view class="guide-frame"><view class="frame-corner corner-tl"></view><view class="frame-corner corner-tr"></view><view class="frame-corner corner-bl"></view><view class="frame-corner corner-br"></view><view class="frame-center"><text class="guide-text">请将面部置于框内</text><view class="pulse-dot"></view></view></view></view><view class="tips-section"><view class="tips-container"><block wx:for="{{shootingTips}}" wx:for-item="tip" wx:for-index="index" wx:key="index"><view class="tip-item"><view class="tip-icon">{{tip.icon}}</view><text class="tip-text">{{tip.text}}</text></view></block></view></view><view class="control-section"><view class="control-container"><view class="control-item"><view data-event-opts="{{[['touchstart',[['chooseImage',['$event']]]],['tap',[['chooseImage',['$event']]]]]}}" class="control-btn secondary" bindtouchstart="__e" bindtap="__e"><image class="control-icon-img" src="{{pre_url+'/static/img/xuanzetupian.png'}}" mode="aspectFit"></image></view><text class="control-label">选择图片</text></view><view class="control-item capture"><view data-event-opts="{{[['touchstart',[['takePhoto',['$event']]]],['tap',[['takePhoto',['$event']]]]]}}" class="{{['control-btn','primary',(captureLoading)?'capturing':'']}}" bindtouchstart="__e" bindtap="__e"><block wx:if="{{!captureLoading}}"><view class="capture-ring"></view></block><view class="capture-dot"></view></view><text class="control-label">拍照</text></view><view class="control-item"><view data-event-opts="{{[['touchstart',[['flipCamera',['$event']]]],['tap',[['flipCamera',['$event']]]]]}}" class="{{['control-btn','secondary',(isFlipping)?'active':'']}}" bindtouchstart="__e" bindtap="__e"><image class="control-icon-img" src="{{pre_url+'/static/img/fanzhuanxiangji.png'}}" mode="aspectFit"></image></view><text class="control-label">翻转相机</text></view></view></view></view></block><block wx:else><view class="preview-mode"><view class="preview-header"><view class="header-bg-pattern"></view><view class="header-content"><view class="status-indicator"><view class="status-dot"></view><text class="status-text">AI 分析就绪</text></view><text class="preview-title">图像预览</text><text class="preview-subtitle">AI 正在检测图像质量</text></view></view><view class="preview-container"><view class="preview-main"><view class="image-container" style="border:2px solid red;"><view class="image-frame" style="border:2px solid blue;"><image class="preview-image" style="border:2px solid yellow;" src="{{capturedImage}}" mode="aspectFit" show-loading="{{true}}" data-event-opts="{{[['error',[['onImageError',['$event']]]],['load',[['onImageLoad',['$event']]]]]}}" binderror="__e" bindload="__e"></image><block wx:if="{{imageLoadError}}"><view class="image-error"><text class="error-text">图片加载失败</text><text class="error-tip">请重新拍摄或选择图片</text></view></block><view class="scan-line"></view><view class="analysis-overlay"><block wx:for="{{analysisPoints}}" wx:for-item="point" wx:for-index="index" wx:key="index"><view class="analysis-point" style="{{'left:'+(point.x+'%')+';'+('top:'+(point.y+'%')+';')+('animation-delay:'+(index*0.2+'s')+';')}}"><view class="point-core"></view><view class="point-ring"></view><view class="point-pulse"></view></view></block></view><view class="grid-overlay"><block wx:for="{{3}}" wx:for-item="i" wx:for-index="__i0__"><view class="grid-line grid-vertical" style="{{'left:'+(i*25+'%')+';'}}"></view></block><block wx:for="{{3}}" wx:for-item="i" wx:for-index="__i1__"><view class="grid-line grid-horizontal" style="{{'top:'+(i*25+'%')+';'}}"></view></block></view></view></view><view class="quality-panel"><view class="panel-header"><view class="panel-icon">🔍</view><text class="panel-title">质量检测</text><view class="panel-status"><view class="status-bar"><view class="status-progress" style="{{'width:'+($root.m0+'%')+';'}}"></view></view><text class="status-score">{{$root.m1+"%"}}</text></view></view><view class="quality-grid"><block wx:for="{{qualityChecks}}" wx:for-item="check" wx:for-index="index" wx:key="index"><view class="quality-card"><view class="{{['card-icon',check.status]}}"><text>{{check.status==='pass'?'✓':'⚠'}}</text></view><text class="card-text">{{check.text}}</text><view class="{{['card-indicator',check.status]}}"></view></view></block></view></view></view></view><view class="preview-actions"><view class="action-container"><view data-event-opts="{{[['touchstart',[['retakePhoto',['$event']]]],['tap',[['retakePhoto',['$event']]]]]}}" class="action-btn secondary" bindtouchstart="__e" bindtap="__e"><view class="btn-bg"></view><view class="btn-content"><view class="btn-icon"><text>↻</text></view><text class="btn-text">重新拍摄</text></view></view><view data-event-opts="{{[['touchstart',[['confirmPhoto',['$event']]]],['tap',[['confirmPhoto',['$event']]]]]}}" class="action-btn primary" bindtouchstart="__e" bindtap="__e"><view class="btn-bg"></view><view class="btn-content"><view class="btn-icon"><text>⚡</text></view><text class="btn-text">开始分析</text></view><view class="btn-particles"><block wx:for="{{6}}" wx:for-item="i" wx:for-index="__i2__" wx:key="*this"><view class="particle" style="{{'animation-delay:'+(i*0.1+'s')+';'}}"></view></block></view></view></view></view></view></block><block wx:if="{{isLoading}}"><view class="loading-overlay"><view class="loading-content"><view class="loading-spinner"><view class="spinner-ring"></view><view class="spinner-dot"></view></view><text class="loading-text">{{loadingText}}</text></view></view></block></view>