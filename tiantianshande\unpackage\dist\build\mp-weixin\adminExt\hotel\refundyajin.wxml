<view class="container"><block wx:if="{{isload}}"><block><dd-tab vue-id="3e0deae1-1" itemdata="{{['全部','待审核','已同意','驳回']}}" itemst="{{['all','1','2','-1']}}" st="{{st}}" isfixed="{{true}}" data-event-opts="{{[['^changetab',[['changetab']]]]}}" bind:changetab="__e" bind:__l="__l"></dd-tab><view style="width:100%;height:100rpx;"></view><view class="topsearch flex-y-center"><view class="f1 flex-y-center"><image class="img" src="{{pre_url+'/static/img/search_ico.png'}}"></image><input placeholder="输入关键字搜索" placeholder-style="font-size:24rpx;color:#C2C2C2" data-event-opts="{{[['confirm',[['searchConfirm',['$event']]]]]}}" value="{{keyword}}" bindconfirm="__e"/></view></view><view class="order-content"><block wx:for="{{datalist}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><view class="order-box" data-url="{{'refundyajinDetail?id='+item.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="head"><view>{{"退款单号："+item.refund_ordernum}}</view><view class="flex1"></view><block wx:if="{{item.refund_status==0}}"><text class="st3">取消</text></block><block wx:if="{{item.refund_status==1}}"><text class="st0">待审核</text></block><block wx:if="{{item.refund_status==2}}"><text class="st4">通过</text></block><block wx:if="{{item.refund_status==3}}"><text class="st2">驳回</text></block></view><view class="content"><view><image src="{{item.pic}}"></image></view><view class="detail"><text class="t1">{{item.title}}</text><text class="t2">{{item.hotelname}}</text><view class="t3">押金：<text class="x1 flex1">{{"￥"+item.refund_money}}</text></view></view></view><view class="op"><view class="bottom1"><block wx:if="{{item.refund_status==0}}"><text style="color:grey;">{{'申请退押金￥'+item.refund_money}}</text></block><block wx:if="{{item.refund_status==1}}"><text style="color:red;">{{'退押金中￥'+item.refund_money}}</text></block><block wx:if="{{item.refund_status==2}}"><text style="color:red;">{{'已退押金￥'+item.refund_money}}</text></block><block wx:if="{{item.refund_status==-1}}"><text style="color:red;">押金申请已驳回</text></block></view><block wx:if="{{item.refund_status==1}}"><view class="btn2" data-id="{{item.id}}" data-event-opts="{{[['tap',[['refundnopassShow',['$event']]]]]}}" catchtap="__e">驳回</view></block><block wx:if="{{item.refund_status==1}}"><view class="btn2" data-id="{{item.id}}" data-event-opts="{{[['tap',[['refundpass',['$event']]]]]}}" catchtap="__e">通过</view></block></view><view class="bottom flex-y-center"><image style="width:40rpx;height:40rpx;border-radius:50%;margin-right:10rpx;" src="{{item.member.headimg}}"></image><text style="font-weight:bold;color:#333;margin-right:8rpx;">{{item.member.nickname}}</text>{{"(ID:"+item.mid+')'}}</view></view></block></block><uni-popup class="vue-ref" vue-id="3e0deae1-2" id="dialogSetremark" type="dialog" data-ref="dialogSetremark" bind:__l="__l" vue-slots="{{['default']}}"><uni-popup-dialog vue-id="{{('3e0deae1-3')+','+('3e0deae1-2')}}" mode="input" title="确定要驳回申请吗？" value="{{remark}}" placeholder="请输入备注" data-event-opts="{{[['^confirm',[['refundnopass']]]]}}" bind:confirm="__e" bind:__l="__l"></uni-popup-dialog></uni-popup></view><block wx:if="{{nomore}}"><nomore vue-id="3e0deae1-4" bind:__l="__l"></nomore></block><block wx:if="{{nodata}}"><nodata vue-id="3e0deae1-5" bind:__l="__l"></nodata></block></block></block><block wx:if="{{loading}}"><loading vue-id="3e0deae1-6" bind:__l="__l"></loading></block><popmsg class="vue-ref" vue-id="3e0deae1-7" data-ref="popmsg" bind:__l="__l"></popmsg></view>