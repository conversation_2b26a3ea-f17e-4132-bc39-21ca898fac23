<view><block wx:if="{{isload}}"><block><form data-event-opts="{{[['submit',[['subform',['$event']]]]]}}" bindsubmit="__e"><view class="form-box"><view class="form-item"><view class="f1">队列名称<text style="color:red;">*</text></view><view class="f2"><input type="text" name="name" placeholder="请填写名称" placeholder-style="color:#888" value="{{info.name}}"/></view></view><view class="form-item"><view class="f1">前缀</view><view class="f2"><input type="text" name="code" placeholder="前缀,如A 大桌" placeholder-style="color:#888" value="{{info.code}}"/></view></view><view class="form-item"><view class="f1">最少座位数</view><view class="f2"><input type="text" name="seat_min" placeholder placeholder-style="color:#888" value="{{info.seat_min}}"/></view></view><view class="form-item"><view class="f1">最大座位数</view><view class="f2"><input type="text" name="seat_max" placeholder placeholder-style="color:#888" value="{{info.seat_max}}"/></view></view><view class="form-item"><view class="f1">等待时间</view><view class="f2"><input type="text" name="per_minute" placeholder="每个号等待时间(分)" placeholder-style="color:#888" value="{{info.per_minute}}"/></view></view><view class="form-item"><view class="f1">叫号语音文字</view><view class="f2"><input type="text" name="call_text" placeholder="排队号用'[排队号]'代替" placeholder-style="color:#888" value="{{info.call_text}}"/></view></view><view class="form-item"><view class="f1">排序</view><view class="f2"><input type="text" name="sort" placeholder="用于排序,越大越靠前" placeholder-style="color:#888" value="{{info.sort}}"/></view></view><view class="form-item"><view>状态<text style="color:red;">*</text></view><view><radio-group class="radio-group" name="status" data-event-opts="{{[['change',[['',['$event']]]]]}}" bindchange="__e"><label><radio value="1" checked="{{info.status==1?true:false}}"></radio>显示</label><label><radio value="0" checked="{{!info||info.status==0?true:false}}"></radio>隐藏</label></radio-group></view></view></view><button class="savebtn" style="{{('background:linear-gradient(90deg,'+$root.m0+' 0%,rgba('+$root.m1+',0.8) 100%)')}}" form-type="submit">提交</button><block wx:if="{{info.id}}"><button data-event-opts="{{[['tap',[['todel',['$event']]]]]}}" class="button text-btn" bindtap="__e">删除</button></block><view style="height:50rpx;"></view></form></block></block><block wx:if="{{loading}}"><loading vue-id="2be2d380-1" bind:__l="__l"></loading></block></view>