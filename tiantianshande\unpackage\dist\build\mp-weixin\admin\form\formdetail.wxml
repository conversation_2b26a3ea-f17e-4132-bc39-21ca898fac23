<view class="container"><block wx:if="{{isload}}"><block><view class="orderinfo"><view class="item"><text class="t1">提交人</text><text class="flex1"></text><image style="width:80rpx;height:80rpx;margin-right:8rpx;" src="{{detail.headimg}}"></image><text style="height:80rpx;line-height:80rpx;">{{detail.nickname}}</text></view><view class="item"><text class="t1">{{$root.m0+"ID"}}</text><text class="t2">{{detail.mid}}</text></view><view class="item"><text class="t1">标题</text><text class="t2">{{detail.title}}</text></view><block wx:for="{{formcontent}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="item"><text class="t1">{{item.val1}}</text><block wx:if="{{item.key!='upload'}}"><text class="t2">{{detail['form'+index]}}</text></block><block wx:else><view class="t2"><image style="width:50px;" src="{{detail['form'+index]}}" mode="widthFix" data-url="{{detail['form'+index]}}" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image></view></block></view></block><view class="item"><text class="t1">提交时间</text><text class="t2">{{detail.createtime}}</text></view><view class="item"><text class="t1">审核状态</text><block wx:if="{{detail.status==0&&(!detail.payorderid||detail.paystatus==1)}}"><text class="t2" style="color:#88e;">待确认</text></block><block wx:if="{{detail.status==0&&detail.payorderid&&detail.paystatus==0}}"><text class="t2" style="color:red;">待支付</text></block><block wx:if="{{detail.status==1}}"><text class="t2" style="color:green;">已确认</text></block><block wx:if="{{detail.status==2}}"><text class="t2" style="color:red;">已驳回</text></block></view><block wx:if="{{detail.status==2}}"><view class="item"><text class="t1">驳回原因</text><text class="t2" style="color:red;">{{detail.reason}}</text></view></block><block wx:if="{{form.payset==1}}"><block><view class="item"><text class="t1">付款金额</text><text class="t2" style="font-size:32rpx;color:#e94745;">{{"￥"+detail.money}}</text></view><view class="item"><text class="t1">付款方式</text><text class="t2">{{detail.paytype}}</text></view><view class="item"><text class="t1">付款状态</text><block wx:if="{{detail.paystatus==1&&detail.isrefund==0}}"><text class="t2" style="color:green;">已付款</text></block><block wx:if="{{detail.paystatus==1&&detail.isrefund==1}}"><text class="t2" style="color:red;">已退款</text></block><block wx:if="{{detail.paystatus==0}}"><text class="t2" style="color:red;">未付款</text></block></view><block wx:if="{{detail.paystatus>0&&detail.paytime}}"><view class="item"><text class="t1">付款时间</text><text class="t2">{{detail.paytime}}</text></view></block></block></block></view><view style="width:100%;height:160rpx;"></view><view class="bottom notabbarbot"><block wx:if="{{detail.status==0}}"><view class="btn2" data-st="{{1}}" data-id="{{detail.id}}" data-event-opts="{{[['tap',[['setst',['$event']]]]]}}" bindtap="__e">确认</view></block><block wx:if="{{detail.status==0}}"><view class="btn2" data-st="{{2}}" data-id="{{detail.id}}" data-event-opts="{{[['tap',[['setst2',['$event']]]]]}}" bindtap="__e">驳回</view></block><view class="btn2" data-id="{{detail.id}}" data-event-opts="{{[['tap',[['del',['$event']]]]]}}" bindtap="__e">删除</view></view><uni-popup class="vue-ref" vue-id="c3f25f38-1" id="dialogSetst2" type="dialog" data-ref="dialogSetst2" bind:__l="__l" vue-slots="{{['default']}}"><uni-popup-dialog vue-id="{{('c3f25f38-2')+','+('c3f25f38-1')}}" mode="input" title="驳回原因" value="{{detail.reason}}" placeholder="请输入驳回原因" data-event-opts="{{[['^confirm',[['setst2confirm']]]]}}" bind:confirm="__e" bind:__l="__l"></uni-popup-dialog></uni-popup></block></block><block wx:if="{{loading}}"><loading vue-id="c3f25f38-3" bind:__l="__l"></loading></block><dp-tabbar vue-id="c3f25f38-4" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="c3f25f38-5" data-ref="popmsg" bind:__l="__l"></popmsg></view>