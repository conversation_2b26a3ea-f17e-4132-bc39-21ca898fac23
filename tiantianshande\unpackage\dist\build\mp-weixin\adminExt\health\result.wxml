<view><block wx:if="{{isload}}"><block><view class="container"><view class="enter"><view class="gobtn" style="{{'background:'+(themeColor)+';'+('color:'+('#FFF')+';')}}" data-url="{{'recordlog?rid='+detail.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">评测详情</view></view><view class="box"><view class="row"><view class="lable">评测量表：</view><view class="value">{{detail.ha_name}}</view></view><view class="row"><view class="lable">评测时间：</view><view class="value">{{detail.createtime}}</view></view><view class="row"><view class="lable">联系方式：</view><view class="value">{{detail.tel}}</view></view><view class="row"><view class="lable">姓名：</view><view class="value">{{detail.name}}</view></view><view class="row"><view class="lable">年龄：</view><view class="value">{{detail.age+"岁"}}</view></view><view class="row"><view class="lable">性别：</view><view class="value">{{detail.sex==2?'女':'男'}}</view></view><block wx:if="{{detail.bname}}"><view class="row"><view class="lable">门店：</view><view class="value">{{detail.bname}}</view></view></block><view class="row"><view class="lable">家庭地址：</view><view class="value">{{detail.address}}</view></view></view><view class="box"><block wx:if="{{health.type!=3}}"><block><view class="tag" style="{{'color:'+(themeColor)+';'}}">{{detail.score_tag}}</view><view class="score" style="{{'color:'+(themeColor)+';'}}">{{detail.score+"分"}}</view></block></block><block wx:if="{{$root.g0>0}}"><view class="child-result"><block wx:for="{{detail.child_result}}" wx:for-item="itemC" wx:for-index="indexC"><view class="child-item" style="{{('border:1rpx solid '+themeColor+'')}}"><view class="child-title">{{itemC.name}}</view><view class="child-score txt1"><view class="score" style="{{'color:'+(themeColor)+';'}}">{{itemC.score+"分"}}</view><view>{{itemC.score_tag}}</view></view><view class="txt1"><rich-text nodes="{{itemC.score_desc}}"></rich-text></view></view></block></view></block><block wx:if="{{health.type!=3}}"><view class="desc"><view class="title">评测概述</view><view class="content"><rich-text nodes="{{detail.score_desc}}"></rich-text></view></view></block><view class="desc"><view class="title">评测说明</view><view class="content"><rich-text nodes="{{detail.desc}}"></rich-text></view></view></view><view class="box"><dp vue-id="fe967136-1" pagecontent="{{pagecontent}}" menuindex="{{menuindex}}" data-event-opts="{{[['^getdata',[['getdata']]]]}}" bind:getdata="__e" bind:__l="__l"></dp></view></view><view style="height:90rpx;"></view><view class="bottom"><button data-event-opts="{{[['tap',[['goback',['$event']]]]]}}" class="btn" style="{{'background:'+(themeColor)+';'+('color:'+('#FFF')+';')}}" bindtap="__e">返 回</button></view></block></block><block wx:if="{{loading}}"><loading vue-id="fe967136-2" bind:__l="__l"></loading></block><popmsg class="vue-ref" vue-id="fe967136-3" data-ref="popmsg" bind:__l="__l"></popmsg></view>