<view><block wx:if="{{isload}}"><block><view class="swiper-container"><swiper class="swiper" indicator-dots="{{false}}" autoplay="{{true}}" interval="{{500000}}" data-event-opts="{{[['change',[['swiperChange',['$event']]]]]}}" bindchange="__e"><block wx:for="{{product.pics}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><swiper-item class="swiper-item"><view class="swiper-item-view"><image class="img" src="{{item}}" mode="widthFix"></image></view></swiper-item></block></block></swiper><view class="imageCount">{{current+1+"/"+$root.g0}}</view></view><view class="header"><view class="price_share"><view class="title">{{product.name}}</view></view><view class="pricebox flex"><view class="price"><block wx:if="{{product.price>0}}"><view class="f1" style="{{'color:'+($root.m0)+';'}}">￥<text style="font-size:36rpx;">{{product.price}}</text></view></block><block wx:else><view class="f1" style="{{'color:'+($root.m1)+';'}}"><text style="font-size:36rpx;">免费</text></view></block><block wx:if="{{product.market_price>0}}"><view class="f2">{{"￥"+product.market_price}}</view></block></view><view class="sales_stock"><view class="f1">{{"包含"+product.count+"道题"}}<block wx:if="{{sysset&&sysset.show_join_num==1}}"><block><text style="margin:0 6rpx;">|</text>{{"已有"+product.join_num+"人学习"}}</block></block></view></view></view><block wx:if="{{kechengset.show_lvupsavemoney==1&&product.upgrade_text&&product.price>0}}"><view class="upsavemoney" style="{{'background:'+('linear-gradient(90deg, rgb(255, 180, 153) 0%, #ffcaa8 100%)')+';'+('color:'+('#653a2b')+';')}}"><view class="flex1">{{product.upgrade_text+''}}</view><view style="margin-left:20rpx;font-weight:bold;display:flex;align-items:center;color:#ca4312;" data-url="/pages/my/levelup" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image style="width:30rpx;height:30rpx;" src="/static/img/arrowright2.png"></image></view></view></block></view><view class="detail"><view class="detail_title"><view class="order-tab2"><view class="{{['item '+(curTopIndex==1?'on':'')]}}" data-index="{{1}}" data-event-opts="{{[['tap',[['switchTopTab',['$event']]]]]}}" bindtap="__e">题库介绍<view class="after" style="{{'background:'+($root.m2)+';'}}"></view></view><view class="{{['item '+(curTopIndex==2?'on':'')]}}" data-index="{{2}}" data-event-opts="{{[['tap',[['switchTopTab',['$event']]]]]}}" bindtap="__e">题库目录<view class="after" style="{{'background:'+($root.m3)+';'}}"></view></view></view></view><block wx:if="{{curTopIndex==1}}"><block><dp vue-id="e5eeb484-1" pagecontent="{{pagecontent}}" bind:__l="__l"></dp></block></block><block wx:if="{{curTopIndex==2}}"><block><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="mulubox flex"><view class="right_box flex"><view class="title_box"></view><block wx:if="{{item.g1}}"><view class="tiku_list"><block wx:for="{{item.$orig.tiku_list}}" wx:for-item="tiku" wx:for-index="idx" wx:key="idx"><view class="tiku_item"><view class="tiku_title">{{idx+1+". "+tiku.title}}</view><view class="tiku_info">{{"类型: "+tiku.type+" - 分数: "+tiku.score}}</view></view></block></view></block></view></view></block><block wx:if="{{nomore}}"><nomore vue-id="e5eeb484-2" text="没有更多题库了" bind:__l="__l"></nomore></block><block wx:if="{{nodata}}"><nodata vue-id="e5eeb484-3" text="没有查找到相关题库" bind:__l="__l"></nodata></block></block></block></view><view><block wx:if="{{$root.g2>0}}"><view class="xihuan"><view class="xihuan-line"></view><view class="xihuan-text"><image class="img" src="/static/img/xihuan.png"></image><text class="txt">为您推荐</text></view><view class="xihuan-line"></view></view></block><view class="prolist"><dp-kecheng-item vue-id="e5eeb484-4" data="{{tjdatalist}}" menuindex="{{menuindex}}" data-event-opts="{{[['^addcart',[['addcart']]]]}}" bind:addcart="__e" bind:__l="__l"></dp-kecheng-item></view></view><view style="width:100%;height:140rpx;"></view><block wx:if="{{product.status==1}}"><view class="{{['bottombar','flex-row',menuindex>-1?'tabbarbot':'notabbarbot']}}"><view class="f1"><view class="item" data-url="/pages/index/index" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image class="img" src="/static/img/shou.png"></image><view class="t1">首页</view></view><block wx:if="{{kfurl!='contact::'}}"><view class="item" data-url="{{kfurl}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image class="img" src="/static/img/kefu.png"></image><view class="t1">客服</view></view></block><block wx:else><button class="item" open-type="contact"><image class="img" src="/static/img/kefu.png"></image><view class="t1">客服</view></button></block><view data-event-opts="{{[['tap',[['addfavorite',['$event']]]]]}}" class="item" bindtap="__e"><image class="img" src="/static/img/shoucang.png"></image><view class="t1">{{isfavorite?'已收藏':'收藏'}}</view></view></view><view class="op"><block wx:if="{{product.price==0||product.ispay==1}}"><view class="tobuy flex-x-center flex-y-center" style="{{'background:'+($root.m4)+';'}}" data-url="{{'tiku?id='+product.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">立即答题</view></block><block wx:else><view data-event-opts="{{[['tap',[['tobuy',['$event']]]]]}}" class="tobuy flex-x-center flex-y-center" style="{{'background:'+($root.m5)+';'}}" bindtap="__e">购买题库</view></block></view></view></block><scrolltop vue-id="e5eeb484-5" isshow="{{scrolltopshow}}" bind:__l="__l"></scrolltop><block wx:if="{{sharetypevisible}}"><view class="popup__container"><view data-event-opts="{{[['tap',[['handleClickMask',['$event']]]]]}}" class="popup__overlay" catchtap="__e"></view><view class="popup__modal" style="height:320rpx;min-height:320rpx;"><view class="popup__content"><view class="sharetypecontent"><block wx:if="{{$root.m6=='app'}}"><view data-event-opts="{{[['tap',[['shareapp',['$event']]]]]}}" class="f1" bindtap="__e"><image class="img" src="/static/img/weixin.png"></image><text class="t1">分享给好友</text></view></block><block wx:else><block wx:if="{{$root.m7=='mp'}}"><view data-event-opts="{{[['tap',[['sharemp',['$event']]]]]}}" class="f1" bindtap="__e"><image class="img" src="/static/img/weixin.png"></image><text class="t1">分享给好友</text></view></block><block wx:else><block wx:if="{{$root.m8=='h5'}}"><view data-event-opts="{{[['tap',[['sharemp',['$event']]]]]}}" class="f1" bindtap="__e"><image class="img" src="/static/img/weixin.png"></image><text class="t1">分享给好友</text></view></block><block wx:else><button class="f1" open-type="share"><image class="img" src="/static/img/weixin.png"></image><text class="t1">分享给好友</text></button></block></block></block><view data-event-opts="{{[['tap',[['showPoster',['$event']]]]]}}" class="f2" bindtap="__e"><image class="img" src="/static/img/sharepic.png"></image><text class="t1">生成分享图片</text></view></view></view></view></view></block><block wx:if="{{showposter}}"><view class="posterDialog"><view class="main"><view data-event-opts="{{[['tap',[['posterDialogClose',['$event']]]]]}}" class="close" bindtap="__e"><image class="img" src="/static/img/close.png"></image></view><view class="content"><image class="img" src="{{posterpic}}" mode="widthFix" data-url="{{posterpic}}" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image></view></view></view></block></block></block><block wx:if="{{loading}}"><loading vue-id="e5eeb484-6" bind:__l="__l"></loading></block><dp-tabbar vue-id="e5eeb484-7" opt="{{opt}}" data-event-opts="{{[['^getmenuindex',[['getmenuindex']]]]}}" bind:getmenuindex="__e" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="e5eeb484-8" data-ref="popmsg" bind:__l="__l"></popmsg></view>