<view class="page"><block wx:if="{{isload}}"><block><view class="tab-bar"><view data-event-opts="{{[['tap',[['changeType',[1]]]]]}}" class="{{['tab-item',(type===1)?'active':'']}}" bindtap="__e"><text class="tab-text">我的采购</text></view><view data-event-opts="{{[['tap',[['changeType',[2]]]]]}}" class="{{['tab-item',(type===2)?'active':'']}}" bindtap="__e"><text class="tab-text">我的供应</text></view></view><view class="list-container"><block wx:for="{{datalist}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><view class="list-item" data-url="{{'detail?id='+item.id+'&type='+type}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="item-header"><text class="title">{{item.title}}</text><text class="category">{{item.categoryname}}</text></view><view class="item-content"><text class="content">{{item.content}}</text></view><view class="item-footer"><view class="info"><text class="time">{{item.addtime}}</text><text class="{{['status',item.status===1?'active':'']}}">{{''+(item.status===1?'已完成':'进行中')+''}}</text></view><block wx:if="{{type===2}}"><view class="price"><text class="price-text">{{"￥"+(item.price||'面议')}}</text></view></block></view></view></block></block><block wx:if="{{nodata}}"><view class="empty"><image class="empty-image" src="/static/images/empty.png" mode="aspectFit"></image><text class="empty-text">{{"暂无"+(type===1?'采购':'供应')+"记录"}}</text></view></block></view><block wx:if="{{nomore&&!nodata}}"><view class="load-more"><text class="load-more-text">没有更多了</text></view></block></block></block><block wx:if="{{loading}}"><loading vue-id="095c66ab-1" bind:__l="__l"></loading></block><dp-tabbar vue-id="095c66ab-2" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="095c66ab-3" data-ref="popmsg" bind:__l="__l"></popmsg></view>