<view style="padding-bottom:250px;"><view style="position:relative;height:350px;"><image style="width:100%;height:300px;position:absolute;" src="{{pics[0]}}"></image><block wx:if="{{is_h5}}"><image style="width:20px;height:20px;position:absolute;top:20px;left:20px;" src="{{pre_url+'/static/img/arrow-left.png'}}" data-event-opts="{{[['tap',[['goBack',['$event']]]]]}}" bindtap="__e"></image></block><view style="position:absolute;left:0;bottom:0px;width:100%;"><view style="margin:0 10px;background:#fffffff2;border-radius:5px;padding:10px;"><view style="display:flex;align-items:center;justify-content:space-between;"><view style="display:flex;align-items:center;"><image style="width:40px;height:40px;border-radius:50px;" src="{{tuanzhang.logo}}"></image><text style="font-size:16px;font-weight:bold;margin-left:10px;">{{tuanzhang.name}}</text></view><view><image style="width:30px;height:30px;" src="{{pre_url+'/static/icon/share.png'}}" data-event-opts="{{[['tap',[['shareClick',['$event']]]]]}}" bindtap="__e"></image><image style="width:30px;height:30px;margin-left:10px;" src="{{pre_url+'/static/icon/pic.png'}}" data-event-opts="{{[['tap',[['shareClick',['$event']]]]]}}" bindtap="__e"></image></view></view><view style="padding:5px 10px;color:#aaa;letter-spacing:1.1px;margin-top:5px;">{{tuanzhang.desc}}</view></view></view></view><view style="background:#fff;margin:10px;padding:15px;border-radius:5px;"><view style="font-size:16px;overflow-wrap:break-word;font-weight:bold;padding:10px 0;letter-spacing:1.1px;">{{''+detail.name+''}}</view><view style="background:#f1f1f1;display:flex;padding:5px;border-radius:8px;margin:10px 0;"><view style="padding:5px 5px 0 10px;text-align:center;color:#3AA578;font-family:DingTalk JinBuTi, DingTalk JinBuTi;font-weight:400;width:15%;font-style:normal;">本团商品</view><view style="width:1px;background:#ccc;margin:5px 10px;"></view><scroll-view class="scroll-view" style="width:75%;" scroll-x="{{true}}"><block wx:for="{{products}}" wx:for-item="ite" wx:for-index="ind" wx:key="ind"><view data-event-opts="{{[['tap',[['gotoItem',['$0'],[[['products','',ind,'id']]]]]]]}}" class="scroll-item" bindtap="__e"><image style="width:50px;height:50px;border-radius:5px;margin-right:5px;" src="{{ite.pic}}"></image></view></block></scroll-view></view><view style="color:#999999;font-size:10px;">{{detail.createtime+"发布"}}</view><block wx:if="{{content}}"><view style="height:1px;background:#eee;margin:15px 0;"></view></block><view style="padding:8rpx 0;"><dp vue-id="f90e2a5e-1" pagecontent="{{pagecontentm}}" bind:__l="__l"></dp><block wx:if="{{is_show}}"><view data-event-opts="{{[['tap',[['openAll']]]]}}" style="color:#3AA578;text-align:center;padding-top:10px;" bindtap="__e">查看全部</view></block></view></view><view style="background:#fff;margin:10px;padding:10px;border-radius:5px;"><block wx:for="{{products}}" wx:for-item="ite" wx:for-index="ind" wx:key="ind"><view style="display:flex;border-bottom:1px solid #eee;padding:10px 0;"><image style="width:120px;height:120px;border-radius:8px;margin-right:10px;" src="{{ite.pic}}" data-event-opts="{{[['tap',[['gotoItem',['$0'],[[['products','',ind,'id']]]]]]]}}" bindtap="__e"></image><view style="position:relative;width:65%;margin:5px 0;"><view data-event-opts="{{[['tap',[['gotoItem',['$0'],[[['products','',ind,'id']]]]]]]}}" class="ellipsis-2-lines" style="font-size:16px;font-weight:bold;" bindtap="__e">{{ite.name}}</view><view data-event-opts="{{[['tap',[['gotoItem',['$0'],[[['products','',ind,'id']]]]]]]}}" class="ellipsis-2-lines" style="color:#aaa;" bindtap="__e">{{ite.sellpoint}}</view><view style="display:flex;justify-content:space-between;position:absolute;bottom:0;width:100%;"><view data-event-opts="{{[['tap',[['gotoItem',['$0'],[[['products','',ind,'id']]]]]]]}}" style="color:red;" bindtap="__e"><text style="font-weight:bold;">￥</text><text style="font-size:18px;font-weight:bold;">{{ite.sell_price}}</text></view><view data-event-opts="{{[['tap',[['addBuy',['$0'],[[['products','',ind,'id']]]]]]]}}" style="{{('background:'+$root.m0+';color: #fff;border-radius: 8px;padding: 5px 10px;')}}" bindtap="__e">加入购物车</view></view></view></view></block></view><block wx:if="{{$root.g0>0}}"><view style="background:#fff;margin:10px;padding:10px;border-radius:5px;"><view style="display:flex;justify-content:space-between;padding-bottom:10px;border-bottom:1px solid #eee;"><view style="font-weight:bold;">评论</view><view style="font-size:12px;color:#aaa;">查看全部</view></view><view style="display:flex;margin:10px 0;"><text style="background:#EEF6EF;color:#58A27E;border-radius:5px;padding:3px 8px;font-size:13px;">性价比高(2)</text></view><block wx:for="{{pinglunlist}}" wx:for-item="ite" wx:for-index="ind" wx:key="ind"><view style="border-bottom:1px solid #eee;padding-bottom:10px;"><view style="display:flex;margin-top:10px;"><image style="width:50px;height:50px;border-radius:5px;" src="{{ite.headimg}}"></image><view style="margin:5px;"><view>{{ite.nickname}}</view><view style="color:#aaa;">{{ite.createtime}}</view></view></view><view style="overflow-wrap:break-word;margin:10px 0;font-size:16px;">{{ite.content}}</view><view><block wx:for="{{ite.pics}}" wx:for-item="it" wx:for-index="i" wx:key="i"><image style="width:80px;height:80px;border-radius:5px;margin-right:10px;" src="{{it}}" data-event-opts="{{[['tap',[['previewImage',['$0'],[[['pinglunlist','',ind],['pics','',i]]]]]]]}}" bindtap="__e"></image></block></view></view></block></view></block><block wx:if="{{$root.g1>0}}"><view style="margin:10px;padding:5px;"><view style="display:flex;align-items:center;"><view style="width:3px;background:#58A27E;height:15px;margin-right:10px;"></view><view style="font-size:16px;font-weight:bold;">经销商推荐</view></view><view class="list"><block wx:for="{{bijis}}" wx:for-item="item" wx:for-index="j" wx:key="j"><view class="pbl" data-url="{{item.yindao_link}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="image"><image fade-show="{{true}}" lazy-load="{{true}}" lazy-load-margin="{{0}}" mode="widthFix" src="{{item.coverimg}}"></image></view><view class="title"><rich-text nodes="{{item.content}}"></rich-text></view><view style="display:flex;align-items:center;justify-content:space-between;padding:10px;color:#aaa;"><view style="display:flex;align-items:center;width:60%;"><image style="width:20px;height:20px;border-radius:50px;" src="{{item.headimg}}" class="_img"></image><view style="font-size:10px;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;margin-left:5px;">{{item.nickname}}</view></view><view style="display:flex;align-items:center;"><image style="width:12px;height:12px;" src="{{pre_url+'/static/restaurant/like1.png'}}"></image><view style="font-size:10px;margin-left:5px;">{{item.zan}}</view></view></view></view></block></view></view></block><view style="position:fixed;bottom:0;display:flex;justify-content:space-between;align-items:center;width:100%;background:#fff;padding:10px 15px;"><view style="width:45%;display:flex;"><view style="text-align:center;" data-url="./tuanlist" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image style="width:20px;height:20px;" src="{{pre_url+'/static/icon/home.png'}}"></image><view>首页</view></view><button style="text-align:center;line-height:1.3;" open-type="contact"><image style="width:20px;height:20px;" src="{{pre_url+'/static/img/kefu.png'}}"></image><view class="t1">客服</view></button><view style="text-align:center;position:relative;" data-url="/shopPackage/shop/cart" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view><image style="width:20px;height:20px;" src="{{pre_url+'/static/icon/buy.png'}}"></image><view>购物车</view></view><block wx:if="{{cart_num>0}}"><view style="position:absolute;top:-5px;right:0;background:red;color:#fff;border-radius:50px;padding:0 5px;">{{cart_num}}</view></block></view></view><view style="{{('background:'+$root.m1+';width: 50%;text-align: center;color: #fff;padding: 10px;border-radius: 5px;font-size: 16px;')}}" data-url="/shopPackage/shop/cart" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">跟团购买<text style="font-size:10px;margin-left:4px;">(56人已跟团)</text></view></view><block wx:if="{{$root.g2>0}}"><view style="width:70px;height:90px;background:#fff;border-radius:5px;position:fixed;right:10px;top:70%;padding:3px;"><view style="position:relative;"><swiper style="height:65px;" autoplay="{{true}}" interval="3000"><block wx:for="{{products}}" wx:for-item="ite" wx:for-index="ind"><swiper-item><image style="width:100%;height:100%;border-radius:5px;" src="{{ite.pic}}"></image><view style="position:absolute;right:0;top:0;background:#00000077;padding:2px 5px;color:#fff;font-size:8px;border-radius:0 5px;">{{''+(ind+1)}}</view></swiper-item></block></swiper></view><view style="text-align:center;font-size:10px;margin-top:3px;">购买同款</view></view></block><block wx:if="{{sharetypevisible}}"><view class="popup__container"><view data-event-opts="{{[['tap',[['handleClickMask',['$event']]]]]}}" class="popup__overlay" catchtap="__e"></view><view class="popup__modal" style="height:320rpx;min-height:320rpx;"><view class="popup__content"><view class="sharetypecontent"><block wx:if="{{$root.m2=='app'}}"><view data-event-opts="{{[['tap',[['shareapp',['$event']]]]]}}" class="f1" bindtap="__e"><image class="img" src="/static/img/weixin.png"></image><text class="t1">分享给好友</text></view></block><block wx:else><block wx:if="{{$root.m3=='mp'}}"><view data-event-opts="{{[['tap',[['sharemp',['$event']]]]]}}" class="f1" bindtap="__e"><image class="img" src="/static/img/weixin.png"></image><text class="t1">分享给好友</text></view></block><block wx:else><block wx:if="{{$root.m4=='h5'}}"><view data-event-opts="{{[['tap',[['sharemp',['$event']]]]]}}" class="f1" bindtap="__e"><image class="img" src="/static/img/weixin.png"></image><text class="t1">分享给好友</text></view></block><block wx:else><button class="f1" open-type="share"><image class="img" src="/static/img/weixin.png"></image><text class="t1">分享给好友</text></button></block></block></block><view data-event-opts="{{[['tap',[['showPoster',['$event']]]]]}}" class="f2" bindtap="__e"><image class="img" src="/static/img/sharepic.png"></image><text class="t1">生成分享图片</text></view></view></view></view></view></block><block wx:if="{{buydialogShow}}"><buydialog vue-id="f90e2a5e-2" proid="{{product_id}}" tid="{{tid}}" btntype="{{btntype}}" menuindex="{{menuindex}}" data-event-opts="{{[['^buydialogChange',[['buydialogChange']]],['^showLinkChange',[['showLinkChange']]],['^addcart',[['addcart']]]]}}" bind:buydialogChange="__e" bind:showLinkChange="__e" bind:addcart="__e" bind:__l="__l"></buydialog></block></view>