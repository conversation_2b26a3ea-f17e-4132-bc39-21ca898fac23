<view class="container"><block wx:for="{{datalist}}" wx:for-item="item" wx:for-index="idx" wx:key="idx"><view class="busbox"><view class="businfo"><view class="f1"><image class="image" src="{{item.banner}}"></image><block wx:if="{{item.status==0}}"><view class="icon">报名中</view></block></view><view class="f2"><view class="title">{{item.name}}</view><view class="address">{{item.distance+" "+item.venues_title}}</view></view></view><view class="tab"><view class="imgs"><text>{{item.joinnum+"人已报名"}}</text></view><view data-event-opts="{{[['tap',[['goDeatail',['$0'],[[['datalist','',idx,'id']]]]]]]}}" class="btn" style="{{('background:'+primary_color)}}" bindtap="__e">{{item.helptext}}</view></view></view></block><block wx:if="{{nomore}}"><nomore vue-id="4e2814f4-1" bind:__l="__l"></nomore></block><block wx:if="{{nodata}}"><nodata vue-id="4e2814f4-2" bind:__l="__l"></nodata></block></view>