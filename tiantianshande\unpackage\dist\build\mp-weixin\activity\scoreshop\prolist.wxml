<view class="container"><block wx:if="{{isload}}"><block><view class="search-container" style="{{(history_show?'height:100%;':'')}}"><view class="topsearch flex-y-center"><view class="f1 flex-y-center"><image src="/static/img/search_ico.png"></image><input placeholder="商品搜索..." data-event-opts="{{[['confirm',[['searchConfirm',['$event']]]],['input',[['searchChange',['$event']]]]]}}" value="{{keyword}}" bindconfirm="__e" bindinput="__e"/></view></view><view hidden="{{!(history_show)}}" class="search-history"><view><text class="search-history-title">最近搜索</text><view data-event-opts="{{[['tap',[['deleteSearchHistory',['$event']]]]]}}" class="delete-search-history" bindtap="__e"><text class="fa fa-trash-o" style="font-size:36rpx;"></text></view></view><view class="search-history-list"><block wx:for="{{history_list}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="search-history-item" data-value="{{item}}" data-event-opts="{{[['tap',[['historyClick',['$event']]]]]}}" bindtap="__e">{{item+''}}</view></block><block wx:if="{{$root.g0}}"><view><text class="fa fa-exclamation-circle"></text>暂无记录</view></block></view></view><view hidden="{{!(!history_show)}}" class="search-navbar"><view class="{{['search-navbar-item '+(!field?'active':'')]}}" data-field data-order data-event-opts="{{[['tap',[['sortClick',['$event']]]]]}}" catchtap="__e">综合</view><view class="{{['search-navbar-item '+(field=='sales'?'active':'')]}}" data-field="sales" data-order="desc" data-event-opts="{{[['tap',[['sortClick',['$event']]]]]}}" catchtap="__e">兑换数</view><view class="search-navbar-item" data-field="score_price" data-order="{{order=='asc'?'desc':'asc'}}" data-event-opts="{{[['tap',[['sortClick',['$event']]]]]}}" catchtap="__e"><text class="{{[field=='score_price'?'active':'']}}">{{"所需"+$root.m0}}</text><text class="{{['fa fa-caret-up '+(field=='score_price'&&order=='asc'?'active':'')]}}"></text><text class="{{['fa fa-caret-down '+(field=='score_price'&&order=='desc'?'active':'')]}}"></text></view><view data-event-opts="{{[['tap',[['filterClick',['$event']]]]]}}" class="search-navbar-item flex-x-center flex-y-center" catchtap="__e">筛选<image style="width:40rpx;height:40rpx;" src="/static/img/filter.png"></image></view></view><block wx:if="{{showfilter}}"><view class="search-filter"><view class="search-filter-content" if="{{$root.g1}}"><block wx:for="{{glist}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><view style="display:flex;align-items:center;justify-content:center;padding:15rpx 30rpx;" data-gid="{{item.id}}" data-event-opts="{{[['tap',[['groupClick',['$event']]]]]}}" catchtap="__e"><block wx:if="{{gid==item.id}}"><icon type="success_no_circle" size="18"></icon></block>{{item.name+''}}</view></block></block></view><view class="search-filter-content" style="border-top:1px solid #f5f5f5;" if="{{$root.g2}}"><block wx:for="{{clist}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><view style="display:flex;align-items:center;justify-content:center;padding:15rpx 30rpx;" data-cid="{{item.id}}" data-event-opts="{{[['tap',[['cateClick',['$event']]]]]}}" catchtap="__e"><block wx:if="{{cid==item.id}}"><icon type="success_no_circle" size="18"></icon></block>{{item.name+''}}</view></block></block></view><view class="search-filter-content" style="border-top:1px solid #eee;text-align:right;"><view class="close _div"><text data-event-opts="{{[['tap',[['filterClick',['$event']]]]]}}" catchtap="__e">关闭</text></view></view></view></block></view><view class="product-container"><block wx:if="{{$root.g3}}"><block><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><view class="product-item" data-url="{{'product?id='+item.$orig.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="itemcontent"><view class="product-pic"><image src="{{item.$orig.pic}}"></image></view><view class="product-info"><view class="p1">{{item.$orig.name}}</view><view class="p3"><view class="t1 flex"><view class="x1" style="{{'color:'+(item.m1)+';'}}"><text style="font-size:13px;"><block wx:if="{{item.$orig.score_price>0}}"><block>{{item.$orig.score_price+item.m2}}</block></block><block wx:if="{{item.$orig.money_price>0}}"><block><block wx:if="{{item.$orig.score_price>0}}"><block>+</block></block>{{item.$orig.money_price+"元"}}</block></block></text></view></view></view></view></view><view class="itembottom"><view class="f1">已兑换<text style="{{'color:'+(item.m3)+';'}}">{{''+item.$orig.sales+''}}</text>件</view><button class="f2" style="{{'background:'+(item.m4)+';'}}" data-id="{{item.$orig.id}}" data-event-opts="{{[['tap',[['todetail',['$event']]]]]}}" bindtap="__e">立即兑换</button></view></view></block></block></block></block><block wx:if="{{nomore}}"><nomore vue-id="792995f4-1" text="没有更多商品了" bind:__l="__l"></nomore></block><block wx:if="{{nodata}}"><nodata vue-id="792995f4-2" text="没有查找到相关商品" bind:__l="__l"></nodata></block><block wx:if="{{loading}}"><loading vue-id="792995f4-3" bind:__l="__l"></loading></block></view></block></block><block wx:if="{{loading}}"><loading vue-id="792995f4-4" bind:__l="__l"></loading></block><dp-tabbar vue-id="792995f4-5" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="792995f4-6" data-ref="popmsg" bind:__l="__l"></popmsg></view>