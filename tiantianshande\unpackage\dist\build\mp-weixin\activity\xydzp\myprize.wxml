<view class="container" style="background-color:#fce243;min-height:100vh;"><block wx:if="{{isload}}"><block><view class="banner"><image src="{{info.banner}}" mode="widthFix"></image></view><view class="activity"><view class="activity-amin"><view class="h2">我的奖品</view><view class="tb0"><view class="tr"><view class="td">中奖时间</view><view class="td">中奖奖品</view><view class="td">领奖状态</view><view class="td">操作</view></view><block wx:for="{{datalist}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="tr"><view class="td td2">{{item.createtime}}</view><view class="td td2">{{item.jxmc}}</view><view class="td td2"><block wx:if="{{item.status==0}}"><block>未领奖</block></block><block wx:else><block>已领奖</block></block></view><view class="td td2"><block wx:if="{{item.status==0}}"><text style="background-color:#fb5a43;padding:4rpx 8rpx;" data-k="{{index}}" data-id="{{item.id}}" data-event-opts="{{[['tap',[['duijiang',['$event']]]]]}}" bindtap="__e">兑奖</text></block><block wx:if="{{item.jxtp==3&&item.status==1}}"><text style="background-color:#fb6a43;padding:4rpx 8rpx;" data-url="/pages/coupon/mycoupon" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">查看</text></block><block wx:if="{{item.express_status==1}}"><text style="background-color:#1890ff;padding:4rpx 8rpx;" data-id="{{item.id}}" data-event-opts="{{[['tap',[['viewExpress',['$event']]]]]}}" bindtap="__e">查物流</text></block></view></view></block></view><block wx:if="{{!datalist}}"><view style="width:100%;padding:40rpx 0;text-align:center;color:#f19132;">暂无中奖记录~</view></block><view data-event-opts="{{[['tap',[['goback',['$event']]]]]}}" class="goback" bindtap="__e">返回</view></view></view><block wx:if="{{maskshow&&formdata}}"><view id="mask-rule2"><view class="box-rule" style="height:900rpx;"><view class="h2">兑奖信息</view><view style="{{('background: no-repeat center / contain;background-image: url('+pre_url+'/static/img/dzp/close.png);')}}" id="close-rule2" data-event-opts="{{[['tap',[['changemaskshow',['$event']]]]]}}" bindtap="__e"></view><view class="con"><view class="text" style="text-align:center;"><view style="text-align:left;margin-left:10%;" id="linkinfo"><block wx:for="{{formdata}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view>{{index+":"+item}}</view></block></view><image style="width:80%;" src="{{record.hexiaoqr}}" id="hexiaoqr" mode="widthFix"></image><view>请出示兑奖码给核销员进行兑奖</view></view></view></view></view></block><block wx:if="{{maskshow&&!formdata}}"><view id="mask-rule1"><view class="box-rule" style="height:640rpx;"><view class="h2">请填写兑奖信息</view><view style="{{('background: no-repeat center / contain;background-image: url('+pre_url+'/static/img/dzp/close.png);')}}" id="close-rule1" data-event-opts="{{[['tap',[['changemaskshow',['$event']]]]]}}" bindtap="__e"></view><view class="con"><form data-event-opts="{{[['submit',[['formsub',['$event']]]]]}}" bindsubmit="__e"><view class="pay-form" style="margin-top:0.18rem;"><block wx:for="{{info.formcontent}}" wx:for-item="item" wx:for-index="idx" wx:key="idx"><view class="item flex-y-center"><view class="f1">{{item.val1+"："}}</view><view class="f2 flex flex1"><block wx:if="{{item.key=='input'}}"><block><input class="input" type="text" name="{{'form'+idx}}" placeholder="{{item.val2}}"/></block></block><block wx:if="{{item.key=='textarea'}}"><block><textarea class="textarea" name="{{'form'+idx}}" placeholder="{{item.val2}}"></textarea></block></block><block wx:if="{{item.key=='radio'}}"><block><radio-group class="radio-group" name="{{'form'+idx}}"><block wx:for="{{item.val2}}" wx:for-item="item1" wx:for-index="index" wx:key="index"><label><radio value="{{item1}}"></radio>{{item1+''}}</label></block></radio-group></block></block><block wx:if="{{item.key=='checkbox'}}"><block><checkbox-group name="{{'form'+idx}}"><block wx:for="{{item.val2}}" wx:for-item="item1" wx:for-index="index" wx:key="index"><label><checkbox class="xyy-zu" value="{{item1}}"></checkbox>{{item1+''}}</label></block></checkbox-group></block></block><block wx:if="{{item.key=='switch'}}"><block><switch class="xyy-zu" value="1" name="{{'form'+idx}}"></switch></block></block><block wx:if="{{item.key=='selector'}}"><block><picker class="xyy-pic" mode="selector" name="{{'form'+idx}}" value="{{true}}" range="{{item.val2}}" data-idx="{{idx}}" data-tplindex="0" data-event-opts="{{[['change',[['_editorBindPickerChange',['$event']]]]]}}" bindchange="__e"><block wx:if="{{item.val2[_editorFormdata[0][idx]]}}"><view class="picker">{{''+item.val2[_editorFormdata[0][idx]]}}</view></block><block wx:else><view>请选择</view></block></picker></block></block><block wx:if="{{item.key=='time'}}"><block><picker class="xyy-pic" mode="time" name="{{'form'+idx}}" value="{{true}}" start="{{item.val2[0]}}" end="{{item.val2[1]}}" range="{{item.val2}}" data-idx="{{idx}}" data-tplindex="0" data-event-opts="{{[['change',[['_editorBindPickerChange',['$event']]]]]}}" bindchange="__e"><block wx:if="{{_editorFormdata[0][idx]}}"><view class="picker">{{_editorFormdata[0][idx]}}</view></block><block wx:else><view>请选择</view></block></picker></block></block><block wx:if="{{item.key=='date'}}"><block><picker class="xyy-pic" mode="date" name="{{'form'+idx}}" value="{{true}}" start="{{item.val2[0]}}" end="{{item.val2[1]}}" range="{{item.val2}}" data-idx="{{idx}}" data-tplindex="0" data-event-opts="{{[['change',[['_editorBindPickerChange',['$event']]]]]}}" bindchange="__e"><block wx:if="{{_editorFormdata[0][idx]}}"><view class="picker">{{''+_editorFormdata[0][idx]}}</view></block><block wx:else><view>请选择</view></block></picker></block></block><block wx:if="{{item.key=='region'}}"><block><picker class="xyy-pic" mode="region" name="{{'form'+idx}}" value="{{true}}" data-idx="{{idx}}" data-tplindex="0" data-event-opts="{{[['change',[['_editorBindPickerChange',['$event']]]]]}}" bindchange="__e"><block wx:if="{{_editorFormdata[0][idx]}}"><view class="picker">{{''+_editorFormdata[0][idx]}}</view></block><block wx:else><view>请选择省市区</view></block></picker></block></block></view></view></block><view style="padding:0 40px 0 80px;"><button class="subbtn" form-type="submit">确 定</button></view></view></form></view></view></view></block></block></block><block wx:if="{{loading}}"><loading vue-id="1543ea30-1" bind:__l="__l"></loading></block><dp-tabbar vue-id="1543ea30-2" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="1543ea30-3" data-ref="popmsg" bind:__l="__l"></popmsg></view>