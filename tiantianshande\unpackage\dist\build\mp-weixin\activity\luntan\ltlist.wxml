<view><block wx:if="{{isload}}"><block><view class="topsearch flex-y-center"><view class="f1 flex-y-center"><image class="img" src="{{pre_url+'/static/img/search_ico.png'}}"></image><input placeholder="搜索感兴趣的帖子" placeholder-style="font-size:24rpx;color:#C2C2C2" data-event-opts="{{[['confirm',[['searchConfirm',['$event']]]]]}}" value="{{keyword}}" bindconfirm="__e"/></view></view><view class="container2"><block wx:if="{{banner}}"><image style="width:100%;height:auto;" src="{{banner}}" mode="widthFix"></image></block><view class="datalist"><block wx:for="{{$root.l1}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><view class="item"><view class="top"><image class="f1" src="{{item.$orig.headimg}}"></image><view class="f2"><view class="t1">{{item.$orig.nickname}}</view><view class="t2">{{item.$orig.showtime}}</view></view></view><view class="title-section" data-url="{{'detail?id='+item.$orig.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><text class="post-title">{{item.$orig.title||'无主题'}}</text></view><view class="con" data-url="{{'detail?id='+item.$orig.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="f1"><text class="{{['content-text',item.$orig.expanded?'expanded':'collapsed']}}">{{item.$orig.expanded?item.$orig.content:item.$orig.summary||item.$orig.content}}</text><block wx:if="{{item.g0}}"><text data-event-opts="{{[['tap',[['toggleExpand',[index]]]]]}}" class="expand-btn" catchtap="__e">展开</text></block><block wx:if="{{item.$orig.expanded}}"><text data-event-opts="{{[['tap',[['toggleExpand',[index]]]]]}}" class="expand-btn" catchtap="__e">收起</text></block></view><block wx:if="{{item.$orig.pics&&!item.$orig.expanded}}"><view class="f2"><block wx:for="{{item.l0}}" wx:for-item="pic" wx:for-index="idx" wx:key="idx"><image style="height:auto;" src="{{pic}}" mode="widthFix"></image></block><block wx:if="{{item.g1>3}}"><text class="more-pics">{{"+"+(item.g2-3)}}</text></block></view></block><block wx:if="{{item.$orig.pics&&item.$orig.expanded}}"><view class="f2"><block wx:for="{{item.$orig.pics}}" wx:for-item="pic" wx:for-index="idx" wx:key="idx"><image style="height:auto;" src="{{pic}}" mode="widthFix"></image></block></view></block><block wx:if="{{item.$orig.video&&item.$orig.expanded}}"><video class="video" id="video" src="{{item.$orig.video}}" data-event-opts="{{[['tap',[['playvideo',['$event']]]]]}}" catchtap="__e"></video></block><block wx:if="{{item.$orig.video&&!item.$orig.expanded}}"><view data-event-opts="{{[['tap',[['toggleExpand',[index]]]]]}}" class="video-placeholder" catchtap="__e"><image class="play-icon" src="{{pre_url+'/static/img/video_play.png'}}"></image><text>点击展开查看视频</text></view></block></view><block wx:if="{{item.g3}}"><view class="comment-preview"><block wx:for="{{item.$orig.preview_comments}}" wx:for-item="comment" wx:for-index="cidx" wx:key="cidx"><view class="comment-item"><image class="comment-avatar" src="{{comment.headimg}}"></image><text class="comment-nickname">{{comment.nickname+":"}}</text><text class="comment-content">{{comment.content}}</text></view></block><block wx:if="{{item.$orig.plcount>3}}"><text class="more-comments" data-url="{{'detail?id='+item.$orig.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">{{"查看全部"+item.$orig.plcount+"条评论"}}</text></block></view></block><view class="bot"><view class="f1"><image style="margin-top:0;" src="{{pre_url+'/static/img/lt_eye.png'}}"></image>{{item.$orig.readcount}}</view><view class="f1" style="margin-left:60rpx;" data-id="{{item.$orig.id}}" data-index="{{index}}" data-event-opts="{{[['tap',[['quickComment',['$event']]]]]}}" bindtap="__e"><image src="{{pre_url+'/static/img/lt_pinglun.png'}}"></image>{{item.$orig.plcount}}</view><view class="f2"></view><block wx:if="{{sysset.cansave}}"><view class="f4" data-id="{{item.$orig.id}}" data-index="{{index}}" data-event-opts="{{[['tap',[['savecontent',['$event']]]]]}}" catchtap="__e"><image src="{{pre_url+'/static/img/lt_save.png'}}"></image>保存</view></block><view class="f3" data-id="{{item.$orig.id}}" data-index="{{index}}" data-event-opts="{{[['tap',[['zan',['$event']]]]]}}" catchtap="__e"><image src="{{pre_url+'/static/img/lt_like'+(item.$orig['iszan']==0?'':'2')+'.png'}}"></image>{{item.$orig.zan}}</view></view></view></block></block><block wx:if="{{nomore}}"><nomore vue-id="38e30c3f-1" bind:__l="__l"></nomore></block><block wx:if="{{nodata}}"><nodata vue-id="38e30c3f-2" bind:__l="__l"></nodata></block></view></view><view class="{{['covermy',menuindex>-1?'tabbarbot':'notabbarbot']}}" data-url="{{'fatie?cid='+cid}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image src="{{pre_url+'/static/img/lt_fatie.png'}}"></image></view><block wx:if="{{showCommentModal}}"><view data-event-opts="{{[['tap',[['hideCommentModal',['$event']]]]]}}" class="comment-modal" bindtap="__e"><view data-event-opts="{{[['tap',[['',['$event']]]]]}}" class="comment-modal-content" catchtap="__e"><view class="comment-modal-header"><text>快速评论</text><text data-event-opts="{{[['tap',[['hideCommentModal',['$event']]]]]}}" class="close-btn" bindtap="__e">×</text></view><view class="comment-modal-body"><textarea class="comment-input" placeholder="说点什么..." maxlength="200" auto-focus="{{true}}" data-event-opts="{{[['input',[['__set_model',['','commentContent','$event',[]]]]]]}}" value="{{commentContent}}" bindinput="__e"></textarea><view class="comment-actions"><text class="char-count">{{$root.g4+"/200"}}</text><button class="send-btn" disabled="{{!$root.g5}}" data-event-opts="{{[['tap',[['sendComment',['$event']]]]]}}" bindtap="__e">发送</button></view></view></view></view></block></block></block><block wx:if="{{loading}}"><loading vue-id="38e30c3f-3" bind:__l="__l"></loading></block><dp-tabbar vue-id="38e30c3f-4" opt="{{opt}}" data-event-opts="{{[['^getmenuindex',[['getmenuindex']]]]}}" bind:getmenuindex="__e" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="38e30c3f-5" data-ref="popmsg" bind:__l="__l"></popmsg></view>