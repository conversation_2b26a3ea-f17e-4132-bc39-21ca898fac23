<view class="container"><block wx:if="{{isload}}"><block><view class="search-container"><view class="search-box"><image class="search-icon" src="/static/img/search_ico.png"></image><input placeholder="搜索" placeholder-style="font-size:30rpx;color:#999" data-event-opts="{{[['confirm',[['searchConfirm',['$event']]]],['input',[['searchChange',['$event']]]]]}}" value="{{keyword}}" bindconfirm="__e" bindinput="__e"/></view></view><view class="stats-section" style="{{'background:'+('linear-gradient(135deg, #4CAF50 0%, #66BB6A 100%)')+';'}}"><view class="filter-buttons"><view data-event-opts="{{[['tap',[['resetAllFilters',['$event']]]]]}}" class="{{['filter-btn',(activeFilter==='all')?'active':'']}}" bindtap="__e">全部</view><view data-event-opts="{{[['tap',[['toggleDateFilter',['$event']]]]]}}" class="{{['filter-btn',(activeFilter==='date')?'active':'']}}" bindtap="__e"><text>日期筛选</text><text class="{{['arrow',(showDateFilter)?'up':'']}}">▼</text></view></view><view class="stats-data"><view class="stat-item"><text class="stat-value">{{"¥"+(statistics.total_amount||'0.00')}}</text><text class="stat-label">总消费金额</text></view><view class="stat-item"><text class="stat-value">{{statistics.total_count||0}}</text><text class="stat-label">当前客户数</text></view><view class="stat-item"><text class="stat-value">{{statistics.team_total_count||0}}</text><text class="stat-label">团队总人数</text></view></view></view><block wx:if="{{userlevel&&userlevel.can_agent==2}}"><dd-tab vue-id="19d47459-1" itemdata="{{[$root.m0,$root.m1]}}" itemst="{{['1','2','3']}}" st="{{st}}" isfixed="{{false}}" data-event-opts="{{[['^changetab',[['changetab']]]]}}" bind:changetab="__e" bind:__l="__l"></dd-tab></block><block wx:if="{{userlevel&&userlevel.can_agent==3}}"><dd-tab vue-id="19d47459-2" itemdata="{{[$root.m2,$root.m3,$root.m4]}}" itemst="{{['1','2','3']}}" st="{{st}}" isfixed="{{false}}" data-event-opts="{{[['^changetab',[['changetab']]]]}}" bind:changetab="__e" bind:__l="__l"></dd-tab></block><block wx:if="{{showDateFilter}}"><view class="date-filter-section"><view class="filter-row"><text class="filter-label">开始：</text><picker mode="date" value="{{start_time}}" data-event-opts="{{[['change',[['onStartDateChange',['$event']]]]]}}" bindchange="__e"><view class="date-picker">{{start_time||'请选择'}}</view></picker></view><view class="filter-row"><text class="filter-label">结束：</text><picker mode="date" value="{{end_time}}" data-event-opts="{{[['change',[['onEndDateChange',['$event']]]]]}}" bindchange="__e"><view class="date-picker">{{end_time||'请选择'}}</view></picker></view><view class="button-row"><view data-event-opts="{{[['tap',[['applyTimeFilter',['$event']]]]]}}" class="filter-button" bindtap="__e">确定</view><view data-event-opts="{{[['tap',[['resetTimeFilter',['$event']]]]]}}" class="filter-button reset" bindtap="__e">重置</view></view></view></block><block wx:if="{{$root.g0}}"><view class="user-list"><block wx:for="{{datalist}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><view class="user-item"><view class="user-avatar"><image class="avatar-img" src="{{item.headimg}}"></image></view><view class="user-info"><view class="user-name-line"><text class="user-name">{{item.nickname||'阿白'}}</text><text class="user-level">{{item.levelname||'会员'}}</text></view><view class="user-time">{{item.createtime||'2025-06-27 22:23:36'}}</view><view class="user-stats"><text class="stat-text">{{"订单："+(item.ordercount||'0')}}</text></view><view class="user-stats"><text class="stat-text">{{"消费金额："+(item.consume_amount||'0.00')+"CNY"}}</text></view><view class="user-stats"><text class="stat-text">{{"销售金额："+(item.tuanduiyeji||'0.00')+"CNY"}}</text></view></view><view class="user-right"><view class="direct-count"><text class="count-label">直推数：</text><text class="count-value">{{item.direct_count||item.downcount||'0'}}</text></view></view></view></block></block></view></block><block wx:if="{{$root.g1}}"><view class="content" style="display:none;"><view class="label"><text class="t1">成员信息</text><block wx:if="{{team_settings&&team_settings.show_other_commission==1}}"><text class="t2">{{"来自TA的"+$root.m5}}</text></block></view><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><view class="item"><view class="f1"><image src="{{item.$orig.headimg}}"></image><view class="t2"><text class="x1">{{item.$orig.nickname}}</text><text class="x2">{{item.$orig.createtime}}</text><text class="x2">{{"等级："+item.$orig.levelname}}</text><block wx:if="{{item.$orig.tel}}"><text class="x2">{{"手机号："+item.$orig.tel}}</text></block><block wx:if="{{item.$orig.has_original_recommender==1&&item.$orig.original_recommender}}"><text class="x2">{{"原推荐人："+item.$orig.original_recommender.nickname}}</text></block><block wx:if="{{team_settings&&team_settings.show_member_count==1}}"><text class="x2">{{"下级人数："+item.$orig.downcount}}</text></block><block wx:if="{{team_settings&&team_settings.show_direct_count==1}}"><text class="x2">{{"直推人数："+(item.$orig.direct_count||item.$orig.downcount)}}</text></block><block wx:if="{{team_settings&&team_settings.show_team_performance==1}}"><text class="x2" style="{{'color:'+(item.m6)+';'}}">{{"团队业绩："+item.$orig.tuanduiyeji}}</text></block><block wx:if="{{team_settings&&team_settings.show_consume_amount==1}}"><text class="x2" style="{{'color:'+(item.m7)+';'}}">{{"消费金额：¥"+(item.$orig.consume_amount||'0.00')}}</text></block></view></view><block wx:if="{{team_settings&&team_settings.show_relation_chart==1||userlevel&&(userlevel.team_givemoney==1||userlevel.team_givescore==1||userlevel.team_levelup==1||userlevel.team_daikexiadan==1)}}"><view class="operation-area"><block wx:if="{{team_settings&&team_settings.show_relation_chart==1}}"><view class="op-btn" style="{{'background:'+('linear-gradient(90deg,'+item.m8+' 0%,rgba('+item.m9+',0.8) 100%)')+';'+('color:'+('#fff')+';')}}" data-id="{{item.$orig.id}}" data-event-opts="{{[['tap',[['goToRelationChart',['$event']]]]]}}" bindtap="__e">关系图</view></block></view></block><block wx:if="{{team_settings&&team_settings.show_other_commission==1}}"><view class="f2"><text class="t1" style="{{'color:'+(item.m10)+';'}}">{{"+"+item.$orig.commission}}</text><view class="t3"><block wx:if="{{userlevel&&userlevel.team_givemoney==1}}"><view class="x1" style="{{'border-color:'+(item.m11)+';'+('color:'+(item.m12)+';')}}" data-id="{{item.$orig.id}}" data-event-opts="{{[['tap',[['givemoneyshow',['$event']]]]]}}" bindtap="__e">{{"转"+item.m13}}</view></block><block wx:if="{{userlevel&&userlevel.team_givescore==1}}"><view class="x1" style="{{'border-color:'+(item.m14)+';'+('color:'+(item.m15)+';')}}" data-id="{{item.$orig.id}}" data-event-opts="{{[['tap',[['givescoreshow',['$event']]]]]}}" bindtap="__e">{{"转"+item.m16}}</view></block><block wx:if="{{userlevel&&userlevel.team_levelup==1}}"><view class="x1" style="{{'border-color:'+(item.m17)+';'+('color:'+(item.m18)+';')}}" data-id="{{item.$orig.id}}" data-levelid="{{item.$orig.levelid}}" data-levelsort="{{item.$orig.levelsort}}" data-event-opts="{{[['tap',[['showDialog',['$event']]]]]}}" bindtap="__e">升级</view></block><block wx:if="{{userlevel&&userlevel.team_daikexiadan==1}}"><view class="x1" style="{{'border-color:'+(item.m19)+';'+('color:'+(item.m20)+';')}}" data-id="{{item.$orig.id}}" data-event-opts="{{[['tap',[['goDaigoupick',['$event']]]]]}}" bindtap="__e">帮他下单</view></block></view></view></block></view></block></block></view></block><uni-popup class="vue-ref" vue-id="19d47459-3" id="dialogmoneyInput" type="dialog" data-ref="dialogmoneyInput" bind:__l="__l" vue-slots="{{['default']}}"><uni-popup-dialog vue-id="{{('19d47459-4')+','+('19d47459-3')}}" mode="input" title="转账金额" value="" placeholder="请输入转账金额" data-event-opts="{{[['^confirm',[['givemoney']]]]}}" bind:confirm="__e" bind:__l="__l"></uni-popup-dialog></uni-popup><uni-popup class="vue-ref" vue-id="19d47459-5" id="dialogscoreInput" type="dialog" data-ref="dialogscoreInput" bind:__l="__l" vue-slots="{{['default']}}"><uni-popup-dialog vue-id="{{('19d47459-6')+','+('19d47459-5')}}" mode="input" title="转账数量" value="" placeholder="请输入转账数量" data-event-opts="{{[['^confirm',[['givescore']]]]}}" bind:confirm="__e" bind:__l="__l"></uni-popup-dialog></uni-popup><block wx:if="{{dialogShow}}"><view class="popup__container"><view data-event-opts="{{[['tap',[['showDialog',['$event']]]]]}}" class="popup__overlay" catchtap="__e"></view><view class="popup__modal"><view class="popup__title"><text class="popup__title-text">升级</text><image class="popup__close" style="width:36rpx;height:36rpx;" src="/static/img/close.png" data-event-opts="{{[['tap',[['showDialog',['$event']]]]]}}" catchtap="__e"></image></view><view class="popup__content"><block wx:for="{{$root.l1}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="sheet-item"><text class="item-text flex-item">{{item.$orig.name}}</text><view class="flex1"></view><block wx:if="{{item.$orig.id!=tempLevelid&&item.$orig.sort>tempLevelsort}}"><view style="{{'color:'+(item.m21)+';'}}" data-id="{{item.$orig.id}}" data-name="{{item.$orig.name}}" data-event-opts="{{[['tap',[['changeLevel',['$event']]]]]}}" bindtap="__e">选择</view></block><block wx:else><view style="color:#ccc;">选择</view></block></view></block></view></view></view></block></block></block><block wx:if="{{nodata}}"><nodata vue-id="19d47459-7" bind:__l="__l"></nodata></block><block wx:if="{{nomore}}"><nomore vue-id="19d47459-8" bind:__l="__l"></nomore></block><block wx:if="{{loading}}"><loading vue-id="19d47459-9" bind:__l="__l"></loading></block><dp-tabbar vue-id="19d47459-10" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="19d47459-11" data-ref="popmsg" bind:__l="__l"></popmsg></view>