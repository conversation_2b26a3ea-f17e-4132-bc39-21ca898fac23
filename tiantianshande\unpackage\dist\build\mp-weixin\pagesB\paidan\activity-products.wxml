<view class="container"><block wx:if="{{isload}}"><block><view class="header-container"><view class="activity-info"><view class="activity-name">{{config.name}}</view><view class="activity-desc">{{config.copy_mode+"复制模式 · 财富奖励"+config.wealth_reward_amount+"元"}}</view></view><view data-event-opts="{{[['tap',[['toMyPosition',['$event']]]]]}}" class="my-position-btn" style="background:#FF5722;" bindtap="__e">我的点位</view></view><block wx:if="{{config.id}}"><view class="test-actions"><view class="test-buttons"><view data-event-opts="{{[['tap',[['showAddPositionModal',['$event']]]]]}}" class="test-btn" style="background:linear-gradient(90deg, #FF5722 0%, rgba(255,87,34,0.8) 100%);" bindtap="__e"><text>?? 快速增加点位</text></view><view data-event-opts="{{[['tap',[['toPositionTree',['$event']]]]]}}" class="test-btn outline" bindtap="__e"><text>?? 查看排单树</text></view></view></view></block><view class="content-container"><scroll-view class="product-box" scroll-y="true" data-event-opts="{{[['scrolltolower',[['scrolltolower',['$event']]]]]}}" bindscrolltolower="__e"><view class="product-itemlist"><block wx:for="{{datalist}}" wx:for-item="item" wx:for-index="index" wx:key="id"><view class="item" data-id="{{item.id}}" data-event-opts="{{[['tap',[['toProductDetail',['$event']]]]]}}" bindtap="__e"><view class="product-pic"><image class="image" src="{{item.thumb}}" mode="widthFix"></image></view><view class="product-info"><view class="p1"><text>{{item.name}}</text></view><view class="p2"><view class="t1" style="color:#FF5722;">{{"¥"+item.price}}</view><block wx:if="{{item.original_price*1>item.price*1}}"><text class="t2">{{"¥"+item.original_price}}</text></block></view><view class="p3"><view class="p3-1">{{item.sales_text}}</view><view class="p3-2">{{item.stock_text}}</view></view><block wx:if="{{item.category_name}}"><view class="category-tag">{{item.category_name}}</view></block></view><view class="buy-btn" style="background:linear-gradient(90deg, #FF5722 0%, rgba(255,87,34,0.8) 100%);" data-id="{{item.id}}" data-event-opts="{{[['tap',[['buyProduct',['$event']]]]]}}" catchtap="__e">购买</view></view></block></view><block wx:if="{{nomore}}"><nomore vue-id="5ee63ec7-1" text="没有更多商品了" bind:__l="__l"></nomore></block><block wx:if="{{nodata}}"><nodata vue-id="5ee63ec7-2" text="暂无参与商品" bind:__l="__l"></nodata></block><view style="width:100%;height:100rpx;"></view></scroll-view></view><block wx:if="{{showAddModal}}"><view data-event-opts="{{[['tap',[['hideAddPositionModal',['$event']]]]]}}" class="modal-overlay" bindtap="__e"><view data-event-opts="{{[['tap',[['',['$event']]]]]}}" class="modal-content" catchtap="__e"><view class="modal-header"><text class="modal-title">快速增加点位</text><text data-event-opts="{{[['tap',[['hideAddPositionModal',['$event']]]]]}}" class="modal-close" bindtap="__e">×</text></view><view class="modal-body"><view class="activity-info-modal"><text class="info-title">{{config.name}}</text><text class="info-desc">{{config.copy_mode+"复制 · 奖励"+config.wealth_reward_amount+"元"}}</text></view><view class="form-item"><text class="form-label">父点位ID（可选）：</text><input class="form-input" type="number" placeholder="不填则创建根点位" data-event-opts="{{[['input',[['__set_model',['$0','parent_id','$event',[]],['addForm']]]]]}}" value="{{addForm.parent_id}}" bindinput="__e"/></view><view class="form-item"><text class="form-label">订单ID（可选）：</text><input class="form-input" type="number" placeholder="关联的订单ID" data-event-opts="{{[['input',[['__set_model',['$0','order_id','$event',[]],['addForm']]]]]}}" value="{{addForm.order_id}}" bindinput="__e"/></view><view class="form-tips"><text>?? 测试功能说明：</text><text>• 此功能仅用于测试排单系统</text><text>• 不填父点位ID将创建根点位</text><text>• 系统自动计算层级和位置</text><text>• 财富点位根据配置自动判断</text></view></view><view class="modal-footer"><view data-event-opts="{{[['tap',[['hideAddPositionModal',['$event']]]]]}}" class="modal-btn cancel" bindtap="__e">取消</view><view data-event-opts="{{[['tap',[['confirmAddPosition',['$event']]]]]}}" class="modal-btn confirm" style="background:#FF5722;" bindtap="__e">立即添加</view></view></view></view></block></block></block><block wx:if="{{loading}}"><loading vue-id="5ee63ec7-3" loadstyle="left:62.5%" bind:__l="__l"></loading></block><dp-tabbar vue-id="5ee63ec7-4" opt="{{opt}}" data-event-opts="{{[['^getmenuindex',[['getmenuindex']]]]}}" bind:getmenuindex="__e" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="5ee63ec7-5" data-ref="popmsg" bind:__l="__l"></popmsg></view>