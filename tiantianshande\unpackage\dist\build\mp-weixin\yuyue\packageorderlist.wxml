<view class="container"><view class="tabs"><block wx:for="{{tabs}}" wx:for-item="tab" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['switchTab',[index]]]]]}}" class="{{['tab-item',(currentTab===index)?'active':'']}}" style="{{(currentTab===index&&themeColor?'color:'+themeColor+';border-bottom-color:'+themeColor:'')}}" bindtap="__e">{{''+tab.name+''}}</view></block></view><view class="order-list"><block wx:if="{{$root.g0>0}}"><view><block wx:for="{{$root.l0}}" wx:for-item="order" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['gotoDetail',['$0'],[[['orderList','',index,'id']]]]]]]}}" class="order-item" bindtap="__e"><view class="item-header"><text class="order-time">{{order.$orig.createtime}}</text><text class="order-status" style="{{'color:'+(order.m0)+';'}}">{{order.$orig.status_text}}</text></view><view class="item-content"><image class="package-pic-list" src="{{order.$orig.package_pic}}" mode="aspectFill"></image><view class="package-info-list"><view class="package-name-list">{{order.$orig.package_name}}</view><view class="package-services"><text>{{"总次数: "+order.$orig.total_services}}</text><text class="remain">{{"剩余: "+order.$orig.remain_services}}</text></view><block wx:if="{{order.$orig.expires_time_format}}"><view class="package-expire">{{"有效期至: "+order.$orig.expires_time_format}}</view></block></view></view><view class="item-footer"><view class="total-price-list">实付:<text style="{{'color:'+(themeColor)+';'}}">{{"￥"+order.$orig.total_price}}</text></view><view class="item-actions"><block wx:if="{{order.$orig.status===0}}"><button data-event-opts="{{[['tap',[['cancelOrder',['$0',index],[[['orderList','',index,'id']]]]]]]}}" class="action-btn plain" catchtap="__e">取消订单</button></block><block wx:if="{{order.$orig.status===0}}"><button data-event-opts="{{[['tap',[['gotoPay',['$0','$1'],[[['orderList','',index,'payorderid']],[['orderList','',index,'id']]]]]]]}}" class="action-btn primary" style="{{'background:'+(themeColor)+';'}}" catchtap="__e">去支付</button></block><block wx:if="{{order.m1}}"><button data-event-opts="{{[['tap',[['gotoDetail',['$0'],[[['orderList','',index,'id']]]]]]]}}" class="action-btn primary" style="{{'background:'+(themeColor)+';'}}" catchtap="__e">去使用</button></block><block wx:if="{{order.m2}}"><button data-event-opts="{{[['tap',[['applyRefund',['$0'],[[['orderList','',index,'id']]]]]]]}}" class="action-btn plain" catchtap="__e">申请退款</button></block><block wx:if="{{order.$orig.status===2||order.$orig.status>4}}"><button data-event-opts="{{[['tap',[['deleteOrder',['$0',index],[[['orderList','',index,'id']]]]]]]}}" class="action-btn plain" catchtap="__e">删除</button></block></view></view></view></block><block wx:if="{{loading}}"><view class="loading-tip">加载中...</view></block><block wx:if="{{nodata}}"><view class="nodata-tip">没有更多订单了~</view></block></view></block><block wx:else><block wx:if="{{!loading}}"><view class="empty-tip list-empty"><image class="empty-img" src="/static/img/nodata.png" mode="widthFix"></image><text>暂无相关订单</text></view></block></block></view></view>