<view class="page"><block wx:if="{{loading}}"><loading vue-id="9454a1d8-1" bind:__l="__l"></loading></block><block wx:if="{{isload}}"><block><view class="search-bar"><view class="search-input-wrap"><input class="search-input" type="text" placeholder="搜索关键词" data-event-opts="{{[['confirm',[['search',['$event']]]],['input',[['__set_model',['','keywords','$event',[]]]]]]}}" value="{{keywords}}" bindconfirm="__e" bindinput="__e"/><text class="search-icon">🔍</text></view></view><block wx:if="{{$root.g0>0}}"><scroll-view class="category-scroll" scroll-x="{{true}}"><view class="category-list"><view data-event-opts="{{[['tap',[['selectCategory',[0]]]]]}}" class="{{['category-item',(currentCid===0)?'active':'']}}" bindtap="__e"><text class="category-text">全部</text></view><block wx:for="{{categories}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['selectCategory',['$0'],[[['categories','',index,'id']]]]]]]}}" class="{{['category-item',(currentCid===item.id)?'active':'']}}" bindtap="__e"><text class="category-text">{{item.name}}</text></view></block></view></scroll-view></block><view class="list-container"><block wx:for="{{list}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="list-item" data-url="{{'detail?id='+item.id+'&type='+type}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="item-header"><text class="title">{{item.title}}</text><text class="category">{{item.categoryname}}</text></view><view class="item-content"><text class="content">{{item.content}}</text></view><view class="item-footer"><view class="info"><text class="time">{{item.addtime}}</text><text class="contact">{{item.contact_name}}</text></view><block wx:if="{{type===2}}"><view class="price-info"><text class="price">{{"￥"+(item.price||'面议')}}</text></view></block><block wx:else><view class="status"><text class="status-text">待报价</text></view></block></view></view></block><block wx:if="{{$root.g1===0}}"><view class="empty"><image class="empty-image" src="/static/images/empty.png" mode="aspectFit"></image><text class="empty-text">暂无数据</text></view></block></view><view class="footer"><block wx:if="{{type===1}}"><view class="publish-btn" data-url="fatie?type=qiugou" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><text class="btn-text">发布采购</text></view></block><block wx:else><view class="publish-btn" data-url="fatie?type=gongying" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><text class="btn-text">发布供应</text></view></block></view></block></block><popmsg class="vue-ref" vue-id="9454a1d8-2" data-ref="popmsg" bind:__l="__l"></popmsg></view>