<view class="container"><block wx:if="{{isload}}"><block><dd-tab vue-id="4a5fb66b-1" itemdata="{{['全部','待付款','待发货','待收货','已完成','退款']}}" itemst="{{['all','0','1','2','3','10']}}" st="{{st}}" isfixed="{{true}}" data-event-opts="{{[['^changetab',[['changetab']]]]}}" bind:changetab="__e" bind:__l="__l"></dd-tab><view style="width:100%;height:100rpx;"></view><view class="topsearch flex-y-center"><view class="f1 flex-y-center"><image class="img" src="{{pre_url+'/static/img/search_ico.png'}}"></image><input placeholder="输入关键字搜索" placeholder-style="font-size:24rpx;color:#C2C2C2" data-event-opts="{{[['confirm',[['searchConfirm',['$event']]]]]}}" value="{{keyword}}" bindconfirm="__e"/></view></view><view class="order-content"><block wx:for="{{datalist}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><view class="order-box" data-url="{{'collageorderdetail?id='+item.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="head"><view>{{"订单号："+item.ordernum}}</view><view class="flex1"></view><block wx:if="{{item.status==0}}"><text class="st0">待付款</text></block><block wx:if="{{item.status==1}}"><block><block wx:if="{{item.buytype!=1}}"><block><block wx:if="{{item.team.status==1}}"><text class="st1">拼团中</text></block><block wx:if="{{item.team.status==2&&item.freight_type!=1}}"><text class="st1">拼团成功,待发货</text></block><block wx:if="{{item.team.status==2&&item.freight_type==1}}"><text class="st1">拼团成功,待提货</text></block><block wx:if="{{item.team.status==3}}"><text class="st4">拼团失败,已退款</text></block></block></block><block wx:else><block><block wx:if="{{item.freight_type!=1}}"><text class="st1">待发货</text></block><block wx:if="{{item.freight_type==1}}"><text class="st1">待提货</text></block></block></block></block></block><block wx:if="{{item.status==2}}"><text class="st2">待收货</text></block><block wx:if="{{item.status==3}}"><text class="st3">已完成</text></block><block wx:if="{{item.status==4}}"><text class="st4">已关闭</text></block></view><view class="content" style="border-bottom:none;"><view data-url="{{'product?id='+item.proid}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image src="{{item.propic}}"></image></view><view class="detail"><text class="t1">{{item.proname}}</text><text class="t2">{{item.ggname}}</text><view class="t3"><text class="x1 flex1">{{"￥"+item.sell_price}}</text><text class="x2">{{"×"+item.num}}</text></view></view></view><view class="bottom"><text>{{"共计"+item.num+"件商品 实付:￥"+item.totalprice}}</text><block wx:if="{{item.refund_status==1}}"><text style="color:red;">{{'退款中￥'+item.refund_money}}</text></block><block wx:if="{{item.refund_status==2}}"><text style="color:red;">{{'已退款￥'+item.refund_money}}</text></block><block wx:if="{{item.refund_status==3}}"><text style="color:red;">退款申请已驳回</text></block></view><view class="bottom flex-y-center"><image style="width:40rpx;height:40rpx;border-radius:50%;margin-right:10rpx;" src="{{item.member.headimg}}"></image><text style="font-weight:bold;color:#333;margin-right:8rpx;">{{item.member.nickname}}</text>{{"(ID:"+item.mid+')'}}</view></view></block></block></view><block wx:if="{{nomore}}"><nomore vue-id="4a5fb66b-2" bind:__l="__l"></nomore></block><block wx:if="{{nodata}}"><nodata vue-id="4a5fb66b-3" bind:__l="__l"></nodata></block></block></block><block wx:if="{{loading}}"><loading vue-id="4a5fb66b-4" bind:__l="__l"></loading></block><popmsg class="vue-ref" vue-id="4a5fb66b-5" data-ref="popmsg" bind:__l="__l"></popmsg></view>