require('../common/vendor.js');(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pagesB/coze/workflow"],{"043c":function(t,o,e){"use strict";e.r(o);var a=e("4077"),n=e.n(a);for(var r in a)["default"].indexOf(r)<0&&function(t){e.d(o,t,(function(){return a[t]}))}(r);o["default"]=n.a},"318cf":function(t,o,e){"use strict";(function(t,o){var a=e("47a9");e("06e9");a(e("3240"));var n=a(e("9cb7"));t.__webpack_require_UNI_MP_PLUGIN__=e,o(n.default)}).call(this,e("3223")["default"],e("df3c")["createPage"])},4077:function(t,o,e){"use strict";(function(t){function e(t,o){var e="undefined"!==typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!e){if(Array.isArray(t)||(e=function(t,o){if(!t)return;if("string"===typeof t)return a(t,o);var e=Object.prototype.toString.call(t).slice(8,-1);"Object"===e&&t.constructor&&(e=t.constructor.name);if("Map"===e||"Set"===e)return Array.from(t);if("Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e))return a(t,o)}(t))||o&&t&&"number"===typeof t.length){e&&(t=e);var n=0,r=function(){};return{s:r,n:function(){return n>=t.length?{done:!0}:{done:!1,value:t[n++]}},e:function(t){throw t},f:r}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var i,s=!0,l=!1;return{s:function(){e=e.call(t)},n:function(){var t=e.next();return s=t.done,t},e:function(t){l=!0,i=t},f:function(){try{s||null==e.return||e.return()}finally{if(l)throw i}}}}function a(t,o){(null==o||o>t.length)&&(o=t.length);for(var e=0,a=new Array(o);e<o;e++)a[e]=t[e];return a}Object.defineProperty(o,"__esModule",{value:!0}),o.default=void 0;var n=getApp(),r={data:function(){return{isload:!1,loading:!1,workflowList:[],selectedWorkflow:{},paramMode:"custom",paramList:[],paramValues:{},jsonParams:"",isAsync:!1,currentSelectParam:{},workflowResult:"",nodata:!1}},onLoad:function(t){this.getWorkflowList()},methods:{getWorkflowList:function(){var o=this;o.loading=!0,n.post("ApiCoze/getWorkflowList",{},(function(e){o.loading=!1,1===e.code?(o.workflowList=e.data||[],0===o.workflowList.length&&(o.nodata=!0)):(t.showToast({title:e.msg,icon:"none",duration:2e3}),o.nodata=!0),o.loaded()}))},selectWorkflow:function(t){var o=JSON.parse(t.currentTarget.dataset.workflow);this.selectedWorkflow=o,this.getWorkflowParams(o.workflow_id)},getWorkflowParams:function(o){var e=this;e.loading=!0,n.post("ApiCoze/getWorkflowParams",{workflow_id:o},(function(o){e.loading=!1,1===o.code?(e.paramMode=o.data.mode,"custom"===o.data.mode?(e.paramList=o.data.params||[],e.paramValues={},e.paramList.forEach((function(t){t.default_value&&(e.paramValues[t.param_key]=t.default_value)}))):e.jsonParams=JSON.stringify(o.data.params||{},null,2),e.$refs.paramPopup.open()):t.showToast({title:o.msg,icon:"none",duration:2e3})}))},closeParamPopup:function(){this.$refs.paramPopup.close()},showSelectOptions:function(t){var o=JSON.parse(t.currentTarget.dataset.param);this.currentSelectParam=o,this.$refs.selectPopup.open()},closeSelectPopup:function(){this.$refs.selectPopup.close()},selectOption:function(t){var o=t.currentTarget.dataset.value,e=t.currentTarget.dataset.paramKey;this.paramValues[e]=o,this.closeSelectPopup()},toggleAsync:function(){this.isAsync=!this.isAsync},chooseFile:function(o){var e=o.currentTarget.dataset.paramKey,a=this;t.chooseFile({count:1,success:function(t){var o=t.tempFiles[0];a.paramValues[e]={name:o.name,path:o.path,size:o.size}}})},runWorkflow:function(){if(this.selectedWorkflow.workflow_id){var o={};if("custom"===this.paramMode){var a,r=e(this.paramList);try{for(r.s();!(a=r.n()).done;){var i=a.value;if(i.is_required&&!this.paramValues[i.param_key])return void t.showToast({title:i.param_name+"不能为空",icon:"none",duration:2e3})}}catch(l){r.e(l)}finally{r.f()}o=this.paramValues}else try{o=JSON.parse(this.jsonParams)}catch(u){return void t.showToast({title:"参数格式错误，请输入有效的JSON",icon:"none",duration:2e3})}var s=this;s.loading=!0,n.post("ApiCoze/runWorkflowWithParams",{workflow_id:s.selectedWorkflow.workflow_id,params:o,is_async:s.isAsync},(function(o){s.loading=!1,1===o.code?s.isAsync?(t.showToast({title:"工作流已开始异步执行，请在执行记录中查看结果",icon:"success",duration:3e3}),s.closeParamPopup()):(s.workflowResult=JSON.stringify(o.data,null,2),s.closeParamPopup(),s.$refs.resultPopup.open()):t.showToast({title:o.msg,icon:"none",duration:2e3})}))}},closeResultPopup:function(){this.$refs.resultPopup.close()}}};o.default=r}).call(this,e("df3c")["default"])},"9cb7":function(t,o,e){"use strict";e.r(o);var a=e("cd2a"),n=e("043c");for(var r in n)["default"].indexOf(r)<0&&function(t){e.d(o,t,(function(){return n[t]}))}(r);e("d56d");var i=e("828b"),s=Object(i["a"])(n["default"],a["b"],a["c"],!1,null,null,null,!1,a["a"],void 0);o["default"]=s.exports},cd2a:function(t,o,e){"use strict";e.d(o,"b",(function(){return n})),e.d(o,"c",(function(){return r})),e.d(o,"a",(function(){return a}));var a={nodata:function(){return e.e("components/nodata/nodata").then(e.bind(null,"101c"))},uniPopup:function(){return Promise.all([e.e("common/vendor"),e.e("components/uni-popup/uni-popup")]).then(e.bind(null,"ca44a"))},loading:function(){return e.e("components/loading/loading").then(e.bind(null,"ceaa"))}},n=function(){var t=this,o=t.$createElement,e=(t._self._c,t.isload?t.__map(t.workflowList,(function(o,e){var a=t.__get_orig(o),n=JSON.stringify(o),r=t.t("color1");return{$orig:a,g0:n,m0:r}})):null),a=t.isload&&"custom"===t.paramMode?t.__map(t.paramList,(function(o,e){var a=t.__get_orig(o),n="text"!==o.param_type&&"number"!==o.param_type&&"select"===o.param_type?JSON.stringify(o):null;return{$orig:a,g1:n}})):null,n=t.isload&&t.isAsync?t.t("color1"):null,r=t.isload?t.t("color1"):null,i=t.isload?t.__map(t.currentSelectParam.param_options,(function(o,e){var a=t.__get_orig(o),n=t.paramValues[t.currentSelectParam.param_key]===o.value?t.t("color1"):null;return{$orig:a,m3:n}})):null,s=t.isload?t.t("color1"):null;t.$mp.data=Object.assign({},{$root:{l0:e,l1:a,m1:n,m2:r,l2:i,m4:s}})},r=[]},d1df:function(t,o,e){},d56d:function(t,o,e){"use strict";var a=e("d1df"),n=e.n(a);n.a}},[["318cf","common/runtime","common/vendor"]]]);