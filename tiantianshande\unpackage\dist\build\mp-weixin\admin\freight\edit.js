require('../common/vendor.js');(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["admin/freight/edit"],{"05f4":function(t,e,r){"use strict";var i=r("593c"),n=r.n(i);n.a},"07ec":function(t,e,r){"use strict";r.r(e);var i=r("65ed"),n=r("10c33");for(var a in n)["default"].indexOf(a)<0&&function(t){r.d(e,t,(function(){return n[t]}))}(a);r("05f4");var o=r("828b"),c=Object(o["a"])(n["default"],i["b"],i["c"],!1,null,null,null,!1,i["a"],void 0);e["default"]=c.exports},"10c33":function(t,e,r){"use strict";r.r(e);var i=r("4a04b"),n=r.n(i);for(var a in i)["default"].indexOf(a)<0&&function(t){r.d(e,t,(function(){return i[t]}))}(a);e["default"]=n.a},"4a04b":function(t,e,r){"use strict";(function(t){var i=r("47a9");Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n=i(r("34cf"));function a(t,e){var r="undefined"!==typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!r){if(Array.isArray(t)||(r=function(t,e){if(!t)return;if("string"===typeof t)return o(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return o(t,e)}(t))||e&&t&&"number"===typeof t.length){r&&(t=r);var i=0,n=function(){};return{s:n,n:function(){return i>=t.length?{done:!0}:{done:!1,value:t[i++]}},e:function(t){throw t},f:n}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,c=!0,s=!1;return{s:function(){r=r.call(t)},n:function(){var t=r.next();return c=t.done,t},e:function(t){s=!0,a=t},f:function(){try{c||null==r.return||r.return()}finally{if(s)throw a}}}}function o(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,i=new Array(e);r<e;r++)i[r]=t[r];return i}var c=getApp(),s={data:function(){return{id:"",formData:{id:"",pstype:0,name:"普通快递",type:1,status:1,sort:0,freeset:0,free_price:0,minpriceset:0,minprice:0,pricedata:[{region:"全国(默认运费)",is_delivery:!0,fristweight:"1000",fristprice:"0",secondweight:"1000",secondprice:"0"}],formdata:[]}}},onLoad:function(t){this.opt=c.getopts(t),t.id&&(this.id=t.id,this.getTemplateInfo())},methods:{getRegionText:function(t){if(!t)return"点击选择区域";if("全国(默认运费)"===t)return t;try{var e=t.split(";").filter((function(t){return t})).length;return"已选".concat(e,"个省市")}catch(r){return console.error("解析区域文本失败:",r),"点击选择区域"}},toggleDelivery:function(t){this.formData.pricedata[t].is_delivery=!this.formData.pricedata[t].is_delivery},addArea:function(){if(this.formData.pricedata.length>=20)t.showToast({title:"最多添加20个区域",icon:"none"});else{var e=this.formData.pricedata.some((function(t){return"全国(默认运费)"===t.region}));this.formData.pricedata.push({region:e?"":"全国(默认运费)",is_delivery:!0,fristweight:"1000",fristprice:"0",secondweight:"1000",secondprice:"0"})}},removeArea:function(t){this.formData.pricedata.splice(t,1)},handleRegionSelect:function(e){var r=this,i=this.formData.pricedata[e];"全国(默认运费)"!==i.region?t.navigateTo({url:"./region-select",events:{onSelected:function(i){if(Array.isArray(i))try{var a=new Map;i.forEach((function(t){var e=t.province.name;a.has(e)||a.set(e,new Set),"全部地区"===t.city.name?a.get(e).add("全部地区"):a.get(e).add(t.city.name)}));var o=Array.from(a.entries()).map((function(t){var e=(0,n.default)(t,2),r=e[0],i=e[1],a=Array.from(i);return"".concat(r,"[").concat(a.join(","),"]")})).join(";");o&&!o.endsWith(";")&&(o+=";"),r.$set(r.formData.pricedata[e],"region",o)}catch(c){console.error("处理区域数据时出错:",c),t.showToast({title:"处理区域数据失败",icon:"none"})}else console.error("接收到的regions不是数组:",i)}},success:function(t){var e=[];try{if(i.region&&"全国(默认运费)"!==i.region){var r=i.region.split(";").filter((function(t){return t}));r.forEach((function(t){var r=t.split("["),i=(0,n.default)(r,2),a=i[0],o=i[1],c=o.replace("]","").split(",");c.forEach((function(t){e.push({province:{name:a},city:{name:"全部地区"},area:{name:t},name:"".concat(a,"全部地区").concat(t),code:"".concat(a,"_").concat(t)})}))}))}}catch(a){console.error("解析已选区域失败:",a)}t.eventChannel.emit("currentSelected",{selected:e})}}):t.showToast({title:"默认运费区域不可修改",icon:"none"})},saveTemplate:function(){if(this.validateForm()){var e={info:{id:this.id||"",pstype:this.formData.pstype,name:this.formData.name,type:this.formData.type,storetype:0,freeset:this.formData.freeset,free_price:this.formData.free_price,minprice:this.formData.minprice,sort:this.formData.sort,status:this.formData.status,pricedata:this.formData.pricedata.map((function(t){return{region:t.region,is_delivery:t.is_delivery?1:0,fristweight:t.fristweight,fristprice:t.fristprice,secondweight:t.secondweight,secondprice:t.secondprice}}))}};c.post("ApiAdminProduct/freightSave",e,(function(e){1===e.status?(c.success(e.msg),setTimeout((function(){t.navigateBack({delta:1})}),1500)):c.error(e.msg)}))}},getTemplateInfo:function(){var t=this;c.post("ApiAdminProduct/freightInfo",{id:t.id},(function(e){if(e.info){var r=e.info.pricedata;try{r="string"===typeof r?JSON.parse(r):r}catch(i){r=[{region:"全国(默认运费)",is_delivery:!0,fristweight:"1000",fristprice:"0",secondweight:"1000",secondprice:"0"}]}t.formData={id:e.info.id||"",pstype:e.info.pstype||0,name:e.info.name||"普通快递",type:e.info.type||1,status:e.info.status||1,sort:e.info.sort||0,freeset:e.info.freeset||0,free_price:e.info.free_price||0,minpriceset:e.info.minpriceset||0,minprice:e.info.minprice||0,pricedata:r}}}))},getAreaList:function(){var e=this;t.request({url:c.globalData.pre_url+"/static/area.json",method:"GET",success:function(t){e.areaList=t.data}})},validateForm:function(){var t=this.formData,e=t.name,r=(t.type,t.pricedata),i=t.freeset,n=t.free_price,o=t.minpriceset,s=t.minprice;if(!e)return c.error("请输入模板名称"),!1;if(!r||0===r.length)return c.error("请设置运费规则"),!1;if(1===i&&(!n||n<=0))return c.error("请输入正确的包邮金额"),!1;if(1===o&&(!s||s<=0))return c.error("请输入正确的起送金额"),!1;var f,u=a(r);try{for(u.s();!(f=u.n()).done;){var d=f.value;if(d.is_delivery){if(!d.fristweight||!d.fristprice)return c.error("请完善首重信息"),!1;if(!d.secondweight||!d.secondprice)return c.error("请完善续重信���"),!1}}}catch(l){u.e(l)}finally{u.f()}return!0},getdata:function(t){t||(this.pagenum=1,this.datalist=[]);var e=this,r=e.pagenum;e.nodata=!1,e.nomore=!1,e.loading=!0,c.post("ApiAdminProduct/freightList",{keyword:e.keyword,pagenum:r,st:e.st},(function(t){e.loading=!1;var i=t.datalist;if(1==r)e.countall=t.countall,e.count0=t.count0,e.count1=t.count1,e.datalist=i,0==i.length&&(e.nodata=!0),e.loaded();else if(0==i.length)e.nomore=!0;else{var n=e.datalist,a=n.concat(i);e.datalist=a}}))},changetab:function(t){this.st=t,this.getdata()},searchConfirm:function(t){this.keyword=t.detail.value,this.getdata(!1)},handleTypeChange:function(t){this.formData.type=t,this.formData.pricedata.forEach((function(e){1===t?(e.fristweight="1000",e.secondweight="1000"):(e.fristweight="1",e.secondweight="1")}))}}};e.default=s}).call(this,r("df3c")["default"])},"593c":function(t,e,r){},"65ed":function(t,e,r){"use strict";r.d(e,"b",(function(){return i})),r.d(e,"c",(function(){return n})),r.d(e,"a",(function(){}));var i=function(){var t=this,e=t.$createElement,r=(t._self._c,t.__map(t.formData.pricedata,(function(e,r){var i=t.__get_orig(e),n=t.getRegionText(e.region);return{$orig:i,m0:n}}))),i=t.formData.pricedata.length;t._isMounted||(t.e0=function(e){t.formData.pstype=0},t.e1=function(e){t.formData.pstype=1},t.e2=function(e){t.formData.pstype=2},t.e3=function(e){return t.formData.freeset=e.detail.value?1:0},t.e4=function(e){return t.formData.minpriceset=e.detail.value?1:0}),t.$mp.data=Object.assign({},{$root:{l0:r,g0:i}})},n=[]},fbc1:function(t,e,r){"use strict";(function(t,e){var i=r("47a9");r("06e9");i(r("3240"));var n=i(r("07ec"));t.__webpack_require_UNI_MP_PLUGIN__=r,e(n.default)}).call(this,r("3223")["default"],r("df3c")["createPage"])}},[["fbc1","common/runtime","common/vendor"]]]);