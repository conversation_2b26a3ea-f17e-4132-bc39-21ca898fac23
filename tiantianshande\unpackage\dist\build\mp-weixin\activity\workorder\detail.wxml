<view class="container"><block wx:if="{{isload}}"><block><view class="orderinfo"><view class="item"><text class="t1">标题</text><text class="t2">{{detail.title}}</text></view><block wx:for="{{formcontent}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="item"><text class="t1">{{item.val1}}</text><block wx:if="{{item.key!='upload'}}"><text class="t2">{{detail['form'+index]}}</text></block><block wx:else><view class="t2" style="display:flex;justify-content:flex-end;"><block wx:for="{{detail['form'+index]}}" wx:for-item="sub" wx:for-index="indx" wx:key="indx"><view><image style="width:50px;margin-left:10rpx;" src="{{sub}}" mode="widthFix" data-url="{{sub}}" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image></view></block></view></block></view></block><view class="item"><text class="t1">提交时间</text><text class="t2">{{detail.createtime}}</text></view><view class="item"><text class="t1">审核状态</text><block wx:if="{{detail.status==0&&(!detail.payorderid||detail.paystatus==1)}}"><text class="t2" style="color:#88e;">待处理</text></block><block wx:if="{{detail.status==0&&detail.payorderid&&detail.paystatus==0}}"><text class="t2" style="color:red;">待支付</text></block><block wx:if="{{detail.status==1}}"><text class="t2" style="color:#F7B52D;">处理中</text></block><block wx:if="{{detail.status==2}}"><text class="t2" style="color:green;">已完成</text></block><block wx:if="{{detail.status==-1}}"><text class="t2" style="color:red;">已驳回</text></block></view><block wx:if="{{detail.status==-1}}"><view class="item"><text class="t1">驳回原因</text><text class="t2" style="color:red;">{{detail.reason}}</text></view></block><block wx:if="{{detail.payorderid}}"><block><view class="item"><text class="t1">付款金额</text><text class="t2" style="font-size:32rpx;color:#e94745;">{{"￥"+detail.money}}</text></view><view class="item"><text class="t1">付款方式</text><text class="t2">{{detail.paytype}}</text></view><view class="item"><text class="t1">付款状态</text><block wx:if="{{detail.paystatus==1&&detail.isrefund==0}}"><text class="t2" style="color:green;">已付款</text></block><block wx:if="{{detail.paystatus==1&&detail.isrefund==1}}"><text class="t2" style="color:red;">已退款</text></block><block wx:if="{{detail.paystatus==0}}"><text class="t2" style="color:red;">未付款</text></block></view><block wx:if="{{detail.paystatus>0&&detail.paytime}}"><view class="item"><text class="t1">付款时间</text><text class="t2">{{detail.paytime}}</text></view></block></block></block><block wx:if="{{detail.ordertype==1&&detail.glordernum&&detail.orderid}}"><block><view class="item"><text class="t1">关联订单</text><text class="t2" data-url="{{'pages/order/detail?id='+detail.orderid}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">{{"商城订单（"+detail.glordernum+')'}}</text></view></block></block><block wx:if="{{detail.ordertype==2&&detail.glordernum&&detail.orderid}}"><block><view class="item"><text class="t1">关联订单</text><text class="t2" data-url="{{'/yuyue/orderdetail?id='+detail.orderid}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">{{"预约订单（"+detail.glordernum+')'}}</text></view></block></block><block wx:if="{{detail.iscomment==1}}"><block><view class="item"><text class="t1">满意度</text><block wx:if="{{detail.comment_status==1}}"><text class="t2" style="color:red;">不满意</text></block><block wx:if="{{detail.comment_status==2}}"><text class="t2" style="color:#88e;">一般</text></block><block wx:if="{{detail.comment_status==3}}"><text class="t2" style="color:green;">满意</text></block></view></block></block></view><view style="width:100%;height:160rpx;"></view><view class="bottom notabbarbot"><block wx:if="{{detail.status==2&&detail.iscomment==0}}"><view data-event-opts="{{[['tap',[['jindu',['$event']]]]]}}" class="btn2" bindtap="__e">评价</view></block><view class="btn2" data-id="{{detail.id}}" data-event-opts="{{[['tap',[['todel',['$event']]]]]}}" bindtap="__e">删除</view><block wx:if="{{detail.fromurl}}"><view class="btn2" data-url="{{detail.fromurl}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">再次提交</view></block><block wx:if="{{detail.payorderid&&detail.paystatus==0}}"><block><view class="btn1" style="{{'background:'+($root.m0)+';'}}" data-url="{{'/pages/pay/pay?id='+detail.payorderid}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">去付款</view></block></block></view><block wx:if="{{ishowjindu}}"><view class="modal"><view class="modal_jindu"><form data-event-opts="{{[['submit',[['formsubmit',['$event']]]]]}}" bindsubmit="__e"><view data-event-opts="{{[['tap',[['close',['$event']]]]]}}" class="close" bindtap="__e"><image src="../../static/img/close.png"></image></view><block wx:if="{{$root.g0>0}}"><block><block wx:for="{{$root.l1}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="item"><view class="f1"><image src="{{'/static/img/jindu'+(index==0?'2':'1')+'.png'}}"></image></view><view class="f2"><text class="t2">{{'时间：'+item.$orig.time}}</text><text class="t1">{{item.$orig.desc+"("+item.$orig.remark+')'}}</text><block wx:for="{{item.l0}}" wx:for-item="hf" wx:for-index="hfindex" wx:key="hfindex"><view><block wx:if="{{hf.$orig.hfremark}}"><view class="t3">{{"我的回复："+hf.$orig.hfremark+''}}</view></block><block wx:if="{{hf.$orig.hftime}}"><view class="t4">{{"回复时间："+hf.$orig.hftime+''}}</view></block><block wx:for="{{hf.$orig.hfcontent_pic}}" wx:for-item="pic2" wx:for-index="ind2"><block wx:if="{{hf.g1>0}}"><view><view class="layui-imgbox-img"><image src="{{pic2}}" data-url="{{pic2}}" mode="widthFix" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image></view></view></block></block></view></block></view></view></block><input style="display:none;" type="hidden" name="lcid" value="{{jdlist[0].id}}"/><block wx:if="{{detail.status!=2}}"><view class="hfbox"><label>回复:</label><textarea placeholder="输入内容" name="content" maxlength="-1" value="{{jdlist[0].hfremark}}"></textarea></view></block><block wx:if="{{detail.status!=2}}"><view class="btnbox"><view class="f1"><button data-event-opts="{{[['tap',[['confirmend',['$event']]]]]}}" class="btn1" bindtap="__e">确认结束</button><button class="btn2" form-type="submit">提交</button></view></view></block><block wx:if="{{detail.status==2&&detail.iscomment==0}}"><view class="btnbox"><view class="pjitem"><view class="item"><view style="margin-right:10rpx;">满意度</view><radio-group data-event-opts="{{[['change',[['radioChange',['$event']]]]]}}" bindchange="__e"><label class="radio"><radio value="1"></radio>不满意</label><label class="radio"><radio value="2"></radio>一般</label><label class="radio"><radio value="3"></radio>满意</label></radio-group></view><button data-event-opts="{{[['tap',[['tocomment',['$event']]]]]}}" class="btn1" bindtap="__e">确认评价</button></view></view></block><block wx:if="{{detail.status==2&&detail.iscomment==1}}"><view class="btnbox"><view class="pjitem"><view class="item"><view style="margin-right:10rpx;">满意度:</view><block wx:if="{{comment_status==1}}"><view class="t1">不满意</view></block><block wx:if="{{comment_status==2}}"><view class="t1">一般</view></block><block wx:if="{{comment_status==3}}"><view class="t1">满意</view></block></view></view></view></block></block></block><block wx:else><block><view style="font-size:14px;color:#f05555;padding:10px;">等待处理</view></block></block></form></view></view></block></block></block><block wx:if="{{loading}}"><loading vue-id="f8e08bfe-1" bind:__l="__l"></loading></block><dp-tabbar vue-id="f8e08bfe-2" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="f8e08bfe-3" data-ref="popmsg" bind:__l="__l"></popmsg></view>