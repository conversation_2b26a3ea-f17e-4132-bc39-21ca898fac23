<view class="container"><block wx:if="{{isload}}"><block><dd-tab vue-id="7ddc4f98-1" itemdata="{{['全部','已发放','未发放']}}" itemst="{{['0','1','2']}}" st="{{st}}" isfixed="{{false}}" data-event-opts="{{[['^changetab',[['changetab']]]]}}" bind:changetab="__e" bind:__l="__l"></dd-tab><view class="content"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="item"><view class="f2"><view class="t1"><text class="x1">{{item.$orig.remark}}</text><text class="x2">{{"产生时间："+item.m0}}</text><view class="x3">来源：<image src="{{item.$orig.fromheadimg}}"></image>{{item.$orig.fromnickname}}</view></view><view class="t2"><text class="x1">{{"+"+item.$orig.commission}}</text><block wx:if="{{item.$orig.status==0}}"><block><block wx:if="{{item.$orig.orderstatus==1||item.$orig.orderstatus==2}}"><text class="dior-sp6 yfk">已付款</text></block><block wx:if="{{item.$orig.orderstatus==0}}"><text class="dior-sp6 dfk">待付款</text></block><block wx:if="{{item.$orig.orderstatus==3}}"><text class="dior-sp6 ywc">待发放</text></block><block wx:if="{{item.$orig.orderstatus==4}}"><text class="dior-sp6 ygb">已关闭</text></block></block></block><block wx:if="{{item.$orig.status==1}}"><block><text class="dior-sp6 yfk">已发放</text></block></block></view></view></view></block></view></block></block><block wx:if="{{nodata}}"><nodata vue-id="7ddc4f98-2" bind:__l="__l"></nodata></block><block wx:if="{{nomore}}"><nomore vue-id="7ddc4f98-3" bind:__l="__l"></nomore></block><block wx:if="{{loading}}"><loading vue-id="7ddc4f98-4" bind:__l="__l"></loading></block><dp-tabbar vue-id="7ddc4f98-5" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="7ddc4f98-6" data-ref="popmsg" bind:__l="__l"></popmsg></view>