<view><block wx:if="{{isload}}"><block><form data-event-opts="{{[['submit',[['subform',['$event']]]]]}}" bindsubmit="__e"><view class="form-box"><view class="form-item flex-col" style="border-bottom:0;"><view class="f1" style="width:100%;">请选择名片背景</view><view class="f2"><block wx:for="{{bgpic}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="layui-imgbox"><view class="layui-imgbox-close" data-index="{{index}}" data-field="bgpic" data-event-opts="{{[['tap',[['removeimg',['$event']]]]]}}" bindtap="__e"><image style="display:block;" src="/static/img/ico-del.png"></image></view><view class="layui-imgbox-img"><image src="{{item}}" data-url="{{item}}" mode="widthFix" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image></view></view></block><block wx:if="{{$root.g0==0}}"><view class="uploadbtn" style="{{('background:url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 60rpx;background-size:80rpx 80rpx;background-color:#F3F3F3;')}}" data-field="bgpic" data-pernum="1" data-event-opts="{{[['tap',[['uploadbgpic',['$event']]]]]}}" bindtap="__e"></view></block></view><input type="text" hidden="true" name="bgpic" maxlength="-1" value="{{$root.g1}}"/></view></view><view class="form-box"><view class="form-item flex-col" style="border-bottom:0;"><view class="f1" style="width:100%;"><text style="color:red;padding-right:6rpx;">*</text>请上传个人照片（上传正方形照片）</view><view class="f2"><block wx:for="{{headimg}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="layui-imgbox"><view class="layui-imgbox-close" data-index="{{index}}" data-field="headimg" data-event-opts="{{[['tap',[['removeimg',['$event']]]]]}}" bindtap="__e"><image style="display:block;" src="/static/img/ico-del.png"></image></view><view class="layui-imgbox-img"><image src="{{item}}" data-url="{{item}}" mode="widthFix" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image></view></view></block><block wx:if="{{$root.g2==0}}"><view class="uploadbtn" style="{{('background:url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 60rpx;background-size:80rpx 80rpx;background-color:#F3F3F3;')}}" data-field="headimg" data-pernum="1" data-event-opts="{{[['tap',[['uploadimg',['$event']]]]]}}" bindtap="__e"></view></block></view><input type="text" hidden="true" name="headimg" maxlength="-1" value="{{$root.g3}}"/></view></view><view class="form-box"><view class="form-item"><view class="f1"><text style="color:red;padding-right:6rpx;">*</text>请输入姓名</view><view class="f2"><input type="text" name="realname" placeholder-style="color:#888" value="{{info.realname}}"/></view></view><view class="form-item flex-col" style="border-bottom:0;"><view class="f1" style="width:100%;"><text style="color:red;padding-right:6rpx;">*</text>请添加头衔(最多添加3个)</view><view class="f2"><input style="text-align:left;" type="text" name="touxian1" placeholder="公司名称+职务，如：某科技有限公司|总经理" placeholder-style="color:#888" value="{{info.touxian1}}"/></view><view class="f2" style="margin-top:10rpx;"><input style="text-align:left;" type="text" name="touxian2" placeholder="公司名称+职务，如：某科技有限公司|总经理" placeholder-style="color:#888" value="{{info.touxian2}}"/></view><view class="f2" style="margin-top:10rpx;"><input style="text-align:left;" type="text" name="touxian3" placeholder="公司名称+职务，如：某科技有限公司|总经理" placeholder-style="color:#888" value="{{info.touxian3}}"/></view></view></view><view class="form-box"><view class="form-item flex-col" style="border-bottom:0;"><view class="f1" style="width:100%;">联系信息<text style="font-size:24rpx;color:#888;">(前三项将显示在名片封面上)</text></view></view><block wx:for="{{field_list}}" wx:for-item="item" wx:for-index="index"><block wx:if="{{item.isshow==1}}"><block><view class="form-item"><view class="f1"><text style="color:red;padding-right:6rpx;">{{item.required==1?'*':' '}}</text>{{"请输入"+item.name}}</view><view class="f2"><input type="text" name="{{index}}" placeholder-style="color:#888" value="{{index=='address'?address:info[index]}}"/></view><block wx:if="{{index=='address'}}"><view data-event-opts="{{[['tap',[['selectzuobiao',['$event']]]]]}}" class="f3" style="color:#58e;font-size:24rpx;margin-left:6rpx;" bindtap="__e">选择位置</view></block></view></block></block></block><input name="latitude" hidden="true" value="{{latitude}}"/><input name="longitude" hidden="true" value="{{longitude}}"/></view><view class="form-box"><view class="form-item flex-col"><text>个人简介</text><view class="detailop"><view data-event-opts="{{[['tap',[['detailAddtxt',['$event']]]]]}}" class="btn" bindtap="__e">+文本</view><view data-event-opts="{{[['tap',[['detailAddpic',['$event']]]]]}}" class="btn" bindtap="__e">+图片</view><view data-event-opts="{{[['tap',[['detailAddvideo',['$event']]]]]}}" class="btn" bindtap="__e">+视频</view></view><view><block wx:for="{{pagecontent}}" wx:for-item="setData" wx:for-index="index" wx:key="index"><block><view class="detaildp"><view class="op"><view class="flex1"></view><view class="btn" data-index="{{index}}" data-event-opts="{{[['tap',[['detailMoveup',['$event']]]]]}}" bindtap="__e">上移</view><view class="btn" data-index="{{index}}" data-event-opts="{{[['tap',[['detailMovedown',['$event']]]]]}}" bindtap="__e">下移</view><view class="btn" data-index="{{index}}" data-event-opts="{{[['tap',[['detailMovedel',['$event']]]]]}}" bindtap="__e">删除</view></view><view class="detailbox"><block wx:if="{{setData.temp=='notice'}}"><block><dp-notice vue-id="{{'79a1982c-1-'+index}}" params="{{setData.params}}" data="{{setData.data}}" bind:__l="__l"></dp-notice></block></block><block wx:if="{{setData.temp=='banner'}}"><block><dp-banner vue-id="{{'79a1982c-2-'+index}}" params="{{setData.params}}" data="{{setData.data}}" bind:__l="__l"></dp-banner></block></block><block wx:if="{{setData.temp=='search'}}"><block><dp-search vue-id="{{'79a1982c-3-'+index}}" params="{{setData.params}}" data="{{setData.data}}" bind:__l="__l"></dp-search></block></block><block wx:if="{{setData.temp=='text'}}"><block><dp-text vue-id="{{'79a1982c-4-'+index}}" params="{{setData.params}}" data="{{setData.data}}" bind:__l="__l"></dp-text></block></block><block wx:if="{{setData.temp=='title'}}"><block><dp-title vue-id="{{'79a1982c-5-'+index}}" params="{{setData.params}}" data="{{setData.data}}" bind:__l="__l"></dp-title></block></block><block wx:if="{{setData.temp=='dhlist'}}"><block><dp-dhlist vue-id="{{'79a1982c-6-'+index}}" params="{{setData.params}}" data="{{setData.data}}" bind:__l="__l"></dp-dhlist></block></block><block wx:if="{{setData.temp=='line'}}"><block><dp-line vue-id="{{'79a1982c-7-'+index}}" params="{{setData.params}}" data="{{setData.data}}" bind:__l="__l"></dp-line></block></block><block wx:if="{{setData.temp=='blank'}}"><block><dp-blank vue-id="{{'79a1982c-8-'+index}}" params="{{setData.params}}" data="{{setData.data}}" bind:__l="__l"></dp-blank></block></block><block wx:if="{{setData.temp=='menu'}}"><block><dp-menu vue-id="{{'79a1982c-9-'+index}}" params="{{setData.params}}" data="{{setData.data}}" bind:__l="__l"></dp-menu></block></block><block wx:if="{{setData.temp=='map'}}"><block><dp-map vue-id="{{'79a1982c-10-'+index}}" params="{{setData.params}}" data="{{setData.data}}" bind:__l="__l"></dp-map></block></block><block wx:if="{{setData.temp=='cube'}}"><block><dp-cube vue-id="{{'79a1982c-11-'+index}}" params="{{setData.params}}" data="{{setData.data}}" bind:__l="__l"></dp-cube></block></block><block wx:if="{{setData.temp=='picture'}}"><block><dp-picture vue-id="{{'79a1982c-12-'+index}}" params="{{setData.params}}" data="{{setData.data}}" bind:__l="__l"></dp-picture></block></block><block wx:if="{{setData.temp=='pictures'}}"><block><dp-pictures vue-id="{{'79a1982c-13-'+index}}" params="{{setData.params}}" data="{{setData.data}}" bind:__l="__l"></dp-pictures></block></block><block wx:if="{{setData.temp=='video'}}"><block><dp-video vue-id="{{'79a1982c-14-'+index}}" params="{{setData.params}}" data="{{setData.data}}" bind:__l="__l"></dp-video></block></block><block wx:if="{{setData.temp=='shop'}}"><block><dp-shop vue-id="{{'79a1982c-15-'+index}}" params="{{setData.params}}" data="{{setData.data}}" shopinfo="{{setData.shopinfo}}" bind:__l="__l"></dp-shop></block></block><block wx:if="{{setData.temp=='product'}}"><block><dp-product vue-id="{{'79a1982c-16-'+index}}" params="{{setData.params}}" data="{{setData.data}}" menuindex="{{menuindex}}" bind:__l="__l"></dp-product></block></block><block wx:if="{{setData.temp=='collage'}}"><block><dp-collage vue-id="{{'79a1982c-17-'+index}}" params="{{setData.params}}" data="{{setData.data}}" bind:__l="__l"></dp-collage></block></block><block wx:if="{{setData.temp=='kanjia'}}"><block><dp-kanjia vue-id="{{'79a1982c-18-'+index}}" params="{{setData.params}}" data="{{setData.data}}" bind:__l="__l"></dp-kanjia></block></block><block wx:if="{{setData.temp=='seckill'}}"><block><dp-seckill vue-id="{{'79a1982c-19-'+index}}" params="{{setData.params}}" data="{{setData.data}}" bind:__l="__l"></dp-seckill></block></block><block wx:if="{{setData.temp=='scoreshop'}}"><block><dp-scoreshop vue-id="{{'79a1982c-20-'+index}}" params="{{setData.params}}" data="{{setData.data}}" bind:__l="__l"></dp-scoreshop></block></block><block wx:if="{{setData.temp=='coupon'}}"><block><dp-coupon vue-id="{{'79a1982c-21-'+index}}" params="{{setData.params}}" data="{{setData.data}}" bind:__l="__l"></dp-coupon></block></block><block wx:if="{{setData.temp=='article'}}"><block><dp-article vue-id="{{'79a1982c-22-'+index}}" params="{{setData.params}}" data="{{setData.data}}" bind:__l="__l"></dp-article></block></block><block wx:if="{{setData.temp=='business'}}"><block><dp-business vue-id="{{'79a1982c-23-'+index}}" params="{{setData.params}}" data="{{setData.data}}" bind:__l="__l"></dp-business></block></block><block wx:if="{{setData.temp=='liveroom'}}"><block><dp-liveroom vue-id="{{'79a1982c-24-'+index}}" params="{{setData.params}}" data="{{setData.data}}" bind:__l="__l"></dp-liveroom></block></block><block wx:if="{{setData.temp=='button'}}"><block><dp-button vue-id="{{'79a1982c-25-'+index}}" params="{{setData.params}}" data="{{setData.data}}" bind:__l="__l"></dp-button></block></block><block wx:if="{{setData.temp=='hotspot'}}"><block><dp-hotspot vue-id="{{'79a1982c-26-'+index}}" params="{{setData.params}}" data="{{setData.data}}" bind:__l="__l"></dp-hotspot></block></block><block wx:if="{{setData.temp=='cover'}}"><block><dp-cover vue-id="{{'79a1982c-27-'+index}}" params="{{setData.params}}" data="{{setData.data}}" bind:__l="__l"></dp-cover></block></block><block wx:if="{{setData.temp=='richtext'}}"><block><dp-richtext vue-id="{{'79a1982c-28-'+index}}" params="{{setData.params}}" data="{{setData.data}}" content="{{setData.content}}" bind:__l="__l"></dp-richtext></block></block><block wx:if="{{setData.temp=='form'}}"><block><dp-form vue-id="{{'79a1982c-29-'+index}}" params="{{setData.params}}" data="{{setData.data}}" content="{{setData.content}}" bind:__l="__l"></dp-form></block></block><block wx:if="{{setData.temp=='userinfo'}}"><block><dp-userinfo vue-id="{{'79a1982c-30-'+index}}" params="{{setData.params}}" data="{{setData.data}}" content="{{setData.content}}" bind:__l="__l"></dp-userinfo></block></block></view></view></block></block></view></view></view><view class="form-box"><view class="form-item flex-col" style="border-bottom:0;"><view class="f1" style="width:100%;">自定义分享标题(不填写则按框中内容显示)</view><view class="f2"><input style="text-align:left;" type="text" name="sharetitle" placeholder="您好，这是我的名片，望惠存！" placeholder-style="color:#888" value="{{info.sharetitle}}"/></view></view></view><button class="savebtn" form-type="submit">提交</button><view style="height:50rpx;"></view></form><block wx:if="{{bglistshow}}"><view class="popup__container"><view data-event-opts="{{[['tap',[['changeGlistDialog',['$event']]]]]}}" class="popup__overlay" catchtap="__e"></view><view class="popup__modal"><view class="popup__title"><text class="popup__title-text">请选择名片背景图</text><image class="popup__close" style="width:36rpx;height:36rpx;" src="/static/img/close.png" data-event-opts="{{[['tap',[['changeBglistDialog',['$event']]]]]}}" catchtap="__e"></image></view><view class="popup__content"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="$orig"><block><view class="clist-item flex-y-center" data-pic="{{item.$orig}}" data-event-opts="{{[['tap',[['bgChange',['$event']]]]]}}" bindtap="__e"><view class="flex1"><image style="width:300rpx;" src="{{item.$orig}}" class="_img"></image></view><view class="radio" style="{{(item.g4==item.$orig?'background:'+item.m0+';border:0':'')}}"><image class="radio-img" src="/static/img/checkd.png"></image></view></view></block></block></view></view></view></block><uni-popup class="vue-ref" vue-id="79a1982c-31" id="dialogDetailtxt" type="dialog" data-ref="dialogDetailtxt" bind:__l="__l" vue-slots="{{['default']}}"><view class="uni-popup-dialog"><view class="uni-dialog-title"><text class="uni-dialog-title-text">请输入文本内容</text></view><view class="uni-dialog-content"><textarea value="" placeholder="请输入文本内容" data-event-opts="{{[['input',[['catcheDetailtxt',['$event']]]]]}}" bindinput="__e"></textarea></view><view class="uni-dialog-button-group"><view data-event-opts="{{[['tap',[['dialogDetailtxtClose',['$event']]]]]}}" class="uni-dialog-button" bindtap="__e"><text class="uni-dialog-button-text">取消</text></view><view data-event-opts="{{[['tap',[['dialogDetailtxtConfirm',['$event']]]]]}}" class="uni-dialog-button uni-border-left" bindtap="__e"><text class="uni-dialog-button-text uni-button-color">确定</text></view></view><view data-event-opts="{{[['tap',[['dialogDetailtxtClose',['$event']]]]]}}" class="uni-popup-dialog__close" bindtap="__e"><label class="uni-popup-dialog__close-icon _span"></label></view></view></uni-popup></block></block><view style="display:none;">{{test}}</view><block wx:if="{{loading}}"><loading vue-id="79a1982c-32" bind:__l="__l"></loading></block><popmsg class="vue-ref" vue-id="79a1982c-33" data-ref="popmsg" bind:__l="__l"></popmsg></view>