(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["zhaopin/workingJob"],{"165bd":function(t,n,e){"use strict";var a=e("8e3f1"),o=e.n(a);o.a},2541:function(t,n,e){"use strict";e.d(n,"b",(function(){return o})),e.d(n,"c",(function(){return r})),e.d(n,"a",(function(){return a}));var a={loading:function(){return e.e("components/loading/loading").then(e.bind(null,"ceaa"))},dpTabbar:function(){return e.e("components/dp-tabbar/dp-tabbar").then(e.bind(null,"b875"))},popmsg:function(){return e.e("components/popmsg/popmsg").then(e.bind(null,"2bf2"))}},o=function(){var t=this.$createElement;this._self._c},r=[]},"4c8b":function(t,n,e){"use strict";e.r(n);var a=e("2541"),o=e("5598");for(var r in o)["default"].indexOf(r)<0&&function(t){e.d(n,t,(function(){return o[t]}))}(r);e("165bd");var i=e("828b"),c=Object(i["a"])(o["default"],a["b"],a["c"],!1,null,null,null,!1,a["a"],void 0);n["default"]=c.exports},"4f35":function(t,n,e){"use strict";(function(t){var a=e("47a9");Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;var o=a(e("7eb4")),r=a(e("ee10")),i=getApp(),c={data:function(){return{opt:{},loading:!1,isload:!1,jobInfo:{companyName:"",companyLogo:"",industry:"",title:"",workingDuration:"",currentSalary:"",startDate:"",location:"",department:"",referrer:{name:"",avatar:"",title:""},referralDate:"",benefits:[]}}},onLoad:function(t){this.opt=i.getopts(t),this.loadJobInfo()},onShareAppMessage:function(){return this._sharewx({title:"我的工作信息",desc:"查看工作详情",pic:""})},onShareTimeline:function(){var t=this._sharewx({title:"我的工作信息",desc:"查看工作详情",pic:""}),n=t.path.split("?")[1];return{title:t.title,imageUrl:t.imageUrl,query:n}},methods:{loadJobInfo:function(){var n=this;return(0,r.default)(o.default.mark((function e(){var a;return o.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return n.loading=!0,e.prev=1,e.next=4,n.mockLoadData();case 4:a=e.sent,n.jobInfo=a.data,n.isload=!0,e.next=12;break;case 9:e.prev=9,e.t0=e["catch"](1),t.showToast({title:"加载失败，请重试",icon:"none"});case 12:return e.prev=12,n.loading=!1,e.finish(12);case 15:case"end":return e.stop()}}),e,null,[[1,9,12,15]])})))()},mockLoadData:function(){return new Promise((function(t){setTimeout((function(){t({data:{companyName:"科技有限公司",companyLogo:"/static/images/company-logo.png",industry:"互联网/软件开发",title:"高级前端工程师",workingDuration:"1年2个月",currentSalary:"25k",startDate:"2023-01-15",location:"上海市浦东新区",department:"技术部-前端组",referrer:{name:"张三",avatar:"/static/images/avatar.png",title:"技术总监"},referralDate:"2022-12-20",benefits:["五险一金","年终奖","带薪年假","餐补","交通补助","节日福利","团建活动","免费零食"]}})}),1e3)}))},contactHR:function(){t.showModal({title:"联系人力资源",content:"是否联系人力资源部门？",success:function(n){n.confirm&&t.makePhoneCall({phoneNumber:"10086",fail:function(){t.showToast({title:"请稍后重试",icon:"none"})}})}})}}};n.default=c}).call(this,e("df3c")["default"])},5598:function(t,n,e){"use strict";e.r(n);var a=e("4f35"),o=e.n(a);for(var r in a)["default"].indexOf(r)<0&&function(t){e.d(n,t,(function(){return a[t]}))}(r);n["default"]=o.a},"8e3f1":function(t,n,e){},acb4:function(t,n,e){"use strict";(function(t,n){var a=e("47a9");e("06e9");a(e("3240"));var o=a(e("4c8b"));t.__webpack_require_UNI_MP_PLUGIN__=e,n(o.default)}).call(this,e("3223")["default"],e("df3c")["createPage"])}},[["acb4","common/runtime","common/vendor"]]]);