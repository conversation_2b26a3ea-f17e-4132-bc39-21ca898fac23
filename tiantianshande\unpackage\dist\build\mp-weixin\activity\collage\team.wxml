<view><block wx:if="{{isload}}"><block><view class="container"><view class="topbg"><image class="image" src="{{pre_url+'/static/img/collage_teambg.png'}}"></image></view><view class="topbox" data-url="{{'product?id='+product.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="left"><image src="{{product.pic}}"></image></view><view class="right"><view class="f1">{{product.name}}</view><view class="f2"><view class="t1">{{product.teamnum+"人团"}}</view></view><view class="f3"><view class="t1">￥</view><view class="t2">{{product.sell_price}}</view><view class="t3">{{product.sales+"人已拼"}}</view></view></view></view><view class="teambox"><view class="userlist"><block wx:for="{{userlist}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="item"><image class="f1" src="{{item.headimg?item.headimg:'/static/img/wh.png'}}"></image><block wx:if="{{item.id==team.mid}}"><text class="f2">团长</text></block></view></block></view><block wx:if="{{team.status==1}}"><view class="join-text"><view>仅剩<text class="join-te1">{{team.teamnum-team.num}}</text>个名额</view><view style="font-size:28rpx;color:#f80;">{{''+rtimeformat+" 后结束"}}</view></view></block><block wx:if="{{team.status==2}}"><view class="join-text">已满员,拼团成功</view></block><block wx:if="{{team.status==3}}"><view class="join-text">拼团失败</view></block><block wx:if="{{team.status==1&&haveme==1}}"><button data-event-opts="{{[['tap',[['shareClick',['$event']]]]]}}" class="join-btn" bindtap="__e">邀请好友参团</button></block><block wx:if="{{team.status==1&&haveme==0}}"><button data-event-opts="{{[['tap',[['buydialogChange',['$event']]]]]}}" class="join-btn" bindtap="__e">我要参团</button></block></view><view hidden="{{buydialogHidden}}"><view class="buydialog-mask"><view class="buydialog"><view data-event-opts="{{[['tap',[['buydialogChange',['$event']]]]]}}" class="close" bindtap="__e"><image class="buydialog-canimg" src="/static/img/close.png"></image></view><view class="title"><image class="img" src="{{guigelist[ks].pic?guigelist[ks].pic:product.pic}}" data-url="{{guigelist[ks].pic?guigelist[ks].pic:product.pic}}" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image><view class="price"><text class="t1">￥</text>{{guigelist[ks].sell_price+''}}<block wx:if="{{guigelist[ks].market_price>guigelist[ks].sell_price}}"><text class="t2">{{"￥"+guigelist[ks].market_price}}</text></block></view><view class="choosename">{{"已选规格: "+guigelist[ks].name}}</view><view class="stock">{{"剩余"+guigelist[ks].stock+"件"}}</view></view><block wx:for="{{guigedata}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="guigelist flex-col"><view class="name">{{item.title}}</view><view class="item flex flex-y-center"><block wx:for="{{item.items}}" wx:for-item="item2" wx:for-index="index2" wx:key="index2"><block><view class="{{['item2 '+(ggselected[item.k]==item2.k?'on':'')]}}" data-itemk="{{item.k}}" data-idx="{{item2.k}}" data-event-opts="{{[['tap',[['ggchange',['$event']]]]]}}" bindtap="__e">{{item2.title}}</view></block></block></view></view></block><view class="buynum flex flex-y-center"><view class="flex1">购买数量：</view><view class="f2 flex flex-y-center"><text data-event-opts="{{[['tap',[['gwcminus',['$event']]]]]}}" class="minus flex-x-center" bindtap="__e">-</text><input class="flex-x-center" type="number" data-event-opts="{{[['input',[['gwcinput',['$event']]]]]}}" value="{{gwcnum}}" bindinput="__e"/><text data-event-opts="{{[['tap',[['gwcplus',['$event']]]]]}}" class="plus flex-x-center" bindtap="__e">+</text></view></view><block><button class="tobuy" data-type="3" data-event-opts="{{[['tap',[['tobuy',['$event']]]]]}}" bindtap="__e">确 定</button></block></view></view></view></view><block wx:if="{{sharetypevisible}}"><view class="popup__container"><view data-event-opts="{{[['tap',[['handleClickMask',['$event']]]]]}}" class="popup__overlay" catchtap="__e"></view><view class="popup__modal" style="height:320rpx;min-height:320rpx;"><view class="popup__content"><view class="sharetypecontent"><block wx:if="{{$root.m0=='app'}}"><view data-event-opts="{{[['tap',[['shareapp',['$event']]]]]}}" class="f1" bindtap="__e"><image class="img" src="/static/img/weixin.png"></image><text class="t1">分享给好友</text></view></block><block wx:else><block wx:if="{{$root.m1=='mp'}}"><view data-event-opts="{{[['tap',[['sharemp',['$event']]]]]}}" class="f1" bindtap="__e"><image class="img" src="/static/img/weixin.png"></image><text class="t1">分享给好友</text></view></block><block wx:else><block wx:if="{{$root.m2=='h5'}}"><view data-event-opts="{{[['tap',[['sharemp',['$event']]]]]}}" class="f1" bindtap="__e"><image class="img" src="/static/img/weixin.png"></image><text class="t1">分享给好友</text></view></block><block wx:else><button class="f1" open-type="share"><image class="img" src="/static/img/weixin.png"></image><text class="t1">分享给好友</text></button></block></block></block><view data-event-opts="{{[['tap',[['showPoster',['$event']]]]]}}" class="f2" bindtap="__e"><image class="img" src="/static/img/sharepic.png"></image><text class="t1">生成分享图片</text></view></view></view></view></view></block><block wx:if="{{showposter}}"><view class="posterDialog"><view class="main"><view data-event-opts="{{[['tap',[['posterDialogClose',['$event']]]]]}}" class="close" bindtap="__e"><image class="img" src="/static/img/close.png"></image></view><view class="content"><image class="img" src="{{posterpic}}" mode="widthFix" data-url="{{posterpic}}" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image></view></view></view></block></block></block><block wx:if="{{loading}}"><loading vue-id="03fb622b-1" bind:__l="__l"></loading></block><dp-tabbar vue-id="03fb622b-2" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="03fb622b-3" data-ref="popmsg" bind:__l="__l"></popmsg></view>