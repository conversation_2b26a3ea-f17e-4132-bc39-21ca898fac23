(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["components/dp-tab/dp-tab"],{"0843":function(t,n,e){"use strict";e.d(n,"b",(function(){return o})),e.d(n,"c",(function(){return a})),e.d(n,"a",(function(){return i}));var i={dpNotice:function(){return e.e("components/dp-notice/dp-notice").then(e.bind(null,"e232"))},dpBanner:function(){return e.e("components/dp-banner/dp-banner").then(e.bind(null,"3d4e"))},dpSearch:function(){return e.e("components/dp-search/dp-search").then(e.bind(null,"c7ac"))},dpText:function(){return e.e("components/dp-text/dp-text").then(e.bind(null,"40f1"))},dpTitle:function(){return e.e("components/dp-title/dp-title").then(e.bind(null,"66f5"))},dpDhlist:function(){return e.e("components/dp-dhlist/dp-dhlist").then(e.bind(null,"d43b"))},dpLine:function(){return e.e("components/dp-line/dp-line").then(e.bind(null,"9aa0"))},dpBlank:function(){return e.e("components/dp-blank/dp-blank").then(e.bind(null,"1ad3"))},dpMenu:function(){return e.e("components/dp-menu/dp-menu").then(e.bind(null,"4c0d"))},dpMap:function(){return e.e("components/dp-map/dp-map").then(e.bind(null,"636a"))},dpCube:function(){return e.e("components/dp-cube/dp-cube").then(e.bind(null,"5df9"))},dpPicture:function(){return e.e("components/dp-picture/dp-picture").then(e.bind(null,"aa81"))},dpPictures:function(){return e.e("components/dp-pictures/dp-pictures").then(e.bind(null,"d2f8"))},dpVideo:function(){return e.e("components/dp-video/dp-video").then(e.bind(null,"5af36"))},dpTab:function(){return Promise.resolve().then(e.bind(null,"fba9"))},dpShop:function(){return e.e("components/dp-shop/dp-shop").then(e.bind(null,"f0f0f"))},dpProduct:function(){return e.e("components/dp-product/dp-product").then(e.bind(null,"705cf"))},dpCollage:function(){return e.e("components/dp-collage/dp-collage").then(e.bind(null,"9392"))},dpLuckycollage:function(){return e.e("components/dp-luckycollage/dp-luckycollage").then(e.bind(null,"de7a"))},dpKanjia:function(){return e.e("components/dp-kanjia/dp-kanjia").then(e.bind(null,"7b75"))},dpYuyue:function(){return e.e("components/dp-yuyue/dp-yuyue").then(e.bind(null,"948e"))},dpSeckill:function(){return e.e("components/dp-seckill/dp-seckill").then(e.bind(null,"874b"))},dpScoreshop:function(){return e.e("components/dp-scoreshop/dp-scoreshop").then(e.bind(null,"3ea4"))},dpTuangou:function(){return e.e("components/dp-tuangou/dp-tuangou").then(e.bind(null,"ec50"))},dpKecheng:function(){return e.e("components/dp-kecheng/dp-kecheng").then(e.bind(null,"db5b"))},dpRestaurantProduct:function(){return e.e("components/dp-restaurant-product/dp-restaurant-product").then(e.bind(null,"2423"))},dpCoupon:function(){return e.e("components/dp-coupon/dp-coupon").then(e.bind(null,"6dd6"))},dpArticle:function(){return e.e("components/dp-article/dp-article").then(e.bind(null,"826a"))},dpBusiness:function(){return e.e("components/dp-business/dp-business").then(e.bind(null,"f711"))},dpShortvideo:function(){return e.e("components/dp-shortvideo/dp-shortvideo").then(e.bind(null,"df60"))},dpDaihuobiji:function(){return e.e("components/dp-daihuobiji/dp-daihuobiji").then(e.bind(null,"017a"))},dpLiveroom:function(){return e.e("components/dp-liveroom/dp-liveroom").then(e.bind(null,"ec74"))},dpButton:function(){return e.e("components/dp-button/dp-button").then(e.bind(null,"9d96"))},dpHotspot:function(){return e.e("components/dp-hotspot/dp-hotspot").then(e.bind(null,"ee23"))},dpCover:function(){return e.e("components/dp-cover/dp-cover").then(e.bind(null,"9d8e"))},dpRichtext:function(){return e.e("components/dp-richtext/dp-richtext").then(e.bind(null,"173c"))},dpForm:function(){return e.e("components/dp-form/dp-form").then(e.bind(null,"a5ade"))},dpFormLog:function(){return e.e("components/dp-form-log/dp-form-log").then(e.bind(null,"5b39"))},dpUserinfo:function(){return Promise.all([e.e("common/vendor"),e.e("components/dp-userinfo/dp-userinfo")]).then(e.bind(null,"1ea8"))},dpWxad:function(){return e.e("components/dp-wxad/dp-wxad").then(e.bind(null,"e7a3"))},dpJidian:function(){return e.e("components/dp-jidian/dp-jidian").then(e.bind(null,"722e"))},dpZhaopin:function(){return e.e("components/dp-zhaopin/dp-zhaopin").then(e.bind(null,"939d"))},dpQiuzhi:function(){return e.e("components/dp-qiuzhi/dp-qiuzhi").then(e.bind(null,"f9cce"))},dpXixie:function(){return e.e("components/dp-xixie/dp-xixie").then(e.bind(null,"8e18"))},loading:function(){return e.e("components/loading/loading").then(e.bind(null,"ceaa"))}},o=function(){var t=this.$createElement;this._self._c},a=[]},1908:function(t,n,e){"use strict";var i=e("fa05"),o=e.n(i);o.a},"28ca":function(t,n,e){"use strict";(function(t){Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;var e=getApp(),i={data:function(){return{loading:!1,loadingTimer:null,fastLoading:!0,tabindex:-1,pagecontent:[],latitude:"",longitude:"",tabtop:0,needfixed:0,windowWidth:375,touchStartX:0,touchStartY:0,touchEndX:0,touchEndY:0,touchMoveX:0,swipeDirection:"",swipeThreshold:50,verticalThreshold:80,isSwiping:!1,showSwipeIndicator:!1,swipeAnimating:!1,currentSwipeOffset:0,maxSwipeOffset:100,tabChangeAnimationDuration:300,contentCache:{},isPreloading:!1,preloadedIndexes:new Set,loadingTimestamp:0,minLoadingTime:500}},props:{params:{},data:{},tabid:{default:""},menuindex:{default:-1},enableSwipe:{type:Boolean,default:!0},swipeAnimationSpeed:{type:Number,default:5},preloadAdjacent:{type:Boolean,default:!0},enableCache:{type:Boolean,default:!0},cacheLifetime:{type:Number,default:3e5}},computed:{hasNextTab:function(){return this.tabindex<this.data.length-1},hasPrevTab:function(){return this.tabindex>0},contentTransform:function(){return this.isSwiping&&!this.swipeAnimating?"translateX(".concat(this.currentSwipeOffset,"px)"):"translateX(0)"},currentTabId:function(){return this.tabindex>=0&&this.data&&this.data[this.tabindex]?this.data[this.tabindex].id:null},cacheKey:function(){return"".concat(this.tabid,"_").concat(this.currentTabId)}},mounted:function(){this.getDeviceInfo(),this.changetab({currentTarget:{dataset:{index:0}}},!0);var n=this;1==this.params.fixedtop&&(setTimeout((function(){var e=t.createSelectorQuery().in(n).select(".dsn-tab-box");e.fields({size:!1,rect:!0,scrollOffset:!1},(function(t){console.log("2023-07-01 14:30:25-INFO-[dp-tab][getDeviceInfo_002] 获取tab位置：",t),n.tabtop=t.top})).exec()}),100),t.$on("onPageScroll",(function(t){n.scrollTop=t.scrollTop,t.scrollTop>0&&t.scrollTop>=n.tabtop?n.needfixed=1:n.needfixed=0}))),t.$on("windowResize",this.getDeviceInfo)},beforeDestroy:function(){t.$off("windowResize",this.getDeviceInfo),t.$off("onPageScroll"),this.loadingTimer&&clearTimeout(this.loadingTimer),this.contentCache=null},methods:{getDeviceInfo:function(){try{var n=t.getSystemInfoSync();console.log("2023-07-01 14:30:25-INFO-[dp-tab][getDeviceInfo_001] 设备信息:",n),this.windowWidth=n.windowWidth,this.swipeThreshold=.15*this.windowWidth,this.maxSwipeOffset=.25*this.windowWidth,this.tabChangeAnimationDuration=500-30*this.swipeAnimationSpeed,this.tabChangeAnimationDuration=Math.min(Math.max(this.tabChangeAnimationDuration,150),500)}catch(e){console.log("2023-07-01 14:30:25-ERROR-[dp-tab][getDeviceInfo_003] 获取设备信息失败:",e)}},touchStart:function(t){!this.enableSwipe||this.swipeAnimating||this.data.length<=1||(this.touchStartX=t.touches[0].clientX,this.touchStartY=t.touches[0].clientY,this.touchMoveX=this.touchStartX,this.isSwiping=!0,this.currentSwipeOffset=0,console.log("2023-07-01 14:30:25-INFO-[dp-tab][touchStart_001] 触摸开始:",{x:this.touchStartX,y:this.touchStartY}))},touchMove:function(t){if(this.enableSwipe&&this.isSwiping&&!this.swipeAnimating&&!(this.data.length<=1)){var n=t.touches[0].clientX,e=t.touches[0].clientY;this.touchMoveX=n;var i=n-this.touchStartX,o=e-this.touchStartY;if(Math.abs(o)>this.verticalThreshold)return this.isSwiping=!1,this.showSwipeIndicator=!1,void(this.currentSwipeOffset=0);i>0?(this.hasPrevTab?(this.currentSwipeOffset=Math.min(i,this.maxSwipeOffset),this.preloadAdjacent&&!this.isPreloading&&i>this.swipeThreshold/2&&this.preloadTabContent(this.tabindex-1)):this.currentSwipeOffset=3*Math.sqrt(i),this.swipeDirection="right"):(this.hasNextTab?(this.currentSwipeOffset=Math.max(i,-this.maxSwipeOffset),this.preloadAdjacent&&!this.isPreloading&&Math.abs(i)>this.swipeThreshold/2&&this.preloadTabContent(this.tabindex+1)):this.currentSwipeOffset=3*-Math.sqrt(Math.abs(i)),this.swipeDirection="left"),Math.abs(i)>this.swipeThreshold/2&&(i>0&&this.hasPrevTab||i<0&&this.hasNextTab)?this.showSwipeIndicator=!0:this.showSwipeIndicator=!1,console.log("2023-07-01 14:30:25-INFO-[dp-tab][touchMove_001] 触摸移动:",{distanceX:i,distanceY:o,direction:this.swipeDirection,offset:this.currentSwipeOffset})}},touchEnd:function(t){if(this.enableSwipe&&this.isSwiping&&!this.swipeAnimating&&!(this.data.length<=1)){this.touchEndX=t.changedTouches?t.changedTouches[0].clientX:this.touchMoveX,this.touchEndY=t.changedTouches?t.changedTouches[0].clientY:0;var n=this.touchEndX-this.touchStartX,e=this.touchEndY-this.touchStartY;console.log("2023-07-01 14:30:25-INFO-[dp-tab][touchEnd_001] 触摸结束:",{distanceX:n,distanceY:e,threshold:this.swipeThreshold}),this.swipeAnimating=!0,Math.abs(e)>this.verticalThreshold?this.animateToOrigin():Math.abs(n)>this.swipeThreshold?n>0&&this.hasPrevTab?this.swipeToTab(this.tabindex-1,"right"):n<0&&this.hasNextTab?this.swipeToTab(this.tabindex+1,"left"):this.animateToOrigin():this.animateToOrigin()}},animateToOrigin:function(){var t=this;this.swipeAnimating=!0,this.currentSwipeOffset=0,setTimeout((function(){t.resetSwipeState()}),300)},swipeToTab:function(t,n){var e=this;this.swipeAnimating=!0,console.log("2023-07-01 14:30:25-INFO-[dp-tab][swipeToTab_001] 滑动切换到标签:",t,"方向:",n),this.currentSwipeOffset="left"===n?-this.windowWidth:this.windowWidth,this.vibrateDevice(),setTimeout((function(){e.changetab({currentTarget:{dataset:{index:t}}},!1),setTimeout((function(){e.resetSwipeState()}),e.tabChangeAnimationDuration)}),50)},vibrateDevice:function(){try{t.vibrateShort&&t.vibrateShort({success:function(){console.log("2023-07-01 14:30:25-INFO-[dp-tab][vibrateDevice_001] 震动反馈成功")},fail:function(t){console.log("2023-07-01 14:30:25-ERROR-[dp-tab][vibrateDevice_002] 震动反馈失败:",t)}})}catch(n){console.log("2023-07-01 14:30:25-ERROR-[dp-tab][vibrateDevice_003] 震动API异常:",n)}},resetSwipeState:function(){this.isSwiping=!1,this.showSwipeIndicator=!1,this.swipeDirection="",this.currentSwipeOffset=0,this.swipeAnimating=!1},changetab:function(n){var e=this,i=!(arguments.length>1&&void 0!==arguments[1])||arguments[1],o=this,a=n.currentTarget.dataset.index;if(o.tabindex!=a){this.loadingTimer&&(clearTimeout(this.loadingTimer),this.loadingTimer=null),o.tabindex=a,console.log("2023-07-01 14:30:25-INFO-[dp-tab][changetab_001] 切换tab:",a,o.data[a].id),this.fastLoading=!i;var d="".concat(o.tabid,"_").concat(o.data[a].id),s=this.getCachedContent(d);s?(console.log("2023-07-01 14:30:25-INFO-[dp-tab][changetab_002] 使用缓存数据:",d),o.pagecontent=s,t.$emit("waterfall"),this.preloadAdjacent&&!this.isPreloading&&setTimeout((function(){e.fetchTabContent(a,!0)}),1e3)):this.getdata(i),this.$nextTick((function(){setTimeout((function(){var n=t.createSelectorQuery().in(e);n.select("#tab-item-"+a).boundingClientRect((function(t){console.log("2023-07-01 14:30:25-INFO-[dp-tab][changetab_003] 当前tab位置:",t)})).exec()}),100)})),this.preloadAdjacent&&this.$nextTick((function(){setTimeout((function(){e.preloadAdjacentTabs(a)}),1e3)}))}},preloadTabContent:function(t){if(!this.isPreloading&&!this.preloadedIndexes.has(t)&&t>=0&&t<this.data.length){this.isPreloading=!0;var n=this.data[t].id,e="".concat(this.tabid,"_").concat(n);this.getCachedContent(e)?(console.log("2023-07-01 14:30:25-INFO-[dp-tab][preloadTabContent_002] 使用缓存内容，无需预加载:",t,n),this.isPreloading=!1):(console.log("2023-07-01 14:30:25-INFO-[dp-tab][preloadTabContent_001] 预加载标签内容:",t,n),this.fetchTabContent(t,!0)),this.preloadedIndexes.add(t)}},preloadAdjacentTabs:function(t){t<this.data.length-1&&this.preloadTabContent(t+1),t>0&&this.preloadTabContent(t-1)},getCachedContent:function(t){if(!this.enableCache)return null;var n=this.contentCache[t];if(!n)return null;var e=Date.now();return e-n.timestamp>this.cacheLifetime?(delete this.contentCache[t],null):n.data},cacheContent:function(t,n){this.enableCache&&(this.contentCache[t]={data:n,timestamp:Date.now()},console.log("2023-07-01 14:30:25-INFO-[dp-tab][cacheContent_001] 缓存内容:",t))},fetchTabContent:function(n){var i=this,o=arguments.length>1&&void 0!==arguments[1]&&arguments[1],a=this.data[n].id,d="".concat(this.tabid,"_").concat(a);console.log("2023-07-01 14:30:25-INFO-[dp-tab][fetchTabContent_001] 获取标签内容:",{index:n,tabid:this.tabid,tabindexid:a,isPreload:o}),e.post("ApiIndex/gettabcontent",{tabid:this.tabid,tabindexid:a,latitude:this.latitude,longitude:this.longitude},(function(e){e&&e.pagecontent&&i.cacheContent(d,e.pagecontent),o&&(i.isPreloading=!1),n!==i.tabindex||o||(i.pagecontent=e.pagecontent,i.loading=!1,t.$emit("waterfall")),""===i.latitude&&""===i.longitude&&e.needlocation&&i.requestLocation()}))},getdata:function(){var t=!(arguments.length>0&&void 0!==arguments[0])||arguments[0],n=this;this.loadingTimestamp=Date.now(),t?this.loadingTimer=setTimeout((function(){n.loading=!0,n.fastLoading=!1}),300):n.loading=!1,console.log("2023-07-01 14:30:25-INFO-[dp-tab][getdata_001] 获取tab内容，参数:",{tabid:n.tabid,tabindexid:n.data[n.tabindex].id,showLoading:t}),e.post("ApiIndex/gettabcontent",{tabid:n.tabid,tabindexid:n.data[n.tabindex].id,latitude:n.latitude,longitude:n.longitude},(function(t){console.log("2023-07-01 14:30:25-INFO-[dp-tab][getdata_002] 获取tab内容结果:",t);var e=Date.now()-n.loadingTimestamp;if(n.loadingTimer&&(clearTimeout(n.loadingTimer),n.loadingTimer=null),e<n.minLoadingTime&&n.loading?setTimeout((function(){n.loading=!1,n.updateContent(t)}),n.minLoadingTime-e):(n.loading=!1,n.updateContent(t)),n.enableCache&&t.pagecontent){var i="".concat(n.tabid,"_").concat(n.data[n.tabindex].id);n.cacheContent(i,t.pagecontent)}}))},updateContent:function(n){this.pagecontent=n.pagecontent,t.$emit("waterfall"),""===this.latitude&&""===this.longitude&&n.needlocation&&this.requestLocation()},requestLocation:function(){var t=this;console.log("2023-07-01 14:30:25-INFO-[dp-tab][requestLocation_001] 需要获取位置"),e.getLocation((function(n){console.log("2023-07-01 14:30:25-INFO-[dp-tab][requestLocation_002] 获取位置成功:",n),t.latitude=n.latitude,t.longitude=n.longitude,t.refreshCurrentTab()}),(function(t){console.log("2023-07-01 14:30:25-WARNING-[dp-tab][requestLocation_003] 获取位置失败:",t)}))},refreshCurrentTab:function(){this.tabindex>=0&&this.fetchTabContent(this.tabindex)},getIndexdata:function(){console.log("2023-07-01 14:30:25-INFO-[dp-tab][getIndexdata_001] 触发父组件获取数据"),this.$emit("getdata")}}};n.default=i}).call(this,e("df3c")["default"])},3510:function(t,n,e){"use strict";e.r(n);var i=e("28ca"),o=e.n(i);for(var a in i)["default"].indexOf(a)<0&&function(t){e.d(n,t,(function(){return i[t]}))}(a);n["default"]=o.a},fa05:function(t,n,e){},fba9:function(t,n,e){"use strict";e.r(n);var i=e("0843"),o=e("3510");for(var a in o)["default"].indexOf(a)<0&&function(t){e.d(n,t,(function(){return o[t]}))}(a);e("1908");var d=e("828b"),s=Object(d["a"])(o["default"],i["b"],i["c"],!1,null,null,null,!1,i["a"],void 0);n["default"]=s.exports}}]);
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'components/dp-tab/dp-tab-create-component',
    {
        'components/dp-tab/dp-tab-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('df3c')['createComponent'](__webpack_require__("fba9"))
        })
    },
    [['components/dp-tab/dp-tab-create-component']]
]);
