require('../common/vendor.js');(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pagesB/coze/index"],{"49a1":function(t,n,o){"use strict";o.r(n);var e=o("b802"),a=o("83d5");for(var u in a)["default"].indexOf(u)<0&&function(t){o.d(n,t,(function(){return a[t]}))}(u);o("4e0a");var i=o("828b"),s=Object(i["a"])(a["default"],e["b"],e["c"],!1,null,null,null,!1,e["a"],void 0);n["default"]=s.exports},"4e0a":function(t,n,o){"use strict";var e=o("e07d"),a=o.n(e);a.a},"66e3":function(t,n,o){"use strict";(function(t,n){var e=o("47a9");o("06e9");e(o("3240"));var a=e(o("49a1"));t.__webpack_require_UNI_MP_PLUGIN__=o,n(a.default)}).call(this,o("3223")["default"],o("df3c")["createPage"])},"6a8a":function(t,n,o){"use strict";(function(t){Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;var o=getApp(),e={data:function(){return{isload:!1,loading:!1,statsData:{}}},onLoad:function(t){this.loaded()},onShow:function(){this.getStats()},methods:{getStats:function(){var t=this;o.post("ApiCoze/getstats",{},(function(n){1===n.code&&(t.statsData=n.data||{})}))},showStats:function(){this.getStats(),this.$refs.statsPopup.open()},closeStats:function(){this.$refs.statsPopup.close()},showHelp:function(){this.$refs.helpPopup.open()},closeHelp:function(){this.$refs.helpPopup.close()},showSettings:function(){t.showToast({title:"设置功能开发中...",icon:"none",duration:2e3})}}};n.default=e}).call(this,o("df3c")["default"])},"83d5":function(t,n,o){"use strict";o.r(n);var e=o("6a8a"),a=o.n(e);for(var u in e)["default"].indexOf(u)<0&&function(t){o.d(n,t,(function(){return e[t]}))}(u);n["default"]=a.a},b802:function(t,n,o){"use strict";o.d(n,"b",(function(){return a})),o.d(n,"c",(function(){return u})),o.d(n,"a",(function(){return e}));var e={uniPopup:function(){return Promise.all([o.e("common/vendor"),o.e("components/uni-popup/uni-popup")]).then(o.bind(null,"ca44a"))},loading:function(){return o.e("components/loading/loading").then(o.bind(null,"ceaa"))}},a=function(){var t=this,n=t.$createElement,o=(t._self._c,t.isload?t.t("color1"):null),e=t.isload?t.t("color1"):null,a=t.isload?t.t("color1"):null,u=t.isload?t.t("color1"):null;t.$mp.data=Object.assign({},{$root:{m0:o,m1:e,m2:a,m3:u}})},u=[]},e07d:function(t,n,o){}},[["66e3","common/runtime","common/vendor"]]]);