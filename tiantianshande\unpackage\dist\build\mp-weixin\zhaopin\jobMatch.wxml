<view class="job-match-page data-v-1ef2ccde"><view class="filter-section data-v-1ef2ccde"><scroll-view class="filter-scroll data-v-1ef2ccde" scroll-x="{{true}}" show-scrollbar="false"><view class="filter-tags data-v-1ef2ccde"><block wx:for="{{$root.l0}}" wx:for-item="tag" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['toggleFilter',[index]]]]]}}" class="tag data-v-1ef2ccde" style="{{'background:'+(tag.$orig.active?tag.m0:'#f5f5f5')+';'+('color:'+(tag.$orig.active?'#ffffff':'#666')+';')}}" bindtap="__e">{{''+tag.$orig.name+''}}</view></block></view></scroll-view></view><view class="job-list data-v-1ef2ccde"><block wx:for="{{$root.l1}}" wx:for-item="job" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['viewJobDetail',['$0'],[[['matchedJobs','',index,'id']]]]]]]}}" class="job-card data-v-1ef2ccde" style="{{'box-shadow:'+('0 2rpx 12rpx rgba('+$root.m1+', 0.05)')+';'}}" bindtap="__e"><view class="job-header data-v-1ef2ccde"><view class="job-info data-v-1ef2ccde"><text class="job-title data-v-1ef2ccde" style="{{'color:'+($root.m2)+';'}}">{{job.$orig.title}}</text></view></view><view class="job-tags data-v-1ef2ccde"><block wx:for="{{job.$orig.tags}}" wx:for-item="tag" wx:for-index="tagIndex" wx:key="tagIndex"><text class="tag data-v-1ef2ccde" style="{{'background:'+('rgba('+job.m3+', 0.1)')+';'}}">{{''+tag+''}}</text></block></view><view class="job-footer data-v-1ef2ccde"><view class="salary data-v-1ef2ccde" style="{{'color:'+($root.m4)+';'}}">{{job.$orig.salary}}</view><view class="location data-v-1ef2ccde">{{job.$orig.location+''}}<block wx:if="{{job.$orig.distance}}"><text class="distance data-v-1ef2ccde">{{"· "+job.$orig.distance}}</text></block></view></view></view></block></view></view>