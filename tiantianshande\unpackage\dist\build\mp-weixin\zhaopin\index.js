(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["zhaopin/index"],{3244:function(e,t,n){"use strict";var o=n("41ce"),i=n.n(o);i.a},"41ce":function(e,t,n){},"474e":function(e,t,n){"use strict";n.r(t);var o=n("a474"),i=n.n(o);for(var a in o)["default"].indexOf(a)<0&&function(e){n.d(t,e,(function(){return o[e]}))}(a);t["default"]=i.a},"518d":function(e,t,n){"use strict";n.d(t,"b",(function(){return i})),n.d(t,"c",(function(){return a})),n.d(t,"a",(function(){return o}));var o={loading:function(){return n.e("components/loading/loading").then(n.bind(null,"ceaa"))},dpTabbar:function(){return n.e("components/dp-tabbar/dp-tabbar").then(n.bind(null,"b875"))},popmsg:function(){return n.e("components/popmsg/popmsg").then(n.bind(null,"2bf2"))}},i=function(){var e=this,t=e.$createElement,n=(e._self._c,e.firstLoaded?e.__map(e.tabList,(function(t,n){var o=e.__get_orig(t),i=Number(e.tabList[e.jobItemType].key),a=0===e.homeList.length&&!e.loading;return{$orig:o,m0:i,g0:a}})):null);e._isMounted||(e.e0=function(t){return e.listFilterVisitor=t.detail}),e.$mp.data=Object.assign({},{$root:{l0:n}})},a=[]},a474:function(e,t,n){"use strict";(function(e){var o=n("47a9");Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i=o(n("34cf")),a=o(n("7ca3")),r=o(n("3b2d")),s=o(n("af34"));function c(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,o)}return n}function l(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?c(Object(n),!0).forEach((function(t){(0,a.default)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):c(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var u=getApp(),d={components:{listFilter:function(){n.e("zhaopin/components/newListFilter/index").then(function(){return resolve(n("e59cd"))}.bind(null,n)).catch(n.oe)},positionGuide:function(){n.e("zhaopin/components/positionGuide/index").then(function(){return resolve(n("b8b8"))}.bind(null,n)).catch(n.oe)},regularItem:function(){n.e("zhaopin/components/regularItem/index").then(function(){return resolve(n("da08"))}.bind(null,n)).catch(n.oe)},tab:function(){n.e("zhaopin/components/tab/index").then(function(){return resolve(n("b7cd"))}.bind(null,n)).catch(n.oe)}},onLoad:function(e){this.opt=u.getopts(e),this.getCategoryList(),this.getTypeList()},onPullDownRefresh:function(){this.pageNum=1,this.getModuleList(this.currentJobType)},onReachBottom:function(){this.loading||this.noMore||(this.pageNum++,this.getModuleList(this.currentJobType))},data:function(){return{opt:{},loading:!1,listFilterVisitor:!1,townName:"茂名",tabList:[],currentJobType:"",loinTotal:0,isSign:!1,pageNum:1,pageSize:20,isEnd:!1,jobItemType:0,prevItemType:0,homeList:[],showFilter:!0,jobHomeList:[],isSearchFixed:!1,isFixed:!1,isLoading:!0,firstLoaded:!0,fixedClass:"padding206",listGroupId:1012,pre_url:getApp().globalData.pre_url,classifications:[],sortRules:[{key:"",value:"默认"},{key:"2",value:"离我近"},{key:"3",value:"最新"}],areas:[{areaId:0,townId:0,areaName:"不限"},{areaId:1806,townId:205,areaName:"茂南区"},{areaId:1809,townId:205,areaName:"高州市"},{areaId:1810,townId:205,areaName:"化州市"},{areaId:1811,townId:205,areaName:"信宜市"},{areaId:2939,townId:205,areaName:"电白区"}],clearingForms:[{key:"99",value:"不限"},{key:"1",value:"日结"},{key:"2",value:"周结"},{key:"3",value:"月结"},{key:"4",value:"完工结算"},{key:"0",value:"其他结算"}],userSex:"",classList:[],searchList:[{name:"服务员",hot:0,sourceType:1},{name:"超市零售",hot:0,sourceType:1},{name:"日结兼职",hot:0,sourceType:2},{name:"日结",hot:0,sourceType:2},{name:"寒假工",hot:0,sourceType:2},{name:"夜班",hot:0,sourceType:2},{name:"配音",hot:0,sourceType:2},{name:"奶茶",hot:0,sourceType:2},{name:"剪辑",hot:0,sourceType:2},{name:"发传单",hot:0,sourceType:2}],showGuide:!1,commandFilterData:{type_id:"",clearingForms:"",areaIds:"",sexRequire:"",sortRules:""},controlPop:"",hostExistsJobIds:"",clearWordChange:!1,collectImage:"",collectContentId:"",collectVisible:!1,newUserTemplate:"",isCollectDialog:!1,universeParam:{},refreshLocation:17,firstPageJobNum:10,waitRenderList:[],noMore:!1,contentHeight:0,jobList:{},loadMoreStatus:{}}},methods:{getCategoryList:function(){var e=this;u.get("apiZhaopin/getCategoryList",{},(function(t){if(1==t.status){var n={key:"0",value:"推荐"};e.tabList=[n].concat((0,s.default)(t.data.map((function(e){return{key:e.id.toString(),value:e.name}})))),e.currentJobType=n.key,e.getModuleList(n.key)}else u.alert(t.msg||"获取分类列表失败")}))},tabChange:function(e){console.log("tabChange triggered:",{index:e,currentIndex:"object"===(0,r.default)(e)?e.detail.index:e,oldJobItemType:this.jobItemType});var t="object"===(0,r.default)(e)?e.detail.index:e;this.jobItemType!==t&&(this.jobItemType=t,this.currentJobType=this.tabList[t].key,this.pageNum=1,this.homeList=[],this.getModuleList(this.currentJobType),this.$forceUpdate())},getModuleList:function(t){var n=this;n.loading=!0,"string"===typeof t&&(t={category_id:t});var o=l(l({page:n.pageNum||1,limit:10},t),{},{keyword:n.searchKeyword||""});console.log("请求参数:",o),u.get("apiZhaopin/getPositionList",o,(function(t){if(n.loading=!1,1==t.status&&t.data.list){var o=t.data.list.map((function(e){var t,n;return{partJobId:e.id,title:e.title,titleSimple:e.title,salary:e.salary,companyName:(null===(t=e.company)||void 0===t?void 0:t.name)||"",logo:(null===(n=e.company)||void 0===n?void 0:n.logo)||"https://qiniu-image.qtshe.com/company_logo_default.png",addressDetail:e.address||"",distance:e.distance?e.distance+"km":"",jobLineType:1,category:1,entryCount:e.views||0,companyType:{key:"1",value:"企业"},labelList:{serviceLabels:[{labelId:61,labelName:"企业认证",labelStyle:'{"id":10,"icon":"","color":"#72AAFA","borderColor":"#72AAFA","background":"#FFFFFF"}'}],descLabels:Object.entries(e.formatted_options||{}).map((function(e){var t=(0,i.default)(e,2),n=(t[0],t[1]);return n.map((function(e){return{labelId:Math.random(),labelName:e,labelStyle:'{"id":6,"icon":"","color":"#FA5555","borderColor":"#FEEEEE","background":"#FEEEEE"}'}}))})).flat()},listStyle:1}}));1===n.pageNum?n.homeList=o:n.homeList=[].concat((0,s.default)(n.homeList),(0,s.default)(o)),n.noMore=o.length<10,n.firstLoaded=!0,console.log("获取职位列表成功:",n.homeList)}else u.alert(t.msg||"获取职位列表失败");e.stopPullDownRefresh()}))},filterChange:function(e){this.commandFilterData=e.detail;var t=l(l({},this.commandFilterData),{},{category_id:this.tabList[this.jobItemType].key});this.pageNum=1,this.getModuleList(t)},btFresh:function(){this.pageNum=1,this.getModuleList(this.tabList[this.jobItemType].key)},locationHandle:function(){this.showGuide=!1},getTypeList:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:-1,t=this;u.get("apiZhaopin/getTypeList",{pid:e},(function(e){if(1==e.status){var n=e.data.map((function(e){return l(l({},e),{},{classificationId:e.id,secondClassifications:(e.children||[]).map((function(e){return l(l({},e),{},{classificationId:e.id,parentId:e.pid})}))})}));t.classifications=[{id:0,pid:0,name:"全部",sort:0,classificationId:0,secondClassifications:[]}].concat((0,s.default)(n))}else u.alert(e.msg||"获取职位类型失败")}))},handleSwiperChange:function(e){var t=e.detail.current;console.log("handleSwiperChange:",{index:t,currentJobItemType:this.jobItemType}),this.jobItemType!==t&&this.tabChange(t)},calculateContentHeight:function(){var t=this,n=e.createSelectorQuery().in(this);n.select(".container").boundingClientRect((function(n){var o=e.getSystemInfoSync().windowHeight;t.contentHeight=o-n.top})).exec()}},mounted:function(){this.calculateContentHeight(),this.tabChange(0)},watch:{jobItemType:function(e){console.log("jobItemType changed:",e),this.$forceUpdate()}}};t.default=d}).call(this,n("df3c")["default"])},b90d:function(e,t,n){"use strict";n.r(t);var o=n("518d"),i=n("474e");for(var a in i)["default"].indexOf(a)<0&&function(e){n.d(t,e,(function(){return i[e]}))}(a);n("3244");var r=n("828b"),s=Object(r["a"])(i["default"],o["b"],o["c"],!1,null,null,null,!1,o["a"],void 0);t["default"]=s.exports},d876:function(e,t,n){"use strict";(function(e,t){var o=n("47a9");n("06e9");o(n("3240"));var i=o(n("b90d"));e.__webpack_require_UNI_MP_PLUGIN__=n,t(i.default)}).call(this,n("3223")["default"],n("df3c")["createPage"])}},[["d876","common/runtime","common/vendor"]]]);