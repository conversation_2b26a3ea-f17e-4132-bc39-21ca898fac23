<view><block wx:if="{{isload}}"><block><view class="topsearch flex-y-center"><view class="f1 flex-y-center"><image class="img" src="{{pre_url+'/static/img/search_ico.png'}}"></image><input placeholder="输入门店名称/手机号搜索" placeholder-style="font-size:24rpx;color:#C2C2C2" data-event-opts="{{[['confirm',[['searchConfirm',['$event']]]],['input',[['searchChange',['$event']]]]]}}" value="{{keyword}}" bindconfirm="__e" bindinput="__e"/></view></view><block wx:if="{{$root.g0}}"><view class="content"><view class="label"><text class="t1">商家列表</text></view><block wx:for="{{datalist}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><view class="item"><view class="f1" data-url="{{'detail?mid='+item.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image src="{{item.logo}}"></image><view class="t2"><view class="x1 flex-y-center">{{''+item.name+''}}<block wx:if="{{item.sex==1}}"><image style="margin-left:10rpx;width:40rpx;height:40rpx;" src="{{pre_url+'/static/img/nan2.png'}}"></image></block><block wx:if="{{item.sex==2}}"><image style="margin-left:10rpx;width:40rpx;height:40rpx;" src="{{pre_url+'/static/img/nv2.png'}}"></image></block></view><block><text class="x2">{{"总额度："+item.sales_quota}}</text><text class="x2">{{"已销售额度："+item.total_sales_quota}}</text><text class="x2">{{"剩余销售额度："+item.syquota}}</text></block></view></view><view class="f2"><view class="btn" data-sales_quota="{{item.sales_quota}}" data-bid="{{item.id}}" data-event-opts="{{[['tap',[['setQuota',['$event']]]]]}}" bindtap="__e">设置额度</view></view></view></block></block></view></block><block wx:if="{{nomore}}"><nomore vue-id="527dc978-1" bind:__l="__l"></nomore></block><block wx:if="{{nodata}}"><nodata vue-id="527dc978-2" bind:__l="__l"></nodata></block><uni-popup class="vue-ref" vue-id="527dc978-3" id="rechargeDialog" type="dialog" data-ref="rechargeDialog" bind:__l="__l" vue-slots="{{['default']}}"><uni-popup-dialog vue-id="{{('527dc978-4')+','+('527dc978-3')}}" mode="input" title="设置额度" value="{{sales_quota}}" placeholder="请输入额度" data-event-opts="{{[['^confirm',[['setConfirm']]]]}}" bind:confirm="__e" bind:__l="__l"></uni-popup-dialog></uni-popup></block></block><popmsg class="vue-ref" vue-id="527dc978-5" data-ref="popmsg" bind:__l="__l"></popmsg><block wx:if="{{loading}}"><loading vue-id="527dc978-6" bind:__l="__l"></loading></block></view>