<view class="animation-area data-v-4a0feea2"><sin-barrage vue-id="4e326b06-1" rows="{{2}}" list="{{giftList}}" bottom="{{welcome?60:0}}" left="{{30}}" opacity="{{0.5}}" msec="{{msec}}" data-ref="sinbarrage" class="data-v-4a0feea2 vue-ref" bind:__l="__l"></sin-barrage><block wx:if="{{welcome}}"><view class="welcome data-v-4a0feea2"><text class="username data-v-4a0feea2">{{msg}}</text></view></block></view>