<view class="dp-menu" style="{{'font-size:'+(params.fontsize*2+'rpx')+';'+('background-color:'+(params.bgcolor)+';')+('margin:'+(params.margin_y*2.2+'rpx '+params.margin_x*2.2+'rpx 0')+';')+('padding:'+(params.padding_y*2.2+'rpx '+params.padding_x*2.2+'rpx')+';')+('border-radius:'+(params.boxradius*2.2+'rpx')+';')}}"><view style="padding-top:16rpx;"><block wx:if="{{params.showtitle==1}}"><view class="menu-title" style="{{'color:'+(params.titlecolor)+';'+('font-size:'+(params.titlesize*2.2+'rpx')+';')}}">{{params.title}}</view></block><block wx:if="{{$root.g0>1}}"><block><swiper style="{{'width:'+('100%')+';'+('height:'+(params.newdata_linenum*(params.iconsize*2.2+params.fontsize*2+50)+'rpx'||'350rpx')+';')+('overflow:'+('hidden')+';')}}" autoplay="{{false}}" indicator-dots="{{false}}" current="{{0}}" data-event-opts="{{[['change',[['bannerchange',['$event']]]]]}}" bindchange="__e"><block wx:for="{{params.newdata}}" wx:for-item="item" wx:for-index="__i0__" wx:key="id"><swiper-item><view class="swiper-item" style="{{'width:'+('100%')+';'+('height:'+(params.newdata_linenum*(params.iconsize*2.2+params.fontsize*2+50)+'rpx'||'350rpx')+';')+('overflow:'+('hidden')+';')}}"><block wx:for="{{item}}" wx:for-item="item2" wx:for-index="__i1__" wx:key="id"><view class="{{['menu-nav'+params.num+' '+(params.showicon==0&&params.showline==1?' showline':'')]}}" data-url="{{item2.hrefurl}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><block wx:if="{{params.showicon==1}}"><image style="{{'border-radius:'+(params.radius/2+'%')+';'+('width:'+(params.iconsize*2.2+'rpx')+';')+('height:'+(params.iconsize*2.2+'rpx')+';')}}" src="{{item2.imgurl}}"></image></block><view class="menu-text" style="{{'color:'+(item2.color)+';'+('height:'+(params.fontheight*2.2+'rpx')+';')+('line-height:'+(params.fontheight*2.2+'rpx')+';')}}">{{item2.text||'按钮文字'}}</view></view></block></view></swiper-item></block></swiper><view class="swiper-pagination" style="justify-content:center;bottom:8px;"><block wx:for="{{params.newdata}}" wx:for-item="item" wx:for-index="index" wx:key="id"><block><block wx:if="{{bannerindex==index}}"><view class="swiper-shape4 swiper-shape4-active" style="background-color:#3db51e;"></view></block><block wx:else><view class="swiper-shape4" style="background-color:#edeef0;"></view></block></block></block></view></block></block><block wx:else><block><view class="swiper-item"><block wx:for="{{data}}" wx:for-item="item" wx:for-index="__i2__" wx:key="id"><view class="{{['menu-nav'+params.num+' '+(params.showicon==0&&params.showline==1?' showline':'')]}}"><block wx:if="{{item.hrefurl=='contact::'}}"><button style="line-height:inherit;" open-type="contact"><block wx:if="{{params.showicon==1}}"><image style="{{'border-radius:'+(params.radius/2+'%')+';'+('width:'+(params.iconsize*2.2+'rpx')+';')+('height:'+(params.iconsize*2.2+'rpx')+';')}}" src="{{item.imgurl}}"></image></block><view class="menu-text" style="{{'color:'+(item.color)+';'+('height:'+(params.fontheight*2.2+'rpx')+';')+('line-height:'+(params.fontheight*2.2+'rpx')+';')}}">{{item.text||'按钮文字'}}</view></button></block><block wx:else><view data-url="{{item.hrefurl}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><block wx:if="{{params.showicon==1}}"><image style="{{'border-radius:'+(params.radius/2+'%')+';'+('width:'+(params.iconsize*2.2+'rpx')+';')+('height:'+(params.iconsize*2.2+'rpx')+';')}}" src="{{item.imgurl}}"></image></block><view class="menu-text" style="{{'color:'+(item.color)+';'+('height:'+(params.fontheight*2.2+'rpx')+';')+('line-height:'+(params.fontheight*2.2+'rpx')+';')}}">{{item.text||'按钮文字'}}</view></view></block></view></block></view></block></block></view></view>