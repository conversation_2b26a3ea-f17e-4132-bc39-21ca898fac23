require('../common/vendor.js');(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pagesExt/cityagent/index"],{"240e":function(t,n,o){"use strict";var e=o("7af1"),a=o.n(e);a.a},"4d1f":function(t,n,o){"use strict";o.r(n);var e=o("5c7a"),a=o("dfc0");for(var i in a)["default"].indexOf(i)<0&&function(t){o.d(n,t,(function(){return a[t]}))}(i);o("240e");var r=o("828b"),c=Object(r["a"])(a["default"],e["b"],e["c"],!1,null,null,null,!1,e["a"],void 0);n["default"]=c.exports},"5c7a":function(t,n,o){"use strict";o.d(n,"b",(function(){return a})),o.d(n,"c",(function(){return i})),o.d(n,"a",(function(){return e}));var e={loading:function(){return o.e("components/loading/loading").then(o.bind(null,"ceaa"))},dpTabbar:function(){return o.e("components/dp-tabbar/dp-tabbar").then(o.bind(null,"b875"))},popmsg:function(){return o.e("components/popmsg/popmsg").then(o.bind(null,"2bf2"))}},a=function(){var t=this,n=t.$createElement,o=(t._self._c,t.isload?t.getColor("color1")||"#4CAF50":null),e=t.isload?t.getColorRgb("color1rgb")||"76,175,80":null,a=t.isload?t.agentinfo&&t.agentinfo.coverage_areas&&t.agentinfo.coverage_areas.length>0:null;t.$mp.data=Object.assign({},{$root:{m0:o,m1:e,g0:a}})},i=[]},"7af1":function(t,n,o){},dfc0:function(t,n,o){"use strict";o.r(n);var e=o("fa52"),a=o.n(e);for(var i in e)["default"].indexOf(i)<0&&function(t){o.d(n,t,(function(){return e[t]}))}(i);n["default"]=a.a},f9ec:function(t,n,o){"use strict";(function(t,n){var e=o("47a9");o("06e9");e(o("3240"));var a=e(o("4d1f"));t.__webpack_require_UNI_MP_PLUGIN__=o,n(a.default)}).call(this,o("3223")["default"],o("df3c")["createPage"])},fa52:function(t,n,o){"use strict";(function(t){Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;var o=getApp(),e={data:function(){return{opt:{},loading:!1,isload:!1,pre_url:o.globalData.pre_url,agentinfo:{},statistics:{}}},onLoad:function(t){this.opt=o.getopts(t),this.getdata()},onPullDownRefresh:function(){this.getdata()},methods:{getColor:function(t){try{return"function"===typeof this.t?this.t(t):null}catch(n){return console.log("获取颜色失败:",n),null}},getColorRgb:function(t){try{return"function"===typeof this.t?this.t(t):null}catch(n){return console.log("获取RGB颜色失败:",n),null}},getdata:function(){var n=this;n.loading=!0,o.get("ApiCityAgent/getAgentInfo",{},(function(e){if(n.loading=!1,t.stopPullDownRefresh(),0==e.status)return o.error(e.msg),void("NOT_AGENT"==e.code&&o.goto("/pagesExt/cityagent/bind"));t.setNavigationBarTitle({title:"代理中心"}),n.agentinfo=e.agentinfo,n.statistics=e.statistics,n.loaded()}))},goto:function(t){var n=t.currentTarget.dataset.url;n&&o.goto("/pagesExt/cityagent/"+n)},towithdraw:function(){o.goto("/pagesExt/cityagent/withdraw")},goWithdraw:function(){o.goto("/pagesExt/cityagent/withdraw")},goConvert:function(){var n=this;!n.agentinfo||!n.agentinfo.money||parseFloat(n.agentinfo.money)<=0?o.error("账户余额不足"):t.showModal({title:"余额转换佣金",content:"是否将账户余额转换为佣金？转换后可提现到微信/支付宝",success:function(t){t.confirm&&n.convertBalance()}})},convertBalance:function(){t.showLoading({title:"转换中..."}),setTimeout((function(){t.hideLoading(),t.showModal({title:"功能提示",content:"余额转换功能正在开发中，敬请期待",showCancel:!1})}),1e3)},loaded:function(){this.isload=!0}}};n.default=e}).call(this,o("df3c")["default"])}},[["f9ec","common/runtime","common/vendor"]]]);