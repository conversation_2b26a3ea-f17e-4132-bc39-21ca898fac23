<view><block wx:if="{{isload}}"><block><view class="container" style="{{('background:url('+pre_url+'/static/img/kanjia-bg.png) no-repeat;background-size:100% auto;')}}"><view class="topcontent"><image class="hongbao" src="{{pre_url+'/static/img/kanjia-hb.png'}}"></image><view class="userinfo"><view class="f1"><image src="{{joinuserinfo.headimg}}"></image></view><view class="f2">{{joinuserinfo.nickname}}</view></view><view class="content"><view class="title">〝我看中这个宝贝, 快帮我砍一刀〞</view><view class="proinfo"><view class="f1"><image src="{{product.pic}}" data-url="{{'product?id='+product.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"></image></view><view class="f2"><view class="t1">{{product.name}}</view><view class="t2">已砍走<text style="color:#FF4C00;">{{product.sales}}</text>件</view><view class="t3">¥<text class="x1">{{product.min_price}}</text><text class="x2">{{"¥"+product.sell_price}}</text></view></view></view><view class="progressinfo"><view class="t0">已砍<text class="x1">{{"￥"+joininfo.yikan_price}}</text>，剩余<text class="x2">{{"￥"+joininfo.now_price}}</text></view><view class="t2"><progress percent="{{cut_percent}}" border-radius="3" activeColor="#FF3143" backgroundColor="#FFD1C9" active="true"></progress></view><view class="t3"><view class="x1">原价：<text style="color:#FF3143;">{{"￥"+product.sell_price}}</text></view><view class="x2">最低砍至：<text style="color:#FF3143;">{{"￥"+product.min_price}}</text></view></view></view><view class="op"><block wx:if="{{mid==joininfo.mid&&joininfo.status==0}}"><button data-event-opts="{{[['tap',[['shareClick',['$event']]]]]}}" class="btn1" style="background:linear-gradient(90deg, #FF3143 0%, #FE6748 100%);" bindtap="__e">召唤好友帮我砍价</button></block><block wx:if="{{mid==joininfo.mid&&joininfo.status==1&&joininfo.isbuy==0}}"><button class="btn1" style="background:#FC4343;width:320rpx;" data-url="{{'buy?joinid='+joininfo.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">前去下单</button></block><block wx:if="{{mid==joininfo.mid&&joininfo.isbuy==1}}"><button class="btn1" style="background:#FC4343;width:320rpx;" data-url="orderlist" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">查看订单</button></block><block wx:if="{{mid!=joininfo.mid&&iskan==0}}"><button data-event-opts="{{[['tap',[['doKan',['$event']]]]]}}" class="btn1" style="background:#FC4343;width:320rpx;" bindtap="__e">帮他砍价</button></block><block wx:if="{{mid!=joininfo.mid&&iskan==1}}"><button class="btn1" style="background:#FC4343;width:320rpx;" data-url="{{'product?id='+product.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">我也要参与</button></block><button class="btn1" data-url="{{'helplist?joinid='+joininfo.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">帮砍记录</button></view><block wx:if="{{mid==joininfo.mid&&joininfo.status==0&&product.directbuy==1&&joininfo.isbuy==0}}"><view class="op" style="margin-top:20rpx;"><button class="btn1" style="background:linear-gradient(90deg,#FE6748  0%, #FF3143 100%);width:560rpx;" data-url="{{'buy?joinid='+joininfo.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">现在就去下单</button></view></block><view class="lefttime"><view class="t1">距活动结束还剩：</view><view class="t2">{{djs}}</view></view></view></view></view><block wx:if="{{sharetypevisible}}"><view class="popup__container"><view data-event-opts="{{[['tap',[['handleClickMask',['$event']]]]]}}" class="popup__overlay" catchtap="__e"></view><view class="popup__modal" style="height:320rpx;min-height:320rpx;"><view class="popup__content"><view class="sharetypecontent"><block wx:if="{{$root.m0=='app'}}"><view data-event-opts="{{[['tap',[['shareapp',['$event']]]]]}}" class="f1" bindtap="__e"><image class="img" src="/static/img/weixin.png"></image><text class="t1">分享给好友</text></view></block><block wx:else><block wx:if="{{$root.m1=='mp'}}"><view data-event-opts="{{[['tap',[['sharemp',['$event']]]]]}}" class="f1" bindtap="__e"><image class="img" src="/static/img/weixin.png"></image><text class="t1">分享给好友</text></view></block><block wx:else><block wx:if="{{$root.m2=='h5'}}"><view data-event-opts="{{[['tap',[['sharemp',['$event']]]]]}}" class="f1" bindtap="__e"><image class="img" src="/static/img/weixin.png"></image><text class="t1">分享给好友</text></view></block><block wx:else><button class="f1" open-type="share"><image class="img" src="/static/img/weixin.png"></image><text class="t1">分享给好友</text></button></block></block></block><view data-event-opts="{{[['tap',[['showPoster',['$event']]]]]}}" class="f2" bindtap="__e"><image class="img" src="/static/img/sharepic.png"></image><text class="t1">生成分享图片</text></view></view></view></view></view></block><block wx:if="{{showposter}}"><view class="posterDialog"><view class="main"><view data-event-opts="{{[['tap',[['posterDialogClose',['$event']]]]]}}" class="close" bindtap="__e"><image src="/static/img/close.png"></image></view><view class="content"><image src="{{posterpic}}" mode="widthFix" data-url="{{posterpic}}" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image></view></view></view></block><block wx:if="{{showkanjia}}"><view class="kanjiaDialog"><view class="main"><view class="content"><view class="bargainIco"><image class="bargainIcoImg" src="{{pre_url+'/static/img/bargainbg.png'}}"></image></view><block wx:if="{{joininfo.mid==helpinfo.mid}}"><block><view class="bargainPrice">{{"砍掉"+helpinfo.cut_price+"元"}}</view><text class="bargainText">您自己砍了第一刀</text></block></block><block wx:else><block><view class="bargainPrice">{{"帮好友砍掉"+helpinfo.cut_price+"元"}}</view><block wx:if="{{helpinfo.givescore>0}}"><text class="bargainText">{{"奖励您"+helpinfo.givescore+$root.m3}}<block wx:if="{{product.helpgive_ff==1}}"><text>，好友买单后发放</text></block></text></block><block wx:if="{{helpinfo.givemoney>0}}"><text class="bargainText">{{"奖励您"+helpinfo.givemoney+$root.m4}}<block wx:if="{{product.helpgive_ff==1}}"><text>，好友买单后发放</text></block></text></block></block></block><form reportSubmit="true" data-event-opts="{{[['submit',[['hideKanjiaDialog',['$event']]]]]}}" bindsubmit="__e"><button class="bargainBtn SysBtn" form-type="submit" type="default">确定</button></form></view></view></view></block></block></block><block wx:if="{{loading}}"><loading vue-id="e37cc8e6-1" bind:__l="__l"></loading></block><dp-tabbar vue-id="e37cc8e6-2" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="e37cc8e6-3" data-ref="popmsg" bind:__l="__l"></popmsg></view>