<view class="waterfalls-box data-v-5c4a1074" style="{{'height:'+(height+'px')+';'}}"><block wx:for="{{list}}" wx:for-item="item" wx:for-index="index" wx:key="idKey"><view class="waterfalls-list data-v-5c4a1074 vue-ref-in-for" style="{{'--offset:'+(offset+'px')+';'+('--cols:'+(cols)+';')+('top:'+(allPositionArr[index].top||0)+';')+('left:'+(allPositionArr[index].left||0)+';')}}" id="{{'waterfalls-list-id-'+item[idKey]}}" data-ref="{{'waterfalls-list-id-'+item[idKey]}}" data-event-opts="{{[['tap',[['$emit',['wapper-lick','$0'],[[['list',''+idKey+'',item[idKey]]]]]]]]}}" bindtap="__e"><image class="{{['waterfalls-list-image','data-v-5c4a1074',(single)?'single':'']}}" style="{{(imageStyle)}}" mode="widthFix" src="{{item[imageSrcKey]||' '}}" data-event-opts="{{[['load',[['imageLoadHandle',[index]]]],['error',[['imageLoadHandle',[index]]]],['tap',[['$emit',['image-click','$0'],[[['list',''+idKey+'',item[idKey]]]]]]]]}}" bindload="__e" binderror="__e" bindtap="__e"></image><slot name="slot{{index}}"></slot></view></block></view>