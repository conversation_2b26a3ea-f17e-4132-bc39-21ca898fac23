<view><block wx:if="{{isload}}"><block><form data-event-opts="{{[['submit',[['subform',['$event']]]]]}}" bindsubmit="__e"><view class="form-box"><view class="form-item"><view class="f1">会员昵称<text style="color:red;">*</text></view><view class="f2"><input type="text" name="nickname" placeholder="请填写会员昵称" placeholder-style="color:#888" value="{{info.nickname}}"/></view></view><view class="form-item"><view class="f1">手机号<text style="color:red;">*</text></view><view class="f2"><input type="Number" name="tel" placeholder="请填写会员手机号" placeholder-style="color:#888" value="{{info.tel}}"/></view></view><view class="form-item"><view class="f1">会员等级</view><view class="f2"><picker class="picker-class" value="{{payTypeIndex}}" range="{{payTypeArr}}" data-event-opts="{{[['change',[['bindPickerChange',['$event']]]]]}}" bindchange="__e"><view class="uni-input">{{payTypeArr[payTypeIndex]}}</view></picker><image style="width:30rpx;height:30rpx;" src="{{pre_url+'/static/img/arrowright.png'}}"></image></view></view><view class="form-item"><view class="f1">推荐人ID</view><view class="f2"><input type="text" name="pid" placeholder="请填写推荐人ID" placeholder-style="color:#888" value="{{info.pid}}"/></view></view><view class="form-item"><view class="f1">密码</view><view class="f2"><input type="text" name="pwd" placeholder="请填写密码" placeholder-style="color:#888" value="{{info.pwd}}"/></view></view><view class="form-item"><view class="f1">确认密码</view><view class="f2"><input type="text" name="repwd" placeholder="请确认密码" placeholder-style="color:#888" value="{{info.repwd}}"/></view></view></view><view class="form-box"><view class="form-item flex-col" style="border-bottom:0;"><view class="f1">头像</view><view class="f2"><block wx:for="{{pic}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="layui-imgbox"><view class="layui-imgbox-close" data-index="{{index}}" data-field="pic" data-event-opts="{{[['tap',[['removeimg',['$event']]]]]}}" bindtap="__e"><image style="display:block;" src="{{pre_url+'/static/img/ico-del.png'}}"></image></view><view class="layui-imgbox-img"><image src="{{item}}" data-url="{{item}}" mode="widthFix" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image></view></view></block><block wx:if="{{$root.g0==0}}"><view class="uploadbtn" style="{{('background:url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 60rpx;background-size:80rpx 80rpx;background-color:#F3F3F3;')}}" data-field="pic" data-pernum="1" data-event-opts="{{[['tap',[['uploadimg',['$event']]]]]}}" bindtap="__e"></view></block></view><input type="text" hidden="true" name="pic" maxlength="-1" value="{{$root.g1}}"/></view></view><button class="savebtn" style="{{('background:linear-gradient(90deg,'+$root.m0+' 0%,rgba('+$root.m1+',0.8) 100%)')}}" form-type="submit">提交</button><view style="height:50rpx;"></view></form></block></block><block wx:if="{{loading}}"><loading vue-id="2198661b-1" bind:__l="__l"></loading></block><popmsg class="vue-ref" vue-id="2198661b-2" data-ref="popmsg" bind:__l="__l"></popmsg><wxxieyi vue-id="2198661b-3" bind:__l="__l"></wxxieyi></view>