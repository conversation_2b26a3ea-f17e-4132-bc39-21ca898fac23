<view class="container"><block><view class="view-show"><view class="search-container"><view class="search-box"><image class="img" src="/static/img/search_ico.png"></image><input type="text" placeholder="输入专业名称搜索" data-event-opts="{{[['confirm',[['_request',['$event']]]],['input',[['__set_model',['','keyword','$event',[]]]]]]}}" value="{{keyword}}" bindconfirm="__e" bindinput="__e"/></view></view><view class="content" style="height:calc(100vh - 94rpx);overflow:hidden;display:flex;flex-direction:row !important;"><scroll-view class="{{['nav_left',menuindex>-1?'tabbarbot':'notabbarbot']}}" scrollWithAnimation="{{animation}}" scroll-y="true"><block wx:for="{{data}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><view class="{{['nav_left_items',index===currentActiveIndex?'active':'']}}" data-root-item-id="{{item.id}}" data-root-item-index="{{index}}" data-event-opts="{{[['tap',[['clickRootItem',['$0'],[[['data','',index]]]]]]]}}" bindtap="__e"><view class="before" style="{{'background:'+($root.m0)+';'}}"></view>{{item.zhuanye_name+''}}</view></block></block></scroll-view><view class="nav_right"><view class="nav_right-content"><scroll-view class="{{['detail-list',menuindex>-1?'tabbarbot':'notabbarbot']}}" scrollIntoView="{{scrollToViewId}}" scrollWithAnimation="{{animation}}" scroll-y="true" data-event-opts="{{[['scroll',[['scroll',['$event']]]]]}}" bindscroll="__e"><block wx:for="{{details}}" wx:for-item="detail" wx:for-index="index" wx:key="index"><view class="classification-detail-item" data-url="{{'/pagesExa/daxuepage/specialityDetails?id='+detail.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e"><view class="head" data-id="{{detail.id}}" id="{{'detail-'+detail.id}}"><view class="txt" style="margin-bottom:30rpx;">{{detail.title}}</view><view style="display:flex;justify-content:space-between;color:#bbbbbb;padding-bottom:30rpx;border-bottom:2rpx solid #bbbbbb;"><view>{{'学制：'+detail.study_duration+''}}</view><view>{{'层次：'+detail.education_level+''}}</view></view></view></view></block><block wx:if="{{nodata}}"><nodata vue-id="30c6bea3-1" bind:__l="__l"></nodata></block></scroll-view></view></view></view></view></block></view>