<view><block wx:if="{{isload}}"><block><form data-event-opts="{{[['submit',[['subform',['$event']]]]]}}" bindsubmit="__e"><view class="form-box"><block wx:if="{{set.activity_time_custom}}"><block><view class="form-item"><view>活动时间设置</view><view><radio-group class="radio-group" name="activity_time_status" data-event-opts="{{[['change',[['',['$event']]]]]}}" bindchange="__e"><label><radio value="0" checked="{{!info||info.activity_time_status==0?true:false}}"></radio>关闭</label><label><radio value="1" checked="{{info.activity_time_status==1?true:false}}"></radio>开启</label></radio-group></view></view><view class="form-item"><view class="f1">活动时间</view><view class="f2"><picker class="picker" mode="date" name="activity_time_start" value="{{info.activity_time_start}}" data-event-opts="{{[['change',[['activityTimeStartChange',['$event']]]]]}}" bindchange="__e"><block wx:if="{{info.activity_time_start}}"><view>{{info.activity_time_start}}</view></block><block wx:else><view>请选择</view></block></picker><view style="padding:0 20rpx;">~</view><picker class="picker" mode="date" name="activity_time_end" value="{{info.activity_time_end}}" data-event-opts="{{[['change',[['activityTimeEndChange',['$event']]]]]}}" bindchange="__e"><block wx:if="{{info.activity_time_end}}"><view>{{info.activity_time_end}}</view></block><block wx:else><view>请选择</view></block></picker></view></view></block></block></view><button class="savebtn" style="{{('background:linear-gradient(90deg,'+$root.m0+' 0%,rgba('+$root.m1+',0.8) 100%)')}}" form-type="submit">提交</button><view style="height:50rpx;"></view></form></block></block><block wx:if="{{loading}}"><loading vue-id="514646b0-1" bind:__l="__l"></loading></block></view>