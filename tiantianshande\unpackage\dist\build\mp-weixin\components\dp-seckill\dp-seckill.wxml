<view class="dp-seckill" style="{{'background-color:'+(params.bgcolor)+';'+('margin:'+(params.margin_y*2.2+'rpx '+params.margin_x*2.2+'rpx 0')+';')+('padding:'+(params.padding_y*2.2+'rpx '+params.padding_x*2.2+'rpx')+';')}}"><block wx:if="{{params.shopstyle=='2'}}"><view><block wx:if="{{params.showtitle=='1'}}"><view><block wx:if="{{params.titlestyle==1}}"><view class="dp-time flex-y-center"><image class="dp-time-back" mode="widthFix" src="{{pre_url+'/static/imgsrc/decoration_crush.png'}}" alt></image><view class="dp-time-module flex flex-bt flex-y-center"><text class="dp-time-title">限时秒杀</text><view class="dp-time-content"><block wx:if="{{data[0].seckill_status==0}}"><text>距开抢</text></block><block wx:if="{{data[0].seckill_status==1}}"><text>还剩余</text></block><block wx:if="{{data[0].seckill_status==2}}"><text>活动已结束</text></block><block wx:if="{{data[0].seckill_status!=2}}"><uni-countdown vue-id="77a90e82-1" show-day="{{false}}" color="#fd4a46" background-color="#fff" hour="{{data[0].hour}}" minute="{{data[0].minute}}" second="{{data[0].second}}" splitorColor="#fff" bind:__l="__l"></uni-countdown></block></view></view></view></block><block wx:if="{{params.titlestyle==2}}"><view class="dp-bTime flex-y-center"><image class="dp-bTime-back" mode="widthFix" src="{{pre_url+'/static/imgsrc/decoration_crush.png'}}" alt></image><view class="dp-bTime-module flex flex-bt flex-y-center"><text class="dp-bTime-title">限时秒杀</text><view class="dp-bTime-content"><block wx:if="{{data[0].seckill_status==0}}"><text>距开抢</text></block><block wx:if="{{data[0].seckill_status==1}}"><text>还剩余</text></block><block wx:if="{{data[0].seckill_status==2}}"><text>活动已结束</text></block><block wx:if="{{data[0].seckill_status!=2}}"><uni-countdown vue-id="77a90e82-2" show-day="{{false}}" color="#fff" background-color="#000" hour="{{data[0].hour}}" minute="{{data[0].minute}}" second="{{data[0].second}}" splitorColor="#999ca7" bind:__l="__l"></uni-countdown></block></view></view></view></block></view></block></view></block><block wx:if="{{!params.shopstyle||params.shopstyle==1}}"><view><block wx:if="{{params.style=='1'||params.style=='2'||params.style=='3'}}"><view class="dp-seckill-item"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="id"><view class="item" style="{{(params.style==2?'width:49%;margin-right:'+(index%2==0?'2%':0):params.style==3?'width:32%;margin-right:'+(index%3!=2?'2%':0):'width:100%')}}" data-url="{{'/activity/seckill/product?id='+item.$orig.proid}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="product-pic"><image class="image" src="{{item.$orig.pic}}" mode="widthFix"></image><block wx:if="{{params.saleimg!=''}}"><image class="saleimg" src="{{params.saleimg}}" mode="widthFix"></image></block></view><view class="product-info"><block wx:if="{{params.showname==1}}"><view class="p1">{{item.$orig.name}}</view></block><view class="p2"><block wx:if="{{params.showprice!='0'}}"><view class="p2-1"><text class="t1" style="{{'color:'+(item.m0)+';'}}"><text style="font-size:24rpx;">￥</text>{{item.$orig.sell_price}}</text><block wx:if="{{params.showprice=='1'}}"><text class="t2">{{"￥"+item.$orig.market_price}}</text></block></view></block></view><block wx:if="{{params.showtime==1&&params.style!='3'}}"><view style="color:#333;font-size:24rpx;"><block wx:if="{{item.$orig.seckill_status==2}}"><view>活动已结束</view></block><block wx:if="{{item.$orig.seckill_status==1}}"><view class="flex-row"><view class="h24">还剩余</view><view class="flex1"></view><uni-countdown vue-id="{{'77a90e82-3-'+index}}" show-day="{{false}}" color="#FFFFFF" background-color="#fd4a46" hour="{{item.$orig.hour}}" minute="{{item.$orig.minute}}" second="{{item.$orig.second}}" splitorColor="#333" bind:__l="__l"></uni-countdown></view></block><block wx:if="{{item.$orig.seckill_status==0}}"><view class="flex-row"><view class="h24">距开抢</view><view class="flex1"></view><uni-countdown vue-id="{{'77a90e82-4-'+index}}" show-day="{{false}}" color="#FFFFFF" background-color="#fd4a46" hour="{{item.$orig.hour}}" minute="{{item.$orig.minute}}" second="{{item.$orig.second}}" splitorColor="#333" bind:__l="__l"></uni-countdown></view></block></view></block><view class="p3"><view class="p3-1" style="{{'background:'+('rgba('+item.m1+',0.12)')+';'+('color:'+(item.m2)+';')}}">秒杀</view><block wx:if="{{params.showsales=='1'&&item.$orig.sales>0}}"><view class="p3-2"><text style="overflow:hidden;">{{"已抢购"+item.$orig.sales+"件"}}</text></view></block></view></view></view></block></view></block><block wx:if="{{params.style=='list'}}"><view class="dp-seckill-itemlist"><block wx:for="{{$root.l1}}" wx:for-item="item" wx:for-index="index" wx:key="id"><view class="item" data-url="{{'/activity/seckill/product?id='+item.$orig.proid}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="product-pic"><image class="image" src="{{item.$orig.pic}}" mode="widthFix"></image><block wx:if="{{params.saleimg!=''}}"><image class="saleimg" src="{{params.saleimg}}" mode="widthFix"></image></block></view><view class="product-info"><block wx:if="{{params.showname==1}}"><view class="p1">{{item.$orig.name}}</view></block><block wx:if="{{params.showprice!='0'}}"><view class="p2"><text class="t1" style="{{'color:'+(item.m3)+';'}}"><text style="font-size:24rpx;">￥</text>{{item.$orig.sell_price}}</text><block wx:if="{{params.showprice=='1'}}"><text class="t2">{{"￥"+item.$orig.market_price}}</text></block></view></block><block wx:if="{{params.showtime==1}}"><view style="color:#333;font-size:24rpx;"><block wx:if="{{item.$orig.seckill_status==2}}"><view>活动已结束</view></block><block wx:if="{{item.$orig.seckill_status==1}}"><view class="flex-row"><view class="h24">距活动结束</view><view class="flex1"></view><block wx:if="{{item.$orig.day>0}}"><uni-countdown vue-id="{{'77a90e82-5-'+index}}" show-day="{{true}}" color="#FFFFFF" background-color="#fd4a46" day="{{item.$orig.day}}" hour="{{item.$orig.day_hour}}" minute="{{item.$orig.minute}}" second="{{item.$orig.second}}" splitorColor="#333" bind:__l="__l"></uni-countdown></block><block wx:else><uni-countdown vue-id="{{'77a90e82-6-'+index}}" show-day="{{false}}" color="#FFFFFF" background-color="#fd4a46" day="{{item.$orig.day}}" hour="{{item.$orig.hour}}" minute="{{item.$orig.minute}}" second="{{item.$orig.second}}" bind:__l="__l"></uni-countdown></block></view></block><block wx:if="{{item.$orig.seckill_status==0}}"><view class="flex-row"><view class="h24">距活动开始</view><view class="flex1"></view><block wx:if="{{item.$orig.day>0}}"><uni-countdown vue-id="{{'77a90e82-7-'+index}}" show-day="{{true}}" color="#FFFFFF" background-color="#fd4a46" day="{{item.$orig.day}}" hour="{{item.$orig.day_hour}}" minute="{{item.$orig.minute}}" second="{{item.$orig.second}}" splitorColor="#333" bind:__l="__l"></uni-countdown></block><block wx:else><uni-countdown vue-id="{{'77a90e82-8-'+index}}" show-day="{{false}}" color="#FFFFFF" background-color="#fd4a46" day="{{item.$orig.day}}" hour="{{item.$orig.hour}}" minute="{{item.$orig.minute}}" second="{{item.$orig.second}}" bind:__l="__l"></uni-countdown></block></view></block></view></block><view class="p3"><view class="p3-1" style="{{'background:'+('rgba('+item.m4+',0.12)')+';'+('color:'+(item.m5)+';')}}">秒杀</view><block wx:if="{{params.showsales=='1'&&item.$orig.sales>0}}"><view class="p3-2"><text style="overflow:hidden;">{{"已抢购"+item.$orig.sales+"件"}}</text></view></block></view></view></view></block></view></block><block wx:if="{{params.style=='line'}}"><view class="dp-seckill-itemline"><block wx:for="{{$root.l2}}" wx:for-item="item" wx:for-index="index" wx:key="id"><view class="item" data-url="{{'/activity/seckill/product?id='+item.$orig.proid}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="product-pic"><image class="image" src="{{item.$orig.pic}}" mode="widthFix"></image><block wx:if="{{params.saleimg!=''}}"><image class="saleimg" src="{{params.saleimg}}" mode="widthFix"></image></block></view><view class="product-info"><block wx:if="{{params.showname==1}}"><view class="p1">{{item.$orig.name}}</view></block><view class="p2"><block wx:if="{{params.showprice!='0'}}"><view class="p2-1"><text class="t1" style="{{'color:'+(item.m6)+';'}}"><text style="font-size:24rpx;">￥</text>{{item.$orig.sell_price}}</text><block wx:if="{{params.showprice=='1'}}"><text class="t2">{{"￥"+item.$orig.market_price}}</text></block></view></block></view><view class="p3"><view class="p3-1" style="{{'background:'+('rgba('+item.m7+',0.12)')+';'+('color:'+(item.m8)+';')}}">秒杀</view><block wx:if="{{params.showsales=='1'&&item.$orig.sales>0}}"><view class="p3-2"><text style="overflow:hidden;">{{"已抢购"+item.$orig.sales+"件"}}</text></view></block></view></view></view></block></view></block></view></block><block wx:if="{{params.shopstyle==2}}"><view><block wx:if="{{params.style=='2'}}"><view class="dp-seckill-item" style="overflow:visible;"><block wx:for="{{$root.l3}}" wx:for-item="item" wx:for-index="index" wx:key="id"><view class="item" style="{{'overflow:visible;'+(params.style==2?'width:49%;margin-right:'+(index%2==0?'2%':0):params.style==3?'width:32%;margin-right:'+(index%3!=2?'2%':0):'width:100%')}}" data-url="{{'/activity/seckill/product?id='+item.$orig.proid}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="product-pic"><image class="image" src="{{item.$orig.pic}}" mode="widthFix"></image><block wx:if="{{params.saleimg!=''}}"><image class="saleimg" src="{{params.saleimg}}" mode="widthFix"></image></block></view><view class="product-info"><block wx:if="{{params.showname==1}}"><view class="p1">{{item.$orig.name}}</view></block><view class="rate flex-y-center flex-bt"><view class="rate_module"><view class="rate_item" style="{{'width:'+(item.$orig.sales/item.$orig.stock*100+'%')+';'}}"><image src="{{pre_url+'/static/imgsrc/decoration_tag.png'}}"></image></view></view><text>{{"仅剩"+(item.$orig.stock-item.$orig.sales)+"件"}}</text></view><view class="cost">原价：<text>{{"￥"+item.$orig.market_price}}</text></view><view class="price flex-y-center flex-bt"><text style="{{'color:'+(item.m9)+';'}}">{{"￥"+item.$orig.sell_price}}</text><view class="flex-xy-center" style="{{'background:'+('rgba('+item.m10+',1)')+';'}}"><image src="{{pre_url+'/static/imgsrc/decoration_add.png'}}"></image></view></view></view></view></block></view></block><block wx:if="{{params.style=='list'}}"><view class="dp-seckill-itemlist1"><block wx:for="{{$root.l4}}" wx:for-item="item" wx:for-index="index" wx:key="id"><view class="item" data-url="{{'/activity/seckill/product?id='+item.$orig.proid}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="product-pic"><image class="image" src="{{item.$orig.pic}}" mode="widthFix"></image><block wx:if="{{params.saleimg!=''}}"><image class="saleimg" src="{{params.saleimg}}" mode="widthFix"></image></block></view><view class="product-info"><block wx:if="{{params.showname==1}}"><view class="p1">{{item.$orig.name}}</view></block><view class="rate flex-y-center flex-bt"><view class="rate_module"><view class="rate_item" style="{{'width:'+(item.$orig.sales/item.$orig.stock*100+'%')+';'}}"><image src="{{pre_url+'/static/imgsrc/decoration_tag.png'}}"></image></view></view><text>{{"仅剩"+(item.$orig.stock-item.$orig.sales)+"件"}}</text></view><view class="cost">原价：<text>{{"￥"+item.$orig.market_price}}</text></view><view class="price flex-y-center flex-bt"><text style="{{'color:'+(item.m11)+';'}}">{{"￥"+item.$orig.sell_price}}</text><view class="flex-xy-center" style="{{'background:'+('rgba('+item.m12+',1)')+';'}}">马上抢</view></view></view></view></block></view></block><block wx:if="{{params.style=='line'}}"><view class="dp-seckill-itemline"><block wx:for="{{$root.l5}}" wx:for-item="item" wx:for-index="index" wx:key="id"><view class="item" data-url="{{'/activity/seckill/product?id='+item.$orig.proid}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="product-pic"><image class="image" src="{{item.$orig.pic}}" mode="widthFix"></image><block wx:if="{{params.saleimg!=''}}"><image class="saleimg" src="{{params.saleimg}}" mode="widthFix"></image></block><view class="tag" style="{{'background:'+('rgba('+item.m13+',1)')+';'}}">秒杀</view></view><view class="product-info"><block wx:if="{{params.showname==1}}"><view class="p1">{{item.$orig.name}}</view></block><view class="flex"><view class="tag" style="{{'background:'+('rgba('+item.m14+',0.12)')+';'+('color:'+(item.m15)+';')}}">{{"剩余"+(item.$orig.stock-item.$orig.sales)+"件"}}</view></view><view class="price flex-y-center flex-bt"><text style="{{'color:'+(item.m16)+';'}}">{{"￥"+item.$orig.sell_price}}</text><view class="flex-xy-center" style="{{'background:'+('rgba('+item.m17+',1)')+';'}}"><image src="{{pre_url+'/static/imgsrc/decoration_add.png'}}"></image></view></view></view></view></block></view></block></view></block></view>