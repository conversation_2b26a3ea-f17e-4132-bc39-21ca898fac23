<view class="container"><block wx:if="{{isload}}"><block><form data-event-opts="{{[['submit',[['topay',['$event']]]]]}}" bindsubmit="__e"><block wx:if="{{sindex==1}}"><view class="address-add"><view class="linkitem"><text class="f1">联 系 人：</text><input class="input" type="text" placeholder="请输入您的姓名" placeholder-style="color:#626262;font-size:28rpx;" data-event-opts="{{[['input',[['inputLinkman',['$event']]]]]}}" value="{{linkman}}" bindinput="__e"/></view><view class="linkitem"><text class="f1">联系电话：</text><input class="input" type="text" placeholder="请输入您的手机号" placeholder-style="color:#626262;font-size:28rpx;" data-event-opts="{{[['input',[['inputTel',['$event']]]]]}}" value="{{tel}}" bindinput="__e"/></view></view></block><block wx:else><view class="address-add flex-y-center" data-url="{{'/pages/address/'+(address.id?'address':'addressadd')+'?fromPage=buy&type=1'}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="f1"><image class="img" src="{{pre_url+'/static/img/address.png'}}"></image></view><block wx:if="{{address.id}}"><view class="f2 flex1"><view style="font-weight:bold;color:#111111;font-size:30rpx;">{{address.name+" "+address.tel+''}}<block wx:if="{{address.company}}"><text>{{address.company}}</text></block></view><view style="font-size:24rpx;">{{address.area+" "+address.address}}</view></view></block><block wx:else><view class="f2 flex1">请选择您的地点</view></block><image class="f3" src="{{pre_url+'/static/img/arrowright.png'}}"></image></view></block><block wx:for="{{$root.l0}}" wx:for-item="buydata" wx:for-index="index" wx:key="index"><view class="buydata"><view class="bcontent"><view class="btitle">服务信息</view><view class="product"><block wx:for="{{buydata.$orig.prodata}}" wx:for-item="item" wx:for-index="index2" wx:key="index2"><view class="item flex"><view class="img" data-url="{{'product?id='+item.product.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><block wx:if="{{item.guige.pic}}"><image src="{{item.guige.pic}}"></image></block><block wx:else><image src="{{item.product.pic}}"></image></block></view><view class="info flex1"><view class="f1">{{item.product.name}}</view><view class="f2">{{item.guige.name}}</view><view class="f3"><text style="font-weight:bold;">{{"￥"+item.guige.sell_price}}</text><text style="padding-left:20rpx;">{{'× '+item.num}}</text></view></view></view></block></view><view class="body_item"><view class="body_title flex flex-bt">服务方式<text class="body_text">请选择服务方式</text></view><view class="body_content"><block wx:for="{{fwtypelist}}" wx:for-item="item" wx:for-index="idx2" wx:key="idx2"><block><view class="{{['body_tag',item.key==sindex?'body_active':'']}}" data-index="{{item.key}}" data-event-opts="{{[['tap',[['selectFwtype',['$event']]]]]}}" bindtap="__e">{{item.name+''}}</view></block></block></view></view><block wx:if="{{order_flow_mode!==1}}"><view class="body_item"><view class="body_title flex flex-bt"><text>预约时间</text><block wx:if="{{isdate}}"><view data-event-opts="{{[['tap',[['chooseTime',['$event']]]]]}}" class="body_data" bindtap="__e">{{''+(yydate?yydate:'请选择预约时间')+''}}<image class="body_detail" style="width:26rpx;height:26rpx;" src="{{pre_url+'/static/img/arrowright.png'}}"></image></view></block><block wx:else><view class="body_data">{{yydate}}</view></block></view></view></block><block wx:if="{{buydata.$orig.fwpeople==1&&order_flow_mode!==1}}"><view class="body_item"><view class="body_title flex flex-bt"><text>服务人员</text><view class="body_data" data-url="{{'selectpeople?prodata='+prodata+'&yydate='+yydate+'&sindex='+sindex+'&linkman='+linkman+'&tel='+tel}}" data-event-opts="{{[['tap',[['gotopeople',['$event']]]]]}}" bindtap="__e">{{''+(!buydata.m0?buydata.$orig.fw.realname:'请选择人员')+''}}<image class="body_detail" style="width:26rpx;height:26rpx;" src="{{pre_url+'/static/img/arrowright.png'}}"></image></view></view></view></block></view><view class="bcontent2"><block wx:if="{{buydata.$orig.leveldk_money>0}}"><view class="price"><text class="f1">{{buydata.m1+"折扣("+userinfo.discount+"折)"}}</text><text class="f2">{{"-¥"+buydata.$orig.leveldk_money}}</text></view></block><block wx:if="{{yyset.iscoupon==1}}"><view class="price"><view class="f1">{{buydata.m2}}</view><block wx:if="{{buydata.$orig.couponCount>0}}"><view class="f2" data-bid="{{buydata.$orig.bid}}" data-event-opts="{{[['tap',[['showCouponList',['$event']]]]]}}" bindtap="__e"><text style="{{'color:#fff;padding:4rpx 16rpx;font-weight:normal;border-radius:8rpx;font-size:24rpx;'+('background:'+(buydata.m3)+';')}}">{{buydata.$orig.couponrid!=0?buydata.$orig.couponList[buydata.$orig.couponkey].couponname:buydata.$orig.couponCount+'张可用'}}</text><text class="iconfont iconjiantou" style="color:#999;font-weight:normal;"></text></view></block><block wx:else><text class="f2" style="color:#999;">{{"无可用"+buydata.m4}}</text></block></view></block><view class="price"><text class="f1">服务价格</text><text class="f2">{{"¥"+buydata.$orig.product_price}}</text></view><view class="price"><text class="f1">应付定金</text><text class="f2">{{"¥"+buydata.$orig.sell_price}}</text></view><block wx:if="{{buydata.$orig.coupontype==3}}"><view class="price"><text class="f1">计次卡</text><text class="f2" style="color:red;">{{"-"+buydata.$orig.product_price}}</text></view></block><view style="display:none;">{{test}}</view><block wx:for="{{buydata.$orig.formdata}}" wx:for-item="item" wx:for-index="idx" wx:key="id"><view class="form-item"><view class="label">{{item.val1}}<block wx:if="{{item.val3==1}}"><text style="color:red;">*</text></block></view><block wx:if="{{item.key=='input'}}"><block><input class="input" type="text" name="{{'form'+buydata.$orig.bid+'_'+idx}}" placeholder="{{item.val2}}" placeholder-style="font-size:28rpx"/></block></block><block wx:if="{{item.key=='textarea'}}"><block><textarea class="textarea" name="{{'form'+buydata.$orig.bid+'_'+idx}}" placeholder="{{item.val2}}" placeholder-style="font-size:28rpx"></textarea></block></block><block wx:if="{{item.key=='radio'}}"><block><radio-group class="radio-group" name="{{'form'+buydata.$orig.bid+'_'+idx}}"><block wx:for="{{item.val2}}" wx:for-item="item1" wx:for-index="idx1" wx:key="id"><label class="flex-y-center"><radio class="radio" value="{{item1}}"></radio>{{item1+''}}</label></block></radio-group></block></block><block wx:if="{{item.key=='checkbox'}}"><block><checkbox-group class="checkbox-group" name="{{'form'+buydata.$orig.bid+'_'+idx}}"><block wx:for="{{item.val2}}" wx:for-item="item1" wx:for-index="idx1" wx:key="id"><label class="flex-y-center"><checkbox class="checkbox" value="{{item1}}"></checkbox>{{item1+''}}</label></block></checkbox-group></block></block><block wx:if="{{item.key=='selector'}}"><block><picker class="picker" mode="selector" name="{{'form'+buydata.$orig.bid+'_'+idx}}" value="" range="{{item.val2}}" data-bid="{{buydata.$orig.bid}}" data-idx="{{idx}}" data-event-opts="{{[['change',[['editorBindPickerChange',['$event']]]]]}}" bindchange="__e"><block wx:if="{{buydata.$orig.editorFormdata[idx]||buydata.$orig.editorFormdata[idx]===0}}"><view>{{''+item.val2[buydata.$orig.editorFormdata[idx]]}}</view></block><block wx:else><view>请选择</view></block></picker><text class="iconfont iconjiantou" style="color:#999;font-weight:normal;"></text></block></block><block wx:if="{{item.key=='time'}}"><block><picker class="picker" mode="time" name="{{'form'+buydata.$orig.bid+'_'+idx}}" value="" start="{{item.val2[0]}}" end="{{item.val2[1]}}" range="{{item.val2}}" data-bid="{{buydata.$orig.bid}}" data-idx="{{idx}}" data-event-opts="{{[['change',[['editorBindPickerChange',['$event']]]]]}}" bindchange="__e"><block wx:if="{{buydata.$orig.editorFormdata[idx]}}"><view>{{buydata.$orig.editorFormdata[idx]}}</view></block><block wx:else><view>请选择</view></block></picker><text class="iconfont iconjiantou" style="color:#999;font-weight:normal;"></text></block></block><block wx:if="{{item.key=='date'}}"><block><picker class="picker" mode="date" name="{{'form'+buydata.$orig.bid+'_'+idx}}" value="" start="{{item.val2[0]}}" end="{{item.val2[1]}}" range="{{item.val2}}" data-bid="{{buydata.$orig.bid}}" data-idx="{{idx}}" data-event-opts="{{[['change',[['editorBindPickerChange',['$event']]]]]}}" bindchange="__e"><block wx:if="{{buydata.$orig.editorFormdata[idx]}}"><view>{{buydata.$orig.editorFormdata[idx]}}</view></block><block wx:else><view>请选择</view></block></picker><text class="iconfont iconjiantou" style="color:#999;font-weight:normal;"></text></block></block><block wx:if="{{item.key=='upload'}}"><block><input style="display:none;" type="text" name="{{'form'+buydata.$orig.bid+'_'+idx}}" value="{{buydata.$orig.editorFormdata[idx]}}"/><view class="flex" style="flex-wrap:wrap;padding-top:20rpx;"><block wx:if="{{buydata.$orig.editorFormdata[idx]}}"><view class="form-imgbox"><view class="form-imgbox-img"><image class="image" src="{{buydata.$orig.editorFormdata[idx]}}" data-url="{{buydata.$orig.editorFormdata[idx]}}" mode="widthFix" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image></view></view></block><view class="form-uploadbtn" style="{{'background:'+('url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 50rpx')+';'+('background-size:'+('80rpx 80rpx')+';')+('background-color:'+('#F3F3F3')+';')}}" data-bid="{{buydata.$orig.bid}}" data-idx="{{idx}}" data-event-opts="{{[['tap',[['editorChooseImage',['$event']]]]]}}" bindtap="__e"></view></view></block></block></view></block></view></view></block><view style="width:100%;height:110rpx;"></view><view class="footer flex"><view class="text1 flex1">总计：<text style="font-weight:bold;font-size:36rpx;">{{"￥"+alltotalprice}}</text></view><block wx:if="{{issubmit}}"><button class="op" style="background:#999;">确认提交</button></block><block wx:else><button class="op" style="{{'background:'+('linear-gradient(-90deg,'+$root.m5+' 0%,rgba('+$root.m6+',0.8) 100%)')+';'}}" form-type="submit">确认提交</button></block></view></form><block wx:if="{{couponvisible}}"><view class="popup__container"><view data-event-opts="{{[['tap',[['handleClickMask',['$event']]]]]}}" class="popup__overlay" catchtap="__e"></view><view class="popup__modal"><view class="popup__title"><text class="popup__title-text">{{"请选择"+$root.m7}}</text><image class="popup__close" style="width:36rpx;height:36rpx;" src="{{pre_url+'/static/img/close.png'}}" data-event-opts="{{[['tap',[['handleClickMask',['$event']]]]]}}" catchtap="__e"></image></view><view class="popup__content"><couponlist vue-id="a7d96080-1" couponlist="{{allbuydata[bid].couponList}}" choosecoupon="{{true}}" selectedrid="{{allbuydata[bid].couponrid}}" bid="{{bid}}" data-event-opts="{{[['^chooseCoupon',[['chooseCoupon']]]]}}" bind:chooseCoupon="__e" bind:__l="__l"></couponlist></view></view></view></block></block></block><block wx:if="{{timeDialogShow}}"><view class="popup__container"><view data-event-opts="{{[['tap',[['hidetimeDialog',['$event']]]]]}}" class="popup__overlay" catchtap="__e"></view><view class="popup__modal"><view class="popup__title"><text class="popup__title-text">请选择时间</text><image class="popup__close" style="width:36rpx;height:36rpx;" src="{{pre_url+'/static/img/close.png'}}" data-event-opts="{{[['tap',[['hidetimeDialog',['$event']]]]]}}" catchtap="__e"></image></view><view class="order-tab"><view class="order-tab2"><block wx:for="{{$root.l1}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><view class="{{['item '+(curTopIndex==index?'on':'')]}}" data-index="{{index}}" data-id="{{item.$orig.id}}" data-event-opts="{{[['tap',[['switchTopTab',['$event']]]]]}}" bindtap="__e"><view class="datetext">{{item.$orig.weeks}}</view><view class="datetext2">{{item.$orig.date}}</view><view class="after" style="{{'background:'+(item.m8)+';'}}"></view></view></block></block></view></view><view class="flex daydate"><block wx:for="{{timelist}}" wx:for-item="item" wx:for-index="index2" wx:key="index2"><block><view class="{{['date '+(timeindex==index2&&item.status==1?'on':'')+(item.status==0?'hui':'')]}}" data-index2="{{index2}}" data-status="{{item.status}}" data-time="{{item.timeint}}" data-event-opts="{{[['tap',[['switchDateTab',['$event']]]]]}}" bindtap="__e">{{''+item.time+''}}</view></block></block></view><view class="op"><button data-event-opts="{{[['tap',[['selectDate',['$event']]]]]}}" class="tobuy on" style="{{'background-color:'+($root.m9)+';'}}" bindtap="__e">确 定</button></view></view></view></block><block wx:if="{{loading}}"><loading vue-id="a7d96080-2" bind:__l="__l"></loading></block><dp-tabbar vue-id="a7d96080-3" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="a7d96080-4" data-ref="popmsg" bind:__l="__l"></popmsg></view>