<view class="container"><block wx:if="{{isload}}"><block><view style="position:fixed;width:100%;top:0;z-index:100;"><dd-tab vue-id="217bb11e-1" itemdata="{{['排队中','已完成']}}" itemst="{{['0','1']}}" st="{{st}}" isfixed="{{false}}" data-event-opts="{{[['^changetab',[['changetab']]]]}}" bind:changetab="__e" bind:__l="__l"></dd-tab></view><view style="width:100%;height:100rpx;"></view><view class="topsearch flex-y-center"><view class="f1 flex-y-center"><image class="img" src="{{pre_url+'/static/img/search_ico.png'}}"></image><input placeholder="输入关键字搜索" placeholder-style="font-size:24rpx;color:#C2C2C2" data-event-opts="{{[['confirm',[['searchConfirm',['$event']]]]]}}" value="{{keyword}}" bindconfirm="__e"/></view></view><view class="order-content"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><view class="order-box"><view class="head"><view class="f1">{{"订单号："+item.$orig.ordernum}}</view><view class="flex1"></view><text class="st0">{{item.$orig.statusLabel}}</text></view><view class="content" style="border-bottom:none;"><view class="detail"><text class="t1">{{"商户名称："+item.$orig.bname}}</text></view><view class="detail"><text class="t1">{{item.m0+"信息："+item.$orig.nickname+"(ID:"+item.$orig.mid+")"}}</text></view><view class="detail"><text class="t1">{{item.m1+"手机号："+item.$orig.tel}}</text></view><view class="detail">排队金额：<text class="t1" style="{{('color:'+item.m2)}}">{{item.$orig.money}}</text></view><view class="detail">{{''+item.m3+"："}}<text class="t1" style="{{('color:'+item.m4)}}">{{item.$orig.money_give}}</text></view><block wx:if="{{item.$orig.queue_no}}"><view class="detail">当前排名：<text class="t1" style="{{('color:'+item.m5)}}">{{item.$orig.queue_noLabel}}</text></view></block><view class="detail"><text class="t1">{{"排队时间："+item.$orig.createtimeFormat}}</text></view><view class="bottom"><block wx:if="{{item.$orig.status==0&&set.edit_money_status==1}}"><view class="btn2" style="{{('background:'+item.m6+';border:0')}}" data-id="{{item.$orig.id}}" data-event-opts="{{[['tap',[['editmoney',['$event']]]]]}}" bindtap="__e">编辑金额</view></block><block wx:if="{{item.$orig.status==0&&item.$orig.show_changeno}}"><view class="btn2" style="{{('background:'+item.m7+';border:0')}}" data-id="{{item.$orig.id}}" data-event-opts="{{[['tap',[['changeno',['$event']]]]]}}" bindtap="__e">更改排队号</view></block><block wx:if="{{item.$orig.status==0}}"><view class="btn" style="{{('background:linear-gradient(90deg,'+item.m8+' 0%,rgba('+item.m9+',0.8) 100%)')}}" data-id="{{item.$orig.id}}" data-event-opts="{{[['tap',[['queueQuit',['$event']]]]]}}" bindtap="__e">退出</view></block></view></view></view></block></block></view><block wx:if="{{nomore}}"><nomore vue-id="217bb11e-2" bind:__l="__l"></nomore></block><block wx:if="{{nodata}}"><nodata vue-id="217bb11e-3" bind:__l="__l"></nodata></block></block></block><uni-popup class="vue-ref" vue-id="217bb11e-4" id="changenoDialog" type="dialog" data-ref="changenoDialog" bind:__l="__l" vue-slots="{{['default']}}"><uni-popup-dialog vue-id="{{('217bb11e-5')+','+('217bb11e-4')}}" mode="input" title="更改排队号" value="" placeholder="请输入排队号" data-event-opts="{{[['^confirm',[['changenoConfirm']]]]}}" bind:confirm="__e" bind:__l="__l"></uni-popup-dialog></uni-popup><uni-popup class="vue-ref" vue-id="217bb11e-6" id="editmoneyDialog" type="dialog" data-ref="editmoneyDialog" bind:__l="__l" vue-slots="{{['default']}}"><uni-popup-dialog vue-id="{{('217bb11e-7')+','+('217bb11e-6')}}" mode="input" title="编辑排队金额" value="" placeholder="请输入排队金额" data-event-opts="{{[['^confirm',[['editmoneyConfirm']]]]}}" bind:confirm="__e" bind:__l="__l"></uni-popup-dialog></uni-popup><block wx:if="{{loading}}"><loading vue-id="217bb11e-8" bind:__l="__l"></loading></block><dp-tabbar vue-id="217bb11e-9" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="217bb11e-10" data-ref="popmsg" bind:__l="__l"></popmsg></view>