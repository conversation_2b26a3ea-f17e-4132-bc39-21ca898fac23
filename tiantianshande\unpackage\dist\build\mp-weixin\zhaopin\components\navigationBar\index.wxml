<view><view class="navigation-bar" style="{{('padding-top:'+paddingTop+'px;height:'+height+'px;line-height:'+height+'px;background:'+background+';color:'+color+';font-size:'+fontSize+';position:'+(fixed&&'fixed')+';transform:translateY('+(!show?-paddingTop-height+'px':'0')+');z-index:'+zIndex)}}"><block wx:if="{{back}}"><view class="back" style="{{('padding-top:'+paddingTop+'px;height:'+height+'px;line-height:'+height+'px;')}}"><view class="style-simple"><block wx:if="{{showHomeButton}}"><view data-event-opts="{{[['tap',[['navigateBackHome',['$event']]]]]}}" class="iconfont iconhome1" bindtap="__e"></view></block><block wx:else><view data-event-opts="{{[['tap',[['navigateBack',['$event']]]]]}}" class="iconfont iconarrow_left" bindtap="__e"></view></block></view></view></block><view class="title">{{title!='none'?title:''}}</view></view><block wx:if="{{fixed&&show&&hasHei}}"><view class="navigation-bar-holder" style="{{('background:'+placeholderBg+';padding-top:'+paddingTop+'px;height:'+height+'px;line-height:'+height+'px;width:750rpx;')}}"></view></block></view>