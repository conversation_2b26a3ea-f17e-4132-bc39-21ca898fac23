<view class="container"><block wx:if="{{isload}}"><block><view class="search-container" data-url="{{'dksearch?mid='+mid}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e"><view class="search-box"><image class="img" src="{{pre_url+'/static/img/search_ico.png'}}"></image><view class="search-text">搜索感兴趣的商品</view></view></view><view class="content-container"><view class="nav_left"><view class="{{['nav_left_items '+(curIndex==-1?'active':'')]}}" style="{{'color:'+(curIndex==-1?$root.m0:'#333')+';'}}" data-index="-1" data-id="0" data-event-opts="{{[['tap',[['switchRightTab',['$event']]]]]}}" bindtap="__e"><view class="before" style="{{'background:'+($root.m1)+';'}}"></view>全部</view><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><view class="{{['nav_left_items '+(curIndex==index?'active':'')]}}" style="{{'color:'+(curIndex==index?item.m2:'#333')+';'}}" data-index="{{index}}" data-id="{{item.$orig.id}}" data-event-opts="{{[['tap',[['switchRightTab',['$event']]]]]}}" bindtap="__e"><view class="before" style="{{'background:'+(item.m3)+';'}}"></view>{{item.$orig.name}}</view></block></block></view><view class="nav_right"><view class="nav_right-content"><view class="nav-pai"><view class="nav-paili" style="{{(!field||field=='sort'?'color:'+$root.m4:'')}}" data-field="sort" data-order="desc" data-event-opts="{{[['tap',[['changeOrder',['$event']]]]]}}" bindtap="__e">综合</view><view class="nav-paili" style="{{(field=='sales'?'color:'+$root.m5:'')}}" data-field="sales" data-order="desc" data-event-opts="{{[['tap',[['changeOrder',['$event']]]]]}}" bindtap="__e">销量</view><view class="nav-paili" data-field="sell_price" data-order="{{order=='asc'?'desc':'asc'}}" data-event-opts="{{[['tap',[['changeOrder',['$event']]]]]}}" bindtap="__e"><text style="{{(field=='sell_price'?'color:'+$root.m6:'')}}">价格</text><text class="iconfont iconshangla" style="{{(field=='sell_price'&&order=='asc'?'color:'+$root.m7:'')}}"></text><text class="iconfont icondaoxu" style="{{(field=='sell_price'&&order=='desc'?'color:'+$root.m8:'')}}"></text></view></view><block wx:if="{{$root.g0}}"><view class="classify-ul"><view class="flex" style="width:100%;overflow-y:hidden;overflow-x:scroll;"><view class="classify-li" style="{{(curIndex2==-1?'color:'+$root.m9+';background:rgba('+$root.m10+',0.2)':'')}}" data-id="{{clist[curIndex].id}}" data-index="-1" data-event-opts="{{[['tap',[['changeCTab',['$event']]]]]}}" bindtap="__e">全部</view><block wx:for="{{$root.l1}}" wx:for-item="item" wx:for-index="idx2" wx:key="idx2"><block><view class="classify-li" style="{{(curIndex2==idx2?'color:'+item.m11+';background:rgba('+item.m12+',0.2)':'')}}" data-id="{{item.$orig.id}}" data-index="{{idx2}}" data-event-opts="{{[['tap',[['changeCTab',['$event']]]]]}}" bindtap="__e">{{item.$orig.name}}</view></block></block></view></view></block><scroll-view class="classify-box" scroll-y="true" data-event-opts="{{[['scrolltolower',[['scrolltolower',['$event']]]]]}}" bindscrolltolower="__e"><view class="product-itemlist"><block wx:for="{{$root.l2}}" wx:for-item="item" wx:for-index="index" wx:key="id"><view class="{{['item',item.$orig.stock<=0?'soldout':'']}}"><view class="product-pic"><image class="image" src="{{item.$orig.pic}}" mode="widthFix"></image><view class="overlay"><view class="text">售罄</view></view></view><view class="product-info"><view class="p1"><text>{{item.$orig.name}}</text></view><block wx:if="{{item.$orig.price_show_type=='0'||!item.$orig.price_show_type}}"><view><block wx:if="{{item.$orig.show_cost&&item.$orig.price_type!=1}}"><view style="{{'color:'+(item.$orig.cost_color?item.$orig.cost_color:'#999')+';'+('font-size:'+('32rpx')+';')}}"><text style="font-size:20rpx;padding-right:1px;">{{item.$orig.cost_tag}}</text>{{item.$orig.cost_price}}</view></block><block wx:if="{{(item.$orig.price_type!=1||item.$orig.sell_price>0)&&(item.$orig.showprice_dollar||item.$orig.show_sellprice)}}"><view class="p2"><block wx:if="{{item.$orig.showprice_dollar}}"><block><view class="t1" style="{{'color:'+(item.m13)+';'}}"><text style="font-size:20rpx;padding-right:1px;">$</text>{{item.$orig.usd_sellprice+''}}<text style="font-size:28rpx;margin-left:6rpx;"><text style="font-size:20rpx;padding-right:1px;">￥</text>{{item.$orig.sell_price}}</text></view></block></block><block wx:else><block><view class="t1" style="{{'color:'+(item.$orig.price_color?item.$orig.price_color:item.m14)+';'}}"><text style="font-size:20rpx;padding-right:1px;">{{item.$orig.price_tag?item.$orig.price_tag:'￥'}}</text>{{item.$orig.sell_price}}<block wx:if="{{item.$orig.product_unit}}"><block>{{"/"+item.$orig.product_unit}}</block></block></view></block></block><block wx:if="{{item.$orig.market_price*1>item.$orig.sell_price*1}}"><text class="t2">{{"￥"+item.$orig.market_price}}</text></block></view></block></view></block><block wx:if="{{item.$orig.price_show_type=='1'||item.$orig.price_show_type=='2'}}"><view><block wx:if="{{item.$orig.is_vip=='0'}}"><view><block wx:if="{{item.$orig.price_type!=1||item.$orig.sell_price>0}}"><view class="p2"><text class="t1" style="{{'color:'+(item.m15)+';'}}"><text style="padding-right:1px;font-size:20rpx;">￥</text><text style="font-size:32rpx;">{{item.$orig.sell_price}}</text></text><block wx:if="{{item.$orig.market_price*1>item.$orig.sell_price*1}}"><text class="t2">{{"￥"+item.$orig.market_price}}</text></block></view></block><block wx:if="{{item.$orig.price_show_type=='2'&&item.$orig.lvprice==1}}"><view class="flex"><view class="member flex" style="{{('border-color:'+item.m16)}}"><view class="member_lable flex-y-center" style="{{'background:'+(item.m17)+';'}}">{{item.$orig.level_name_show}}</view><view class="member_value flex-y-center" style="{{('color:'+item.m18)}}">￥<text>{{item.$orig.sell_price_origin}}</text></view></view></view></block></view></block><block wx:if="{{item.$orig.is_vip=='1'}}"><view><block wx:if="{{item.$orig.lvprice==1}}"><view class="flex"><view class="member flex" style="{{('border-color:'+item.m19)}}"><view class="member_lable flex-y-center" style="{{'background:'+(item.m20)+';'}}">{{item.$orig.level_name_show}}</view><view class="member_value flex-y-center" style="{{('color:'+item.m21)}}">￥<text style="font-size:32rpx;">{{item.$orig.sell_price}}</text></view></view></view></block><block wx:if="{{item.$orig.price_type!=1||item.$orig.sell_price>0}}"><view class="p2"><text class="t1" style="{{'color:'+(item.m22)+';'}}"><text style="font-size:20rpx;padding-right:1px;">￥</text><text style="{{(item.$orig.lvprice=='1'?'font-size:26rpx;':'font-size:32rpx;')}}">{{item.$orig.sell_price_origin}}</text></text><block wx:if="{{item.$orig.market_price*1>item.$orig.sell_price*1}}"><text class="t2">{{"￥"+item.$orig.market_price}}</text></block></view></block></view></block></view></block><block wx:if="{{item.$orig.xunjia_text&&item.$orig.price_type==1&&item.$orig.sell_price<=0}}"><view class="p2" style="height:50rpx;line-height:44rpx;"><text class="t1" style="{{'color:'+(item.m23)+';'+('font-size:'+('30rpx')+';')}}">询价</text><block wx:if="{{item.$orig.xunjia_type==1}}"><block><block wx:if="{{item.$orig.xunjia_btn_url}}"><view class="lianxi" style="{{'background:'+(item.$orig.xunjia_btn_bgcolor?item.$orig.xunjia_btn_bgcolor:item.m24)+';'+('color:'+(item.$orig.xunjia_btn_color?item.$orig.xunjia_btn_color:'#FFF')+';')}}" data-url="{{item.$orig.xunjia_btn_url}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e">{{item.$orig.xunjia_text?item.$orig.xunjia_text:'联系TA'}}</view></block><block wx:else><view class="lianxi" style="{{'background:'+(item.$orig.xunjia_btn_bgcolor?item.$orig.xunjia_btn_bgcolor:item.m25)+';'+('color:'+(item.$orig.xunjia_btn_color?item.$orig.xunjia_btn_color:'#FFF')+';')}}" data-lx_name="{{item.$orig.lx_name}}" data-lx_bid="{{item.$orig.lx_bid}}" data-lx_bname="{{item.$orig.lx_bname}}" data-lx_tel="{{item.$orig.lx_tel}}" data-btntype="2" data-event-opts="{{[['tap',[['showLinkChange',['$event']]]]]}}" catchtap="__e">{{item.$orig.xunjia_text?item.$orig.xunjia_text:'联系TA'}}</view></block></block></block><block wx:else><view class="lianxi" style="{{'background:'+(item.m26)+';'}}" data-lx_name="{{item.$orig.lx_name}}" data-lx_bid="{{item.$orig.lx_bid}}" data-lx_bname="{{item.$orig.lx_bname}}" data-lx_tel="{{item.$orig.lx_tel}}" data-btntype="2" data-event-opts="{{[['tap',[['showLinkChange',['$event']]]]]}}" catchtap="__e">{{item.$orig.xunjia_text?item.$orig.xunjia_text:'联系TA'}}</view></block></view></block><block wx:if="{{item.$orig.merchant_name}}"><view class="p1" style="color:#666;font-size:24rpx;white-space:nowrap;text-overflow:ellipsis;margin-top:6rpx;height:30rpx;line-height:30rpx;font-weight:normal;"><text>{{item.$orig.merchant_name}}</text></view></block><block wx:if="{{item.$orig.main_business}}"><view class="p1" style="color:#666;font-size:24rpx;margin-top:4rpx;font-weight:normal;"><text>{{item.$orig.main_business}}</text></view></block><view class="p3"><block wx:if="{{item.$orig.sales>0}}"><view class="p3-1"><text style="overflow:hidden;">{{"已售"+item.$orig.sales+"件"}}</text></view></block></view><block wx:if="{{item.$orig.sales<=0&&item.$orig.merchant_name}}"><view style="height:44rpx;"></view></block><block wx:if="{{!item.$orig.price_type&&item.$orig.hide_cart!=true}}"><view class="p4" style="{{'background:'+('rgba('+item.m27+',0.1)')+';'+('color:'+(item.m28)+';')}}" data-proid="{{item.$orig.id}}" data-event-opts="{{[['tap',[['buydialogChange',['$event']]]]]}}" catchtap="__e"><text class="iconfont icon_gouwuche"></text></view></block></view></view></block></view><block wx:if="{{nomore}}"><nomore vue-id="fb5519f4-1" text="没有更多商品了" bind:__l="__l"></nomore></block><block wx:if="{{nodata}}"><nodata vue-id="fb5519f4-2" text="暂无相关商品" bind:__l="__l"></nodata></block><view style="width:100%;height:100rpx;"></view></scroll-view></view></view></view><block wx:if="{{buydialogShow}}"><buydialog vue-id="fb5519f4-3" proid="{{proid}}" menuindex="{{menuindex}}" btntype="1" needaddcart="{{false}}" data-event-opts="{{[['^buydialogChange',[['buydialogChange']]],['^addcart',[['afteraddcart']]]]}}" bind:buydialogChange="__e" bind:addcart="__e" bind:__l="__l"></buydialog></block><view style="height:auto;position:relative;"><view style="width:100%;height:100rpx;"></view><view class="{{['footer','flex',menuindex>-1?'tabbarbot':'notabbarbot']}}"><view data-event-opts="{{[['tap',[['handleClickMask',['$event']]]]]}}" class="cart_ico" style="{{'background:'+('linear-gradient(0deg,'+$root.m29+' 0%,rgba('+$root.m30+',0.8) 100%)')+';'}}" catchtap="__e"><image class="img" src="{{pre_url+'/static/img/cart.png'}}"></image><block wx:if="{{$root.g1>0}}"><view class="cartnum" style="{{'background:'+($root.m31)+';'}}">{{cartData.total}}</view></block></view><view class="text1">合计</view><view class="text2 flex1" style="{{'color:'+($root.m32)+';'}}"><text style="font-size:20rpx;">￥</text>{{cartData.totalprice}}</view><view data-event-opts="{{[['tap',[['gopay',['$event']]]]]}}" class="op" style="{{'background:'+('linear-gradient(270deg,'+$root.m33+' 0%,rgba('+$root.m34+',0.8) 100%)')+';'}}" bindtap="__e">去录入</view></view></view><block wx:if="{{cartListShow}}"><view class="{{['popup__container',menuindex>-1?'tabbarbot':'notabbarbot']}}" style="margin-bottom:100rpx;"><view data-event-opts="{{[['tap',[['handleClickMask',['$event']]]]]}}" class="{{['popup__overlay',menuindex>-1?'tabbarbot':'notabbarbot']}}" style="margin-bottom:100rpx;" catchtap="__e"></view><view class="popup__modal" style="min-height:400rpx;padding:0;"><view class="popup__title" style="border-bottom:1px solid #EFEFEF;"><text class="popup__title-text" style="color:#323232;font-weight:bold;font-size:32rpx;">购物车</text><view data-event-opts="{{[['tap',[['clearShopCartFn',['$event']]]]]}}" class="popup__close flex-y-center" style="color:#999999;font-size:24rpx;" catchtap="__e"><image style="width:24rpx;height:24rpx;margin-right:6rpx;" src="{{pre_url+'/static/img/del.png'}}"></image>清空</view></view><view class="popup__content" style="padding:0;"><scroll-view class="prolist" scroll-y="{{true}}"><block wx:for="{{cartList.list}}" wx:for-item="cart" wx:for-index="index" wx:key="index"><block><view class="proitem"><image class="pic flex0" src="{{cart.guige.pic?cart.guige.pic:cart.product.pic}}"></image><view class="con"><view class="f1">{{cart.product.name}}</view><block wx:if="{{cart.guige.name!='默认规格'}}"><view class="f2">{{cart.guige.name}}</view></block><view class="f3" style="color:#ff5555;margin-top:10rpx;font-size:28rpx;">{{"￥"+cart.guige.sell_price}}</view></view><view class="addnum"><view class="minus"><image class="img" src="{{pre_url+'/static/img/cart-minus.png'}}" data-num="-1" data-proid="{{cart.guige.proid}}" data-ggid="{{cart.guige.id}}" data-stock="{{cart.guige.stock}}" data-event-opts="{{[['tap',[['addcart',['$event']]]]]}}" bindtap="__e"></image></view><text class="i">{{cart.num}}</text><view class="plus"><image class="img" src="{{pre_url+'/static/img/cart-plus.png'}}" data-num="1" data-proid="{{cart.guige.proid}}" data-ggid="{{cart.guige.id}}" data-stock="{{cart.guige.stock}}" data-event-opts="{{[['tap',[['addcart',['$event']]]]]}}" bindtap="__e"></image></view></view></view></block></block><block wx:if="{{!$root.g2}}"><block><text class="nopro">暂时没有商品喔~</text></block></block></scroll-view></view></view></view></block></block></block><block wx:if="{{loading}}"><loading vue-id="fb5519f4-4" loadstyle="left:62.5%" bind:__l="__l"></loading></block><dp-tabbar vue-id="fb5519f4-5" opt="{{opt}}" data-event-opts="{{[['^getmenuindex',[['getmenuindex']]]]}}" bind:getmenuindex="__e" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="fb5519f4-6" data-ref="popmsg" bind:__l="__l"></popmsg><wxxieyi vue-id="fb5519f4-7" bind:__l="__l"></wxxieyi></view>