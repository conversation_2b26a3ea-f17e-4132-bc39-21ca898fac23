<view class="container"><canvas class="particles-canvas" style="{{'width:'+(canvasWidth+'px')+';'+('height:'+(canvasHeight+'px')+';')}}" canvas-id="particlesCanvas"></canvas><view class="bg-grid"></view><view class="bg-circles"></view><view class="ending-console"><view class="header-section"><view class="completion-badge"><view class="badge-glow"></view><text class="badge-icon">🎉</text></view><view class="title-info"><text class="main-title" style="{{'color:'+($root.m0)+';'}}">时空之旅完成</text><text class="subtitle">恭喜您完成了与未来自己的对话</text></view><view class="time-display"><text class="year">2049</text><text class="status">任务完成</text></view></view><view class="ending-guidance"><view class="guidance-content"><text class="guidance-text">{{displayText}}</text><block wx:if="{{showCursor}}"><text class="typing-cursor">|</text></block></view></view><view class="dream-wall-section"><view class="section-header"><text class="section-title" style="{{'color:'+($root.m1)+';'}}">选择您的明日萌像</text><text class="section-subtitle">每一种选择都代表着不同的未来可能</text></view><view class="dream-wall"><block wx:for="{{dreamOptions}}" wx:for-item="dream" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['selectDream',[index]]]]]}}" class="{{['dream-item',[(selectedDream===index)?'selected':'']]}}" bindtap="__e"><view class="dream-icon"><text>{{dream.icon}}</text></view><view class="dream-info"><text class="dream-name">{{dream.name}}</text><text class="dream-desc">{{dream.description}}</text></view><block wx:if="{{selectedDream===index}}"><view class="selected-indicator"><text class="indicator-icon">✓</text></view></block></view></block></view></view><block wx:if="{{selectedDream!==-1}}"><view class="declaration-section"><view class="declaration-header"><text class="declaration-title">您的未来宣言</text></view><view class="declaration-content"><text class="declaration-text">{{personalDeclaration}}</text></view></view></block><view class="achievement-section"><view class="achievement-header"><text class="achievement-title">🏆 解锁成就</text></view><view class="achievements"><block wx:for="{{unlockedAchievements}}" wx:for-item="achievement" wx:for-index="index" wx:key="index"><view class="achievement-item"><view class="achievement-icon"><text>{{achievement.icon}}</text></view><view class="achievement-info"><text class="achievement-name">{{achievement.name}}</text><text class="achievement-desc">{{achievement.description}}</text></view></view></block></view></view><view class="statistics-section"><view class="stats-header"><text class="stats-title">时空之旅统计</text></view><view class="stats-grid"><view class="stat-item"><text class="stat-value">{{journeyStats.totalTime}}</text><text class="stat-label">总用时</text></view><view class="stat-item"><text class="stat-value">{{journeyStats.dialogueCount}}</text><text class="stat-label">对话轮数</text></view><view class="stat-item"><text class="stat-value">{{journeyStats.voiceTime}}</text><text class="stat-label">语音时长</text></view><view class="stat-item"><text class="stat-value">{{journeyStats.completionRate}}</text><text class="stat-label">完成度</text></view></view></view><view class="share-section"><view class="share-header"><text class="share-title">分享您的时空之旅</text></view><view class="share-actions"><view data-event-opts="{{[['tap',[['generateCertificate',['$event']]]]]}}" class="share-btn" bindtap="__e"><text class="share-icon">📜</text><text class="share-text">生成证书</text></view><view data-event-opts="{{[['tap',[['shareToWeChat',['$event']]]]]}}" class="share-btn" bindtap="__e"><text class="share-icon">💬</text><text class="share-text">微信分享</text></view><view data-event-opts="{{[['tap',[['shareToWeibo',['$event']]]]]}}" class="share-btn" bindtap="__e"><text class="share-icon">📱</text><text class="share-text">微博分享</text></view><view data-event-opts="{{[['tap',[['downloadResults',['$event']]]]]}}" class="share-btn" bindtap="__e"><text class="share-icon">⬇</text><text class="share-text">下载结果</text></view></view></view><view class="control-section"><view class="control-buttons"><view data-event-opts="{{[['tap',[['restartJourney',['$event']]]]]}}" class="control-btn secondary" bindtap="__e"><text class="btn-icon">🔄</text><text class="btn-text">重新开始</text></view><view data-event-opts="{{[['tap',[['exploreMore',['$event']]]]]}}" class="control-btn primary" bindtap="__e"><text class="btn-icon">🚀</text><text class="btn-text">探索更多</text></view><view data-event-opts="{{[['tap',[['returnHome',['$event']]]]]}}" class="control-btn" bindtap="__e"><text class="btn-icon">🏠</text><text class="btn-text">返回首页</text></view></view></view></view><block wx:if="{{showCelebration}}"><view class="celebration-overlay"><view class="celebration-content"><view class="celebration-fireworks"><view class="firework firework-1">🎆</view><view class="firework firework-2">✨</view><view class="firework firework-3">🎇</view><view class="firework firework-4">⭐</view></view><text class="celebration-text">恭喜完成时空之旅！</text></view></view></block><view class="footer"><text class="footer-text">梦想方舟计划 v2049.1 | 时空之旅已完成 | 感谢您的参与</text></view></view>