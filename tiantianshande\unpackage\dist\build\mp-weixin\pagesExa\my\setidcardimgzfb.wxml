<view class="container data-v-ff7946c4"><block wx:if="{{isload}}"><block class="data-v-ff7946c4"><form data-event-opts="{{[['submit',[['formSubmit',['$event']]]],['reset',[['formReset',['$event']]]]]}}" bindsubmit="__e" bindreset="__e" class="data-v-ff7946c4"><view class="auth data-v-ff7946c4"><view class="infos data-v-ff7946c4"><view class="list data-v-ff7946c4"><text class="data-v-ff7946c4">您的姓名</text><input placeholder="请输入内容" placeholderStyle="font-size: 28rpx;color: #999999;line-height: 40rpx;font-family: PingFangSC-Regular;" border="surround" name="realname" data-event-opts="{{[['input',[['__set_model',['','realname','$event',[]]]]]]}}" value="{{realname}}" bindinput="__e" class="data-v-ff7946c4"/></view><view class="list data-v-ff7946c4"><text class="data-v-ff7946c4">您的身份证</text><input placeholder="请输入身份证号码" placeholderStyle="font-size: 28rpx;color: #999999;line-height: 40rpx;font-family: PingFangSC-Regular;" border="surround" name="idcard" data-event-opts="{{[['input',[['__set_model',['','idcard','$event',[]]]]]]}}" value="{{idcard}}" bindinput="__e" class="data-v-ff7946c4"/></view><view class="list data-v-ff7946c4"><text class="data-v-ff7946c4">上传身份证头像面</text><view data-event-opts="{{[['tap',[['upIdcardHead',['$event']]]]]}}" class="upload data-v-ff7946c4" bindtap="__e"><image src="{{idcard_front}}" class="data-v-ff7946c4"></image></view></view><view class="list data-v-ff7946c4"><text class="data-v-ff7946c4">上传身份证背面</text><view data-event-opts="{{[['tap',[['upIdcardBack',['$event']]]]]}}" class="upload data-v-ff7946c4" bindtap="__e"><image src="{{idcard_back}}" class="data-v-ff7946c4"></image></view></view><view data-event-opts="{{[['tap',[['formSubmit',['$event']]]]]}}" class="button data-v-ff7946c4" bindtap="__e"><text class="data-v-ff7946c4">提交认证信息</text></view><view class="text data-v-ff7946c4"><text class="data-v-ff7946c4">根据监管要求身份证照片仅用于实名认证</text></view></view></view></form><block wx:if="{{showposter}}"><view class="posterDialog data-v-ff7946c4"><view class="main data-v-ff7946c4"><view data-event-opts="{{[['tap',[['posterDialogClose',['$event']]]]]}}" class="close data-v-ff7946c4" bindtap="__e"><image class="img data-v-ff7946c4" src="/static/img/close.png"></image></view><view data-event-opts="{{[['tap',[['baocun',['$event']]]]]}}" class="content data-v-ff7946c4" bindtap="__e"><canvas canvas-id="qrcode" class="data-v-ff7946c4"></canvas></view><view class="tips data-v-ff7946c4"><text class="data-v-ff7946c4">{{"请使用"+(cert_type==='ZHIMACREDIT'?'支付宝':'微信')+"扫码进行认证"}}</text><text class="sub-tips data-v-ff7946c4">{{cert_type==='ZHIMACREDIT'?'请保存二维码打开支付宝扫码认证':'请用另一台手机微信进行人脸验证'}}</text><text class="sub-tips data-v-ff7946c4">认证成功后打开此页面点击<text class="red data-v-ff7946c4">我已认证</text></text></view><view data-event-opts="{{[['tap',[['baocun',['$event']]]]]}}" class="btn-s data-v-ff7946c4" bindtap="__e">长按识别</view><view data-event-opts="{{[['tap',[['renzheng',['$event']]]]]}}" class="btn-s data-v-ff7946c4" bindtap="__e">我已认证</view></view></view></block></block></block><block wx:if="{{loading}}"><loading vue-id="1be42ea2-1" class="data-v-ff7946c4" bind:__l="__l"></loading></block><dp-tabbar vue-id="1be42ea2-2" opt="{{opt}}" class="data-v-ff7946c4" bind:__l="__l"></dp-tabbar><popmsg vue-id="1be42ea2-3" data-ref="popmsg" class="data-v-ff7946c4 vue-ref" bind:__l="__l"></popmsg></view>