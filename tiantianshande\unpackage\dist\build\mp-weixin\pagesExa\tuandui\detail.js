require('../common/vendor.js');(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pagesExa/tuandui/detail"],{"173a":function(t,e,a){"use strict";var n=a("764f"),i=a.n(n);i.a},"427f":function(t,e,a){"use strict";(function(t,e){var n=a("47a9");a("06e9");n(a("3240"));var i=n(a("5b67"));t.__webpack_require_UNI_MP_PLUGIN__=a,e(i.default)}).call(this,a("3223")["default"],a("df3c")["createPage"])},"5b67":function(t,e,a){"use strict";a.r(e);var n=a("7525"),i=a("ce4e3");for(var o in i)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(o);a("173a");var r=a("828b"),d=Object(r["a"])(i["default"],n["b"],n["c"],!1,null,null,null,!1,n["a"],void 0);e["default"]=d.exports},7525:function(t,e,a){"use strict";a.d(e,"b",(function(){return i})),a.d(e,"c",(function(){return o})),a.d(e,"a",(function(){return n}));var n={loading:function(){return a.e("components/loading/loading").then(a.bind(null,"ceaa"))},dpTabbar:function(){return a.e("components/dp-tabbar/dp-tabbar").then(a.bind(null,"b875"))},popmsg:function(){return a.e("components/popmsg/popmsg").then(a.bind(null,"2bf2"))}},i=function(){var t=this.$createElement;this._self._c},o=[]},"764f":function(t,e,a){},8655:function(t,e,a){"use strict";(function(t){Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var a=getApp(),n={data:function(){return{opt:{},loading:!1,isload:!1,activityId:0,activityInfo:{},rewardRules:[],userRewardInfo:null}},onLoad:function(t){this.opt=a.getopts(t),this.activityId=t.id||0,this.getdata()},onPullDownRefresh:function(){this.getdata()},onShow:function(){this.isload&&this.activityId>0&&this.getUserRewardInfo()},onShareAppMessage:function(){return this._sharewx({title:this.activityInfo.title,desc:"团队业绩奖励活动",pic:"/static/img/tuandui-share.png",callback:function(){}})},methods:{getdata:function(){var e=this;if(!e.activityId)return console.log("2025-01-03 22:55:53,565-ERROR-[detail.vue][getdata_001] 活动ID不能为空"),void a.alert("活动ID不能为空");console.log("2025-01-03 22:55:53,565-INFO-[detail.vue][getdata_002] 获取活动详情, ID:",e.activityId),e.loading=!0,a.get("ApiTuandui/getActivityList",{},(function(n){if(console.log("2025-01-03 22:55:53,565-INFO-[detail.vue][getdata_003] 活动列表响应:",n),1==n.status){for(var i=null,o=0;o<n.data.length;o++)if(n.data[o].id==e.activityId){i=n.data[o];break}i?(e.activityInfo=i,e.rewardRules=i.reward_rules||[],t.setNavigationBarTitle({title:i.title||"活动详情"}),e.getUserRewardInfo()):(e.loading=!1,console.log("2025-01-03 22:55:53,565-ERROR-[detail.vue][getdata_004] 未找到指定活动"),a.alert("活动不存在"))}else e.loading=!1,console.log("2025-01-03 22:55:53,565-ERROR-[detail.vue][getdata_005] 获取活动列表失败:",n.msg),a.alert(n.msg||"获取活动信息失败")}))},getUserRewardInfo:function(){var e=this;console.log("2025-01-03 22:55:53,565-INFO-[detail.vue][getUserRewardInfo_001] 获取用户奖励信息, 活动ID:",e.activityId),a.get("ApiTuandui/getUserRewardInfo",{activity_id:e.activityId},(function(a){e.loading=!1,e.isload=!0,console.log("2025-01-03 22:55:53,565-INFO-[detail.vue][getUserRewardInfo_002] 用户奖励信息响应:",a),1==a.status?(e.userRewardInfo=a.data,console.log("2025-01-03 22:55:53,565-INFO-[detail.vue][getUserRewardInfo_003] 设置用户奖励信息成功")):(console.log("2025-01-03 22:55:53,565-WARN-[detail.vue][getUserRewardInfo_004] 获取用户奖励信息失败：",a.msg),e.userRewardInfo=null),t.stopPullDownRefresh()}))},refreshData:function(){console.log("2025-01-03 22:55:53,565-INFO-[detail.vue][refreshData_001] 刷新数据"),this.getActivityInfo(),this.getUserRewardInfo()},gotoRecords:function(){t.navigateTo({url:"/pagesExa/tuandui/records?activity_id="+this.activityId})},claimReward:function(t){var e=t.currentTarget.dataset.activity,a=t.currentTarget.dataset.level;console.log("2025-01-03 22:55:53,565-INFO-[detail.vue][claimReward_001] 领取奖励:",e,a),this.$refs.popmsg.show("提示","领取奖励功能待实现")}}};e.default=n}).call(this,a("df3c")["default"])},ce4e3:function(t,e,a){"use strict";a.r(e);var n=a("8655"),i=a.n(n);for(var o in n)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(o);e["default"]=i.a}},[["427f","common/runtime","common/vendor"]]]);