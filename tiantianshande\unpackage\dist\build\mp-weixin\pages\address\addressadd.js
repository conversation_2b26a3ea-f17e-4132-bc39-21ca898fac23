(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages/address/addressadd"],{"1ced":function(t,e,a){"use strict";(function(t,a){Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n=getApp(),o={data:function(){return{opt:{},loading:!1,isload:!1,menuindex:-1,name:"",tel:"",area:"",address:"",longitude:"",latitude:"",regiondata:"",type:0,addressxx:"",company:"",items:[],showCompany:!1}},onLoad:function(e){this.opt=n.getopts(e),this.type=this.opt.type||0;var a=this;n.get("ApiIndex/getCustom",{},(function(e){var o=n.globalData.pre_url+"/static/area.json";e.data.includes("plug_zhiming")&&(o=n.globalData.pre_url+"/static/area_gaoxin.json"),t.request({url:o,data:{},method:"GET",header:{"content-type":"application/json"},success:function(t){a.items=t.data}})})),this.getdata()},onPullDownRefresh:function(){this.getdata()},methods:{getdata:function(){var t=this,e=t.opt.id||"";n.get("ApiIndex/getCustom",{},(function(e){e.data.includes("plug_xiongmao")&&(t.showCompany=!0)})),e?(t.loading=!0,n.get("ApiAddress/addressadd",{id:e,type:t.type},(function(e){if(t.loading=!1,t.name=e.data.name,t.tel=e.data.tel,t.area=e.data.area,t.address=e.data.address,t.longitude=e.data.longitude,t.latitude=e.data.latitude,t.company=e.data.company,e.data.province)var a=e.data.province+","+e.data.city+","+e.data.district;else a="";t.regiondata=a,t.loaded()}))):t.loaded()},regionchange:function(t){var e=t.detail.value;console.log(e[0].text+","+e[1].text+","+e[2].text),this.regiondata=e[0].text+","+e[1].text+","+e[2].text},selectzuobiao:function(){console.log("selectzuobiao");var e=this;t.chooseLocation({success:function(t){console.log(t),e.area=t.address,e.address=t.name,e.latitude=t.latitude,e.longitude=t.longitude},fail:function(e){console.log(e),"chooseLocation:fail auth deny"==e.errMsg&&n.confirm("获取位置失败，请在设置中开启位置信息",(function(){t.openSetting({})}))}})},formSubmit:function(t){var e=t.detail.value,a=this.opt.id||"",o=e.name,i=e.tel,d=this.regiondata;if(1==this.type){var s=this.area;if(""==s)return void n.error("请选择位置")}else{s=d;if(""==s)return void n.error("请选择省市区")}var r=e.address,u=this.longitude,c=this.latitude,l=e.company;""!=o&&""!=i&&""!=r?(n.showLoading("提交中"),n.post("ApiAddress/addressadd",{type:this.type,addressid:a,name:o,tel:i,area:s,address:r,latitude:c,longitude:u,company:l},(function(t){n.showLoading(!1),0!=t.status?(n.success("保存成功"),setTimeout((function(){n.goback(!0)}),1e3)):n.alert(t.msg)}))):n.error("请填写完整信息")},delAddress:function(){var t=this.opt.id;n.confirm("确定要删除该收获地址吗?",(function(){n.showLoading("删除中"),n.post("ApiAddress/del",{addressid:t},(function(){n.showLoading(!1),n.success("删除成功"),setTimeout((function(){n.goback(!0)}),1e3)}))}))},bindPickerChange:function(t){var e=t.detail.value;this.regiondata=e},setaddressxx:function(t){this.addressxx=t.detail.value},shibie:function(){var t=this,e=t.addressxx;n.post("ApiAddress/shibie",{addressxx:e},(function(e){var a=0;e.province&&(a=1,t.regiondata=e.province+","+e.city+","+e.county),e.detail&&(a=1,t.address=e.detail),e.person&&(a=1,t.name=e.person),e.phonenum&&(a=1,t.tel=e.phonenum),0==a?n.error("识别失败"):n.success("识别完成")}))},getweixinaddress:function(){var t=this;a.chooseAddress({success:function(e){n.showLoading("提交中"),n.post("ApiAddress/addressadd",{type:t.type,addressid:"",name:e.userName,tel:e.telNumber,area:e.provinceName+","+e.cityName+","+e.countyName,address:e.detailInfo},(function(t){n.showLoading(!1),0!=t.status?(n.success("添加成功"),setTimeout((function(){n.goback(!0)}),1e3)):n.alert(t.msg)}))}})}}};e.default=o}).call(this,a("df3c")["default"],a("3223")["default"])},"85d6":function(t,e,a){"use strict";a.r(e);var n=a("1ced"),o=a.n(n);for(var i in n)["default"].indexOf(i)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(i);e["default"]=o.a},"9a19":function(t,e,a){"use strict";(function(t,e){var n=a("47a9");a("06e9");n(a("3240"));var o=n(a("9cf81"));t.__webpack_require_UNI_MP_PLUGIN__=a,e(o.default)}).call(this,a("3223")["default"],a("df3c")["createPage"])},"9cf81":function(t,e,a){"use strict";a.r(e);var n=a("eca0"),o=a("85d6");for(var i in o)["default"].indexOf(i)<0&&function(t){a.d(e,t,(function(){return o[t]}))}(i);a("b1255");var d=a("828b"),s=Object(d["a"])(o["default"],n["b"],n["c"],!1,null,null,null,!1,n["a"],void 0);e["default"]=s.exports},b1255:function(t,e,a){"use strict";var n=a("d96b"),o=a.n(n);o.a},d96b:function(t,e,a){},eca0:function(t,e,a){"use strict";a.d(e,"b",(function(){return o})),a.d(e,"c",(function(){return i})),a.d(e,"a",(function(){return n}));var n={uniDataPicker:function(){return Promise.all([a.e("common/vendor"),a.e("components/uni-data-picker/uni-data-picker")]).then(a.bind(null,"8157"))},loading:function(){return a.e("components/loading/loading").then(a.bind(null,"ceaa"))},dpTabbar:function(){return a.e("components/dp-tabbar/dp-tabbar").then(a.bind(null,"b875"))},popmsg:function(){return a.e("components/popmsg/popmsg").then(a.bind(null,"2bf2"))}},o=function(){var t=this,e=t.$createElement,a=(t._self._c,t.isload?"wx"==t.getplatform()&&1!=t.type:null),n=t.isload?t.t("详细地址"):null,o=t.isload?t.t("详细地址"):null,i=t.isload?t.t("color1"):null,d=t.isload?t.t("color1rgb"):null;t.$mp.data=Object.assign({},{$root:{m0:a,m1:n,m2:o,m3:i,m4:d}})},i=[]}},[["9a19","common/runtime","common/vendor"]]]);