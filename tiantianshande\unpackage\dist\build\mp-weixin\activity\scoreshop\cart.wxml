<view class="container"><block wx:if="{{isload}}"><block><block wx:if="{{$root.g0>0}}"><block><view class="cartmain"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="id"><block><view class="item"><view class="content flex-y-center"><view class="radio" style="{{(item.$orig.checked?'background:'+item.m0+';border:0':'')}}" data-index="{{index}}" data-event-opts="{{[['tap',[['changeradio',['$event']]]]]}}" catchtap="__e"><image class="radio-img" src="/static/img/checkd.png"></image></view><image class="img" src="{{item.$orig.product.pic}}" data-url="{{'/shopPackage/shop/product?id='+item.$orig.product.id}}" data-event-opts="{{[['tap',[['gotoFun',['$event']]]]]}}" bindtap="__e"></image><view class="detail"><view class="title"><text>{{item.$orig.product.name}}</text></view><view class="desc"><text>{{"价值：￥"+item.$orig.product.sell_price}}</text></view><view class="buynum flex flex-y-center"><view class="price flex1" style="{{'color:'+(item.m1)+';'}}"><block wx:if="{{item.$orig.product.money_price>0}}"><text>{{"￥"+item.$orig.product.money_price+"+"}}</text></block>{{item.$orig.product.score_price+item.m2}}</view><view class="f2 flex flex-y-center"><view class="minus flex-x-center" data-index="{{index}}" data-cartid="{{item.$orig.id}}" data-num="{{item.$orig.num}}" data-event-opts="{{[['tap',[['gwcminus',['$event']]]]]}}" bindtap="__e">-</view><input class="flex-x-center" type="number" data-max="{{item.$orig.product.stock}}" data-index="{{index}}" data-cartid="{{item.$orig.id}}" data-num="{{item.$orig.num}}" data-event-opts="{{[['blur',[['gwcinput',['$event']]]]]}}" value="{{item.$orig.num}}" bindblur="__e"/><view class="plus flex-x-center" data-index="{{index}}" data-max="{{item.$orig.product.stock}}" data-num="{{item.$orig.num}}" data-cartid="{{item.$orig.id}}" data-event-opts="{{[['tap',[['gwcplus',['$event']]]]]}}" bindtap="__e">+</view></view></view></view><view class="prodel" data-cartid="{{item.$orig.id}}" data-event-opts="{{[['tap',[['cartdelete',['$event']]]]]}}" bindtap="__e"><image src="/static/img/del.png"></image></view></view></view></block></block></view><view style="height:auto;position:relative;"><view style="width:100%;height:110rpx;"></view><view class="{{['footer','flex',menuindex>-1?'tabbarbot':'notabbarbot']}}"><view class="text1">合计：</view><view class="text2" style="{{'color:'+($root.m3)+';'}}"><block wx:if="{{totalmoney>0}}"><text>{{"￥"+totalmoney+"+"}}</text></block><text>{{totalscore+$root.m4}}</text></view><view class="flex1"></view><view data-event-opts="{{[['tap',[['toOrder',['$event']]]]]}}" class="op" style="{{'background:'+('linear-gradient(-90deg,'+$root.m5+' 0%,rgba('+$root.m6+',0.8) 100%)')+';'}}" bindtap="__e">去结算</view></view></view></block></block><block wx:else><block><view class="data-empty"><image class="data-empty-img" style="width:120rpx;height:120rpx;" src="{{pre_url+'/static/img/cartnull.png'}}"></image><view class="data-empty-text" style="margin-top:20rpx;font-size:24rpx;">购物车空空如也~</view><button style="width:400rpx;border:1px solid #ff6801;border-radius:6rpx;background:#ff6801;margin-top:20px;color:#fff;" data-url="index" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">去选购</button></view></block></block></block></block><block wx:if="{{loading}}"><loading vue-id="0ae2b506-1" bind:__l="__l"></loading></block><dp-tabbar vue-id="0ae2b506-2" opt="{{opt}}" data-event-opts="{{[['^getmenuindex',[['getmenuindex']]]]}}" bind:getmenuindex="__e" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="0ae2b506-3" data-ref="popmsg" bind:__l="__l"></popmsg></view>