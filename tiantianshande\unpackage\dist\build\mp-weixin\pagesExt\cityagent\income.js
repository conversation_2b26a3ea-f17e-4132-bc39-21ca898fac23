require('../common/vendor.js');(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pagesExt/cityagent/income"],{"00b0":function(t,e,n){"use strict";n.r(e);var a=n("a846"),i=n("e6d4");for(var o in i)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(o);n("52c7");var s=n("828b"),d=Object(s["a"])(i["default"],a["b"],a["c"],!1,null,null,null,!1,a["a"],void 0);e["default"]=d.exports},"52c7":function(t,e,n){"use strict";var a=n("71ad"),i=n.n(a);i.a},"71ad":function(t,e,n){},a846:function(t,e,n){"use strict";n.d(e,"b",(function(){return i})),n.d(e,"c",(function(){return o})),n.d(e,"a",(function(){return a}));var a={ddTab:function(){return n.e("components/dd-tab/dd-tab").then(n.bind(null,"caa1"))},nodata:function(){return n.e("components/nodata/nodata").then(n.bind(null,"101c"))},nomore:function(){return n.e("components/nomore/nomore").then(n.bind(null,"3892"))},loading:function(){return n.e("components/loading/loading").then(n.bind(null,"ceaa"))},dpTabbar:function(){return n.e("components/dp-tabbar/dp-tabbar").then(n.bind(null,"b875"))},popmsg:function(){return n.e("components/popmsg/popmsg").then(n.bind(null,"2bf2"))}},i=function(){var t=this,e=t.$createElement,n=(t._self._c,t.isload&&0==t.st?t.__map(t.datalist,(function(e,n){var a=t.__get_orig(e),i=t.getIncomeTypeName(e.income_type);return{$orig:a,m0:i}})):null),a=t.isload&&1==t.st?t.__map(t.datalist,(function(e,n){var a=t.__get_orig(e),i=t.getWithdrawTypeName(e.withdraw_type);return{$orig:a,m1:i}})):null;t.$mp.data=Object.assign({},{$root:{l0:n,l1:a}})},o=[]},c697:function(t,e,n){"use strict";(function(t){var a=n("47a9");Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var i=a(n("7ca3")),o=getApp(),s={data:function(){return{opt:{},loading:!1,isload:!1,nodata:!1,nomore:!1,st:"0",page:1,limit:20,datalist:[],statistics:{total_income:"0.00",today_income:"0.00",month_income:"0.00"},incomeTypeIndex:0,incomeTypeList:[{key:"all",name:"全部类型"},{key:"1",name:"订单佣金"},{key:"2",name:"推荐奖励"},{key:"3",name:"平台奖励"},{key:"4",name:"其他收益"}],startDate:"",endDate:""}},onLoad:function(t){this.opt=o.getopts(t),this.st=t.st||"0",this.getdata()},onPullDownRefresh:function(){this.page=1,this.getdata()},onReachBottom:function(){this.nomore||(this.page++,this.getdata(!0))},methods:{getdata:function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0],n=this;e||(n.loading=!0,n.nodata=!1,n.nomore=!1);var a={page:n.page,limit:n.limit};"0"==n.st&&(n.incomeTypeIndex>0&&(a.income_type=n.incomeTypeList[n.incomeTypeIndex].key),n.startDate&&(a.start_time=n.startDate),n.endDate&&(a.end_time=n.endDate));var i="0"==n.st?"ApiCityAgent/getIncomeList":"ApiCityAgent/getWithdrawList";o.get(i,a,(function(a){n.loading=!1,t.stopPullDownRefresh(),0!=a.status?(t.setNavigationBarTitle({title:"0"==n.st?"收益明细":"转换记录"}),a.statistics&&(n.statistics=a.statistics),n.datalist=e?n.datalist.concat(a.list):a.list,a.list.length<n.limit&&(n.nomore=!0),0===n.datalist.length&&(n.nodata=!0),n.loaded()):o.error(a.msg)}))},changetab:function(t){this.st=t,this.page=1,this.datalist=[],this.getdata()},onIncomeTypeChange:function(t){this.incomeTypeIndex=t.detail.value,this.page=1,this.getdata()},onStartDateChange:function(t){this.startDate=t.detail.value,this.page=1,this.getdata()},onEndDateChange:function(t){this.endDate=t.detail.value,this.page=1,this.getdata()},getIncomeTypeName:function(t){var e;if("string"===typeof t&&isNaN(t))return t;var n=(e={1:"订单佣金",2:"推荐奖励",3:"平台奖励",4:"其他收益"},(0,i.default)(e,"1","订单佣金"),(0,i.default)(e,"2","推荐奖励"),(0,i.default)(e,"3","平台奖励"),(0,i.default)(e,"4","其他收益"),e);return n[t]||t||"未知"},getWithdrawTypeName:function(t){return{weixin:"微信钱包",alipay:"支付宝",bank:"银行卡",commission:"余额转换"}[t]||"未知"},loaded:function(){this.isload=!0}}};e.default=s}).call(this,n("df3c")["default"])},e6d4:function(t,e,n){"use strict";n.r(e);var a=n("c697"),i=n.n(a);for(var o in a)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(o);e["default"]=i.a},ff81:function(t,e,n){"use strict";(function(t,e){var a=n("47a9");n("06e9");a(n("3240"));var i=a(n("00b0"));t.__webpack_require_UNI_MP_PLUGIN__=n,e(i.default)}).call(this,n("3223")["default"],n("df3c")["createPage"])}},[["ff81","common/runtime","common/vendor"]]]);