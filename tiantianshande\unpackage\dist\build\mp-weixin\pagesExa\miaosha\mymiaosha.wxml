<view class="container"><block wx:if="{{isload}}"><block><dd-tab vue-id="11166e14-1" itemdata="{{['已结束','抢购中','即将开始','待审核']}}" itemst="{{['0','1','2','3']}}" st="{{st}}" isfixed="{{true}}" data-event-opts="{{[['^changetab',[['changetab']]]]}}" bind:changetab="__e" bind:__l="__l"></dd-tab><view style="width:100%;height:100rpx;"></view><view class="content"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="item"><image class="f1" style="height:150px;" src="{{item.$orig.pic}}" data-url="{{'product?id='+item.$orig.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"></image><view class="f2"><text class="t1">{{item.$orig.name}}</text><view class="t2" style="{{'color:'+(item.m0)+';'}}"><text>{{"售卖人:"+item.$orig.outname}}</text></view><view class="t2" style="{{'color:'+(item.m1)+';'}}"><text>{{"开始时间:"+item.$orig.miaosha_date}}</text></view><view class="t2" style="{{'color:'+(item.m2)+';'}}"><block wx:if="{{st==2&&item.$orig.qiang==1}}"><text>{{"可提前开始时间:"+item.$orig.miaosha_date2}}</text></block></view><block wx:if="{{st!=3}}"><view class="t3"><text class="x1" style="{{'color:'+(item.m3)+';'}}"><text style="font-size:24rpx;">￥</text>{{item.$orig.sell_price}}</text></view></block><block wx:if="{{st==3}}"><view class="t3"><button class="x3" style="{{'background:'+(item.m4)+';'}}" data-url="{{'shenhe?id='+item.$orig.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">去审核</button></view></block></view></view></block><block wx:if="{{nodata}}"><view class="item" style="display:block;"><nodata vue-id="11166e14-2" bind:__l="__l"></nodata></view></block><block wx:if="{{nomore}}"><nomore vue-id="11166e14-3" bind:__l="__l"></nomore></block></view></block></block><block wx:if="{{loading}}"><loading vue-id="11166e14-4" bind:__l="__l"></loading></block><dp-tabbar vue-id="11166e14-5" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="11166e14-6" data-ref="popmsg" bind:__l="__l"></popmsg></view>