<view class="container"><block wx:if="{{isload}}"><block><view class="content"><block wx:if="{{st==0}}"><block><block wx:for="{{datalist}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="item"><view class="f1"><text class="t1">{{item.date}}</text></view><view class="f2"><block wx:if="{{item.money>0}}"><text class="t1">{{"+"+item.money}}</text></block><block wx:else><text class="t1">{{item.num}}</text></block></view></view></block></block></block></view></block></block><block wx:if="{{nodata}}"><nodata vue-id="7734506f-1" bind:__l="__l"></nodata></block><block wx:if="{{nomore}}"><nomore vue-id="7734506f-2" bind:__l="__l"></nomore></block><block wx:if="{{loading}}"><loading vue-id="7734506f-3" bind:__l="__l"></loading></block><dp-tabbar vue-id="7734506f-4" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="7734506f-5" data-ref="popmsg" bind:__l="__l"></popmsg></view>