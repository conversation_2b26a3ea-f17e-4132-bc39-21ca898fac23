<view class="job-detail"><block wx:if="{{loading}}"><view class="loading-wrapper"><view class="loading-spinner"></view></view></block><block wx:else><block><view class="header"><view class="title-wrapper"><text class="title">{{jobInfo.title}}</text><text class="{{['status',(jobInfo.status===1)?'status-active':'']}}">{{''+(jobInfo.status===1?'招聘中':'已结束')+''}}</text></view><view class="salary" style="{{'color:'+($root.m0)+';'}}">{{jobInfo.salary}}</view><view class="basic-info"><text><text class="iconfont icon-location"></text>{{jobInfo.work_address}}</text><text><text class="iconfont icon-education"></text>{{jobInfo.education}}</text><text><text class="iconfont icon-experience"></text>{{jobInfo.experience}}</text></view><scroll-view class="tags-scroll" scroll-x="{{true}}" show-scrollbar="false"><view class="tags"><block wx:for="{{jobInfo.formatted_options}}" wx:for-item="tags" wx:for-index="type" wx:key="type"><text class="tag"><block wx:for="{{tags}}" wx:for-item="tag" wx:for-index="index" wx:key="index"><text>{{tag}}</text></block></text></block></view></scroll-view></view><view class="company" hover-class="company-hover" data-event-opts="{{[['tap',[['goToCompany',['$event']]]]]}}" bindtap="__e"><image class="logo" src="{{jobInfo.company_logo}}" mode="aspectFit"></image><view class="company-info"><view class="name">{{jobInfo.company_name}}</view><view class="desc"><text>{{getCompanyNature}}</text><text>{{getCompanyScale}}</text></view></view><text class="iconfont icon-arrow-right"></text></view><scroll-view class="content-scroll" scroll-y="{{true}}"><view class="section"><view class="section-title"><text class="iconfont icon-info"></text>工作信息</view><view class="info-list"><view class="info-item"><text class="label">工作方式</text><text class="value">{{jobInfo.work_mode}}</text></view><view class="info-item"><text class="label">工作强度</text><text class="value">{{jobInfo.work_intensity}}</text></view><view class="info-item"><text class="label">工作时间</text><text class="value">{{jobInfo.work_time_type}}</text></view><view class="info-item"><text class="label">结算方式</text><text class="value">{{jobInfo.payment}}</text></view><view class="info-item"><text class="label">招聘人数</text><text class="value highlight">{{jobInfo.numbers+"人"}}</text></view></view></view><view class="section"><view class="section-title"><text class="iconfont icon-description"></text>职位描述</view><rich-text class="rich-content" nodes="{{jobInfo.description}}"></rich-text></view><view class="section"><view class="section-title"><text class="iconfont icon-requirement"></text>任职要求</view><rich-text class="rich-content" nodes="{{jobInfo.requirement}}"></rich-text></view><view class="section"><view class="section-title"><text class="iconfont icon-welfare"></text>工作福利</view><rich-text class="rich-content" nodes="{{jobInfo.benefits}}"></rich-text></view><view style="height:120rpx;"></view></scroll-view><view class="footer"><view data-event-opts="{{[['tap',[['handleCollect',['$event']]]]]}}" class="collect" bindtap="__e"><text class="{{['iconfont',isCollected?'icon-heart-fill':'icon-heart']}}"></text><text>{{isCollected?'已收藏':'收藏'}}</text></view><button class="{{['apply-btn',(jobInfo.status!==1)?'apply-btn-disabled':'',(hasApplied)?'apply-btn-applied':'']}}" style="{{'background:'+(hasApplied?'#52c41a':'linear-gradient(135deg, '+$root.m1+' 0%, rgba('+$root.m2+',0.8) 100%)')+';'+('box-shadow:'+(hasApplied?'0 8rpx 24rpx rgba(82, 196, 26, 0.25)':'0 8rpx 24rpx rgba('+$root.m3+',0.25)')+';')}}" disabled="{{jobInfo.status!==1||hasApplied}}" data-event-opts="{{[['tap',[['handleApply',['$event']]]]]}}" bindtap="__e"><block wx:if="{{jobInfo.status===1}}"><block>{{''+(hasApplied?'已报名':'立即报名')+''}}</block></block><block wx:else><block>已结束</block></block></button></view></block></block><block wx:if="{{reportVisible}}"><view class="report-dialog"><view data-event-opts="{{[['tap',[['closeReportDialog',['$event']]]]]}}" class="dialog-mask" bindtap="__e"></view><view class="dialog-content"><view class="dialog-header"><text class="dialog-title">职位报名</text><text data-event-opts="{{[['tap',[['closeReportDialog',['$event']]]]]}}" class="dialog-close iconfont icon-close" bindtap="__e"></text></view><view class="dialog-job-info"><text class="job-title">{{jobInfo.title}}</text><text class="job-salary">{{jobInfo.salary}}</text><text class="job-company">{{jobInfo.company_name}}</text></view><scroll-view class="dialog-form" scroll-y="{{true}}"><view class="form-section"><view class="form-title">基本信息</view><view class="form-item"><text class="label">姓名</text><input class="input" placeholder="请输入真实姓名" maxlength="{{20}}" data-event-opts="{{[['input',[['__set_model',['$0','name','$event',[]],['formData']]]]]}}" value="{{formData.name}}" bindinput="__e"/></view><view class="form-item"><text class="label">手机号</text><input class="input" type="number" placeholder="请输入手机号" maxlength="{{11}}" data-event-opts="{{[['input',[['__set_model',['$0','phone','$event',[]],['formData']]]]]}}" value="{{formData.phone}}" bindinput="__e"/></view></view></scroll-view><view class="dialog-footer"><checkbox-group data-event-opts="{{[['change',[['handleAgreementChange',['$event']]]]]}}" class="agreement" bindchange="__e"><checkbox checked="{{formData.agreement}}"></checkbox><text>我已阅读并同意</text><text data-event-opts="{{[['tap',[['showAgreement',['$event']]]]]}}" class="link" bindtap="__e">《用户服务协议》</text></checkbox-group><button class="submit-btn" style="{{'background:'+('linear-gradient(135deg, '+$root.m4+' 0%, rgba('+$root.m5+',0.8) 100%)')+';'}}" disabled="{{!isFormValid||submitting}}" data-event-opts="{{[['tap',[['submitApply',['$event']]]]]}}" bindtap="__e">{{''+(submitting?'提交中...':'确认报名')+''}}</button></view></view></view></block><block wx:if="{{agreementVisible}}"><view class="agreement-dialog"><view data-event-opts="{{[['tap',[['closeAgreement',['$event']]]]]}}" class="dialog-mask" bindtap="__e"></view><view class="dialog-content"><view class="dialog-header"><text class="dialog-title">用户服务协议</text><text data-event-opts="{{[['tap',[['closeAgreement',['$event']]]]]}}" class="dialog-close iconfont icon-close" bindtap="__e"></text></view><scroll-view class="agreement-content" scroll-y="{{true}}"><rich-text nodes="{{agreementContent}}"></rich-text></scroll-view><view class="dialog-footer"><button data-event-opts="{{[['tap',[['closeAgreement',['$event']]]]]}}" class="confirm-btn" style="{{'background:'+($root.m6)+';'}}" bindtap="__e">我知道了</button></view></view></view></block></view>