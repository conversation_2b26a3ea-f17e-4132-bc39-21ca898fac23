<view class="container"><block wx:if="{{isload}}"><block><view class="bill-select"><picker mode="selector" range="{{billTypes}}" data-event-opts="{{[['change',[['onBillTypeChange',['$event']]]]]}}" bindchange="__e"><view class="picker"><text>{{billTypeLabel}}</text></view></picker><block wx:if="{{billType==='annual'||billType==='monthly'}}"><picker mode="selector" range="{{years}}" data-event-opts="{{[['change',[['onYearChange',['$event']]]]]}}" bindchange="__e"><view class="picker"><text>{{selectedYearLabel}}</text></view></picker></block><block wx:if="{{billType==='monthly'}}"><picker mode="selector" range="{{months}}" data-event-opts="{{[['change',[['onMonthChange',['$event']]]]]}}" bindchange="__e"><view class="picker"><text>{{selectedMonthLabel}}</text></view></picker></block></view><block wx:if="{{showBillSummary}}"><view class="bill-summary"><text>{{"累计消费金额："+totalAmount+" 元"}}</text><text>{{"购买次数："+orderCount+" 次"}}</text></view></block><view class="order-content"><block wx:for="{{datalist}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><view class="order-box" data-url="{{'detail?id='+item.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="head"><block wx:if="{{item.bid!=0}}"><view class="f1" data-url="{{'/pagesExt/business/index?id='+item.bid}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e"><image src="/static/img/ico-shop.png"></image>{{''+item.binfo.name}}</view></block><block wx:else><view class="f1"><image class="logo-row" src="{{item.binfo.logo}}"></image>{{''+item.binfo.name}}</view></block><view class="flex1"></view><block wx:if="{{item.status==0}}"><text class="st0">待付款</text></block><block wx:if="{{item.status==1&&item.freight_type!=1}}"><text class="st1">待发货</text></block><block wx:if="{{item.status==1&&item.freight_type==1}}"><text class="st1">待提货</text></block><block wx:if="{{item.status==2}}"><text class="st2">待收货</text></block><block wx:if="{{item.status==3}}"><text class="st3">已完成</text></block><block wx:if="{{item.status==4}}"><text class="st4">已关闭</text></block></view><block wx:for="{{item.prolist}}" wx:for-item="item2" wx:for-index="idx" wx:key="idx"><block><block wx:if="{{item2.is_yh==0}}"><view class="content" style="{{(idx+1==item.procount?'border-bottom:none':'')}}"><view data-url="{{'/shopPackage/shop/product?id='+item2.proid}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e"><image src="{{item2.pic}}"></image></view><view class="detail"><text class="t1">{{item2.name}}</text><text class="t2">{{item2.ggname}}</text><view class="t3"><text class="x1 flex1">{{"￥"+item2.sell_price}}</text><text class="x2">{{"×"+item2.num}}</text></view></view></view></block><block wx:if="{{item2.is_yh==1}}"><view class="content" style="{{(idx+1==item.procount?'border-bottom:none':'')}}"><view data-url="{{'/shopPackage/shop/product?id='+item2.proid}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e"><image src="{{item2.pic}}"></image></view><view class="detail"><text class="t1">{{item2.name}}</text><text class="t2">{{item2.ggname}}</text><view class="t3"><text class="x1 flex1">{{"￥"+item2.yh_prices+"(优惠价格)"}}</text><text class="x2">{{"×"+item2.yh_nums}}</text></view></view></view></block></block></block><view class="bottom"><text class="t2">{{item.typenamename}}</text></view><view class="bottom"><text>{{"共计"+item.procount+"件商品 实付:￥"+item.totalprice+''}}<block wx:if="{{item.balance_price>0&&item.balance_pay_status==0}}"><label style="display:block;float:right;" class="_span">{{"尾款：￥"+item.balance_price}}</label></block></text><block wx:if="{{item.refund_status==1}}"><text style="color:red;padding-left:6rpx;">{{"退款中￥"+item.refund_money}}</text></block><block wx:if="{{item.refund_status==2}}"><text style="color:red;padding-left:6rpx;">{{"已退款￥"+item.refund_money}}</text></block><block wx:if="{{item.refund_status==3}}"><text style="color:red;padding-left:6rpx;">退款申请已驳回</text></block></view><block wx:if="{{item.tips!=''}}"><view class="bottom"><text style="color:red;">{{item.tips}}</text></view></block></view></block></block></view><block wx:if="{{nomore}}"><nomore vue-id="63e725d0-1" bind:__l="__l"></nomore></block><block wx:if="{{nodata}}"><nodata vue-id="63e725d0-2" bind:__l="__l"></nodata></block><uni-popup class="vue-ref" vue-id="63e725d0-3" id="dialogHxqr" type="dialog" data-ref="dialogHxqr" bind:__l="__l" vue-slots="{{['default']}}"><view class="hxqrbox"><image class="img" src="{{hexiao_qr}}" data-url="{{hexiao_qr}}" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image><view class="txt">请出示核销码给核销员进行核销</view><view data-event-opts="{{[['tap',[['closeHxqr',['$event']]]]]}}" class="close" bindtap="__e"><image style="width:100%;height:100%;" src="/static/img/close2.png"></image></view></view></uni-popup><uni-popup class="vue-ref" vue-id="63e725d0-4" id="dialogSelectExpress" type="dialog" data-ref="dialogSelectExpress" bind:__l="__l" vue-slots="{{['default']}}"><block wx:if="{{express_content}}"><view style="background:#fff;padding:20rpx 30rpx;border-radius:10rpx;width:600rpx;"><block wx:for="{{express_content}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="sendexpress" style="border-bottom:1px solid #f5f5f5;padding:20rpx 0;"><view class="sendexpress-item" style="display:flex;" data-url="{{'/pagesExt/order/logistics?express_com='+item.express_com+'&express_no='+item.express_no}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="flex1" style="color:#121212;">{{item.express_com+" - "+item.express_no}}</view><image style="width:30rpx;height:30rpx;" src="/static/img/arrowright.png"></image></view><block wx:if="{{item.express_oglist}}"><view style="margin-top:20rpx;"><block wx:for="{{item.express_oglist}}" wx:for-item="item2" wx:for-index="index2" wx:key="index2"><view class="oginfo-item" style="display:flex;align-items:center;margin-bottom:10rpx;"><image style="width:50rpx;height:50rpx;margin-right:10rpx;flex-shrink:0;" src="{{item2.pic}}"></image><view class="flex1" style="color:#555;">{{item2.name+"("+item2.ggname+")"}}</view></view></block></view></block></view></block></view></block></uni-popup></block></block><block wx:if="{{loading}}"><loading vue-id="63e725d0-5" bind:__l="__l"></loading></block><dp-tabbar vue-id="63e725d0-6" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="63e725d0-7" data-ref="popmsg" bind:__l="__l"></popmsg><uni-popup class="vue-ref" vue-id="63e725d0-8" type="center" data-ref="more_one" bind:__l="__l" vue-slots="{{['default']}}"><uni-popup-dialog vue-id="{{('63e725d0-9')+','+('63e725d0-8')}}" mode="input" message="成功消息" duration="{{2000}}" valueType="number" before-close="{{true}}" data-event-opts="{{[['^close',[['close']]],['^confirm',[['confirm']]]]}}" bind:close="__e" bind:confirm="__e" bind:__l="__l"></uni-popup-dialog></uni-popup></view>