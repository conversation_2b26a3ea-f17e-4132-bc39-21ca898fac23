<view class="container"><block wx:if="{{isload}}"><block><view class="topsearch flex-y-center"><view class="f1 flex-y-center"><image class="img" src="/static/img/search_ico.png"></image><input placeholder="搜索文件" placeholder-style="font-size:24rpx;color:#C2C2C2" data-event-opts="{{[['confirm',[['searchConfirm',['$event']]]]]}}" value="{{keyword}}" bindconfirm="__e"/></view></view><block wx:if="{{$root.g0>0}}"><dd-tab vue-id="4d960f4b-1" itemdata="{{cnamelist}}" itemst="{{cidlist}}" st="{{cid}}" isfixed="{{false}}" data-event-opts="{{[['^changetab',[['changetab']]]]}}" bind:changetab="__e" bind:__l="__l"></dd-tab></block><view class="filem_list"><block wx:for="{{datalist}}" wx:for-item="item" wx:for-index="index" wx:key="id"><view class="filem-item" data-url="{{'/pagesExa/filem/detail?id='+item.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="filem-icon"><image class="image" src="{{'/static/img/filetypes/'+(item.filetype_icon||'file')+'.png'}}" mode="aspectFit"></image></view><view class="filem-info"><view class="p1">{{item.name}}</view><block wx:if="{{item.subname}}"><view class="p2">{{item.subname}}</view></block><view class="p3"><text class="filesize">{{item.filesize_text}}</text><text class="filedate">{{item.createtime}}</text><block wx:if="{{item.showviewcount}}"><text class="fileview">{{"查看: "+item.viewcount}}</text></block><block wx:if="{{item.showdownloads}}"><text class="filedown">{{"下载: "+item.downloads}}</text></block></view></view></view></block></view></block></block><block wx:if="{{nodata}}"><nodata vue-id="4d960f4b-2" bind:__l="__l"></nodata></block><block wx:if="{{nomore}}"><nomore vue-id="4d960f4b-3" bind:__l="__l"></nomore></block><block wx:if="{{loading}}"><loading vue-id="4d960f4b-4" bind:__l="__l"></loading></block><dp-tabbar vue-id="4d960f4b-5" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="4d960f4b-6" data-ref="popmsg" bind:__l="__l"></popmsg></view>