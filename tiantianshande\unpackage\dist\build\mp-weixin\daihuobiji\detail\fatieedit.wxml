<view><block wx:if="{{isload}}"><block><form data-event-opts="{{[['submit',[['formsubmit',['$event']]]]]}}" bindsubmit="__e"><view class="st_box"><view class="st_title flex-y-center" style="position:sticky;top:0rpx;background-color:#fff;z-index:3;justify-content:space-between;"><view data-event-opts="{{[['tap',[['goback',['$event']]]]]}}" style="width:100rpx;" bindtap="__e"><image src="{{pre_url+'/static/img/goback.jpg'}}"></image></view><view><image style="width:40rpx;" src="{{pre_url+'/static/icon/warning.png'}}" mode="widthFix"></image></view></view><view class="st_form"><view class="flex" style="flex-wrap:wrap;"><block wx:for="{{pics}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="layui-imgbox"><view class="layui-imgbox-close" data-index="{{index}}" data-field="pics" data-event-opts="{{[['tap',[['removeimg',['$event']]]]]}}" bindtap="__e"><image src="{{pre_url+'/static/img/ico-del.png'}}"></image></view><view class="layui-imgbox-img"><image src="{{item}}" data-url="{{item}}" mode="widthFix" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image></view></view></block><block wx:if="{{$root.g0<9}}"><view data-event-opts="{{[['tap',[['uploadimg',['$event']]]]]}}" class="uploadbtn" style="{{('background:url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 75rpx;background-size:50rpx 50rpx;background-color:#F3F3F3;')}}" bindtap="__e"></view></block></view><view style="margin:20rpx 0;"><input style="height:100rpx;" placeholder="填写标题会有更多赞哦~" name="title" data-event-opts="{{[['input',[['__set_model',['','title','$event',[]]]]]]}}" value="{{title}}" bindinput="__e"/></view><view style="border-bottom:1px solid #EEEEEE;padding-bottom:20rpx;"><textarea class="feedback-textarea" placeholder="添加正文" name="content" maxlength="-1" data-event-opts="{{[['input',[['__set_model',['','content','$event',[]]]]]]}}" value="{{content}}" bindinput="__e"></textarea><view><scroll-view style="white-space:nowrap;width:100%;" scroll-x="true"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['cateChange',[index]]]]]}}" class="tag-box" style="{{'display:'+('inline-block')+';'+('color:'+(cindex===index?'#fff':'#999999')+';')+('background:'+(cindex===index?item.m0:'transparent')+';')}}" bindtap="__e">{{''+item.$orig+''}}</view></block></scroll-view></view></view><input type="text" hidden="true" name="pics" maxlength="-1" value="{{pics}}"/><input type="text" hidden="true" name="video" maxlength="-1" value="{{video}}"/><view class="picker-container"><view data-event-opts="{{[['tap',[['openShop',['$event']]]]]}}" class="picker-label" bindtap="__e"><image style="width:26rpx;margin-right:10rpx;" src="{{pre_url+'/static/img/shortvideo_cart.png'}}" mode="widthFix"></image>{{''+(selectedObj.name?selectedObj.name:'选择商品')+''}}</view><view data-event-opts="{{[['tap',[['selectProduct',['$event']]]]]}}" class="picker-text" bindtap="__e">{{''+($root.g1>0?$root.g2+'件商品':'请选择商品')+''}}</view><image style="width:26rpx;" src="{{pre_url+'/static/icon/right.png'}}" mode="widthFix" data-event-opts="{{[['tap',[['selectProduct',['$event']]]]]}}" bindtap="__e"></image></view></view></view><view class="st_title flex-y-center" style="{{'padding-bottom:'+(safeBottomHeight+80+'rpx')+';'}}"><button style="{{('background:'+$root.m1)}}" form-type="submit">保存修改</button></view><view style="width:100%;height:50rpx;"></view></form></block></block><block wx:if="{{loading}}"><loading vue-id="70ec74a0-1" bind:__l="__l"></loading></block><dp-tabbar vue-id="70ec74a0-2" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="70ec74a0-3" data-ref="popmsg" bind:__l="__l"></popmsg><uni-popup class="vue-ref" vue-id="70ec74a0-4" mask-click="{{false}}" data-ref="videoShow" bind:__l="__l" vue-slots="{{['default']}}"><view class="viewVideo"><view data-event-opts="{{[['tap',[['fullscreenchange',['$event']]]]]}}" class="close" bindtap="__e"></view><video id="myVideo" src="{{cVideo}}" object-fit="contain" autoplay="false" controls="{{true}}" show-fullscreen-btn="false" play-btn-position="center" show-loading="true" data-event-opts="{{[['fullscreenchange',[['fullscreenchange',['$event']]]]]}}" bindfullscreenchange="__e"></video></view></uni-popup><uni-popup class="vue-ref" vue-id="70ec74a0-5" type="bottom" data-ref="shopShow" bind:__l="__l" vue-slots="{{['default']}}"><view class="viewShop"><view style="text-align:center;font-weight:bold;margin-bottom:20rpx;position:sticky;top:0;">{{'文中提到的商品（'+$root.g3+'）'}}</view><view class="cart-container"><scroll-view style="height:50vh;" scroll-top="{{0}}" scroll-y="true"><block wx:for="{{selectedObj.matchedData}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="cart-item" data-url="{{'/shopPackage/shop/product?id='+item.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="image-box"><image class="item-image" src="{{item.pic}}" mode="heightFix"></image></view><view class="item-info"><text class="item-name">{{item.name}}</text><view style="display:flex;justify-content:space-between;width:100%;"><text class="item-price"><label class="_span">￥</label><label style="font-size:34rpx;font-weight:bold;" class="_span">{{item.sell_price}}</label></text></view></view></view></block></scroll-view><view style="position:sticky;bottom:0;height:100rpx;display:flex;justify-content:space-between;align-items:center;padding:0 20rpx;"><view data-event-opts="{{[['tap',[['shopAllSelectedClick',['$event']]]]]}}" style="visibility:hidden;" bindtap="__e"><checkbox style="transform:scale(0.8);" checked="{{shopAllSelected}}"></checkbox>全选</view><view data-event-opts="{{[['tap',[['closePop',['$event']]]]]}}" class="shopButtonActive" bindtap="__e">点击关闭</view></view></view></view></uni-popup></view>