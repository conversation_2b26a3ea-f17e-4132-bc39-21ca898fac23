<view style="width:100%;"><view class="dp-qiuzhi-itemlist"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="id"><view class="item" data-url="{{'/zhaopin/qiuzhi/detail?id='+item.$orig[idfield]}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="item1 flex"><view class="product-pic"><text class="{{['status','st'+item.$orig.has_job]}}">{{item.$orig.has_job==1?'在职':'离职'}}</text><image class="image" style="{{('filter: blur('+item.$orig.mohu+'px);-webkit-filter: blur('+item.$orig.mohu+'px);-moz-filter: blur('+item.$orig.mohu+'px)')}}" src="{{item.$orig.thumb}}" mode="aspectFit"></image><block wx:if="{{item.$orig.qianyue_id>0}}"><text class="qianyue">签约保障中</text></block><block wx:else><block><block wx:if="{{item.$orig.apply_id>0}}"><text class="qianyue">认证保障中</text></block><block wx:else><text data-event-opts="{{[['tap',[['gorenzheng',['$event']]]]]}}" class="norenzheng" catchtap="__e">未认证</text></block></block></block></view><view class="product-info"><block wx:if="{{showname==1}}"><view class="p1">{{''+item.$orig.name+''}}<block wx:if="{{item.$orig.sex==1}}"><image src="../../static/img/nan.png"></image></block><block wx:if="{{item.$orig.sex==2}}"><image src="../../static/img/nv.png"></image></block></view></block><view class="p2"><text>期望薪资：</text><text class="number">{{item.$orig.salary}}</text></view><view class="p2"><text>期望岗位：</text><text>{{item.$orig.cnames}}</text></view><view class="p2"><text>期望城市：</text><text>{{item.$orig.area}}</text></view></view></view><block wx:if="{{item.g0}}"><view class="item2 flex"><block wx:for="{{item.$orig.tags}}" wx:for-item="wf" wx:for-index="wk" wx:key="wk"><view class="tagitem">{{wf}}</view></block></view></block></view></block></view><block wx:if="{{buydialogShow}}"><buydialog vue-id="e73895bc-1" proid="{{proid}}" menuindex="{{menuindex}}" data-event-opts="{{[['^buydialogChange',[['buydialogChange']]]]}}" bind:buydialogChange="__e" bind:__l="__l"></buydialog></block></view>