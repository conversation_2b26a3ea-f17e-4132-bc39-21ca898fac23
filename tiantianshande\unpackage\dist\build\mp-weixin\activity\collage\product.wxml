<view class="container"><block wx:if="{{isload}}"><block><block wx:if="{{showtoptabbar==1&&toptabbar_show==1}}"><view class="toptabbar_tab"><view class="{{['item',toptabbar_index==0?'on':'']}}" style="{{'color:'+(toptabbar_index==0?$root.m0:'#333')+';'}}" data-index="0" data-event-opts="{{[['tap',[['changetoptab',['$event']]]]]}}" bindtap="__e">商品<view class="after" style="{{'background:'+($root.m1)+';'}}"></view></view><view class="{{['item',toptabbar_index==1?'on':'']}}" style="{{'color:'+(toptabbar_index==1?$root.m2:'#333')+';'}}" data-index="1" data-event-opts="{{[['tap',[['changetoptab',['$event']]]]]}}" bindtap="__e">评价<view class="after" style="{{'background:'+($root.m3)+';'}}"></view></view><view class="{{['item',toptabbar_index==2?'on':'']}}" style="{{'color:'+(toptabbar_index==2?$root.m4:'#333')+';'}}" data-index="2" data-event-opts="{{[['tap',[['changetoptab',['$event']]]]]}}" bindtap="__e">详情<view class="after" style="{{'background:'+($root.m5)+';'}}"></view></view><block wx:if="{{$root.g0>0}}"><view class="{{['item',toptabbar_index==3?'on':'']}}" style="{{'color:'+(toptabbar_index==3?$root.m6:'#333')+';'}}" data-index="3" data-event-opts="{{[['tap',[['changetoptab',['$event']]]]]}}" bindtap="__e">推荐<view class="after" style="{{'background:'+($root.m7)+';'}}"></view></view></block></view></block><scroll-view style="height:100%;overflow:scroll;" scrollIntoView="{{scrollToViewId}}" scrollTop="{{scrollTop}}" scroll-y="{{true}}" data-event-opts="{{[['scroll',[['scroll',['$event']]]]]}}" bindscroll="__e"><view id="scroll_view_tab0"><view class="swiper-container"><swiper class="swiper" indicator-dots="{{false}}" autoplay="{{true}}" interval="{{5000}}" data-event-opts="{{[['change',[['swiperChange',['$event']]]]]}}" bindchange="__e"><block wx:for="{{product.pics}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><swiper-item class="swiper-item"><view class="swiper-item-view"><image class="img" src="{{item}}" mode="widthFix"></image></view></swiper-item></block></block></swiper><view class="imageCount">{{current+1+"/"+$root.g1}}</view></view><view class="collage_title"><view class="f1"><view class="t1"><view class="x1">￥</view><view class="x2">{{product.sell_price}}</view><view class="x3">{{product.teamnum+"人团"}}</view></view><view class="t2">{{"单买价：￥"+product.market_price}}</view></view><view class="f2">{{"已拼"+product.sales+"件"}}</view></view><block wx:if="{{shopset.detail_guangao1}}"><view style="background:#fff;width:100%;height:auto;padding:20rpx 20rpx 0;"><block wx:if="{{shopset.detail_guangao1}}"><image style="width:100%;height:auto;" src="{{shopset.detail_guangao1}}" mode="widthFix" data-event-opts="{{[['tap',[['showgg1Dialog',['$event']]]]]}}" bindtap="__e"></image></block></view></block><block wx:if="{{shopset.detail_guangao1&&shopset.detail_guangao1_t}}"><uni-popup class="vue-ref" vue-id="c72bb2b6-1" id="gg1Dialog" type="dialog" data-ref="gg1Dialog" bind:__l="__l" vue-slots="{{['default']}}"><image class="img" style="width:600rpx;height:auto;border-radius:10rpx;" src="{{shopset.detail_guangao1_t}}" data-url="{{shopset.detail_guangao1_t}}" mode="widthFix" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image><view data-event-opts="{{[['tap',[['closegg1Dialog',['$event']]]]]}}" class="ggdiaplog_close" bindtap="__e"><image style="width:100%;height:100%;" src="/static/img/close2.png"></image></view></uni-popup></block><view class="header"><view class="title"><view class="lef"><text>{{product.name}}</text></view><view data-event-opts="{{[['tap',[['shareClick',['$event']]]]]}}" class="share" bindtap="__e"><image src="/static/img/share.png"></image><text>分享</text></view></view><block wx:if="{{product.sellpoint}}"><view class="sellpoint">{{product.sellpoint}}</view></block></view><block wx:if="{{teamCount>0}}"><view class="teamlist"><view class="label">{{teamCount+"人在拼单，可直接参与"}}</view><scroll-view class="content" scroll-y="{{true}}"><block wx:for="{{teamList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="item"><view class="f1"><image src="{{item.headimg}}"></image><view class="t1">{{item.nickname}}</view></view><view class="f2"><view class="t1">{{"还差"+(item.teamnum-item.num)+"人拼成"}}</view><view class="t2">{{"剩余"+item.djs}}</view></view><button class="f3" data-btntype="3" data-teamid="{{item.id}}" data-event-opts="{{[['tap',[['buydialogShow',['$event']]]]]}}" bindtap="__e">去拼单</button></view></block></scroll-view></view></block><block wx:if="{{shopset.detail_guangao2}}"><view style="width:100%;height:auto;padding:20rpx 0 0;"><block wx:if="{{shopset.detail_guangao2}}"><image style="width:100%;height:auto;" src="{{shopset.detail_guangao2}}" mode="widthFix" data-event-opts="{{[['tap',[['showgg2Dialog',['$event']]]]]}}" bindtap="__e"></image></block></view></block><block wx:if="{{shopset.detail_guangao2&&shopset.detail_guangao2_t}}"><uni-popup class="vue-ref" vue-id="c72bb2b6-2" id="gg2Dialog" type="dialog" data-ref="gg2Dialog" bind:__l="__l" vue-slots="{{['default']}}"><image class="img" style="width:600rpx;height:auto;border-radius:10rpx;" src="{{shopset.detail_guangao2_t}}" data-url="{{shopset.detail_guangao2_t}}" mode="widthFix" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image><view data-event-opts="{{[['tap',[['closegg2Dialog',['$event']]]]]}}" class="ggdiaplog_close" bindtap="__e"><image style="width:100%;height:100%;" src="/static/img/close2.png"></image></view></uni-popup></block></view><view id="scroll_view_tab1"><block wx:if="{{shopset.comment==1&&commentcount>0}}"><view class="commentbox"><view class="title"><view class="f1">{{"评价("+commentcount+")"}}</view><view class="f2" data-url="{{'commentlist?proid='+product.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">好评度<text style="{{'color:'+($root.m8)+';'}}">{{product.comment_haopercent+"%"}}</text><image style="width:32rpx;height:32rpx;" src="/static/img/arrowright.png"></image></view></view><view class="comment"><block wx:if="{{$root.g2>0}}"><view class="item"><view class="f1"><image class="t1" src="{{commentlist[0].headimg}}"></image><view class="t2">{{commentlist[0].nickname}}</view><view class="flex1"></view><view class="t3"><block wx:for="{{[0,1,2,3,4]}}" wx:for-item="item2" wx:for-index="index2" wx:key="index2"><image class="img" src="{{'/static/img/star'+(commentlist[0].score>item2?'2':'')+'.png'}}"></image></block></view></view><view class="f2"><text class="t1">{{commentlist[0].content}}</text><view class="t2"><block wx:if="{{commentlist[0].content_pic!=''}}"><block><block wx:for="{{commentlist[0].content_pic}}" wx:for-item="itemp" wx:for-index="index" wx:key="index"><block><view data-url="{{itemp}}" data-urls="{{commentlist[0].content_pic}}" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"><image src="{{itemp}}" mode="widthFix"></image></view></block></block></block></block></view></view><view class="f3" data-url="{{'commentlist?proid='+product.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">查看全部评价</view></view></block><block wx:else><view class="nocomment">暂无评价~</view></block></view></view></block></view><view id="scroll_view_tab2"><block wx:if="{{shopset.showjd==1}}"><view class="shop"><image class="p1" src="{{business.logo}}"></image><view class="p2 flex1"><view class="t1">{{business.name}}</view><view class="t2">{{business.desc}}</view></view><button class="p4" style="{{('background:linear-gradient(90deg,'+$root.m9+' 0%,rgba('+$root.m10+',0.8) 100%)')}}" data-url="{{product.bid==0?'/pages/index/index':'/pagesExt/business/index?id='+product.bid}}" data-opentype="reLaunch" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">进入店铺</button></view></block><view class="detail_title"><view class="t1"></view><view class="t2"></view><view class="t0">商品描述</view><view class="t2"></view><view class="t1"></view></view><view class="detail"><dp vue-id="c72bb2b6-3" pagecontent="{{pagecontent}}" bind:__l="__l"></dp></view></view><view id="scroll_view_tab3"><block wx:if="{{$root.g3>0}}"><view><view class="xihuan"><view class="xihuan-line"></view><view class="xihuan-text"><image class="img" src="/static/img/xihuan.png"></image><text class="txt">为您推荐</text></view><view class="xihuan-line"></view></view><view class="prolist"><view class="dp-collage-item"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="id"><view class="item" style="{{('width:49%;margin-right:'+(index%2==0?'2%':0))}}" data-url="{{'/activity/collage/product?id='+item.$orig.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="product-pic"><image class="image" src="{{item.$orig.pic}}" mode="widthFix"></image></view><view class="product-info"><view class="p1">{{item.$orig.name}}</view><view class="p2"><view class="p2-1"><text class="t1" style="{{'color:'+(item.m11)+';'}}"><text style="font-size:24rpx;">￥</text>{{item.$orig.sell_price}}</text><block wx:if="{{item.$orig.market_price*1>item.$orig.sell_price*1}}"><text class="t2">{{"￥"+item.$orig.market_price}}</text></block></view></view><view class="p3"><view class="p3-1" style="{{'background:'+('rgba('+item.m12+',0.12)')+';'+('color:'+(item.m13)+';')}}">{{item.$orig.teamnum+"人团"}}</view><block wx:if="{{item.$orig.sales>0}}"><view class="p3-2"><text style="overflow:hidden;">{{"已拼成"+item.$orig.sales+"件"}}</text></view></block></view></view></view></block></view></view></view></block></view><view class="notabbarbot" style="width:100%;height:110rpx;box-sizing:content-box;"></view></scroll-view><block wx:if="{{product.status==1}}"><view class="{{['bottombar','flex-row',menuindex>-1?'tabbarbot':'notabbarbot']}}"><block wx:if="{{kfurl!='contact::'}}"><view class="cart flex-col flex-x-center flex-y-center" data-url="{{kfurl}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image class="img" src="/static/img/kefu.png"></image><view class="t1">客服</view></view></block><block wx:else><button class="cart flex-col flex-x-center flex-y-center" open-type="contact"><image class="img" src="/static/img/kefu.png"></image><view class="t1">客服</view></button></block><view data-event-opts="{{[['tap',[['addfavorite',['$event']]]]]}}" class="favorite flex-col flex-x-center flex-y-center" bindtap="__e"><image class="img" src="/static/img/shoucang.png"></image><view class="t1">{{isfavorite?'已收藏':'收藏'}}</view></view><view class="tocart" style="{{'background:'+($root.m14)+';'}}" data-btntype="1" data-event-opts="{{[['tap',[['buydialogShow',['$event']]]]]}}" bindtap="__e"><text>单独购买</text><text>{{"￥"+product.market_price}}</text></view><view class="tobuy" style="{{'background:'+($root.m15)+';'}}" data-btntype="2" data-event-opts="{{[['tap',[['buydialogShow',['$event']]]]]}}" bindtap="__e"><text>发起拼团</text><text>{{"￥"+product.sell_price}}</text></view></view></block><view hidden="{{buydialogHidden}}"><view class="buydialog-mask"><view class="{{['buydialog',menuindex>-1?'tabbarbot':'notabbarbot']}}"><view data-event-opts="{{[['tap',[['buydialogChange',['$event']]]]]}}" class="close" bindtap="__e"><image class="image" src="/static/img/close.png"></image></view><view class="title"><image class="img" src="{{guigelist[ks].pic?guigelist[ks].pic:product.pic}}" data-url="{{guigelist[ks].pic?guigelist[ks].pic:product.pic}}" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image><block wx:if="{{btntype==1}}"><view class="price"><text class="t1">￥</text>{{guigelist[ks].market_price}}</view></block><block wx:else><view class="price"><text class="t1">￥</text>{{guigelist[ks].sell_price+''}}<block wx:if="{{guigelist[ks].market_price>guigelist[ks].sell_price}}"><text class="t2">{{"￥"+guigelist[ks].market_price}}</text></block></view></block><view class="choosename">{{"已选规格: "+guigelist[ks].name}}</view><view class="stock">{{"剩余"+guigelist[ks].stock+"件"}}</view></view><block wx:for="{{guigedata}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="guigelist flex-col"><view class="name">{{item.title}}</view><view class="item flex flex-y-center"><block wx:for="{{item.items}}" wx:for-item="item2" wx:for-index="index2" wx:key="index2"><block><view class="{{['item2 '+(ggselected[item.k]==item2.k?'on':'')]}}" data-itemk="{{item.k}}" data-idx="{{item2.k}}" data-event-opts="{{[['tap',[['ggchange',['$event']]]]]}}" bindtap="__e">{{item2.title}}</view></block></block></view></view></block><view class="buynum flex flex-y-center"><view class="flex1">购买数量：</view><view class="addnum"><view class="minus"><image class="img" src="/static/img/cart-minus.png" data-event-opts="{{[['tap',[['gwcminus',['$event']]]]]}}" bindtap="__e"></image></view><input class="input" type="number" data-event-opts="{{[['input',[['gwcinput',['$event']]]]]}}" value="{{gwcnum}}" bindinput="__e"/><view class="plus"><image class="img" src="/static/img/cart-plus.png" data-event-opts="{{[['tap',[['gwcplus',['$event']]]]]}}" bindtap="__e"></image></view></view></view><view class="op"><block wx:if="{{btntype==1}}"><block><button class="tobuy" style="{{'background:'+($root.m16)+';'}}" data-type="1" data-event-opts="{{[['tap',[['tobuy',['$event']]]]]}}" bindtap="__e">确定</button></block></block><block wx:if="{{btntype==2}}"><block><button class="tobuy" style="{{'background:'+($root.m17)+';'}}" data-type="2" data-event-opts="{{[['tap',[['tobuy',['$event']]]]]}}" bindtap="__e">下一步</button></block></block><block wx:if="{{btntype==3}}"><block><button class="tobuy" style="{{'background:'+($root.m18)+';'}}" data-type="3" data-event-opts="{{[['tap',[['tobuy',['$event']]]]]}}" bindtap="__e">确 定</button></block></block></view></view></view></view><block wx:if="{{sharetypevisible}}"><view class="popup__container"><view data-event-opts="{{[['tap',[['handleClickMask',['$event']]]]]}}" class="popup__overlay" catchtap="__e"></view><view class="popup__modal" style="height:320rpx;min-height:320rpx;"><view class="popup__content"><view class="sharetypecontent"><block wx:if="{{$root.m19=='app'}}"><view data-event-opts="{{[['tap',[['shareapp',['$event']]]]]}}" class="f1" bindtap="__e"><image class="img" src="/static/img/weixin.png"></image><text class="t1">分享给好友</text></view></block><block wx:else><block wx:if="{{$root.m20=='mp'}}"><view data-event-opts="{{[['tap',[['sharemp',['$event']]]]]}}" class="f1" bindtap="__e"><image class="img" src="/static/img/weixin.png"></image><text class="t1">分享给好友</text></view></block><block wx:else><block wx:if="{{$root.m21=='h5'}}"><view data-event-opts="{{[['tap',[['sharemp',['$event']]]]]}}" class="f1" bindtap="__e"><image class="img" src="/static/img/weixin.png"></image><text class="t1">分享给好友</text></view></block><block wx:else><button class="f1" open-type="share"><image class="img" src="/static/img/weixin.png"></image><text class="t1">分享给好友</text></button></block></block></block><view data-event-opts="{{[['tap',[['showPoster',['$event']]]]]}}" class="f2" bindtap="__e"><image class="img" src="/static/img/sharepic.png"></image><text class="t1">生成分享图片</text></view></view></view></view></view></block><block wx:if="{{showposter}}"><view class="posterDialog"><view class="main"><view data-event-opts="{{[['tap',[['posterDialogClose',['$event']]]]]}}" class="close" bindtap="__e"><image class="img" src="/static/img/close.png"></image></view><view class="content"><image class="img" src="{{posterpic}}" mode="widthFix" data-url="{{posterpic}}" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image></view></view></view></block></block></block><block wx:if="{{loading}}"><loading vue-id="c72bb2b6-4" bind:__l="__l"></loading></block><dp-tabbar vue-id="c72bb2b6-5" opt="{{opt}}" data-event-opts="{{[['^getmenuindex',[['getmenuindex']]]]}}" bind:getmenuindex="__e" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="c72bb2b6-6" data-ref="popmsg" bind:__l="__l"></popmsg></view>