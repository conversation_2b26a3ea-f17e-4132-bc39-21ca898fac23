<view><block wx:if="{{isload}}"><block><block wx:if="{{!hexiao_status}}"><block><block wx:if="{{type=='shop'||type=='collage'||type=='lucky_collage'||type=='kanjia'||type=='scoreshop'||type=='cycle'||type=='restaurant_shop'||type=='restaurant_takeaway'||type=='tuangou'||type=='seckill'}}"><block><view class="address"><view class="img"><image src="{{pre_url+'/static/img/address3.png'}}"></image></view><view class="info"><text class="t1">{{order.linkman+" "+order.tel}}</text><block wx:if="{{order.freight_type!=1&&order.freight_type!=3}}"><text class="t2">{{"地址："+order.area+order.address}}</text></block><block wx:if="{{order.freight_type==1}}"><text class="t2" data-address="{{order.storeinfo.address}}" data-latitude="{{order.storeinfo.latitude}}" data-longitude="{{order.storeinfo.longitude}}" data-event-opts="{{[['tap',[['openLocation',['$event']]]]]}}" bindtap="__e">{{"取货地点："+order.storeinfo.name+" - "+order.storeinfo.address}}</text></block></view></view><view class="product"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="idx" wx:key="idx"><view class="content"><view><image src="{{item.$orig.pic}}"></image></view><view class="detail"><text class="t1">{{item.$orig.name}}</text><block wx:if="{{item.$orig.ggname}}"><text class="t2">{{item.$orig.ggname}}</text></block><block wx:if="{{type=='scoreshop'}}"><view class="t3"><text class="x1 flex1"><block wx:if="{{item.$orig.money_price>0}}"><text>{{"￥"+item.$orig.money_price+"+"}}</text></block>{{item.$orig.score_price+item.m0}}</text><text class="x2">{{"×"+item.$orig.num}}</text></view></block><block wx:else><view class="t3"><text class="x1 flex1">{{"￥"+item.$orig.sell_price}}</text><block wx:if="{{type!='cycle'}}"><text class="x2">{{"×"+item.$orig.num}}</text></block></view></block><block wx:if="{{mendian_no_select==1&&item.$orig.is_hx}}"><view class="t3">已核销</view></block></view></view></block></view><block wx:if="{{(order.status==3||order.status==2)&&(order.freight_type==3||order.freight_type==4)}}"><view class="orderinfo"><view class="item flex-col"><text class="t1" style="color:#111;">发货信息</text><text class="t2" style="text-align:left;margin-top:10rpx;padding:0 10rpx;" user-select="true" selectable="true">{{order.freight_content}}</text></view></view></block><view class="orderinfo"><view class="item"><text class="t1">下单人</text><text class="flex1"></text><image style="width:80rpx;height:80rpx;margin-right:8rpx;" src="{{order.headimg}}"></image><text style="height:80rpx;line-height:80rpx;">{{order.nickname}}</text></view><view class="item"><text class="t1">{{$root.m1+"ID"}}</text><text class="t2">{{order.mid}}</text></view></view><block wx:if="{{order.remark}}"><view class="orderinfo"><view class="item"><text class="t1">备注</text><text class="t2">{{order.remark}}</text></view></view></block><view class="orderinfo"><view class="item"><text class="t1">订单编号</text><text class="t2" user-select="true" selectable="true">{{order.ordernum}}</text></view><view class="item"><text class="t1">下单时间</text><text class="t2">{{order.createtime}}</text></view><block wx:if="{{order.status>0&&order.paytypeid!='4'&&order.paytime}}"><view class="item"><text class="t1">支付时间</text><text class="t2">{{order.paytime}}</text></view></block><block wx:if="{{order.status>0&&order.paytime}}"><view class="item"><text class="t1">支付方式</text><text class="t2">{{order.paytype}}</text></view></block><block wx:if="{{order.status>1&&order.send_time}}"><view class="item"><text class="t1">发货时间</text><text class="t2">{{$root.m2}}</text></view></block><block wx:if="{{order.status==3&&order.collect_time}}"><view class="item"><text class="t1">收货时间</text><text class="t2">{{order.collect_time}}</text></view></block></view><view class="orderinfo"><block wx:if="{{type=='cycle'}}"><view class="item"><text class="t1">总配送期数</text><text class="t2 red">{{"共"+order.qsnum+"期"}}</text></view></block><block wx:if="{{type=='cycle'}}"><view class="item"><text class="t1">当前配送期数</text><text class="t2 red">{{"第"+order.stage.cycle_number+"期"}}</text></view></block><block wx:if="{{type=='cycle'}}"><view class="item"><text class="t1">每期数量</text><text class="t2 red">{{"共"+order.stage.num+"件"}}</text></view></block><block wx:if="{{type=='scoreshop'}}"><view class="item"><text class="t1">商品金额</text><block wx:if="{{order.totalmoney}}"><text class="t2 red">{{"¥"+order.totalmoney+" + "+order.totalscore+$root.m3}}</text></block><block wx:else><text class="t2 red">{{order.totalscore+$root.m4}}</text></block></view></block><block wx:else><view class="item"><text class="t1">商品金额</text><text class="t2 red">{{"¥"+order.product_price}}</text></view></block><block wx:if="{{order.disprice>0}}"><view class="item"><text class="t1">{{$root.m5+"折扣"}}</text><text class="t2 red">{{"-¥"+order.leveldk_money}}</text></view></block><block wx:if="{{order.jianmoney>0}}"><view class="item"><text class="t1">满减活动</text><text class="t2 red">{{"-¥"+order.manjian_money}}</text></view></block><view class="item"><text class="t1">配送方式</text><text class="t2">{{order.freight_text}}</text></view><block wx:if="{{order.freight_type==1&&order.freightprice>0}}"><view class="item"><text class="t1">服务费</text><text class="t2 red">{{"+¥"+order.freight_price}}</text></view></block><block wx:if="{{order.freight_time}}"><view class="item"><text class="t1">{{(order.freight_type!=1?'配送':'提货')+"时间"}}</text><text class="t2">{{order.freight_time}}</text></view></block><block wx:if="{{order.couponmoney>0}}"><view class="item"><text class="t1">{{$root.m6+"抵扣"}}</text><text class="t2 red">{{"-¥"+order.coupon_money}}</text></view></block><block wx:if="{{order.scoredk>0}}"><view class="item"><text class="t1">{{$root.m7+"抵扣"}}</text><text class="t2 red">{{"-¥"+order.scoredk_money}}</text></view></block><block wx:if="{{type=='scoreshop'}}"><view class="item"><text class="t1">实付款</text><text class="t2 red">{{"¥"+order.totalprice+" + "+order.totalscore+$root.m8}}</text></view></block><block wx:else><view class="item"><text class="t1">实付款</text><text class="t2 red">{{"¥"+order.totalprice}}</text></view></block><view class="item"><text class="t1">订单状态</text><block wx:if="{{order.status==0}}"><text class="t2">未付款</text></block><block wx:if="{{order.status==1}}"><text class="t2">已付款</text></block><block wx:if="{{order.status==2}}"><text class="t2">已发货</text></block><block wx:if="{{order.status==3}}"><text class="t2">已收货</text></block><block wx:if="{{order.status==4}}"><text class="t2">已关闭</text></block></view><block wx:if="{{order.refund_status>0}}"><view class="item"><text class="t1">退款状态</text><block wx:if="{{order.refund_status==1}}"><text class="t2 red">{{"审核中,¥"+order.refund_money}}</text></block><block wx:if="{{order.refund_status==2}}"><text class="t2 red">{{"已退款,¥"+order.refund_money}}</text></block><block wx:if="{{order.refund_status==3}}"><text class="t2 red">{{"已驳回,¥"+order.refund_money}}</text></block></view></block><block wx:if="{{order.refund_status>0}}"><view class="item"><text class="t1">退款原因</text><text class="t2 red">{{order.refund_reason}}</text></view></block><block wx:if="{{order.refund_checkremark}}"><view class="item"><text class="t1">审核备注</text><text class="t2 red">{{order.refund_checkremark}}</text></view></block><view class="item"><text class="t1">备注</text><text class="t2 red">{{order.message?order.message:'无'}}</text></view><block wx:if="{{order.field1}}"><view class="item"><text class="t1">{{order.field1data[0]}}</text><text class="t2 red">{{order.field1data[1]}}</text></view></block><block wx:if="{{order.field2}}"><view class="item"><text class="t1">{{order.field2data[0]}}</text><text class="t2 red">{{order.field2data[1]}}</text></view></block><block wx:if="{{order.field3}}"><view class="item"><text class="t1">{{order.field3data[0]}}</text><text class="t2 red">{{order.field3data[1]}}</text></view></block><block wx:if="{{order.field4}}"><view class="item"><text class="t1">{{order.field4data[0]}}</text><text class="t2 red">{{order.field4data[1]}}</text></view></block><block wx:if="{{order.field5}}"><view class="item"><text class="t1">{{order.field5data[0]}}</text><text class="t2 red">{{order.field5data[1]}}</text></view></block></view></block></block><block wx:if="{{type=='coupon'}}"><block><view class="couponbg" style="{{'background:'+('linear-gradient(90deg,'+$root.m9+' 0%,rgba('+$root.m10+',0.8) 100%)')+';'}}"></view><view class="orderinfo"><view class="topitem"><block wx:if="{{order.type==1}}"><view class="f1" style="{{'color:'+($root.m11)+';'}}"><text style="font-size:32rpx;">￥</text><text class="t1">{{order.money}}</text></view></block><block wx:else><block wx:if="{{order.type==2}}"><view class="f1" style="{{'color:'+($root.m12)+';'}}">礼品券</view></block><block wx:else><block wx:if="{{order.type==3}}"><view class="f1" style="{{'color:'+($root.m13)+';'}}"><text class="t1">{{order.limit_count}}</text><text class="t2">次</text></view></block><block wx:else><block wx:if="{{order.type==4}}"><view class="f1" style="{{'color:'+($root.m14)+';'}}">抵运费</view></block></block></block></block><view class="f2"><view class="t1">{{order.couponname}}</view><block wx:if="{{order.type==1||order.type==4}}"><view class="t2"><block wx:if="{{order.minprice>0}}"><text>{{"满"+order.minprice+"元可用"}}</text></block><block wx:else><text>无门槛</text></block></view></block><block wx:if="{{order.type==2}}"><view class="t2">礼品券</view></block><block wx:if="{{order.type==3}}"><view class="t2">计次券</view></block></view></view><view class="item"><text class="t1">类型</text><block wx:if="{{order.type==1}}"><text class="t2">代金券</text></block><block wx:if="{{order.type==2}}"><text class="t2">礼品券</text></block><block wx:if="{{order.type==3}}"><text class="t2">计次券</text></block><block wx:if="{{order.type==4}}"><text class="t2">运费抵扣券</text></block></view><block wx:if="{{order.type==3}}"><block><view class="item"><text class="t1">共计次数</text><text class="t2">{{order.limit_count}}</text></view><view class="item"><text class="t1">已使用次数</text><text class="t2">{{order.used_count}}</text></view><block wx:if="{{order.limit_perday>0}}"><block><view class="item"><text class="t1">每天限制次数</text><text class="t2">{{order.limit_perday}}</text></view></block></block></block></block><block wx:if="{{order.show_addnum==1}}"><view class="item"><text class="t1" style="flex:1;">核销次数</text><view class="addnum"><view data-event-opts="{{[['tap',[['hxminus',['$event']]]]]}}" class="minus" bindtap="__e"><image class="img" src="{{pre_url+'/static/img/cart-minus.png'}}"></image></view><input class="input" type="number" data-event-opts="{{[['input',[['hxinput',['$event']]]]]}}" value="{{hxnum}}" bindinput="__e"/><view data-event-opts="{{[['tap',[['hxplus',['$event']]]]]}}" class="plus" bindtap="__e"><image class="img" src="{{pre_url+'/static/img/cart-plus.png'}}"></image></view></view></view></block><view class="item"><text class="t1">领取时间</text><text class="t2">{{order.createtime}}</text></view><block wx:if="{{order.status==1}}"><block><view class="item"><text class="t1">使用时间</text><text class="t2">{{order.usetime}}</text></view></block></block><view class="item flex-col"><text class="t1">有效期</text><text class="t2">{{order.starttime+" 至 "+order.endtime}}</text></view><block wx:if="{{order.is_show_mendians==1}}"><view class="item flex-col"><text class="t1">选择门店</text><text class="t2"><radio-group data-event-opts="{{[['change',[['radioChange',['$event']]]]]}}" class="radio-group" bindchange="__e"><block wx:for="{{mendians}}" wx:for-item="item1" wx:for-index="idx" wx:key="idx"><label class="flex-y-center"><radio class="radio" value="{{idx}}"></radio>{{item1+''}}</label></block></radio-group></text></view></block><view class="item flex-col"><text class="t1">使用说明</text><view class="t2 textarea">{{order.usetips}}</view></view></view></block></block><block wx:if="{{type=='choujiang'}}"><block><view style="padding:15px 0 15px 0;"><view style="text-align:center;font-size:20px;color:#3cc51f;font-weight:400;margin:0 15%;">核销信息</view></view><view class="orderinfo"><view class="item"><view class="t1">核销类型</view><view class="t2" style="font-size:32rpx;">抽奖奖品</view></view><view class="item"><view class="t1">活动名称</view><view class="t2">{{order.name}}</view></view><view class="item"><view class="t1">奖品名称</view><view class="t2" style="font-size:16px;color:#000;">{{order.jxmc}}</view></view><view class="item"><view class="t1">中奖时间</view><view class="t2">{{$root.m15}}</view></view><block wx:if="{{order.formdata}}"><block><block wx:for="{{order.formdata}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="item"><text class="t1">{{index}}</text><text class="t2" user-select="{{true}}" selectable="{{true}}">{{item}}</text></view></block></block></block></view></block></block><block wx:if="{{type=='huodong_baoming'}}"><block><view style="padding:15px 0 15px 0;"><view style="text-align:center;font-size:20px;color:#3cc51f;font-weight:400;margin:0 15%;">核销信息</view></view><view class="orderinfo"><view class="item"><view class="t1">核销类型</view><view class="t2" style="font-size:32rpx;">活动报名</view></view><view class="item"><view class="t1">活动名称</view><view class="t2">{{order.title}}</view></view><view class="item"><view class="t1">核销数量</view><view class="t2" style="font-size:16px;color:#000;">{{order.num}}</view></view><view class="item"><view class="t1">报名时间</view><view class="t2">{{$root.m16}}</view></view></view></block></block><block wx:if="{{type=='business_miandan'}}"><block><view style="padding:15px 0 15px 0;"><view style="text-align:center;font-size:20px;color:#3cc51f;font-weight:400;margin:0 15%;">核销信息</view></view><view class="orderinfo"><view class="item"><view class="t1">核销类型</view><view class="t2" style="font-size:32rpx;">商户免单</view></view><view class="item"><view class="t1">活动名称</view><view class="t2">{{order.title}}</view></view><view class="item"><view class="t1">核销数量</view><view class="t2" style="font-size:16px;color:#000;">{{order.num}}</view></view><view class="item"><view class="t1">报名时间</view><view class="t2">{{$root.m17}}</view></view></view></block></block><block wx:if="{{type=='hbtk'}}"><block><view class="product"><view class="content"><view><image src="{{order.pic}}"></image></view><view class="detail"><text class="t1">{{order.name}}</text><view class="t3"><text class="x1 flex1">{{"￥"+order.price}}</text></view><view class="t2"><text>{{"已邀请"+order.yqnum+"人"}}</text></view></view></view></view><view class="orderinfo"><view class="item"><text class="t1 flex-y-center">下单人</text><text class="flex1"></text><image style="width:80rpx;height:80rpx;margin-right:8rpx;" src="{{order.headimg}}"></image><text style="height:80rpx;line-height:80rpx;">{{order.nickname}}</text></view></view><block wx:if="{{order.yqlist}}"><view class="orderinfo"><view class="item flex-y-center" style="padding:10rpx 0;"><text class="t1">邀请人员</text><view class="t2" style="overflow:hidden;" user-select="true" selectable="true"><block wx:for="{{order.yqlist}}" wx:for-item="item" wx:for-index="index"><block><image class="yq_image" src="{{item.headimg}}"></image></block></block></view></view></view></block><view class="orderinfo"><view class="item"><text class="t1">订单编号</text><text class="t2" user-select="true" selectable="true">{{order.ordernum}}</text></view><view class="item"><text class="t1">下单时间</text><text class="t2">{{order.createtime}}</text></view><block wx:if="{{order.status>0&&order.price>0&&order.paytime}}"><view class="item"><text class="t1">支付时间</text><text class="t2">{{order.paytime}}</text></view></block><block wx:if="{{order.status>=1&&order.price>0}}"><view class="item"><text class="t1">支付方式</text><text class="t2">{{order.paytype}}</text></view></block></view><view class="orderinfo"><block wx:if="{{order.price>0}}"><view class="item"><text class="t1">支付金额</text><text class="t2 red">{{"¥"+order.price}}</text></view></block><view class="item"><text class="t1">订单状态</text><block wx:if="{{order.status==1}}"><text class="t2">待核销</text></block><block wx:if="{{order.status==2}}"><text class="t2">已核销</text></block></view></view></block></block><block wx:if="{{type=='shopproduct'||type=='takeaway_order_product'}}"><block><view class="product"><view class="content"><view><image src="{{order.ogdata.pic}}"></image></view><view class="detail"><text class="t1">{{order.ogdata.name}}</text><text class="t2">{{order.ogdata.ggname}}</text><view class="t3"><text class="x1 flex1">{{"￥"+order.ogdata.sell_price}}</text><text class="x2">{{"×"+order.ogdata.num}}</text></view><block wx:if="{{order.ogdata.refund_num&&order.ogdata.refund_num>0}}"><view class="t3"><text class="x1 flex1"></text><text>{{"已退："+order.ogdata.refund_num+"件"}}</text></view></block></view></view></view><view class="orderinfo"><view class="item"><text class="t1">订单编号</text><text class="t2" user-select="true" selectable="true">{{order.ordernum}}</text></view><view class="item"><text class="t1">下单时间</text><text class="t2">{{order.createtime}}</text></view><block wx:if="{{order.status>0&&order.paytypeid!='4'&&order.paytime}}"><view class="item"><text class="t1">支付时间</text><text class="t2">{{order.paytime}}</text></view></block><block wx:if="{{order.status>0&&order.paytime}}"><view class="item"><text class="t1">支付方式</text><text class="t2">{{order.paytype}}</text></view></block><block wx:if="{{type=='shopproduct'}}"><view class="item"><text class="t1">已核销数</text><text class="t2">{{order.ogdata.hexiao_num}}</text></view></block></view><block wx:if="{{type=='shopproduct'}}"><view class="orderinfo"><view class="item"><text class="t1">本次核销数</text><text class="t2" style="color:#223;font-weight:bold;font-size:32rpx;">{{order.hxnum}}</text></view></view></block><block wx:if="{{type=='takeaway_order_product'}}"><view class="orderinfo"><view class="item"><text class="t1">本次核销数</text><text class="t2" style="color:#223;font-weight:bold;font-size:32rpx;">{{order.now_hxnum}}</text></view></view></block></block></block><block wx:if="{{type=='gift_bag'||type=='gift_bag_goods'}}"><block><block wx:if="{{type=='gift_bag'}}"><view class="product"><block wx:for="{{order.prolist}}" wx:for-item="item" wx:for-index="idx" wx:key="idx"><view class="content"><view><image src="{{item.pic}}"></image></view><view class="detail"><text class="t1">{{item.name}}</text><view class="t3"><text class="x1 flex1">{{"￥"+item.sell_price}}</text><block wx:if="{{type!='cycle'}}"><text class="x2">{{"×"+item.num}}</text></block></view></view></view></block></view></block><block wx:if="{{type=='gift_bag_goods'}}"><view class="product"><view class="content"><view><image src="{{order.ogdata.pic}}"></image></view><view class="detail"><text class="t1">{{order.ogdata.name}}</text><view class="t3"><text class="x1 flex1">{{"￥"+order.ogdata.sell_price}}</text><text class="x2">{{"×"+order.ogdata.num}}</text></view><block wx:if="{{order.ogdata.refund_num&&order.ogdata.refund_num>0}}"><view class="t3"><text class="x1 flex1"></text><text>{{"已退："+order.ogdata.refund_num+"件"}}</text></view></block></view></view></view></block><view class="orderinfo"><view class="item"><text class="t1">订单编号</text><text class="t2" user-select="true" selectable="true">{{order.ordernum}}</text></view><view class="item"><text class="t1">下单时间</text><text class="t2">{{order.createtime}}</text></view><block wx:if="{{order.status>0&&order.paytypeid!='4'&&order.paytime}}"><view class="item"><text class="t1">支付时间</text><text class="t2">{{order.paytime}}</text></view></block><block wx:if="{{order.status>0&&order.paytime}}"><view class="item"><text class="t1">支付方式</text><text class="t2">{{order.paytype}}</text></view></block><block wx:if="{{type=='gift_bag_goods'}}"><view class="item"><text class="t1">已核销数</text><text class="t2">{{order.ogdata.hexiao_num}}</text></view></block><block wx:if="{{type=='gift_bag'}}"><view class="item"><text class="t1">实付款</text><text class="t2 red">{{"¥"+order.totalprice}}</text></view></block><block wx:if="{{type=='gift_bag'}}"><block><block wx:if="{{order.refund_status>0}}"><view class="item"><text class="t1">退款状态</text><block wx:if="{{order.refund_status==1}}"><text class="t2 red">{{"审核中,¥"+order.refund_money}}</text></block><block wx:if="{{order.refund_status==2}}"><text class="t2 red">{{"已退款,¥"+order.refund_money}}</text></block><block wx:if="{{order.refund_status==3}}"><text class="t2 red">{{"已驳回,¥"+order.refund_money}}</text></block></view></block><block wx:if="{{order.refund_status>0}}"><view class="item"><text class="t1">退款原因</text><text class="t2 red">{{order.refund_reason}}</text></view></block><block wx:if="{{order.refund_checkremark}}"><view class="item"><text class="t1">审核备注</text><text class="t2 red">{{order.refund_checkremark}}</text></view></block></block></block></view><block wx:if="{{type=='gift_bag_goods'}}"><view class="orderinfo"><view class="item"><text class="t1">本次核销数</text><text class="t2" style="color:#223;font-weight:bold;font-size:32rpx;">{{order.hxnum}}</text></view></view></block></block></block><block wx:if="{{type=='verifyauth'}}"><block><view class="flex" style="flex-direction:column;align-items:center;margin-top:40%;"><image style="width:300rpx;height:300rpx;" src="{{pre_url+'/static/img/shouquan.png'}}" alt class="_img"></image><view style="font-size:32rpx;">{{order.tip}}</view><view style="text-align:center;color:#999;padding:20rpx;">{{''+order.title+''}}</view><view data-event-opts="{{[['tap',[['hexiao',['$event']]]]]}}" class="btn-add" style="{{'bottom:15%;'+('background:'+($root.m18)+';')}}" bindtap="__e"><block wx:if="{{type=='verifyauth'}}"><text>确认授权</text></block><block wx:else><text>立即核销</text></block></view></view></block></block><block wx:if="{{type=='hotel'}}"><block><view class="product"><view class="content"><view><image src="{{order.pic}}"></image></view><view class="detail"><text class="t1">{{order.title}}</text><block wx:if="{{order.title}}"><text class="t2">{{order.title}}</text></block><view class="t3">{{"￥"+order.sell_price}}</view></view></view><view class="orderinfo"><view class="item"><label class="t1">入住姓名</label><text class="t2">{{order.linkman+''}}</text></view><view class="item"><label class="t1">联系手机</label><text class="t2">{{order.tel+''}}</text></view><view class="item"><text class="t1">入住日期</text><text class="t2">{{order.in_date}}</text></view><view class="item"><text class="t1">离店日期</text><text class="t2">{{order.leave_date}}</text></view></view><view class="orderinfo"><view class="item"><text class="t1">订单编号</text><text class="t2" user-select="true" selectable="true">{{order.ordernum}}</text></view><view class="item"><text class="t1">下单时间</text><text class="t2">{{order.createtime}}</text></view><block wx:if="{{order.status>0&&order.paytime}}"><view class="item"><text class="t1">支付时间</text><text class="t2">{{order.paytime}}</text></view></block><block wx:if="{{order.status>0&&order.paytime}}"><view class="item"><text class="t1">支付方式</text><text class="t2">{{order.paytype}}</text></view></block><block wx:if="{{order.status>1&&order.send_time}}"><view class="item"><text class="t1">确认时间</text><text class="t2">{{$root.m19}}</text></view></block><block wx:if="{{order.status==3&&order.daodian_time}}"><view class="item"><text class="t1">到店时间</text><text class="t2">{{order.daodian_time}}</text></view></block><block wx:if="{{order.status==3&&order.collect_time}}"><view class="item"><text class="t1">离店时间</text><text class="t2">{{order.collect_time}}</text></view></block></view><view class="orderinfo"><view class="item"><text class="t1">押金</text><text class="t2 red">{{"¥"+order.yajin_money}}</text></view><block wx:if="{{order.fuwu_money>0}}"><view class="item"><text class="t1">服务费</text><text class="t2 red">{{"¥"+order.fuwu_money}}</text></view></block><block wx:if="{{order.dikou_money>0}}"><view class="item"><text class="t1">余额抵扣</text><text class="t2 red">{{"-¥"+order.dikou_money}}</text></view></block><block wx:if="{{order.couponmoney>0}}"><view class="item"><text class="t1">{{$root.m20+"抵扣"}}</text><text class="t2 red">{{"-¥"+order.coupon_money}}</text></view></block><block wx:if="{{order.scoredk>0}}"><view class="item"><text class="t1">{{$root.m21+"抵扣"}}</text><text class="t2 red">{{"-¥"+order.scoredk_money}}</text></view></block><block wx:else><view class="item"><text class="t1">实付款</text><text class="t2 red">{{"¥"+order.totalprice}}</text></view></block><view class="item"><text class="t1">订单状态</text><block wx:if="{{order.status==0}}"><text class="t2">未付款</text></block><block wx:if="{{order.status==1}}"><text class="t2">待确认</text></block><block wx:if="{{order.status==2}}"><text class="t2">待使用</text></block><block wx:if="{{order.status==3}}"><text class="t2">已到店</text></block><block wx:if="{{order.status==4}}"><text class="t2">已完成</text></block><block wx:if="{{order.status==4}}"><text class="t2">已关闭</text></block></view><block wx:if="{{order.refund_status>0}}"><view class="item"><text class="t1">退款状态</text><block wx:if="{{order.refund_status==1}}"><text class="t2 red">{{"审核中,¥"+order.refund_money}}</text></block><block wx:if="{{order.refund_status==2}}"><text class="t2 red">{{"已退款,¥"+order.refund_money}}</text></block><block wx:if="{{order.refund_status==3}}"><text class="t2 red">{{"已驳回,¥"+order.refund_money}}</text></block></view></block><block wx:if="{{order.refund_status>0}}"><view class="item"><text class="t1">退款原因</text><text class="t2 red">{{order.refund_reason}}</text></view></block><block wx:if="{{order.refund_checkremark}}"><view class="item"><text class="t1">审核备注</text><text class="t2 red">{{order.refund_checkremark}}</text></view></block><view class="item"><text class="t1">备注</text><text class="t2 red">{{order.message?order.message:'无'}}</text></view></view></view></block></block><block wx:if="{{type=='form'}}"><block><view style="padding:15px 0 15px 0;"><view style="text-align:center;font-size:20px;color:#3cc51f;font-weight:400;margin:0 15%;">核销信息</view></view><view class="orderinfo"><view class="item"><view class="t1">核销类型</view><view class="t2" style="font-size:32rpx;">表单信息</view></view><view class="item"><view class="t1">表单名称</view><view class="t2">{{order.title}}</view></view><view class="item"><view class="t1">填写时间</view><view class="t2">{{order.createtime}}</view></view><block wx:if="{{order.paytime}}"><view class="item"><view class="t1">支付时间</view><view class="t2">{{order.paytime}}</view></view></block></view><view class="orderinfo"><block wx:for="{{order.formdata}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><block wx:if="{{!item.hidden&&item.val12}}"><view class="item"><text class="{{['t1',item.key=='separate'?'title':'']}}">{{item.val1}}</text><block wx:if="{{item.key!='upload'&&item.key!='upload_file'&&item.key!='upload_video'&&item.key!='upload_pics'}}"><text class="t2">{{''+order['form'+index]+''}}</text></block><block wx:if="{{item.key=='upload'}}"><view class="t2"><image style="width:50px;" src="{{order['form'+index]}}" mode="widthFix" data-url="{{order['form'+index]}}" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image></view></block><block wx:if="{{item.key=='upload_file'}}"><view class="t2" style="overflow:hidden;white-space:pre-wrap;word-wrap:break-word;color:#4786BC;" data-file="{{order['form'+index]}}" data-event-opts="{{[['tap',[['download',['$event']]]]]}}" bindtap="__e">点击下载查看</view></block><block wx:if="{{item.key=='upload_video'}}"><view class="t2"><video style="width:80%;height:300rpx;margin-top:20rpx;" src="{{order['form'+index]}}"></video></view></block></view></block><block wx:if="{{item.key=='map'&&detail.show_distance}}"><view class="item" data-latitude="{{detail.adr_lat}}" data-longitude="{{detail.adr_lon}}" data-event-opts="{{[['tap',[['openLocation',['$event']]]]]}}" bindtap="__e"><text class="t1"></text><view class="t2">{{'距离您'+detail.distance+''}}<image style="width:26rpx;height:26rpx;margin-right:10rpx;" src="{{pre_url+'/static/img/b_addr.png'}}"></image>点击导航</view></view></block><block wx:if="{{item.key=='upload_pics'}}"><view class="t2"><block wx:for="{{order['form'+index]}}" wx:for-item="item2" wx:for-index="index2" wx:key="index2"><block><image style="width:50px;margin-right:10rpx;" src="{{item2}}" mode="widthFix" data-url="{{item2}}" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image></block></block></view></block></block></block></view></block></block><view style="height:140rpx;"></view><block wx:if="{{type!='verifyauth'}}"><view data-event-opts="{{[['tap',[['hexiao',['$event']]]]]}}" class="btn-add" style="{{'background:'+($root.m22)+';'}}" bindtap="__e">立即核销</view></block></block></block><block wx:else><block><view style="height:140rpx;"></view><view data-event-opts="{{[['tap',[['saoyisao',['$event']]]]]}}" class="btn-add" style="{{'background:'+($root.m23)+';'}}" bindtap="__e">继续核销</view></block></block></block></block></view>