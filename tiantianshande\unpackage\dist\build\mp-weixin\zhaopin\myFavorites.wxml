<view><block wx:if="{{isload}}"><block><view class="favorites-page"><view class="filter-bar"><view data-event-opts="{{[['tap',[['switchTab',['all']]]]]}}" class="{{['filter-item',(currentTab==='all')?'active':'']}}" style="{{'background-color:'+(currentTab==='all'?'rgba('+$root.m0+', 0.1)':'#f8f8f8')+';'+('color:'+(currentTab==='all'?$root.m1:'#666')+';')+('box-shadow:'+(currentTab==='all'?'0 4rpx 12rpx rgba('+$root.m2+', 0.15)':'0 2rpx 8rpx rgba(0, 0, 0, 0.02)')+';')}}" bindtap="__e">全部</view><view data-event-opts="{{[['tap',[['switchTab',['fulltime']]]]]}}" class="{{['filter-item',(currentTab==='fulltime')?'active':'']}}" style="{{'background-color:'+(currentTab==='fulltime'?'rgba('+$root.m3+', 0.1)':'#f8f8f8')+';'+('color:'+(currentTab==='fulltime'?$root.m4:'#666')+';')+('box-shadow:'+(currentTab==='fulltime'?'0 4rpx 12rpx rgba('+$root.m5+', 0.15)':'0 2rpx 8rpx rgba(0, 0, 0, 0.02)')+';')}}" bindtap="__e">全职</view><view data-event-opts="{{[['tap',[['switchTab',['parttime']]]]]}}" class="{{['filter-item',(currentTab==='parttime')?'active':'']}}" style="{{'background-color:'+(currentTab==='parttime'?'rgba('+$root.m6+', 0.1)':'#f8f8f8')+';'+('color:'+(currentTab==='parttime'?$root.m7:'#666')+';')+('box-shadow:'+(currentTab==='parttime'?'0 4rpx 12rpx rgba('+$root.m8+', 0.15)':'0 2rpx 8rpx rgba(0, 0, 0, 0.02)')+';')}}" bindtap="__e">兼职</view></view><scroll-view class="job-list" scroll-y="{{true}}" refresher-enabled="{{true}}" refresher-triggered="{{isRefreshing}}" data-event-opts="{{[['scrolltolower',[['loadMore',['$event']]]],['refresherrefresh',[['onRefresh',['$event']]]]]}}" bindscrolltolower="__e" bindrefresherrefresh="__e"><block wx:if="{{$root.g0>0}}"><block><block wx:for="{{favoriteJobs}}" wx:for-item="job" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['viewJobDetail',['$0'],[[['favoriteJobs','',index,'id']]]]]]]}}" class="job-card" bindtap="__e"><view class="job-info"><view class="job-header"><text class="job-title">{{job.title}}</text><text class="job-salary">{{job.salary}}</text></view><view class="company-info"><image class="company-logo" src="{{job.companyLogo}}" mode="aspectFit"></image><text class="company-name">{{job.companyName}}</text><text class="collect-time">{{job.collectTime}}</text></view></view><view class="job-footer"><view class="job-tags"><block wx:for="{{job.tags}}" wx:for-item="tag" wx:for-index="tagIndex" wx:key="tagIndex"><text class="tag">{{''+tag+''}}</text></block></view><view class="action-buttons"><button data-event-opts="{{[['tap',[['removeFromFavorites',['$0'],[[['favoriteJobs','',index,'id']]]]]]]}}" class="action-btn delete" catchtap="__e">取消收藏</button></view></view></view></block></block></block><block wx:else><view class="empty-state"><image class="empty-icon" src="/static/icons/empty-favorites.png" mode="aspectFit"></image><text class="empty-text">暂无收藏的职位</text><button data-event-opts="{{[['tap',[['goToJobList',['$event']]]]]}}" class="browse-btn" style="{{'background:'+('linear-gradient(135deg, '+$root.m9+', '+$root.m10+'dd)')+';'+('box-shadow:'+('0 8rpx 20rpx rgba('+$root.m11+', 0.25)')+';')}}" bindtap="__e">去浏览职位</button></view></block><block wx:if="{{$root.g1}}"><view class="loading-more">正在加载更多...</view></block><block wx:if="{{$root.g2}}"><view class="no-more-data">没有更多数据了</view></block></scroll-view></view></block></block><block wx:if="{{loading}}"><loading vue-id="c1ce2962-1" bind:__l="__l"></loading></block><dp-tabbar vue-id="c1ce2962-2" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="c1ce2962-3" data-ref="popmsg" bind:__l="__l"></popmsg></view>