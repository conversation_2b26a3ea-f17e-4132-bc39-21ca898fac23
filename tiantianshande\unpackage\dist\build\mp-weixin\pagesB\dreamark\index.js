require('../common/vendor.js');(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pagesB/dreamark/index"],{"112b":function(t,o,i){"use strict";i.r(o);var n=i("a392"),e=i("a698");for(var a in e)["default"].indexOf(a)<0&&function(t){i.d(o,t,(function(){return e[t]}))}(a);i("c0cf");var r=i("828b"),s=Object(r["a"])(e["default"],n["b"],n["c"],!1,null,null,null,!1,n["a"],void 0);o["default"]=s.exports},3363:function(t,o,i){},"46c8":function(t,o,i){"use strict";(function(t,o){var n=i("47a9");i("06e9");n(i("3240"));var e=n(i("112b"));t.__webpack_require_UNI_MP_PLUGIN__=i,o(e.default)}).call(this,i("3223")["default"],i("df3c")["createPage"])},"7b7f":function(t,o,i){"use strict";(function(t){Object.defineProperty(o,"__esModule",{value:!0}),o.default=void 0;var i={data:function(){return{showStartOverlay:!0,showTimePortalOverlay:!1,displayText:"",showCursor:!1,isRobotSpeaking:!1,showStartButton:!1,autoJumpCountdown:0,autoJumpTimer:null,audioEnabled:!0,hasDialogueData:!1,canvasWidth:375,canvasHeight:667,welcomeText:'欢迎来到梦想方舟计划！我是舟长"明日萌像"，将带您穿越时空，与2049年的自己对话。准备好开启这段奇妙的时空之旅了吗？',typingIndex:0,typingTimer:null,particleSystem:null,hologramIcons:["🚀","⚛️","🔬","🌏","⭐","∞","🎯","💫"],pre_url:"",openingAudio:null}},onLoad:function(){var t=getApp();this.pre_url=t.globalData.pre_url||"",this.initCanvas(),this.checkSystemState(),this.checkDialogueData()},onReady:function(){this.initParticleSystem()},onUnload:function(){this.clearTypingTimer(),this.clearAutoJumpTimer(),this.destroyParticleSystem(),this.destroyOpeningAudio()},methods:{initCanvas:function(){var o=t.getSystemInfoSync();this.canvasWidth=o.windowWidth,this.canvasHeight=o.windowHeight},checkSystemState:function(){var o=t.getStorageSync("user_dialogue_data");o&&(this.showStartOverlay=!1,this.startWelcomeSequence())},startSystem:function(){var t=this;this.showStartOverlay=!1,this.playStartupSound(),setTimeout((function(){t.startWelcomeSequence()}),500)},startWelcomeSequence:function(){this.isRobotSpeaking=!0,this.showCursor=!0,this.playOpeningAudio(),this.startTypingEffect()},startTypingEffect:function(){var t=this;this.clearTypingTimer(),this.typingIndex=0,this.displayText="",this.typingTimer=setInterval((function(){t.typingIndex<t.welcomeText.length?(t.displayText+=t.welcomeText[t.typingIndex],t.typingIndex++,t.playTypingSound()):t.completeTyping()}),80)},completeTyping:function(){var t=this;this.clearTypingTimer(),this.showCursor=!1,this.isRobotSpeaking=!1,setTimeout((function(){t.showStartButton=!0,t.activateTimePortal(),t.startAutoJumpCountdown()}),1e3)},clearTypingTimer:function(){this.typingTimer&&(clearInterval(this.typingTimer),this.typingTimer=null)},activateTimePortal:function(){this.particleSystem&&this.particleSystem.activateTimePortal()},startDialogue:function(){var o=this;console.log("=== 启动时空对话被点击 ==="),this.showTimePortalOverlay=!0,this.playPortalSound(),this.activateTimePortal(),setTimeout((function(){console.log("跳转到对话页面"),o.showTimePortalOverlay=!1,t.navigateTo({url:"/pagesB/dreamark/dialogue"})}),2e3)},startAutoJumpCountdown:function(){var t=this;this.autoJumpCountdown=3,this.autoJumpTimer=setInterval((function(){t.autoJumpCountdown--,t.autoJumpCountdown<=0&&(t.clearAutoJumpTimer(),t.autoStartDialogue())}),1e3)},cancelAutoJump:function(){this.clearAutoJumpTimer(),this.autoJumpCountdown=0,t.showToast({title:"已取消自动穿越",icon:"success",duration:1500})},clearAutoJumpTimer:function(){this.autoJumpTimer&&(clearInterval(this.autoJumpTimer),this.autoJumpTimer=null)},autoStartDialogue:function(){var o=this;this.showTimePortalOverlay=!0,this.playPortalSound(),this.activateTimePortal(),setTimeout((function(){o.showTimePortalOverlay=!1,t.navigateTo({url:"/pagesB/dreamark/dialogue"})}),2e3)},toggleAudio:function(){this.audioEnabled=!this.audioEnabled,t.showToast({title:this.audioEnabled?"音效已开启":"音效已关闭",icon:"none"})},adminClear:function(){var o=this;t.showModal({title:"管理员功能",content:"确定要清空所有用户数据吗？此操作不可恢复。",success:function(t){t.confirm&&o.clearAllData()}})},checkDialogueData:function(){try{var o=t.getStorageSync("user_dialogue_data");this.hasDialogueData=!!(o&&Object.keys(o).length>0),console.log("检查对话数据:",this.hasDialogueData,o)}catch(i){console.error("检查对话数据失败:",i),this.hasDialogueData=!1}},showClearDataDialog:function(){var o=this;t.showModal({title:"清空对话数据",content:"确定要清空已保存的对话数据吗？清空后将重新开始对话流程。",confirmText:"确定清空",cancelText:"取消",confirmColor:"#ff4444",success:function(t){t.confirm&&o.clearDialogueData()}})},clearDialogueData:function(){try{t.removeStorageSync("user_dialogue_data"),this.hasDialogueData=!1,t.showToast({title:"对话数据已清空",icon:"success",duration:2e3}),console.log("对话数据已清空")}catch(o){console.error("清空对话数据失败:",o),t.showToast({title:"清空失败",icon:"error"})}},testButtonClick:function(){console.log("=== 测试按钮被点击 ==="),t.showModal({title:"测试成功",content:"按钮点击事件正常工作！这说明基础的点击功能是正常的。",success:function(o){o.confirm&&t.showToast({title:"测试通过",icon:"success"})}})},clearAllData:function(){var o=this;try{t.clearStorageSync(),t.showToast({title:"数据已清空",icon:"success"}),setTimeout((function(){o.resetSystem()}),1e3)}catch(i){t.showToast({title:"清空失败",icon:"error"})}},resetSystem:function(){this.showStartOverlay=!0,this.displayText="",this.showCursor=!1,this.isRobotSpeaking=!1,this.showStartButton=!1,this.typingIndex=0},playOpeningAudio:function(){var o=this;if(this.audioEnabled){this.destroyOpeningAudio(),this.openingAudio=t.createInnerAudioContext(),this.openingAudio.src=this.pre_url+"/static/MP3/kaichang.mp3",this.openingAudio.onError((function(){console.log("开场音频文件不存在，跳过播放"),o.destroyOpeningAudio()})),this.openingAudio.onEnded((function(){console.log("开场音频播放完成"),o.destroyOpeningAudio()}));try{this.openingAudio.play()}catch(i){console.log("音频播放失败，跳过播放"),this.destroyOpeningAudio()}}},destroyOpeningAudio:function(){this.openingAudio&&(this.openingAudio.stop(),this.openingAudio.destroy(),this.openingAudio=null)},playStartupSound:function(){if(this.audioEnabled){var o=t.createInnerAudioContext();o.src=this.pre_url+"/static/audio/startup.mp3",o.onError((function(){console.log("启动音效文件不存在，跳过播放"),o.destroy()}));try{o.play()}catch(i){console.log("启动音效播放失败，跳过播放"),o.destroy()}}},playTypingSound:function(){this.audioEnabled},playPortalSound:function(){if(this.audioEnabled){var o=t.createInnerAudioContext();o.src=this.pre_url+"/static/audio/portal.mp3",o.onError((function(){console.log("传送门音效文件不存在，跳过播放"),o.destroy()}));try{o.play()}catch(i){console.log("传送门音效播放失败，跳过播放"),o.destroy()}}},initParticleSystem:function(){},destroyParticleSystem:function(){this.particleSystem&&(this.particleSystem=null)}}};o.default=i}).call(this,i("df3c")["default"])},a392:function(t,o,i){"use strict";i.d(o,"b",(function(){return n})),i.d(o,"c",(function(){return e})),i.d(o,"a",(function(){}));var n=function(){var t=this,o=t.$createElement,i=(t._self._c,t.showStartOverlay?t.t("color1"):null),n=t.showStartOverlay?t.t("color1rgb"):null,e=t.t("color1"),a=t.t("color1"),r=t.t("color1"),s=t.t("color1"),u=t.t("color1"),l=t.t("color1"),c=t.t("color1"),h=t.t("color1"),d=t.showStartButton&&t.autoJumpCountdown>0?t.t("color1"):null,m=t.showStartButton?t.t("color1"):null,p=t.showStartButton?t.t("color1rgb"):null,g=t.showStartButton?t.t("color1"):null,f=t.showStartButton?t.t("color1"):null,y=t.showStartButton?t.t("color1"):null,T=t.t("color1");t.$mp.data=Object.assign({},{$root:{m0:i,m1:n,m2:e,m3:a,m4:r,m5:s,m6:u,m7:l,m8:c,m9:h,m10:d,m11:m,m12:p,m13:g,m14:f,m15:y,m16:T}})},e=[]},a698:function(t,o,i){"use strict";i.r(o);var n=i("7b7f"),e=i.n(n);for(var a in n)["default"].indexOf(a)<0&&function(t){i.d(o,t,(function(){return n[t]}))}(a);o["default"]=e.a},c0cf:function(t,o,i){"use strict";var n=i("3363"),e=i.n(n);e.a}},[["46c8","common/runtime","common/vendor"]]]);