<block wx:if="{{!scroll}}"><view class="dd-tab" style="{{(isfixed?'position:fixed;':'')}}"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="{{['item',st==itemst[index]?'on':'']}}" data-st="{{itemst[index]}}" data-event-opts="{{[['tap',[['changetab',['$event']]]]]}}" bindtap="__e">{{item.$orig}}<view class="after" style="{{'background:'+(color1?color1:item.m0)+';'}}"></view></view></block></view></block><block wx:else><view class="dd-tab2" style="{{(isfixed?'position:fixed;':'')}}"><scroll-view class="vue-ref" scroll-x="true" scroll-left="{{scrollLeft}}" scroll-with-animation="{{true}}" data-ref="tabScroll"><view class="dd-tab2-content"><block wx:for="{{$root.l1}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="{{['item',st==itemst[index]?'on':'']}}" data-st="{{itemst[index]}}" id="{{'tab-'+itemst[index]}}" data-event-opts="{{[['tap',[['changetab',['$event']]]]]}}" bindtap="__e">{{item.$orig}}<view class="after" style="{{'background:'+(color1?color1:item.m1)+';'}}"></view></view></block></view></scroll-view></view></block>