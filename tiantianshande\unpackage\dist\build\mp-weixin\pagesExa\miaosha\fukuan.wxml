<view class="container"><block wx:if="{{isload}}"><block><dd-tab vue-id="9f37fe28-1" itemdata="{{['本场售出','本场购入']}}" itemst="{{['0','1']}}" st="{{st}}" isfixed="{{true}}" data-event-opts="{{[['^changetab',[['changetab']]]]}}" bind:changetab="__e" bind:__l="__l"></dd-tab><view style="width:100%;height:100rpx;"></view><block wx:if="{{st==0}}"><view class="order-content"><view class="order-box2"><text class="order-title2">当前场次售卖金额</text><text class="order-amount2">{{changshouchu}}</text>元</view><block wx:for="{{datalist}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><view class="order-box" data-url="{{'detail?id='+item.neworderid}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="head"><block wx:if="{{item.bid!=0}}"><view class="f1" data-url="{{'/pagesExt/business/index?id='+item.bid}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e"><image src="/static/img/ico-shop.png"></image>{{''+item.binfo.name}}</view></block><block wx:else><view class="f1"><image class="logo-row" src="{{item.binfo.logo}}"></image>{{''+item.binfo.name+''}}</view></block><view class="flex1"></view><block wx:if="{{item.prostatus2==0}}"><text style="color:red;">未上架</text></block><block wx:if="{{item.prostatus2==2}}"><text style="color:red;">委卖中</text></block><block wx:if="{{item.prostatus2==3}}"><text style="color:red;">待付款</text></block><block wx:if="{{item.prostatus2==4}}"><text style="color:red;">待确认</text></block><block wx:if="{{item.prostatus2==5}}"><text style="color:red;">已提货</text></block><block wx:if="{{item.prostatus2==6}}"><text style="color:red;">已确认</text></block></view><block wx:for="{{item.prolist}}" wx:for-item="item2" wx:for-index="idx" wx:key="idx"><block><view class="content" style="{{(idx+1==item.procount?'border-bottom:none':'')}}"><view data-url="{{'/pagesExa/miaosha/product?id='+item.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e"><image src="{{item2.pic}}"></image></view><view class="detail"><text class="t1">{{item2.name}}</text><text class="t2">{{item2.ggname}}</text><view class="t3"><text class="x1 flex1">{{"￥"+item2.sell_price}}</text></view></view></view></block></block><view class="bottom">商品状态:<block wx:if="{{item.prostatus2==0}}"><text style="color:red;">未上架</text></block><block wx:if="{{item.prostatus2==2}}"><text style="color:red;">委卖中</text></block><block wx:if="{{item.prostatus2==3}}"><text style="color:red;">待付款</text></block><block wx:if="{{item.prostatus2==4}}"><text style="color:red;">待确认</text></block><block wx:if="{{item.prostatus2==5}}"><text style="color:red;">已提货</text></block><block wx:if="{{item.prostatus2==6}}"><text style="color:red;">已确认</text></block></view><view class="bottom">购买人:<text style="color:red;">{{item.gmr}}</text></view></view></block></block></view></block><block wx:if="{{st==1}}"><view class="order-content"><view class="order-box2"><text class="order-title2">当前场次购入金额</text><text class="order-amount2">{{goumai}}</text>元</view><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><view class="order-box" data-url="{{'detail?id='+item.$orig.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="head"><block wx:if="{{item.$orig.bid!=0}}"><view class="f1" data-url="{{'/pagesExt/business/index?id='+item.$orig.bid}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e"><image src="/static/img/ico-shop.png"></image>{{''+item.$orig.binfo.name}}</view></block><block wx:else><view class="f1"><image class="logo-row" src="{{item.$orig.binfo.logo}}"></image>{{''+item.$orig.binfo.name+''}}</view></block><view class="flex1"></view><block wx:if="{{item.$orig.status==0}}"><text class="st0">待付款</text></block><block wx:if="{{item.$orig.status==1&&item.$orig.freight_type!=1}}"><text class="st1">待发货</text></block><block wx:if="{{item.$orig.status==1&&item.$orig.freight_type==1}}"><text class="st1">待提货</text></block><block wx:if="{{item.$orig.status==2}}"><text class="st2">待收货</text></block><block wx:if="{{item.$orig.status==3}}"><text class="st3">已完成</text></block><block wx:if="{{item.$orig.status==4}}"><text class="st4">已关闭</text></block></view><block wx:for="{{item.$orig.prolist}}" wx:for-item="item2" wx:for-index="idx" wx:key="idx"><block><view class="content" style="{{(idx+1==item.$orig.procount?'border-bottom:none':'')}}"><block wx:if="{{item.$orig.miaoshaid==0}}"><view data-url="{{'/shopPackage/shop/product?id='+item2.proid}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e"><image src="{{item2.pic}}"></image></view></block><block wx:if="{{item.$orig.miaoshaid>0}}"><view data-url="{{'/pagesExa/miaosha/product?id='+item.$orig.miaoshaid}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e"><image src="{{item2.pic}}"></image></view></block><view class="detail"><text class="t1">{{item2.name}}</text><text class="t2">{{item2.ggname}}</text><view class="t3"><text class="x1 flex1">{{"￥"+item.$orig.totalprice+''}}</text><text class="x2">{{"×"+item2.num}}</text></view></view></view></block></block><view class="bottom"><text>{{"共计"+item.$orig.procount+"件商品 实付:￥"+item.$orig.totalprice+''}}<block wx:if="{{item.$orig.balance_price>0&&item.$orig.balance_pay_status==0}}"><label style="display:block;float:right;" class="_span">{{"尾款：￥"+item.$orig.balance_price}}</label></block></text></view><view class="bottom">售卖人昵称:<text style="color:red;">{{item.$orig.outname}}</text></view><view class="op"><block wx:if="{{item.g0}}"><block><view class="btn2" data-url="{{'invoice?type=shop&orderid='+item.$orig.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e">发票</view></block></block><view class="btn2" data-url="{{'detail?id='+item.$orig.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e">详情</view><block wx:if="{{item.$orig.paytypeid==5}}"><block><view class="btn2" data-url="{{'detail2?id='+item.$orig.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e">查看凭证</view></block></block><block wx:if="{{item.$orig.weituo==1&&item.$orig.status==3}}"><block><view class="btn2" data-url="{{'buyweituo?id='+item.$orig.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e">委托上架</view></block></block><block wx:if="{{item.$orig.status==0}}"><block></block></block><block wx:if="{{item.$orig.status==1}}"><block><block wx:if="{{item.$orig.paytypeid!='4'}}"><block><block wx:if="{{canrefund==1&&item.$orig.refundnum<item.$orig.procount}}"><view class="btn2" data-url="{{'refundSelect?orderid='+item.$orig.id+'&price='+item.$orig.totalprice}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e">退款</view></block></block></block><block wx:else><block></block></block></block></block><block wx:if="{{item.$orig.status==2}}"><block><block wx:if="{{item.$orig.paytypeid!='4'}}"><block><block wx:if="{{canrefund==1&&item.$orig.refundnum<item.$orig.procount}}"><view class="btn2" data-url="{{'refundSelect?orderid='+item.$orig.id+'&price='+item.$orig.totalprice}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e">退款</view></block></block></block><block wx:else><block></block></block><block wx:if="{{item.$orig.freight_type!=3&&item.$orig.freight_type!=4}}"><block><block wx:if="{{item.$orig.express_type=='express_wx'}}"><view class="btn2" data-index="{{index}}" data-event-opts="{{[['tap',[['logistics',['$event']]]]]}}" catchtap="__e">订单跟踪</view></block><block wx:else><view class="btn2" data-index="{{index}}" data-event-opts="{{[['tap',[['logistics',['$event']]]]]}}" catchtap="__e">查看物流</view></block></block></block><block wx:if="{{item.$orig.balance_pay_status==0&&item.$orig.balance_price>0}}"><view class="btn1" style="{{'background:'+(item.m0)+';'}}" data-url="{{'/pages/pay/pay?id='+item.$orig.balance_pay_orderid}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e">支付尾款</view></block><block wx:if="{{item.$orig.paytypeid!='4'&&(item.$orig.balance_pay_status==1||item.$orig.balance_price==0)}}"><view class="btn1" style="{{'background:'+(item.m1)+';'}}" data-id="{{item.$orig.id}}" data-event-opts="{{[['tap',[['orderCollect',['$event']]]]]}}" catchtap="__e">确认收货</view></block></block></block><block wx:if="{{(item.$orig.status==1||item.$orig.status==2)&&item.$orig.freight_type==1}}"><block><view class="btn2" data-hexiao_qr="{{item.$orig.hexiao_qr}}" data-event-opts="{{[['tap',[['showhxqr',['$event']]]]]}}" catchtap="__e">核销码</view></block></block><block wx:if="{{item.$orig.refundCount}}"><view class="btn2" data-url="{{'refundlist?orderid='+item.$orig.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e">查看退款</view></block><block wx:if="{{item.$orig.status==3||item.$orig.status==4}}"><block></block></block><block wx:if="{{item.$orig.bid>0&&item.$orig.status==3}}"><block><block wx:if="{{item.$orig.iscommentdp==0}}"><view class="btn1" style="{{'background:'+(item.m2)+';'}}" data-url="{{'/pagesExt/order/commentdp?orderid='+item.$orig.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e">评价店铺</view></block></block></block></view></view></block></block></view></block><block wx:if="{{nomore}}"><nomore vue-id="9f37fe28-2" bind:__l="__l"></nomore></block><block wx:if="{{nodata}}"><nodata vue-id="9f37fe28-3" bind:__l="__l"></nodata></block><view class="summary-section"><view class="summary-text">当前场次售卖金额<text class="text-red">{{changshouchu}}</text>元, 当前场次购入金额<text class="text-red">{{goumai}}</text>元</view><view class="action-buttons"><block wx:if="{{changshouchu==goumai}}"><view class="btn red-btn">当前场次无需支付, 无需收款</view></block><block wx:if="{{changshouchu>goumai}}"><view class="btn red-btn" data-url="{{'pay3?id='+changciid}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">{{'需收款 '+shoukuan+' 元 ，去收款'}}</view></block><block wx:if="{{changshouchu<goumai}}"><view class="btn red-btn" data-url="{{'pay2?id='+changciid}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">去付款/查看付款</view></block></view></view></block></block><block wx:if="{{loading}}"><loading vue-id="9f37fe28-4" bind:__l="__l"></loading></block><dp-tabbar vue-id="9f37fe28-5" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="9f37fe28-6" data-ref="popmsg" bind:__l="__l"></popmsg></view>