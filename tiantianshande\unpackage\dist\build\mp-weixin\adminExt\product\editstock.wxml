<view><block wx:if="{{isload}}"><block><form data-event-opts="{{[['submit',[['subform',['$event']]]]]}}" bindsubmit="__e"><view class="form-box"><view class="form-item"><view class="f1">商品名称<text style="color:red;">*</text></view><view class="f2">{{info.name}}</view></view></view><view class="form-box"><view class="form-item flex-col" style="border-bottom:0;"><view class="f1">商品主图<text style="color:red;">*</text></view><view class="f2"><block wx:for="{{pic}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="layui-imgbox"><view class="layui-imgbox-close" data-index="{{index}}" data-field="pic" data-event-opts="{{[['tap',[['removeimg',['$event']]]]]}}" bindtap="__e"><image style="display:block;" src="{{pre_url+'/static/img/ico-del.png'}}"></image></view><view class="layui-imgbox-img"><image src="{{item}}" data-url="{{item}}" mode="widthFix" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image></view></view></block><block wx:if="{{$root.g0==0}}"><view class="uploadbtn" style="{{('background:url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 60rpx;background-size:80rpx 80rpx;background-color:#F3F3F3;')}}" data-field="pic" data-pernum="1" data-event-opts="{{[['tap',[['uploadimg',['$event']]]]]}}" bindtap="__e"></view></block></view><input type="text" hidden="true" name="pic" maxlength="-1" value="{{$root.g1}}"/></view></view><block wx:for="{{gglist}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="form-box"><view class="form-item" style="height:80rpx;line-height:80rpx;"><view class="f1">规格</view><view class="f2" style="font-weight:bold;line-height:40rpx;">{{item.name}}</view></view><view class="form-item" style="height:80rpx;line-height:80rpx;"><view class="f1">市场价（元）</view><view class="f2"><input type="text" data-index="{{index}}" data-field="market_price" name="{{'market_price['+index+']'}}" placeholder="请填写市场价" placeholder-style="color:#888" data-event-opts="{{[['input',[['gglistInput',['$event']]]]]}}" value="{{item.market_price}}" bindinput="__e"/></view></view><view class="form-item" style="height:80rpx;line-height:80rpx;"><view class="f1">成本价（元）</view><view class="f2"><input type="text" data-index="{{index}}" data-field="cost_price" name="{{'cost_price['+index+']'}}" placeholder="请填写成本价" placeholder-style="color:#888" data-event-opts="{{[['input',[['gglistInput',['$event']]]]]}}" value="{{item.cost_price}}" bindinput="__e"/></view></view><view class="form-item" style="height:80rpx;line-height:80rpx;"><view class="f1">销售价（元）</view><view class="f2"><input type="text" data-index="{{index}}" data-field="sell_price" name="{{'sell_price['+index+']'}}" placeholder="请填写销售价" placeholder-style="color:#888" data-event-opts="{{[['input',[['gglistInput',['$event']]]]]}}" value="{{item.sell_price}}" bindinput="__e"/></view></view><view class="form-item" style="height:80rpx;line-height:80rpx;"><view class="f1">重量（克）</view><view class="f2">{{item.weight}}</view></view><view class="form-item" style="height:80rpx;line-height:80rpx;"><view class="f1" style="position:relative;">库存<block wx:if="{{item.isstock_warning==1}}"><block><view class="stockwarning"><image style="width:30rpx;height:30rpx;" src="{{pre_url+'/static/img/workorder/ts.png'}}"></image>库存不足</view></block></block></view><view class="f2"><input type="text" data-index="{{index}}" data-field="stock" name="{{'stock['+index+']'}}" placeholder="请填写库存" placeholder-style="color:#888" data-event-opts="{{[['input',[['gglistInput',['$event']]]]]}}" value="{{item.stock}}" bindinput="__e"/></view></view></view></block><button class="savebtn" style="{{('background:linear-gradient(90deg,'+$root.m0+' 0%,rgba('+$root.m1+',0.8) 100%)')}}" form-type="submit">提交</button><view style="height:50rpx;"></view></form></block></block><view style="display:none;">{{test}}</view><block wx:if="{{loading}}"><loading vue-id="4ba0a32e-1" bind:__l="__l"></loading></block><popmsg class="vue-ref" vue-id="4ba0a32e-2" data-ref="popmsg" bind:__l="__l"></popmsg><wxxieyi vue-id="4ba0a32e-3" bind:__l="__l"></wxxieyi></view>