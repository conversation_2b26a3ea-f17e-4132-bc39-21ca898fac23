<view><block wx:if="{{isload}}"><block><form data-event-opts="{{[['submit',[['formsubmit',['$event']]]]]}}" bindsubmit="__e"><view class="st_box"><block wx:if="{{courseInfo&&courseInfo.id}}"><view class="course-info"><view class="course-header"><view class="course-image"><image src="{{courseInfo.pic}}" mode="aspectFill"></image></view><view class="course-details"><view class="course-title">{{courseInfo.name}}</view><view class="course-desc">{{courseInfo.count+"节课 | 已有"+courseInfo.join_num+"人学习"}}</view></view></view></view></block><view class="st_form"><view class="flex" style="flex-wrap:wrap;"><block wx:for="{{pics}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="layui-imgbox"><view class="layui-imgbox-close" data-index="{{index}}" data-field="pics" data-event-opts="{{[['tap',[['removeimg',['$event']]]]]}}" bindtap="__e"><image src="{{pre_url+'/static/img/ico-del.png'}}"></image></view><block wx:if="{{item.type=='img'}}"><view class="layui-imgbox-img"><image src="{{item.url}}" data-url="{{item}}" mode="widthFix" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image></view></block><block wx:if="{{item.type=='video'}}"><view class="layui-imgbox-img"><image style="width:200rpx;height:200rpx;background:#eee;" src="{{pre_url+'/static/img/uploadvideo.png'}}" data-event-opts="{{[['tap',[['previewVideo',['$0'],[[['pics','',index,'url']]]]]]]}}" bindtap="__e"></image></view></block></view></block><block wx:if="{{$root.g0<9}}"><view data-event-opts="{{[['tap',[['openSelect',['$event']]]]]}}" class="uploadbtn" style="{{('background:url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 75rpx;background-size:50rpx 50rpx;background-color:#F3F3F3;')}}" bindtap="__e"></view></block></view><view style="margin:20rpx 0;"><input style="height:100rpx;" placeholder="填写标题会有更多赞哦~" name="title"/></view><view style="border-bottom:1px solid #EEEEEE;padding-bottom:20rpx;"><textarea class="feedback-textarea" placeholder="添加正文" name="content" maxlength="-1"></textarea><view><view><scroll-view style="white-space:nowrap;width:100%;" scroll-x="true"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['cateChange',[index]]]]]}}" class="tag-box" style="{{'display:'+('inline-block')+';'+('color:'+(cindex===index?'#fff':'#999999')+';')+('background:'+(cindex===index?item.m0:'transparent')+';')}}" bindtap="__e">{{''+item.$orig+''}}</view></block></scroll-view></view></view></view></view><input type="text" hidden="true" name="pics" maxlength="-1" value="{{pics}}"/><input type="text" hidden="true" name="kcid" maxlength="-1" value="{{kcid}}"/><input type="text" hidden="true" name="type" maxlength="-1" value="{{type}}"/><block wx:if="{{bind_product==1}}"><view class="picker-container"><view data-event-opts="{{[['tap',[['openShop',['$event']]]]]}}" class="picker-label" bindtap="__e"><image style="width:26rpx;margin-right:10rpx;" src="{{pre_url+'/static/img/shortvideo_cart.png'}}" mode="widthFix"></image>{{''+(selectedObj.name?selectedObj.name:'选择商品')+''}}</view><view data-event-opts="{{[['tap',[['selectProduct',['$event']]]]]}}" class="picker-text" bindtap="__e">{{''+($root.g1>0?$root.g2+'件商品':'请选择商品')+''}}</view><image style="width:26rpx;" src="{{pre_url+'/static/icon/right.png'}}" mode="widthFix" data-event-opts="{{[['tap',[['selectProduct',['$event']]]]]}}" bindtap="__e"></image></view></block><block wx:if="{{bind_kecheng==1&&!courseInfo}}"><view class="picker-container"><view class="picker-label"><image style="width:26rpx;margin-right:10rpx;" src="{{pre_url+'/static/icon/kecheng.png'}}" mode="widthFix"></image>选择课程</view><view class="picker-text">请选择课程</view><image style="width:26rpx;" src="{{pre_url+'/static/icon/right.png'}}" mode="widthFix"></image></view></block></view><view class="st_title flex-y-center" style="{{'padding-bottom:'+(safeBottomHeight+80+'rpx')+';'}}"><view style="color:#878787;font-size:12rpx;text-align:center;margin-right:20rpx;width:80rpx;"><view class="flex-y-center tag-box" style="width:70rpx;height:70rpx;padding:0rpx;line-height:70rpx;border-radius:50rpx;margin:0;"><image style="width:34rpx;margin:0 auto;" src="{{pre_url+'/static/icon/draft.png'}}" mode="widthFix"></image></view><view style="margin-top:10rpx;">首页</view></view><button style="{{('background:'+$root.m1)}}" form-type="submit">发布笔记</button></view><view style="width:100%;height:50rpx;"></view></form><uni-popup class="vue-ref" vue-id="433be37a-1" mask-click="{{false}}" data-ref="videoShow" bind:__l="__l" vue-slots="{{['default']}}"><view class="viewVideo"><view data-event-opts="{{[['tap',[['fullscreenchange',['$event']]]]]}}" class="close" bindtap="__e"></view><video id="myVideo" src="{{cVideo}}" object-fit="contain" autoplay="false" controls="{{true}}" show-fullscreen-btn="false" play-btn-position="center" show-loading="true" data-event-opts="{{[['fullscreenchange',[['fullscreenchange',['$event']]]]]}}" bindfullscreenchange="__e"></video></view></uni-popup><uni-popup class="vue-ref" vue-id="433be37a-2" type="bottom" data-ref="shopShow" bind:__l="__l" vue-slots="{{['default']}}"><view class="viewShop"><view style="text-align:center;font-weight:bold;margin-bottom:20rpx;position:sticky;top:0;">{{'文中提到的商品（'+$root.g3+'）'}}</view><view class="cart-container"><scroll-view style="height:50vh;" scroll-top="{{0}}" scroll-y="true"><block wx:for="{{selectedObj.matchedData}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="cart-item" data-url="{{'/shopPackage/shop/product?id='+item.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="image-box"><image class="item-image" src="{{item.pic}}" mode="heightFix"></image></view><view class="item-info"><text class="item-name">{{item.name}}</text><view style="display:flex;justify-content:space-between;width:100%;"><text class="item-price"><label class="_span">￥</label><label style="font-size:34rpx;font-weight:bold;" class="_span">{{item.sell_price}}</label></text></view></view></view></block></scroll-view><view style="position:sticky;bottom:0;height:100rpx;display:flex;justify-content:space-between;align-items:center;padding:0 20rpx;"><view data-event-opts="{{[['tap',[['shopAllSelectedClick',['$event']]]]]}}" style="visibility:hidden;" bindtap="__e"><checkbox style="transform:scale(0.8);" checked="{{shopAllSelected}}"></checkbox>全选</view><view data-event-opts="{{[['tap',[['closePop',['$event']]]]]}}" class="shopButtonActive" bindtap="__e">点击关闭</view></view></view></view></uni-popup></block></block><block wx:if="{{loading}}"><loading vue-id="433be37a-3" bind:__l="__l"></loading></block><dp-tabbar vue-id="433be37a-4" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="433be37a-5" data-ref="popmsg" bind:__l="__l"></popmsg></view>