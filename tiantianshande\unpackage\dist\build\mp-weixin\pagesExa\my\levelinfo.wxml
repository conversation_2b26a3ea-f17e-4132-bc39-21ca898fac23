<view class="container"><block wx:if="{{isload}}"><block><view class="topbg" style="{{('background:url('+pre_url+'/static/img/lv-topbg.png) no-repeat;background-size:100%')}}"><view class="topinfo" style="{{('background:url('+pre_url+'/static/img/lv-top.png);background-size:100%')}}"><image class="headimg" src="{{userinfo.headimg}}" background-size="cover"></image><view class="info"><view class="nickname">{{userinfo.nickname}}</view><view class="flex"><view class="user-level"><block wx:if="{{userlevel.icon}}"><image class="level-img" src="{{userlevel.icon}}"></image></block><view class="level-name">{{userlevel.name}}</view></view></view><block wx:if="{{userinfo.levelendtime>0}}"><view class="endtime">{{"到期时间："+$root.m0}}</view></block><block wx:if="{{userlevel.areafenhong==1&&userinfo.province}}"><view class="endtime">{{"代理区域："+userinfo.province}}</view></block><block wx:if="{{userlevel.areafenhong==2&&userinfo.province&&userinfo.city}}"><view class="endtime">{{"代理区域："+userinfo.province+","+userinfo.city}}</view></block><block wx:if="{{userlevel.areafenhong==3&&userinfo.province&&userinfo.city&&userinfo.area}}"><view class="endtime">{{"代理区域："+userinfo.province+","+userinfo.city+","+userinfo.area}}</view></block><block wx:if="{{userlevel.areafenhong==10&&userinfo.largearea}}"><view class="endtime">{{"代理区域："+userinfo.largearea}}</view></block></view><view class="set" data-url="set" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><text class="fa fa-cog"></text></view></view><view class="upbtn" style="{{('background:url('+pre_url+'/static/img/lv-upbtn.png) no-repeat;background-size:100%')}}" data-url="{{opt.id?'levelup?id='+opt.id:'levelup'}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">我要升级</view></view><view style="width:100%;height:20rpx;background-color:#f6f6f6;"></view><view class="explain"><view class="f1">— 等级特权 —</view><view class="f2"><parse vue-id="63dae690-1" content="{{userlevel.explain}}" bind:__l="__l"></parse></view></view></block></block><block wx:if="{{loading}}"><loading vue-id="63dae690-2" bind:__l="__l"></loading></block><dp-tabbar vue-id="63dae690-3" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="63dae690-4" data-ref="popmsg" bind:__l="__l"></popmsg></view>