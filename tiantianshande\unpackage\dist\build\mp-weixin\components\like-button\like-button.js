(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["components/like-button/like-button"],{"2b56":function(t,e,n){"use strict";n.d(e,"b",(function(){return i})),n.d(e,"c",(function(){return a})),n.d(e,"a",(function(){}));var i=function(){var t=this.$createElement;this._self._c},a=[]},"3f34":function(t,e,n){"use strict";n.r(e);var i=n("dd6f7"),a=n.n(i);for(var u in i)["default"].indexOf(u)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(u);e["default"]=a.a},"633b":function(t,e,n){"use strict";var i=n("e2d5"),a=n.n(i);a.a},dd6f7:function(t,e,n){"use strict";(function(t){Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n={props:{src:{type:String,default:""},showImgs:{type:Array,default:function(){return[]}},duration:{type:Number,default:5e3},range:{type:Number,default:50},high:{type:Number,default:360},width:{type:Number||String,default:52},height:{type:Number||String,default:52},imgWidth:{type:Number||String,default:52},imgHeight:{type:Number||String,default:52},throttle:{type:Number,default:200},site:{type:Array||Object,default:function(){return[30,160]||!1}},large:{type:[Number,Boolean],default:!1},alone:{type:Boolean,default:!0}},data:function(){return{viewList:[],elId:0,oldTime:0,timer:null,waitDeleteIndex:0}},mounted:function(){},methods:{handleClick:function(e){var n=this,i=e.timeStamp-this.oldTime;if(i<this.throttle)return null;this.oldTime=e.timeStamp;var a=Math.floor(Math.random()*this.showImgs.length),u={elId:"el_likeicon_"+this.elId,src:this.showImgs[a],animation:{},x:Math.ceil(Math.random()*this.range),q:Math.floor(2*Math.random())},r=Number(["-",""][u.q]+u.x),o=this.high-10*Math.random();this.elId++,this.viewList.push(u),u.animation=t.createAnimation({duration:this.duration,timingFunction:"ease-out"}),setTimeout((function(){return console.log("animation finished."),n.$emit("finished"),n.alone?(n.waitDeleteIndex++,n.onThrottle(n.deleteView,n.duration),null):(clearTimeout(n.timer),n.timer=setTimeout((function(){n.viewList=[]}),n.duration),null)}),this.duration),setTimeout((function(){n.$nextTick((function(){var t=1;n.large&&(t="number"===typeof n.large?n.large:2),u.animation.translateY(-o).translateX(r).scale(t,t).opacity(0).step(),u.animation=u.animation.export()}))}),0),this.$emit("handleClick",this.elId)},deleteView:function(){this.viewList.splice(0,this.waitDeleteIndex),this.waitDeleteIndex=0},onThrottle:function(t,e){var n=!0;return function(){if(!n)return!1;n=!1,setTimeout((function(){t(),n=!0}),e)}()}}};e.default=n}).call(this,n("df3c")["default"])},e2a2:function(t,e,n){"use strict";n.r(e);var i=n("2b56"),a=n("3f34");for(var u in a)["default"].indexOf(u)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(u);n("633b");var r=n("828b"),o=Object(r["a"])(a["default"],i["b"],i["c"],!1,null,null,null,!1,i["a"],void 0);e["default"]=o.exports},e2d5:function(t,e,n){}}]);
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'components/like-button/like-button-create-component',
    {
        'components/like-button/like-button-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('df3c')['createComponent'](__webpack_require__("e2a2"))
        })
    },
    [['components/like-button/like-button-create-component']]
]);
