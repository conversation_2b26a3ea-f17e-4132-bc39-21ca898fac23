<view class="container"><block wx:if="{{showStartOverlay}}"><view data-event-opts="{{[['tap',[['startDialogue',['$event']]]]]}}" class="start-overlay" bindtap="__e"><view data-event-opts="{{[['tap',[['startDialogue',['$event']]]]]}}" class="start-btn" style="{{'background:'+('linear-gradient(45deg,'+$root.m0+' 0%,rgba('+$root.m1+',0.8) 100%)')+';'}}" catchtap="__e"><text class="icon">▶</text><text class="btn-text">开启时空连接</text></view></view></block><canvas class="particles-canvas" style="{{'width:'+(canvasWidth+'px')+';'+('height:'+(canvasHeight+'px')+';')}}" canvas-id="particlesCanvas"></canvas><view class="bg-grid"></view><view class="bg-circles"></view><block wx:if="{{!showStartOverlay}}"><view class="dialogue-console"><view class="time-display"><text class="year">2049</text><text class="date">{{currentDate}}</text></view><view class="ai-avatar-section"><view class="ai-avatar"><view class="avatar-glow"></view><view class="ai-face"><view class="{{['ai-eye','left-eye',(isAISpeaking)?'speaking':'']}}"></view><view class="{{['ai-eye','right-eye',(isAISpeaking)?'speaking':'']}}"></view><view class="{{['ai-mouth',(isAISpeaking)?'speaking':'']}}"></view><view class="neural-network"><view class="neural-node"></view><view class="neural-node"></view><view class="neural-node"></view><view class="neural-connection"></view><view class="neural-connection"></view></view></view></view><view class="ai-name"><text class="ai-title" style="{{'color:'+($root.m2)+';'}}">明日萌像 AI</text><text class="ai-subtitle">2049年智能对话系统</text></view></view><view class="dialogue-area"><view class="dialogue-header"><view class="header-left"><text class="header-icon">💬</text><text class="header-text">时空对话进行中...</text></view><view class="header-right"><view class="connection-status"><view class="status-dot"></view><text class="status-text">已连接</text></view><button class="clear-btn" type="default" data-event-opts="{{[['tap',[['showClearDialog',['$event']]]]]}}" bindtap="__e">🗑️</button></view></view><scroll-view class="messages-container" scroll-y="true" scroll-top="{{scrollTop}}" scroll-with-animation="{{true}}" enable-back-to-top="{{false}}" scroll-anchoring="{{true}}" scroll-into-view="{{scrollIntoView}}"><block wx:for="{{messages}}" wx:for-item="message" wx:for-index="index" wx:key="index"><view class="{{['message',message.type==='ai'?'ai-message':'user-message']}}" id="{{'message-'+index}}"><view class="message-avatar"><text class="avatar-icon">{{message.type==='ai'?'🤖':'👤'}}</text></view><view class="message-content"><view class="message-text"><text>{{message.text}}</text><block wx:if="{{message.typing}}"><text class="typing-cursor">|</text></block></view><block wx:if="{{message.type==='ai'&&message.showPlayBtn}}"><view data-event-opts="{{[['tap',[['playAudio',[index]]]]]}}" class="play-audio-btn" bindtap="__e"><text class="play-icon">▶</text><text class="play-text">播放</text></view></block></view><view class="message-time"><text>{{message.time}}</text></view></view></block><view class="scroll-anchor" id="scroll-bottom"></view></scroll-view><block wx:if="{{showInput}}"><view class="input-area"><view class="input-container"><input class="user-input" placeholder="{{inputPlaceholder}}" placeholder-style="color:#B2B5BE;font-size:28rpx" maxlength="{{100}}" focus="{{inputFocus}}" confirm-type="send" data-event-opts="{{[['confirm',[['sendMessage',['$event']]]],['input',[['__set_model',['','userInput','$event',[]]],['onInputChange',['$event']]]],['focus',[['onInputFocus',['$event']]]],['blur',[['onInputBlur',['$event']]]]]}}" value="{{userInput}}" bindconfirm="__e" bindinput="__e" bindfocus="__e" bindblur="__e"/><view data-event-opts="{{[['tap',[['sendMessage',['$event']]]]]}}" class="{{['send-btn',(!$root.g0)?'disabled':'']}}" bindtap="__e"><text class="send-icon">📤</text></view></view><view class="input-tip"><text class="tip-text">输入完成后点击发送按钮或按回车键</text></view></view></block></view><view class="progress-indicator"><block wx:for="{{progressSteps}}" wx:for-item="step" wx:for-index="index" wx:key="index"><view class="{{['progress-step',[(currentStep===index+1)?'active':'',(currentStep>index+1)?'completed':'']]}}"><view class="step-number">{{index+1}}</view><view class="step-label">{{step}}</view></view></block><view class="progress-line" style="{{'width:'+(progressWidth)+';'}}"></view></view><view class="dialogue-controls"><view data-event-opts="{{[['tap',[['goBack',['$event']]]]]}}" class="control-btn" bindtap="__e"><text class="btn-icon">←</text><text class="btn-text">返回方舟</text></view><view data-event-opts="{{[['tap',[['skipQuestion',['$event']]]]]}}" class="control-btn" bindtap="__e"><text class="btn-icon">⏭</text><text class="btn-text">跳过</text></view><view data-event-opts="{{[['tap',[['clearAndRestart',['$event']]]]]}}" class="control-btn clear-btn" bindtap="__e"><text class="btn-icon">🔄</text><text class="btn-text">清空重新开始</text></view></view></view></block><view class="footer"><text class="footer-text">时空对话系统 v2049.1 | 与未来自己的深度交流 | 数据加密传输中...</text></view></view>