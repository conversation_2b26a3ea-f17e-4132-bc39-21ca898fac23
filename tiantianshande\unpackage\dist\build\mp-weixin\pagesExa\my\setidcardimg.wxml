<view class="container data-v-5fe237e2"><block wx:if="{{isload}}"><block class="data-v-5fe237e2"><form data-event-opts="{{[['submit',[['formSubmit',['$event']]]],['reset',[['formReset',['$event']]]]]}}" bindsubmit="__e" bindreset="__e" class="data-v-5fe237e2"><view class="auth data-v-5fe237e2"><view class="infos data-v-5fe237e2"><view class="list data-v-5fe237e2"><text class="data-v-5fe237e2">您的姓名</text><input placeholder="请输入内容" placeholderStyle="font-size: 28rpx;color: #999999;line-height: 40rpx;font-family: PingFangSC-Regular;" border="surround" name="realname" data-event-opts="{{[['input',[['__set_model',['','realname','$event',[]]]]]]}}" value="{{realname}}" bindinput="__e" class="data-v-5fe237e2"/></view><view class="list data-v-5fe237e2"><text class="data-v-5fe237e2">您的身份证</text><input placeholder="请输入身份证号码" placeholderStyle="font-size: 28rpx;color: #999999;line-height: 40rpx;font-family: PingFangSC-Regular;" border="surround" name="idcard" data-event-opts="{{[['input',[['__set_model',['','idcard','$event',[]]]]]]}}" value="{{idcard}}" bindinput="__e" class="data-v-5fe237e2"/></view><view class="list data-v-5fe237e2"><text class="data-v-5fe237e2">上传身份证头像面</text><view data-event-opts="{{[['tap',[['upIdcardHead',['$event']]]]]}}" class="upload data-v-5fe237e2" bindtap="__e"><image src="{{idcard_front}}" class="data-v-5fe237e2"></image></view></view><view class="list data-v-5fe237e2"><text class="data-v-5fe237e2">上传身份证背面</text><view data-event-opts="{{[['tap',[['upIdcardBack',['$event']]]]]}}" class="upload data-v-5fe237e2" bindtap="__e"><image src="{{idcard_back}}" class="data-v-5fe237e2"></image></view></view><view data-event-opts="{{[['tap',[['formSubmit',['$event']]]]]}}" class="button data-v-5fe237e2" bindtap="__e"><text class="data-v-5fe237e2">提交认证信息</text></view><view class="text data-v-5fe237e2"><text class="data-v-5fe237e2">根据监管要求身份证照片仅用于实名认证</text></view></view></view></form></block></block><block wx:if="{{loading}}"><loading vue-id="2da180f8-1" class="data-v-5fe237e2" bind:__l="__l"></loading></block><dp-tabbar vue-id="2da180f8-2" opt="{{opt}}" class="data-v-5fe237e2" bind:__l="__l"></dp-tabbar><popmsg vue-id="2da180f8-3" data-ref="popmsg" class="data-v-5fe237e2 vue-ref" bind:__l="__l"></popmsg></view>