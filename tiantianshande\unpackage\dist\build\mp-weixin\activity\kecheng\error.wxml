<view><block wx:if="{{isload}}"><block><view class="wrap"><view class="top flex"><block wx:if="{{tkdata.type==1&&tkdata.rightcount==1}}"><view class="f1">单选题</view></block><block wx:if="{{tkdata.type==1&&tkdata.rightcount==2}}"><view class="f1">多选题</view></block><block wx:if="{{tkdata.type==2}}"><view class="f1">填空题</view></block><view class="f3">{{tkdata.sort+"/"+tkdata.nums}}</view></view><view class="question"><view class="title">{{''+tkdata.sort+"."+tkdata.title+''}}</view><block wx:if="{{tkdata.type==1&&tkdata.rightcount==1}}"><block><view class="option_group"><block wx:for="{{tkdata.option}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="{{['option flex '+(index==currentindex?'on':'')]}}">{{''+tkdata.sorts[index]+''}}<view class="after"></view><view class="t1">{{item}}</view></view></block></view></block></block><block wx:if="{{tkdata.type==1&&tkdata.rightcount>1}}"><block><view class="option_group"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="{{['option flex '+(item.g0!=-1?'on':'')]}}" data-index="{{index}}" data-event-opts="{{[['tap',[['selectOption',[index]]]]]}}" bindtap="__e">{{''+tkdata.sorts[index]+''}}<view class="after"></view><view class="t1">{{item.$orig}}</view></view></block></view></block></block><block wx:if="{{tkdata.type==1&&!tkdata.rightcount}}"><block><view class="option_group"><block wx:for="{{tkdata.option}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="{{['option flex ']}}" data-index="{{index}}" data-event-opts="{{[['tap',[['selectOption',[index]]]]]}}" bindtap="__e">{{''+tkdata.sorts[index]+''}}<view class="after"></view><view class="t1">{{item}}</view></view></block></view></block></block><block wx:if="{{tkdata.type==2}}"><block><view class="option_group"><view class="uni-textarea"><textarea placeholder-style="color:#222" placeholder="答:" data-event-opts="{{[['blur',[['bindTextAreaBlur',['$event']]]]]}}" value="{{tkdata.answer}}" bindblur="__e"></textarea></view></view></block></block></view></view><view class="right_content"><text class="t1">正确答案</text><text class="t2">{{''+(tkdata.type==2?tkdata.right_option:'')+" \r\n\t\t\t\t"+(tkdata.type==1?tkdata.right_options:'')+''}}<view>{{"题目解析："+tkdata.jiexi}}</view></text></view><view class="bottom flex"><block wx:if="{{tkdata.isup!=1}}"><block><button class="upbut flex-x-center flex-y-center hui">上一题</button></block></block><block wx:if="{{tkdata.isup==1}}"><block><button class="upbut flex-x-center flex-y-center" style="{{'background:'+($root.m0)+';'}}" data-dttype="up" data-event-opts="{{[['tap',[['toanswer',['$event']]]]]}}" bindtap="__e">上一题</button></block></block><block wx:if="{{tkdata.isdown==1}}"><button class="downbtn flex-x-center flex-y-center" style="{{'background:'+($root.m1)+';'}}" data-dttype="down" data-event-opts="{{[['tap',[['toanswer',['$event']]]]]}}" bindtap="__e">下一题</button></block><block wx:if="{{tkdata.isdown!=1}}"><button class="downbtn flex-x-center flex-y-center hui">下一题</button></block></view></block></block><block wx:if="{{loading}}"><loading vue-id="372f7080-1" bind:__l="__l"></loading></block><dp-tabbar vue-id="372f7080-2" opt="{{opt}}" data-event-opts="{{[['^getmenuindex',[['getmenuindex']]]]}}" bind:getmenuindex="__e" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="372f7080-3" data-ref="popmsg" bind:__l="__l"></popmsg></view>