<view class="container"><block wx:if="{{isload}}"><block><dd-tab vue-id="4c47e601-1" itemdata="{{['全部','待处理','处理中','已处理','待支付']}}" itemst="{{['all','0','1','2','10']}}" st="{{st}}" isfixed="{{true}}" data-event-opts="{{[['^changetab',[['changetab']]]]}}" bind:changetab="__e" bind:__l="__l"></dd-tab><view style="width:100%;height:90rpx;"></view><view class="content" id="datalist"><block wx:for="{{datalist}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="item"><view class="f1"><view class="flex" style="justify-content:space-between;"><text class="t1" data-url="{{'detail?id='+item.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">{{"工单类型："+item.title}}</text><view class="f2"><block wx:if="{{item.status==0&&(!item.payorderid||item.paystatus==1)}}"><text class="t1" style="color:#88e;">待处理</text></block><block wx:if="{{item.status==0&&item.payorderid&&item.paystatus==0}}"><text class="t1" style="color:red;">待支付</text></block><block wx:if="{{item.status==1}}"><text class="t1" style="color:green;">处理中</text></block><block wx:if="{{item.status==2}}"><text class="t1" style="color:red;">已处理</text></block><block wx:if="{{item.status==-1}}"><text class="t1" style="color:red;">已驳回</text></block></view></view><view class="flex" style="justify-content:space-between;"><text class="t2" data-url="{{'detail?id='+item.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">{{"提交时间："+item.createtime}}</text><block wx:if="{{!item.payorderid||item.payorderid&&item.paystatus==1}}"><view class="jindu" data-id="{{item.id}}" data-status="{{item.status}}" data-iscomment="{{item.iscomment}}" data-comment_status="{{item.comment_status}}" data-event-opts="{{[['tap',[['jindu',['$event']]]]]}}" bindtap="__e">查看进度</view></block></view><block wx:if="{{item.ordertype==1&&item.glordernum&&item.orderid}}"><block><view class="flex" style="justify-content:space-between;"><text class="t2" data-url="{{'pages/order/detail?id='+item.orderid}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">{{"关联订单：商城订单（"+item.glordernum+"）"}}</text></view></block></block><block wx:if="{{item.ordertype==2&&item.glordernum&&item.orderid}}"><block><view class="flex" style="justify-content:space-between;"><text class="t2" data-url="{{'/yuyue/orderdetail?id='+item.orderid}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">{{"关联订单：预约订单（"+item.glordernum+"）"}}</text></view></block></block></view></view></block></view><block wx:if="{{ishowjindu}}"><view class="modal"><view class="modal_jindu"><form data-event-opts="{{[['submit',[['formsubmit',['$event']]]]]}}" bindsubmit="__e"><view data-event-opts="{{[['tap',[['close',['$event']]]]]}}" class="close" bindtap="__e"><image src="../../static/img/close.png"></image></view><block wx:if="{{$root.g0>0}}"><block><block wx:for="{{$root.l1}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="item"><view class="f1"><image src="{{'/static/img/jindu'+(index==0?'2':'1')+'.png'}}"></image></view><view class="f2"><text class="t2">{{'时间：'+item.$orig.time}}</text><text class="t1">{{item.$orig.desc+"("+item.$orig.remark+')'}}</text><block wx:for="{{item.$orig.content_pic}}" wx:for-item="pic" wx:for-index="ind"><block wx:if="{{item.g1>0}}"><view><view class="layui-imgbox-img"><image src="{{pic}}" data-url="{{pic}}" mode="widthFix" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image></view></view></block></block><block wx:for="{{item.l0}}" wx:for-item="hf" wx:for-index="hfindex" wx:key="hfindex"><view><block wx:if="{{hf.$orig.hfremark}}"><view class="t3">{{"我的回复："+hf.$orig.hfremark+''}}</view></block><block wx:if="{{hf.$orig.hftime}}"><view class="t4">{{"回复时间："+hf.$orig.hftime+''}}</view></block><block wx:for="{{hf.$orig.hfcontent_pic}}" wx:for-item="pic2" wx:for-index="ind2"><block wx:if="{{hf.g2>0}}"><view><view class="layui-imgbox-img"><image src="{{pic2}}" data-url="{{pic2}}" mode="widthFix" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image></view></view></block></block></view></block></view></view></block><input style="display:none;" type="hidden" name="lcid" value="{{jdlist[0].id}}"/><block wx:if="{{statuss!=2}}"><view class="huifu"><view class="form-item4 flex-col"><view class="label"><label>回复:</label></view><view class="flex" style="flex-wrap:wrap;padding-top:20rpx;" id="content_picpreview"><block wx:for="{{content_pic}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="layui-imgbox"><view class="layui-imgbox-close" data-index="{{index}}" data-field="content_pic" data-event-opts="{{[['tap',[['removeimg',['$event']]]]]}}" bindtap="__e"><image src="/static/img/ico-del.png"></image></view><view class="layui-imgbox-img"><image src="{{item}}" data-url="{{item}}" mode="widthFix" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image></view></view></block><block wx:if="{{$root.g3<5}}"><view class="uploadbtn" style="{{('background:url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 40rpx;background-size:80rpx 80rpx;background-color:#F3F3F3;')}}" data-field="content_pic" data-event-opts="{{[['tap',[['uploadimg',['$event']]]]]}}" bindtap="__e"></view></block></view></view><view class="hfbox"><textarea placeholder="输入回复内容" name="content" maxlength="-1" value="{{jdlist[0].hfremark}}"></textarea></view><view class="btnbox"><view class="f1"><button data-event-opts="{{[['tap',[['confirmend',['$event']]]]]}}" class="btn1" bindtap="__e">确认结束</button><button class="btn2" form-type="submit">提交</button></view></view></view></block><block wx:if="{{statuss==2&&iscomment==0}}"><view class="btnbox"><view class="pjitem"><view class="item"><view style="margin-right:10rpx;">满意度</view><radio-group data-event-opts="{{[['change',[['radioChange',['$event']]]]]}}" bindchange="__e"><label class="radio"><radio value="1"></radio>不满意</label><label class="radio"><radio value="2"></radio>一般</label><label class="radio"><radio value="3"></radio>满意</label></radio-group></view><button data-event-opts="{{[['tap',[['tocomment',['$event']]]]]}}" class="btn1" bindtap="__e">确认评价</button></view></view></block><block wx:if="{{statuss==2&&iscomment==1}}"><view class="btnbox"><view class="pjitem"><view class="item"><view style="margin-right:10rpx;">满意度:</view><block wx:if="{{comment_status==1}}"><view class="t1" style="color:red;">不满意</view></block><block wx:if="{{comment_status==2}}"><view class="t1" style="color:#88e;">一般</view></block><block wx:if="{{comment_status==3}}"><view class="t1" style="color:green;">满意</view></block></view></view></view></block></block></block><block wx:else><block wx:if="{{statuss==-1}}"><block><view style="font-size:14px;color:#f05555;padding:10px;">工单已驳回</view></block></block><block wx:else><block><view style="font-size:14px;color:#f05555;padding:10px;">等待处理</view></block></block></block></form></view></view></block></block></block><block wx:if="{{nodata}}"><nodata vue-id="4c47e601-2" bind:__l="__l"></nodata></block><block wx:if="{{nomore}}"><nomore vue-id="4c47e601-3" bind:__l="__l"></nomore></block><block wx:if="{{loading}}"><loading vue-id="4c47e601-4" bind:__l="__l"></loading></block><dp-tabbar vue-id="4c47e601-5" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="4c47e601-6" data-ref="popmsg" bind:__l="__l"></popmsg></view>