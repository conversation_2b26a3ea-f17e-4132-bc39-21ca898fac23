<view class="container"><view class="header"><text class="title">{{cityInfo.name||'城市详情'}}</text><text class="subtitle">查看城市详细数据</text></view><view class="content"><view class="info-card"><view class="card-header"><image class="card-icon" src="/static/img/icon-city.png"></image><text class="card-title">基本信息</text></view><view class="info-list"><view class="info-item"><text class="info-label">城市名称：</text><text class="info-value">{{cityInfo.name}}</text></view><view class="info-item"><text class="info-label">行政代码：</text><text class="info-value">{{cityInfo.code}}</text></view><view class="info-item"><text class="info-label">下辖区县：</text><text class="info-value">{{cityInfo.district_count+"个"}}</text></view><view class="info-item"><text class="info-label">代理开始：</text><text class="info-value">{{$root.m0}}</text></view></view></view><view class="stats-card"><view class="card-header"><image class="card-icon" src="/static/img/icon-stats.png"></image><text class="card-title">业绩统计</text></view><view class="stats-grid"><view class="stats-item"><text class="stats-number">{{statistics.total_orders}}</text><text class="stats-label">总订单数</text></view><view class="stats-item"><text class="stats-number">{{"￥"+statistics.total_amount}}</text><text class="stats-label">总业绩</text></view><view class="stats-item"><text class="stats-number">{{"￥"+statistics.total_commission}}</text><text class="stats-label">总佣金</text></view><view class="stats-item"><text class="stats-number">{{statistics.month_orders}}</text><text class="stats-label">本月订单</text></view></view></view><block wx:if="{{$root.g0>0}}"><view class="district-card"><view class="card-header"><image class="card-icon" src="/static/img/icon-district.png"></image><text class="card-title">下辖区县</text></view><view class="district-list"><block wx:for="{{districts}}" wx:for-item="district" wx:for-index="__i0__" wx:key="id"><view data-event-opts="{{[['tap',[['viewDistrictDetail',['$0'],[[['districts','id',district.id]]]]]]]}}" class="district-item" bindtap="__e"><view class="district-info"><text class="district-name">{{district.name}}</text><text class="district-stats">{{district.orders+"笔订单 | ￥"+district.revenue+"收益"}}</text></view><image class="arrow-icon" src="/static/img/arrow-right.png"></image></view></block></view></view></block><view class="trend-card"><view class="card-header"><image class="card-icon" src="/static/img/icon-trend.png"></image><text class="card-title">月度趋势</text></view><view class="trend-chart"><block wx:for="{{monthlyTrend}}" wx:for-item="item" wx:for-index="__i1__" wx:key="month"><view class="chart-item"><view class="chart-bar"><view class="bar-fill" style="{{'height:'+(item.percentage+'%')+';'+('background:'+($root.m1)+';')}}"></view></view><text class="chart-label">{{item.month}}</text></view></block></view><view class="trend-legend"><view class="legend-item"><view class="legend-color" style="{{'background:'+($root.m2)+';'}}"></view><text class="legend-text">订单量</text></view></view></view><view class="action-container"><button data-event-opts="{{[['tap',[['viewOrders',['$event']]]]]}}" class="action-btn" bindtap="__e">查看订单</button><button data-event-opts="{{[['tap',[['viewIncome',['$event']]]]]}}" class="action-btn" bindtap="__e">查看收益</button></view></view><block wx:if="{{loading}}"><loading vue-id="08e19da8-1" bind:__l="__l"></loading></block><popmsg class="vue-ref" vue-id="08e19da8-2" data-ref="popmsg" bind:__l="__l"></popmsg></view>