<view class="data-v-401e300c"><view class="container data-v-401e300c"><view class="user-info data-v-401e300c"><view class="user-balance data-v-401e300c"><text class="data-v-401e300c">{{"余额："+balance+" 元"}}</text></view><view class="user-points data-v-401e300c"><text class="data-v-401e300c">{{"积分："+points}}</text></view><view class="user-commission data-v-401e300c"><text class="data-v-401e300c">{{"佣金："+commission+" 元"}}</text></view></view><scroll-view class="piglet-list data-v-401e300c" scroll-y="{{true}}"><block wx:for="{{$root.l0}}" wx:for-item="plot" wx:for-index="index" wx:key="index"><view class="piglet-item data-v-401e300c"><block wx:if="{{plot.$orig.pig}}"><image class="piglet-image data-v-401e300c" src="{{plot.m0}}"></image><view class="piglet-info data-v-401e300c"><view class="piglet-id data-v-401e300c"><block wx:if="{{plot.$orig.pig.feed_days>=10}}"><text class="data-v-401e300c">成猪</text></block><block wx:else><block wx:if="{{plot.$orig.pig.feed_days>=5}}"><text class="data-v-401e300c">中猪</text></block><block wx:else><text class="data-v-401e300c">幼猪</text></block></block>{{'ID：'+plot.$orig.pig.pig_id+''}}</view><text class="piglet-days data-v-401e300c">{{"已连续喂养天数："+plot.$orig.pig.feed_days+"/10"}}</text><view class="action-buttons data-v-401e300c"><block wx:if="{{!plot.$orig.pig.is_mature&&plot.$orig.pig.status===0}}"><button data-event-opts="{{[['tap',[['feedPiglet',['$0'],[[['plots','',index,'pig.pig_id']]]]]]]}}" class="feed-button data-v-401e300c" bindtap="__e">喂养</button></block><block wx:if="{{plot.$orig.pig.is_mature&&plot.$orig.pig.status===0}}"><button data-event-opts="{{[['tap',[['sellPiglet',['$0'],[[['plots','',index,'pig.pig_id']]]]]]]}}" class="sell-button data-v-401e300c" bindtap="__e">出售</button></block></view></view></block></view></block></scroll-view><button data-event-opts="{{[['tap',[['viewSaleRecords',['$event']]]]]}}" class="records-button data-v-401e300c" bindtap="__e">查看出售记录</button></view><block wx:if="{{loading}}"><loading vue-id="948c679a-1" class="data-v-401e300c" bind:__l="__l"></loading></block><popmsg vue-id="948c679a-2" data-ref="popmsg" class="data-v-401e300c vue-ref" bind:__l="__l"></popmsg><dp-tabbar vue-id="948c679a-3" opt="{{opt}}" class="data-v-401e300c" bind:__l="__l"></dp-tabbar></view>