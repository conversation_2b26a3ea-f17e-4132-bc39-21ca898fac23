require('../common/vendor.js');(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["admin/return/create"],{1595:function(t,e,r){"use strict";var i=r("9822"),n=r.n(i);n.a},5372:function(t,e,r){"use strict";r.r(e);var i=r("ed2a"),n=r.n(i);for(var o in i)["default"].indexOf(o)<0&&function(t){r.d(e,t,(function(){return i[t]}))}(o);e["default"]=n.a},9822:function(t,e,r){},c623:function(t,e,r){"use strict";r.r(e);var i=r("d6d4"),n=r("5372");for(var o in n)["default"].indexOf(o)<0&&function(t){r.d(e,t,(function(){return n[t]}))}(o);r("1595");var u=r("828b"),d=Object(u["a"])(n["default"],i["b"],i["c"],!1,null,null,null,!1,i["a"],void 0);e["default"]=d.exports},d6d4:function(t,e,r){"use strict";r.d(e,"b",(function(){return n})),r.d(e,"c",(function(){return o})),r.d(e,"a",(function(){return i}));var i={nodata:function(){return r.e("components/nodata/nodata").then(r.bind(null,"101c"))},loading:function(){return r.e("components/loading/loading").then(r.bind(null,"ceaa"))},popmsg:function(){return r.e("components/popmsg/popmsg").then(r.bind(null,"2bf2"))}},n=function(){var t=this,e=t.$createElement,r=(t._self._c,t.purchaseOrder.order_no?t.getStatusColor(t.purchaseOrder.status):null),i=t.purchaseOrder.order_no?t.getStatusText(t.purchaseOrder.status):null,n=t.productList.length,o=n>0?t.__map(t.productList,(function(e,r){var i=t.__get_orig(e),n=t.isProductSelected(e.id),o=t.isProductSelected(e.id),u=o?t.getSelectedQuantity(e.id):null;return{$orig:i,m2:n,m3:o,m4:u}})):null,u=0===t.productList.length&&!t.loading,d=t.selectedProducts.length,c=d>0?t.getTotalItems():null,a=d>0?t.getTotalPrice():null;t.$mp.data=Object.assign({},{$root:{m0:r,m1:i,g0:n,l0:o,g1:u,g2:d,m5:c,m6:a}})},o=[]},ebaa:function(t,e,r){"use strict";(function(t,e){var i=r("47a9");r("06e9");i(r("3240"));var n=i(r("c623"));t.__webpack_require_UNI_MP_PLUGIN__=r,e(n.default)}).call(this,r("3223")["default"],r("df3c")["createPage"])},ed2a:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var i=getApp(),n={data:function(){return{purchaseId:0,purchaseOrder:{},loading:!1,productList:[],selectedProducts:[],remark:""}},onLoad:function(t){if(this.opt=i.getopts(t),this.purchaseId=this.opt.purchase_id||0,!this.purchaseId)return i.error("缺少进货单ID"),void setTimeout((function(){i.goback()}),1500);this.getPurchaseOrderDetail()},methods:{getPurchaseOrderDetail:function(){var t=this;i.post("ApiAdminPurchase/getPurchaseOrderDetail",{id:t.purchaseId},(function(e){if(0===e.code){if(t.purchaseOrder=e.data.order||{},console.log("获取到订单详情:",t.purchaseOrder),e.data.items&&e.data.items.length>0?t.processOrderItems(e.data.items):t.getOrderItems(),1!=t.purchaseOrder.status&&3!=t.purchaseOrder.status)return t.loading=!1,i.error("只有已通过或已完成的订单才能申请退货"),void setTimeout((function(){i.goback()}),1500);if(!t.purchaseOrder.id||!t.purchaseOrder.order_no)return t.loading=!1,i.error("订单信息不完整，无法申请退货"),void setTimeout((function(){i.goback()}),1500)}else t.loading=!1,i.error(e.msg||"获取订单详情失败"),setTimeout((function(){i.goback()}),1500)}))},getOrderItems:function(){var t=this;i.post("ApiAdminPurchase/getPurchaseOrderItems",{purchase_order_id:t.purchaseId},(function(e){if(t.loading=!1,0===e.code){var r=e.data.items||[];console.log("获取到订单商品:",r),t.processOrderItems(r)}else i.error(e.msg||"获取商品数据失败"),setTimeout((function(){i.goback()}),1500)}))},processOrderItems:function(t){return t&&0!==t.length?(t.forEach((function(t){!t.pic&&t.product_pic&&(t.pic=t.product_pic),!t.pic&&t.product_detail&&t.product_detail.pic&&(t.pic=t.product_detail.pic),!t.name&&t.product_name&&(t.name=t.product_name),!t.name&&t.product_detail&&t.product_detail.name&&(t.name=t.product_detail.name),void 0===t.available_for_return&&void 0!==t.quantity?t.available_for_return=t.quantity-(t.returned_quantity||0):void 0===t.available_for_return&&(t.available_for_return=0)})),t=t.filter((function(t){return(t.available_for_return||0)>0})),0===t.length?(console.log("订单所有商品已退货完毕:",this.purchaseId),i.error("该订单所有商品已退货完毕，没有可退商品"),void setTimeout((function(){i.goback()}),1500)):(this.productList=t,this.loading=!1,void console.log("处理后的商品数据:",this.productList))):(console.log("订单没有可退商品:",this.purchaseId),i.error("该订单没有可退商品"),void setTimeout((function(){i.goback()}),1500))},getStatusText:function(t){return{0:"待审核",1:"已通过",2:"已驳回",3:"已完成"}[t]||"未知状态"},getStatusColor:function(t){return{0:"#FF9800",1:"#4CAF50",2:"#F44336",3:"#2196F3"}[t]||"#999999"},isProductSelected:function(t){return this.selectedProducts.findIndex((function(e){return e.id===t}))>-1},toggleSelectProduct:function(t){var e=this.selectedProducts.findIndex((function(e){return e.id===t.id}));e>-1?this.selectedProducts.splice(e,1):this.selectedProducts.push({id:t.id,item_id:t.item_id,quantity:1})},getSelectedQuantity:function(t){var e=this.selectedProducts.find((function(e){return e.id===t}));return e?e.quantity:0},updateQuantity:function(t,e){var r=this.selectedProducts.findIndex((function(e){return e.id===t.id})),n=void 0!==t.available_for_return?t.available_for_return:t.quantity;if(r>-1){var o=this.selectedProducts[r].quantity+e;o<=0?this.selectedProducts.splice(r,1):o<=n?this.selectedProducts[r].quantity=o:i.error("超出可退数量")}},onQuantityInput:function(t,e){var r=parseInt(t.detail.value),n=void 0!==e.available_for_return?e.available_for_return:e.quantity;(isNaN(r)||r<0)&&(r=0),r>n&&(r=n,i.error("超出可退数量"));var o=this.selectedProducts.findIndex((function(t){return t.id===e.id}));0===r&&o>-1?this.selectedProducts.splice(o,1):r>0&&(o>-1?this.selectedProducts[o].quantity=r:this.selectedProducts.push({id:e.id,item_id:e.item_id,quantity:r}))},getTotalItems:function(){return this.selectedProducts.length},getTotalPrice:function(){var t=this,e=0;return this.selectedProducts.forEach((function(r){var i=t.productList.find((function(t){return t.id===r.id}));if(i){var n=i.price||(i.product_detail?i.product_detail.sell_price||i.product_detail.cost_price:i.sell_price||i.cost_price||0);e+=n*r.quantity}})),e.toFixed(2)},submitOrder:function(){if(0!==this.selectedProducts.length)if(this.remark.trim()){var t=[],e=[];this.selectedProducts.forEach((function(r){t.push(r.item_id),e.push(r.quantity)})),i.post("ApiAdminPurchase/createReturnOrder",{purchase_order_id:this.purchaseId,item_ids:t.join(","),quantities:e.join(","),remark:this.remark},(function(t){0===t.code?(i.success("退货申请提交成功"),setTimeout((function(){i.goto("../return/detail?id="+t.data.order_id)}),1e3)):i.error(t.msg)}))}else i.error("请输入退货原因");else i.error("请选择要退货的商品")}}};e.default=n}},[["ebaa","common/runtime","common/vendor"]]]);