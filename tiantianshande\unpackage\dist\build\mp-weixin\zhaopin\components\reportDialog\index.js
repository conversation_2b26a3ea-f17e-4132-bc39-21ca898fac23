(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["zhaopin/components/reportDialog/index"],{"0663":function(t,e,o){"use strict";o.r(e);var i=o("3ef3"),n=o("7b70");for(var r in n)["default"].indexOf(r)<0&&function(t){o.d(e,t,(function(){return n[t]}))}(r);o("321e");var a=o("828b"),s=Object(a["a"])(n["default"],i["b"],i["c"],!1,null,null,null,!1,i["a"],void 0);e["default"]=s.exports},"321e":function(t,e,o){"use strict";var i=o("4550"),n=o.n(i);n.a},"3ef3":function(t,e,o){"use strict";o.d(e,"b",(function(){return i})),o.d(e,"c",(function(){return n})),o.d(e,"a",(function(){}));var i=function(){var t=this,e=t.$createElement,o=(t._self._c,t.visible&&16!==t.partJobVo.template.templateId&&11!==t.partJobVo.template.templateId?t.famousList.length:null),i=t.visible&&16!==t.partJobVo.template.templateId&&11!==t.partJobVo.template.templateId&&o>0?t.__map(t.famousList,(function(e,o){var i=t.__get_orig(e),n=t.famousList.length,r=t.famousList.length;return{$orig:i,g1:n,g2:r}})):null,n=t.visible&&!t.tips?t.t("color1"):null;t.$mp.data=Object.assign({},{$root:{g0:o,l0:i,m0:n}})},n=[]},4550:function(t,e,o){},"7b70":function(t,e,o){"use strict";o.r(e);var i=o("ba960"),n=o.n(i);for(var r in i)["default"].indexOf(r)<0&&function(t){o.d(e,t,(function(){return i[t]}))}(r);e["default"]=n.a},ba960:function(t,e,o){"use strict";(function(t){var i=o("47a9");Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n=i(o("7ca3"));function r(t,e){var o=Object.keys(t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);e&&(i=i.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),o.push.apply(o,i)}return o}function a(t){for(var e=1;e<arguments.length;e++){var o=null!=arguments[e]?arguments[e]:{};e%2?r(Object(o),!0).forEach((function(e){(0,n.default)(t,e,o[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(o)):r(Object(o)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(o,e))}))}return t}var s=getApp(),l={components:{payDialog:function(){o.e("zhaopin/components/payDialog/index").then(function(){return resolve(o("6abfd"))}.bind(null,o)).catch(o.oe)}},data:function(){return{tips:"",applyId:"",sendData:{},payVisible:!1,successVisible:!1,contactWay:"",contactNo:"",reportVisibleClone:!1,famousList:[],requireList:[],partJobVoClone:{jobFeeVO:{feeRushPrice:0,rushStatus:0}},serviceQrcode:"",serviceTips:"扫码添加客服微信，获取更多工作机会"}},created:function(){this.initData()},props:{visible:{type:Boolean,default:!0},partJobId:{type:String,default:""},reportVisible:{type:Boolean,default:!1},partJobVo:{type:Object,default:function(){return{}}},famousJobList:{type:Array,default:function(){return[]}},hasLatitude:{type:Boolean,default:!1},recommendTips:{type:String,default:""},authorizedKey:{type:String,default:""},shareUserId:{type:String,default:""},source:{type:String,default:""},isPartDetails:{type:Boolean,default:!1},fromRecommend:{type:Boolean,default:!1},agreementVo:{type:Object,default:function(){return{}}},isDirect:{type:Boolean,default:!1},reportDialogText:{type:String,default:"报名后需要参与课程学习，技能提升后才会有就业机会哦～"}},beforeMount:function(){},methods:{initData:function(){this.famousList=this.famousJobList||[],this.partJobVo&&this.partJobVo.requireList&&(this.requireList=this.partJobVo.requireList)},cancelPayHandle:function(){},successPayHandle:function(){},queryMobile:function(){},selectJob:function(t){},saveAgreeMent:function(){},applyValidate:function(){console.log("点击确认报名");var e=this;if(e.partJobVo.is_apply)return t.showToast({title:"您已报名过该职位",icon:"none"}),void e.closeReportDialog();e.partJobId?(console.log("开始报名，职位ID:",e.partJobId),s.post("/apiZhaopin/apply",{position_id:e.partJobId},(function(o){console.log("报名接口返回:",o),1==o.status?(console.log("报名成功，显示成功弹窗"),e.reportVisibleClone=!1,setTimeout((function(){e.successVisible=!0,e.partJobVoClone.hasApply=!0,e.partJobVo.is_apply=1,e.getServiceQrcode(),t.showToast({title:"报名成功，客服会马上联系您，请保持电话畅通",icon:"success"})}),300)):(e.closeReportDialog(),setTimeout((function(){o.msg?t.showToast({title:o.msg,icon:"none"}):t.showToast({title:"报名失败",icon:"none"})}),300))}))):t.showToast({title:"职位ID不能为空",icon:"none"})},goApplyJob:function(t,e,o,i){},normalApply:function(){},closeReportDialog:function(){console.log("关闭报名弹窗"),this.reportVisibleClone=!1,this.successVisible=!1,this.$emit("close")},copyContact:function(){},signUserContacted:function(){},t:function(t){if("color1"==t)return getApp().globalData.initdata.color1;if("color2"==t)return getApp().globalData.initdata.color2;if("color1rgb"==t){var e=getApp().globalData.initdata.color1rgb;return e["red"]+","+e["green"]+","+e["blue"]}if("color2rgb"==t){var o=getApp().globalData.initdata.color2rgb;return o["red"]+","+o["green"]+","+o["blue"]}return getApp().globalData.initdata.textset[t]||t},getServiceQrcode:function(){var t=this;s.get("/apiZhaopin/getServiceQrcode",{},(function(e){console.log("获取客服二维码信息:",e),1==e.status&&e.data&&(t.serviceQrcode=e.data.service_qrcode||"",t.serviceTips=e.data.service_tips||"扫码添加客服微信，获取更多工作机会")}))},closeSuccessDialog:function(){console.log("关闭成功弹窗"),this.successVisible=!1,this.closeReportDialog()}},watch:{reportVisible:{handler:function(t,e){console.log("reportDialog - visible变化:",t),this.reportVisibleClone=t},immediate:!0},visible:{handler:function(t){console.log("reportDialog - visible变化:",t),this.reportVisibleClone=t,t&&this.initData()},immediate:!0},famousJobList:{handler:function(t){console.log("reportDialog - famousJobList变化:",t),Array.isArray(t)&&(this.famousList=t)},immediate:!0},partJobVo:{handler:function(t){t&&(this.partJobVoClone=a(a({},t),{},{jobFeeVO:t.jobFeeVO||{feeRushPrice:0,rushStatus:0}}),t.requireList&&(this.requireList=t.requireList))},immediate:!0,deep:!0}}};e.default=l}).call(this,o("df3c")["default"])}}]);
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'zhaopin/components/reportDialog/index-create-component',
    {
        'zhaopin/components/reportDialog/index-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('df3c')['createComponent'](__webpack_require__("0663"))
        })
    },
    [['zhaopin/components/reportDialog/index-create-component']]
]);
