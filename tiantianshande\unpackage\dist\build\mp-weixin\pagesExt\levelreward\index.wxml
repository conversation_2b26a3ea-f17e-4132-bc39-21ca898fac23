<view><block wx:if="{{isload}}"><block><view class="container nodiydata"><view class="topcontent"><view class="logo"><view class="level-icon" style="{{'background:'+($root.m0)+';'}}">{{rewardRule.name?$root.g0:'R'}}</view></view><view class="title">{{rewardRule.name}}</view><view class="desc"><view class="{{['status-tag',rewardRule.status==1?'active':'inactive']}}">{{''+(rewardRule.status==1?'启用中':'已禁用')+''}}</view></view></view><view class="contentbox"><view class="shop_tab"><view class="{{['cptab_text '+(st==0?'cptab_current':'')]}}" data-st="0" data-event-opts="{{[['tap',[['changetab',['$event']]]]]}}" bindtap="__e">规则详情<view class="after" style="{{'background:'+($root.m1)+';'}}"></view></view><view class="{{['cptab_text '+(st==1?'cptab_current':'')]}}" data-st="1" data-event-opts="{{[['tap',[['changetab',['$event']]]]]}}" bindtap="__e">奖励记录<view class="after" style="{{'background:'+($root.m2)+';'}}"></view></view><view class="{{['cptab_text '+(st==2?'cptab_current':'')]}}" data-st="2" data-event-opts="{{[['tap',[['changetab',['$event']]]]]}}" bindtap="__e">统计数据<view class="after" style="{{'background:'+($root.m3)+';'}}"></view></view></view><block wx:if="{{st==0}}"><view class="cp_detail"><view class="detail-section"><view class="section-title">推荐人等级</view><view class="section-content"><block wx:for="{{$root.l0}}" wx:for-item="levelName" wx:for-index="idx"><block wx:if="{{levelName.g1}}"><view class="level-tag">{{''+levelName.$orig+''}}</view></block><block wx:else><view class="level-tag">{{rewardRule.from_level_name}}</view></block></block></view></view><view class="detail-section"><view class="section-title">被推荐人等级</view><view class="section-content"><block wx:for="{{$root.l1}}" wx:for-item="levelName" wx:for-index="idx"><block wx:if="{{levelName.g2}}"><view class="level-tag">{{''+levelName.$orig+''}}</view></block><block wx:else><view class="level-tag">{{rewardRule.to_level_name}}</view></block></block></view></view><view class="detail-section"><view class="section-title">奖励模式</view><view class="section-content"><view class="{{['reward-mode-tag',rewardRule.reward_mode]}}"><text class="reward-mode-icon"><text class="{{['iconfont',rewardRule.reward_mode==='regular'?'icon-gift':'icon-chart-line']}}"></text></text><text class="reward-mode-text">{{''+(rewardRule.reward_mode==='regular'?'常规奖励':'累计创客奖励')+''}}</text><view class="reward-mode-desc">{{''+(rewardRule.reward_mode==='regular'?'每推荐一个人获得一次奖励':'累计达到一定人数时发放奖励')+''}}</view></view></view></view><view class="detail-section"><view class="section-title">奖励规则</view><view class="section-content"><block wx:for="{{$root.l2}}" wx:for-item="rule" wx:for-index="idx" wx:key="idx"><view class="reward-rule-item"><view class="rule-icon" style="{{'background:'+(rule.m4)+';'}}">{{idx+1}}</view><view class="rule-content"><view class="rule-text"><block wx:if="{{rewardRule.reward_mode==='regular'}}"><text>每推荐<text class="highlight">{{rule.$orig.recommend_count}}</text>个人</text></block><block wx:else><text>累计达到<text class="highlight">{{rule.$orig.recommend_count}}</text>人</text></block></view><view class="rule-text">奖励<text class="highlight">{{rule.$orig.reward_amount}}</text>{{''+(rule.$orig.reward_type==='balance'?rule.m5:rule.$orig.reward_type==='points'?rule.m6:rule.$orig.reward_type==='commission'?rule.m7:rule.$orig.reward_type==='xianjinquan'?rule.m8:rule.m9)+''}}</view></view></view></block></view></view><view class="detail-section"><view class="section-title">创建时间</view><view class="section-content"><view class="time-text">{{$root.m10}}</view></view></view></view></block><block wx:if="{{st==1}}"><view class="cp_detail"><view class="rewards-list"><block wx:if="{{$root.g3>0}}"><block><block wx:for="{{$root.l3}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="reward-record"><view class="record-header"><view class="user-info"><image class="avatar" src="{{item.$orig.to_headimg||'/static/img/default_avatar.png'}}"></image><view class="user-details"><view class="nickname">{{item.$orig.to_nickname}}</view><view class="tel">{{item.$orig.to_tel}}</view></view></view><view class="reward-amount" style="{{'color:'+(item.m11)+';'}}">{{'+'+item.$orig.reward_amount+" "+item.m12+''}}</view></view><view class="record-content"><view class="level-info"><text>{{"推荐人等级: "+item.$orig.from_level_name}}</text><text class="separator">|</text><text>{{"被推荐人等级: "+item.$orig.to_level_name}}</text></view><view class="record-time">{{item.$orig.createtime_format}}</view></view></view></block></block></block><block wx:if="{{nomore}}"><nomore vue-id="0c3740d8-1" bind:__l="__l"></nomore></block><block wx:if="{{nodata}}"><nodata vue-id="0c3740d8-2" bind:__l="__l"></nodata></block></view></view></block><block wx:if="{{st==2}}"><view class="cp_detail"><view class="stats-cards"><view class="stats-card"><view class="stats-value">{{statsData.total_recommend||0}}</view><view class="stats-label">总推荐人数</view></view><view class="stats-card"><view class="stats-value">{{statsData.today_recommend||0}}</view><view class="stats-label">今日推荐</view></view><view class="stats-card"><view class="stats-value">{{statsData.total_reward||0}}</view><view class="stats-label">总奖励金额</view></view></view><block wx:if="{{myReferrer}}"><view class="my-referrer"><view class="section-title">我的推荐人</view><view class="referrer-card"><image class="referrer-avatar" src="{{myReferrer.headimg||'/static/img/default_avatar.png'}}"></image><view class="referrer-info"><view class="referrer-name">{{myReferrer.nickname}}</view><view class="referrer-tel">{{myReferrer.tel}}</view><view class="referrer-level">{{"等级: "+myReferrer.level_name}}</view></view></view></view></block><view class="my-referrals"><view class="section-title">{{"我推荐的会员 ("+(referralsCount||0)+")"}}</view><block wx:if="{{$root.g4>0}}"><view class="referrals-list"><block wx:for="{{myReferrals}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="referral-item"><image class="referral-avatar" src="{{item.headimg||'/static/img/default_avatar.png'}}"></image><view class="referral-info"><view class="referral-name">{{item.nickname}}</view><view class="referral-level">{{item.level_name}}</view></view><view class="referral-time">{{item.createtime_format}}</view></view></block></view></block><block wx:if="{{referralsNomore}}"><nomore vue-id="0c3740d8-3" bind:__l="__l"></nomore></block><block wx:if="{{referralsNodata}}"><nodata vue-id="0c3740d8-4" bind:__l="__l"></nodata></block></view></view></block></view><view data-event-opts="{{[['tap',[['goBack',['$event']]]]]}}" class="covermy" style="top:75vh;background:rgba(0,0,0,0.7);" bindtap="__e"><text style="padding:0 4rpx;height:36rpx;line-height:36rpx;">返回</text><text style="padding:0 4rpx;height:36rpx;line-height:36rpx;">上一页</text></view></view></block></block><block wx:if="{{loading}}"><loading vue-id="0c3740d8-5" bind:__l="__l"></loading></block><dp-tabbar vue-id="0c3740d8-6" opt="{{opt}}" data-event-opts="{{[['^getmenuindex',[['getmenuindex']]]]}}" bind:getmenuindex="__e" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="0c3740d8-7" data-ref="popmsg" bind:__l="__l"></popmsg></view>