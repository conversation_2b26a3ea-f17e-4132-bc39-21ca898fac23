<view class="content"><view class="header"><view class="top"><view class="title-box"><view class="title">高压办电</view><view>帮代办、少跑腿</view></view><image class="logo" src="/static/highVoltage/icon1.png"></image></view><valtage-tab vue-id="704630ae-1" status="{{detailInfo.status}}" bind:__l="__l"></valtage-tab></view><view class="container"><view style="padding:10px;"><block wx:if="{{detailInfo.formdata&&detailInfo.formdata[0]}}"><view>{{''+detailInfo.formdata[0][0]+":"+detailInfo.formdata[0][1]+''}}</view></block><block wx:else><view>数据不完整</view></block><block wx:if="{{detailInfo.formdata&&detailInfo.formdata[1]}}"><view>{{''+detailInfo.formdata[1][0]+":"+detailInfo.formdata[1][1]+''}}</view></block><block wx:else><view>数据不完整</view></block><block wx:if="{{detailInfo.formdata&&detailInfo.formdata[2]}}"><view>{{''+detailInfo.formdata[2][0]+":"+detailInfo.formdata[2][1]+''}}</view></block><block wx:else><view>数据不完整</view></block><block wx:if="{{detailInfo.formdata&&detailInfo.formdata[3]}}"><view>{{''+detailInfo.formdata[3][0]+":"+detailInfo.formdata[3][1]+''}}</view></block><block wx:else><view>数据不完整</view></block></view><view class="title" style="padding-left:10px;">办电流程</view><process-schedule vue-id="704630ae-2" status="{{detailInfo.status}}" data="{{detailInfo}}" data-event-opts="{{[['^apply',[['applyVoltage']]]]}}" bind:apply="__e" bind:__l="__l"></process-schedule></view><view data-event-opts="{{[['tap',[['back',['$event']]]]]}}" class="btn back-i" bindtap="__e">返回</view></view>