<view><block wx:if="{{isload}}"><block><view class="wrap"><view class="top flex"><block wx:if="{{tkdata.type==1}}"><view class="f1">判断题</view></block><block wx:if="{{tkdata.type==2}}"><view class="f1">单选题</view></block><block wx:if="{{tkdata.type==3}}"><view class="f1">多选题</view></block><block wx:if="{{tkdata.type==4}}"><view class="f1">填空题</view></block><block wx:if="{{tkdata.type==5}}"><view class="f1">简答题</view></block><view class="f3">{{tkdata.sort+"/"+tkdata.nums}}</view></view><view class="question"><view class="title"><text class="serial-number">{{tkdata.sort+"."}}</text><rich-text nodes="{{tkdata.title}}"></rich-text></view><block wx:if="{{tkdata.type==1||tkdata.type==2}}"><block><view class="option_group"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['selectOption',[index]]]]]}}" class="{{['option '+(item.m0?'on':'')]}}" bindtap="__e"><view class="sort">{{tkdata.sorts[index]}}</view><view class="after"></view><view class="t1"><rich-text nodes="{{item.$orig}}"></rich-text></view></view></block></view></block></block><block wx:if="{{tkdata.type==3}}"><block><view class="option_group"><block wx:for="{{$root.l1}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['selectOption',[index]]]]]}}" class="{{['option '+(item.m1?'on':'')]}}" bindtap="__e"><view class="sort">{{tkdata.sorts[index]}}</view><view class="after"></view><view class="t1"><rich-text nodes="{{item.$orig}}"></rich-text></view></view></block></view></block></block><block wx:if="{{tkdata.type==4}}"><block><view class="option_group"><block wx:for="{{tkdata.option}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="input-group"><input class="uni-input" placeholder="请填写答案" data-event-opts="{{[['input',[['selectInput',[index,'$event']]]]]}}" value="{{tkdata.answer[index]||''}}" bindinput="__e"/></view></block></view></block></block><block wx:if="{{tkdata.type==5}}"><block><view class="option_group"><view class="uni-textarea"><textarea placeholder-style="color:#222" placeholder="答:" data-event-opts="{{[['blur',[['bindTextAreaBlur',['$event']]]]]}}" value="{{tkdata.answer}}" bindblur="__e"></textarea></view></view></block></block></view></view><view class="right_content"><text class="t1">正确答案</text><view class="t2">{{''+$root.m2+''}}<view>{{"题目解析："+tkdata.jiexi}}</view></view></view><view class="bottom flex"><block wx:if="{{tkdata.isup!=1}}"><block><button class="upbut flex-x-center flex-y-center hui">上一题</button></block></block><block wx:if="{{tkdata.isup==1}}"><block><button class="upbut flex-x-center flex-y-center" style="{{'background:'+($root.m3)+';'}}" data-dttype="up" data-event-opts="{{[['tap',[['toanswer',['$event']]]]]}}" bindtap="__e">上一题</button></block></block><block wx:if="{{tkdata.isdown==1}}"><button class="downbtn flex-x-center flex-y-center" style="{{'background:'+($root.m4)+';'}}" data-dttype="down" data-event-opts="{{[['tap',[['toanswer',['$event']]]]]}}" bindtap="__e">下一题</button></block><block wx:if="{{tkdata.isdown!=1}}"><button class="downbtn flex-x-center flex-y-center hui">下一题</button></block></view></block></block><block wx:if="{{loading}}"><loading vue-id="cad157f6-1" bind:__l="__l"></loading></block><dp-tabbar vue-id="cad157f6-2" opt="{{opt}}" data-event-opts="{{[['^getmenuindex',[['getmenuindex']]]]}}" bind:getmenuindex="__e" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="cad157f6-3" data-ref="popmsg" bind:__l="__l"></popmsg></view>