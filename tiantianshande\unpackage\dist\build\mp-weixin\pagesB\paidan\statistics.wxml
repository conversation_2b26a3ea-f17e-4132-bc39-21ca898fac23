<view class="container"><block wx:if="{{isload}}"><block><view class="header-container"><view class="title">排单统计</view><view class="subtitle">我的排单数据概览</view></view><view class="stats-grid"><view class="stats-card"><view class="stats-icon"><image class="icon" src="/static/img/paidan-total.png"></image></view><view class="stats-info"><view class="stats-value" style="color:#FF5722;">{{statistics.total_positions||0}}</view><view class="stats-label">总点位数</view></view></view><view class="stats-card"><view class="stats-icon"><image class="icon" src="/static/img/paidan-wealth.png"></image></view><view class="stats-info"><view class="stats-value" style="color:#FF5722;">{{statistics.wealth_positions||0}}</view><view class="stats-label">财富点位</view></view></view><view class="stats-card"><view class="stats-icon"><image class="icon" src="/static/img/paidan-rewarded.png"></image></view><view class="stats-info"><view class="stats-value" style="color:#FF5722;">{{statistics.rewarded_positions||0}}</view><view class="stats-label">已奖励点位</view></view></view><view class="stats-card"><view class="stats-icon"><image class="icon" src="/static/img/paidan-unrewarded.png"></image></view><view class="stats-info"><view class="stats-value" style="color:#FF5722;">{{statistics.unrewarded_positions||0}}</view><view class="stats-label">待奖励点位</view></view></view><view class="stats-card reward-card"><view class="stats-icon"><image class="icon" src="/static/img/paidan-reward.png"></image></view><view class="stats-info"><view class="stats-value" style="color:#FF5722;">{{"¥"+(statistics.total_reward||0)}}</view><view class="stats-label">累计奖励</view></view></view></view><view class="config-selector"><view class="selector-title">选择活动配置</view><scroll-view class="config-list" scroll-x="true"><block wx:for="{{configList}}" wx:for-item="item" wx:for-index="index" wx:key="id"><view data-event-opts="{{[['tap',[['selectConfig',['$0'],[[['configList','id',item.id,'id']]]]]]]}}" class="{{['config-item',(selectedConfigId==item.id)?'active':'']}}" bindtap="__e"><view class="config-name">{{item.name}}</view><view class="config-info">{{item.copy_mode_text+" · ¥"+item.wealth_reward_amount}}</view></view></block><view data-event-opts="{{[['tap',[['selectConfig',[0]]]]]}}" class="{{['config-item',(selectedConfigId==0)?'active':'']}}" bindtap="__e"><view class="config-name">全部活动</view><view class="config-info">所有配置统计</view></view></scroll-view></view><view class="action-buttons"><view data-event-opts="{{[['tap',[['toPositionList',['$event']]]]]}}" class="action-btn" style="background:linear-gradient(90deg, #FF5722 0%, rgba(255,87,34,0.8) 100%);" bindtap="__e">查看点位</view><view data-event-opts="{{[['tap',[['toRewardList',['$event']]]]]}}" class="action-btn" style="background:linear-gradient(90deg, #FF5722 0%, rgba(255,87,34,0.8) 100%);" bindtap="__e">奖励记录</view><view data-event-opts="{{[['tap',[['toPositionTree',['$event']]]]]}}" class="action-btn" style="background:linear-gradient(90deg, #FF5722 0%, rgba(255,87,34,0.8) 100%);" bindtap="__e">排单树图</view></view><block wx:if="{{$root.g0>0}}"><view class="chart-container"><view class="chart-title">最近7天点位增长</view><view class="chart-content"><block wx:for="{{chartData}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="chart-item"><view class="chart-bar"><view class="bar-fill" style="height:item.percentage + '%';background:#FF5722;"></view></view><view class="chart-label">{{item.date}}</view><view class="chart-value">{{item.count}}</view></view></block></view></view></block></block></block><block wx:if="{{loading}}"><loading vue-id="4e793fe0-1" loadstyle="left:62.5%" bind:__l="__l"></loading></block><dp-tabbar vue-id="4e793fe0-2" opt="{{opt}}" data-event-opts="{{[['^getmenuindex',[['getmenuindex']]]]}}" bind:getmenuindex="__e" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="4e793fe0-3" data-ref="popmsg" bind:__l="__l"></popmsg></view>