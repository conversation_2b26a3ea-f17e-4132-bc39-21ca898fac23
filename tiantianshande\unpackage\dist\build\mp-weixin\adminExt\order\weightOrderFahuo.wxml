<view class="container"><block wx:if="{{isload}}"><block><view class="address"><view class="img"><image src="{{pre_url+'/static/img/address3.png'}}"></image></view><view class="info"><view class="t1" user-select="true" selectable="true">{{detail.linkman+''}}<block wx:if="{{detail.tel}}"><text style="margin-left:20rpx;" data-url="{{'tel:'+detail.tel}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">{{detail.tel}}</text></block></view><block wx:if="{{detail.freight_type!=1&&detail.freight_type!=3}}"><text class="t2" user-select="true" selectable="true">{{"地址："+detail.area+detail.address}}</text></block><block wx:if="{{detail.freight_type==1}}"><text class="t2" data-address="{{storeinfo.address}}" data-latitude="{{storeinfo.latitude}}" data-longitude="{{storeinfo.longitude}}" user-select="true" selectable="true" data-event-opts="{{[['tap',[['openLocation',['$event']]]]]}}" bindtap="__e">{{"取货地点："+storeinfo.name+" - "+storeinfo.address}}</text></block></view></view><view class="orderinfo"><view class="item"><text class="t1">订单金额</text><text class="t2 red">{{"¥"+detail.product_price}}</text></view><block wx:if="{{detail.leveldk_money>0}}"><view class="item"><text class="t1">{{$root.m0+"折扣"}}</text><text class="t2 red">{{"-¥"+detail.leveldk_money}}</text></view></block><block wx:if="{{detail.manjian_money>0}}"><view class="item"><text class="t1">满减活动</text><text class="t2 red">{{"-¥"+detail.manjian_money}}</text></view></block><block wx:if="{{detail.coupon_money>0}}"><view class="item"><text class="t1">{{$root.m1+"抵扣"}}</text><text class="t2 red">{{"-¥"+detail.coupon_money}}</text></view></block><block wx:if="{{detail.scoredk_money>0}}"><view class="item"><text class="t1">{{$root.m2+"抵扣"}}</text><text class="t2 red">{{"-¥"+detail.scoredk_money}}</text></view></block><block wx:if="{{detail.dec_money>0}}"><view class="item"><text class="t1">{{$root.m3+"抵扣"}}</text><text class="t2 red">{{"-¥"+detail.dec_money}}</text></view></block><view class="item"><text class="t1">实付金额</text><text class="t2 red">{{"¥"+detail.totalprice}}</text></view><view class="item"><text class="t1">下单时间</text><text class="t2">{{detail.createtime}}</text></view><view class="item"><text class="t1">订单状态</text><block wx:if="{{detail.status==0}}"><text class="t2">未付款</text></block><block wx:if="{{detail.status==1}}"><text class="t2">待发货</text></block><block wx:if="{{detail.status==2}}"><text class="t2">已发货</text></block><block wx:if="{{detail.status==3}}"><text class="t2">已收货</text></block><block wx:if="{{detail.status==4}}"><text class="t2">已关闭</text></block></view><block wx:if="{{detail.refund_status>0}}"><view class="item"><text class="t1">退款状态</text><block wx:if="{{detail.refund_status==1}}"><text class="t2 red">{{"审核中,¥"+detail.refund_money}}</text></block><block wx:if="{{detail.refund_status==2}}"><text class="t2 red">{{"已退款,¥"+detail.refund_money}}</text></block><block wx:if="{{detail.refund_status==3}}"><text class="t2 red">{{"已驳回,¥"+detail.refund_money}}</text></block></view></block><block wx:if="{{detail.refund_status>0}}"><view class="item"><text class="t1">退款原因</text><text class="t2 red">{{detail.refund_reason||'暂无'}}</text></view></block><block wx:if="{{detail.refund_checkremark}}"><view class="item"><text class="t1">审核备注</text><text class="t2 red">{{detail.refund_checkremark}}</text></view></block></view><view class="product"><view class="colitem row-header"><view class="col col-1">商品</view><view class="col col-2">单价(元/斤)</view><view class="col col-3">应拣(斤)</view><view class="col col-4">实拣(斤)</view><view class="col col-5">总价</view></view><block wx:for="{{prolist}}" wx:for-item="item" wx:for-index="idx" wx:key="idx"><view class="colitem"><view class="col col-1"><view>{{item.name}}</view><view>{{item.ggname}}</view></view><view class="col col-2"><input type="text" data-index="{{idx}}" data-field="real_sell_price" data-event-opts="{{[['input',[['inputChange',['$event']]]]]}}" value="{{item.real_sell_price}}" bindinput="__e"/></view><view class="col col-3">{{item.total_weight}}</view><view class="col col-4"><input type="text" data-index="{{idx}}" data-field="real_total_weight" data-event-opts="{{[['input',[['inputChange',['$event']]]]]}}" value="{{item.real_total_weight}}" bindinput="__e"/></view><view class="col col-5">{{item.real_totalprice}}</view></view></block><view class="heji"><text>合计：</text><text style="{{'color:'+($root.m4)+';'}}">{{"￥"+totalprice}}</text></view></view><view class="tips"><view>* 发货重量为实际结算重量;</view><view>* 实拣重量小于购买重量，订单差额会原路退还用户;</view><view>* 实拣重量大于购买重量，用户无法追加订单金额，请谨慎操作！</view></view><view style="width:100%;height:160rpx;"></view><view class="bottom"><view data-event-opts="{{[['tap',[['fahuo',['$event']]]]]}}" class="btn2" style="{{'background:'+($root.m5)+';'+('color:'+('#FFF')+';')}}" bindtap="__e">确定发货</view></view></block></block><block wx:if="{{loading}}"><loading vue-id="90f20f16-1" bind:__l="__l"></loading></block><dp-tabbar vue-id="90f20f16-2" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="90f20f16-3" data-ref="popmsg" bind:__l="__l"></popmsg></view>