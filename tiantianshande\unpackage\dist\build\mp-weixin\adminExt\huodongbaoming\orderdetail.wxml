<view class="container"><block wx:if="{{isload}}"><block><view class="ordertop" style="{{('background:url('+pre_url+'/static/img/ordertop.png);background-size:100%')}}"><block wx:if="{{detail.status==0}}"><view class="f1"><view class="t1">等待买家付款</view><block wx:if="{{djs}}"><view class="t2">{{"剩余时间："+djs}}</view></block></view></block><block wx:if="{{detail.status==1}}"><view class="f1"><view class="t1">已成功付款</view><view class="t2">待核销</view></view></block><block wx:if="{{detail.status==3}}"><view class="f1"><view class="t1">订单已完成</view></view></block><block wx:if="{{detail.status==4}}"><view class="f1"><view class="t1">订单已取消</view></view></block></view><view class="product"><view class="content"><view data-url="{{'/pagesB/huodongbaoming/product?id='+detail.proid}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image src="{{detail.propic}}"></image></view><view class="detail"><text class="t1">{{detail.proname}}</text><block wx:if="{{detail.totalprice>0&&detail.totalscore>0}}"><block><text class="t2">{{"实付金额：￥"+detail.totalprice+" + "+detail.totalscore+''}}</text></block></block><block wx:if="{{detail.totalprice>0&&detail.totalscore==0}}"><block><text class="t2">{{"实付金额：￥"+detail.totalprice+''}}</text></block></block><block wx:if="{{detail.totalprice==0&&detail.totalscore>0}}"><block><text class="t2">{{"实付金额："+detail.totalscore+" "+$root.m0}}</text></block></block></view></view></view><block wx:if="{{(detail.status==3||detail.status==2)&&(detail.freight_type==3||detail.freight_type==4)}}"><view class="orderinfo"><view class="item flex-col"><text class="t1" style="color:#111;">发货信息</text><text class="t2" style="text-align:left;margin-top:10rpx;padding:0 10rpx;" user-select="true" selectable="true">{{detail.freight_content}}</text></view></view></block><view class="orderinfo"><view class="item"><text class="t1">下单人</text><text class="flex1"></text><image style="width:80rpx;height:80rpx;margin-right:8rpx;" src="{{detail.member.headimg}}"></image><text style="height:80rpx;line-height:80rpx;">{{detail.member.nickname}}</text></view><view class="item"><text class="t1">{{$root.m1+"ID"}}</text><text class="t2">{{detail.mid}}</text></view></view><view class="orderinfo"><view class="item"><text class="t1">姓名</text><text class="t2">{{detail.linkman}}</text></view><view class="item"><text class="t1">手机号</text><text class="t2">{{detail.tel}}</text></view></view><block wx:if="{{detail.remark}}"><view class="orderinfo"><view class="item"><text class="t1">备注</text><text class="t2">{{detail.remark}}</text></view></view></block><view class="orderinfo"><view class="item"><text class="t1">订单编号</text><text class="t2" user-select="true" selectable="true">{{detail.ordernum}}</text></view><view class="item"><text class="t1">下单时间</text><text class="t2">{{detail.createtime}}</text></view><block wx:if="{{detail.status>0&&detail.paytypeid!='4'&&detail.paytime}}"><view class="item"><text class="t1">支付时间</text><text class="t2">{{detail.paytime}}</text></view></block><block wx:if="{{detail.status>0&&detail.paytime}}"><view class="item"><text class="t1">支付方式</text><block wx:if="{{detail.totalmoney>0||detail.totalscore>0}}"><text class="t2">{{detail.paytype}}</text></block><block wx:else><text class="t2">无须付款</text></block></view></block><block wx:if="{{detail.status>1&&detail.send_time}}"><view class="item"><text class="t1">发货时间</text><text class="t2">{{detail.send_time}}</text></view></block><block wx:if="{{detail.status==3&&detail.collect_time}}"><view class="item"><text class="t1">完成时间</text><text class="t2">{{detail.collect_time}}</text></view></block></view><view class="orderinfo"><view class="item"><text class="t1">商品金额</text><block wx:if="{{detail.totalmoney>0}}"><view class="t2 red">{{"¥"+detail.totalmoney+" + "+detail.totalscore+$root.m2}}</view></block><block wx:else><text class="t2 red">{{detail.totalscore+$root.m3}}</text></block></view><block wx:if="{{detail.disprice>0}}"><view class="item"><text class="t1">{{$root.m4+"折扣"}}</text><text class="t2 red">{{"-¥"+detail.leveldk_money}}</text></view></block><block wx:if="{{detail.jianmoney>0}}"><view class="item"><text class="t1">满减活动</text><text class="t2 red">{{"-¥"+detail.manjian_money}}</text></view></block><block wx:if="{{detail.couponmoney>0}}"><view class="item"><text class="t1">{{$root.m5+"抵扣"}}</text><text class="t2 red">{{"-¥"+detail.coupon_money}}</text></view></block><block wx:if="{{detail.scoredk>0}}"><view class="item"><text class="t1">{{$root.m6+"抵扣"}}</text><text class="t2 red">{{"-¥"+detail.scoredk_money}}</text></view></block><view class="item"><text class="t1">实付款</text><text class="t2 red">{{"¥"+detail.totalprice}}</text></view><view class="item"><text class="t1">订单状态</text><block wx:if="{{detail.status==0}}"><text class="t2">未付款</text></block><block wx:if="{{detail.status==1}}"><text class="t2">已付款</text></block><block wx:if="{{detail.status==3}}"><text class="t2">已完成</text></block><block wx:if="{{detail.status==4}}"><text class="t2">已关闭</text></block></view><block wx:if="{{detail.refund_status>0}}"><view class="item"><text class="t1">退款状态</text><block wx:if="{{detail.refund_status==1}}"><text class="t2 red">{{"审核中,¥"+detail.refund_money}}</text></block><block wx:if="{{detail.refund_status==2}}"><text class="t2 red">{{"已退款,¥"+detail.refund_money}}</text></block><block wx:if="{{detail.refund_status==3}}"><text class="t2 red">{{"已驳回,¥"+detail.refund_money}}</text></block></view></block><block wx:if="{{detail.refund_status>0}}"><view class="item"><text class="t1">退款原因</text><text class="t2 red">{{detail.refund_reason}}</text></view></block><block wx:if="{{detail.refund_checkremark}}"><view class="item"><text class="t1">审核备注</text><text class="t2 red">{{detail.refund_checkremark}}</text></view></block><block wx:if="{{detail.status==1||detail.status==2}}"><view class="item flex-col"><text class="t1">核销码</text><view class="flex-x-center"><image style="width:400rpx;height:400rpx;" src="{{detail.hexiao_qr}}" data-url="{{detail.hexiao_qr}}" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image></view></view></block></view><block wx:if="{{$root.g0>0}}"><view class="orderinfo"><block wx:for="{{detail.formdata}}" wx:for-item="item" wx:for-index="__i0__" wx:key="*this"><view class="item"><text class="t1">{{item[0]}}</text><block wx:if="{{item[2]=='upload'}}"><view class="t2"><image style="width:400rpx;height:auto;" src="{{item[1]}}" mode="widthFix" data-url="{{item[1]}}" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image></view></block><block wx:else><text class="t2" user-select="true" selectable="true">{{item[1]}}</text></block></view></block></view></block><view style="width:100%;height:calc(160rpx + env(safe-area-inset-bottom));"></view><view class="bottom notabbarbot"><block wx:if="{{detail.refund_status==1}}"><view class="btn2" data-id="{{detail.id}}" data-event-opts="{{[['tap',[['refundnopass',['$event']]]]]}}" bindtap="__e">退款驳回</view></block><block wx:if="{{detail.refund_status==1}}"><view class="btn2" data-id="{{detail.id}}" data-event-opts="{{[['tap',[['refundpass',['$event']]]]]}}" bindtap="__e">退款通过</view></block><block wx:if="{{detail.status==0}}"><view class="btn2" data-id="{{detail.id}}" data-event-opts="{{[['tap',[['closeOrder',['$event']]]]]}}" bindtap="__e">关闭订单</view></block><block wx:if="{{detail.status==1}}"><view class="btn2" data-id="{{detail.id}}" data-event-opts="{{[['tap',[['hexiao',['$event']]]]]}}" bindtap="__e">核销</view></block><block wx:if="{{detail.status==4}}"><view class="btn2" data-id="{{detail.id}}" data-event-opts="{{[['tap',[['delOrder',['$event']]]]]}}" bindtap="__e">删除</view></block><view class="btn2" data-id="{{detail.id}}" data-event-opts="{{[['tap',[['setremark',['$event']]]]]}}" bindtap="__e">设置备注</view></view><uni-popup class="vue-ref" vue-id="57f38dd8-1" id="dialogSetremark" type="dialog" data-ref="dialogSetremark" bind:__l="__l" vue-slots="{{['default']}}"><uni-popup-dialog vue-id="{{('57f38dd8-2')+','+('57f38dd8-1')}}" mode="input" title="设置备注" value="{{detail.remark}}" placeholder="请输入备注" data-event-opts="{{[['^confirm',[['setremarkconfirm']]]]}}" bind:confirm="__e" bind:__l="__l"></uni-popup-dialog></uni-popup></block></block><block wx:if="{{loading}}"><loading vue-id="57f38dd8-3" bind:__l="__l"></loading></block><dp-tabbar vue-id="57f38dd8-4" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="57f38dd8-5" data-ref="popmsg" bind:__l="__l"></popmsg></view>