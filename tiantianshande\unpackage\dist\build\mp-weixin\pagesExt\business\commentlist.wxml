<view class="container"><view class="shop"><image class="p1" src="{{business.logo}}"></image><view class="p2 flex1"><view class="t1">{{business.name}}</view><view class="t2">{{business.desc}}</view></view><button class="p4" style="{{'background:'+('linear-gradient(90deg,'+$root.m0+' 0%,rgba('+$root.m1+',0.8) 100%)')+';'}}" data-url="{{'/pagesExt/business/index?id='+business.id}}" data-opentype="reLaunch" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">进入店铺</button></view><view class="title"><view class="f1">{{"评价("+business.comment_num+")"}}</view><view data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" class="f2" bindtap="__e">好评率<text style="{{'color:'+($root.m2)+';'}}">{{business.comment_haopercent+"%"}}</text></view></view><view class="comment"><block wx:for="{{datalist}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="item"><view class="f1"><image class="t1" src="{{item.headimg}}"></image><view class="t2">{{item.nickname}}</view><view class="flex1"></view><view class="t3"><block wx:for="{{[0,1,2,3,4]}}" wx:for-item="item2" wx:for-index="index2" wx:key="index2"><image class="img" src="{{'/static/img/star'+(item.score>item2?'2':'')+'.png'}}"></image></block></view></view><view style="color:#777;font-size:22rpx;">{{item.createtime}}</view><view class="f2"><text class="t1">{{item.content}}</text><view class="t2"><block wx:if="{{item.content_pic!=''}}"><block><block wx:for="{{item.content_pic}}" wx:for-item="itemp" wx:for-index="index" wx:key="index"><block><view data-url="{{itemp}}" data-urls="{{item.content_pic}}" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"><image src="{{itemp}}" mode="widthFix"></image></view></block></block></block></block></view></view><block wx:if="{{item.reply_content}}"><view class="f3"><view class="arrow"></view><view class="t1">{{"商家回复："+item.reply_content}}</view></view></block></view></block></view><block wx:if="{{nomore}}"><nomore vue-id="4ddb95b8-1" bind:__l="__l"></nomore></block><block wx:if="{{nodata}}"><nodata vue-id="4ddb95b8-2" text="暂无评价~" bind:__l="__l"></nodata></block><block wx:if="{{loading}}"><loading vue-id="4ddb95b8-3" bind:__l="__l"></loading></block><dp-tabbar vue-id="4ddb95b8-4" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="4ddb95b8-5" data-ref="popmsg" bind:__l="__l"></popmsg></view>