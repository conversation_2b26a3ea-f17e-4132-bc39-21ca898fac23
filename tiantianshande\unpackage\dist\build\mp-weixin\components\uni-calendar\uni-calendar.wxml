<view class="uni-calendar data-v-7454b26b"><block wx:if="{{!insert&&show}}"><view data-event-opts="{{[['tap',[['clean',['$event']]]]]}}" class="{{['uni-calendar__mask','data-v-7454b26b',(aniMaskShow)?'uni-calendar--mask-show':'']}}" bindtap="__e"></view></block><block wx:if="{{insert||show}}"><view class="{{['uni-calendar__content','data-v-7454b26b',(!insert)?'uni-calendar--fixed':'',(aniMaskShow)?'uni-calendar--ani-show':'']}}"><block wx:if="{{!insert}}"><view class="uni-calendar__header uni-calendar--fixed-top data-v-7454b26b"><view data-event-opts="{{[['tap',[['close',['$event']]]]]}}" class="uni-calendar__header-btn-box data-v-7454b26b" bindtap="__e"><text class="uni-calendar__header-text uni-calendar--fixed-width data-v-7454b26b">{{cancelText}}</text></view><view data-event-opts="{{[['tap',[['confirm',['$event']]]]]}}" class="uni-calendar__header-btn-box data-v-7454b26b" bindtap="__e"><text class="uni-calendar__header-text uni-calendar--fixed-width data-v-7454b26b">{{okText}}</text></view></view></block><view class="uni-calendar__header data-v-7454b26b"><view data-event-opts="{{[['tap',[['pre',['$event']]]]]}}" class="uni-calendar__header-btn-box data-v-7454b26b" catchtap="__e"><view class="uni-calendar__header-btn uni-calendar--left data-v-7454b26b"></view></view><picker mode="date" value="{{date}}" fields="month" data-event-opts="{{[['change',[['bindDateChange',['$event']]]]]}}" bindchange="__e" class="data-v-7454b26b"><text class="uni-calendar__header-text data-v-7454b26b">{{(nowDate.year||'')+' / '+(nowDate.month||'')}}</text></picker><view data-event-opts="{{[['tap',[['next',['$event']]]]]}}" class="uni-calendar__header-btn-box data-v-7454b26b" catchtap="__e"><view class="uni-calendar__header-btn uni-calendar--right data-v-7454b26b"></view></view></view><view class="uni-calendar__box data-v-7454b26b"><block wx:if="{{showMonth}}"><view class="uni-calendar__box-bg data-v-7454b26b"><text class="uni-calendar__box-bg-text data-v-7454b26b">{{nowDate.month}}</text></view></block><view class="uni-calendar__weeks data-v-7454b26b"><view class="uni-calendar__weeks-day data-v-7454b26b"><text class="uni-calendar__weeks-day-text data-v-7454b26b">{{SUNText}}</text></view><view class="uni-calendar__weeks-day data-v-7454b26b"><text class="uni-calendar__weeks-day-text data-v-7454b26b">{{monText}}</text></view><view class="uni-calendar__weeks-day data-v-7454b26b"><text class="uni-calendar__weeks-day-text data-v-7454b26b">{{TUEText}}</text></view><view class="uni-calendar__weeks-day data-v-7454b26b"><text class="uni-calendar__weeks-day-text data-v-7454b26b">{{WEDText}}</text></view><view class="uni-calendar__weeks-day data-v-7454b26b"><text class="uni-calendar__weeks-day-text data-v-7454b26b">{{THUText}}</text></view><view class="uni-calendar__weeks-day data-v-7454b26b"><text class="uni-calendar__weeks-day-text data-v-7454b26b">{{FRIText}}</text></view><view class="uni-calendar__weeks-day data-v-7454b26b"><text class="uni-calendar__weeks-day-text data-v-7454b26b">{{SATText}}</text></view></view><block wx:for="{{weeks}}" wx:for-item="item" wx:for-index="weekIndex" wx:key="weekIndex"><view class="uni-calendar__weeks data-v-7454b26b"><block wx:for="{{item}}" wx:for-item="weeks" wx:for-index="weeksIndex" wx:key="weeksIndex"><view class="uni-calendar__weeks-item data-v-7454b26b"><calendar-item class="uni-calendar-item--hook data-v-7454b26b" vue-id="{{'138a9022-1-'+weekIndex+'-'+weeksIndex}}" weeks="{{weeks}}" calendar="{{calendar}}" selected="{{selected}}" lunar="{{lunar}}" backColor="{{backColor}}" fontColor="{{fontColor}}" data-event-opts="{{[['^change',[['choiceDate']]]]}}" bind:change="__e" bind:__l="__l"></calendar-item></view></block></view></block></view></view></block></view>