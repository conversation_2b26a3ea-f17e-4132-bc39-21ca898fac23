<view class="page"><block wx:if="{{loading}}"><loading vue-id="e0f569e6-1" bind:__l="__l"></loading></block><view class="form-container"><view class="form-item"><text class="label required">选择分类</text><picker value="{{categoryIndex}}" range="{{categories}}" range-key="name" data-event-opts="{{[['change',[['bindCategoryChange',['$event']]]]]}}" bindchange="__e"><view class="picker"><block wx:if="{{categoryIndex>-1}}"><text>{{categories[categoryIndex].name}}</text></block><block wx:else><text class="placeholder">请选择分类</text></block><text class="arrow">></text></view></picker></view><view class="form-item"><text class="label required">{{type===1?'采购标题':'供应标题'}}</text><input class="input" type="text" placeholder="{{type===1?'请输入您要采购的商品标题':'请输入您要供应的商品标题'}}" data-event-opts="{{[['input',[['__set_model',['$0','title','$event',[]],['formData']]]]]}}" value="{{formData.title}}" bindinput="__e"/></view><view class="form-item"><text class="label required">详细说明</text><textarea class="textarea" placeholder="{{type===1?'请详细描述您的采购需求，包括商品规格、质量要求等':'请详细描述您的供应商品，包括商品规格、质量等级等'}}" maxlength="500" data-event-opts="{{[['input',[['__set_model',['$0','content','$event',[]],['formData']]]]]}}" value="{{formData.content}}" bindinput="__e"></textarea><text class="word-count">{{$root.g0+"/500"}}</text></view><view class="form-item"><text class="label required">{{type===1?'采购数量':'供应库存'}}</text><input class="input" type="number" placeholder="{{type===1?'请输入采购数量':'请输入供应库存'}}" data-event-opts="{{[['input',[['__set_model',['$0','quantity','$event',[]],['formData']]]]]}}" value="{{formData.quantity}}" bindinput="__e"/></view><view class="form-item"><text class="label">{{type===1?'期望单价':'供应单价'}}</text><view class="price-input"><text class="price-symbol">¥</text><input class="input" type="digit" placeholder="{{type===1?'请输入期望单价(选填)':'请输入供应单价(选填)'}}" data-event-opts="{{[['input',[['__set_model',['$0','price','$event',[]],['formData']]]]]}}" value="{{formData.price}}" bindinput="__e"/><text class="price-unit">/件</text></view><block wx:if="{{type===1}}"><text class="price-tip">填写期望单价可以帮助供应商更好地了解您的预算</text></block></view><block wx:if="{{type===1}}"><view class="form-item"><text class="label">期望交货日期</text><picker mode="date" value="{{formData.delivery_date}}" start="{{minDate}}" data-event-opts="{{[['change',[['bindDateChange',['$event']]]]]}}" bindchange="__e"><view class="picker"><block wx:if="{{formData.delivery_date}}"><text>{{formData.delivery_date}}</text></block><block wx:else><text class="placeholder">请选择期望交货日期(选填)</text></block><text class="arrow">></text></view></picker></view></block><block wx:if="{{type===2}}"><view class="form-item"><text class="label required">发货地</text><input class="input" type="text" placeholder="请输入发货地" data-event-opts="{{[['input',[['__set_model',['$0','shipping_address','$event',[]],['formData']]]]]}}" value="{{formData.shipping_address}}" bindinput="__e"/></view></block><view class="form-item"><text class="label required">联系人</text><input class="input" type="text" placeholder="请输入联系人姓名" data-event-opts="{{[['input',[['__set_model',['$0','contact_name','$event',[]],['formData']]]]]}}" value="{{formData.contact_name}}" bindinput="__e"/></view><view class="form-item"><text class="label required">联系电话</text><input class="input" type="number" placeholder="请输入联系电话" data-event-opts="{{[['input',[['__set_model',['$0','contact_phone','$event',[]],['formData']]]]]}}" value="{{formData.contact_phone}}" bindinput="__e"/></view><view class="form-item"><text class="label">补充说明</text><textarea class="textarea" placeholder="{{type===1?'其他采购要求说明(选填)':'其他供应说明(选填)'}}" maxlength="200" data-event-opts="{{[['input',[['__set_model',['$0','remark','$event',[]],['formData']]]]]}}" value="{{formData.remark}}" bindinput="__e"></textarea><text class="word-count">{{$root.g1+"/200"}}</text></view></view><view data-event-opts="{{[['tap',[['submitForm',['$event']]]]]}}" class="submit-btn" bindtap="__e"><text class="btn-text">{{type===1?'发布采购':'发布供应'}}</text></view><popmsg class="vue-ref" vue-id="e0f569e6-2" data-ref="popmsg" bind:__l="__l"></popmsg></view>