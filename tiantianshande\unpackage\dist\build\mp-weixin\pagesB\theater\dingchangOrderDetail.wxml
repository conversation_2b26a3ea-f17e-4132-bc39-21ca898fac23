<view class="jc_center"><view class="content"><block wx:if="{{data&&data.status==3}}"><view class="state">已退款</view></block><block wx:else><block wx:if="{{data&&data.status==2}}"><view class="state">已完成</view></block><block wx:else><block wx:if="{{data&&data.status==1}}"><view class="state">已支付</view></block><block wx:else><block wx:if="{{data}}"><view class="state">待支付</view></block></block></block></block><block wx:if="{{data&&data.episode&&data.order_detail}}"><view class="message"><view class="message_txt1">{{data.episode.title||''}}</view><block wx:if="{{data.remarks==='锁座订单'}}"><view class="message_txt2" style="color:#ff4246;font-weight:bold;">🔒 锁座订单</view></block><view class="message_txt2">{{"预定日期: "+(data.order_detail.title||'')}}</view><view class="message_txt2"><block wx:for="{{data.order_detail.list}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view><text style="margin-right:10rpx;">{{item.y+"排"+item.x+"座  "+item.money+"元"}}</text></view></block></view><view class="message_txt2">{{"订单编号："+(data.ordernum||'')}}</view><view class="message_txt2 fl"><text>{{"实付金额："+(data.totalprice||0)+"元"}}</text><block wx:if="{{data.status==1}}"><view><block wx:if="{{$root.g0}}"><text class="mbn">退款审核中</text></block><block wx:else><block wx:if="{{$root.g1}}"><text class="mbn">退款未通过</text></block><block wx:else><block wx:if="{{data.remarks!=='锁座订单'}}"><text data-event-opts="{{[['tap',[['goList']]]]}}" class="mbt" bindtap="__e">退款</text></block></block></block></view></block></view></view></block><block wx:if="{{data&&data.status==1&&tel}}"><view class="message1"><view class="message_txt1">{{"手机号码："+tel}}</view><view class="message_txt2">凭改手机号后四位验证</view></view></block><block wx:if="{{data&&data.status==1&&data.hexiao_qr}}"><view class="hxqrbox"><image class="img" src="{{data.hexiao_qr}}" data-url="{{data.hexiao_qr}}" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image><view class="txt">请出示核销码给核销员进行核销</view></view></block></view><uni-popup class="vue-ref" vue-id="6806a76d-1" id="dialogShowCategory" type="bottom" mask-click="{{true}}" data-ref="dialogShowCategory" bind:__l="__l" vue-slots="{{['default']}}"><view class="view-main"><view class="view-title"><view style="display:flex;align-items:center;"><text style="margin-right:20px;">退款座位</text><view><checkbox-group data-event-opts="{{[['change',[['allChoose',['$event']]]]]}}" bindchange="__e"><checkbox class="{{[(allChecked)?'checked':'']}}" value="all" checked="{{allChecked?true:false}}"></checkbox><text>全选</text></checkbox-group></view></view><image class="icon" src="../../static/img/close.png" data-event-opts="{{[['tap',[['hideDialog']]]]}}" bindtap="__e"></image></view><scroll-view style="height:600rpx;" scroll-y="{{true}}"><view class="tl-section"><checkbox-group data-event-opts="{{[['change',[['changeCheckbox',['$event']]]]]}}" class="block" bindchange="__e"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="ind" wx:key="ind"><view class="tl-row"><view class="tl-center"><view>{{item.$orig.label}}</view></view><view><checkbox class="{{[(item.g2)?'checked':'']}}" value="{{item.m0}}" checked="{{item.g3}}"></checkbox></view></view></block></checkbox-group></view></scroll-view><view data-event-opts="{{[['tap',[['goRefund']]]]}}" class="view-btn" style="{{'background:'+($root.m1)+';'}}" bindtap="__e">确定退款</view></view></uni-popup></view>