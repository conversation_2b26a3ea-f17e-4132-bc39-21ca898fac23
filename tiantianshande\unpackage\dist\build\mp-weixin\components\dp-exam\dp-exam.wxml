<view class="dp-product" style="{{'background-color:'+(params.bgcolor)+';'+('margin:'+(params.margin_y*2.2+'rpx '+params.margin_x*2.2+'rpx 0')+';')+('padding:'+(params.padding_y*2.2+'rpx '+params.padding_x*2.2+'rpx')+';')+('width:'+('calc(100% - '+params.margin_x*2.2*2+'rpx)')+';')}}"><block wx:if="{{params.style=='1'||params.style=='2'||params.style=='3'}}"><dp-exam-item vue-id="6d952f80-1" showstyle="{{params.style}}" data="{{data}}" saleimg="{{params.saleimg}}" showname="{{params.showname}}" showprice="{{params.showprice}}" showsales="{{params.showsales}}" idfield="proid" menuindex="{{menuindex}}" bind:__l="__l"></dp-exam-item></block><block wx:if="{{params.style=='list'}}"><dp-exam-itemlist vue-id="6d952f80-2" data="{{data}}" saleimg="{{params.saleimg}}" showname="{{params.showname}}" showprice="{{params.showprice}}" showsales="{{params.showsales}}" idfield="proid" menuindex="{{menuindex}}" bind:__l="__l"></dp-exam-itemlist></block></view>