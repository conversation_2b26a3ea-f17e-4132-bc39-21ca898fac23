(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["yuyue/peolist"],{"02e5":function(t,i,e){"use strict";e.d(i,"b",(function(){return o})),e.d(i,"c",(function(){return a})),e.d(i,"a",(function(){return n}));var n={nodata:function(){return e.e("components/nodata/nodata").then(e.bind(null,"101c"))},nomore:function(){return e.e("components/nomore/nomore").then(e.bind(null,"3892"))},loading:function(){return e.e("components/loading/loading").then(e.bind(null,"ceaa"))},dpTabbar:function(){return e.e("components/dp-tabbar/dp-tabbar").then(e.bind(null,"b875"))},popmsg:function(){return e.e("components/popmsg/popmsg").then(e.bind(null,"2bf2"))}},o=function(){var t=this,i=t.$createElement,e=(t._self._c,t.isload&&t.showFilterOptions&&"juli"==t.field?t.t("color1"):null),n=t.isload&&t.showFilterOptions&&"juli"==t.field?t.t("color1"):null,o=t.isload&&t.showFilterOptions&&"comment_score"==t.field?t.t("color1"):null,a=t.isload&&t.showFilterOptions&&"comment_score"==t.field?t.t("color1"):null,d=t.isload&&t.showFilterOptions?t.t("color1"):null,l=t.isload?t.t("color1"):null,s=t.isload?t.__map(t.clist,(function(i,e){var n=t.__get_orig(i),o=t.t("color1");return{$orig:n,m6:o}})):null,r=t.isload&&t.locationFailed?t.t("color1"):null,c=t.isload&&t.locationFailed?t.t("color1"):null,u=t.isload?t.__map(t.datalist,(function(i,e){var n=t.__get_orig(i),o=i.jineng&&i.jineng.includes("、"),a=o?i.jineng.split("、").slice(0,3):null,d=t.t("color1");return{$orig:n,g0:o,l1:a,m9:d}})):null,h=t.isload?0===t.datalist.length&&!t.loading&&t.nodata:null,p=t.isload?t.nodata&&0===t.datalist.length&&!t.loading:null,f=t.loading&&0===t.datalist.length;t.$mp.data=Object.assign({},{$root:{m0:e,m1:n,m2:o,m3:a,m4:d,m5:l,l0:s,m7:r,m8:c,l2:u,g1:h,g2:p,g3:f}})},a=[]},"03a3":function(t,i,e){"use strict";e.r(i);var n=e("02e5"),o=e("e59c");for(var a in o)["default"].indexOf(a)<0&&function(t){e.d(i,t,(function(){return o[t]}))}(a);e("8d0a");var d=e("828b"),l=Object(d["a"])(o["default"],n["b"],n["c"],!1,null,null,null,!1,n["a"],void 0);i["default"]=l.exports},5291:function(t,i,e){},"6ab7":function(t,i,e){"use strict";(function(t){Object.defineProperty(i,"__esModule",{value:!0}),i.default=void 0;var e=getApp(),n={data:function(){return{opt:{},loading:!1,isload:!1,menuindex:-1,keyword:"",datalist:[],type:"",nodata:!1,curTopIndex:-1,index:0,curCid:0,nomore:!1,pagenum:1,longitude:"",latitude:"",field:"",order:"",locationFailed:!1,currentCity:"",currentCityId:0,provinceId:0,cityId:0,districtId:0,showFilterOptions:!1}},onLoad:function(i){this.opt=e.getopts(i),this.type=this.opt.type||"",this.provinceId=0,this.cityId=0,this.districtId=0,this.currentCity="",this.currentCityId=0,this.getdata(),t.$on("city",this.handleCitySelect),console.log("2023-06-15 11:05:23-INFO-[peolist][onLoad_001] 页面初始化完成")},onUnload:function(){t.$off("city",this.handleCitySelect)},onPullDownRefresh:function(){this.getdata()},onReachBottom:function(){this.nodata||this.nomore||(this.pagenum=this.pagenum+1,this.getdatalist(!0))},methods:{checkTapOutside:function(t){var i=this;setTimeout((function(){i.showFilterOptions&&(console.log("2023-06-15 15:55:23-INFO-[peolist][checkTapOutside_001] 检测到外部点击，关闭筛选面板"),i.showFilterOptions=!1)}),10)},toggleFilterOptions:function(t){console.log("2023-06-15 15:55:23-INFO-[peolist][toggleFilterOptions_001] 切换筛选面板状态:",!this.showFilterOptions),t&&t.stopPropagation(),this.showFilterOptions=!this.showFilterOptions},sortClick:function(t){t.stopPropagation();var i=t.currentTarget.dataset.field,e=t.currentTarget.dataset.order;this.field=i,this.order=e},resetFilter:function(t){t.stopPropagation(),this.field="",this.order=""},confirmFilter:function(t){t.stopPropagation(),this.showFilterOptions=!1,this.getdatalist()},handleCitySelect:function(t){if(console.log("2023-06-15 11:05:23-INFO-[peolist][handleCitySelect_001] 选择的城市：",t),t&&t.id){if(this.currentCityId=t.id,this.currentCity=t.name,void 0!==t.level)0===t.level?(this.provinceId=t.id,this.cityId=0,this.districtId=0):1===t.level?(this.cityId=t.id,this.provinceId=t.parent_id||0,this.districtId=0):2===t.level&&(this.districtId=t.id,this.cityId=t.parent_id||0,t.province_id?this.provinceId=t.province_id:this.provinceId=0);else if(t.parent_id)this.cityId=t.id,this.provinceId=t.parent_id,this.districtId=0;else if(t.first_letter){var i=t.name||"";i.indexOf("省")>-1||i.indexOf("自治区")>-1||i.indexOf("市")===i.length-1&&i.length>2?(this.provinceId=t.id,this.cityId=0,this.districtId=0):(this.cityId=t.id,this.provinceId=0,this.districtId=0)}console.log("2023-06-15 11:05:23-INFO-[peolist][handleCitySelect_002] 设置的地区ID：",{provinceId:this.provinceId,cityId:this.cityId,districtId:this.districtId}),this.getdatalist()}},gotoCity:function(){e.get("ApiArea/getActiveAreas",{with_children:!0},(function(i){if(1===i.status){var e=encodeURIComponent(JSON.stringify({areas:i.data,area_config:{hotareas:"",hotareas_str:"",hotarea:1,switcharearange:0,switcharearangeareas_str:""}}));t.navigateTo({url:"/pages/index/city?data="+e})}else t.showToast({title:"获取城市列表失败",icon:"none"})}))},getdata:function(){var i=this,n=i.opt.cid,o=i.opt.bid||0;n||(n=""),i.loading=!0,t.startPullDownRefresh&&t.startPullDownRefresh(),e.get("ApiYuyue/peocategory",{cid:n,bid:o},(function(t){i.loading=!1;var o=t.data;if(i.clist=o,n)for(var a=0;a<o.length;a++){if(o[a]["id"]==n){i.curTopIndex=a,i.curCid=n;break}for(var d=o[a]["child"],l=0,s=0;s<d;s++)if(d[s]["id"]==n){i.curIndex=a,i.curIndex2=s,i.curCid=n,l=1;break}if(l)break}i.loaded(),e.getLocation((function(t){console.log("位置获取成功：",t);var e=t.latitude,n=t.longitude;i.longitude=n,i.latitude=e,i.getdatalist()}),(function(t){console.log("位置获取失败：",t),i.locationFailed=!0,i.getdatalist()}))}))},getdatalist:function(i){i||(this.pagenum=1,this.datalist=[]);var n=this,o=n.pagenum,a=n.curCid,d=n.opt.bid?n.opt.bid:"",l=n.order,s=n.keyword,r=n.field;n.loading=!0,n.nodata=!1,n.nomore=!1;var c=n.latitude,u=n.longitude;console.log("2023-06-15 11:05:23-INFO-[peolist][getdatalist_001] 发送请求，地区ID：",{provinceId:n.provinceId,cityId:n.cityId,districtId:n.districtId});var h={pagenum:o,keyword:s,field:r,order:l,cid:a,bid:d,type:"list",province_id:parseInt(n.provinceId||0),city_id:parseInt(n.cityId||0),district_id:parseInt(n.districtId||0)};u&&c&&!n.locationFailed&&(h.longitude=u,h.latitude=c,console.log("发送位置信息到API：",u,c)),e.post("ApiYuyue/selectpeople",h,(function(i){n.loading=!1;var e=i.data;if(1==o)n.datalist=e,0==e.length&&(n.nodata=!0);else if(0==e.length)n.nomore=!0;else{var a=n.datalist,d=a.concat(e);n.datalist=d}if(e.length>0&&e[0].distance&&console.log("技师列表已按距离排序，最近的技师距离："+e[0].distance),e.length>0){for(var l=0;l<e.length;l++)e[l].distance&&"未知"!==e[l].distance||(e[l].distance="");"juli"===n.field&&n.locationFailed&&(t.showToast({title:"位置获取失败，无法按距离排序",icon:"none",duration:2e3}),n.field="")}t.stopPullDownRefresh()}))},switchTopTab:function(t){var i=t.currentTarget.dataset.id,e=parseInt(t.currentTarget.dataset.index);this.curTopIndex=e,this.curIndex=-1,this.curIndex2=-1,this.prolist=[],this.nopro=0,this.curCid=i,this.getdatalist()},searchChange:function(t){this.keyword=t.detail.value},searchConfirm:function(t){var i=t.detail.value;this.keyword=i,this.getdata()},goto:function(i){var e=i.currentTarget.dataset.url;t.navigateTo({url:e})},loaded:function(){this.isload=!0},getmenuindex:function(t){this.menuindex=t},retryLocation:function(){var i=this;i.locationFailed=!1,e.getLocation((function(e){console.log("位置获取成功：",e);var n=e.latitude,o=e.longitude;i.longitude=o,i.latitude=n,i.getdatalist(),t.showToast({title:"位置获取成功",icon:"success",duration:2e3})}),(function(e){console.log("位置获取失败：",e),i.locationFailed=!0,i.getdatalist(),t.showToast({title:"位置获取失败，请检查位置权限",icon:"none",duration:2e3})}))}}};i.default=n}).call(this,e("df3c")["default"])},"8d0a":function(t,i,e){"use strict";var n=e("5291"),o=e.n(n);o.a},dd2a:function(t,i,e){"use strict";(function(t,i){var n=e("47a9");e("06e9");n(e("3240"));var o=n(e("03a3"));t.__webpack_require_UNI_MP_PLUGIN__=e,i(o.default)}).call(this,e("3223")["default"],e("df3c")["createPage"])},e59c:function(t,i,e){"use strict";e.r(i);var n=e("6ab7"),o=e.n(n);for(var a in n)["default"].indexOf(a)<0&&function(t){e.d(i,t,(function(){return n[t]}))}(a);i["default"]=o.a}},[["dd2a","common/runtime","common/vendor"]]]);