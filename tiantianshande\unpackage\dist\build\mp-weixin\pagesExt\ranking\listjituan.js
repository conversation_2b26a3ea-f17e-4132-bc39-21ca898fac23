require('../common/vendor.js');(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pagesExt/ranking/listjituan"],{"0c1a":function(t,n,i){"use strict";(function(t,n){var e=i("47a9");i("06e9");e(i("3240"));var a=e(i("ba81"));t.__webpack_require_UNI_MP_PLUGIN__=i,n(a.default)}).call(this,i("3223")["default"],i("df3c")["createPage"])},"4eef":function(t,n,i){"use strict";var e=i("b479"),a=i.n(e);a.a},7281:function(t,n,i){"use strict";(function(t){Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;var i=getApp(),e={data:function(){return{isload:!1,loading:!0,nomore:!1,rankType:1,page:1,limit:10,rankList:[],monthList:[],monthIndex:0,currentMonth:"",settings:null,pre_url:"",medals:{0:"/static/img/gold-medal.png",1:"/static/img/silver-medal.png",2:"/static/img/bronze-medal.png"}}},computed:{getMonthName:function(){return this.monthList.length>0&&this.monthList[this.monthIndex]?this.monthList[this.monthIndex].name:"选择月份"}},onLoad:function(){this.pre_url=i.globalData.pre_url,this.getMonthList(),this.getRankingSetting()},onPullDownRefresh:function(){this.page=1,this.rankList=[],this.getRankingList()},onReachBottom:function(){this.nomore||this.loading||(this.page++,this.getRankingList())},methods:{getRankingSetting:function(){var t=this;i.get("ApiJietikaohe/getRankingSetting",{},(function(n){1==n.code&&(t.settings=n.data,t.isload=!0)}))},getMonthList:function(){var t=this;i.get("ApiJietikaohe/getMonthList",{},(function(n){1==n.code&&n.data&&n.data.length>0&&(t.monthList=n.data,t.monthIndex=0,t.currentMonth=n.data[0].value,t.getRankingList())}))},getMedalImage:function(t){return this.pre_url+this.medals[t]||""},getRankingList:function(){var n=this;if(n.currentMonth){n.loading=!0;var e={type:n.rankType,page:n.page,limit:n.limit,month:n.currentMonth};i.get("ApiJietikaohe/getAllRankingList",e,(function(i){n.loading=!1,t.stopPullDownRefresh(),1==i.code?(1==n.page?n.rankList=i.data.list||[]:n.rankList=n.rankList.concat(i.data.list||[]),n.nomore=n.rankList.length>=(i.data.total||0)):(n.rankList=[],n.nomore=!0)}))}},changeRankType:function(t){this.rankType!==t&&(this.rankType=t,this.page=1,this.rankList=[],this.getRankingList())},onMonthChange:function(t){this.monthIndex=t.detail.value,this.currentMonth=this.monthList[this.monthIndex].value,this.page=1,this.rankList=[],this.getRankingList()},goToProduct:function(n){n&&t.navigateTo({url:"/pages/product/detail?id="+n})}}};n.default=e}).call(this,i("df3c")["default"])},b479:function(t,n,i){},b8db:function(t,n,i){"use strict";i.r(n);var e=i("7281"),a=i.n(e);for(var o in e)["default"].indexOf(o)<0&&function(t){i.d(n,t,(function(){return e[t]}))}(o);n["default"]=a.a},ba81:function(t,n,i){"use strict";i.r(n);var e=i("dbc6"),a=i("b8db");for(var o in a)["default"].indexOf(o)<0&&function(t){i.d(n,t,(function(){return a[t]}))}(o);i("4eef");var r=i("828b"),s=Object(r["a"])(a["default"],e["b"],e["c"],!1,null,null,null,!1,e["a"],void 0);n["default"]=s.exports},dbc6:function(t,n,i){"use strict";i.d(n,"b",(function(){return a})),i.d(n,"c",(function(){return o})),i.d(n,"a",(function(){return e}));var e={loading:function(){return i.e("components/loading/loading").then(i.bind(null,"ceaa"))}},a=function(){var t=this,n=t.$createElement,i=(t._self._c,t.isload?t.__map(t.rankList,(function(n,i){var e=t.__get_orig(n),a=1===t.rankType&&i<3?t.getMedalImage(i):null,o=2===t.rankType&&i<3?t.getMedalImage(i):null;return{$orig:e,m0:a,m1:o}})):null);t.$mp.data=Object.assign({},{$root:{l0:i}})},o=[]}},[["0c1a","common/runtime","common/vendor"]]]);