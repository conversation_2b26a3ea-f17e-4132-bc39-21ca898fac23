(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["activity/kecheng/learninglog"],{"636c":function(t,e,a){"use strict";(function(t){Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var a=getApp(),n={data:function(){return{opt:{},loading:!1,isload:!1,menuindex:-1,nomore:!1,nodata:!1,pagenum:1,pernum:10,datalist:[],userInfo:{},pre_url:a.globalData.pre_url,filterType:"all",counts:{all:0,purchased:0,unpurchased:0}}},onLoad:function(t){this.opt=a.getopts(t),this.getTabbarMenu(),this.getLearningProgress(!0),this.getUserInfo()},onPullDownRefresh:function(){this.getLearningProgress(!0)},onReachBottom:function(){this.nodata||this.nomore||(this.pagenum=this.pagenum+1,this.getLearningProgress(!1))},computed:{filteredDatalist:function(){return"all"===this.filterType?this.datalist:"purchased"===this.filterType?this.datalist.filter((function(t){return 1===t.is_purchased})):"unpurchased"===this.filterType?this.datalist.filter((function(t){return 0===t.is_purchased})):this.datalist},showNoData:function(){return this.isload&&(!this.filteredDatalist||0===this.filteredDatalist.length)}},methods:{getTabbarMenu:function(){var e="/"+this.__route__,n=a.globalData.tarbar;if(n&&n.list)for(var r=0;r<n.list.length;r++)n.list[r].pagePath==e&&(this.menuindex=r);this.opt&&1==this.opt.hidettabbar&&t.hideTabBar()},getLearningProgress:function(e){var n=this;e&&(n.pagenum=1,n.datalist=[]),n.loading=!0,n.nodata=!1,n.nomore=!1,a.post("ApiKecheng/getAllLearningProgress",{pagenum:n.pagenum,pernum:n.pernum},(function(e){if(n.loading=!1,n.isload=!0,t.stopPullDownRefresh(),1==e.status){for(var r=e.data,i=0;i<r.length;i++)r[i].showChapter=!1,0===r[i].is_purchased&&(r[i].needPurchase=!0);r.filter((function(t){t.deg=parseInt(t.overall_progress_percent/100*360),t.degc=360-t.deg})),1==n.pagenum?(n.datalist=r,0==r.length&&(n.nodata=!0),n.calculateCounts()):(n.datalist=n.datalist.concat(r),n.calculateCounts()),r.length<n.pernum&&(n.nomore=!0)}else-1==e.status?a.showModal(e.msg,(function(){a.goto("/pages/login/login")})):a.alert(e.msg)}))},gotoCourseOrDetail:function(t){t.target_chapter_id?a.goto("/activity/kecheng/mldetail?kcid="+t.kcid+"&id="+t.target_chapter_id):(t.is_purchased,a.goto("/activity/kecheng/detail?id="+t.kcid))},formatDuration:function(t){if(!t||t<=0)return"00:00";t=Math.floor(t);var e=Math.floor(t/60),a=t%60,n=String(e).padStart(2,"0"),r=String(a).padStart(2,"0");return"".concat(n,":").concat(r)},getmenuindex:function(t){this.menuindex=t},toggleChapter:function(t){this.datalist[t].showChapter=!this.datalist[t].showChapter},navTo:function(t){a.goto(t)},goto:function(t){var e=t.currentTarget.dataset.url;e&&a.goto("/activity/kecheng/"+e)},getUserInfo:function(){var t=this;a.get("ApiMy/getCurrentUserInfo",{},(function(e){1==e.status&&(t.userInfo=e.data)}))},getProgressBgStyle:function(t){var e=(120*(1-t/100)).toString(10);return"hsl(".concat(e,", 100%, 50%)")},changeFilter:function(t){this.filterType=t,this.getLearningProgress(!0)},calculateCounts:function(){this.counts.all=this.datalist.length,this.counts.purchased=this.datalist.filter((function(t){return 1===t.is_purchased})).length,this.counts.unpurchased=this.datalist.filter((function(t){return 0===t.is_purchased})).length},getNoDataText:function(){return"all"===this.filterType?"暂无学习记录":"purchased"===this.filterType?"暂无已购买的学习记录":"unpurchased"===this.filterType?"暂无未购买的学习记录":"暂无学习记录"}}};e.default=n}).call(this,a("df3c")["default"])},"7b2d":function(t,e,a){"use strict";a.r(e);var n=a("636c"),r=a.n(n);for(var i in n)["default"].indexOf(i)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(i);e["default"]=r.a},"8eb1":function(t,e,a){"use strict";(function(t,e){var n=a("47a9");a("06e9");n(a("3240"));var r=n(a("f70c"));t.__webpack_require_UNI_MP_PLUGIN__=a,e(r.default)}).call(this,a("3223")["default"],a("df3c")["createPage"])},"8f65":function(t,e,a){},"919a8":function(t,e,a){"use strict";a.d(e,"b",(function(){return r})),a.d(e,"c",(function(){return i})),a.d(e,"a",(function(){return n}));var n={nodata:function(){return a.e("components/nodata/nodata").then(a.bind(null,"101c"))},loading:function(){return a.e("components/loading/loading").then(a.bind(null,"ceaa"))},dpTabbar:function(){return a.e("components/dp-tabbar/dp-tabbar").then(a.bind(null,"b875"))},popmsg:function(){return a.e("components/popmsg/popmsg").then(a.bind(null,"2bf2"))}},r=function(){var t=this,e=t.$createElement,a=(t._self._c,t.isload?t.filteredDatalist&&t.filteredDatalist.length>0:null),n=t.isload&&a?t.__map(t.filteredDatalist,(function(e,a){var n=t.__get_orig(e),r=e.showChapter&&e.target_chapter_id&&e.target_chapter_duration&&(2===e.target_chapter_type||3===e.target_chapter_type)?t.formatDuration(e.target_chapter_duration):null,i=e.showChapter&&e.target_chapter_id&&1!==e.target_chapter_log_status&&e.target_chapter_current_time>0&&(2===e.target_chapter_type||3===e.target_chapter_type)?t.formatDuration(e.target_chapter_current_time):null;return{$orig:n,m0:r,m1:i}})):null,r=t.isload&&t.showNoData?t.getNoDataText():null;t.$mp.data=Object.assign({},{$root:{g0:a,l0:n,m2:r}})},i=[]},"9efd":function(t,e,a){"use strict";var n=a("8f65"),r=a.n(n);r.a},f70c:function(t,e,a){"use strict";a.r(e);var n=a("919a8"),r=a("7b2d");for(var i in r)["default"].indexOf(i)<0&&function(t){a.d(e,t,(function(){return r[t]}))}(i);a("9efd");var o=a("828b"),s=Object(o["a"])(r["default"],n["b"],n["c"],!1,null,null,null,!1,n["a"],void 0);e["default"]=s.exports}},[["8eb1","common/runtime","common/vendor"]]]);