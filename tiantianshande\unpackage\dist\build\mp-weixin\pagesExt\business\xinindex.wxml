<view><block wx:if="{{isdiy}}"><block><view style="{{('display:flex;min-height: 100vh;flex-direction: column;background-color:'+pageinfo.bgcolor)}}"><view class="container"><dp vue-id="b4e131f4-1" pagecontent="{{pagecontent}}" menuindex="{{menuindex}}" bind:__l="__l"></dp></view></view><dp-guanggao vue-id="b4e131f4-2" guanggaopic="{{guanggaopic}}" guanggaourl="{{guanggaourl}}" bind:__l="__l"></dp-guanggao></block></block><block wx:else><block><block wx:if="{{isload}}"><view class="container nodiydata"><block wx:if="{{$root.g0>0}}"><swiper class="swiper" indicator-dots="{{pics[1]?true:false}}" autoplay="{{true}}" interval="{{5000}}" indicator-color="#dcdcdc" indicator-active-color="#fff"><block wx:for="{{pics}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><swiper-item class="swiper-item"><image class="image" src="{{item}}" mode="widthFix"></image></swiper-item></block></block></swiper></block><view class="topcontent"><view class="logo"><image class="img" src="{{business.logo}}"></image></view><view class="title">{{business.name}}</view><view class="desc"><view class="f1"><block wx:for="{{[0,1,2,3,4]}}" wx:for-item="item2" wx:for-index="index2" wx:key="index2"><image class="img" src="{{'/static/img/star'+(business.comment_score>item2?'2':'')+'.png'}}"></image></block><text class="txt">{{business.comment_score}}</text></view><view class="f2">{{"销量 "+business.sales}}</view></view><block wx:if="{{bset&&bset.show_link}}"><view class="tel" style="{{'background:'+('linear-gradient(90deg,'+$root.m0+' 0%, rgba('+$root.m1+',0.8) 100%)')+';'}}"><view class="tel_online" data-phone="{{business.tel}}" data-event-opts="{{[['tap',[['phone',['$event']]]]]}}" bindtap="__e"><image class="img" src="/static/img/tel.png"></image>{{''+(bset&&bset.show_linktext?bset.show_linktext:'联系商家')+''}}</view></view></block><view class="address" data-latitude="{{business.latitude}}" data-longitude="{{business.longitude}}" data-company="{{business.name}}" data-address="{{business.address}}" data-event-opts="{{[['tap',[['openLocation',['$event']]]]]}}" bindtap="__e"><image class="f1" src="/static/img/shop_addr.png"></image><view class="f2">{{business.address}}</view><image class="f3" src="/static/img/arrowright.png"></image></view></view><block wx:if="{{pay_switch==1}}"><view class="container2"><view class="card"><view class="content"><text class="discount-text">付款买单</text><text class="sub-text">共享补贴</text></view><button class="pay-button" style="{{'background:'+('linear-gradient(90deg,'+$root.m2+' 0%, rgba('+$root.m3+',0.8) 100%)')+';'}}" data-url="{{'/pagesExt/maidan/pay?bid='+business.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">点击买单</button></view></view></block><view class="contentbox"><view class="shop_tab"><block wx:if="{{showfw}}"><view class="{{['cptab_text '+(st==-1?'cptab_current':'')]}}" data-st="-1" data-event-opts="{{[['tap',[['changetab',['$event']]]]]}}" bindtap="__e">本店服务<view class="after" style="{{'background:'+($root.m4)+';'}}"></view></view></block><block wx:if="{{bset&&bset.show_product}}"><view class="{{['cptab_text '+(st==0?'cptab_current':'')]}}" data-st="0" data-event-opts="{{[['tap',[['changetab',['$event']]]]]}}" bindtap="__e">{{bset&&bset.show_producttext?bset.show_producttext:'本店商品'}}<view class="after" style="{{'background:'+($root.m5)+';'}}"></view></view></block><block wx:if="{{bset&&bset.show_comment}}"><view class="{{['cptab_text '+(st==1?'cptab_current':'')]}}" data-st="1" data-event-opts="{{[['tap',[['changetab',['$event']]]]]}}" bindtap="__e">{{(bset&&bset.show_commenttext?bset.show_commenttext:'店铺评价')+"("+countcomment+")"}}<view class="after" style="{{'background:'+($root.m6)+';'}}"></view></view></block><block wx:if="{{bset&&bset.show_detail}}"><view class="{{['cptab_text '+(st==2?'cptab_current':'')]}}" data-st="2" data-event-opts="{{[['tap',[['changetab',['$event']]]]]}}" bindtap="__e">{{bset&&bset.show_detailtext?bset.show_detailtext:'商家详情'}}<view class="after" style="{{'background:'+($root.m7)+';'}}"></view></view></block></view><block wx:if="{{st==-1}}"><view class="cp_detail" style="padding-top:20rpx;"><block wx:if="{{$root.g1>0}}"><view class="classify-ul"><view class="flex" style="width:100%;overflow-y:hidden;overflow-x:scroll;"><view class="classify-li" style="{{(yuyue_cid==0?'color:'+$root.m8+';background:rgba('+$root.m9+',0.2)':'')}}" data-id="{{0}}" data-event-opts="{{[['tap',[['changeyuyueCTab',['$event']]]]]}}" bindtap="__e">全部</view><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="idx2" wx:key="idx2"><block><view class="classify-li" style="{{(yuyue_cid==item.$orig.id?'color:'+item.m10+';background:rgba('+item.m11+',0.2)':'')}}" data-id="{{item.$orig.id}}" data-event-opts="{{[['tap',[['changeyuyueCTab',['$event']]]]]}}" bindtap="__e">{{item.$orig.name}}</view></block></block></view></view></block><dp-yuyue-itemlist vue-id="b4e131f4-3" data="{{datalist}}" menuindex="{{menuindex}}" bind:__l="__l"></dp-yuyue-itemlist><block wx:if="{{nomore}}"><nomore vue-id="b4e131f4-4" bind:__l="__l"></nomore></block><block wx:if="{{nodata}}"><nodata vue-id="b4e131f4-5" bind:__l="__l"></nodata></block></view></block><block wx:if="{{st==0}}"><view class="cp_detail" style="padding-top:20rpx;"><dp-product-itemlist vue-id="b4e131f4-6" data="{{datalist}}" menuindex="{{menuindex}}" bind:__l="__l"></dp-product-itemlist><block wx:if="{{nomore}}"><nomore vue-id="b4e131f4-7" bind:__l="__l"></nomore></block><block wx:if="{{nodata}}"><nodata vue-id="b4e131f4-8" bind:__l="__l"></nodata></block></view></block><block wx:if="{{st==1}}"><view class="cp_detail"><view class="comment"><block wx:if="{{$root.g2>0}}"><block><block wx:for="{{datalist}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="item"><view class="f1"><image class="t1" src="{{item.headimg}}"></image><view class="t2">{{item.nickname}}</view><view class="flex1"></view><view class="t3"><block wx:for="{{[0,1,2,3,4]}}" wx:for-item="item2" wx:for-index="index2" wx:key="index2"><image class="img" src="{{'/static/img/star'+(item.score>item2?'2':'')+'.png'}}"></image></block></view></view><view style="color:#777;font-size:22rpx;">{{item.createtime}}</view><view class="f2"><text class="t1">{{item.content}}</text><view class="t2"><block wx:if="{{item.content_pic!=''}}"><block><block wx:for="{{item.content_pic}}" wx:for-item="itemp" wx:for-index="index" wx:key="index"><block><view data-url="{{itemp}}" data-urls="{{item.content_pic}}" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"><image src="{{itemp}}" mode="widthFix"></image></view></block></block></block></block></view></view><block wx:if="{{item.reply_content}}"><view class="f3"><view class="arrow"></view><view class="t1">{{"商家回复："+item.reply_content}}</view></view></block></view></block></block></block><block wx:else><block><nodata data-custom-hidden="{{!(nodata)}}" vue-id="b4e131f4-9" bind:__l="__l"></nodata></block></block></view></view></block><block wx:if="{{st==2}}"><view class="cp_detail" style="padding:20rpx;"><parse vue-id="b4e131f4-10" content="{{business.content}}" bind:__l="__l"></parse></view></block></view><block wx:if="{{couponcount>0}}"><view class="covermy" style="top:65vh;" data-url="{{'/pages/coupon/couponlist?bid='+business.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><text style="padding:0 4rpx;height:36rpx;line-height:36rpx;">商家</text><text style="padding:0 4rpx;height:36rpx;line-height:36rpx;">{{$root.m12}}</text></view></block><view class="covermy" style="top:65vh;" data-url="{{'/pages/coupon/couponlist?bid='+business.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><text style="padding:0 4rpx;height:36rpx;line-height:36rpx;">商家</text><text style="padding:0 4rpx;height:36rpx;line-height:36rpx;">{{$root.m13}}</text></view><view class="covermy" style="top:65vh;" data-url="{{'/pages/coupon/couponlist?bid='+business.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><text style="padding:0 4rpx;height:36rpx;line-height:36rpx;">商家</text><text style="padding:0 4rpx;height:36rpx;line-height:36rpx;">{{$root.m14}}</text></view><view data-event-opts="{{[['tap',[['goBack',['$event']]]]]}}" class="covermy" style="top:75vh;background:rgba(0,0,0,0.7);" bindtap="__e"><text style="padding:0 4rpx;height:36rpx;line-height:36rpx;">返回</text><text style="padding:0 4rpx;height:36rpx;line-height:36rpx;">上一页</text></view></view></block></block></block><block wx:if="{{loading}}"><loading vue-id="b4e131f4-11" bind:__l="__l"></loading></block><dp-tabbar vue-id="b4e131f4-12" opt="{{opt}}" data-event-opts="{{[['^getmenuindex',[['getmenuindex']]]]}}" bind:getmenuindex="__e" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="b4e131f4-13" data-ref="popmsg" bind:__l="__l"></popmsg></view>