<view class="container"><block wx:if="{{isload}}"><block><dd-tab vue-id="3b03450a-1" itemdata="{{[$root.m0,$root.m1]}}" itemst="{{['1','2']}}" st="{{st}}" isfixed="{{false}}" data-event-opts="{{[['^changetab',[['changetab']]]]}}" bind:changetab="__e" bind:__l="__l"></dd-tab><block wx:if="{{$root.g0}}"><view class="content"><view class="label"><text class="t1">成员信息</text></view><block wx:for="{{datalist}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><view class="item"><view class="f1"><image src="{{item.headimg}}"></image><view class="t2"><text class="x1">{{"用户昵称:"+item.nickname}}</text><text class="x2">{{"创建时间:"+item.createtime1}}</text><text class="x2">{{"等级："+item.levelname}}</text><block wx:if="{{st==1}}"><text class="x2">{{"脱离时间："+item.createtime}}</text></block><block wx:if="{{st==2}}"><text class="x2">{{"回归时间："+item.createtime}}</text></block><block wx:if="{{item.tel}}"><text class="x2">{{"手机号："+item.tel}}</text></block><block wx:if="{{item.newnickname&&st==1}}"><text class="x1">{{"脱离之后:"+item.newnickname}}</text></block><block wx:if="{{!item.newnickname&&st==1}}"><text class="x1">脱离之后为空</text></block></view></view><view class="f2"></view></view></block></block></view></block></block></block><block wx:if="{{nodata}}"><nodata vue-id="3b03450a-2" bind:__l="__l"></nodata></block><block wx:if="{{nomore}}"><nomore vue-id="3b03450a-3" bind:__l="__l"></nomore></block><block wx:if="{{loading}}"><loading vue-id="3b03450a-4" bind:__l="__l"></loading></block><dp-tabbar vue-id="3b03450a-5" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="3b03450a-6" data-ref="popmsg" bind:__l="__l"></popmsg></view>