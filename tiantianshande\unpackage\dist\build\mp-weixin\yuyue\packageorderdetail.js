(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["yuyue/packageorderdetail"],{"0c17":function(e,t,r){},4591:function(e,t,r){"use strict";(function(e){var i=r("47a9");Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var a=i(r("7ca3"));function o(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);t&&(i=i.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,i)}return r}function n(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?o(Object(r),!0).forEach((function(t){(0,a.default)(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):o(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}var c={data:function(){return{themeColor:"#2979ff",themeColorRgb:"41,121,255",orderId:null,orderDetail:{},loading:!0,refunding:!1,refundReason:"",refundAmount:0}},onLoad:function(t){var r=getApp();if(r&&r.globalData&&r.globalData.config&&r.globalData.config.t?(this.themeColor=r.globalData.config.t("color1"),this.themeColorRgb=r.globalData.config.t("color1rgb"),console.log("2025-01-03 22:55:53,565-INFO-[packageorderdetail][onLoad_001] 主题色初始化成功")):console.warn("2025-01-03 22:55:53,565-INFO-[packageorderdetail][onLoad_002] 无法获取主题色配置，使用默认值"),t&&t.order_id)this.orderId=t.order_id,this.getOrderDetail();else{var i=getApp();i&&i.error&&i.goback?i.error("缺少订单ID",(function(){i.goback()})):(console.error("2025-01-03 22:55:53,565-ERROR-[packageorderdetail][onLoad_003] app对象未初始化"),e.showToast({title:"缺少订单ID",icon:"none"}),setTimeout((function(){e.navigateBack()}),1500)),this.loading=!1}e.setNavigationBarTitle({title:"订单详情"})},methods:{getOrderDetail:function(){var t=this;t.loading=!0;var r=getApp();if(!r||!r.post)return console.error("2025-01-03 22:55:53,565-ERROR-[packageorderdetail][getOrderDetail_001] app对象未初始化或post方法不存在"),t.loading=!1,void e.showToast({title:"获取订单详情失败",icon:"none"});r.post("ApiYuyuePackage/getPackageOrderDetail",{order_id:t.orderId},(function(e){if(t.loading=!1,1==e.status&&e.data){console.log("2025-01-03 22:55:53,565-INFO-[packageorderdetail][getOrderDetail_002] 获取订单数据成功",e.data);var i=e.data;if(1===i.status){var a=Math.floor(Date.now()/1e3),o=0===i.expires_time||i.expires_time<a&&i.expires_time<1672531200;if(o&&i.pay_time){console.log("2025-01-03 22:55:53,565-INFO-[packageorderdetail][getOrderDetail_003] 检测到无效的过期时间:",i.expires_time);var c=new Date(i.pay_time.replace(/-/g,"/")).getTime(),s=i.valid_days||365;i.expires_time=Math.floor(c/1e3)+86400*s,console.log("2025-01-03 22:55:53,565-INFO-[packageorderdetail][getOrderDetail_004] 自动计算新的过期时间:",{payTime:i.pay_time,payTimeStamp:Math.floor(c/1e3),validDays:s,newExpiresTime:i.expires_time,currentTime:a})}if(i.expires_time>0){var d=new Date(1e3*i.expires_time);i.expires_time_format=d.getFullYear()+"-"+String(d.getMonth()+1).padStart(2,"0")+"-"+String(d.getDate()).padStart(2,"0")+" "+String(d.getHours()).padStart(2,"0")+":"+String(d.getMinutes()).padStart(2,"0")+":"+String(d.getSeconds()).padStart(2,"0")}i.expires_time>0&&a>i.expires_time?i.status_text="已过期":i.remain_services<=0?i.status_text="已用完":i.status_text="可使用",console.log("2025-01-03 22:55:53,565-INFO-[packageorderdetail][getOrderDetail_005] 订单状态判断:",{expiresTime:i.expires_time,currentTime:a,remainServices:i.remain_services,statusText:i.status_text})}i.package_pic&&0===i.package_pic.indexOf("https://localhost")&&(i.package_pic=i.package_pic.replace("https://localhost","")),i.package_pic&&0===i.package_pic.indexOf("https://localhosthttps://")&&(i.package_pic=i.package_pic.replace("https://localhost","")),i.items&&i.items.length>0&&(i.items=i.items.map((function(e){return n(n({},e),{},{service_id:e.product_id,service_name:e.product_name||"未命名服务",service_pic:t.fixImageUrl(e.product_pic),buy_times:e.total_num||0,remain_times:e.remain_num||0})}))),console.log("2025-01-03 22:55:53,565-INFO-[packageorderdetail][getOrderDetail_006] 数据处理完成",i),t.orderDetail=i}else r.error(e.msg||"获取订单详情失败",(function(){r.goback()}))}),(function(){t.loading=!1,r.error("请求失败",(function(){r.goback()}))}))},fixImageUrl:function(e){return e?(0===e.indexOf("https://localhosthttps://")&&(e=e.replace("https://localhost","")),0===e.indexOf("https://localhost")&&(e=e.replace("https://localhost","")),e):"/static/img/goods-default.png"},canUseItem:function(e){var t="已过期"!==this.orderDetail.status_text;return 1===this.orderDetail.status&&e.remain_times>0&&t},gotoUseService:function(e){var t=this.orderDetail.items.find((function(t){return t.service_id===e}));if(!t||t.remain_times<=0){var r=getApp();r.error("该服务已无剩余次数")}else if("已过期"!==this.orderDetail.status_text){var i=getApp();i.goto("/yuyue/packageappoint?package_order_id="+this.orderId+"&product_id="+e)}else{var a=getApp();a.error("套餐已过期，无法使用")}},copyText:function(e){var t=getApp();t.copy(e)},gotoPay:function(e,t){var r=this,i=getApp();e?i.payorder({payorderid:e,orderid:t,type:"yuyue_package",success:function(){i.success("支付成功"),r.getOrderDetail()},fail:function(){i.error("支付失败或取消")}}):i.error("支付单号不存在")},cancelOrder:function(e){var t=this,r=getApp();r.confirm("确定要取消该订单吗？",(function(){r.showLoading("处理中..."),r.post("ApiYuyuePackage/cancelOrder",{order_id:e},(function(e){r.showLoading(!1),1==e.status?r.success("取消成功",(function(){t.getOrderDetail()})):r.error(e.msg||"取消失败")}))}))},deleteOrder:function(e){var t=getApp();t.confirm("确定要删除该订单吗？删除后不可恢复。",(function(){t.showLoading("处理中..."),t.post("ApiYuyuePackage/deleteOrder",{order_id:e},(function(e){t.showLoading(!1),1==e.status?t.success("删除成功",(function(){t.goback()})):t.error(e.msg||"删除失败")}))}))},canRefundDetail:function(e,t){return 1===e&&(!t||0===t)&&"已过期"!==this.orderDetail.status_text&&"已用完"!==this.orderDetail.status_text},canDelete:function(e){return 2===e||5===e||"已过期"===this.orderDetail.status_text},applyRefund:function(e,t){if("已过期"!==this.orderDetail.status_text)this.refundAmount=t,this.$refs.refundPopup.open();else{var r=getApp();r.error("套餐已过期，无法申请退款")}},closeRefundPopup:function(){this.$refs.refundPopup.close()},confirmRefund:function(e,t){this.refundReason=t||"用户申请退款",e();var r=this;if(!r.refunding){r.refunding=!0;var i=getApp();i.showLoading("提交中..."),i.post("ApiYuyuePackage/refund",{orderid:r.orderId,money:r.refundAmount,reason:r.refundReason},(function(e){i.showLoading(!1),r.refunding=!1,1==e.status?i.success("退款申请已提交",(function(){r.getOrderDetail()})):i.error(e.msg||"提交失败")}),(function(){i.showLoading(!1),r.refunding=!1,i.error("请求失败")}))}}}};t.default=c}).call(this,r("df3c")["default"])},"52ed":function(e,t,r){"use strict";r.d(t,"b",(function(){return a})),r.d(t,"c",(function(){return o})),r.d(t,"a",(function(){return i}));var i={uniPopup:function(){return Promise.all([r.e("common/vendor"),r.e("components/uni-popup/uni-popup")]).then(r.bind(null,"ca44a"))},uniPopupDialog:function(){return r.e("components/uni-popup-dialog/uni-popup-dialog").then(r.bind(null,"267c"))}},a=function(){var e=this,t=e.$createElement,r=(e._self._c,!e.loading&&e.orderDetail.id?e.orderDetail.items&&e.orderDetail.items.length>0:null),i=!e.loading&&e.orderDetail.id&&r?e.orderDetail.items.length:null,a=!e.loading&&e.orderDetail.id&&r?e.__map(e.orderDetail.items,(function(t,r){var i=e.__get_orig(t),a=e.fixImageUrl(t.service_pic||t.product_pic),o=e.canUseItem(t);return{$orig:i,m0:a,m1:o}})):null,o=!e.loading&&e.orderDetail.id?e.orderDetail.use_records&&e.orderDetail.use_records.length>0:null,n=!e.loading&&e.orderDetail.id?e.canRefundDetail(e.orderDetail.status,e.orderDetail.refund_status):null,c=!e.loading&&e.orderDetail.id?e.canDelete(e.orderDetail.status):null;e.$mp.data=Object.assign({},{$root:{g0:r,g1:i,l0:a,g2:o,m2:n,m3:c}})},o=[]},"6a1d":function(e,t,r){"use strict";r.r(t);var i=r("4591"),a=r.n(i);for(var o in i)["default"].indexOf(o)<0&&function(e){r.d(t,e,(function(){return i[e]}))}(o);t["default"]=a.a},cec7:function(e,t,r){"use strict";(function(e,t){var i=r("47a9");r("06e9");i(r("3240"));var a=i(r("e732"));e.__webpack_require_UNI_MP_PLUGIN__=r,t(a.default)}).call(this,r("3223")["default"],r("df3c")["createPage"])},e732:function(e,t,r){"use strict";r.r(t);var i=r("52ed"),a=r("6a1d");for(var o in a)["default"].indexOf(o)<0&&function(e){r.d(t,e,(function(){return a[e]}))}(o);r("f55b");var n=r("828b"),c=Object(n["a"])(a["default"],i["b"],i["c"],!1,null,null,null,!1,i["a"],void 0);t["default"]=c.exports},f55b:function(e,t,r){"use strict";var i=r("0c17"),a=r.n(i);a.a}},[["cec7","common/runtime","common/vendor"]]]);