require('../common/vendor.js');(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pagesExt/business/sales-ranking"],{"1aae":function(t,e,a){"use strict";var n=a("3314"),i=a.n(n);i.a},"2bb3a":function(t,e,a){"use strict";a.r(e);var n=a("78b6"),i=a("2cda");for(var s in i)["default"].indexOf(s)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(s);a("1aae");var r=a("828b"),u=Object(r["a"])(i["default"],n["b"],n["c"],!1,null,null,null,!1,n["a"],void 0);e["default"]=u.exports},"2cda":function(t,e,a){"use strict";a.r(e);var n=a("4957"),i=a.n(n);for(var s in n)["default"].indexOf(s)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(s);e["default"]=i.a},3314:function(t,e,a){},4957:function(t,e,a){"use strict";(function(t){Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var a=getApp(),n={data:function(){return{rankingType:"business",rankingList:[],totalCount:0,dateTypeOptions:[{value:"",label:"全部时间"},{value:"year",label:"按年"},{value:"month",label:"按月"},{value:"day",label:"按日"}],currentDateType:{value:"",label:"全部时间"},dateValue:"",datePickerMode:"date",dateFields:"day",displayDateValue:"",categoryOptions:[{id:"",name:"全部学校"}],currentCategory:{id:"",name:"全部学校"},keyword:"",currentPage:1,perPage:10}},computed:{totalPages:function(){return Math.ceil(this.totalCount/this.perPage)||1}},onLoad:function(){this.getCategories(),this.fetchRankingData()},methods:{switchRankingType:function(t){this.rankingType!==t&&(this.rankingType=t,this.currentPage=1,this.fetchRankingData())},handleDateTypeChange:function(t){var e=t.detail.value;switch(this.currentDateType=this.dateTypeOptions[e],this.currentDateType.value){case"year":this.datePickerMode="date",this.dateFields="year";break;case"month":this.datePickerMode="date",this.dateFields="month";break;case"day":this.datePickerMode="date",this.dateFields="day";break;default:this.dateValue="",this.displayDateValue="";break}this.dateValue="",this.displayDateValue="",this.currentPage=1,this.fetchRankingData()},handleDateChange:function(t){this.dateValue=t.detail.value,this.displayDateValue=this.dateValue,this.currentPage=1,this.fetchRankingData()},handleCategoryChange:function(t){var e=t.detail.value;this.currentCategory=this.categoryOptions[e],this.currentPage=1,this.fetchRankingData()},handleSearch:function(){this.currentPage=1,this.fetchRankingData()},prevPage:function(){this.currentPage>1&&(this.currentPage--,this.fetchRankingData())},nextPage:function(){this.currentPage<this.totalPages&&(this.currentPage++,this.fetchRankingData())},getCategories:function(){var t=this;a.get("/ApiBusiness/getBusinessCategories",{},(function(e){1===e.status&&e.data&&e.data.length>0?t.categoryOptions=[{id:"",name:"全部学校"}].concat(e.data):console.error("获取学校列表失败:",e.msg||"未知错误")}))},fetchRankingData:function(){var e=this;t.showLoading({title:"加载中..."});var n={ranking_type:this.rankingType,pagenum:this.currentPage,pernum:this.perPage};this.currentDateType.value&&this.dateValue&&(n.date_type=this.currentDateType.value,n.date_value=this.dateValue),"business"===this.rankingType&&this.currentCategory.id&&(n.category_id=this.currentCategory.id),this.keyword&&(n.business_keyword=this.keyword),a.get("ApiBusiness/salesRanking",n,(function(n){t.hideLoading(),1===n.status?(e.rankingList=n.data||[],e.totalCount=n.total_count||0):(a.error(n.msg||"获取数据失败"),e.rankingList=[],e.totalCount=0)}),(function(){t.hideLoading(),a.error("网络请求失败")}))}}};e.default=n}).call(this,a("df3c")["default"])},"78b6":function(t,e,a){"use strict";a.d(e,"b",(function(){return n})),a.d(e,"c",(function(){return i})),a.d(e,"a",(function(){}));var n=function(){var t=this.$createElement,e=(this._self._c,this.rankingList.length);this.$mp.data=Object.assign({},{$root:{g0:e}})},i=[]},fdec:function(t,e,a){"use strict";(function(t,e){var n=a("47a9");a("06e9");n(a("3240"));var i=n(a("2bb3a"));t.__webpack_require_UNI_MP_PLUGIN__=a,e(i.default)}).call(this,a("3223")["default"],a("df3c")["createPage"])}},[["fdec","common/runtime","common/vendor"]]]);