<view><block wx:if="{{isload}}"><block><form data-event-opts="{{[['submit',[['formsubmit',['$event']]]]]}}" bindsubmit="__e"><view class="st_box"><view class="st_form"><block wx:if="{{cateArr}}"><view><picker style="height:80rpx;line-height:80rpx;border-bottom:1px solid #EEEEEE;font-size:18px;" value="{{cindex}}" range="{{cateArr}}" data-event-opts="{{[['change',[['cateChange',['$event']]]]]}}" bindchange="__e"><view class="picker">{{cindex==-1?'请选择类型':cateArr[cindex]}}</view></picker></view></block><view><input placeholder="输入标题" name="title" maxlength="-1"/></view><view><textarea style="height:100rpx;" placeholder="输入简介" name="content" maxlength="-1"></textarea></view><view class="uploadbtn_ziti1">上传封面</view><view class="flex" style="flex-wrap:wrap;padding-top:20rpx;"><block wx:for="{{pics}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="layui-imgbox"><view class="layui-imgbox-close" data-index="{{index}}" data-field="pics" data-event-opts="{{[['tap',[['removeimg',['$event']]]]]}}" bindtap="__e"><image src="{{pre_url+'/static/img/ico-del.png'}}"></image></view><view class="layui-imgbox-img"><image src="{{item}}" data-url="{{item}}" mode="widthFix" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image></view></view></block><block wx:if="{{$root.g0<1}}"><view class="uploadbtn" style="{{('background:url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 60rpx;background-size:80rpx 80rpx;background-color:#F3F3F3;')}}" data-field="pics" data-event-opts="{{[['tap',[['uploadimg',['$event']]]]]}}" bindtap="__e"></view></block></view><input type="text" hidden="true" name="pics" maxlength="-1" value="{{$root.g1}}"/><view class="uploadbtn_ziti2">上传短视频</view><view class="flex-y-center" style="width:100%;padding:20rpx 0;margin-top:20rpx;"><image style="width:200rpx;height:200rpx;background:#eee;" src="{{pre_url+'/static/img/uploadvideo.png'}}" data-event-opts="{{[['tap',[['uploadvideo',['$event']]]]]}}" bindtap="__e"></image><block wx:if="{{video}}"><text style="padding-left:20rpx;color:#333;">已上传短视频</text></block></view><input type="text" hidden="true" name="video" maxlength="-1" value="{{video}}"/></view></view><view class="st_button flex-y-center"><button style="{{'background:'+($root.m0)+';'}}" form-type="submit">发表</button></view><view style="width:100%;margin-top:40rpx;text-align:center;color:#999;display:flex;align-items:center;justify-content:center;" data-url="myupload" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">我的发表记录<image style="width:30rpx;height:30rpx;" src="{{pre_url+'/static/img/arrowright.png'}}"></image></view><view style="width:100%;height:60rpx;"></view></form></block></block><block wx:if="{{loading}}"><loading vue-id="1bf73c46-1" bind:__l="__l"></loading></block><dp-tabbar vue-id="1bf73c46-2" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="1bf73c46-3" data-ref="popmsg" bind:__l="__l"></popmsg></view>