<view class="container"><block wx:if="{{isload}}"><block><form data-event-opts="{{[['submit',[['formSubmit',['$event']]]]]}}" bindsubmit="__e"><view class="form"><view class="form-item"><text class="label">寄存名称</text><text class="flex1"></text><text>{{info.name}}</text></view><view class="form-item"><text class="label">寄存数量</text><text class="flex1"></text><text>{{info.num}}</text></view><view class="form-item"><text class="label">寄存人</text><text class="flex1"></text><text>{{info.linkman}}</text></view><view class="form-item"><text class="label">手机号</text><text class="flex1"></text><text>{{info.tel}}</text></view></view><view class="form"><view class="form-item"><text class="label">寄存备注</text><text class="flex1"></text><text>{{info.message}}</text></view></view><view class="form"><view class="form-item"><text class="label">状态</text><text class="flex1"></text><text>{{info.statusLabel}}</text></view></view><view class="form"><view class="flex-col"><text class="label" style="height:98rpx;line-height:98rpx;font-size:30rpx;">寄存拍照</text><view class="flex" style="flex-wrap:wrap;padding-bottom:20rpx;"><view class="layui-imgbox-img"><image src="{{info.pic}}" data-url="{{info.pic}}" mode="widthFix" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image></view></view><input type="text" hidden="true" name="pic" maxlength="-1" value="{{$root.g0}}"/></view></view><block wx:if="{{info.status==1}}"><button class="btn" style="{{('background:linear-gradient(90deg,'+$root.m0+' 0%,rgba('+$root.m1+',0.8) 100%)')}}" data-num="{{info.num}}" data-event-opts="{{[['tap',[['takeout',['$event']]]]]}}" bindtap="__e">取出</button></block><block wx:if="{{info.status==0}}"><button class="btn" style="{{('background:linear-gradient(90deg,'+$root.m2+' 0%,rgba('+$root.m3+',0.8) 100%)')}}" data-type="access" data-operate="通过审核" data-event-opts="{{[['tap',[['check',['$event']]]]]}}" bindtap="__e">通过审核</button></block><block wx:if="{{info.status==0}}"><button class="btn btn2" data-type="refuse" data-operate="驳回" data-event-opts="{{[['tap',[['check',['$event']]]]]}}" bindtap="__e">驳回</button></block></form><block wx:if="{{$root.g1>0}}"><view class="expressinfo"><view class="content"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="{{['item '+(index==0?'on':'')]}}"><view class="f1"><image src="{{'/static/img/dot'+(index==0?'2':'1')+'.png'}}"></image></view><view class="f2"><text class="t2">{{item.m4}}</text><text class="t1">{{item.$orig.remark+item.$orig.num+"件"}}</text></view></view></block></view></view></block><block wx:if="{{boxShow}}"><view data-event-opts="{{[['touchmove',[['disabledScroll',['$event']]]]]}}" catchtouchmove="__e"><view data-event-opts="{{[['tap',[['handleClickMask',['$event']]]]]}}" class="popup__overlay" catchtap="__e"></view><view class="popup__modal"><view class="popup__title"><text class="popup__title-text">请输入取出数量</text><image class="popup__close" style="width:36rpx;height:36rpx;" src="{{pre_url+'/static/img/close.png'}}" data-event-opts="{{[['tap',[['handleClickMask',['$event']]]]]}}" catchtap="__e"></image></view><view class="popup__content takeoutBox"><form report-submit="true" data-event-opts="{{[['submit',[['formSubmit',['$event']]]],['reset',[['formReset',['$event']]]]]}}" bindsubmit="__e" bindreset="__e"><view class="orderinfo"><view class="item"><text class="t1">取出数量</text><input class="t2" type="text" placeholder="请输入要取出的数量" placeholder-style="font-size:28rpx;color:#BBBBBB" name="numbers" value="{{num}}"/></view></view><button class="btn" style="{{'background:'+($root.m5)+';'}}" form-type="submit">确定</button></form></view></view></view></block></block></block><block wx:if="{{loading}}"><loading vue-id="5a5f0aa2-1" bind:__l="__l"></loading></block></view>