<view class="container"><block wx:if="{{isload}}"><block><view class="mymoney" style="{{'background:'+($root.m0)+';'}}"><view class="balance-info"><view class="balance-item"><view class="f1">账户余额</view><view class="f2"><text style="font-size:26rpx;">￥</text>{{userinfo.money}}</view></view><view class="balance-item"><view class="f1">进货款余额</view><view class="f2"><text style="font-size:26rpx;">￥</text>{{userinfo.purchase_money||'0.00'}}</view></view></view><view class="f3" data-url="/pages/money/moneylog" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><text>余额明细</text><text class="iconfont iconjiantou" style="font-size:20rpx;"></text></view></view><view class="content2"><block wx:if="{{caninput==1}}"><block><view class="item3"><view class="f1">￥</view><view class="f2"><input style="font-size:60rpx;" type="digit" name="money" placeholder="请输入转入金额" placeholder-style="color:#999;font-size:40rpx" data-event-opts="{{[['input',[['moneyinput',['$event']]]]]}}" value="{{money}}" bindinput="__e"/></view></view></block></block><block wx:if="{{shuoming}}"><view style="margin-top:40rpx;padding:0 30rpx;line-height:42rpx;"><parse vue-id="3e05619c-1" content="{{shuoming}}" data-event-opts="{{[['^navigate',[['navigate']]]]}}" bind:navigate="__e" bind:__l="__l"></parse></view></block></view><view class="op"><view data-event-opts="{{[['tap',[['topay',['$event']]]]]}}" class="btn" style="{{'background:'+($root.m1)+';'}}" bindtap="__e">确认转入</view></view></block></block><block wx:if="{{loading}}"><loading vue-id="3e05619c-2" bind:__l="__l"></loading></block><dp-tabbar vue-id="3e05619c-3" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="3e05619c-4" data-ref="popmsg" bind:__l="__l"></popmsg></view>