<view class="container"><dd-tab vue-id="2c9d1ab6-1" itemdata="{{['全部('+countall+')','待审核('+count0+')','已通过('+count1+')','已驳回('+count2+')','已完成('+count3+')']}}" itemst="{{['all','0','1','2','3']}}" st="{{st}}" isfixed="{{true}}" data-event-opts="{{[['^changetab',[['changetab']]]]}}" bind:changetab="__e" bind:__l="__l"></dd-tab><view style="width:100%;height:100rpx;"></view><view class="topsearch flex-y-center"><view class="f1 flex-y-center"><image class="img" src="/static/img/search_ico.png"></image><input placeholder="输入订单号搜索" placeholder-style="font-size:24rpx;color:#C2C2C2" data-event-opts="{{[['confirm',[['searchConfirm',['$event']]]]]}}" value="{{search.orderNo}}" bindconfirm="__e"/></view></view><view class="order-content"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><view class="order-box"><view class="order-header"><text class="order-no">{{"订单号: "+item.$orig.order_no}}</text><text class="order-status" style="{{'color:'+(item.m0)+';'}}">{{item.m1}}</text></view><block wx:if="{{item.$orig.purchase_order_no}}"><view class="purchase-info"><text class="purchase-label">关联进货单号:</text><text class="purchase-value">{{item.$orig.purchase_order_no}}</text></view></block><view class="order-info"><view class="info-item"><text class="label">退款金额:</text><text class="value">{{"￥"+item.$orig.total_price}}</text></view><view class="info-item"><text class="label">退货数量:</text><text class="value">{{item.$orig.product_count+"种"}}</text></view><view class="info-item"><text class="label">申请时间:</text><text class="value">{{item.$orig.create_time}}</text></view><block wx:if="{{item.$orig.audit_time}}"><view class="info-item"><text class="label">审核时间:</text><text class="value">{{item.$orig.audit_time}}</text></view></block><block wx:if="{{item.$orig.audit_remark}}"><view class="info-item"><text class="label">审核备注:</text><text class="value">{{item.$orig.audit_remark}}</text></view></block></view><view class="order-footer"><view class="btn" data-id="{{item.$orig.id}}" data-event-opts="{{[['tap',[['viewDetail',['$event']]]]]}}" bindtap="__e">查看详情</view></view></view></block></block></view><view class="bottom-btn-container"><view data-event-opts="{{[['tap',[['createReturnOrder',['$event']]]]]}}" class="bottom-btn primary" bindtap="__e">申请退货</view></view><block wx:if="{{loading}}"><loading vue-id="2c9d1ab6-2" bind:__l="__l"></loading></block><block wx:if="{{nomore}}"><nomore vue-id="2c9d1ab6-3" bind:__l="__l"></nomore></block><block wx:if="{{nodata}}"><nodata vue-id="2c9d1ab6-4" bind:__l="__l"></nodata></block><popmsg class="vue-ref" vue-id="2c9d1ab6-5" data-ref="popmsg" bind:__l="__l"></popmsg></view>