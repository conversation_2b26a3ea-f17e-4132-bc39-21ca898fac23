<view class="container"><block wx:if="{{isload}}"><block><view class="topsearch flex-y-center"><view class="content" style="height:50px;line-height:50px;font-size:22px;text-align:center;">我的云库存列表</view></view><block wx:if="{{$root.g0}}"><view class="content"><view class="label"><text class="t1">商品信息</text></view><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><view class="item"><view class="f1"><image src="{{item.$orig.pic}}"></image><view class="t2"><text class="x1">{{"商品名称:"+item.$orig.proname}}</text><text class="x1">{{"规格:"+item.$orig.ggname}}</text><text class="x1">{{"数量:"+item.$orig.num+"件"}}</text></view></view><view class="f2"><view class="t3"><view class="x1" data-id="{{item.$orig.id}}" data-ggid="{{item.$orig.ggid}}" data-proid="{{item.$orig.proid}}" data-event-opts="{{[['tap',[['mytihuo',['$event']]]]]}}" bindtap="__e">{{item.m0}}</view></view></view></view></block></block></view></block><uni-popup class="vue-ref" vue-id="311020fe-1" id="dialogmoneyInput" type="dialog" data-ref="dialogmoneyInput" bind:__l="__l" vue-slots="{{['default']}}"><uni-popup-dialog vue-id="{{('311020fe-2')+','+('311020fe-1')}}" mode="input" title="我要提货" value="" placeholder="请输入提货数量" data-event-opts="{{[['^confirm',[['givetihuo']]]]}}" bind:confirm="__e" bind:__l="__l"></uni-popup-dialog></uni-popup><block wx:if="{{dialogShow}}"><view class="popup__container"><view data-event-opts="{{[['tap',[['showDialog',['$event']]]]]}}" class="popup__overlay" catchtap="__e"></view><view class="popup__modal"><view class="popup__title"><text class="popup__title-text">升级</text><image class="popup__close" style="width:36rpx;height:36rpx;" src="/static/img/close.png" data-event-opts="{{[['tap',[['showDialog',['$event']]]]]}}" catchtap="__e"></image></view><view class="popup__content"><block wx:for="{{$root.l1}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="sheet-item"><text class="item-text flex-item">{{item.$orig.name}}</text><view class="flex1"></view><block wx:if="{{item.$orig.id!=tempLevelid&&item.$orig.sort>tempLevelsort}}"><view style="{{'color:'+(item.m1)+';'}}" data-id="{{item.$orig.id}}" data-name="{{item.$orig.name}}" data-event-opts="{{[['tap',[['changeLevel',['$event']]]]]}}" bindtap="__e">选择</view></block><block wx:else><view style="color:#ccc;">选择</view></block></view></block></view></view></view></block></block></block><block wx:if="{{nodata}}"><nodata vue-id="311020fe-3" bind:__l="__l"></nodata></block><block wx:if="{{nomore}}"><nomore vue-id="311020fe-4" bind:__l="__l"></nomore></block><block wx:if="{{loading}}"><loading vue-id="311020fe-5" bind:__l="__l"></loading></block><dp-tabbar vue-id="311020fe-6" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="311020fe-7" data-ref="popmsg" bind:__l="__l"></popmsg></view>