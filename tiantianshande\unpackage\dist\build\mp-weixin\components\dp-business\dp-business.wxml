<view class="dp-business" style="{{'color:'+(params.color)+';'+('background-color:'+(params.bgcolor)+';')+('margin:'+(params.margin_y*2.2+'rpx '+params.margin_x*2.2+'rpx')+';')+('padding:'+(params.padding_y*2.2+'rpx '+params.padding_x*2.2+'rpx')+';')+('font-size:'+(params.fontsize*2+'rpx')+';')}}"><block wx:for="{{$root.l1}}" wx:for-item="item" wx:for-index="index" wx:key="id"><view class="busbox"><view class="businfo" data-url="{{'/pagesExt/business/index?id='+item.$orig.bid}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="f1"><image class="image" src="{{item.$orig.logo}}"></image></view><view class="f2"><view class="title">{{item.$orig.name}}</view><block wx:if="{{params.showpingfen!=='0'}}"><view class="score"><image class="image" src="{{pre_url+'/static/img/star'+item.$orig.commentscore+'.png'}}"></image>{{item.$orig.comment_score+"分"}}</view></block><block wx:if="{{params.showsales!=='0'}}"><view class="sales"><text>销量：</text>{{item.$orig.sales}}</view></block><block wx:if="{{params.showjianjie=='1'}}"><view class="address"><text decode="{{true}}">{{item.$orig.content}}</text></view></block><view class="address flex"><view class="flex1"><block wx:if="{{params.showaddress!=='0'}}"><text>{{item.$orig.address}}</text></block></view><block wx:if="{{params.showdistance}}"><view style="{{'color:'+($root.m0)+';'}}">{{item.$orig.juli}}</view></block></view></view></view><block wx:if="{{params.showproduct==1}}"><view class="buspro"><block wx:for="{{item.l0}}" wx:for-item="item2" wx:for-index="index2" wx:key="id"><view class="item" style="{{('width:23%;margin-right:'+(index2%4!=3?'2%':0))}}" data-url="{{item2.$orig.module=='yuyue'?'/yuyue/product?id='+item2.$orig.id:'/shopPackage/shop/product?id='+item2.$orig.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="product-pic"><image class="image" src="{{item2.$orig.pic}}" mode="widthFix"></image></view><view class="product-info"><view class="p1">{{item2.$orig.name}}</view><view class="p2"><view class="p2-1"><text class="t1" style="{{'color:'+(item2.m1)+';'}}"><text style="font-size:24rpx;">￥</text>{{item2.$orig.sell_price}}<block wx:if="{{item2.$orig.module=='yuyue'}}"><text style="font-size:24rpx;">{{"/"+item2.$orig.danwei}}</text></block></text></view></view><block wx:if="{{item2.$orig.module=='yuyue'}}"><view class="p4" style="{{'background:'+('rgba('+item2.m2+',0.1)')+';'+('color:'+(item2.m3)+';')}}" data-url="{{'/yuyue/product?id='+item2.$orig.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e"><text class="iconfont icon_gouwuche"></text></view></block><block wx:if="{{item2.$orig.module!='yuyue'&&!item2.$orig.price_type}}"><view class="p4" style="{{'background:'+('rgba('+item2.m4+',0.1)')+';'+('color:'+(item2.m5)+';')}}" data-proid="{{item2.$orig.id}}" data-event-opts="{{[['tap',[['buydialogChange',['$event']]]]]}}" catchtap="__e"><text class="iconfont icon_gouwuche"></text></view></block></view></view></block></view></block></view></block><block wx:if="{{buydialogShow}}"><buydialog vue-id="2a108b84-1" proid="{{proid}}" menuindex="{{menuindex}}" data-event-opts="{{[['^addcart',[['addcart']]],['^buydialogChange',[['buydialogChange']]]]}}" bind:addcart="__e" bind:buydialogChange="__e" bind:__l="__l"></buydialog></block></view>