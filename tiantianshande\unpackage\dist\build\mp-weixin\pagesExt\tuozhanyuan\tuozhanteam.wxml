<view class="container"><block wx:if="{{isload}}"><block><block wx:if="{{userlevel&&userlevel.can_agent==2}}"><dd-tab vue-id="2d3d0dfe-1" itemdata="{{[$root.m0,$root.m1]}}" itemst="{{['1','2','3']}}" st="{{st}}" isfixed="{{false}}" data-event-opts="{{[['^changetab',[['changetab']]]]}}" bind:changetab="__e" bind:__l="__l"></dd-tab></block><block wx:if="{{userlevel&&userlevel.can_agent==3}}"><dd-tab vue-id="2d3d0dfe-2" itemdata="{{[$root.m2,$root.m3,$root.m4]}}" itemst="{{['1','2','3']}}" st="{{st}}" isfixed="{{false}}" data-event-opts="{{[['^changetab',[['changetab']]]]}}" bind:changetab="__e" bind:__l="__l"></dd-tab></block><view class="topsearch flex-y-center"><view class="f1 flex-y-center"><image class="img" src="/static/img/search_ico.png"></image><input placeholder="输入昵称/姓名/手机号搜索" placeholder-style="font-size:24rpx;color:#C2C2C2" data-event-opts="{{[['confirm',[['searchConfirm',['$event']]]],['input',[['searchChange',['$event']]]]]}}" value="{{keyword}}" bindconfirm="__e" bindinput="__e"/></view></view><view data-event-opts="{{[['tap',[['givescoreshow',['$event']]]]]}}" class="x1" bindtap="__e">添加成员</view><block wx:if="{{$root.g0}}"><view class="content"><view class="label"><text class="t1">成员信息</text><text class="t2">TA的额度</text></view><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><view class="item"><view class="f1"><image src="{{item.$orig.headimg}}"></image><view class="t2"><text class="x1">{{item.$orig.nickname}}</text><text class="x2">{{item.$orig.createtime}}</text><text class="x2">等级：事业部</text><block wx:if="{{item.$orig.tel}}"><text class="x2">{{"手机号："+item.$orig.tel}}</text></block></view></view><view class="f2"><text class="t1">+0</text><text class="t2">{{item.$orig.downcount+"个商家"}}</text><view class="t3"><block wx:if="{{userlevel&&userlevel.team_givescore==1}}"><view class="x1" data-id="{{item.$orig.id}}" data-event-opts="{{[['tap',[['givescoreshow',['$event']]]]]}}" bindtap="__e">{{"充"+item.m5}}</view></block><block wx:if="{{userlevel}}"><view class="x1" data-id="{{item.$orig.id}}" data-levelid="{{item.$orig.levelid}}" data-levelsort="{{item.$orig.levelsort}}" data-event-opts="{{[['tap',[['showDialog',['$event']]]]]}}" bindtap="__e">设为招商经理</view></block></view></view></view></block></block></view></block><uni-popup class="vue-ref" vue-id="2d3d0dfe-3" id="dialogmoneyInput" type="dialog" data-ref="dialogmoneyInput" bind:__l="__l" vue-slots="{{['default']}}"><uni-popup-dialog vue-id="{{('2d3d0dfe-4')+','+('2d3d0dfe-3')}}" mode="input" title="转账金额" value="" placeholder="请输入转账金额" data-event-opts="{{[['^confirm',[['givemoney']]]]}}" bind:confirm="__e" bind:__l="__l"></uni-popup-dialog></uni-popup><uni-popup class="vue-ref" vue-id="2d3d0dfe-5" id="dialogscoreInput" type="dialog" data-ref="dialogscoreInput" bind:__l="__l" vue-slots="{{['default']}}"><uni-popup-dialog vue-id="{{('2d3d0dfe-6')+','+('2d3d0dfe-5')}}" mode="input" title="转账数量" value="" placeholder="请输入转账数量" data-event-opts="{{[['^confirm',[['givescore']]]]}}" bind:confirm="__e" bind:__l="__l"></uni-popup-dialog></uni-popup><block wx:if="{{dialogShow}}"><view class="popup__container"><view data-event-opts="{{[['tap',[['showDialog',['$event']]]]]}}" class="popup__overlay" catchtap="__e"></view><view class="popup__modal"><view class="popup__title"><text class="popup__title-text">升级</text><image class="popup__close" style="width:36rpx;height:36rpx;" src="/static/img/close.png" data-event-opts="{{[['tap',[['showDialog',['$event']]]]]}}" catchtap="__e"></image></view><view class="popup__content"><block wx:for="{{$root.l1}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="sheet-item"><text class="item-text flex-item">{{item.$orig.name}}</text><view class="flex1"></view><block wx:if="{{item.$orig.id!=tempLevelid&&item.$orig.sort>tempLevelsort}}"><view style="{{'color:'+(item.m6)+';'}}" data-id="{{item.$orig.id}}" data-name="{{item.$orig.name}}" data-event-opts="{{[['tap',[['changeLevel',['$event']]]]]}}" bindtap="__e">选择</view></block><block wx:else><view style="color:#ccc;">选择</view></block></view></block></view></view></view></block></block></block><block wx:if="{{nodata}}"><nodata vue-id="2d3d0dfe-7" bind:__l="__l"></nodata></block><block wx:if="{{nomore}}"><nomore vue-id="2d3d0dfe-8" bind:__l="__l"></nomore></block><block wx:if="{{loading}}"><loading vue-id="2d3d0dfe-9" bind:__l="__l"></loading></block><dp-tabbar vue-id="2d3d0dfe-10" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="2d3d0dfe-11" data-ref="popmsg" bind:__l="__l"></popmsg></view>