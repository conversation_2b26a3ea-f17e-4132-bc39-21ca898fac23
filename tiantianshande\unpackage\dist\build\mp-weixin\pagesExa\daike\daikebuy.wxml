<view class="container"><block wx:if="{{isload}}"><block><view class="view-show"><view class="search-container" style="text-align:center;" data-url="{{'/shopPackage/shop/search?bid='+bid}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e"><view class="search-text" style="height:70rpx;line-height:70rpx;font-size:35rpx;">代购单</view></view><view class="content" style="{{'overflow:hidden;display:flex;'+('height:'+('calc(100% - '+(menuindex>-1?294:194)+'rpx)')+';')}}"><view class="nav_right" style="width:100%;"><view class="nav_right-content"><scroll-view class="detail-list" scrollIntoView="{{scrollToViewId}}" scrollWithAnimation="{{animation}}" scroll-y="true" show-scrollbar="{{false}}" data-event-opts="{{[['scroll',[['scroll',['$event']]]]]}}" bindscroll="__e"><view class="classification-detail-item"><view class="product-itemlist"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="id"><view class="item" data-url="{{'/shopPackage/shop/product?id='+item.$orig.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="product-pic"><image class="image" src="{{item.$orig.pic}}" mode="widthFix"></image></view><view class="product-info"><view class="p1"><text>{{item.$orig.name}}</text></view><view class="p2"><text class="t1" style="{{'font-size:'+('25rpx')+';'}}">{{item.$orig.ggname}}</text></view><view class="p1" style="margin-top:20rpx;"><text class="t1" style="{{'color:'+(item.m0)+';'+('font-size:'+('35rpx')+';')}}"><text style="font-size:20rpx;padding-right:1px;">￥</text>{{item.$orig.sell_price}}</text><text style="font-size:25rpx;padding-right:1px;margin-left:50rpx;">{{"X"+item.$orig.num}}</text></view></view></view></block></view></view></scroll-view></view></view></view></view><view style="height:auto;position:relative;"><view style="width:100%;height:100rpx;"></view><view class="{{['footer','flex',menuindex>-1?'tabbarbot':'notabbarbot']}}"><view data-event-opts="{{[['tap',[['handleClickMask',['$event']]]]]}}" class="cart_ico" style="{{'background:'+('linear-gradient(0deg,'+$root.m1+' 0%,rgba('+$root.m2+',0.8) 100%)')+';'}}" catchtap="__e"><image class="img" src="/static/img/cart.png"></image><block wx:if="{{cartList.total>0}}"><view class="cartnum" style="{{'background:'+($root.m3)+';'}}">{{''+cartList.total}}</view></block></view><view class="text1">合计</view><view class="text2 flex1" style="{{'color:'+($root.m4)+';'}}"><text style="font-size:20rpx;">￥</text>{{orderData.totalprice}}</view><view class="{{['op',(!canSettle)?'disabled-btn':'']}}" style="{{'background:'+(canSettle?'linear-gradient(270deg,'+$root.m5+' 0%,rgba('+$root.m6+',0.8) 100%)':'#ccc')+';'}}" data-orderid="{{order_id}}" data-event-opts="{{[['tap',[['gopay',['$event']]]]]}}" bindtap="__e">去结算</view></view></view><block wx:if="{{cartListShow}}"><view class="{{['popup__container',menuindex>-1?'tabbarbot':'notabbarbot']}}" style="margin-bottom:100rpx;"><view data-event-opts="{{[['tap',[['handleClickMask',['$event']]]]]}}" class="{{['popup__overlay',menuindex>-1?'tabbarbot':'notabbarbot']}}" style="margin-bottom:100rpx;" catchtap="__e"></view><view class="popup__modal" style="min-height:400rpx;padding:0;"><view class="popup__title" style="border-bottom:1px solid #EFEFEF;"><text class="popup__title-text" style="color:#323232;font-weight:bold;font-size:32rpx;">购物车</text><view data-event-opts="{{[['tap',[['clearShopCartFn',['$event']]]]]}}" class="popup__close flex-y-center" style="color:#999999;font-size:24rpx;" catchtap="__e"><image style="width:24rpx;height:24rpx;margin-right:6rpx;" src="/static/img/del.png"></image>清空</view></view></view></view></block></block></block><dp-tabbar vue-id="be0d5994-1" opt="{{opt}}" data-event-opts="{{[['^getmenuindex',[['getmenuindex']]]]}}" bind:getmenuindex="__e" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="be0d5994-2" data-ref="popmsg" bind:__l="__l"></popmsg></view>