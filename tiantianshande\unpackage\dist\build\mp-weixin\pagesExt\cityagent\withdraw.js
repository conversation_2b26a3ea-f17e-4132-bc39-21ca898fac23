require('../common/vendor.js');(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pagesExt/cityagent/withdraw"],{"02fc":function(t,a,n){"use strict";(function(t,a){var i=n("47a9");n("06e9");i(n("3240"));var e=i(n("9196"));t.__webpack_require_UNI_MP_PLUGIN__=n,a(e.default)}).call(this,n("3223")["default"],n("df3c")["createPage"])},"5d47":function(t,a,n){"use strict";var i=n("feb2"),e=n.n(i);e.a},"791a":function(t,a,n){"use strict";(function(t){Object.defineProperty(a,"__esModule",{value:!0}),a.default=void 0;var n=getApp(),i={data:function(){return{opt:{},loading:!1,isload:!1,menuindex:-1,agentInfo:{},money:0,sysset:{},paytype:"微信钱包",tmplids:[],accountInfo:{aliaccountname:"",aliaccount:"",bankcarduser:"",bankcardnum:"",bankname:"",bankaddress:""}}},onLoad:function(t){this.opt=n.getopts(t);this.getdata()},onPullDownRefresh:function(){this.getdata()},methods:{getdata:function(){var a=this;a.loading=!0,n.post("ApiCityAgent/getAgentInfo",{},(function(i){a.loading=!1,t.stopPullDownRefresh(),1==i.status?(a.agentInfo=i.agentinfo,a.tmplids=[],a.getWithdrawSetting()):n.error(i.msg)}))},getWithdrawSetting:function(){var t=this;n.post("ApiCityAgent/getWithdrawSetting",{},(function(a){if(a&&1==a.status){var n=a.withdraw_setting||{},i=a.convert_setting||{};t.sysset={withdrawmin:n.withdraw_min||i.min_convert_amount||0,withdrawfee:n.withdraw_fee||i.convert_fee_rate||0,withdraw_weixin:void 0!==n.withdraw_weixin?n.withdraw_weixin:1,withdraw_aliaccount:void 0!==n.withdraw_aliaccount?n.withdraw_aliaccount:1,withdraw_bankcard:void 0!==n.withdraw_bankcard?n.withdraw_bankcard:1};var e="微信钱包";1==t.sysset.withdraw_weixin&&(e="微信钱包"),t.sysset.withdraw_weixin&&0!=t.sysset.withdraw_weixin||(e="支付宝"),t.sysset.withdraw_weixin&&0!=t.sysset.withdraw_weixin||t.sysset.withdraw_aliaccount&&0!=t.sysset.withdraw_aliaccount||(e="银行卡"),t.paytype=e,t.loaded()}else t.sysset={withdrawmin:0,withdrawfee:0,withdraw_weixin:1,withdraw_aliaccount:1,withdraw_bankcard:1},t.paytype="微信钱包",t.loaded()}),(function(a){t.sysset={withdrawmin:0,withdrawfee:0,withdraw_weixin:1,withdraw_aliaccount:1,withdraw_bankcard:1},t.paytype="微信钱包",t.loaded()}))},loaded:function(){this.isload=!0,t.setNavigationBarTitle({title:"余额提现"})},moneyinput:function(t){var a=parseFloat(this.agentInfo&&this.agentInfo.money||0),i=parseFloat(t.detail.value);i<0?n.error("必须大于0"):i>a&&n.error("可提现余额不足"),this.money=i},changeradio:function(t){var a=t.currentTarget.dataset.paytype;this.paytype=a},formSubmit:function(){var t=parseFloat(this.agentInfo&&this.agentInfo.money||0),a=parseFloat(this.sysset.withdrawmin||0),i=parseFloat(this.money),e=this.paytype;if(isNaN(i)||i<=0)n.error("提现金额必须大于0");else if(a>0&&i<a)n.error("提现金额必须大于¥"+a);else if(i>t)n.error("余额不足");else{if("支付宝"==e){if(!this.accountInfo.aliaccount||""==this.accountInfo.aliaccount.trim())return void n.error("请输入支付宝账号");if(!this.accountInfo.aliaccountname||""==this.accountInfo.aliaccountname.trim())return void n.error("请输入支付宝账户名")}if("银行卡"==e){if(!this.accountInfo.bankcardnum||""==this.accountInfo.bankcardnum.trim())return void n.error("请输入银行卡号");if(!this.accountInfo.bankcarduser||""==this.accountInfo.bankcarduser.trim())return void n.error("请输入持卡人姓名");if(!this.accountInfo.bankname||""==this.accountInfo.bankname.trim())return void n.error("请输入开户银行")}n.showLoading("提交中");var o={amount:i,paytype:e,remark:"城市代理提现申请",account_info:this.accountInfo};n.post("ApiCityAgent/applyWithdraw",o,(function(t){if(n.showLoading(!1),1==t.status){var a="提现申请成功！\n";a+="申请金额：¥"+t.data.amount+"\n",parseFloat(t.data.fee)>0&&(a+="手续费：¥"+t.data.fee+"\n",a+="实际到账：¥"+t.data.actual_amount+"\n"),a+="请等待审核...",n.alert(a,(function(){n.goto("/pagesExt/cityagent/withdrawlog?st=1")}))}else n.error(t.msg)}),(function(t){n.showLoading(!1),console.log("提现申请失败:",t),n.error("网络错误，请重试")}))}},goto:function(t){var a=t.currentTarget.dataset.url;a&&n.goto("/pagesExt/cityagent/"+a)}}};a.default=i}).call(this,n("df3c")["default"])},9196:function(t,a,n){"use strict";n.r(a);var i=n("d6ff"),e=n("c164");for(var o in e)["default"].indexOf(o)<0&&function(t){n.d(a,t,(function(){return e[t]}))}(o);n("5d47");var r=n("828b"),s=Object(r["a"])(e["default"],i["b"],i["c"],!1,null,null,null,!1,i["a"],void 0);a["default"]=s.exports},c164:function(t,a,n){"use strict";n.r(a);var i=n("791a"),e=n.n(i);for(var o in i)["default"].indexOf(o)<0&&function(t){n.d(a,t,(function(){return i[t]}))}(o);a["default"]=e.a},d6ff:function(t,a,n){"use strict";n.d(a,"b",(function(){return e})),n.d(a,"c",(function(){return o})),n.d(a,"a",(function(){return i}));var i={loading:function(){return n.e("components/loading/loading").then(n.bind(null,"ceaa"))},dpTabbar:function(){return n.e("components/dp-tabbar/dp-tabbar").then(n.bind(null,"b875"))},popmsg:function(){return n.e("components/popmsg/popmsg").then(n.bind(null,"2bf2"))}},e=function(){var t=this,a=t.$createElement,n=(t._self._c,t.isload?t.t("color1"):null),i=t.isload&&1==t.sysset.withdraw_weixin&&"微信钱包"==t.paytype?t.t("color1"):null,e=t.isload&&1==t.sysset.withdraw_aliaccount&&"支付宝"==t.paytype?t.t("color1"):null,o=t.isload&&1==t.sysset.withdraw_bankcard&&"银行卡"==t.paytype?t.t("color1"):null,r=t.isload?t.t("color1"):null;t.$mp.data=Object.assign({},{$root:{m0:n,m1:i,m2:e,m3:o,m4:r}})},o=[]},feb2:function(t,a,n){}},[["02fc","common/runtime","common/vendor"]]]);