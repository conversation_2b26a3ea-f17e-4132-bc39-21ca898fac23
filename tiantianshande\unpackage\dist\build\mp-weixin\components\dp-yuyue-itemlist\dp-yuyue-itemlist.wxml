<view style="width:100%;"><view class="dp-product-itemlist"><block wx:for="{{data}}" wx:for-item="item" wx:for-index="index" wx:key="id"><view class="item" data-url="{{'/yuyue/product?id='+item[idfield]}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="product-pic"><image class="image" src="{{item.pic}}" mode="widthFix"></image><block wx:if="{{saleimg!=''}}"><image class="saleimg" src="{{saleimg}}" mode="widthFix"></image></block></view><view class="product-info"><block wx:if="{{showname==1}}"><view class="p1">{{item.name}}</view></block><block wx:if="{{showprice!='0'}}"><view class="p2"><text class="t1" style="{{'color:'+($root.m0)+';'}}">{{item.sell_price}}<text style="font-size:24rpx;padding-left:4rpx;">{{"元/"+item.danwei}}</text></text></view></block><view class="p3"><block wx:if="{{showsales=='1'&&item.sales>0}}"><view class="p3-1"><text style="overflow:hidden;">{{"已售"+item.sales}}</text></view></block></view></view></view></block></view><block wx:if="{{buydialogShow}}"><buydialog vue-id="02ecec1c-1" proid="{{proid}}" menuindex="{{menuindex}}" data-event-opts="{{[['^buydialogChange',[['buydialogChange']]]]}}" bind:buydialogChange="__e" bind:__l="__l"></buydialog></block></view>