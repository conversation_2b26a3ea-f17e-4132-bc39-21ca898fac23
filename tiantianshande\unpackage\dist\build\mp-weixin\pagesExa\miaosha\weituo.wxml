<view class="container"><block wx:if="{{isload}}"><block><dd-tab vue-id="3b7786c2-1" itemdata="{{['我的仓库']}}" itemst="{{['all']}}" st="{{st}}" isfixed="{{true}}" data-event-opts="{{[['^changetab',[['changetab']]]]}}" bind:changetab="__e" bind:__l="__l"></dd-tab><view style="width:100%;height:100rpx;"></view><view class="order-content"><block wx:for="{{datalist}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><view class="order-box"><view class="head"><block wx:if="{{item.bid!=0}}"><view class="f1" data-url="{{'/pagesExt/business/index?id='+item.bid}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e"><image src="/static/img/ico-shop.png"></image>{{''+item.binfo.name}}</view></block><block wx:else><view class="f1"><image class="logo-row" src="{{item.binfo.logo}}"></image>{{''+item.binfo.name+''}}</view></block><view class="flex1"></view><block wx:if="{{item.status2==0}}"><text class="st0">待转售</text></block><block wx:if="{{item.status2==2}}"><text class="st1">委卖中</text></block><block wx:if="{{item.status2==3}}"><text class="st1">待付款</text></block><block wx:if="{{item.status2==4}}"><text class="st2">待确认</text></block><block wx:if="{{item.status2==5}}"><text class="st3">已提货</text></block><block wx:if="{{item.status2==6}}"><text class="st4">已确认</text></block><block wx:if="{{item.status2==7}}"><text class="st4">审核驳回</text></block></view><view class="content" style="{{(index+1==item.procount?'border-bottom:none':'')}}"><view><image src="{{item.pic}}"></image></view><view class="detail"><text class="t1">{{item.name}}</text><text class="t2">默认规格</text><view class="t3"><text class="x1 flex1">{{"￥"+item.sell_price+''}}</text><text class="x2">×1</text></view></view></view><view class="op"><block wx:if="{{item.status2==0}}"><block><view class="btn2" data-url="{{'buyweituo?id='+item.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e">委托上架</view></block></block></view></view></block></block></view><block wx:if="{{nomore}}"><nomore vue-id="3b7786c2-2" bind:__l="__l"></nomore></block><block wx:if="{{nodata}}"><nodata vue-id="3b7786c2-3" bind:__l="__l"></nodata></block></block></block><block wx:if="{{loading}}"><loading vue-id="3b7786c2-4" bind:__l="__l"></loading></block><dp-tabbar vue-id="3b7786c2-5" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="3b7786c2-6" data-ref="popmsg" bind:__l="__l"></popmsg></view>