<view class="container"><block wx:if="{{isload}}"><block><view class="view-show"><view class="search-container" data-url="{{'/shopPackage/shop/search?bid='+bid}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e"><view class="search-box"><image class="img" src="/static/img/search_ico.png"></image><view class="search-text">搜索感兴趣的商品</view></view></view><view class="content" style="{{'overflow:hidden;display:flex;'+('height:'+('calc(100% - '+(menuindex>-1?294:194)+'rpx)')+';')}}"><scroll-view class="{{['nav_left',menuindex>-1?'tabbarbot':'notabbarbot']}}" scrollWithAnimation="{{animation}}" scroll-y="true"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><view class="{{['nav_left_items',index===currentActiveIndex?'active':'']}}" style="{{'color:'+(index===currentActiveIndex?item.m0:'#333')+';'}}" data-root-item-id="{{item.$orig.id}}" data-root-item-index="{{index}}" data-event-opts="{{[['tap',[['clickRootItem',['$event']]]]]}}" bindtap="__e"><view class="before" style="{{'background:'+(item.m1)+';'}}"></view>{{item.$orig.name}}</view></block></block></scroll-view><view class="nav_right"><view class="nav_right-content"><scroll-view class="detail-list" scrollIntoView="{{scrollToViewId}}" scrollWithAnimation="{{animation}}" scroll-y="true" show-scrollbar="{{false}}" data-event-opts="{{[['scroll',[['scroll',['$event']]]]]}}" bindscroll="__e"><block wx:for="{{$root.l2}}" wx:for-item="detail" wx:for-index="index" wx:key="index"><view class="classification-detail-item"><view class="head" data-id="{{detail.$orig.id}}" id="{{'detail-'+detail.$orig.id}}"><view class="txt">{{detail.$orig.name}}</view><view class="show-all" data-id="{{detail.$orig.id}}" data-event-opts="{{[['tap',[['gotoCatproductPage',['$event']]]]]}}" bindtap="__e">查看全部<text class="iconfont iconjiantou"></text></view></view><view class="product-itemlist"><block wx:for="{{detail.l1}}" wx:for-item="item" wx:for-index="index" wx:key="id"><view class="item" data-url="{{'/shopPackage/shop/product?id='+item.$orig.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="product-pic"><image class="image" src="{{item.$orig.pic}}" mode="widthFix"></image></view><view class="product-info"><view class="p1"><text>{{item.$orig.name}}</text></view><block wx:if="{{item.$orig.price_type!=1||item.$orig.sell_price>0}}"><view class="p2"><text class="t1" style="{{'color:'+(item.m2)+';'}}"><text style="font-size:20rpx;padding-right:1px;">￥</text>{{item.$orig.sell_price}}</text><block wx:if="{{item.$orig.market_price*1>item.$orig.sell_price*1}}"><text class="t2">{{"￥"+item.$orig.market_price}}</text></block></view></block><view class="p3"><block wx:if="{{item.$orig.sales>0}}"><view class="p3-1"><text style="overflow:hidden;">{{"已售"+item.$orig.sales+"件"}}</text></view></block><block wx:if="{{item.$orig.limit_start>0}}"><view class="p3-2"><text style="overflow:hidden;">{{item.$orig.limit_start+"件起售"}}</text></view></block></view><block wx:if="{{!item.$orig.price_type}}"><view class="p4" style="{{'background:'+('rgba('+item.m3+',0.1)')+';'+('color:'+(item.m4)+';')}}" data-proid="{{item.$orig.id}}" data-event-opts="{{[['tap',[['buydialogChange',['$event']]]]]}}" catchtap="__e"><text class="iconfont icon_gouwuche"></text></view></block></view></view></block></view></view></block></scroll-view></view></view></view></view><block wx:if="{{buydialogShow}}"><buydialog vue-id="53c4a88f-1" proid="{{proid}}" menuindex="{{menuindex}}" btntype="1" needaddcart="{{false}}" data-event-opts="{{[['^buydialogChange',[['buydialogChange']]],['^addcart',[['afteraddcart']]]]}}" bind:buydialogChange="__e" bind:addcart="__e" bind:__l="__l"></buydialog></block><view style="height:auto;position:relative;"><view style="width:100%;height:100rpx;"></view><view class="{{['footer','flex',menuindex>-1?'tabbarbot':'notabbarbot']}}"><view data-event-opts="{{[['tap',[['handleClickMask',['$event']]]]]}}" class="cart_ico" style="{{'background:'+('linear-gradient(0deg,'+$root.m5+' 0%,rgba('+$root.m6+',0.8) 100%)')+';'}}" catchtap="__e"><image class="img" src="/static/img/cart.png"></image><block wx:if="{{cartList.total>0}}"><view class="cartnum" style="{{'background:'+($root.m7)+';'}}">{{cartList.total}}</view></block></view><view class="text1">合计</view><view class="text2 flex1" style="{{'color:'+($root.m8)+';'}}"><text style="font-size:20rpx;">￥</text>{{cartList.totalprice}}</view><view data-event-opts="{{[['tap',[['gopay',['$event']]]]]}}" class="op" style="{{'background:'+('linear-gradient(270deg,'+$root.m9+' 0%,rgba('+$root.m10+',0.8) 100%)')+';'}}" bindtap="__e">去结算</view></view></view><block wx:if="{{cartListShow}}"><view class="{{['popup__container',menuindex>-1?'tabbarbot':'notabbarbot']}}" style="margin-bottom:100rpx;"><view data-event-opts="{{[['tap',[['handleClickMask',['$event']]]]]}}" class="{{['popup__overlay',menuindex>-1?'tabbarbot':'notabbarbot']}}" style="margin-bottom:100rpx;" catchtap="__e"></view><view class="popup__modal" style="min-height:400rpx;padding:0;"><view class="popup__title" style="border-bottom:1px solid #EFEFEF;"><text class="popup__title-text" style="color:#323232;font-weight:bold;font-size:32rpx;">购物车</text><view data-event-opts="{{[['tap',[['clearShopCartFn',['$event']]]]]}}" class="popup__close flex-y-center" style="color:#999999;font-size:24rpx;" catchtap="__e"><image style="width:24rpx;height:24rpx;margin-right:6rpx;" src="/static/img/del.png"></image>清空</view></view><view class="popup__content" style="padding:0;"><scroll-view class="prolist" scroll-y="{{true}}"><block wx:for="{{cartList.list}}" wx:for-item="cart" wx:for-index="index" wx:key="index"><block><view class="proitem"><image class="pic flex0" src="{{cart.guige.pic?cart.guige.pic:cart.product.pic}}"></image><view class="con"><view class="f1">{{cart.product.name}}</view><block wx:if="{{cart.guige.name!='默认规格'}}"><view class="f2">{{cart.guige.name}}</view></block><view class="f3" style="color:#ff5555;margin-top:10rpx;font-size:28rpx;">{{"￥"+cart.guige.sell_price}}</view></view><view class="addnum"><view class="minus"><image class="img" src="/static/img/cart-minus.png" data-num="-1" data-proid="{{cart.proid}}" data-ggid="{{cart.ggid}}" data-stock="{{cart.guige.stock}}" data-event-opts="{{[['tap',[['addcart',['$event']]]]]}}" bindtap="__e"></image></view><text class="i">{{cart.num}}</text><view class="plus"><image class="img" src="/static/img/cart-plus.png" data-num="1" data-proid="{{cart.proid}}" data-ggid="{{cart.ggid}}" data-stock="{{cart.guige.stock}}" data-event-opts="{{[['tap',[['addcart',['$event']]]]]}}" bindtap="__e"></image></view></view></view></block></block><block wx:if="{{!$root.g0}}"><block><text class="nopro">暂时没有商品喔~</text></block></block></scroll-view></view></view></view></block></block></block><block wx:if="{{loading}}"><loading vue-id="53c4a88f-2" bind:__l="__l"></loading></block><dp-tabbar vue-id="53c4a88f-3" opt="{{opt}}" data-event-opts="{{[['^getmenuindex',[['getmenuindex']]]]}}" bind:getmenuindex="__e" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="53c4a88f-4" data-ref="popmsg" bind:__l="__l"></popmsg></view>