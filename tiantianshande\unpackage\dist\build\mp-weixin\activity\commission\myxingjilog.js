(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["activity/commission/myxingjilog"],{"042c":function(t,e,n){"use strict";var o=n("cab0"),a=n.n(o);a.a},"2c2b":function(t,e,n){"use strict";(function(t){Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n=getApp(),o={data:function(){return{opt:{},loading:!1,isload:!1,menuindex:-1,st:1,datalist:[],pagenum:1,userlevel:{},userinfo:{},textset:{},levelList:{},keyword:"",tomid:"",tomoney:0,toscore:0,nodata:!1,nomore:!1,dialogShow:!1,tempMid:"",tempLevelid:"",tempLevelsort:""}},onLoad:function(t){this.opt=n.getopts(t),this.getdata()},onPullDownRefresh:function(){this.getdata()},onReachBottom:function(){this.nodata||this.nomore||(this.pagenum=this.pagenum+1,this.getdata(!0))},methods:{getdata:function(e){e||(this.pagenum=1,this.datalist=[]);var o=this,a=o.st,i=o.pagenum,s=o.keyword;o.loading=!0,o.nodata=!1,o.nomore=!1,n.post("ApiAgent/myxingjilog",{st:a,pagenum:i,keyword:s},(function(e){o.loading=!1;var a=e.data;if(1==i)o.userinfo=e.userinfo,o.userlevel=e.userlevel,o.textset=n.globalData.textset,o.datalist=a,o.levelList=e.levelList,0==a.length&&(o.nodata=!0),t.setNavigationBarTitle({title:o.t("我的团队")}),o.loaded();else if(0==a.length)o.nomore=!0;else{var s=o.datalist,r=s.concat(a);o.datalist=r}}))},changetab:function(e){this.st=e,t.pageScrollTo({scrollTop:0,duration:0}),this.getdata()},givemoneyshow:function(t){var e=t.currentTarget.dataset.id;this.tomid=e,this.$refs.dialogmoneyInput.open()},givescoreshow:function(t){var e=t.currentTarget.dataset.id;this.tomid=e,this.$refs.dialogscoreInput.open()},givemoney:function(t,e){var o=this,a=o.tomid;n.showLoading("提交中"),n.post("ApiAgent/givemoney",{id:a,money:e},(function(t){n.showLoading(!1),0==t.status?n.error(t.msg):(n.success(t.msg),o.getdata(),o.$refs.dialogmoneyInput.close())}))},givescore:function(t,e){var o=this,a=o.tomid;n.showLoading("提交中"),n.post("ApiAgent/givescore",{id:a,score:e},(function(t){n.showLoading(!1),0==t.status?n.error(t.msg):(n.success(t.msg),o.getdata(),o.$refs.dialogscoreInput.close())}))},searchChange:function(t){this.keyword=t.detail.value},searchConfirm:function(t){var e=t.detail.value;this.keyword=e,this.getdata()},showDialog:function(t){this.tempMid=t.currentTarget.dataset.id,this.tempLevelid=t.currentTarget.dataset.levelid,this.tempLevelsort=t.currentTarget.dataset.levelsort,this.dialogShow=!this.dialogShow},changeLevel:function(t){var e=this,o=e.tempMid,a=t.currentTarget.dataset.id,i=t.currentTarget.dataset.name;n.confirm("确定要升级为"+i+"吗?",(function(){n.showLoading("提交中"),n.post("ApiAgent/levelUp",{mid:o,levelId:a},(function(t){n.showLoading(!1),0==t.status?n.error(t.msg):(n.success(t.msg),e.dialogShow=!1,e.getdata())}))}))}}};e.default=o}).call(this,n("df3c")["default"])},"3ef6":function(t,e,n){"use strict";n.r(e);var o=n("2c2b"),a=n.n(o);for(var i in o)["default"].indexOf(i)<0&&function(t){n.d(e,t,(function(){return o[t]}))}(i);e["default"]=a.a},"8a2b":function(t,e,n){"use strict";(function(t,e){var o=n("47a9");n("06e9");o(n("3240"));var a=o(n("96ac2"));t.__webpack_require_UNI_MP_PLUGIN__=n,e(a.default)}).call(this,n("3223")["default"],n("df3c")["createPage"])},"96ac2":function(t,e,n){"use strict";n.r(e);var o=n("e68d"),a=n("3ef6");for(var i in a)["default"].indexOf(i)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(i);n("042c");var s=n("828b"),r=Object(s["a"])(a["default"],o["b"],o["c"],!1,null,null,null,!1,o["a"],void 0);e["default"]=r.exports},cab0:function(t,e,n){},e68d:function(t,e,n){"use strict";n.d(e,"b",(function(){return a})),n.d(e,"c",(function(){return i})),n.d(e,"a",(function(){return o}));var o={nodata:function(){return n.e("components/nodata/nodata").then(n.bind(null,"101c"))},nomore:function(){return n.e("components/nomore/nomore").then(n.bind(null,"3892"))},loading:function(){return n.e("components/loading/loading").then(n.bind(null,"ceaa"))},dpTabbar:function(){return n.e("components/dp-tabbar/dp-tabbar").then(n.bind(null,"b875"))},popmsg:function(){return n.e("components/popmsg/popmsg").then(n.bind(null,"2bf2"))}},a=function(){var t=this.$createElement,e=(this._self._c,this.isload?this.datalist&&this.datalist.length>0:null);this.$mp.data=Object.assign({},{$root:{g0:e}})},i=[]}},[["8a2b","common/runtime","common/vendor"]]]);