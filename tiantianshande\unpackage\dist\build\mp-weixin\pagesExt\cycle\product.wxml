<view><block wx:if="{{isload}}"><block><view class="container"><view class="swiper-container"><swiper class="swiper" indicator-dots="{{false}}" autoplay="{{true}}" interval="{{5000}}" data-event-opts="{{[['change',[['swiperChange',['$event']]]]]}}" bindchange="__e"><block wx:for="{{product.pics}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><swiper-item class="swiper-item"><view class="swiper-item-view"><image class="img" src="{{item}}" mode="widthFix"></image></view></swiper-item></block></block></swiper><view class="imageCount">{{current+1+"/"+$root.g0}}</view></view><view class="collage_title flex-bt"><text><text class="price">{{"￥"+product.sell_price}}</text><text class="m-price">{{"￥"+product.market_price}}</text></text><view class="ps_title flex-y-center" style="{{'background:'+('rgba('+$root.m0+',0.12)')+';'+('color:'+($root.m1)+';')}}">{{product.ps_cycle_title}}</view></view><view class="header"><view class="title"><view class="lef"><text>{{product.name}}</text></view><view data-event-opts="{{[['tap',[['shareClick',['$event']]]]]}}" class="share" bindtap="__e"><image src="/static/img/share.png"></image><text>分享</text></view></view><view class="sales_stock"><view class="f2">{{"库存:"+product.stock}}</view></view></view><block wx:if="{{shopset.comment==1&&commentcount>0}}"><view class="commentbox"><view class="title"><view class="f1">{{"评价("+commentcount+")"}}</view><view class="f2" data-url="{{'commentlist?proid='+product.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">好评度<text style="{{'color:'+($root.m2)+';'}}">{{product.comment_haopercent+"%"}}</text><image style="width:32rpx;height:32rpx;" src="/static/img/arrowright.png"></image></view></view><view class="comment"><block wx:if="{{$root.g1>0}}"><view class="item"><view class="f1"><image class="t1" src="{{commentlist[0].headimg}}"></image><view class="t2">{{commentlist[0].nickname}}</view><view class="flex1"></view><view class="t3"><block wx:for="{{[0,1,2,3,4]}}" wx:for-item="item2" wx:for-index="index2" wx:key="index2"><image class="img" src="{{'/static/img/star'+(commentlist[0].score>item2?'2':'')+'.png'}}"></image></block></view></view><view class="f2"><text class="t1">{{commentlist[0].content}}</text><view class="t2"><block wx:if="{{commentlist[0].content_pic!=''}}"><block><block wx:for="{{commentlist[0].content_pic}}" wx:for-item="itemp" wx:for-index="index" wx:key="index"><block><view data-url="{{itemp}}" data-urls="{{commentlist[0].content_pic}}" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"><image src="{{itemp}}" mode="widthFix"></image></view></block></block></block></block></view></view><view class="f3" data-url="{{'commentlist?proid='+product.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">查看全部评价</view></view></block><block wx:else><view class="nocomment">暂无评价~</view></block></view></view></block><block wx:if="{{shopset.showjd==1}}"><view class="shop"><image class="p1" src="{{business.logo}}"></image><view class="p2 flex1"><view class="t1">{{business.name}}</view><view class="t2">{{business.desc}}</view></view><button class="p4" style="{{('background:linear-gradient(90deg,'+$root.m3+' 0%,rgba('+$root.m4+',0.8) 100%)')}}" data-url="{{product.bid==0?'/pages/index/index':'/pagesExt/business/index?id='+product.bid}}" data-opentype="reLaunch" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e">进入店铺</button></view></block><view class="detail_title"><view class="t1"></view><view class="t2"></view><view class="t0">商品描述</view><view class="t2"></view><view class="t1"></view></view><view class="detail"><dp vue-id="15fbf644-1" pagecontent="{{pagecontent}}" bind:__l="__l"></dp></view><view style="width:100%;height:70px;"></view><block wx:if="{{product.status==1}}"><view class="{{['bottombar','flex-row','flex-xy-center',menuindex>-1?'tabbarbot':'notabbarbot']}}"><block wx:if="{{kfurl!='contact::'}}"><view class="cart flex-col flex-x-center flex-y-center" data-url="{{kfurl}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image class="img" src="/static/img/kefu.png"></image><view class="t1">客服</view></view></block><block wx:else><button class="cart flex-col flex-x-center flex-y-center" open-type="contact"><image class="img" src="/static/img/kefu.png"></image><view class="t1">客服</view></button></block><view data-event-opts="{{[['tap',[['shareClick',['$event']]]]]}}" class="cart flex-col flex-x-center flex-y-center" bindtap="__e"><image class="img" src="/static/img/share2.png"></image><view class="t1">分享</view></view><view data-event-opts="{{[['tap',[['addfavorite',['$event']]]]]}}" class="favorite flex-col flex-x-center flex-y-center" bindtap="__e"><image class="img" src="/static/img/shoucang.png"></image><view class="t1">{{isfavorite?'已收藏':'收藏'}}</view></view><view class="tobuy flex1" style="{{'background:'+($root.m5)+';'}}" data-url="{{'/pagesExt/cycle/planWrite?id='+product.id}}" data-event-opts="{{[['tap',[['toPlanList',['$event']]]]]}}" bindtap="__e"><text>购买</text></view></view></block><view hidden="{{buydialogHidden}}"><view class="buydialog-mask"><view class="{{['buydialog',menuindex>-1?'tabbarbot':'notabbarbot']}}"><view data-event-opts="{{[['tap',[['buydialogChange',['$event']]]]]}}" class="close" bindtap="__e"><image class="image" src="/static/img/close.png"></image></view><view class="title"><image class="img" src="{{guigelist[ks].pic?guigelist[ks].pic:product.pic}}" data-url="{{guigelist[ks].pic?guigelist[ks].pic:product.pic}}" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image><block wx:if="{{btntype==1}}"><view class="price"><text class="t1">￥</text>{{guigelist[ks].market_price}}</view></block><block wx:else><view class="price"><text class="t1">￥</text>{{guigelist[ks].sell_price+''}}<block wx:if="{{guigelist[ks].market_price>guigelist[ks].sell_price}}"><text class="t2">{{"￥"+guigelist[ks].market_price}}</text></block></view></block><view class="choosename">{{"已选规格: "+guigelist[ks].name}}</view><view class="stock">{{"剩余"+guigelist[ks].stock+"件"}}</view></view><block wx:for="{{guigedata}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="guigelist flex-col"><view class="name">{{item.title}}</view><view class="item flex flex-y-center"><block wx:for="{{item.items}}" wx:for-item="item2" wx:for-index="index2" wx:key="index2"><block><view class="{{['item2 '+(ggselected[item.k]==item2.k?'on':'')]}}" data-itemk="{{item.k}}" data-idx="{{item2.k}}" data-event-opts="{{[['tap',[['ggchange',['$event']]]]]}}" bindtap="__e">{{item2.title}}</view></block></block></view></view></block><view class="buynum flex flex-y-center"><view class="flex1">购买数量：</view><view class="addnum"><view class="minus"><image class="img" src="/static/img/cart-minus.png" data-event-opts="{{[['tap',[['gwcminus',['$event']]]]]}}" bindtap="__e"></image></view><input class="input" type="number" data-event-opts="{{[['input',[['gwcinput',['$event']]]]]}}" value="{{gwcnum}}" bindinput="__e"/><view class="plus"><image class="img" src="/static/img/cart-plus.png" data-event-opts="{{[['tap',[['gwcplus',['$event']]]]]}}" bindtap="__e"></image></view></view></view><view class="op"><block wx:if="{{btntype==1}}"><block><button class="tobuy" style="{{'background:'+($root.m6)+';'}}" data-type="1" data-event-opts="{{[['tap',[['tobuy',['$event']]]]]}}" bindtap="__e">确定</button></block></block><block wx:if="{{btntype==2}}"><block><button class="tobuy" style="{{'background:'+($root.m7)+';'}}" data-type="2" data-event-opts="{{[['tap',[['tobuy',['$event']]]]]}}" bindtap="__e">下一步</button></block></block><block wx:if="{{btntype==3}}"><block><button class="tobuy" style="{{'background:'+($root.m8)+';'}}" data-type="3" data-event-opts="{{[['tap',[['tobuy',['$event']]]]]}}" bindtap="__e">确 定</button></block></block></view></view></view></view></view><block wx:if="{{sharetypevisible}}"><view class="popup__container"><view data-event-opts="{{[['tap',[['handleClickMask',['$event']]]]]}}" class="popup__overlay" catchtap="__e"></view><view class="popup__modal" style="height:320rpx;min-height:320rpx;"><view class="popup__content"><view class="sharetypecontent"><block wx:if="{{$root.m9=='app'}}"><view data-event-opts="{{[['tap',[['shareapp',['$event']]]]]}}" class="f1" bindtap="__e"><image class="img" src="/static/img/weixin.png"></image><text class="t1">分享给好友</text></view></block><block wx:else><block wx:if="{{$root.m10=='mp'}}"><view data-event-opts="{{[['tap',[['sharemp',['$event']]]]]}}" class="f1" bindtap="__e"><image class="img" src="/static/img/weixin.png"></image><text class="t1">分享给好友</text></view></block><block wx:else><block wx:if="{{$root.m11=='h5'}}"><view data-event-opts="{{[['tap',[['sharemp',['$event']]]]]}}" class="f1" bindtap="__e"><image class="img" src="/static/img/weixin.png"></image><text class="t1">分享给好友</text></view></block><block wx:else><button class="f1" open-type="share"><image class="img" src="/static/img/weixin.png"></image><text class="t1">分享给好友</text></button></block></block></block><view data-event-opts="{{[['tap',[['showPoster',['$event']]]]]}}" class="f2" bindtap="__e"><image class="img" src="/static/img/sharepic.png"></image><text class="t1">生成分享图片</text></view></view></view></view></view></block><block wx:if="{{showposter}}"><view class="posterDialog"><view class="main"><view data-event-opts="{{[['tap',[['posterDialogClose',['$event']]]]]}}" class="close" bindtap="__e"><image class="img" src="/static/img/close.png"></image></view><view class="content"><image class="img" src="{{posterpic}}" mode="widthFix" data-url="{{posterpic}}" data-event-opts="{{[['tap',[['previewImage',['$event']]]]]}}" bindtap="__e"></image></view></view></view></block></block></block><block wx:if="{{loading}}"><loading vue-id="15fbf644-2" bind:__l="__l"></loading></block><dp-tabbar vue-id="15fbf644-3" opt="{{opt}}" data-event-opts="{{[['^getmenuindex',[['getmenuindex']]]]}}" bind:getmenuindex="__e" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="15fbf644-4" data-ref="popmsg" bind:__l="__l"></popmsg></view>