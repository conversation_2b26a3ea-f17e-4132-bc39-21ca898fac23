<view class="container"><block wx:if="{{isload}}"><block><form data-event-opts="{{[['submit',[['formSubmit',['$event']]]],['reset',[['formReset',['$event']]]]]}}" bindsubmit="__e" bindreset="__e"><view class="form"><view class="form-item"><text class="label">开户行</text><input class="input" type="text" placeholder="请输入开户行" name="bankname" placeholder-style="color:#BBBBBB;font-size:28rpx" value="{{info.bankname}}"/></view><view class="form-item"><text class="label">账户名</text><input class="input" type="text" placeholder="请输入账户名" name="bankcarduser" placeholder-style="color:#BBBBBB;font-size:28rpx" value="{{info.bankcarduser}}"/></view><view class="form-item"><text class="label">账户号</text><input class="input" type="text" placeholder="请输入银行卡号" name="bankcardnum" placeholder-style="color:#BBBBBB;font-size:28rpx" value="{{info.bankcardnum}}"/></view></view><button class="set-btn" form-type="submit">保 存</button></form></block></block><block wx:if="{{loading}}"><loading vue-id="abf05a4a-1" bind:__l="__l"></loading></block><dp-tabbar vue-id="abf05a4a-2" opt="{{opt}}" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="abf05a4a-3" data-ref="popmsg" bind:__l="__l"></popmsg></view>