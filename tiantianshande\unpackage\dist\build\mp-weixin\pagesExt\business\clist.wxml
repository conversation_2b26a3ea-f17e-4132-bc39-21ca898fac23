<view class="container"><block wx:if="{{isload}}"><block><view class="search-container" data-url="/pagesExt/business/blist" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e"><view class="search-box"><image class="img" src="/static/img/search_ico.png"></image><view class="search-text">搜索感兴趣的商家</view></view></view><view class="content-container"><view class="nav_left"><view class="{{['nav_left_items '+(curIndex==-1?'active':'')]}}" data-index="-1" data-id="0" data-event-opts="{{[['tap',[['switchRightTab',['$event']]]]]}}" bindtap="__e"><view class="before" style="{{'background:'+($root.m0)+';'}}"></view>全部</view><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><view class="{{['nav_left_items '+(curIndex==index?'active':'')]}}" data-index="{{index}}" data-id="{{item.$orig.id}}" data-event-opts="{{[['tap',[['switchRightTab',['$event']]]]]}}" bindtap="__e"><view class="before" style="{{'background:'+(item.m1)+';'}}"></view>{{item.$orig.name}}</view></block></block></view><view class="nav_right"><view class="nav_right-content"><scroll-view class="classify-box" scroll-y="true" data-event-opts="{{[['scrolltolower',[['scrolltolower',['$event']]]]]}}" bindscrolltolower="__e"><block wx:for="{{datalist}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="item" data-url="{{'/pagesExt/business/index?id='+item.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><image class="logo" src="{{item.logo}}"></image><view class="detail"><view class="f1">{{item.name}}</view><view class="f2"><block wx:if="{{item.tel}}"><block>电话：<text style="font-weight:bold;">{{item.tel}}</text></block></block></view><view class="f4">地址：<text style="font-weight:bold;">{{item.address}}</text></view><block wx:if="{{item.juli}}"><view class="f3">{{"距离："+item.juli}}</view></block></view></view></block><block wx:if="{{nomore}}"><nomore vue-id="1a2431e0-1" text="没有更多商家了" bind:__l="__l"></nomore></block><block wx:if="{{nodata}}"><nodata vue-id="1a2431e0-2" text="暂无相关商家" bind:__l="__l"></nodata></block></scroll-view></view></view></view></block></block><block wx:if="{{loading}}"><loading vue-id="1a2431e0-3" loadstyle="left:62.5%" bind:__l="__l"></loading></block><dp-tabbar vue-id="1a2431e0-4" opt="{{opt}}" data-event-opts="{{[['^getmenuindex',[['getmenuindex']]]]}}" bind:getmenuindex="__e" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="1a2431e0-5" data-ref="popmsg" bind:__l="__l"></popmsg></view>