<view class="container"><block wx:if="{{isload}}"><block><dd-tab vue-id="2f2e0161-1" itemdata="{{['未使用','已使用','已过期']}}" itemst="{{['0','1','2']}}" st="{{st}}" isfixed="{{false}}" data-event-opts="{{[['^changetab',[['changetab']]]]}}" bind:changetab="__e" bind:__l="__l"></dd-tab><view class="coupon-list"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="coupon" style="{{(!item.$orig.from_mid&&(item.$orig.isgive==1||item.$orig.isgive==2)?'padding-left:40rpx':'')}}" data-url="{{'coupondetail?rid='+item.$orig.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e"><view class="radiobox" data-index="{{index}}" data-event-opts="{{[['tap',[['changeradio',['$event']]]]]}}" catchtap="__e"><block wx:if="{{!item.$orig.from_mid&&(item.$orig.isgive==1||item.$orig.isgive==2)}}"><view class="radio" style="{{(item.$orig.checked?'background:'+item.m0+';border:0':'')}}"><image class="radio-img" src="/static/img/checkd.png"></image></view></block></view><view class="pt_left"><view class="pt_left-content"><block wx:if="{{item.$orig.type==1}}"><view class="f1" style="{{'color:'+(item.m1)+';'}}"><text class="t0">￥</text><text class="t1">{{item.$orig.money}}</text></view></block><block wx:if="{{item.$orig.type==10}}"><view class="f1" style="{{'color:'+(item.m2)+';'}}"><text class="t1">{{item.$orig.discount/10}}</text><text class="t0">折</text></view></block><block wx:if="{{item.$orig.type==2}}"><view class="f1" style="{{'color:'+(item.m3)+';'}}">礼品券</view></block><block wx:if="{{item.$orig.type==3}}"><view class="f1" style="{{'color:'+(item.m4)+';'}}"><text class="t1">{{item.$orig.limit_count}}</text><text class="t2">次</text></view></block><block wx:if="{{item.$orig.type==4}}"><view class="f1" style="{{'color:'+(item.m5)+';'}}">抵运费</view></block><block wx:if="{{item.$orig.type==5}}"><view class="f1" style="{{'color:'+(item.m6)+';'}}"><text class="t0">￥</text><text class="t1">{{item.$orig.money}}</text></view></block><block wx:if="{{item.$orig.type==1||item.$orig.type==4||item.$orig.type==5}}"><view class="f2" style="{{'color:'+(item.m7)+';'}}"><block wx:if="{{item.$orig.minprice>0}}"><text>{{"满"+item.$orig.minprice+"元可用"}}</text></block><block wx:else><text>无门槛</text></block></view></block></view></view><view class="pt_right"><view class="f1"><view class="t1">{{item.$orig.couponname}}</view><block wx:if="{{item.$orig.type==1}}"><text class="t2" style="{{'background:'+('rgba('+item.m8+',0.1)')+';'+('color:'+(item.m9)+';')}}">代金券</text></block><block wx:if="{{item.$orig.type==2}}"><text class="t2" style="{{'background:'+('rgba('+item.m10+',0.1)')+';'+('color:'+(item.m11)+';')}}">礼品券</text></block><block wx:if="{{item.$orig.type==3}}"><text class="t2" style="{{'background:'+('rgba('+item.m12+',0.1)')+';'+('color:'+(item.m13)+';')}}">计次券</text></block><block wx:if="{{item.$orig.type==4}}"><text class="t2" style="{{'background:'+('rgba('+item.m14+',0.1)')+';'+('color:'+(item.m15)+';')}}">运费抵扣券</text></block><block wx:if="{{item.$orig.type==5}}"><text class="t2" style="{{'background:'+('rgba('+item.m16+',0.1)')+';'+('color:'+(item.m17)+';')}}">餐饮券</text></block><block wx:if="{{!item.$orig.from_mid&&(item.$orig.isgive==1||item.$orig.isgive==2)}}"><text class="t2" style="{{'background:'+('rgba('+item.m18+',0.1)')+';'+('color:'+(item.m19)+';')}}">可赠送</text></block><block wx:if="{{item.$orig.bid>0}}"><view class="t4">{{"适用商家："+item.$orig.bname}}</view></block><view class="t3" style="{{(item.$orig.bid>0?'margin-top:0':'margin-top:10rpx')}}">{{"有效期至 "+item.$orig.endtime}}</view></view><block wx:if="{{item.$orig.isgive!=2&&st==0}}"><block><block wx:if="{{item.$orig.type==1||item.$orig.type==10}}"><block><block wx:if="{{item.m20}}"><button class="btn" style="{{'background:'+('linear-gradient(270deg,'+item.m21+' 0%,rgba('+item.m22+',0.8) 100%)')+';'}}" data-url="{{'/shopPackage/shop/prolist?cpid='+item.$orig.couponid+(item.$orig.bid?'&bid='+item.$orig.bid:'')}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e">去使用</button></block><block wx:if="{{item.$orig.fwtype==4}}"><button class="btn" style="{{'background:'+('linear-gradient(270deg,'+item.m23+' 0%,rgba('+item.m24+',0.8) 100%)')+';'}}" data-url="{{'/yuyue/prolist?cpid='+item.$orig.couponid+(item.$orig.bid?'&bid='+item.$orig.bid:'')}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e">去使用</button></block></block></block><block wx:else><block><button class="btn" style="{{'background:'+('linear-gradient(270deg,'+item.m25+' 0%,rgba('+item.m26+',0.8) 100%)')+';'}}" data-url="{{'coupondetail?rid='+item.$orig.id}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" catchtap="__e">去使用</button></block></block></block></block><block wx:if="{{st==1}}"><image class="sygq" src="{{pre_url+'/static/img/ysy.png'}}"></image></block><block wx:if="{{st==2}}"><image class="sygq" src="{{pre_url+'/static/img/ygq.png'}}"></image></block></view></view></block></view><block wx:if="{{nomore}}"><nomore vue-id="2f2e0161-2" bind:__l="__l"></nomore></block><block wx:if="{{nodata}}"><nodata vue-id="2f2e0161-3" bind:__l="__l"></nodata></block><block wx:if="{{checkednum>0}}"><view class="{{['giveopbox',menuindex>-1?'tabbarbot':'notabbarbot3']}}"><block wx:if="{{$root.m27=='app'}}"><view data-event-opts="{{[['tap',[['shareapp',['$event']]]]]}}" class="btn-give" style="{{'background:'+('linear-gradient(90deg,'+$root.m28+' 0%,rgba('+$root.m29+',0.8) 100%)')+';'}}" bindtap="__e">{{"转赠好友("+checkednum+"张)"}}</view></block><block wx:else><block wx:if="{{$root.m30=='mp'}}"><view data-event-opts="{{[['tap',[['sharemp',['$event']]]]]}}" class="btn-give" style="{{'background:'+('linear-gradient(90deg,'+$root.m31+' 0%,rgba('+$root.m32+',0.8) 100%)')+';'}}" bindtap="__e">{{"转赠好友("+checkednum+"张)"}}</view></block><block wx:else><block wx:if="{{$root.m33=='h5'}}"><view data-event-opts="{{[['tap',[['sharemp',['$event']]]]]}}" class="btn-give" style="{{'background:'+('linear-gradient(90deg,'+$root.m34+' 0%,rgba('+$root.m35+',0.8) 100%)')+';'}}" bindtap="__e">{{"转赠好友("+checkednum+"张)"}}</view></block><block wx:else><button class="btn-give" style="{{'background:'+('linear-gradient(90deg,'+$root.m36+' 0%,rgba('+$root.m37+',0.8) 100%)')+';'}}" open-type="share">{{"转赠好友("+checkednum+"张)"}}</button></block></block></block></view></block><view style="display:none;">{{test}}</view></block></block><block wx:if="{{loading}}"><loading vue-id="2f2e0161-4" bind:__l="__l"></loading></block><dp-tabbar vue-id="2f2e0161-5" opt="{{opt}}" data-event-opts="{{[['^getmenuindex',[['getmenuindex']]]]}}" bind:getmenuindex="__e" bind:__l="__l"></dp-tabbar><popmsg class="vue-ref" vue-id="2f2e0161-6" data-ref="popmsg" bind:__l="__l"></popmsg></view>