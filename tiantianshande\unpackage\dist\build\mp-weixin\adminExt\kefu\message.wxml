<view class="container"><block wx:if="{{isload}}"><block><view class="message-list"><block wx:for="{{datalist}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><block wx:if="{{item.formatTime}}"><view class="message-time">{{item.formatTime}}</view></block><block wx:if="{{item.isreply==0}}"><view class="message-item"><image class="message-avatar" mode="aspectFill" src="{{item.headimg}}"></image><view class="message-text-left"><view class="arrow-box arrow-left"><image class="arrow-icon" src="{{pre_url+'/static/img/arrow-white.png'}}"></image></view><view class="message-text"><parse vue-id="{{'47f1de66-1-'+index}}" content="{{item.content}}" bind:__l="__l"></parse></view></view></view></block><block wx:else><view class="message-item" style="justify-content:flex-end;"><view class="message-text-right"><view class="arrow-box arrow-right"><image class="arrow-icon" src="{{pre_url+'/static/img/arrow-green.png'}}"></image></view><view class="message-text"><parse vue-id="{{'47f1de66-2-'+index}}" content="{{item.content}}" bind:__l="__l"></parse></view></view><image class="message-avatar" mode="aspectFill" src="{{item.uheadimg}}"></image></view></block></block></block></view><view class="input-box notabbarbot" id="input-box"><view class="input-form"><image class="pic-icon" src="{{pre_url+'/static/img/msg-pic.png'}}" data-event-opts="{{[['tap',[['sendimg',['$event']]]]]}}" bindtap="__e"></image><input class="input" confirmHold="{{true}}" confirmType="send" cursorSpacing="20" type="text" maxlength="-1" data-event-opts="{{[['confirm',[['sendMessage',['$event']]]],['focus',[['onInputFocus',['$event']]]],['input',[['messageChange',['$event']]]]]}}" value="{{message}}" bindconfirm="__e" bindfocus="__e" bindinput="__e"/><image class="face-icon" src="{{pre_url+'/static/img/face-icon.png'}}" data-event-opts="{{[['tap',[['toggleFaceBox',['$event']]]]]}}" bindtap="__e"></image><block wx:if="{{!trimMessage}}"><button class="send-button">发送</button></block><block wx:if="{{trimMessage}}"><button data-event-opts="{{[['tap',[['sendMessage',['$event']]]]]}}" class="send-button-active" bindtap="__e">发送</button></block></view><block wx:if="{{faceshow}}"><wxface bind:selectface="__e" vue-id="47f1de66-3" data-event-opts="{{[['^selectface',[['selectface']]]]}}" bind:__l="__l"></wxface></block></view></block></block><block wx:if="{{loading}}"><loading vue-id="47f1de66-4" bind:__l="__l"></loading></block><popmsg class="vue-ref" vue-id="47f1de66-5" data-ref="popmsg" bind:__l="__l"></popmsg></view>