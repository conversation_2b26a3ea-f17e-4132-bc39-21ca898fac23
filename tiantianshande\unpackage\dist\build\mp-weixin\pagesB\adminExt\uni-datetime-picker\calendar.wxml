<view data-event-opts="{{[['mouseleave',[['leaveCale',['$event']]]]]}}" class="uni-calendar" bindmouseleave="__e"><block wx:if="{{!insert&&show}}"><view data-event-opts="{{[['tap',[['maskClick',['$event']]]]]}}" class="{{['uni-calendar__mask',(aniMaskShow)?'uni-calendar--mask-show':'']}}" bindtap="__e"></view></block><block wx:if="{{insert||show}}"><view class="{{['uni-calendar__content',(!insert)?'uni-calendar--fixed':'',(aniMaskShow)?'uni-calendar--ani-show':'',(aniMaskShow)?'uni-calendar__content-mobile':'']}}"><view class="{{['uni-calendar__header',(!insert)?'uni-calendar__header-mobile':'']}}"><view data-event-opts="{{[['tap',[['changeMonth',['pre']]]]]}}" class="uni-calendar__header-btn-box" catchtap="__e"><view class="uni-calendar__header-btn uni-calendar--left"></view></view><picker mode="date" value="{{date}}" fields="month" data-event-opts="{{[['change',[['bindDateChange',['$event']]]]]}}" bindchange="__e"><text class="uni-calendar__header-text">{{(nowDate.year||'')+yearText+(nowDate.month||'')+monthText}}</text></picker><view data-event-opts="{{[['tap',[['changeMonth',['next']]]]]}}" class="uni-calendar__header-btn-box" catchtap="__e"><view class="uni-calendar__header-btn uni-calendar--right"></view></view><block wx:if="{{!insert}}"><view data-event-opts="{{[['tap',[['maskClick',['$event']]]]]}}" class="dialog-close" bindtap="__e"><view class="dialog-close-plus" data-id="close"></view><view class="dialog-close-plus dialog-close-rotate" data-id="close"></view></view></block></view><view class="uni-calendar__box"><block wx:if="{{showMonth}}"><view class="uni-calendar__box-bg"><text class="uni-calendar__box-bg-text">{{nowDate.month}}</text></view></block><view class="uni-calendar__weeks" style="padding-bottom:7px;"><view class="uni-calendar__weeks-day"><text class="uni-calendar__weeks-day-text">{{SUNText}}</text></view><view class="uni-calendar__weeks-day"><text class="uni-calendar__weeks-day-text">{{MONText}}</text></view><view class="uni-calendar__weeks-day"><text class="uni-calendar__weeks-day-text">{{TUEText}}</text></view><view class="uni-calendar__weeks-day"><text class="uni-calendar__weeks-day-text">{{WEDText}}</text></view><view class="uni-calendar__weeks-day"><text class="uni-calendar__weeks-day-text">{{THUText}}</text></view><view class="uni-calendar__weeks-day"><text class="uni-calendar__weeks-day-text">{{FRIText}}</text></view><view class="uni-calendar__weeks-day"><text class="uni-calendar__weeks-day-text">{{SATText}}</text></view></view><block wx:for="{{weeks}}" wx:for-item="item" wx:for-index="weekIndex" wx:key="weekIndex"><view class="uni-calendar__weeks"><block wx:for="{{item}}" wx:for-item="weeks" wx:for-index="weeksIndex" wx:key="weeksIndex"><view class="uni-calendar__weeks-item"><calendar-item class="uni-calendar-item--hook" vue-id="{{'b8001bbc-1-'+weekIndex+'-'+weeksIndex}}" weeks="{{weeks}}" calendar="{{calendar}}" selected="{{selected}}" checkHover="{{range}}" data-event-opts="{{[['^change',[['choiceDate']]],['^handleMouse',[['handleMouse']]]]}}" bind:change="__e" bind:handleMouse="__e" bind:__l="__l"></calendar-item></view></block></view></block></view><block wx:if="{{!insert&&!range&&hasTime}}"><view class="uni-date-changed uni-calendar--fixed-top" style="padding:0 80px;"><view class="uni-date-changed--time-date">{{tempSingleDate?tempSingleDate:selectDateText}}</view><time-picker bind:input="__e" class="time-picker-style" vue-id="b8001bbc-2" type="time" start="{{timepickerStartTime}}" end="{{timepickerEndTime}}" disabled="{{!tempSingleDate}}" border="{{false}}" hide-second="{{hideSecond}}" value="{{time}}" data-event-opts="{{[['^input',[['__set_model',['','time','$event',[]]]]]]}}" bind:__l="__l"></time-picker></view></block><block wx:if="{{!insert&&range&&hasTime}}"><view class="uni-date-changed uni-calendar--fixed-top"><view class="uni-date-changed--time-start"><view class="uni-date-changed--time-date">{{(tempRange.before?tempRange.before:startDateText)+''}}</view><time-picker bind:input="__e" class="time-picker-style" vue-id="b8001bbc-3" type="time" start="{{timepickerStartTime}}" border="{{false}}" hide-second="{{hideSecond}}" disabled="{{!tempRange.before}}" value="{{timeRange.startTime}}" data-event-opts="{{[['^input',[['__set_model',['$0','startTime','$event',[]],['timeRange']]]]]}}" bind:__l="__l"></time-picker></view><view style="line-height:50px;"><uni-icons vue-id="b8001bbc-4" type="arrowthinright" color="#999" bind:__l="__l"></uni-icons></view><view class="uni-date-changed--time-end"><view class="uni-date-changed--time-date">{{tempRange.after?tempRange.after:endDateText}}</view><time-picker bind:input="__e" class="time-picker-style" vue-id="b8001bbc-5" type="time" end="{{timepickerEndTime}}" border="{{false}}" hide-second="{{hideSecond}}" disabled="{{!tempRange.after}}" value="{{timeRange.endTime}}" data-event-opts="{{[['^input',[['__set_model',['$0','endTime','$event',[]],['timeRange']]]]]}}" bind:__l="__l"></time-picker></view></view></block><block wx:if="{{!insert}}"><view class="uni-date-changed uni-date-btn--ok"><view data-event-opts="{{[['tap',[['confirm',['$event']]]]]}}" class="uni-datetime-picker--btn" bindtap="__e">{{confirmText}}</view></view></block></view></block></view>