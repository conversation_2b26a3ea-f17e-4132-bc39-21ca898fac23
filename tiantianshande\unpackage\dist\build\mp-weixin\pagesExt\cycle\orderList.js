require('../common/vendor.js');(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pagesExt/cycle/orderList"],{"0686":function(t,n,e){"use strict";var o=e("8d34"),a=e.n(o);a.a},6477:function(t,n,e){"use strict";(function(t,n){var o=e("47a9");e("06e9");o(e("3240"));var a=o(e("806a6"));t.__webpack_require_UNI_MP_PLUGIN__=e,n(a.default)}).call(this,e("3223")["default"],e("df3c")["createPage"])},"806a6":function(t,n,e){"use strict";e.r(n);var o=e("b752"),a=e("f4b5");for(var i in a)["default"].indexOf(i)<0&&function(t){e.d(n,t,(function(){return a[t]}))}(i);e("0686");var r=e("828b"),c=Object(r["a"])(a["default"],o["b"],o["c"],!1,null,null,null,!1,o["a"],void 0);n["default"]=c.exports},"8d34":function(t,n,e){},b752:function(t,n,e){"use strict";e.d(n,"b",(function(){return a})),e.d(n,"c",(function(){return i})),e.d(n,"a",(function(){return o}));var o={ddTab:function(){return e.e("components/dd-tab/dd-tab").then(e.bind(null,"caa1"))},nomore:function(){return e.e("components/nomore/nomore").then(e.bind(null,"3892"))},nodata:function(){return e.e("components/nodata/nodata").then(e.bind(null,"101c"))},loading:function(){return e.e("components/loading/loading").then(e.bind(null,"ceaa"))},dpTabbar:function(){return e.e("components/dp-tabbar/dp-tabbar").then(e.bind(null,"b875"))},popmsg:function(){return e.e("components/popmsg/popmsg").then(e.bind(null,"2bf2"))}},a=function(){var t=this,n=t.$createElement,e=(t._self._c,t.isload?t.__map(t.datalist,(function(n,e){var o=t.__get_orig(n),a=[1,2,3].includes(n.status)&&n.invoice&&n.team&&2==n.team.status,i=0==n.status?t.t("color1"):null;return{$orig:o,g0:a,m0:i}})):null);t.$mp.data=Object.assign({},{$root:{l0:e}})},i=[]},f4b5:function(t,n,e){"use strict";e.r(n);var o=e("f63b"),a=e.n(o);for(var i in o)["default"].indexOf(i)<0&&function(t){e.d(n,t,(function(){return o[t]}))}(i);n["default"]=a.a},f63b:function(t,n,e){"use strict";(function(t){Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;var e=getApp(),o={data:function(){return{opt:{},loading:!1,isload:!1,menuindex:-1,st:"all",datalist:[],pagenum:1,nomore:!1,nodata:!1,keyword:""}},onLoad:function(t){this.opt=e.getopts(t),this.getdata()},onShow:function(){this.getdata()},onPullDownRefresh:function(){this.getdata()},onReachBottom:function(){this.nodata||this.nomore||(this.pagenum=this.pagenum+1,this.getdata(!0))},onNavigationBarSearchInputConfirmed:function(t){this.searchConfirm({detail:{value:t.text}})},methods:{toCycleList:function(t){var n=t.currentTarget.dataset.id,o="/pagesExt/cycle/planList?id="+n;e.goto(o)},toDetail:function(t){var n=t.currentTarget.dataset.id;e.goto("/pagesExt/cycle/orderDetail?id="+n)},changetab:function(n){console.log(n,"st"),this.st=n,t.pageScrollTo({scrollTop:0,duration:0}),this.getdata()},getdata:function(t){t||(this.pagenum=1,this.datalist=[]);var n=this,o=n.pagenum,a=n.st;n.nodata=!1,n.nomore=!1,n.loading=!0,e.post("ApiCycle/orderlist",{st:a,pagenum:o,keyword:n.keyword},(function(t){n.loading=!1;var e=t.datalist;if(1==o)n.datalist=e,0==e.length&&(n.nodata=!0),n.loaded();else if(0==e.length)n.nomore=!0;else{var a=n.datalist,i=a.concat(e);n.datalist=i}}))},toclose:function(t){var n=this,o=t.currentTarget.dataset.id;e.confirm("确定要关闭该订单吗?",(function(){e.showLoading("提交中"),e.post("ApiCycle/closeOrder",{orderid:o},(function(t){e.showLoading(!1),e.success(t.msg),setTimeout((function(){n.getdata()}),1e3)}))}))},todel:function(t){var n=this,o=t.currentTarget.dataset.id;e.confirm("确定要删除该订单吗?",(function(){e.showLoading("删除中"),e.post("ApiCycle/delOrder",{orderid:o},(function(t){e.showLoading(!1),e.success(t.msg),setTimeout((function(){n.getdata()}),1e3)}))}))},orderCollect:function(t){var n=this,o=t.currentTarget.dataset.id;e.confirm("确定要收货吗?",(function(){e.showLoading("提交中"),e.post("ApiCycle/orderCollect",{orderid:o},(function(t){e.showLoading(!1),e.success(t.msg),setTimeout((function(){n.getdata()}),1e3)}))}))},searchConfirm:function(t){this.keyword=t.detail.value,this.getdata(!1)}}};n.default=o}).call(this,e("df3c")["default"])}},[["6477","common/runtime","common/vendor"]]]);