(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["yuyue/usercalendar"],{"02dc":function(e,t,a){"use strict";a.r(t);var n=a("a5fa"),r=a("905a");for(var o in r)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(o);a("7317");var s=a("828b"),i=Object(s["a"])(r["default"],n["b"],n["c"],!1,null,null,null,!1,n["a"],void 0);t["default"]=i.exports},7317:function(e,t,a){"use strict";var n=a("e758"),r=a.n(n);r.a},"7b262":function(e,t,a){"use strict";(function(e){Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var a=getApp(),n={data:function(){return{opt:{},loading:!1,isload:!1,year:(new Date).getFullYear(),month:(new Date).getMonth()+1,weekDays:["日","一","二","三","四","五","六"],calendarDays:[],selectedDate:"",selectedDayText:"",selectedDayWorkers:0,product:{},orderList:[],pagenum:1,nomore:!1,nodata:!1,selectedOrderId:null,orderSelected:!1,calendarData:[],showOrderPanel:!1,showDatePanel:!1}},onLoad:function(e){this.opt=a.getopts(e),e.id&&(this.selectedOrderId=e.id),e.proid?this.getCalendarData(e.proid):(this.getCalendarData(),this.getOrderList()),this.isload=!0},onPullDownRefresh:function(){this.getCalendarData(),this.getOrderList()},methods:{getOrderList:function(e){e||(this.pagenum=1,this.orderList=[]);var t=this,n=t.pagenum;t.nodata=!1,t.nomore=!1,t.loading=!0,a.post("ApiYuyue/orderlistrili",{pagenum:n},(function(e){t.loading=!1;var a=e.datalist||[];console.log("获取到订单列表:",a);var r=a.filter((function(e){return 1==e.status||5==e.status||6==e.status}));if(1==n){if(t.orderList=r,0==r.length&&(t.nodata=!0),t.selectedOrderId)for(var o=0;o<r.length;o++)if(r[o].id==t.selectedOrderId){t.selectOrder(r[o]);break}}else if(0==r.length)t.nomore=!0;else{var s=t.orderList,i=s.concat(r);t.orderList=i}}))},getCalendarData:function(e){var t=this,n={year:t.year,month:t.month};e?n.proid=e:t.selectedOrderId&&(n.id=t.selectedOrderId),t.loading=!0,a.post("ApiYuyue/userCalendar",n,(function(e){t.loading=!1,console.log("获取到日历数据:",e),1==e.status?(t.product=e.product||{},t.calendarData=e.calendar||[],t.calendarData.forEach((function(e){e.available&&(e.specialPrice=Math.random()>.7)})),t.generateCalendar()):a.error(e.msg||"获取日历数据失败")}))},generateCalendar:function(){for(var e=this,t=new Date(this.year,this.month-1,1).getDay(),a=new Date(this.year,this.month,0).getDate(),n=[],r=0;r<t;r++)n.push({day:0,disabled:!0});for(var o=new Date,s="".concat(o.getFullYear(),"-").concat(String(o.getMonth()+1).padStart(2,"0"),"-").concat(String(o.getDate()).padStart(2,"0")),i=function(t){var a="".concat(e.year,"-").concat(String(e.month).padStart(2,"0"),"-").concat(String(t).padStart(2,"0")),r=e.calendarData.find((function(e){return e.date===a}))||{},o=a===s,i=new Date(a)<new Date(s),l=i||!0===r.disabled||!r.available||!r.available_worker_count;n.push({day:t,date:a,weekday:new Date(a).getDay(),weekdayText:e.weekDays[new Date(a).getDay()],available:!l&&r.available,disabled:l,isToday:o,orders:r.orders||0,available_worker_count:r.available_worker_count||0,specialPrice:r.specialPrice||!1})},l=1;l<=a;l++)i(l);var d=7-n.length%7;if(d<7)for(var c=0;c<d;c++)n.push({day:0,disabled:!0});this.calendarDays=n},selectDate:function(t){t.day&&!t.disabled&&(t.available?(this.selectedDate=t.date,this.selectedDayText=this.weekDays[t.weekday]+"（"+this.month+"月"+t.day+"日）",this.selectedDayWorkers=t.available_worker_count,console.log("选择的日期:",this.selectedDate),this.selectedOrderId?this.showDatePanel=!0:(a.alert("请先选择一个订单"),e.pageScrollTo({scrollTop:500,duration:300}))):a.error("该日期不可预约"))},confirmDate:function(){if(this.selectedDate)if(this.selectedOrderId){console.log("准备跳转到预约页面，订单ID:",this.selectedOrderId,"选中日期:",this.selectedDate);var t=this.selectedDate,n="/yuyue/appoint?id=".concat(this.selectedOrderId,"&date=").concat(t);console.log("跳转URL:",n);try{a.goto(n)}catch(r){console.error("跳转失败:",r),e.navigateTo({url:n,fail:function(e){console.error("备用跳转也失败:",e),a.error("页面跳转失败，请稍后重试")}})}}else a.alert("请先选择一个订单");else a.alert("请选择预约日期")},selectOrder:function(e){console.log("选择订单:",e),this.selectedOrderId=e.id,this.showOrderPanel=!1,this.selectedDate?this.showDatePanel=!0:this.getCalendarData()},prevMonth:function(){1===this.month?(this.year--,this.month=12):this.month--,this.selectedDate="",this.selectedDayText="",this.getCalendarData()},nextMonth:function(){12===this.month?(this.year++,this.month=1):this.month++,this.selectedDate="",this.selectedDayText="",this.getCalendarData()},getStatusText:function(e){return 0==e.status?"待付款":1==e.status?1==e.refund_status?"退款审核中":0!==e.appointment_status&&e.appointment_status?1!==e.appointment_status||0!==e.worker_assign_status&&e.worker_id?"派单中":"待选择服务人员":"待预约":2==e.status?"服务中":3==e.status?"已完成":4==e.status?"已取消":5==e.status?"待选择时间":6==e.status?"待分配服务":"未知状态"},goToPurchase:function(){a.goto("/pages/index/index")},closeOrderPanel:function(){this.showOrderPanel=!1},closeDatePanel:function(){this.showDatePanel=!1},closeAllPanels:function(){this.showOrderPanel=!1,this.showDatePanel=!1},selectAndShowCalendar:function(t){console.log("选择订单并展示日历:",t),this.selectedOrderId=t.id,this.selectedDate?(console.log("已选中日期:",this.selectedDate),this.showDatePanel=!0):(this.getCalendarData(),e.pageScrollTo({scrollTop:0,duration:300}))}}};t.default=n}).call(this,a("df3c")["default"])},"905a":function(e,t,a){"use strict";a.r(t);var n=a("7b262"),r=a.n(n);for(var o in n)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(o);t["default"]=r.a},a5fa:function(e,t,a){"use strict";a.d(t,"b",(function(){return r})),a.d(t,"c",(function(){return o})),a.d(t,"a",(function(){return n}));var n={nomore:function(){return a.e("components/nomore/nomore").then(a.bind(null,"3892"))},nodata:function(){return a.e("components/nodata/nodata").then(a.bind(null,"101c"))},loading:function(){return a.e("components/loading/loading").then(a.bind(null,"ceaa"))},dpTabbar:function(){return a.e("components/dp-tabbar/dp-tabbar").then(a.bind(null,"b875"))},popmsg:function(){return a.e("components/popmsg/popmsg").then(a.bind(null,"2bf2"))}},r=function(){var e=this,t=e.$createElement,a=(e._self._c,e.isload?e.orderList.length:null),n=e.isload&&a>0?e.__map(e.orderList,(function(t,a){var n=e.__get_orig(t),r=e.getStatusText(t);return{$orig:n,m0:r}})):null,r=e.isload&&e.showOrderPanel?e.__map(e.orderList,(function(t,a){var n=e.__get_orig(t),r=e.getStatusText(t);return{$orig:n,m1:r}})):null,o=e.isload&&e.showOrderPanel?e.orderList.length:null,s=e.isload&&e.showDatePanel?e.t("color1"):null;e.$mp.data=Object.assign({},{$root:{g0:a,l0:n,l1:r,g1:o,m2:s}})},o=[]},e154:function(e,t,a){"use strict";(function(e,t){var n=a("47a9");a("06e9");n(a("3240"));var r=n(a("02dc"));e.__webpack_require_UNI_MP_PLUGIN__=a,t(r.default)}).call(this,a("3223")["default"],a("df3c")["createPage"])},e758:function(e,t,a){}},[["e154","common/runtime","common/vendor"]]]);