<view><block wx:if="{{isload}}"><block><form data-event-opts="{{[['submit',[['subform',['$event']]]]]}}" bindsubmit="__e"><view class="form-box"><view class="form-item"><view class="f1">菜品分类名称<text style="color:red;">*</text></view><view class="f2"><input type="text" name="name" placeholder="请填写名称" placeholder-style="color:#888" value="{{info.name}}"/></view></view><view class="form-item"><view class="f1">排序</view><view class="f2"><input type="text" name="sort" placeholder="用于排序,越大越靠前" placeholder-style="color:#888" value="{{info.sort}}"/></view></view><view class="form-item"><text>支持店内<text style="color:red;">*</text></text><view><radio-group class="radio-group" name="is_shop" data-event-opts="{{[['change',[['',['$event']]]]]}}" bindchange="__e"><label><radio value="1" checked="{{info.is_shop==1?true:false}}"></radio>显示</label><label><radio value="0" checked="{{!info||info.is_shop==0?true:false}}"></radio>隐藏</label></radio-group></view></view><view class="form-item"><text>支持外卖<text style="color:red;">*</text></text><view><radio-group class="radio-group" name="is_takeaway" data-event-opts="{{[['change',[['',['$event']]]]]}}" bindchange="__e"><label><radio value="1" checked="{{info.is_takeaway==1?true:false}}"></radio>显示</label><label><radio value="0" checked="{{!info||info.is_takeaway==0?true:false}}"></radio>隐藏</label></radio-group></view></view><view class="form-item"><text>支持预定<text style="color:red;">*</text></text><view><radio-group class="radio-group" name="is_booking" data-event-opts="{{[['change',[['',['$event']]]]]}}" bindchange="__e"><label><radio value="1" checked="{{info.is_booking==1?true:false}}"></radio>显示</label><label><radio value="0" checked="{{!info||info.is_booking==0?true:false}}"></radio>隐藏</label></radio-group></view></view><view class="form-item"><text>状态<text style="color:red;">*</text></text><view><radio-group class="radio-group" name="status" data-event-opts="{{[['change',[['',['$event']]]]]}}" bindchange="__e"><label><radio value="1" checked="{{info.status==1?true:false}}"></radio>显示</label><label><radio value="0" checked="{{!info||info.status==0?true:false}}"></radio>隐藏</label></radio-group></view></view></view><button class="savebtn" style="{{('background:linear-gradient(90deg,'+$root.m0+' 0%,rgba('+$root.m1+',0.8) 100%)')}}" form-type="submit">提交</button><block wx:if="{{info.id}}"><button data-event-opts="{{[['tap',[['todel',['$event']]]]]}}" class="button text-btn" bindtap="__e">删除</button></block><view style="height:50rpx;"></view></form></block></block><block wx:if="{{loading}}"><loading vue-id="f9a71922-1" bind:__l="__l"></loading></block></view>