<view class="dp-business" style="{{'color:'+(params.color)+';'+('background-color:'+(params.bgcolor)+';')+('margin:'+(params.margin_y*2.2+'rpx '+params.margin_x*2.2+'rpx')+';')+('padding:'+(params.padding_y*2.2+'rpx '+params.padding_x*2.2+'rpx')+';')+('font-size:'+(params.fontsize*2+'rpx')+';')}}"><block wx:if="{{params.csstype==2}}"><view class="bento-grid"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="{{['bento-item',(index%5===0||index%5===3)?'bento-item-large':'',(index%5===1||index%5===2||index%5===4)?'bento-item-small':'']}}" data-url="{{'/pagesB/dingchang/dingchangdetail?id='+item.$orig.bid+'&services='+item.g0}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="bento-image-container"><image class="bento-image" src="{{item.$orig.pic_list}}" mode="aspectFill"></image><view class="bento-overlay"></view><view class="bento-tags"><block wx:for="{{item.$orig.catalog}}" wx:for-item="ite" wx:for-index="ind" wx:key="ind"><text class="bento-tag">{{ite.name}}</text></block></view></view><view class="bento-content"><view class="bento-title">{{item.$orig.title}}</view><view class="bento-location"><text class="location-text">{{item.$orig.location}}</text><text class="distance-text">{{item.$orig.distance}}</text></view><block wx:if="{{params.showdservice=='1'}}"><view class="bento-services"><block wx:for="{{item.$orig.service}}" wx:for-item="ite" wx:for-index="ind" wx:key="ind"><view class="service-tag" style="{{('border:1rpx solid '+params.primary_color+';color:'+params.primary_color)}}">{{''+ite.name+''}}</view></block></view></block><view class="bento-footer"><view class="bento-intro" decode="{{true}}"><rich-text nodes="{{item.$orig.introduce}}"></rich-text></view><view class="bento-btn" style="{{('background:'+params.primary_color)}}">去订场</view></view></view></view></block></view></block><block wx:for="{{$root.l3}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block wx:if="{{params.csstype==1}}"><view class="busbox modern-card"><view class="businfo" data-url="{{'/pagesB/dingchang/dingchangdetail?id='+item.$orig.bid+'&services='+item.g1}}" data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" bindtap="__e"><view class="f1"><image class="image" src="{{item.$orig.logo}}"></image><view class="image-overlay"></view><block wx:if="{{item.g2}}"><view class="catalog-tags"><block wx:for="{{item.l1}}" wx:for-item="cat" wx:for-index="catIndex" wx:key="catIndex"><text class="catalog-tag">{{cat.name}}</text></block></view></block></view><view class="f2"><view class="title">{{item.$orig.title}}</view><view class="location-info"><view class="location-main"><block wx:if="{{params.location!=='0'}}"><text class="location-text">{{item.$orig.location}}</text></block></view><block wx:if="{{params.showdistance}}"><view class="distance-badge" style="{{'background:'+('linear-gradient(135deg,'+$root.m0+' 0%, rgba('+$root.m1+',0.8) 100%)')+';'}}">{{''+item.$orig.distance+''}}</view></block></view><block wx:if="{{params.showjianjie=='1'}}"><view class="intro-text"><view decode="{{true}}"><rich-text nodes="{{item.$orig.introduce}}"></rich-text></view></view></block><block wx:if="{{params.showdservice=='1'}}"><view class="other"><block wx:for="{{item.l2}}" wx:for-item="ite" wx:for-index="ind" wx:key="ind"><view class="otherTag modern-tag" style="{{('border:1rpx solid '+params.primary_color+';color:'+params.primary_color+';background:rgba('+ite.g3+',0.1)')}}">{{''+ite.$orig.name+''}}</view></block></view></block></view></view></view></block></block><block wx:if="{{buydialogShow}}"><buydialog vue-id="0261bf14-1" proid="{{proid}}" menuindex="{{menuindex}}" data-event-opts="{{[['^addcart',[['addcart']]],['^buydialogChange',[['buydialogChange']]]]}}" bind:addcart="__e" bind:buydialogChange="__e" bind:__l="__l"></buydialog></block></view>